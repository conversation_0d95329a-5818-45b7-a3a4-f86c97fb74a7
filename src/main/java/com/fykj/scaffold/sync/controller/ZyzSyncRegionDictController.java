package com.fykj.scaffold.sync.controller;

import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDict;
import com.fykj.scaffold.sync.domain.params.ZyzSyncRegionDictParams;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 市平台地域数据字典
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@RestController
@RequestMapping("/admin/zyz/sync/region-dict")
public class ZyzSyncRegionDictController extends BaseController<IZyzSyncRegionDictService, ZyzSyncRegionDict, ZyzSyncRegionDictParams> {

}
