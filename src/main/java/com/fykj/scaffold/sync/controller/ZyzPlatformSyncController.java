package com.fykj.scaffold.sync.controller;

import cn.hutool.core.util.IdUtil;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.sync.service.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

/**
 * 市平台信息同步日志
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@RestController
@RequestMapping({"/admin/zyz/platform-sync", "/api/admin/zyz/platform-sync"})
public class ZyzPlatformSyncController {

    @Autowired
    private IPlatformSyncService platformSyncService;

    @Autowired
    private IZyzSyncRegionDictService regionDictService;

    @Autowired
    private IZyzSyncRegionDictAllService regionDictAllService;

    @Autowired
    private IZyzSyncBelongFieldDictService belongFieldDictService;

    @Autowired
    private IZyzSyncDepTypeDictService depTypeDictService;

    @AuditLog("手动同步志愿者")
    @ApiOperation("同步志愿者")
    @PostMapping("/syncVolunteer")
    public Result syncVolunteer(Long volunteerId) {
        return new JsonResult<>(platformSyncService.syncVolunteer(volunteerId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步团队")
    @ApiOperation("同步团队")
    @PostMapping("/syncTeam")
    public Result syncTeam(Long teamId) {
        return new JsonResult<>(platformSyncService.syncTeam(teamId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步新闻")
    @ApiOperation("同步新闻")
    @PostMapping("/syncNews")
    public Result syncNews(Long newsId) {
        return new JsonResult<>(platformSyncService.syncCms(newsId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步需求")
    @ApiOperation("同步需求")
    @PostMapping("/syncRequirement")
    public Result syncRequirement(Long requirementId) {
        return new JsonResult<>(platformSyncService.syncRequirement(requirementId, IdUtil.getSnowflake(1, 1).nextId(), null));
    }

    @AuditLog("手动同步资源")
    @ApiOperation("同步资源")
    @PostMapping("/syncResource")
    public Result syncResource(Long resourceId) {
        return new JsonResult<>(platformSyncService.syncResource(resourceId, IdUtil.getSnowflake(1, 1).nextId(), null));
    }

    @AuditLog("手动同步团队成员")
    @ApiOperation("同步团队成员")
    @PostMapping("/syncTeamMember")
    public Result syncTeamMember(Long volunteerId, Long teamId) {
        return new JsonResult<>(platformSyncService.syncTeamMember(teamId, volunteerId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步职能部门")
    @ApiOperation("同步职能部门")
    @PostMapping("/syncDep")
    public Result syncDep(Long depId) {
        return new JsonResult<>(platformSyncService.syncDep(depId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步实践点")
    @ApiOperation("同步实践点")
    @PostMapping("/syncSpot")
    public Result syncSpot(Long spotId) {
        return new JsonResult<>(platformSyncService.syncSpot(spotId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步阵地")
    @ApiOperation("手动同步阵地")
    @PostMapping("/syncBase")
    public Result syncBase(Long baseId) {
        return new JsonResult<>(platformSyncService.syncBase(baseId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步地域")
    @ApiOperation("地域同步")
    @PostMapping("/syncRegion")
    public Result syncRegion() {
        regionDictService.syncRegionDict();
        return new Result();
    }

    @AuditLog("手动同步地域(苏州大市所有的)")
    @ApiOperation("手动同步地域(苏州大市所有的)")
    @PostMapping("/syncRegionAll")
    public Result syncRegionAll() {
        regionDictAllService.syncRegionDictAll();
        return new Result();
    }

    @AuditLog("手动同步所属领域")
    @ApiOperation("同步所属领域")
    @PostMapping("/syncBelongField")
    public Result syncBelongField() {
        belongFieldDictService.syncBelongFieldDict();
        return new Result();
    }

    @AuditLog("手动同步职能部门类型")
    @ApiOperation("同步职能部门类型")
    @PostMapping("/syncDepType")
    public Result syncDepType() {
        depTypeDictService.syncDepType();
        return new Result();
    }

    @AuditLog("手动同步活动公示")
    @ApiOperation("手动同步活动公示")
    @PostMapping("/syncActShow")
    public Result syncActShow(Long actShowId) {
        return new JsonResult<>(platformSyncService.syncActShow(actShowId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步阵地计划")
    @ApiOperation("手动同步阵地计划")
    @PostMapping("/syncActSchedule")
    public Result syncActSchedule(Long scheduleId) {
        return new JsonResult<>(platformSyncService.syncActSchedule(scheduleId, IdUtil.getSnowflake(1, 1).nextId()));
    }

    @AuditLog("手动同步活动预告关联活动计划")
    @ApiOperation("手动同步活动预告关联活动计划")
    @PostMapping("/syncScheduleActPlan")
    public Result syncScheduleActPlan(Long planId) {
        return new JsonResult<>(platformSyncService.syncScheduleActPlan(planId, IdUtil.getSnowflake(1, 1).nextId()));
    }
}
