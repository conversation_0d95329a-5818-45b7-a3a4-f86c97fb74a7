package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsDto;
import com.fykj.scaffold.mall.domain.dto.OrderFormDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
public interface IMallScoreGoodsService extends IBaseService<MallScoreGoods> {
    /**
     * 分页查询--后端页面
     */
    IPage<MallScoreGoods> getPage(MallScoreGoodsParams params);

    /**
     * 分页查询--后端页面
     */
    IPage<MallScoreGoods> pageForApi(MallScoreGoodsParams params);

    boolean updateStatus(long goodsId, boolean isClearStorage);

    /**
     * 后台变动商品数量
     *
     * @param goodsId  商品ID
     * @param goodsNum 变动数量
     */
    void editGoodsNum(long goodsId, int goodsNum);

    /**
     * 商品数量变动
     *
     * @param goodsId  商品ID
     * @param goodsNum 商品数量
     * @param remark   备注
     */
    void changeGoodsNum(long goodsId, int goodsNum, String remark);

    OrderFormDto getOrderFormDto(Long id);

    List<MallScoreGoodsDto> getGoodsList(MallScoreGoodsParams params);

    void saveMallGoods(MallScoreGoods goods);

    boolean updateMallGoods(MallScoreGoods goods);

    int sumGoodsCountBySku(String sku);

    /**
     * 创建商品兑换小程序二维码
     *
     * @param appId
     * @param goodsId
     * @return
     */
    String createQrCode(String appId, long goodsId, Boolean prod);


    /**
     * 分页查询（pc/小程序 无需登录）
     *
     * @param params 参数
     * @return 分页列表
     */
    IPage<MallScoreGoods> pageForApiStar(MallScoreGoodsParams params);

    /**
     * 分页查询（pc/小程序 需登录）
     *
     * @param params 参数
     * @return 分页列表
     */
    IPage<MallScoreGoods> pageForStar(MallScoreGoodsParams params);

    /**
     * 获取第三方商品信息
     *
     * @param thirdPlatformCode      第三方平台编码
     * @param thirdPlatformGoodsCode 第三方商品编码
     * @return 商品信息
     */
    MallScoreGoods getGoodsByThirdPartGoodsCode(String thirdPlatformCode, String thirdPlatformGoodsCode);

}

