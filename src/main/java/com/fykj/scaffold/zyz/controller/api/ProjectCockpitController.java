package com.fykj.scaffold.zyz.controller.api;


import com.fykj.scaffold.security.business.domain.dto.IdNameDto;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.CockpitCorpDto;
import com.fykj.scaffold.zyz.domain.dto.ProjectCockpitDto;
import com.fykj.scaffold.zyz.domain.dto.ProjectCockpitListDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzProject;
import com.fykj.scaffold.zyz.service.IZyzProjectCompanyService;
import com.fykj.scaffold.zyz.service.IZyzProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 项目表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/api/project/cockpit")
@Api(tags = "公益伙伴计划-驾驶舱接口")
public class ProjectCockpitController {

    @Autowired
    private IZyzProjectService zyzProjectService;

    @Autowired
    private IZyzProjectCompanyService zyzProjectCompanyService;

    @ApiOperation("最新项目年份")
    @GetMapping("/maxProjectYear")
    public JsonResult<String> maxProjectYear() {
        return new JsonResult<>(zyzProjectService.getMaxYear());
    }

    @ApiOperation("总体概览列表")
    @GetMapping("/list")
    public JsonResult<List<ProjectCockpitListDto>> list(@RequestParam String year) {
        return new JsonResult<>(zyzProjectService.cockpitList(year));
    }

    @ApiOperation(value = "获取项目通过id")
    @GetMapping(value = "/getProjectById")
    public Result getProjectById(Long id) {
        ZyzProject res = zyzProjectService.getCockpitProjectById(id);
        DesensitiseUtil.desensitise(res);
        return new JsonResult<>(res);
    }

    @ApiOperation("最新爱心企业")
    @GetMapping("/cockpitCorpList")
    public JsonResult<List<IdNameDto>> cockpitCorpList() {
        return new JsonResult<>(zyzProjectCompanyService.cockpitCorpList());
    }
    @ApiOperation("概览")
    @GetMapping("/cockpitOverview")
    public JsonResult<ProjectCockpitDto> cockpitOverview(@RequestParam String year) {
        return new JsonResult<>(zyzProjectService.cockpitOverview(year));
    }
    @ApiOperation("企业责任联盟爱心文明单位")
    @GetMapping("/csrCockpit")
    public JsonResult<CockpitCorpDto> csrCockpit(@RequestParam String creditCode) {
        return new JsonResult<>(zyzProjectCompanyService.csrCockpit(creditCode));
    }
}
