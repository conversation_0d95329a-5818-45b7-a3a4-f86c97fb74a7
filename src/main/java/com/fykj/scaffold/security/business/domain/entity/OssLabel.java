package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_oss_label")
public class OssLabel extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 对象存储id
     */
    @TableField("oss_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ossId;

    /**
     * 标签id
     */
    @TableField("label_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long labelId;


}
