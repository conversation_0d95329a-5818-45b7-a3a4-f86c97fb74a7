package com.fykj.scaffold.message.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.domain.dto.MsgRecordExportDto;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.param.MsgRecordParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 消息记录服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface IMsgRecordService extends IBaseService<MsgRecord> {

    /**
     * 处理每条消息记录
     * @param sendType
     * @param receiver
     * @param entity
     * @param records
     */
    void handleEverySendTypeData(String sendType, MsgRecord receiver, MsgSend entity, List<MsgRecord> records);

    /**
     * 批量阅读消息
     *
     * @param ids
     * @return
     */
    boolean readBatch(String ids);

    /**
     * 导出
     * @param params
     * @return
     */
    List<MsgRecordExportDto> export(MsgRecordParams params);

    /**
     * 推送进度查询
     * @param sendId
     * @return
     */
    Map<String, Integer> sendProgress(Long sendId);

    /**
     * 删除尚未推送的明细
     * @param sendId
     */
    void removeNotSendRecord(Long sendId);

    /**
     * 消息推送
     * @param sendId
     */
    void sendMessage(Long sendId);

    /**
     * 消息记录推送
     * @param recordId
     */
    void sendMsgRecord(Long recordId);

    /**
     * 处理消息推送 mq消费失败的数据
     * @param recordId
     * @param consumeFailReason
     */
    void dealMqConsumeFailRecord(Long recordId, String consumeFailReason);

    /**
     * 接收者维护页分页查询
     * @param params
     * @return
     */
    IPage<MsgRecord> pageForMaintain(MsgRecordParams params);

    /**
     * 更新记录
     * @param record
     */
    void updateRecord(MsgRecord record);

    /**
     * 删除记录
     * @param id
     */
    void removeRecord(Long id);

    /**
     * 根据标题查找短信发送列表
     * @param title 短信标题
     * @return 列表
     */
    List<MsgRecord> getListByTitle(String title);
}
