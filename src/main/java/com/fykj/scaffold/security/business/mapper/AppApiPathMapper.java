package com.fykj.scaffold.security.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.security.business.domain.entity.AppApiPath;


/**
 * 应用接口权限
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2021-05-28
 */
public interface AppApiPathMapper extends BaseMapper<AppApiPath> {


    /**
     * 删除appid对应的权限
     * @param appId
     */
    void deleteAppApiPathByAppId(String appId);
}
