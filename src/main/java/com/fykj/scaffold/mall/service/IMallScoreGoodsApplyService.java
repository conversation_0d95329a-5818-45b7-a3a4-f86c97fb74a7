package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsApplyParams;
import fykj.microservice.core.base.IBaseService;

/**
 * 星光闪耀商品报名表
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
public interface IMallScoreGoodsApplyService extends IBaseService<MallScoreGoodsApply> {

    /**
     * 新增/修改报名信息
     *
     * @param apply 报名信息
     * @return 是否成功
     */
    Boolean saveOrUpdateApply(MallScoreGoodsApply apply);

    /**
     * 分页查询--后端页面
     */
    IPage<MallScoreGoodsApplyDto> getPage(MallScoreGoodsApplyParams params);

    /**
     * 获取报名信息
     * @param exchangeId
     * @return
     */
    MallScoreGoodsApply getApplyInfo(Long exchangeId);
}

