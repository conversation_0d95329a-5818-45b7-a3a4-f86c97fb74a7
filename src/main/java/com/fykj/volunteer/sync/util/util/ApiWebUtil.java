package com.fykj.volunteer.sync.util.util;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.fykj.volunteer.sync.util.domain.cms.SyncCms;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ApiWebUtil {
    static String APPKEY = "201601125162924000007";
    static String APISECRETKEY = "womgvc90gc90gxmj1vd567btf7gzqfhz";
    static String URL = "http://szzyz.xcb.suzhou.com.cn/openCloudNew/api";


    public static String sendHttpRequest(String method, String xml) {
        return sendHttpRequest(method, xml,"xml", false);
    }

    public static String sendHttpRequestJson(String method, String xml) {
        return sendHttpRequest(method, xml, "json",false);
    }

    public static String sendHttpRequest(String method, String xml,String format, boolean withCbc) {
        Map<String, Object> headers = new HashMap<>();
        String sigBegin = APISECRETKEY + "appKey" + APPKEY;
        if (withCbc) {
            sigBegin = sigBegin + "con_encryptionT";
            headers.put("con_encryption", "T");
        }
        sigBegin = sigBegin + "format"+format+"localezh_CNmethod" + method + "request_xml" + xml + "sign_methodmd5v1.0" + APISECRETKEY;
        String sign = MD5Util.stringToMD5(sigBegin);
        headers.put("appKey", APPKEY);
        headers.put("format", format);
        headers.put("locale", "zh_CN");
        headers.put("method", method);
        headers.put("request_xml", xml);
        headers.put("sign", sign);
        headers.put("sign_method", "md5");
        headers.put("v", "1.0");
        return doPost(URL, headers);
    }

    /**
     * cms的字段进行cbc加密
     *
     * @param syncCms
     */
    public static void syncCmsCBCDs3Encoding(SyncCms syncCms) {
        String secretKey = APPKEY + APISECRETKEY;
        syncCms.setCategoryName(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getCategoryName()));
        syncCms.setTitle(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getTitle()));
        syncCms.setKeywords(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getKeywords()));
        syncCms.setContent(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getContent()));
        syncCms.setPicLogoUrl(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getPicLogoUrl()));
        syncCms.setInputDate(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getInputDate()));
        syncCms.setSource(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getSource()));
        syncCms.setIdcode(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getIdcode()));
        syncCms.setUniqueCode(ThreeDESUtil.des3EncodeCBC(secretKey, syncCms.getUniqueCode()));
    }

    public static String doPost(String url, Map<String, Object> param) {
        try {
            HttpResponse execute = HttpUtil.createPost(url)
                    .form(param).timeout(10 * 1000).execute();
            if (HttpStatus.HTTP_OK != execute.getStatus()) {
                log.error("请求市志愿者平台接口错误" + execute);
                throw new BusinessException(ResultCode.SERVICE_FAIL, "请求市平台接口失败！");
            }
            return execute.body();
        } catch (Exception e) {
            log.error("请求市志愿者平台接口错误，请联系管理员", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "请求市平台接口失败！");
        }
    }

    /*public static void main(String[] args) {
        String s = "(12345678)34";
        //单参数截取：从下标为4开始截取到最后一个字符
        String sub1 = s.substring(s.indexOf("(")+1);
        //双参数截取：从下标0~4的字符
        String sub2 = s.substring(s.indexOf("(")+1,s.indexOf(")"));

        System.out.println(multiply(
                "56682685688368769975687158456712639985994238723884836982688868825888411257229812387269856872691286347123882339844563157664"
                ,"2915334725239712155517715887936541884369523698741368742369874333987413687211705332887598866969993998568526714742687136874368712"));
        System.out.println(sub2);
    }*/


//    public static void main(String[] args) {
//        System.out.println(multiply("123456789012345534", "88888888888888888888"));
//        System.out.println(new BigInteger("123456789012345534").multiply(new BigInteger("88888888888888888888")));
//    }

   /* public static String multiply(String nums1, String nums2) {
        //结果数组
        int[] res = new int[nums1.length() + nums2.length()];
        for (int i = 0; i < nums1.length(); i++) {
            for (int j = 0; j < nums2.length(); j++) {
                //取数字每位
                int value1 = nums1.charAt(i) - '0';
                int value2 = nums2.charAt(j) - '0';
                int multiply = value1 * value2;
                res[i + j + 1] += multiply;
            }
        }

        //处理进位，由低到高位处理
        for (int i = res.length - 1; i >= 0; i--) {
            if (res[i] >= 10) {
                res[i - 1] += res[i] / 10;
                res[i] %= 10;
            }
        }

        //处理结果
        StringBuilder sb = new StringBuilder();
        //排除首位开始无用的0
        boolean firstZeroFlag = true;
        for (int i = 0; i < res.length; i++) {
            if (res[i] == 0 && firstZeroFlag) {
                continue;
            } else {
                sb.append(res[i]);
                firstZeroFlag = false;
            }
        }
        return sb.toString();
    }*/
}
