package com.fykj.scaffold.support.ems;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fykj.scaffold.support.ems.in.OrderSubmitDto;
import com.fykj.scaffold.support.ems.out.CommonEmsOut;
import com.fykj.scaffold.support.ems.out.EmsTraceDto;
import com.fykj.scaffold.support.ems.out.OrderSubmitOutDto;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;
import utils.LocalDateTimeUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 邮政发货接口工具类
 */
@Slf4j
public class EmsExpressUtil {
    private static final String USER_CODE = "JUZCUWUGP";
    private static final String AUTHORIZATION = "UKucYc0HHvOucwwm";
    private static final String SIGN_KEY = "M2t3VVhKV09telVVc1BhNw==";
    private static final String URL = "https://api.ems.com.cn/amp-prod-api/f/amp/api/test";
    private static final String SENDER_NO = "1030003760943";

    /**
     * 提交订单API_CODE
     */
    private static final String ORDER_SUBMIT_CODE = "030001";
    /**
     * 查看面单API_CODE
     */
    private static final String FRESHET_QUERY_CODE = "010004";
    /**
     * 物流追踪API_CODE
     */
    private static final String TRACE_CODE = "040001";

    /**
     * 服务接入（下单）
     *
     * @param orderSubmitDto 下单信息
     * @return 追踪结果
     */
    public static OrderSubmitOutDto orderSubmit(OrderSubmitDto orderSubmitDto) {
        String retJson = callEms(ORDER_SUBMIT_CODE, JSONUtil.toJsonStr(orderSubmitDto));
        // 返回结果DTO
        return JSON.parseObject(retJson, OrderSubmitOutDto.class);
    }

    /**
     * 物流追踪
     *
     * @param waybillNo 运单号
     * @return 追踪结果
     */
    public static List<EmsTraceDto> traceWaybill(String waybillNo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("waybillNo", waybillNo);
        String retJsonStr = callEms(TRACE_CODE, jsonObject.toJSONString());
        JSONObject retJson = JSONObject.parseObject(retJsonStr);
        return JSON.parseObject(retJson.getString("responseItems"), new TypeReference<List<EmsTraceDto>>() {
        });
    }

    /**
     * 面单查询
     *
     * @param waybillNo 运单号
     * @return base64加密的二进制流
     * Facesheet query
     */
    public static String freshetQuery(String waybillNo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("waybillNo", waybillNo);
        jsonObject.put("type", "129");
        String retJsonStr = callEms(FRESHET_QUERY_CODE, jsonObject.toJSONString());
/*
        JSONObject retJson = JSONObject.parseObject(retJsonStr);
*/
        return  retJsonStr;
    }

    /**
     * 调用邮政接口
     *
     * @param apiCode  业务code
     * @param jsonBody 请求体
     * @return
     */
    private static String callEms(String apiCode, String jsonBody) {
        HttpResponse response = HttpRequest.post(URL)
                .form("apiCode", apiCode)
                .form("senderNo", SENDER_NO)
                .form("userCode", USER_CODE)
                .form("authorization", AUTHORIZATION)
                .form("timeStamp", LocalDateTimeUtil.formatDateTime(LocalDateTime.now()))
                .form("logitcsInterface", buildDecryptStr(jsonBody))
                .execute();
        String logit=buildDecryptStr(jsonBody);
        log.error(logit);
        log.error("调用邮政接口失败:{}"+jsonBody);
        if (!response.isOk() || !JSONUtil.isTypeJSON(response.body())) {
            log.error("调用邮政接口失败:{}", response.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "调用邮政接口失败，请联系管理员排查");
        }
        // 解析响应JSON
        String responseJson = response.body();
        // 返回结果DTO
        CommonEmsOut out = JSON.parseObject(responseJson, CommonEmsOut.class);
        if (!"00000".equals(out.getRetCode())) {
            log.error("调用邮政接口失败:{}", response.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "邮政返回错误：" + out.getRetMsg());
        }
        return out.getRetBody();
    }

    /**
     * 加密
     *
     * @param json 请求json
     * @return 加密后的logisticsInterface串
     */
    private static String buildDecryptStr(String json) {
        String content = json + SIGN_KEY;
        return CryptoThirdSM4Tools.sm4Encrypt(content, SIGN_KEY);
    }
    public static void main(String[] args) {
        freshetQuery("9746557007887");
    }
}

//    public static void main(String[] args) {
//        OrderSubmitDto orderSubmitDto = new OrderSubmitDto();
//        orderSubmitDto.setEcommerceUserId("1234544");
//        orderSubmitDto.setLogisticsOrderNo("1000001");
//        orderSubmitDto.setEcommerceUserId("1234544");
//        OrderSubmitDto.Address sender = new OrderSubmitDto.Address();
//        sender.setName("薛伟");
//        sender.setMobile("***********");
//        sender.setProv("江苏省");
//        sender.setCity("苏州市");
//        sender.setCounty("吴中区");
//        sender.setAddress("木渎镇1355号");
//        OrderSubmitDto.Address receiver = new OrderSubmitDto.Address();
//        receiver.setName("薛伟收");
//        receiver.setMobile("13012345678");
//        receiver.setProv("江苏省");
//        receiver.setCity("苏州市");
//        receiver.setCounty("工业园区");
//        receiver.setAddress("金鸡湖大道");
//        orderSubmitDto.setSender(sender);
//        orderSubmitDto.setReceiver(receiver);
//        orderSubmit(orderSubmitDto);
//    }

