package com.fykj.scaffold.portal_website.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.cms.domain.dto.CascaderDto;
import com.fykj.scaffold.cms.service.ICmsCategoryService;
import com.fykj.scaffold.portal_website.domain.entity.VideoCategory;
import com.fykj.scaffold.portal_website.domain.params.VideoCategoryParams;
import com.fykj.scaffold.portal_website.service.IVideoCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;

/**
 * 视频接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/portal_website/video")
@Api(tags = "pc视频接口--无需登录")
public class VideoApi {
    @Autowired
    private IVideoCategoryService videoService;

    @Autowired
    private ICmsCategoryService cmsCategoryService;


    @ApiOperation("分页查询视频")
    @PostMapping({"/pages"})
    public JsonResult<IPage<VideoCategory>> pageForNews(@RequestBody VideoCategoryParams params) {
        IPage<VideoCategory> result = videoService.pageForApi(params);
        return new JsonResult<>(result);
    }

    @GetMapping("cascaderByParentCode")
    @ApiOperation("通过父级code获取级联树")
    public JsonResult<List<CascaderDto>> cascader(@RequestParam String parentCode) {
        List<CascaderDto> categories = cmsCategoryService.cascadeByParentCode(parentCode);
        return new JsonResult<>(categories);
    }


}
