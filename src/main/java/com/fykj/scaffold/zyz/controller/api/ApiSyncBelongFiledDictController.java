package com.fykj.scaffold.zyz.controller.api;

import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;
import com.fykj.scaffold.sync.domain.params.ZyzSyncBelongFieldDictParams;
import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 市平台所属领域数据字典
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-23
 */
@RestController
@RequestMapping("/api/zyz/sync-belong-filed-dict")
public class ApiSyncBelongFiledDictController extends BaseController<IZyzSyncBelongFieldDictService, ZyzSyncBelongFieldDict, ZyzSyncBelongFieldDictParams> {

    @GetMapping("/getTopBelongField")
    @ApiOperation("获取一级领域")
    public JsonResult<List<ZyzSyncBelongFieldDict>> getTopBelongField() {
        List<ZyzSyncBelongFieldDict> fields = baseService.lambdaQuery().eq(ZyzSyncBelongFieldDict::getNodePath, "1").orderByAsc(ZyzSyncBelongFieldDict::getTypeId).list();
        return new JsonResult<>(fields);
    }
}
