package com.fykj.scaffold.portal_website.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.portal_website.domain.dto.VideoCategoryDto;
import com.fykj.scaffold.portal_website.domain.entity.VideoCategory;
import com.fykj.scaffold.portal_website.domain.params.VideoCategoryParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * <p>
 * 培训视频服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface IVideoCategoryService extends IBaseService<VideoCategory> {

    /**
     * 视频上下架
     * @param id
     */
    void ground(String id);

    public IPage<VideoCategory> pageForApi(VideoCategoryParams params);

    /**
     * 获取培训视频---小程序端
     * @return
     */
    List<VideoCategoryDto> getTrainVideoForMini();
}
