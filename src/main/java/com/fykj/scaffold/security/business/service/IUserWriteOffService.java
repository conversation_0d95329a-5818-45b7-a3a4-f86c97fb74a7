package com.fykj.scaffold.security.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.params.UserWriteOffParams;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.security.business.domain.entity.UserWriteOff;

import java.util.List;
import java.util.Map;

/**
 * 系统用户注销管理-服务类
 *
 * @date 2025-05-23
 */
public interface IUserWriteOffService extends IBaseService<UserWriteOff> {

    /**
     * 分页查询用户注销记录
     * @param params
     * @return
     */
    IPage<UserWriteOff> pageQuery(UserWriteOffParams params);

    /**
     * 获取用户志愿者信息和管理团队信息
     * @return
     */
    Map<String, Object> getUserVolunteerAndTeamAdminInfo(Long userId);

    /**
     * 用户注销
     * @param userId
     */
    void writeOff(Long userId);

    /**
     * 用户取消注销
     * @param id
     */
    void writeOffCancel(Long id);

    /**
     * 用户取消注销(通过手机号)
     * @param mobile
     */
    void writeOffCancelByMobile(String mobile);

    /**
     * 最终用户注销处理（管理端手动）
     * @param id
     */
    void finalWriteOffDeal(Long id);

    /**
     * 最终用户注销处理
     * @param writeOffList
     */
    void writeOffFinal(List<UserWriteOff> writeOffList);
}

