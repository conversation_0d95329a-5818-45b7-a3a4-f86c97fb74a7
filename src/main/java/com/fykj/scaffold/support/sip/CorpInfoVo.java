package com.fykj.scaffold.support.sip;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CsrCorpRegisterInfoVo
 * <AUTHOR>
 * @Date 2023/5/5 14:28
 * @Description
 * @Version 1.0
 */
@Data
public class CorpInfoVo implements Serializable {

    // 社会信用代码
    private String tyshxydm;
    // 社会信用代码
    private String qymc;
    // 注册类型
    private String zclx;
    // 企业机构类型 jglx
    private String qylxdm;
    // 注册地址 zcdz
    private String qyzs;
    // 法定代表人证件号码
    private String fddbrzjhm;
    // 注册资本 zczb
    private String zczj;
    // 注册币种 zcbz
    private String zczbbz;
    // 成立日期
    private String clrq;
    // 营业期限自 yyqxzi
    private String jyhyyqsrq;
    // 营业期限至 yyqxzhi
    private String jyhyyjzrq;
    // 核准日期
    private String hzrq;
    // 经营范围
    private String jyfw;
    // 所属行业 sshy
    private String hydm;
    // 企业状态 qyzt
    private String jyzt;
}
