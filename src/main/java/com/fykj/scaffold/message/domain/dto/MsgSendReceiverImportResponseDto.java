package com.fykj.scaffold.message.domain.dto;

import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.security.business.domain.entity.SysOss;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class MsgSendReceiverImportResponseDto {

    /**
     * 上传成功返回对象
     */
    private SysOss ossObj;

    /**
     * 消息接收者列表
     */
    private List<MsgRecord> receiverList;

    /**
     * 接收者是否超出限度
     */
    private Boolean receiverOverLimit;
}
