package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzCertificateCategory;
import com.fykj.scaffold.zyz.domain.params.ZyzCertificateCategoryParams;
import com.fykj.scaffold.zyz.service.IZyzCertificateCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 活动证书-分类表
 * <p>
 * API前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-03
 */
@RestController
@RequestMapping("/api/zyz/certificate/category")
@Api(tags = "活动证书分类API接口")
public class ZyzCertificateCategoryApiController {

    @Autowired
    private IZyzCertificateCategoryService zyzCertificateCategoryService;

    @GetMapping("/all")
    @ApiOperation(value = "查询活动证书全部")
    public JsonResult<List<ZyzCertificateCategory>> all() {
        return new JsonResult<>(zyzCertificateCategoryService.list());
    }

    @GetMapping("/list")
    @ApiOperation(value = "分页查询活动证书分类列表")
    public JsonResult<List<ZyzCertificateCategory>> list(ZyzCertificateCategoryParams params) {
        // 使用XML实现的分页查询
        List<ZyzCertificateCategory> page = zyzCertificateCategoryService.list(params);
        return new JsonResult<>(page);
    }

} 
