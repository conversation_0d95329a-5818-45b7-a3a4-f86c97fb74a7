package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncSpot;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.service.IZyzSyncSpotService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_dep.SyncDep;
import com.fykj.volunteer.sync.util.domain.sync_spot.SyncSpot;
import com.fykj.volunteer.sync.util.domain.sync_spot.SyncSpotResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;

/**
 * 职能部门同步
 */
@Component
public class SpotSyncer {

    @Autowired
    private IZyzSyncSpotService spotService;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog sync(long spotId, long syncBillId) {
        ZyzSyncSpot spot = spotService.getById(spotId);
        //新闻审核通过，且处于待同步状态需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(spot.getIsSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }

        SyncSpot syncSpot = new SyncSpot();
        if (StringUtil.isNotEmpty(spot.getSyncId())) {
            syncSpot.setSaveType(SyncDep.SaveType.MODIFY);
            syncSpot.setPsId(spot.getSyncId());
        } else {
            syncSpot.setSaveType(SyncDep.SaveType.ADD);
        }

        BeanUtil.copyProperties(spot, syncSpot);
        syncSpot.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(spot.getPicLogoUrl())));
        syncSpot.setUniCode(String.valueOf(spot.getId()));
        SyncSpotResult result = ApiWebService.syncSpot(syncSpot);

        spot.setSyncRemark(result.getMsg());
        spot.setSyncTime(LocalDateTime.now());
        //成功
        if ("同步成功".equals(result.getMsg())) {
            spot.setIsSync(Cons.PlatformSyncState.SUCCESS);
            spot.setSyncId(result.getPsId());
        }
        //其他一律失败
        else {
            spot.setIsSync(Cons.PlatformSyncState.FAILURE);
        }
        spotService.updateById(spot);

        //记录日志
        return logService.buildSyncLog(Cons.PlatformSyncBiz.SPOT_SYNC, result.getCode(), result.getMsg()
                , syncBillId, spot.getId(), spot.getName()
                , JSONUtil.toJsonStr(syncSpot), JSONUtil.toJsonStr(result));
    }
}
