package com.fykj.volunteer.sync.util.domain.cms;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <msgs>
 *   <info>
 *     <info>
 *       <code>SUCCESS</code>
 *       <msg>同步成功</msg>
 *       <uniqueCode>12345551234</uniqueCode>
 *       <spcode>190024</spcode>
 *     </info>
 *   </info>
 * </msgs>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncCmsResult {
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniqueCcode;
    /**
     * 消息
     */
    private String msg;
    /**
     * 业务状态码
     */
    private String spcode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "info")
    public static class Info {
        private List<SyncCmsResult> info;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "msgs")
    public static class Msgs {
        private List<Info> info;
    }
}
