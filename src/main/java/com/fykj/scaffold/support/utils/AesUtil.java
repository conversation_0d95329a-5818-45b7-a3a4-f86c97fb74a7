package com.fykj.scaffold.support.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

@UtilityClass
@Slf4j
public class AesUtil {
    /**
     * 加密方法
     *
     * @param data 要加密的数据
     * @param key  加密key
     * @return 加密的结果
     * @throws Exception
     */
    public static String encrypt(String data, String key) {
        try {
            //"算法/模式/补码方式"NoPadding PkcsPadding
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            int blockSize = cipher.getBlockSize();
            byte[] dataBytes = data.getBytes();
            int plaintextLength = dataBytes.length;
            if (plaintextLength % blockSize != 0) {
                plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
            }
            byte[] plaintext = new byte[plaintextLength];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(key.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, keyspec, ivspec);
            byte[] encrypted = cipher.doFinal(plaintext);
            return Base64.encode(encrypted);
        } catch (Exception e) {
            log.error("Aes加密失败", e);
            throw new IllegalArgumentException("Aes加密失败");
        }
    }

    /**
     * 解密方法
     *
     * @param data 要解密的数据
     * @param key  解密key
     * @return 解密的结果
     * @throws Exception
     */
    public static String decrypt(String data, String key) {
        try {
            byte[] encrypted1 = Base64.decode(data);
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(key.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);
            byte[] original = cipher.doFinal(encrypted1);
            return new String(original, StandardCharsets.UTF_8).trim();
        } catch (Exception e) {
            log.error("Aes解码失败", e);
            throw new IllegalArgumentException("Aes解码失败");
        }
    }

    public static String decryptZeroPadding(String data, String key) {
        try {
            // 创建 AES 实例，使用 CBC 模式和 ZeroPadding 填充
            AES aes = new AES(Mode.CBC, Padding.ZeroPadding,
                    key.getBytes(StandardCharsets.UTF_8),
                    key.getBytes(StandardCharsets.UTF_8));
            // 解密 Base64 编码的数据
            return aes.decryptStr(data);
        } catch (Exception e) {
            log.error("Aes解码失败", e);
            throw new IllegalArgumentException("Aes解码失败");
        }
    }
}