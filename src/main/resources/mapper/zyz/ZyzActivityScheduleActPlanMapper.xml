<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityScheduleActPlanMapper">
    <select id="getNeedSyncRecords" resultType="java.lang.Long">
        SELECT p.id
        FROM zyz_activity_schedule_act_plan p
        JOIN zyz_activity a on a.id=p.activity_id
        JOIN zyz_activity_schedule_detail d on p.schedule_detail_id=d.id
        WHERE p.is_deleted = 0
          AND a.is_sync='sync_success' AND d.pre_id is not null
          AND (p.sync = 'sync_wait' OR ( p.sync = 'sync_failure' AND p.sync_time &gt;= date_sub(now(),interval 3 day)))
    </select>
</mapper>
