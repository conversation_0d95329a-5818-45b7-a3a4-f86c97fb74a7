package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用客户端授权详情
 *
 * <AUTHOR>
 * @date 2021-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("oauth_client_details")
public class AppClient {
    private static final long serialVersionUID = 1L;

    /**
     * AppId
     */
    @TableField("client_id")
    @ApiModelProperty(value = "AppId")
    private String clientId;

    /**
     * 资源id
     */
    @TableField("resource_ids")
    @ApiModelProperty(value = "资源id")
    private String resourceIds;

    /**
     * AppId
     */
    @TableField("client_secret")
    @ApiModelProperty(value = "客户端密码")
    private String clientSecret;

    /**
     * 授权范围（数据字典code）
     */
    @TableField("scope")
    @ApiModelProperty(value = "授权范围")
    private String scope;

    /**
     * 授权类型（数据字典code 逗号拼接）
     */
    @TableField("authorized_grant_types")
    @ApiModelProperty(value = "授权类型")
    private String authorizedGrantTypes;

    /**
     * 客户端跳转地址
     */
    @TableField("web_server_redirect_uri")
    @ApiModelProperty(value = "客户端跳转地址")
    private String webServerRedirectUri;

    /**
     * 授权集合
     */
    @TableField("authorities")
    @ApiModelProperty(value = "授权集合")
    private String authorities;

    /**
     * 访问token有效时长（秒）
     */
    @TableField("access_token_validity")
    @ApiModelProperty(value = "访问token有效时长（秒）")
    private Integer accessTokenValidity;

    /**
     * 刷新token有效时长（秒）
     */
    @TableField("refresh_token_validity")
    @ApiModelProperty(value = "刷新token有效时长（秒）")
    private Integer refreshTokenValidity;

    /**
     * 附加信息
     */
    @TableField("additional_information")
    @ApiModelProperty(value = "附加信息")
    private String additionalInformation;

    /**
     * 是否自动授权
     */
    @TableField("autoapprove")
    @ApiModelProperty(value = "是否自动授权")
    private String autoapprove;

    @TableField(exist = false)
    private List<String> types = new ArrayList<>();

}
