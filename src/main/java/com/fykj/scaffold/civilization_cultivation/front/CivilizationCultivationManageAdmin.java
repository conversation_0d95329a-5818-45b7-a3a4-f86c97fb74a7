package com.fykj.scaffold.civilization_cultivation.front;

import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationCollectParams;
import com.fykj.scaffold.civilization_cultivation.service.ICivilizationCultivationCollectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import static fykj.microservice.core.base.AbstractController.OK;


@RestController
@RequestMapping("/admin/manage/civilization_cultivation")
@Api(tags = "文明培育征集--需要登录")
public class CivilizationCultivationManageAdmin {

    @Autowired
    private ICivilizationCultivationCollectService ccCollectService;

    @ApiOperation("征集提交")
    @PostMapping({"/collectSubmit"})
    public Result collectSubmit(@RequestBody CivilizationCultivationCollect collectInfo) {
        ccCollectService.collectSubmit(collectInfo);
        return OK;
    }

    @ApiOperation("我的征集提交分页查询")
    @PostMapping({"/myCollectSubmit"})
    public Result myCollectSubmit(@RequestBody CivilizationCultivationCollectParams params) {
        return new JsonResult<>(ccCollectService.myCollectSubmit(params));
    }

    @ApiOperation("我的征集提交详情")
    @GetMapping({"/getDetail"})
    public Result getDetail(@RequestParam Long id) {
        return new JsonResult<>(ccCollectService.getDetail(id));
    }

    @ApiOperation("我的征集撤回")
    @GetMapping({"/collectCallback"})
    public Result collectCallback(@RequestParam Long id) {
        ccCollectService.collectCallback(id);
        return OK;
    }

    @ApiOperation("我的征集删除")
    @GetMapping({"/collectRemove"})
    public Result collectRemove(@RequestParam Long id) {
        ccCollectService.collectRemove(id);
        return OK;
    }
}
