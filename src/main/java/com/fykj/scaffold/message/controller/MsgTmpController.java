package com.fykj.scaffold.message.controller;


import com.fykj.scaffold.message.domain.entity.MsgTmp;
import com.fykj.scaffold.message.domain.param.MsgTmpParams;
import com.fykj.scaffold.message.service.impl.MsgTmpServiceImpl;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@RestController
@RequestMapping("/admin/msg_tmp")
@Api(tags = "系统通知模板")
public class MsgTmpController extends BaseController<MsgTmpServiceImpl, MsgTmp, MsgTmpParams> {

    @ApiOperation(value = "校验code是否重复 - true(重复)")
    @GetMapping(value = "/checkCode")
    public JsonResult<Boolean> checkCode(@RequestParam(value = "id", required = false) Long id, @RequestParam("code") String code) {
        return new JsonResult<>(baseService.checkCode(id, code));
    }

    @ApiOperation("已启用的消息列表")
    @GetMapping("/listOfEnable")
    public JsonResult<List<MsgTmp>> listOfEnable() {
        return new JsonResult<>(baseService.listOfEnable());
    }
}
