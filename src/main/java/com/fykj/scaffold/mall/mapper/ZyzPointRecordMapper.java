package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.entity.ZyzPointRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointRecordParams;
import org.apache.ibatis.annotations.Param;

/**
 * 积分商城-积分历史记录
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface ZyzPointRecordMapper extends BaseMapper<ZyzPointRecord> {
    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    @DataFilter
    IPage<ZyzPointRecord> getPage(IPage<ZyzPointRecord> page, @Param("params") ZyzPointRecordParams params);

    IPage<ZyzPointRecord> pageForApi(IPage<ZyzPointRecord> page, @Param("params") ZyzPointRecordParams params);

}
