package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.mall.domain.dto.PointContributePersonDto;
import com.fykj.scaffold.mall.domain.dto.PointContributeSumDto;
import com.fykj.scaffold.mall.domain.entity.ZyzPointContributeRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointContributeRecordParams;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.IBaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分商城-积分捐赠记录
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface IZyzPointContributeRecordService extends IService<ZyzPointContributeRecord>, IBaseService<ZyzPointContributeRecord> {

    /**
     * 分页查询--后端页面
     */
    IPage<ZyzPointContributeRecord> getPage(ZyzPointContributeRecordParams params);

    /**
     * 获取捐赠记录统计
     * @param params
     * @return
     */
    PointContributeSumDto getSum(ZyzPointContributeRecordParams params);

    /**
     * 积分捐赠
     */
    boolean savePointContribute(BigDecimal contributePoint, String orgCode, String orgName);

    /**
     * 积分捐赠名单分页查询
     * @param params
     * @return
     */
    IPage<PointContributePersonDto> pageForPointContributePerson(BaseParams params);

    /**
     * 获取积分捐赠相关统计（累计捐赠总积分、累计捐赠人数）
     * @return
     */
    List<Object> getContributeSum();
}

