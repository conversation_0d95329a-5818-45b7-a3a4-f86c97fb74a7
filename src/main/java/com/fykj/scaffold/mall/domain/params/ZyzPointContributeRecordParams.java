package com.fykj.scaffold.mall.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 积分商城-积分捐赠记录
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ZyzPointContributeRecordParams extends BaseParams {
    private String name;
    private String certificateId;
    private BigDecimal maxPoint;
    private BigDecimal minPoint;

    @JsonFormat(pattern = Cons.DATE_FORMAT)
    @DateTimeFormat(pattern = Cons.DATE_FORMAT)
    private LocalDate start;

    @JsonFormat(pattern = Cons.DATE_FORMAT)
    @DateTimeFormat(pattern = Cons.DATE_FORMAT)
    private LocalDate end;
}
