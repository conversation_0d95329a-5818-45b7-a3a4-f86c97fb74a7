package com.fykj.scaffold.zyz.controller.pc;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectParams;
import com.fykj.scaffold.zyz.service.*;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 项目表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/api/project")
@Api(tags = "PC公益伙伴接口--无需要登录")
public class PcProjectController {


    @Autowired
    private IZyzProjectService projectService;

    @Autowired
    private IZyzProjectTypeService projectTypeService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private INewsService newsService;

    @Autowired
    private IZyzProjectDockingService projectDockingService;

    @Autowired
    private IZyzProjectCompanyService zyzProjectCompanyService;


    @ApiOperation(value = "获取项目通过id")
    @GetMapping(value = "/getProjectById")
    public Result getProjectById(Long id) {
        ZyzProject res = projectService.getProjectById(id);
        //小程序/pc端数据安全，身份证，手机号、姓名 不要全部展示出来   示例：姓名：李**  手机号：130****253  身份证号码不要返回
        DesensitiseUtil.desensitise(res);
        res.setContactIdCard(null);
        res.setContactName(displayNameWithMask(res.getContactName()));
        return new JsonResult<>(res);
    }

    @ApiOperation("获取项目--分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzProject>> getPagesForPc(@RequestBody ZyzProjectParams params) {
        IPage<ZyzProject> iPage = projectService.getPagesForPc(params);
        DictTransUtil.trans(iPage.getRecords());
        //小程序/pc端数据安全，身份证，手机号、姓名 不要全部展示出来   示例：姓名：李**  手机号：130****253  身份证号码不要返回
        DesensitiseUtil.desensitise(iPage.getRecords());
        iPage.getRecords().forEach(x -> {
            x.setContactIdCard(null);
            x.setContactName(displayNameWithMask(x.getContactName()));
        });
        return new JsonResult<>(iPage);
    }

    private String displayNameWithMask(String fullName) {
        return fullName.replaceAll("(?<=.{1}).", "*");
    }

    @ApiOperation("获取项目类型列表")
    @GetMapping("/getTypeList")
    public JsonResult<List<ZyzProjectType>> getTypeList(Long projectId) {
        return new JsonResult<>(projectTypeService.getTypeList(projectId));
    }

    @ApiOperation("获取项目关联活动列表")
    @GetMapping("/getActivityListByProjectId")
    public JsonResult<List<ZyzActivity>> getActivityListByProjectId(Long projectId) {
        return new JsonResult<>(activityService.listForProjectActivity(projectId));
    }

    @ApiOperation("获取项目关联新闻列表")
    @GetMapping("/getNewsListByProjectId")
    public JsonResult<List<News>> getNewsListByProjectId(Long projectId) {
        return new JsonResult<>(newsService.getNewsByProjectId(projectId));
    }

    @ApiOperation("获取项目对接记录列表")
    @GetMapping("/getDockingListByProjectId")
    public JsonResult<List<ZyzProjectDocking>> getDockingListByProjectId(Long projectId) {
        return new JsonResult<>(projectDockingService.getListByProjectId(projectId));
    }


    @ApiOperation("年度爱心企业接口")
    @GetMapping("/findDockingCompanyListByYear")
    public JsonResult<List<ZyzProjectCompany>> findLoveEnterpriseByYear(@RequestParam Integer year) {
        List<ZyzProjectCompany> result = zyzProjectCompanyService.lambdaQuery()
                .like(ZyzProjectCompany::getLoveEnterpriseYears, year)
                .eq(ZyzProjectCompany::getStatus, true)
                .list();
        return new JsonResult<>(result);
    }


}
