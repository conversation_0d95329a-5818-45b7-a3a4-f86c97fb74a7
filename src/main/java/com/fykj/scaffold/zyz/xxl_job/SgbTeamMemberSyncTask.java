package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncPushService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * SGB团队成员同步定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbTeamMemberSyncTask {

    @Autowired
    private IZyzVolunteerTeamService vtService;

    @Autowired
    private ISgbSyncPushService sgbSyncPushService;

    @XxlJob("SGB_Team_Member_Sync_Handler")
    public void sgbTeamMemberSyncJobHandler() {
        XxlJobHelper.log("SGB_Team_Member_Sync_Handler-Job, START!");

        // SGB团队成员同步逻辑
        List<Long> sgbVtIds = vtService.getSgbNeedSync();
        if (CollectionUtil.isNotEmpty(sgbVtIds)) {
            try {
                sgbSyncPushService.syncTeamVolunteerBatch(sgbVtIds);
            } catch (Exception e) {
                log.error("SGB志愿者加入团队批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }

        XxlJobHelper.log("SGB_Team_Member_Sync_Handler-Job, END!");
    }
}
