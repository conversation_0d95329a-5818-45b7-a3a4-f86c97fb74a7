package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.security.business.domain.entity.UserWriteOff;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.security.business.service.IUserWriteOffService;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 用户注销删除用户定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserWriteOffFinalRemoveTask {

    @Autowired
    private IUserService userService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IUserWriteOffService writeOffService;

    @XxlJob("User_Write_Off_Final_Remove_Handler")
    public void zsqUserSyncJobHandler() {
        XxlJobHelper.log("User_Write_Off_Final_Remove_Handler-Job, START!");
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        List<UserWriteOff> needRemove = writeOffService.lambdaQuery()
                .eq(UserWriteOff::getCancelApply, false)
                        .eq(UserWriteOff::getPlanOrRealAccountLinkInfoRemoveDate, yesterday)
                                .eq(UserWriteOff::getRemoved, false)
                                        .list();
        if (needRemove == null || needRemove.isEmpty()) {
            log.info("User_Write_Off_Final_Remove_Handler-Job, no need remove user!");
            return;
        }
        writeOffService.writeOffFinal(needRemove);
        XxlJobHelper.log("User_Write_Off_Final_Remove_Handler-Job, END!");
    }
}
