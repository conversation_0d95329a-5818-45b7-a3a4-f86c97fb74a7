package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 刷新用户活跃度
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VolunteerLivelyRefreshTask {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @XxlJob("Volunteer_Lively_Refresh_Handler")
    public void refreshVolunteerLivelyJobHandler() {
        XxlJobHelper.log("Volunteer_Lively_Refresh_Handler-Job, START!");
        try {
            volunteerService.refreshVolunteerLively();
        } catch (Exception e) {
            log.error("志愿者是否活跃刷新任务失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Volunteer_Lively_Refresh_Handler-Job, END!");
    }
}
