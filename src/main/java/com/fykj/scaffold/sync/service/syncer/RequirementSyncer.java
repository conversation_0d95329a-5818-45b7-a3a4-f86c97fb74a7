package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDep;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncDepService;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzRequirementService;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.requirement.SyncRequirement;
import com.fykj.volunteer.sync.util.domain.requirement.SyncRequirementResult;
import com.fykj.volunteer.sync.util.domain.res.SyncRes;
import exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 需求同步
 */
@Component
public class RequirementSyncer {

    @Autowired
    private IZyzSyncLogService logService;

    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzSyncDepService depService;

    //需求类型
    private final static Map<String, String> REQ_TYPE_MAP = new HashMap<>();

    static {
        REQ_TYPE_MAP.put("RT_project", SyncRequirement.ReqType.PROJECT_SERVICE);
        REQ_TYPE_MAP.put("RT_specialist", SyncRequirement.ReqType.TEAM_PRO);
        REQ_TYPE_MAP.put("RT_team", SyncRequirement.ReqType.TEAM_PRO);
        REQ_TYPE_MAP.put("RT_pavilion", SyncRequirement.ReqType.AREA);
    }

    public ZyzSyncLog sync(long requirementId, long syncBillId, String saveType) {
        ZyzRequirement requirement = requirementService.getById(requirementId);
        //处于待同步,且审核同步才需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(requirement.getSync()) && (StringUtils.isEmpty(saveType) || !SyncRequirement.SaveType.MODIFY.equals(saveType))) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }
        if (!ZyzCons.REQUIREMENT_STATUS_RAS_DOCKING.equals(requirement.getStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此需求审核未通过，无法同步");
        }
        SysOrg org = orgService.getByCode(requirement.getPublishOrgCode());
        SyncRequirement syncRequirement = new SyncRequirement();
        syncRequirement.setUniCode(String.valueOf(requirement.getId()));
        boolean teamPublish = requirement.getTeamPublish();
        syncRequirement.setPubType(teamPublish ? SyncRequirement.PubType.TEAM : SyncRequirement.PubType.BASE);
        String pubId;
        if (teamPublish) {
            ZyzTeam team = teamService.getById(requirement.getTeamId());
            if (!Cons.PlatformSyncState.SUCCESS.equals(team.getIsSync()) || StringUtil.isEmpty(team.getSyncId())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "由于该需求发布团队未同步成功，所以无法同步该需求");
            }
            pubId = team.getSyncId();
        } else {
            pubId = org.getSyncId();
            if (StringUtils.isEmpty(pubId)) {
                pubId = org.getDepId();
                if (StringUtil.isEmpty(org.getDepId())) {
                    throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到该需求发布组织的deptId，无法同步该需求");
                }
                ZyzSyncDep dep = depService.getByDepId(org.getDepId());
                if (ObjectUtil.isEmpty(dep)) {
                    throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到该需求发布组织的dept，无法同步该需求");
                }
                syncRequirement.setPubType(SyncRes.PubType.DEP);
            }
        }
        syncRequirement.setPubId(pubId);
        syncRequirement.setServiceType(requirement.getBelongFieldEnd());
        syncRequirement.setReqType(REQ_TYPE_MAP.get(requirement.getType()));
        syncRequirement.setTitle(requirement.getName());
        syncRequirement.setDescription(requirement.getDetail());
        syncRequirement.setContact(requirement.getContactPerson());
        syncRequirement.setContactNo(requirement.getContactPhone());
        syncRequirement.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(requirement.getPicture())));
        syncRequirement.setSaveType(StringUtils.isEmpty(saveType) ? SyncRequirement.SaveType.ADD : saveType);
        SyncRequirementResult result = ApiWebService.syncRequirement(syncRequirement);
        String resultCode = result.getCode();
        String msg = result.getMsg();

        requirement.setSyncTime(LocalDateTime.now());
        requirement.setSyncRemark(msg);
        //成功
        if ("同步成功".equals(msg)) {
            requirement.setSyncId(result.getReqId());
            requirement.setSync(Cons.PlatformSyncState.SUCCESS);
        }
        else if (msg.contains("唯一号已存在")) {
            if (StringUtil.isNotEmpty(result.getReqId())) {
                requirement.setSyncId(result.getReqId());
            }
            requirement.setSync(Cons.PlatformSyncState.SUCCESS);
            requirement.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            requirement.setSync(Cons.PlatformSyncState.FAILURE);
        }

        //启用单独事务处理日志
        ZyzSyncLog log = logService
                .buildSyncLog(Cons.PlatformSyncBiz.REQUIREMENT_SYNC, resultCode, msg
                        , syncBillId, requirementId, requirement.getName()
                        , JSONUtil.toJsonStr(syncRequirement), JSONUtil.toJsonStr(result));

        //回写业务
        requirementService.updateById(requirement);

        return log;
    }
}
