package com.fykj.scaffold.sgb_docking.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 市平台信息同步日志-查询参数
 *
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("市平台信息同步日志-查询参数")
public class SgbSyncLogParams extends BaseParams {

    private static final long serialVersionUID = 867331168334333309L;

    @ApiModelProperty("同步业务类型")
    @MatchType(value = QueryType.EQ)
    private String bizType;

    @ApiModelProperty("业务表ID")
    @MatchType(value = QueryType.EQ)
    private Long bizId;

    @ApiModelProperty("市平台返回消息，模糊匹配")
    @MatchType(value = QueryType.LIKE)
    private String message;

    @ApiModelProperty("返回code筛选")
    @MatchType(value = QueryType.EQ)
    private String resultCode;

    @ApiModelProperty("同步时间开始")
    @JsonFormat(pattern = DATETIME_FORMAT)
    @MatchType(value = QueryType.GE, fieldName = {"syncTime"})
    private LocalDateTime syncTimeStart;

    @ApiModelProperty("同步时间结束")
    @JsonFormat(pattern = DATETIME_FORMAT)
    @MatchType(value = QueryType.LE, fieldName = {"syncTime"})
    private LocalDateTime syncTimeEnd;
}
