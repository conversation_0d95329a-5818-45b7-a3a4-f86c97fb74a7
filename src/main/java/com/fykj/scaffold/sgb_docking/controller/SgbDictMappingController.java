package com.fykj.scaffold.sgb_docking.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbDictMapping;
import com.fykj.scaffold.sgb_docking.domain.params.SgbDictMappingParams;
import com.fykj.scaffold.sgb_docking.domain.vo.SgbDictMappingSysTypeVo;
import com.fykj.scaffold.sgb_docking.service.ISgbDictMappingService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统字典值与三方字典值映射 控制器
 */
@RestController
@RequestMapping("/admin/sgb/dict-mapping")
@Api(tags = "系统字典值与三方字典值映射")
public class SgbDictMappingController
        extends BaseController<ISgbDictMappingService, SgbDictMapping, SgbDictMappingParams> {

    @ApiOperation("分页查询")
    @PostMapping({"/pages"})
    public JsonResult<IPage<SgbDictMapping>> list(
            @RequestBody(required = false) SgbDictMappingParams params) {
        IPage<SgbDictMapping> result = this.baseService.page(params);
        // 字典翻译
        DictTransUtil.trans(result.getRecords());

        return new JsonResult<>(result);
    }

    @ApiOperation("获取系统字典类型列表")
    @GetMapping("/sys-types")
    public JsonResult<List<SgbDictMappingSysTypeVo>> getSysTypeList() {
        List<SgbDictMappingSysTypeVo> result = this.baseService.getSysTypeList();
        return new JsonResult<>(result);
    }

    @SysLogMethod("批量保存或更新字典映射")
    @ApiOperation("批量保存或更新字典映射")
    @PostMapping("/batch-save")
    public Result saveOrUpdateBatch(@RequestBody @Valid List<SgbDictMapping> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            throw new BusinessException(ResultCode.NOT_VALID, "数据列表不能为空");
        }
        boolean result = this.baseService.saveOrUpdateBatch(entityList);
        return result ? new Result() : new Result(ResultCode.FAIL);
    }

}