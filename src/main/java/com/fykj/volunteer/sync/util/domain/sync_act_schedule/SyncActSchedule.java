package com.fykj.volunteer.sync.util.domain.sync_act_schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShow;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActSchedule {

    /**
     * 唯一标识
     * 必填
     */
    private String uniCode;
    /**
     * 阵地计划的唯一标识
     * 必填
     */
    private String pbId;
    /**
     * 计划主题
     * 必填
     */
    private String title;
    /**
     * 计划数值
     * 必填
     */
    private String typeData;

    /**
     * 活动预告数据列表
     * 必填
     */
    private String previewData;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "actschedule")
    public static class ActSchedule{
        private List<SyncActSchedule> info;
    }
    public String buildXml() {
        SyncActSchedule.ActSchedule schedule = new SyncActSchedule.ActSchedule();
        schedule.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(schedule);
    }
}
