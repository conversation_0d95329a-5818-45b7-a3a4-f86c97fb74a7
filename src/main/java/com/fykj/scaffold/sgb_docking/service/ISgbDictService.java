package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbDict;
import fykj.microservice.core.base.IBaseService;
import fykj.microservice.core.beans.vo.IdTextVo;

import java.util.List;

/**
 * 苏州志愿者平台字典数据服务接口
 */
public interface ISgbDictService extends IBaseService<SgbDict> {
    
    /**
     * 同步字典数据
     */
    void syncDict();
    
    /**
     * 同步指定类型的字典数据
     * @param dictType 字典类型
     */
    void syncDict(String dictType);
    
    /**
     * 根据字典编号获取字典数据
     * @param dictCode 字典编号
     * @return 字典数据
     */
    SgbDict getByDictCode(String dictCode);
    
    /**
     * 根据字典类型获取字典数据列表
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<SgbDict> listByDictType(String dictType);
    
    /**
     * 获取所有字典类型
     *
     * @return 字典类型列表，使用IdTextVo格式（id=type, text=type）
     */
    List<IdTextVo> getAllDictTypes();

    /**
     * 根据字典类型编码获取字典列表
     *
     * @param type 字典类型编码
     * @return 字典列表，使用IdTextVo格式（id=value, text=label）
     */
    List<IdTextVo> getIdTextByType(String type);
}