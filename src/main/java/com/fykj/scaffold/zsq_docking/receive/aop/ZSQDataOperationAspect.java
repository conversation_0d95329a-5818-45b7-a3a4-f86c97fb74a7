package com.fykj.scaffold.zsq_docking.receive.aop;

import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingActivityRecord;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingActivityRecordService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingLogService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingNewsRecordService;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ActivityDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.NewsDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQActivityInfoDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.Result;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;

@Aspect
@Component
@Slf4j
public class ZSQDataOperationAspect {

    @Autowired
    private IZSQDockingNewsRecordService newsDockingRecordService;

    @Autowired
    private IZSQDockingActivityRecordService activityDockingRecordService;

    @Autowired
    private IZSQDockingLogService logService;

    // 正常返回时记录日志
    @AfterReturning(pointcut = "@annotation(ZSQDataOperationLog)", returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, Result result) {
        logOperation(joinPoint, result);
    }

    // 抛出异常时记录日志
    @AfterThrowing(pointcut = "@annotation(ZSQDataOperationLog)", throwing = "ex")
    public void logAfterThrowing(JoinPoint joinPoint, Exception ex) {
        // 构造一个失败的 Result
        Result result = Result.get(false, ex.getMessage());
        logOperation(joinPoint, result);
    }

    // 提取公共日志记录逻辑
    private void logOperation(JoinPoint joinPoint, Result result) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        Object dockingInfo = null;
        Object data = null;
        Object dockingRecord = null;
        for (int i = 0; i < paramNames.length; i++) {
            String paramName = paramNames[i];
            if (ZSQ_DOCKING_RECEIVE_LOG_PARAM_DOCKING_INFO.equals(paramName)) {
                dockingInfo = args[i];
            } else if (ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA.equals(paramName)) {
                data = args[i];
            } else if (ZSQ_DOCKING_RECEIVE_LOG_PARAM_DOCKING_RECORD.equals(paramName)) {
                dockingRecord = args[i];
            }
        }
        if (dockingInfo == null) {
            result = Result.get(result.isSuccess(), result.isSuccess() ? "AOP未获取到从与知社区对接接收到的对接信息，请技术排查原因！" : result.getMsg());
            String declaringType = joinPoint.getSignature().getDeclaringTypeName();
            if (ZSQ_DOCKING_RECEIVE_DEAL_SERVICE_NEWS.equals(declaringType)) {
                dockingInfo = new NewsDockingDto();
            }
            if (ZSQ_DOCKING_RECEIVE_DEAL_SERVICE_ACTIVITY.equals(declaringType)) {
                dockingInfo = new ActivityDockingDto();
            }
        }
        if (dockingRecord instanceof ZSQDockingNewsRecord) {
            try {
                newsDockingRecordService.updateDockingRecord((ZSQDockingNewsRecord)dockingRecord, result);
            } catch (Exception e) {
                log.error("知社区新闻对接-新闻数据接收-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
        }
        if (dockingInfo instanceof NewsDockingDto) {
            try {
                logService.saveNewsReceiveLog((NewsDockingDto)dockingInfo, result, (ZSQNewsInfoDto)data, dockingRecord != null ? ((ZSQDockingNewsRecord)dockingRecord).getId() : null);
            } catch (Exception e) {
                log.error("知社区新闻对接-新闻数据接收-AOP保存新闻数据接收日志异常，异常原因：【{}】!", e.getMessage());
            }
        }

        if (dockingRecord instanceof ZSQDockingActivityRecord) {
            try {
                activityDockingRecordService.updateDockingRecord((ZSQDockingActivityRecord)dockingRecord, result);
            } catch (Exception e) {
                log.error("知社区活动对接-新闻数据接收-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
        }
        if (dockingInfo instanceof ActivityDockingDto) {
            try {
                logService.saveActivityReceiveLog((ActivityDockingDto)dockingInfo, result, (ZSQActivityInfoDto)data, dockingRecord != null ? ((ZSQDockingNewsRecord)dockingRecord).getId() : null);
            } catch (Exception e) {
                log.error("知社区活动对接-新闻数据接收-AOP保存新闻数据接收日志异常，异常原因：【{}】!", e.getMessage());
            }
        }
    }
}
