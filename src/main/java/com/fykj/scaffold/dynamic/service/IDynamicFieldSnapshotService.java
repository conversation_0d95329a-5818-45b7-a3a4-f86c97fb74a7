package com.fykj.scaffold.dynamic.service;

import com.fykj.scaffold.dynamic.domain.entity.DynamicFieldSnapshot;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 字段信息快照-服务类
 *
 * @date 2025-05-20
 */
public interface IDynamicFieldSnapshotService extends IBaseService<DynamicFieldSnapshot> {

    /**
     * 根据snapshotId和modelId查找字段list
     * @param snapshotId 快照id
     * @param moduleCode 模块code
     * @return 字段list
     */
    List<DynamicFieldSnapshot> getBySnapshotIdAndModuleCode(Long snapshotId, String moduleCode);

    /**
     * 根据snapshotId和modelId查找字段list(过滤系统原有字段
     * @param snapshotId 快照id
     * @param moduleCode 模块code
     * @return 字段list
     */
    List<DynamicFieldSnapshot> getBySnapshotIdAndModuleCodeForApi(Long snapshotId, String moduleCode);

}

