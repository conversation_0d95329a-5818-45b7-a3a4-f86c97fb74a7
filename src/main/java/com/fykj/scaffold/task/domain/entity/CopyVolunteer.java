package com.fykj.scaffold.task.domain.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * <AUTHOR>
 */
@Data
@Document(indexName = "copy_volunteer")
public class CopyVolunteer {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;

    @Field(type = FieldType.Text)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parameterId;

    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "身份证号")
    private String certificateId;

    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "志愿者姓名")
    private String name;

    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "电话")
    private String phone;
}
