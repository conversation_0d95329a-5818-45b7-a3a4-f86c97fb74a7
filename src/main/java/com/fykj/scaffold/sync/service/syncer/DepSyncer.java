package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDep;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncDepService;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_dep.SyncDep;
import com.fykj.volunteer.sync.util.domain.sync_dep.SyncDepResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;

import java.time.LocalDateTime;

/**
 * 职能部门同步
 */
@Component
public class DepSyncer {

    @Autowired
    private IZyzSyncDepService depService;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog sync(long depId, long syncBillId) {
        ZyzSyncDep dep = depService.getById(depId);
        //新闻审核通过，且处于待同步状态需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(dep.getIsSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }

        SyncDep syncDep = new SyncDep();
        syncDep.setSaveType(SyncDep.SaveType.ADD);
        BeanUtil.copyProperties(dep, syncDep);
        syncDep.setUniCode(dep.getDepId());
        syncDep.setName(dep.getDepName());
        syncDep.setTypeId(dep.getDepTypeId());
        syncDep.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(dep.getPicLogoUrl())));
        SyncDepResult result = ApiWebService.syncDep(syncDep);

        dep.setSyncTime(LocalDateTime.now());
        dep.setSyncRemark(result.getMsg());
        //成功
        if ("同步成功".equals(result.getMsg())) {
            dep.setIsSync(Cons.PlatformSyncState.SUCCESS);
            dep.setSyncId(result.getDpId());
        }
        //其他一律失败
        else {
            dep.setIsSync(Cons.PlatformSyncState.FAILURE);
        }
        depService.updateById(dep);

        //记录日志
        return logService.buildSyncLog(Cons.PlatformSyncBiz.DEP_SYNC, result.getCode(), result.getMsg()
                , syncBillId, dep.getId(), dep.getDepName()
                , JSONUtil.toJsonStr(syncDep), JSONUtil.toJsonStr(result));
    }
}
