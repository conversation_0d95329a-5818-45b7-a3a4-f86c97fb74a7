package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzSugAdvice;
import com.fykj.scaffold.zyz.domain.params.ZyzSugAdviceParams;
import com.fykj.scaffold.zyz.service.IZyzSugAdviceService;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 咨询建议-接口控制器
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@RestController
@RequestMapping("/admin/zyz/sugAdvice")
@Api(tags = "咨询建议-管理接口")
public class ZyzSugAdviceController {

    @Autowired
    private IZyzSugAdviceService baseService;

    @ApiOperation("根据id获取")
    @GetMapping
    public Result get(@RequestParam Long id) {
        return new JsonResult(this.baseService.getById(id));
    }

    @ApiOperation("咨询建议回复")
    @PostMapping({"/reply"})
    public Result reply(@RequestBody ZyzSugAdvice entity) {
        boolean result = baseService.reply(entity);
        return result ? new Result() : new Result(ResultCode.FAIL);
    }

    @ApiOperation("分页查询")
    @PostMapping({"/pages"})
    public JsonResult<IPage<ZyzSugAdvice>> list(@RequestBody(required = false) ZyzSugAdviceParams params) {
        IPage<ZyzSugAdvice> result = this.baseService.page(params);
        DictTransUtil.trans(result.getRecords());
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult(result);
    }

}
