package com.fykj.scaffold.mall.domain.params;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 星光闪耀适老化改造福申请表
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2024-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MallScoreGoodsElderApplyParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"goods_name", "volunteer_name", "volunteer_phone", "volunteer_certificate_id", "volunteer_address", "elder_name", "elder_phone", "elder_address", "elder_certificate_id", "elder_certificate_id"})
    private String keyWord;


    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime startTime;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime endTime;
}
