package com.fykj.scaffold.mall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsNumChange;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsNumChangeParams;
import com.fykj.scaffold.mall.mapper.MallScoreGoodsNumChangeMapper;
import com.fykj.scaffold.mall.service.IMallScoreGoodsNumChangeService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallScoreGoodsNumChangeServiceImpl extends BaseServiceImpl<MallScoreGoodsNumChangeMapper, MallScoreGoodsNumChange> implements IMallScoreGoodsNumChangeService {

    @Override
    public IPage<MallScoreGoodsNumChange> getPage(MallScoreGoodsNumChangeParams params) {
        if (params == null) {
            params = new MallScoreGoodsNumChangeParams();
        }
        IPage<MallScoreGoodsNumChange> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }
}