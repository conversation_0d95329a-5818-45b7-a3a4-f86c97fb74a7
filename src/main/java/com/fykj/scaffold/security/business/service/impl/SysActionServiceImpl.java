package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.domain.entity.Resource;
import com.fykj.scaffold.security.business.domain.entity.SysAction;
import com.fykj.scaffold.security.business.mapper.SysActionMapper;
import com.fykj.scaffold.security.business.service.ISysActionService;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 系统资源-功能操作
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysActionServiceImpl extends BaseServiceImpl<SysActionMapper, SysAction> implements ISysActionService {
    @Override
    public boolean isExist(String code, Long id) {
        return lambdaQuery().eq(SysAction::getActionCode, code).ne(id != null, BaseEntity::getId, id).count() > 0;
    }

    @Override
    public List<SysAction> findActionByUserIdAndRoleIds(long userId, List<Long> roleIds) {
        return baseMapper.findActionByUserIdAndRoleIds(userId, roleIds);
    }
}
