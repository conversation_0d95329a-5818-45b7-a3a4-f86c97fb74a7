package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamAuditLog;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamAuditLogParams;
import com.fykj.scaffold.zyz.service.IZyzTeamAuditLogService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static constants.Mark.COMMA;

/**
 * 审核操作记录表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-14
 */
@RestController
@RequestMapping("/admin/team/audit/log")
public class ZyzTeamAuditLogController extends BaseController<IZyzTeamAuditLogService, ZyzTeamAuditLog, ZyzTeamAuditLogParams> {


    @AuditLog("团队审核--批量")
    @ApiOperation("批量审核")
    @GetMapping("/audit")
    public Result audit(String teamIds, String remark, Boolean pass) {
        List<Long> idList = Arrays.stream(teamIds.split(COMMA)).map(Long::valueOf).collect(Collectors.toList());
        baseService.audit(idList, remark, pass);
        return OK;
    }

    @ApiOperation("获取审核操作记录")
    @GetMapping("/getLogsByTeamId")
    public JsonResult<List<ZyzTeamAuditLog>> getLogsByTeamId(Long teamId) {
        List<ZyzTeamAuditLog> res = baseService.getLogsByTeamId(teamId);
        DictTransUtil.trans(res);
        return new JsonResult<>(res);
    }

//    @ApiOperation("获取团队审核--分页查询")
//    @PostMapping("/getPages")
//    public JsonResult<IPage<ZyzTeamAuditLog>> getMyPages(@RequestBody ZyzVolunteerTeamAuditParams params) {
//        return new JsonResult<>(baseService.getPages(params));
//    }
}
