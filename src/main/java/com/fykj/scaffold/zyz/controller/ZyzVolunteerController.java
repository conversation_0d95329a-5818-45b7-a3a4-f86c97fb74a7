package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.VolunteerImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerParams;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 志愿者表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-01
 */
@RestController
@RequestMapping("/admin/zyz/volunteer")
@Api(tags = "志愿者接口")
public class ZyzVolunteerController extends BaseController<IZyzVolunteerService, ZyzVolunteer, ZyzVolunteerParams> {


    @AuditLog("志愿者-删除")
    @Override
    public Result removeByIds(String ids) {
        return super.removeByIds(ids);
    }

    /**
     * 员工Excel模板下载
     */
    @ApiOperation("员工Excel模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/volunteer_template.xlsx");
    }

    @AuditLog("通过Excel批量导入志愿者信息")
    @ApiOperation("导入")
    @PostMapping(value = "/dataImport")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<VolunteerImportDto> failureList = baseService.dataImport(excel);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "volunteer_fill_template.xlsx", VolunteerImportDto.class);
        }
    }


    @ApiOperation("求服务时间总长")
    @PostMapping("/getServiceLongCount")
    public Result getServiceLongCount(@RequestBody ZyzVolunteerParams params) {
        return new JsonResult<>(baseService.getServiceLongCount(params));
    }


    @AuditLog("冻结志愿者账号")
    @ApiOperation("冻结")
    @GetMapping("/freeze")
    public Result freeze(Long id) {
        boolean result = this.baseService.freeze(id);
        return result ? OK : new Result(ResultCode.FAIL);
    }


    @AuditLog("新增志愿者账号")
    @ApiOperation("新增志愿者")
    @PostMapping("/saveVolunteer")
    public Result saveVolunteer(@RequestBody ZyzVolunteer entity) {
        if (!baseService.saveVolunteer(entity)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ApiOperation("志愿者分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzVolunteer>> getPages(@RequestBody ZyzVolunteerParams params) {
        IPage<ZyzVolunteer> iPage = baseService.getPage(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @ApiOperation("根据id获取")
    @GetMapping("/getVolunteerById")
    public Result getVolunteerById(@RequestParam Long id) {
        return new JsonResult<>(this.baseService.getVolunteerById(id));
    }

    @ApiOperation("根据id获取")
    @GetMapping("/getVolunteerByPhone")
    public Result getVolunteerById(@RequestParam String phone) {
        ZyzVolunteer volunteer = this.baseService.getByPhone(phone);
        if (ObjectUtils.isEmpty(volunteer)) {
            throw new BusinessException(ResultCode.FAIL, "未找到志愿者！");
        }
        return new JsonResult<>(volunteer);
    }

    @AuditLog("志愿者移入脏数据表")
    @ApiOperation("将志愿者移动到脏数据表")
    @GetMapping("/moveToDirty")
    public Result moveToDirty(@RequestParam Long id) {
        this.baseService.moveToDirty(id);
        return OK;
    }

    @AuditLog("志愿者手机号变更")
    @ApiOperation("更改手机号")
    @GetMapping("/updatePhone")
    public Result updatePhone( Long id,  String phone) {
        this.baseService.updatePhone(id, phone);
        return OK;
    }
}
