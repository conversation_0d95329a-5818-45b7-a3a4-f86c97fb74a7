package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.dto.RequirementDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingRequirementRecord;
import fykj.microservice.core.base.IBaseService;
import result.Result;

import java.util.List;

public interface IZSQDockingRequirementRecordService extends IBaseService<ZSQDockingRequirementRecord> {

    /**
     * 获取需求对接记录列表
     *
     * @param requirementId
     * @return
     */
    List<ZSQDockingRequirementRecord> getRequirementDockingRecords(Long requirementId);

    /**
     * 失败重试
     * @param requirementId
     * @param recordId
     */
    void retry(Long requirementId, Long recordId);

    /**
     * 获取对接记录详情
     * @param recordId
     * @return
     */
    RequirementDockingRecordDetailDto getDockingRecordDetail(Long recordId);


    /**
     * 更新对接记录
     *
     * @param record
     * @param result
     */
    void updateDockingRecord(ZSQDockingRequirementRecord record, Result result);

    /**
     * 更新对接记录
     *
     * @param recordId
     * @param result
     */
    void updateDockingRecord(Long recordId, Result result);
}
