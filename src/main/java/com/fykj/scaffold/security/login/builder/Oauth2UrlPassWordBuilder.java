package com.fykj.scaffold.security.login.builder;

import com.fykj.scaffold.security.login.domain.dto.OAuth2RequestDto;
import constants.Mark;
import lombok.Getter;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;

/**
 * <AUTHOR>
 * 密码模式url
 */
@Getter
public class Oauth2UrlPassWordBuilder extends Oauth2UrlBaseBuilder {

    public Oauth2UrlPassWordBuilder(ClientCredentialsResourceDetails clientCredentialsResourceDetails,OAuth2RequestDto dto) {
        super(clientCredentialsResourceDetails,dto);
    }

    @Override
    public Oauth2UrlPassWordBuilder buildOauth2Url(){
        OAuth2RequestDto oauth2BaseDto = (OAuth2RequestDto) getOauth2BaseDto();
        getUri().append(Mark.QUESTION).append(OAUTH2_TOKEN_URL_USERNAME).append(Mark.EQUAL).append(oauth2BaseDto.getUsername())
                .append("&").append(OAUTH2_TOKEN_URL_PASSWORD).append(Mark.EQUAL).append(oauth2BaseDto.getPassword())
                .append("&").append(OAUTH2_TOKEN_URL_GRANT_TYPE).append(Mark.EQUAL).append(oauth2BaseDto.getLoginType())
                .append("&").append(OAUTH2_TOKEN_URL_SCOPE).append(Mark.EQUAL).append(String.join("", getClientCredentialsResourceDetails().getScope()))
                .append("&").append(OAUTH2_TOKEN_URL_CLIENT_ID).append(Mark.EQUAL).append(getClientCredentialsResourceDetails().getClientId())
                .append("&").append(OAUTH2_TOKEN_URL_CLIENT_SECRET).append(Mark.EQUAL).append(getClientCredentialsResourceDetails().getClientSecret());
        return this;
    }

}
