package com.fykj.scaffold.security.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.domain.params.UserParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-16
 */
public interface UserMapper extends BaseMapper<User> {

    /**
     * 分页查询用户信息
     * @param page
     * @param params
     * @return
     */
    IPage<User> findByPage(Page<User> page, @Param("params")UserParams params);

    /**
     * 分页查询实践所下管理员用户信息
     * @param page
     * @param params
     * @return
     */
    IPage<User> pageOrListForSubAssociationAdmin(Page<User> page, @Param("params")UserParams params);

    /**
     * 查询实践所下管理员用户信息列表
     * @param params
     * @return
     */
    List<User> pageOrListForSubAssociationAdmin( @Param("params")UserParams params);

    /**
     * 用户注销删除用户
     * @param ids
     */
    void userWriteOffRemove(@Param("ids") List<Long> ids);
}
