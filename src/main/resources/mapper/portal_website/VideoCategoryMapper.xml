<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.portal_website.mapper.VideoCategoryMapper">

    <select id="pageForVideoContent" resultType="com.fykj.scaffold.portal_website.domain.entity.VideoCategory">
        SELECT
        cc.id AS id,
        cc.title AS title,
        cc.title_img_url AS titleImgUrl,
        cc.brief_introduction AS briefIntroduction,
        cc.grounding AS grounding,
        ccc.category_list AS category,
        ccc.sequence AS sequence,
        cc.description AS description,
        cc.actual_reading AS actualReading,
        cc.effective_date AS effectiveDate,
        cc.district AS district
        FROM cms_content cc
        LEFT JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0 AND ccc.is_banner = 0
        WHERE cc.is_deleted = 0
        <if test="params.type != null and params.type != ''">
            AND cc.type = #{params.type}
        </if>
        <if test="params.district != null and params.district != ''">
            AND cc.district = #{params.district}
        </if>
        <if test="params.title != null and params.title != ''">
            AND cc.title LIKE CONCAT('%', CONCAT(#{params.title}, '%'))
        </if>
        <if test="params.grounding != null">
            AND cc.grounding = #{params.grounding}
        </if>
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        ORDER BY cc.create_date DESC, cc.effective_date DESC, ccc.sequence ASC
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.portal_website.domain.entity.VideoCategory">
        SELECT
            cc.id AS id,
            cc.title AS title,
            cc.title_img_url AS titleImgUrl,
            cc.brief_introduction AS briefIntroduction,
            cc.grounding AS grounding,
            ccc.category_list AS category,
            ccc.sequence AS sequence,
            cc.description AS description,
            cc.actual_reading AS actualReading,
            cc.effective_date AS effectiveDate,
            cc.district AS district
        FROM cms_content cc
        LEFT JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0 AND ccc.is_banner = 0
        WHERE cc.is_deleted = 0 and cc.grounding=1
        <if test="params.type != null and params.type != ''">
            AND cc.type = #{params.type}
        </if>
        <if test="params.title != null and params.title != ''">
            AND cc.title LIKE CONCAT('%', CONCAT(#{params.title}, '%'))
        </if>
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        ORDER BY ccc.sequence ASC, cc.effective_date DESC
    </select>
    <select id="getTrainVideo" resultType="com.fykj.scaffold.portal_website.domain.dto.VideoCategoryDto">
        SELECT cc.title         AS title,
               cc.title_img_url AS titleImgUrl,
               ccc.sequence     AS sequence,
               vu.path          AS videoShowPath
        FROM cms_content cc
                 LEFT JOIN cms_category_content ccc ON cc.id = ccc.content_id
            AND ccc.is_deleted = 0
            AND ccc.is_banner = 0
                 LEFT JOIN video_upload vu ON vu.id = cc.description
            AND vu.is_deleted = 0
        WHERE cc.is_deleted = 0
          AND cc.grounding = 1
          AND cc.type = 'TRAIN_VIDEO'
          AND cc.effective_date &lt;= NOW()
          AND (cc.expiration_date IS NULL OR cc.expiration_date >= NOW())
        ORDER BY ccc.sequence ASC
    </select>
</mapper>
