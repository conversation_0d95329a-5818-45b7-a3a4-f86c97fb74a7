package com.fykj.scaffold.advanced_unit.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitEvaluateListDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitIndexEvaluateCountDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardIndexUnit;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardIndexUnitParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 文明单位-指标标准-单位打分
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
public interface IAuStandardIndexUnitService extends IBaseService<AuStandardIndexUnit> {

    /**
     * 根据给定的指标单元进行评价处理
     *
     * @param unit 待评价的指标单元对象
     */
    void evaluate(AuStandardIndexUnit unit);

    /**
     * 根据给定的指标单元进行评价处理
     *
     * @param unit 待评价的指标单元对象
     */
    void evaluate(List<AuStandardIndexUnit> unit);

    /**
     * 查询评价页面
     *
     * @param params 评价页面查询参数
     * @return 返回包含评价信息的分页对象
     */
    IPage<AuUnitEvaluateListDTO> queryEvaluatePage(AuStandardIndexUnitParams params);

    /**
     * 计算指标评价数量
     *
     * @param params 指标参数
     * @return 返回AuUnitIndexEvaluateCountDTO对象，包含指标评价数量信息
     */
    IPage<AuStandardIndexUnit> queryIndexEvaluatePage(AuStandardIndexUnitParams params);

    /**
     * 统计指标评价次数
     *
     * @param params 评价指标参数
     * @return 返回包含统计结果的DTO对象
     */
    AuUnitIndexEvaluateCountDTO countIndexEvaluate(AuStandardIndexUnitParams params);

}
