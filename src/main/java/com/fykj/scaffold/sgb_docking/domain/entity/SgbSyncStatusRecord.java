package com.fykj.scaffold.sgb_docking.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 数据同步状态记录-实体类
 * 业务解耦,不污染原有业务表结构,业务表保持纯净,避免业务表字段膨胀
 *
 * @date 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sgb_sync_status_record")
	public class SgbSyncStatusRecord extends BaseEntity  {

	private static final long serialVersionUID = 3512367047676937946L;

	/**
	 * 业务类型
	 */
	@DictTrans(transTo = "businessTypeText")
	@TableField("business_type")
    @ApiModelProperty(value = "业务类型")
	private String businessType;

	/**
	 * 业务类型名称
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "业务类型名称")
	private String businessTypeText;

	/**
	 * 业务ID
	 */
	@TableField("business_id")
    @ApiModelProperty(value = "业务ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long businessId;

	/**
	 * 同步状态：0-未同步，1-已同步
	 */
	@TableField("sync_status")
    @ApiModelProperty(value = "同步状态：0-未同步，1-已同步")
	private Boolean syncStatus;

	/**
	 * 同步备注
	 */
	@TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
	private String syncRemark;

	/**
	 * 社工部ID
	 */
	@TableField("sgb_id")
	@ApiModelProperty(value = "社工部ID")
	private String sgbId;

	/**
	 * 同步时间
	 */
	@TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
	@JsonFormat(pattern = DATETIME_FORMAT)
	@DateTimeFormat(pattern = DATETIME_FORMAT)
	private LocalDateTime syncTime;

	/**
	 * 业务数据名称
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "业务数据名称")
	private String bizName;

}
