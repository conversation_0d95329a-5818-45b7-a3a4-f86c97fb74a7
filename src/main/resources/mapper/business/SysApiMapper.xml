<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.security.business.mapper.SysApiMapper">
	<select id="getRoleApiPathList" resultType="com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto">
		SELECT a.api_path as path,
		       ar.role_id as roleId
		FROM sys_api_role ar
			     INNER JOIN sys_api a ON a.id = ar.api_id
		WHERE ar.is_deleted = FALSE
		  AND a.is_deleted = FALSE
		union all
		SELECT a.api_path as path,
		       ra.role_id as roleId
		FROM sys_role_action ra
			     INNER JOIN sys_api_action aa ON aa.action_id = ra.action_id
		    	 INNER JOIN sys_action ac on ac.id = aa.action_id
			     INNER JOIN sys_api a ON a.id = aa.api_id
		WHERE aa.is_deleted = FALSE
		  AND ra.is_deleted = FALSE
		  and ac.status = true
		  AND a.is_deleted = FALSE
	</select>
</mapper>
