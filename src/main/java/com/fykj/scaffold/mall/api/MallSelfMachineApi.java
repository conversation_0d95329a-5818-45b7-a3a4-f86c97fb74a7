package com.fykj.scaffold.mall.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.entity.MallSelfMachine;
import com.fykj.scaffold.mall.domain.params.MallSelfMachineParams;
import com.fykj.scaffold.mall.service.IMallSelfMachineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

/**
 * 商品微信前端接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/mall/self/machine")
@Api(tags="pc --自助服务机器无需登录")
public class MallSelfMachineApi {

    @Autowired
    private IMallSelfMachineService mallSelfMachineService;
    @ApiOperation("自助服务机器分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallSelfMachine>> getPages(@RequestBody MallSelfMachineParams params) {
        params.setStatus(MallCons.MALL_SELF_MACHINE_STATUS_NORMAL);
        return new JsonResult<>(mallSelfMachineService.pageForApi(params));
    }
    @ApiOperation("自助服务机器查询详情")
    @GetMapping({"/get"})
    public JsonResult<MallSelfMachine> get(@RequestParam String id) {
        MallSelfMachine result = mallSelfMachineService.getById(id);
        return new JsonResult<>(result);
    }

    @ApiOperation("自助服务机器查询详情")
    @GetMapping({"/getMachine"})
    public JsonResult<MallSelfMachine> getMachine(@RequestParam String machineNo) {
        MallSelfMachine result = mallSelfMachineService.lambdaQuery().eq(MallSelfMachine::getMachineNo,machineNo).one();
        return new JsonResult<>(result);
    }
}
