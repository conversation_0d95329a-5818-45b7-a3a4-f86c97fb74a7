package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户弱口令列表
 *
 * <AUTHOR> @email ${email}
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_weak_pw")
public class SysWeakPw extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 弱口令
     */
    @TableField("password")
    @ApiModelProperty(value = "弱口令")
    private String password;

    /**
     * 状态
     */
    @TableField("status")
    @ApiModelProperty(value = "状态")
    private Boolean status;


}
