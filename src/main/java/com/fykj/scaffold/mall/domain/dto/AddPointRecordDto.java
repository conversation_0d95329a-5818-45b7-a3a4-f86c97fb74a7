package com.fykj.scaffold.mall.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AddPointRecordDto {
    /**
     * 志愿者id
     */
    private Long volunteerId;
    /**
     * 变动积分数（正数）
     */
    private BigDecimal changePoint;
    /**
     * 备注
     */
    private String remark;

    /**
     * 业务ID
     */
    private long bizId;

    /**
     * 业务类型
     */
    private String bizType;
}
