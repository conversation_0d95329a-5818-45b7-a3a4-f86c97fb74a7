package com.fykj.scaffold.portal_website.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.domain.params.NewsParams;
import com.fykj.scaffold.portal_website.service.INewsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

/**
 * 新闻公共接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/portal_website/news")
@Api(tags = "pc--新闻公告接口--无需登录")
public class NewsApi {

    @Autowired
    private INewsService newsService;

    @ApiOperation("分页查询新闻公告")
    @PostMapping({"/pages"})
    public JsonResult<IPage<News>> pageForNews(@RequestBody NewsParams params) {
        IPage<News> result = newsService.pageForApi(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("新闻公告详情")
    @GetMapping({"/get"})
    public JsonResult<News> get(@RequestParam String id) {
        News result = newsService.getForApi(id);
        return new JsonResult<>(result);
    }
    @ApiOperation(value = "新增浏览量")
    @GetMapping(value = "/updateCmsContentById")
    public Result updateCmsContentById(@RequestParam String id){
        newsService.updateCmsContentById(id);
        return new Result();
    }
}
