package com.fykj.scaffold.dynamic.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 模板信息-实体类
 *
 * @date 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dynamic_form")
public class DynamicForm extends BaseEntity  {

	private static final long serialVersionUID = 1727392816207514595L;

	/**
	 * 模块标识
	 */
	@TableField("module_code")
    @ApiModelProperty(value = "模块标识")
	private String moduleCode;

	/**
	 * 模板名称
	 */
	@TableField("form_name")
    @ApiModelProperty(value = "模板名称")
	private String formName;

	/**
	 * 模板描述
	 */
	@TableField("form_description")
    @ApiModelProperty(value = "模板描述")
	private String formDescription;

	/**
	 * 状态：1-启用，0-禁用
	 */
	@TableField("status_flag")
    @ApiModelProperty(value = "状态：1-启用，0-禁用")
	private Boolean statusFlag;

	/**
	 * 关联字段信息
	 */
	@TableField(exist = false)
	private List<DynamicField> fields;
}
