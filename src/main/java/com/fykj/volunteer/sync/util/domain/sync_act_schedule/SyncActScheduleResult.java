package com.fykj.volunteer.sync.util.domain.sync_act_schedule;

import com.fykj.scaffold.zyz.domain.dto.ActScheduleResultPreDataDto;
import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShowResult;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActScheduleResult {

    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 消息
     */
    private String msg;
    /**
     * 唯一编号
     */
    private String uniCode;

    /**
     * 计划id
     */
    private String schId;

    /**
     * 活动预告id列表，uniCode为活动预告第三方唯一值，preId为开放平台活动预告编号;
     */
    private List<ActScheduleResultPreDataDto> preData;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "actschedule")
    public static class ActSchedule {
        private List<SyncActScheduleResult> info;
    }
}
