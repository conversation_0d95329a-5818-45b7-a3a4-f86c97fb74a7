package com.fykj.scaffold.race.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.race.domain.dto.PhotoImportDto;
import com.fykj.scaffold.race.domain.entity.ActivityPrize;
import com.fykj.scaffold.race.domain.params.ActivityPrizeParams;
import com.fykj.scaffold.race.service.IActivityPrizeService;
import com.fykj.scaffold.zyz.domain.dto.ActivityMemberImportDto;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 摄影大赛-活动奖项作品关联表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-10
 */
@RestController
@RequestMapping("/admin/race/activity/prize")
public class ActivityPrizeController extends BaseController<IActivityPrizeService, ActivityPrize, ActivityPrizeParams> {

    /**
     * 录入导入excel
     *
     * @param excel
     * @return
     */
    @ApiOperation(value = "userId录入导入excel")
    @PostMapping(value = "/uploadExcel")
    public void uploadExcel(@RequestParam(value = "excel") MultipartFile excel,@RequestParam("activityId") Long activityId) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.DATA_EXPIRED, "excel文件不能为空");
        }
        List<PhotoImportDto> failureList = baseService.uploadExcel(excel,activityId);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.writeExcel(failureList,"获奖作品导入失败", PhotoImportDto.class);
        }

    }

    /**
     * 发送对象excel模版下载
     */
    @ApiOperation("获奖作品模版下载")
    @GetMapping("/winPhotoExcelTemplate")
    public void winPhotoExcelTemplate(HttpServletResponse response) {
        downloadFile(response, "/data/excelTemplate/photo_template.xlsx");
    }



}
