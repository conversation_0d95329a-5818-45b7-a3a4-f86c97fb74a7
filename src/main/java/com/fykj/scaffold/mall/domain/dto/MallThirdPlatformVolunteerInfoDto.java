package com.fykj.scaffold.mall.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Desensitise;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

;

/**
 * 志愿者表
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-01
 */
@Data
public class MallThirdPlatformVolunteerInfoDto {
    /**
     * 志愿者姓名
     */
    private String name;

    /**
     * 服务时长
     */
    private BigDecimal serviceLong;

    /**
     * 今年服务时长
     */
    private BigDecimal serviceLongThisYear;

    /**
     * 总积分
     */
    private BigDecimal totalPoint;

    /**
     * 证件类型（身份证还是护照等等)
     */
    @DictTrans
    private String certificateType;

    /**
     * 身份证号
     */
    private String certificateId;

    /**
     * 电话
     */
    private String phone;

    /**
     * 性别
     */
    private String sex;


    /**
     * 注册时间
     */
    private String founded;

    private Integer star;
}
