package com.fykj.scaffold.advanced_unit.service;


import com.fykj.scaffold.advanced_unit.domain.entity.AuStandard;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 文明单位-指标标准-测评版本
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
public interface IAuStandardService extends IBaseService<AuStandard> {

    /**
     * 获取所有AuStandard对象中年份去重后的列表, 用于文明单位注册下拉框选择
     *
     * @return 所有年份去重后的列表
     */
    List<Integer> getYearsHaveStandard();

    /**
     * 更新展示状态
     *
     * @param id 标识ID
     */
    void updateShowStatus(String id);

    /**
     * 根据年份获取标准
     * @param year 年份
     * @return 标准
     */
    AuStandard getByYear(Integer year);
}
