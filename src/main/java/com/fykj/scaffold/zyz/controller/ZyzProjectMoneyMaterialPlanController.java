package com.fykj.scaffold.zyz.controller;


import com.fykj.scaffold.zyz.domain.entity.ZyzProjectMoneyMaterialPlan;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectMoneyMaterialPlanParams;
import com.fykj.scaffold.zyz.service.IZyzProjectMoneyMaterialPlanService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 资金/物资使用计划表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/admin/project/money/material/plan")
public class ZyzProjectMoneyMaterialPlanController extends BaseController<IZyzProjectMoneyMaterialPlanService, ZyzProjectMoneyMaterialPlan, ZyzProjectMoneyMaterialPlanParams> {

}
