package com.fykj.scaffold.sync.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 市平台同步实践点
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ZyzSyncSpotParams extends BaseParams {
    @MatchType(value = QueryType.LIKE, fieldName = "name")
    private String spotName;
}
