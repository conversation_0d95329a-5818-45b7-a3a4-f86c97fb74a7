package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzOrgTeam;
import com.fykj.scaffold.zyz.domain.params.ZyzOrgTeamParams;
import com.fykj.scaffold.zyz.service.IZyzOrgTeamService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 志愿者社区同名团队表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@RestController
@RequestMapping("/admin/zyz/org/team")
@Api(tags = " 志愿者社区同名团队表接口")
public class ZyzOrgTeamController extends BaseController<IZyzOrgTeamService, <PERSON><PERSON><PERSON><PERSON><PERSON>Team, ZyzOrgTeamParams> {

}
