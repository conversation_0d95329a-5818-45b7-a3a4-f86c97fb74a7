package com.fykj.scaffold.zsq_docking.push_mq.event;

import com.fykj.scaffold.zsq_docking.push_mq.dto.RequirementSyncForZyzDto;
import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequirementDockingEvent extends BaseDockingMqEvent {

    /**
     * 对接记录 id
     */
    private Long dockingRecordId;

    /**
     * 需求 id
     */
    private Long requirementId;

    /**
     * 消息 id
     */
    private String operation;
    /**
     * 消息 id
     */
    private String zwtUserId;
    /**
     * 上/下架
     */
    private Boolean onShelf;
    /**
     * 需求信息
     */
    private RequirementSyncForZyzDto requirementSyncForZyzDto;

    /**
     * 志愿者平台用户 id
     */
    private Serializable zyzUserId;
}
