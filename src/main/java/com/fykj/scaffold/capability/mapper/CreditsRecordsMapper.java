package com.fykj.scaffold.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.capability.domain.dto.CreditsRecordsDTO;
import com.fykj.scaffold.capability.domain.entity.CreditsRecords;
import com.fykj.scaffold.capability.domain.params.CreditsRecordParams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学分记录管理 Mapper 接口
 * 表对应：cu_credits_records
 * 2025/2/26
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface CreditsRecordsMapper extends BaseMapper<CreditsRecords> {
    List<CreditsRecordsDTO> getUserCredits(IPage<CreditsRecordsDTO> page,
                                           @Param("params") CreditsRecordParams params);

    List<CreditsRecordsDTO> getUserCredits(@Param("params") CreditsRecordParams params);
}
