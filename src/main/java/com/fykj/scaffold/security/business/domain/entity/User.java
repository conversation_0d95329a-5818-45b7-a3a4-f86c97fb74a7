package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 系统用户
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 登录账号
     */
    @TableField("username")
    private String username;

    /**
     * 是否强制修改密码
     */
    @TableField("force_update_pwd")
    private Boolean forceUpdatePwd;

    /**
     * 上次修改密码时间
     */
    @TableField("last_update_pwd_date")
    private LocalDate lastUpdatePwdDate;

    /**
     * 密码    //TODO 加密不生效
     */
    @TableField("password")
    private String password;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 状态
     */
    @TableField("status")
    private Boolean status;

    /**
     * 手机号码
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 显示名称（冗余字段）
     */
    @TableField("name")
    private String name;

    /**
     * 绑定的openid
     */
    @TableField(value = "open_id", updateStrategy = FieldStrategy.IGNORED)
    private String openId;

    /**
     *  小程序的openid
     */
    @TableField(value = "ma_open_id", updateStrategy = FieldStrategy.IGNORED)
    private String maOpenId;

    /**
     * 开放平台union_id
     */
    @TableField(value = "union_id", updateStrategy = FieldStrategy.IGNORED)
    private String unionId;

    /**
     * 是否是管理端账号
     */
    @TableField(value = "is_manager_account")
    private Boolean managerAccount;

    @TableField(exist = false)
    private String roleCodesStr;

    @TableField(exist = false)
    private String roleNames;

    @TableField(exist = false)
    private String saCodes;

    @TableField(exist = false)
    private String communityCodes;

    @TableField(exist = false)
    private String saNames;

    @TableField(exist = false)
    private String communityNames;

    @TableField(exist = false)
    private String certId;

    @TableField(exist = false)
    private List<Long> teamIds = new ArrayList<>();

    @TableField(exist = false)
    private List<String> roleCodes = new ArrayList<>();

    @TableField(exist = false)
    private List<SysUserExpand> expandList = new ArrayList<>();
}
