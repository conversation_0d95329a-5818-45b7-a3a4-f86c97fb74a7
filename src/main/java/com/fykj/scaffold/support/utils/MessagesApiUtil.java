package com.fykj.scaffold.support.utils;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.message.domain.entity.MsgRecordRealSendStatus;
import com.fykj.volunteer.sync.util.util.MD5Util;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MessagesApiUtil {
    static String SHORT_MESSAGE_URL = "https://zyz.sipac.gov.cn/sms_proxy/v2sms.aspx";
    static String STATUS_QUERY_URL = "https://zyz.sipac.gov.cn/sms_proxy/v2statusApi.aspx";
//    static String SHORT_MESSAGE_URL = "http://************:8888/v2sms.aspx";
//    static String STATUS_QUERY_URL = "http://************:8888/v2statusApi.aspx";
    static String LOGIN_USER = "SJZYFW";
    static String PASSWORD = "123@SJZYFW@abcd";
    static String USERID = "142";
    static String TOTAL="【苏州工业园区】（新时代文明实践志愿服务平台）";


    public interface ReturnStatus{

        String SUCCESS = "Success";

        String FAIL = "Faild";
    }
    /**
     * 短信发送接口
     *
     * @param content  内容
     * @param mobile   多个手机号以,隔开
     * @return
     */
    public static MessagesApiUtil.Result sendShortMessage(String content, String mobile) {
      return sendShortMessage(content, mobile,null);
    }
    /**
     * 短信发送接口
     *
     * @param content  内容
     * @param mobile   多个手机号以,隔开
     * @param sendTime 发送时间可以为空 为空表示立即发送 格式为yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static MessagesApiUtil.Result sendShortMessage(String content, String mobile, String sendTime) {
        Map<String, Object> headers = new HashMap<>();
        long timestamp = System.currentTimeMillis();
        String signBegin = LOGIN_USER + PASSWORD + timestamp;
        String sign = MD5Util.stringToMD532Small(signBegin);
        headers.put("userid", USERID);
        headers.put("timestamp", timestamp);
        headers.put("sign", sign);
        headers.put("mobile", mobile);
        headers.put("content", TOTAL+ content);
        headers.put("sendTime", sendTime);
        headers.put("action", "send");
        headers.put("extno", null);
        try {
            System.setProperty("jdk.tls.allowUnsafeServerCertChange", "true");
            HttpResponse execute = HttpUtil.createPost(SHORT_MESSAGE_URL)
                    .form(headers).timeout(10000).execute();
            if (HttpStatus.HTTP_OK != execute.getStatus()) {
                log.error("请求短信平台接口错误，发送内容：{}，请求结果：{}", content, execute);
                throw new BusinessException(ResultCode.SERVICE_FAIL, "请求短信平台接口失败！");
            }
            String result = execute.body();
            MessagesApiUtil.Result resultBack = XmlUtils.parseBean(result, MessagesApiUtil.Result.class);
            if (resultBack == null) {
                log.error("请求短信平台接口调用成功，发送内容：{}，返回的xml无法解析:{}", content, result);
                throw new BusinessException(ResultCode.BAD_REQUEST, "请求短信平台接口出错");
            }
            resultBack.setResult(result);
            return resultBack;
        } catch (Exception e) {
            log.error("请求短信平台接口错误，发送内容：{}, 请联系管理员！",content, e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "请求短信平台接口失败！");
        }
    }

    public static void main(String[] args) {
        MessagesApiUtil.OverageResult message = overage();
//        MessagesApiUtil.Result message = sendShortMessage("尊敬的用户你好，感谢您成为一名志愿者", "***********", null);
        System.out.println(message);
        System.out.println(message);
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "returnsms")
    public static class Result {
        /**
         * 返回状态值：成功返回Success 失败返回：Faild
         */
        private String returnstatus;
        /**
         * 返回信息提示	说明
         * ok	提交成功
         * 用户名或密码不能为空	提交的用户名或密码为空
         * 发送内容包含sql注入字符	包含sql注入字符
         * 用户名或密码错误	表示用户名或密码错误
         * 短信号码不能为空	提交的被叫号码为空
         * 短信内容不能为空	发送内容为空
         * 包含非法字符：	表示检查到不允许发送的非法字符
         * 对不起，您当前要发送的量大于您当前余额	当支付方式为预付费是，检查到账户余额不足
         * 其他错误	其他数据库操作方面的错误
         */
        private String message;
        /**
         * 返回余额
         */
        private String remainpoint;
        /**
         * 返回本次任务的序列ID
         */
        private String taskID;
        /**
         * 成功短信数：当成功后返回提交成功短信数
         */
        private String successCounts;

        private String result;
    }

    /**
     * 余额及已发送量查询接口
     *
     * @return
     */
    public static MessagesApiUtil.OverageResult overage() {
        Map<String, Object> formData = new HashMap<>();
        long timestamp = System.currentTimeMillis();
        String signBegin = LOGIN_USER + PASSWORD + timestamp;
        String sign = MD5Util.stringToMD532Small(signBegin);
        formData.put("userid", USERID);
        formData.put("timestamp", timestamp);
        formData.put("sign", sign);
        formData.put("action", "overage");
        formData.put("rt", "json");
        try {
            HttpResponse execute = HttpUtil.createPost(SHORT_MESSAGE_URL)
                    .form(formData).timeout(10000).execute();
            if (HttpStatus.HTTP_OK != execute.getStatus()) {
                log.error("请求短信平台接口错误" + execute);
                throw new BusinessException(ResultCode.SERVICE_FAIL, "请求短信平台接口失败！");
            }
            String result = execute.body();
            MessagesApiUtil.OverageResult resultBack;
            try {
                resultBack = JSON.parseObject(result, MessagesApiUtil.OverageResult.class);
            } catch (Exception e) {
                log.error("请求短信平台接口调用成功，返回的json无法解析:{}", result);
                throw new BusinessException(ResultCode.BAD_REQUEST, "请求短信平台接口出错");
            }
            resultBack.setResult(result);
            return resultBack;
        } catch (Exception e) {
            log.error("请求短信平台接口错误，请联系管理员", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "请求短信平台接口失败！");
        }
    }

    @Data
    public static class OverageResult {
        /**
         * 返回状态值：成功返回Success 失败返回：Faild
         */
        private String ReturnStatus;
        /**
         * 返回信息提示	说明
         * 1、返回空	查询成功 将返回相应的支付方式、账户使用条数、总充值点数
         * 2、用户名或密码不能为空	提交的用户名或密码为空
         * 3、用户名或密码错误	表示用户名或密码错误
         */
        private String Message;
        /**
         * 返回支付方式  后付费，预付费
         */
        private String Payinfo;
        /**
         * 返回余额
         */
        private Integer Overage;
        /**
         * 返回总点数  当支付方式为预付费是返回总充值点数
         */
        private Integer SendTotal;

        private String result;
    }

    /**
     * 信息发送真是状态查询
     *
     * @return
     */
    public static MessagesApiUtil.QueryStatusResult queryStatus() {
        Map<String, Object> formData = new HashMap<>();
        long timestamp = System.currentTimeMillis();
        String signBegin = LOGIN_USER + PASSWORD + timestamp;
        String sign = MD5Util.stringToMD532Small(signBegin);
        formData.put("userid", USERID);
        formData.put("timestamp", timestamp);
        formData.put("sign", sign);
        formData.put("action", "query");
        formData.put("rt", "json");
        formData.put("statusNum", 1000);
        try {
            HttpResponse execute = HttpUtil.createPost(STATUS_QUERY_URL)
                    .form(formData).timeout(10000).execute();
            if (HttpStatus.HTTP_OK != execute.getStatus()) {
                log.error("请求短信平台接口错误" + execute);
                throw new BusinessException(ResultCode.SERVICE_FAIL, "请求短信平台接口失败！");
            }
            String result = execute.body();
            MessagesApiUtil.QueryStatusResult resultBack;
            try {
                resultBack = JSON.parseObject(result, MessagesApiUtil.QueryStatusResult.class);
            } catch (Exception e) {
                log.error("请求短信平台接口调用成功，返回的json无法解析:{}", result);
                throw new BusinessException(ResultCode.BAD_REQUEST, "请求短信平台接口出错");
            }
            resultBack.setResult(result);
            return resultBack;
        } catch (Exception e) {
            log.error("请求短信平台接口错误，请联系管理员", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "请求短信平台接口失败！");
        }
    }

    @Data
    public static class QueryStatusResult {
        /**
         * 返回状态值：成功返回Success 失败返回：Faild
         */
        private String ReturnStatus;
        /**
         * 返回信息提示	说明
         * 1：用户名或密码不能为空
         * 2：用户名或密码错误
         * 3：该用户不允许查看状态报告
         * 4：参数不正确
         */
        private String Message;
        /**
         * 返回支付方式  后付费，预付费
         */
        private List<MsgRecordRealSendStatus> Task;

        private String result;
    }
}
