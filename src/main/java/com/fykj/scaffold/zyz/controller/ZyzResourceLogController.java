package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzResourceLog;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceLogParams;
import com.fykj.scaffold.zyz.service.IZyzResourceLogService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 资源操作记录表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-03-02
 */
@RestController
@RequestMapping("/admin/zyz/resource/log")
@Api(tags = "资源操作记录接口")
public class ZyzResourceLogController extends BaseController<IZyzResourceLogService, ZyzResourceLog,ZyzResourceLogParams> {


    @ApiOperation("查询资源操作日志")
    @GetMapping({"/getLogsByResourceId"})
    public JsonResult<List<ZyzResourceLog>> getLogsByResourceId(Long resourceId) {
        List<ZyzResourceLog> result = this.baseService.getLogsByResourceId(resourceId);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }

}
