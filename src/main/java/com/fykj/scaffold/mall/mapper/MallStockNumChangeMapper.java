package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.entity.MallStockNumChange;
import com.fykj.scaffold.mall.domain.params.MallStockNumChangeParams;
import org.apache.ibatis.annotations.Param;

/**
 * 积分商城-库存变动
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
public interface MallStockNumChangeMapper extends BaseMapper<MallStockNumChange> {
    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    IPage<MallStockNumChange> getPage(IPage<MallStockNumChange> page, @Param("params") MallStockNumChangeParams params);
}
