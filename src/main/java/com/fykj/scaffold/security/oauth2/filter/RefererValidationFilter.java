package com.fykj.scaffold.security.oauth2.filter;

import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.config.RefererConfig;
import fykj.microservice.core.support.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.web.filter.OncePerRequestFilter;
import result.Result;
import utils.StringUtil;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

@Slf4j
public class RefererValidationFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
            throws ServletException, IOException {
        log.debug("RefererValidationFilter doFilter");
        //options 直接放行
        if (!HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
            String referer = request.getHeader("referer");
            if (StringUtil.isNotEmpty(referer)) {
                //获取配置文件参数
                List<String> refererList = SystemUtil.getBean(RefererConfig.class).getRefererList();
                if (refererList.stream().noneMatch(referer::startsWith)) {
                    Result result = new Result(HttpServletResponse.SC_FORBIDDEN, "Invalid Referer");
                    response.setContentType("application/json;charset=UTF-8");
                    PrintWriter writer = response.getWriter();
                    writer.print(JSON.toJSONString(result));
                    return;
                }
            }
        }
        filterChain.doFilter(request, response);
    }
}
