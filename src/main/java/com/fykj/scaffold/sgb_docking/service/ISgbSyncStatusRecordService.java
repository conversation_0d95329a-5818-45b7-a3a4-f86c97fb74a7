package com.fykj.scaffold.sgb_docking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sgb_docking.domain.bo.SyncInfo;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncStatusRecord;
import com.fykj.scaffold.sgb_docking.domain.params.SgbSyncStatusRecordParams;
import fykj.microservice.core.base.IBaseService;

/**
 * 数据同步状态记录-服务类
 *
 * @date 2025-06-24
 */
public interface ISgbSyncStatusRecordService extends IBaseService<SgbSyncStatusRecord> {

    /**
     * 根据bizType和bizId保存或更新同步状态
     * @param syncStatus 同步状态
     * @param syncRemark 同步备注
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @param sgbId 社工部ID
     */
    void saveOrUpdateSyncStatus(Boolean syncStatus, String syncRemark,
                                String bizType, Long bizId, String sgbId);

    /**
     * 根据业务类型和业务ID获取同步状态记录
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 同步状态记录
     */
    SgbSyncStatusRecord getByBizTypeAndId(String bizType, Long bizId);

    /**
     * 判断业务数据是否同步成功
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 业务数据是否同步成功
     */
    boolean isBizDataSyncSuccess(String bizType, Long bizId);

    /**
     * 确定同步信息（包含保存类型和sgbId）
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 同步信息
     */
    SyncInfo determineSyncInfo(String bizType, Long bizId);

    /**
     * 确定保存类型：新增或更新
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 保存类型
     */
    String determineSaveType(String bizType, Long bizId);

    /**
     * 分页查询同步状态记录（包含业务数据名称）
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<SgbSyncStatusRecord> getPageWithBizName(SgbSyncStatusRecordParams params);
}

