package com.fykj.scaffold.support.conns;

/**
 * 系统消息模版
 */
public class SysMsgTmpCons {

    public static final String SMT_NEWS_AUDIT_NOTICE = "NEWS_AUDIT_NOTICE"; // 新闻模块-新闻待审核提醒

    public static final String SMT_NEWS_AUDIT_RESULT = "NEWS_AUDIT_RESULT"; // 新闻模块-审核结果告知发布人

    public static final String SMT_RESOURCE_AUDIT_NOTICE = "RESOURCE_AUDIT_NOTICE"; // 资源模块-资源待审核提醒

    public static final String SMT_RESOURCE_AUDIT_RESULT = "RESOURCE_AUDIT_RESULT"; // 资源模块-审核结果告知发布人

    public static final String SMT_RESOURCE_HAS_APPOINTMENT = "RESOURCE_HAS_APPOINTMENT"; // 资源模块-资源被预约通知资源发布人

    public static final String SMT_RESOURCE_APPOINTMENT_AUDIT_RESULT = "RESOURCE_APPOINTMENT_AUDIT_RESULT"; // 资源模块-预约审核结果通知资源预约人

    public static final String SMT_REQUIREMENT_AUDIT_NOTICE = "REQUIREMENT_AUDIT_NOTICE"; // 需求模块-需求待审核提醒

    public static final String SMT_REQUIREMENT_AUDIT_RESULT = "REQUIREMENT_AUDIT_RESULT"; // 需求模块-审核结果告知发布人

    public static final String SMT_REQUIREMENT_DOCKING_SUCCESS = "REQUIREMENT_DOCKING_SUCCESS"; // 需求模块-通知发布人需求对接成功

    public static final String SMT_ACTIVITY_AUDIT = "ACTIVITY_AUDIT"; // 活动模块-活动待审核提醒

    public static final String SMT_ACTIVITY_AUDIT_RESULT = "ACTIVITY_AUDIT_RESULT"; // 活动模块-审核结果告知发布人

    public static final String SMT_ACTIVITY_AUDIT_TIMEOUT = "ACTIVITY_AUDIT_TIMEOUT"; // 活动模块-活动发布超时审核通知

    public static final String SMT_ACTIVITY_APPLY_AUDIT = "ACTIVITY_APPLY_AUDIT"; // 活动模块-活动有志愿者报名通知

    public static final String SMT_ACTIVITY_APPLY_AUDIT_AGREE = "ACTIVITY_APPLY_AUDIT_AGREE"; // 活动模块-活动报名审核通过通知

    public static final String SMT_ACTIVITY_NOTICE_START = "ACTIVITY_NOTICE_START"; // 活动模块-提醒活动报名者活动开始和签到

    public static final String SMT_ACTIVITY_NOTICE_SIGN = "ACTIVITY_NOTICE_SIGN"; // 活动模块-活动开始前10分钟提醒签到

    public static final String SMT_ACTIVITY_SERVICE_LONG = "ACTIVITY_SERVICE_LONG"; // 活动模块-活动报名生成服务时长通知

    public static final String SMT_ACTIVITY_APPRAISE_AUDIT = "ACTIVITY_APPRAISE_AUDIT"; // 活动模块-活动评价待审核
    public static final String SMT_TEAM_ADMIN_USER_INFO_REMIND = "TEAM_ADMIN_USER_INFO_REMIND"; // 团队模块-团队注册待审核通知

    public static final String SMT_TEAM_JOIN_APPLY = "TEAM_JOIN_APPLY"; // 团队模块-团队加入申请消息

    public static final String SMT_TEAM_JOIN_AUDIT = "TEAM_JOIN_AUDIT"; // 团队模块-团队加入审核通知申请人

    public static final String SMT_TEAM_REGIST_WAIT_AUDIT = "TEAM_REGIST_WAIT_AUDIT"; // 团队模块-团队注册待审核通知

    public static final String SMT_TEAM_REGIST_AUDIT_SUCCESS = "TEAM_REGIST_AUDIT_SUCCESS"; // 团队模块-团队注册成功通知

    public static final String SMT_TEAM_REGIST_AUDIT_FAIL = "TEAM_REGIST_AUDIT_FAIL"; // 团队模块-团队注册失败通知

    public static final String SMT_TEAM_INVITE_ADMIN = "TEAM_INVITE_ADMIN"; // 团队模块-团队管理员通知受邀志愿者加入

    public static final String SMT_TEAM_JOIN_AGREE = "TEAM_JOIN_AGREE"; // 团队模块-志愿者通知团队管理员同意受邀加入团队

    public static final String SMT_TEAM_JOIN_DISAGREE = "TEAM_JOIN_DISAGREE"; // 团队模块-志愿者通知团队管理员不同意受邀加入团队

    public static final String SMT_SCORE_GOODS_CHANGE = "SCORE_GOODS_CHANGE"; // 积分模块-兑换商品扣除说明

    public static final String SMT_SCORE_ZERO_CLEAR = "SCORE_ZERO_CLEAR"; // 积分模块-年度积分清零

    public static final String SMT_SCORE_SEND_OUT_GOODS = "SCORE_SEND_OUT_GOODS"; // 积分模块-物流消息通知发货

    public static final String SMT_SCORE_GOODS_SIGN = "SCORE_GOODS_SIGN"; // 积分模块-商品签收成功通知

    public static final String SMT_SCORE_POINT_CONTRIBUTE = "SCORE_POINT_CONTRIBUTE"; // 积分模块-积分捐赠

    public static final String SMT_PHONE_CHANGE_CONFIRM = "PHONE_CHANGE_CONFIRM"; // 手机号变更模块-手机号变更确认提醒

    public static final String SMT_PHONE_CHANGE_SUCCESS_PROMPT = "PHONE_CHANGE_SUCCESS_PROMPT"; // 手机号变更模块-更换手机号成功提示

    public static final String SMT_ACTIVITY_APPLY_PUBLIC = "ACT_APPLY_PUBLIC"; // 活动报名模块-群众报名成功发送信息

    public static final String SMT_ACTIVITY_APPLY_NOTICE = "ACT_APPLY_NOTICE"; // 有人报名/取消报名通知活动发布人

    public static final String ACT_APPLY_NOTICE_PUBLIC = "ACT_APPLY_NOTICE_PUBLIC"; // 通知活动发布人查看群众报名详情

    public static final String RACE_PHOTO_AUDIT_NO_PASS = "RACE_PHOTO_AUDIT_NO_PASS"; // 摄影大赛作品审核-摄影大赛审核不通过

    //活动被下架短信提醒
    public static final String SMT_ACTIVITY_OFF_SHELF = "ACTIVITY_OFF_SHELF";

    public static final String SMT_ACTIVITY_SHOW_AUDIT_RESULT = "ACTIVITY_SHOW_AUDIT_RESULT"; // 活动公示模块-审核结果告知发布人

    public static final String SMT_ACTIVITY_SHOW_AUDIT_NOTICE = "ACTIVITY_SHOW_AUDIT_NOTICE"; // 活动公示模块-活动公示待审核提醒

    public static final String SMT_ACTIVITY_SCHEDULE_AUDIT_RESULT = "ACTIVITY_SCHEDULE_AUDIT_RESULT"; // 阵地计划模块-审核结果告知发布人

    public static final String SMT_ACTIVITY_SCHEDULE_AUDIT_NOTICE = "ACTIVITY_SCHEDULE_AUDIT_NOTICE"; // 阵地计划模块-活动公示待审核提醒

    public static final String SMT_ACTIVITY_SHOW_UPLOAD_NOTICE = "ACTIVITY_SHOW_UPLOAD_NOTICE"; // 活动公示上传提醒

    /**
     * 注册文明单位审核通过
     */
    public static final String ADVANCE_AUDIT_PASS = "ADVANCE_AUDIT_PASS";

    /**
     * 注册文明单位已审核未通过
     */
    public static final String ADVANCE_UNIT_AUDIT_REJECT = "ADVANCE_UNIT_AUDIT_REJECT";

    /**
     * 课程报名用户提前1天短信提醒
     */
    public static final String COURSE_SIGN_UP_REMIND_EARLY_ONE_DAY = "COURSE_SIGN_UP_REMIND_EARLY_ONE_DAY";

    /**
     * 课程报名用户提前2小时短信提醒
     */
    public static final String COURSE_SIGN_UP_REMIND_EARLY_TWO_HOURS = "COURSE_SIGN_UP_REMIND_EARLY_TWO_HOURS";

}
