package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzProjectTypeProject;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectTypeProjectParams;
import com.fykj.scaffold.zyz.service.IZyzProjectTypeProjectService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公益伙伴计划-项目类别中间表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
@RestController
@RequestMapping("/admin/project/type/project")
public class ZyzProjectTypeProjectController extends BaseController<IZyzProjectTypeProjectService, ZyzProjectTypeProject,ZyzProjectTypeProjectParams> {

}
