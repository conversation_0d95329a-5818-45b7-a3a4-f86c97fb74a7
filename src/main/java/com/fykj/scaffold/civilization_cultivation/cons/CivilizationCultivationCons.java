package com.fykj.scaffold.civilization_cultivation.cons;

public interface CivilizationCultivationCons {

    /**
     * 文明培育类型-父字典 code
     */
    String CC_TYPE_DICT = "CIVILIZATION_CULTIVATION_TYPE";

    /**
     * 文明培育类型-道德模范
     */
    String CC_TYPE_DDMF = "CCT_DDMF";

    /**
     * 文明培育类型-时代新人
     */
    String CC_TYPE_SDXR = "CCT_SDXR";

    /**
     * 文明培育类型-园区好人
     */
    String CC_TYPE_YQHR = "CCT_YQHR";

    /**
     * 文明培育类型-先进典型
     */
    String CC_TYPE_XJDX = "CCT_XJDX";

    /**
     * 文明培育状态-待处理
     */
    String STATUS_WAIT_DEAL = "CC_WAIT_DEAL";

    /**
     * 文明培育状态-已撤回
     */
    String STATUS_CALLBACK = "CC_CALLBACK";

    /**
     * 文明培育状态-待核实
     */
    String STATUS_WAIT_CHECK = "CC_WAIT_CHECK";

    /**
     * 文明培育状态-已核实
     */
    String STATUS_CHECK_PASS = "CC_CHECK_PASS";

    /**
     * 文明培育状态-已驳回
     */
    String STATUS_CHECK_REFUSE = "CC_CHECK_REFUSE";

}
