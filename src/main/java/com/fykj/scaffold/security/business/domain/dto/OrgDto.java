package com.fykj.scaffold.security.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.cms.domain.dto.CascaderDto;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-25
 */
@Data
@Accessors(chain = true)
public class OrgDto {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "code")
    private String value;

    @ApiModelProperty(value = "名称")
    private String label;



    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;



}
