package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbDictMapping;
import com.fykj.scaffold.sgb_docking.domain.vo.SgbDictMappingSysTypeVo;
import fykj.microservice.core.base.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 系统字典值与三方字典值映射服务接口
 */
public interface ISgbDictMappingService extends IBaseService<SgbDictMapping> {

    /**
     * 根据本系统字典类型和值获取第三方字典值列表
     * 对于系统没有的字段；用sgb_拼上社工部的字典类型，然后映射几个默认值；
     *
     * @param sysDictValue 本系统字典值
     * @return 第三方字典值列表
     */
    Map<String, String> multiGetSgbDictStrBySysDictValue(List<String> sysDictValue);

    String getSgbDictStrBySysDictValue(String sysDictValue);

    /**
     * 获取系统字典类型聚合列表
     *
     * @return 系统字典类型列表，使用SgbDictMappingSysTypeVo格式，支持字典翻译
     */
    List<SgbDictMappingSysTypeVo> getSysTypeList();
}