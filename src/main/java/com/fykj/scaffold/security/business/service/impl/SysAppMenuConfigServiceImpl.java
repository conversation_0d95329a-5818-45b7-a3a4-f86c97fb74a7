package com.fykj.scaffold.security.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.SysAppMenuConfig;
import com.fykj.scaffold.security.business.mapper.SysAppMenuConfigMapper;
import com.fykj.scaffold.security.business.service.ISysAppMenuConfigService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysAppMenuConfigServiceImpl extends BaseServiceImpl<SysAppMenuConfigMapper, SysAppMenuConfig> implements ISysAppMenuConfigService {

    @Override
    public IPage<SysAppMenuConfig> page(BaseParams params) {
        IPage<SysAppMenuConfig> menuConfigIPage = super.page(params);
        DictTransUtil.trans(menuConfigIPage.getRecords());
        return menuConfigIPage;
    }
}