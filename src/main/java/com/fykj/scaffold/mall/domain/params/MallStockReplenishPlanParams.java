package com.fykj.scaffold.mall.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 物料进补计划管理查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("物料进补计划管理查询参数")
public class MallStockReplenishPlanParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    private String type;

    private String orgCode;

    private String year;

    private Integer status;
}
