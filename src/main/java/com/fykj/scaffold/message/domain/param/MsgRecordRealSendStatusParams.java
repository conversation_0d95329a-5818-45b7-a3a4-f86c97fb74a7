package com.fykj.scaffold.message.domain.param;

import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date: 2023-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("消息真实发送状态查询参数")
public class MsgRecordRealSendStatusParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"mobile", "task_id"})
    @ApiModelProperty("关键词")
    private String keyword;

    @MatchType(value = QueryType.EQ, fieldName = "status")
    @ApiModelProperty("发送状态")
    private String status;

    @MatchType(value = QueryType.GE, fieldName = "receive_time")
    @ApiModelProperty("接收时间起")
    private String start;

    @MatchType(value = QueryType.LE, fieldName = "receive_time")
    @ApiModelProperty("接收时间止")
    private String end;
}
