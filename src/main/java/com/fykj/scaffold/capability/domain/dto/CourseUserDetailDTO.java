package com.fykj.scaffold.capability.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel("CourseUser Detail DTO")
public class CourseUserDetailDTO {

    @ApiModelProperty("ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("课程ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long courseId;

    @ApiModelProperty("用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty("应得学分")
    private Integer shouldCredits;

    @ApiModelProperty("实际学分")
    private Integer actualCredits;

    @ApiModelProperty("完成情况")
    private Boolean completeStatus;

    @ApiModelProperty("学分发放情况")
    private Boolean creditsStatus;

    @ApiModelProperty("签到时间")
    private LocalDateTime signInAt;

    @ApiModelProperty("签到地点")
    private String signInAddress;

    @ApiModelProperty("签到经度")
    private BigDecimal signInLongitude;

    @ApiModelProperty("签到纬度")
    private BigDecimal signInLatitude;

    @ApiModelProperty("完成时间")
    private LocalDateTime completedAt;
}
