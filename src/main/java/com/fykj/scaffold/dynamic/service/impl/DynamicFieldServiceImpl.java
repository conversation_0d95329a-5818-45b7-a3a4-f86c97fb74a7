package com.fykj.scaffold.dynamic.service.impl;

import com.fykj.scaffold.dynamic.domain.entity.DynamicField;
import com.fykj.scaffold.dynamic.mapper.DynamicFieldMapper;
import com.fykj.scaffold.dynamic.service.IDynamicFieldService;
import com.fykj.scaffold.support.error.exception.FieldBindException;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import java.util.Objects;


/**
 * 字段信息-服务实现类
 *
 * @date 2025-05-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DynamicFieldServiceImpl extends BaseServiceImpl<DynamicFieldMapper, DynamicField> implements IDynamicFieldService {

    @Override
    public boolean add(DynamicField entity) {
        // 校验字段是否存在
        if(isFieldNameExist(entity.getFieldName())) {
            throw new BusinessException(ResultCode.FAIL, "");
        }
        initSaveEntity(entity);
        return super.save(entity);
    }

    @Override
    public boolean edit(DynamicField newEntity) {
        // 校验字段是否存在
        DynamicField oldEntity = getById(newEntity.getId());
        if(Objects.equals(oldEntity.getLabel(), newEntity.getLabel())) {
            throw new BusinessException(ResultCode.FAIL, "不允许修改字段名称");
        }
        return super.updateById(newEntity);
    }

    @Override
    public boolean remove(Long id) {
        DynamicField entity = getById(id);
        if(Boolean.TRUE.equals(entity.getSystemFlag())) {
            throw new BusinessException(ResultCode.FAIL, "不允许删除系统字段");
        }
        return removeById(id);
    }

    @Override
    public void validateFieldNameExist(String fieldName) {
        if(isFieldNameExist(fieldName)) {
            throw new FieldBindException(DynamicField.Fields.label, "字段名称已存在");
        }
    }

    private void initSaveEntity(DynamicField entity) {
        entity.setFieldName(entity.getLabel());
        entity.setFieldType(entity.getFieldType());
        entity.setSystemFlag(false);
    }

    private boolean isFieldNameExist(String fieldName) {
        return baseMapper.isFieldNameExist(fieldName);
    }

}
