package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActs;
import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActsResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 活动发布招募同步
 */
@Component
public class ActivityRecruitSyncer {

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzSyncLogService logService;

    @Value("${act-sync-by-time-period}")
    private Boolean actSyncByTimePeriod = false;

    public ZyzSyncLog sync(long actTimePeriodId, long syncBillId) {
        ZyzActivityTimePeriod timePeriod = actTimePeriodService.getById(actTimePeriodId);
        Long activityId = timePeriod.getActivityId();
        ZyzActivity activity = activityService.getById(timePeriod.getActivityId());
        //活动同步成功，且发布招募处于待同步状态需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(timePeriod.getRecruitSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动时间段发布招募已同步成功，无需再次同步");
        }

        if (!ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(activity.getAuditStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动还未审核通过");
        }

        if (!timePeriod.getEndTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动时间段尚未结束");
        }
        String apId = Boolean.TRUE.equals(actSyncByTimePeriod) ? timePeriod.getSyncId() : activity.getSyncId();
        if (StringUtil.isEmpty(apId)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动还未同步");
        }

//        if (!Cons.PlatformSyncState.SUCCESS.equals(activity.getSync())) {
//            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动还未同步成功");
//        }
//
//        if (StringUtil.isEmpty(activity.getSyncId())) {
//            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动同步id未知");
//        }
        String startTime = timePeriod.getStartTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        String endTime = timePeriod.getEndTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));

        SyncActs syncActs = new SyncActs();
        syncActs.setUniCode(String.valueOf(timePeriod.getId()));
        syncActs.setApId(actSyncByTimePeriod ? timePeriod.getSyncId() : activity.getSyncId());
        syncActs.setActDate(startTime.substring(0, 10));
        syncActs.setStartTime(startTime.substring(11, 16));
        syncActs.setEndTime(endTime.substring(11, 16));

        SyncActsResult syncActsResult = ApiWebService.syncActivityRecruit(syncActs);

        timePeriod.setRecruitSyncTime(LocalDateTime.now());
        timePeriod.setRecruitSyncRemark(syncActsResult.getMsg());
        //成功
        if (syncActsResult.getMsg().contains("同步成功")) {
            timePeriod.setRecruitSyncId(syncActsResult.getActId());
            timePeriod.setRecruitSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (syncActsResult.getMsg().contains("数据重复")) {
            if (StringUtil.isNotEmpty(syncActsResult.getActId())) {
                timePeriod.setRecruitSyncId(syncActsResult.getActId());
            }
            timePeriod.setRecruitSync(Cons.PlatformSyncState.SUCCESS);
            timePeriod.setRecruitSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            timePeriod.setRecruitSync(Cons.PlatformSyncState.FAILURE);
        }
        // 启用单独事务处理日志
        int timePeriods = actTimePeriodService.lambdaQuery().eq(ZyzActivityTimePeriod::getActivityId, activityId).count().intValue();
        String actName = activity.getName();
        if (timePeriods > 1) {
            actName += ("【" + startTime + "--" + endTime + "】");
        }
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.ACTIVITY_RECRUIT_SYNC, syncActsResult.getCode(), syncActsResult.getMsg()
                , syncBillId, timePeriod.getId(), actName + "时间段发布招募"
                , JSONUtil.toJsonStr(syncActs), JSONUtil.toJsonStr(syncActsResult));

        // 回写业务
        actTimePeriodService.updateById(timePeriod);

        return log;
    }
}
