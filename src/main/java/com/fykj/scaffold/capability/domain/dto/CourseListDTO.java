package com.fykj.scaffold.capability.domain.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.capability.cons.CourseCategory;
import com.fykj.scaffold.capability.cons.CourseCreditRange;
import com.fykj.scaffold.capability.cons.CourseType;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel("Course List DTO")
public class CourseListDTO {

    @ApiModelProperty("课程ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课程地点")
    private String address;

    @ApiModelProperty("课程分类 字典CODE:" + CourseCategory.DICT_CODE)
    @DictTrans(transTo = "courseCategoryName")
    private String courseCategory;

    @ApiModelProperty("课程分类")
    private String courseCategoryName;

    @ApiModelProperty("课程类型 字典CODE:" + CourseType.DICT_CODE)
    @DictTrans(transTo = "courseTypeName")
    private String courseType;

    @ApiModelProperty("课程类型")
    private String courseTypeName;

    @ApiModelProperty("课程学分 (正常签到范围内可获得学分，线上视频学习需要学习完整可获得学分)")
    private Integer courseCredits;

    @ApiModelProperty("排序")
    private Integer orderNum;

    @ApiModelProperty("适用学分区间 字典CODE" + CourseCreditRange.DICT_CODE)
    @DictTrans(transTo = "applicableCreditRangeName")
    private String applicableCreditRange;

    @ApiModelProperty("适用学分区间")
    private String applicableCreditRangeName;

    @ApiModelProperty("线上课程截止时间 (过期后不可学习)")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN)
    private LocalDateTime onlineCourseDeadline;

    @ApiModelProperty("长期有效")
    private Boolean longTermValid;

    @ApiModelProperty("课程日期")
    private LocalDate courseDate;

    @ApiModelProperty("课程开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime courseStartTime;

    @ApiModelProperty("课程结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime courseEndTime;

    @ApiModelProperty("发布组织 (发布人所属组织)")
//    @DictTrans(transTo = "publishOrgName")
    private String publishOrg;

    @ApiModelProperty("发布组织 (发布人所属组织)")
    private String publishOrgName;

    @ApiModelProperty("完成情况")
    private Boolean completeStatus;

    @ApiModelProperty("对外展示(上下架")
    private Boolean outerDisplay;

    @ApiModelProperty("置顶")
    private Boolean top;

    @ApiModelProperty("状态")
    @DictTrans(transTo = "statusName")
    private String status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createDate;

}
