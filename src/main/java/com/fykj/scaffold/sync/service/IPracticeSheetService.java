package com.fykj.scaffold.sync.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto;
import com.fykj.scaffold.sync.domain.entity.PracticeSheet;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogStatisticsParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;


/**
 * <p>
 * 实践单服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface IPracticeSheetService extends IBaseService<PracticeSheet> {

    /**
     * 批量同步实践单
     * @param orderIds
     * @param type
     */
    void syncBatch(List<Long> orderIds, String type);

    /**
     * 单条同步实践单
     * @param orderId
     */
    void syncOne(Long orderId, String type);


    /**
     * 统计分页数据
     *
     * @param params
     * @return
     */
    IPage<SyncStatisticsDto> statisticsPage(ZyzSyncLogStatisticsParams params);


    /**
     * 获取年月下的数据
     *
     * @param yearMon
     * @return
     */
    SyncStatisticsDto getByYearMon(String yearMon);

    /**
     * 导出失败数据
     *
     * @param params
     * @return
     */
    List<SyncFailStatisticsDto> statisticsFailList(ZyzSyncLogStatisticsParams params);

    /**
     * 导出统计数据
     *
     * @param params
     * @return
     */
    List<ZyzSyncLogDto> exportStatistics(ZyzSyncLogStatisticsParams params);
}
