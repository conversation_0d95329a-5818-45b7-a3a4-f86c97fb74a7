package com.fykj.scaffold.security.business.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 2025/5/8
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZsqUserSyncDto implements Serializable {

    private static final long serialVersionUID = 5037703219971417242L;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "手机号")
    private String mobile;

    /**
     * 用户id（政务通id）
     */
    @ExcelProperty(value = "用户id")
    private String userId;

    /**
     * 层级（街道/社区）
     */
    @ExcelProperty(value = "层级")
    private String level;

    @ExcelProperty(value = "组织名称")
    private String gridName;

    @ExcelProperty(value = "是否离职")
    private Boolean quit;

    /**
     * 类型（社工/非社工）
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 数据时间（quit=true->离职时间；quit=false–>账号创建时间）
     */
    @ExcelProperty(value = "数据时间")
    private String queryDate;

    @ExcelProperty(value = "处理情况")
    private String status;

    @ExcelProperty(value = "备注（失败原因）")
    private String remark;

    @ExcelIgnore
    private Boolean validatePass;

    @ExcelIgnore
    private Boolean street;
}
