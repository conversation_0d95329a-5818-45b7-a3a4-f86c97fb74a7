package com.fykj.scaffold.portal_website.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 新闻资讯查询参数
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("新闻资讯查询参数")
public class NewsParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    /**
     * 标题
     */
    private String title;

    /**
     * 栏目id
     */
    private String categoryId;

    /**
     * 栏目id
     */
    private List<String> categoryIds;

    /**
     * 上级组织
     */
    private String orgCode;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 当前登录人组织编码
     */
    private String currentOrgCode;

    /**
     * 需要我审核的
     */
    private Boolean needAudit;

    /**
     * 栏目code
     */
    private String categoryCode;

    /**
     * 是否置顶
     */
    @ApiModelProperty("是否置顶")
    private Boolean stick;

    /**
     * 是否上架
     */
    private Boolean grounding;

    /**
     * 同步状态
     */
    private String sync;

    @ApiModelProperty("关键字")
    private String key;

    private Long userId;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime startTime;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime endTime;

    private String teamId;

    public NewsParams(BaseParams params) {
        setPageSize(params.getPageSize());
        setCurrentPage(params.getCurrentPage());
        setOrders(params.getOrders());
    }

}
