package com.fykj.scaffold.race.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
public class PhotoDto {

    /**
     * 作者
     */

    @ApiModelProperty(value = "作者")
    private String author;



    @ApiModelProperty(value = "编号")
    private Integer number;


    @ApiModelProperty(value = "作品名")
    private String name;


    /**
     * 图片列表
     */
    @TableField(value = "picture_list", typeHandler = PictureDto.pictureListTypeHandler.class)
    @ApiModelProperty(value = "图片列表")
    private List<PictureDto> pictureList = new ArrayList<>();
}
