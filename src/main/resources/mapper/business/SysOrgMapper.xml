<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.security.business.mapper.SysOrgMapper">

    <select id="findByParent" resultType="com.fykj.scaffold.security.business.domain.dto.OrgDto">
        SELECT
        code as value,
        name as label,
        id as id
        FROM
        sys_org
        WHERE
        is_deleted = 0
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
        ORDER BY sequence ASC
    </select>

    <select id="getSubAssociationSumData" resultType="com.fykj.scaffold.zyz.domain.dto.dashboard_sum.SubAssociationDataSumDto">
        SELECT
            org_t.name AS subAssociationName,
            v_t.vNum AS volunteerNum,
            v_t.vaNum AS activeVolunteerNum,
            IFNULL(v_t.tsl, 0.00) AS serviceTimeTotal,
            t_t.tNum AS teamNum,
            a_t.aNum AS actNum
        FROM (
            SELECT id, code, name
            FROM sys_org
            WHERE code IN
            <foreach item="item" collection="orgCodes" index="index" separator="," open="(" close=")">
             #{item}
            </foreach>
        ) AS org_t
        LEFT JOIN (
            SELECT o1.parent_id AS cpid, COUNT(v.id) AS vNum, SUM(IF(v.is_lively = 1, 1, 0)) AS vaNum, SUM(IF(v.service_long IS NULL, 0.00, v.service_long)) AS tsl
            FROM zyz_volunteer v
            LEFT JOIN sys_org o1 ON v.org_code = o1.code
            WHERE v.is_deleted = 0 AND v.write_off = 0
            GROUP BY o1.parent_id
        ) AS v_t ON org_t.id = v_t.cpid
        LEFT JOIN (
            SELECT org_code, SUM(IF(is_deleted = 0, 1, 0)) AS tNum FROM zyz_team
            WHERE org_code IN
            <foreach item="item" collection="orgCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            GROUP BY org_code
        ) AS t_t ON org_t.code = t_t.org_code
        LEFT JOIN (
            SELECT publish_org_code, SUM(IF(is_deleted = 0, 1, 0)) AS aNum FROM zyz_activity
            WHERE publish_org_code IN
            <foreach item="item" collection="orgCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            GROUP BY publish_org_code
        ) AS a_t ON org_t.code = a_t.publish_org_code
        GROUP BY org_t.code
        ORDER BY volunteerNum DESC
    </select>

    <select id="getNameByCodes" resultType="com.fykj.scaffold.security.business.domain.entity.SysOrg">
        SELECT code, name
        FROM sys_org
        WHERE code IN (<foreach item="item" collection="codes" index="" separator=",">#{item}</foreach>)
    </select>
</mapper>
