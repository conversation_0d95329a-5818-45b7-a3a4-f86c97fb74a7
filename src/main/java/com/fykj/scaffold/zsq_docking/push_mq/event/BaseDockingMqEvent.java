package com.fykj.scaffold.zsq_docking.push_mq.event;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 对接消息事件（base）
 *
 * <AUTHOR>
 * @date 2025.04.21
 */
@Data
public class BaseDockingMqEvent {

    /**
     * 消息 id
     */
    private long msgId;

    /**
     * 消息创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime msgCreateTime = LocalDateTime.now();

    public BaseDockingMqEvent() {
        this.msgId = IdUtil.getSnowflake(1L, 1L).nextId();
    }
}
