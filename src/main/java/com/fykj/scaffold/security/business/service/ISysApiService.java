package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.dto.ApiListDto;
import com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.security.business.domain.entity.SysApi;

import java.util.List;

/**
 * 系统资源-API接口
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface ISysApiService extends IBaseService<SysApi> {
    /**
     * 获取系统中角色与接口的关联关系
     *
     * @return
     */
    List<ProtectedApiOAuthDto> getRoleApiPathList();

    /**
     * 获取接口列表
     * @return
     */
    List<ApiListDto> bindList();
}

