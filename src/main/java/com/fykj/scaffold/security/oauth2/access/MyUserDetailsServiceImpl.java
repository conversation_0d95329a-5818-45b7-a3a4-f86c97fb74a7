package com.fykj.scaffold.security.oauth2.access;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.security.business.domain.entity.*;
import com.fykj.scaffold.security.business.service.*;
import constants.Mark;
import fykj.microservice.core.base.BaseEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import utils.StringUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MyUserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private ISysUserExpandService userExpandService;

    @Autowired
    private IUserWriteOffService userWriteOffService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User one = userService.lambdaQuery().eq(User::getUsername, username).one();

        if (one == null) {
            throw new UsernameNotFoundException("该用户不存在");
        }

        if (!one.getStatus()) {
            UserWriteOff userWriteOff = userWriteOffService.lambdaQuery().eq(UserWriteOff::getUserId, one.getId()).one();
            if (userWriteOff == null) {
                throw new LockedException("账号已被禁用");
            }
            if (userWriteOff.getCancelApply()) {
                throw new LockedException("账号已被禁用");
            }
            if (!userWriteOff.getRemoved()) {
                throw new LockedException("您已申请注销账号，账号被禁用！");
            } else {
                throw new LockedException("您账号已被彻底注销，可通过小程序重新注册！");
            }
        }

        List<Role> roles = userRoleService.getByUserId(one.getId());
        if (CollectionUtil.isEmpty(roles)) {
            throw new LockedException("此用户未分配权限，无法登录系统");
        }

        String roleIds = StringUtil.join(roles.stream().map(BaseEntity::getId).collect(Collectors.toList()), Mark.COMMA);

        BackendUserDetail user = new BackendUserDetail(one.getUsername(), one.getPassword(), one.getStatus(), one.getStatus(), true, true, AuthorityUtils.commaSeparatedStringToAuthorityList(roleIds));

        user.setId(one.getId());
        user.setNickName(one.getName());
        BeanUtils.copyProperties(one, user);
        user.setRoleCodes(roles.stream().map(Role::getCode).collect(Collectors.toList()));
        user.setRoleIds(roles.stream().map(Role::getId).collect(Collectors.toList()));
        user.setCanWechatFastLogin(StringUtil.isNotEmpty(one.getUnionId()));
        handleExpand(user);
        return user;
    }

    /**
     * 处理用户身份
     *
     * @param user
     */
    private void handleExpand(BackendUserDetail user) {
        List<SysUserExpand> expandList = userExpandService.getByUserId(user.getId());
        if (CollectionUtil.isEmpty(expandList)) {
            return;
        }
        //获取默认的身份
        SysUserExpand expand = expandList.stream().filter(SysUserExpand::getDefaultSetting).findAny().orElse(expandList.get(0));
        //如果有超过一个身份，可以切换
        if (expandList.size() > 1) {
            user.setNeedSelectCapacity(true);
        }
        user.setExpandId(expand.getId());
        user.setManagerCapacityName(expand.getCapacityFullName());
        user.setManagerCapacity(expand.getRoleCode());
        if (StringUtil.isNotEmpty(expand.getOrgCode())) {
            SysOrg org = orgService.getByCode(expand.getOrgCode());
            if (org == null) {
                throw new LockedException("您账号所在组织的存在异常，无法登录系统，请联系管理员");
            }
            user.setOrgCode(org.getCode());
            user.setOrgCodePrefix(org.getCodePrefix());
        }
        user.setTeamId(expand.getTeamId());
    }
}
