package com.fykj.scaffold.race.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.domain.params.PhotoParams;
import org.apache.ibatis.annotations.Param;

/**
 * 摄影大赛-作品表
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
public interface PhotoMapper extends BaseMapper<Photo> {

    IPage<Photo> getPage(IPage<Photo> page,@Param("param") PhotoParams params);

    Integer getMaxNumber();
}
