<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallPadExchangeStationMapper">

    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation">
        SELECT
            pes.*,
            o.name AS orgName
        FROM mall_pad_exchange_station pes
        LEFT JOIN sys_org o on pes.org_code = o.code
        WHERE pes.is_deleted = 0
        <if test="params.keyword != null and params.keyword != ''">
            AND (pes.code LIKE CONCAT('%',#{params.keyword},'%') OR pes.name LIKE CONCAT('%',#{params.keyword},'%'))
        </if>
        <if test="params.linkPhone != null and params.linkPhone != ''">
            AND pes.link_phone LIKE CONCAT('%',#{params.linkPhone},'%')
        </if>
        <if test="params.status != null">
            AND pes.status = #{params.status}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND o.code_prefix LIKE CONCAT(#{params.orgCode},'%')
        </if>
        ORDER BY pes.create_date DESC
    </select>

    <select id="pageForApi" resultType="com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation">
        SELECT
            pes.*,
        <if test="params.latitude != null and params.latitude != '' and params.longitude != null and params.longitude != '' ">
            (6378.138*2*ASIN(SQRT(POW(SIN((#{params.latitude}*PI()/180-pes.latitude*PI()/180)/2),2)
            + COS(#{params.latitude}*PI()/180) * COS(pes.latitude*PI()/180) *
            POW(SIN((#{params.longitude}*PI()/180-pes.longitude*PI()/180)/2),2)))*1000) as distance,
        </if>
            o.name as orgName
        FROM mall_pad_exchange_station pes
        LEFT JOIN sys_org o on pes.org_code = o.code
        WHERE pes.is_deleted=FALSE
        <if test="params.keyword != null and params.keyword != ''">
            AND (pes.code LIKE CONCAT('%',#{params.keyword},'%') OR pes.name LIKE CONCAT('%',#{params.keyword},'%'))
        </if>
        <if test="params.linkPhone != null and params.linkPhone != ''">
            AND pes.link_phone LIKE CONCAT('%',#{params.linkPhone},'%')
        </if>
        <if test="params.status != null">
            AND pes.status = #{params.status}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND o.code_prefix LIKE CONCAT(#{params.orgCode},'%')
        </if>
        <if test="params.latitude != null and params.latitude != '' and params.longitude != null and params.longitude != '' ">
         ORDER BY distance
        </if>
        <if test="params.latitude == null or params.latitude == '' or params.longitude == null or params.longitude == '' ">
            ORDER BY pes.create_date DESC
        </if>
    </select>

    <select id="getStationsByCurrOrg" resultType="com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation">
        SELECT
            pes.*,
            o.name AS orgName
        FROM mall_pad_exchange_station pes
        LEFT JOIN sys_org o ON pes.org_code = o.code
        WHERE pes.is_deleted = 0
        ORDER BY pes.create_date DESC
    </select>
</mapper>
