package com.fykj.scaffold.big_screen.service;

import com.fykj.scaffold.big_screen.domain.entity.CivilizationEnterprise;
import com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationEnterpriseDto;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * <p>
 * 文明培育服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface ICivilizationEnterpriseService extends IBaseService<CivilizationEnterprise> {

    /**
     * 文明单位状态变更
     * @param id
     */
    void statusChange(Long id);

    /**
     * 获取大屏数据列表
     * @param params
     * @return
     */
    List<CivilizationEnterpriseDto> getScreenDataList(DataScreenParams params);
}
