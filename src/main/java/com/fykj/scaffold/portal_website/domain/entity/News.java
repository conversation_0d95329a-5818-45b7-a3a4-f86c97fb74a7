package com.fykj.scaffold.portal_website.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cms_content")
public class News extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 标题
     */
    @TableField("title")
    @NotBlank(message = "标题不能为空")
    @ApiModelProperty(value = " 标题")
    private String title;
    /**
     * 虚拟阅读量
     */
    @TableField("virtual_Reading")
    @ApiModelProperty(value = " 虚拟阅读量")
    private Integer virtualReading;
    /**
     * 实际阅读量
     */
    @TableField("actual_reading")
    @ApiModelProperty(value = " 实际阅读量")
    private Integer actualReading;

    /**
     * 图片
     */
    @TableField(value = "title_img_url", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = " 图片url")
    private String titleImgUrl;

    /**
     * 简介
     */
    @TableField(value = "brief_introduction", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = " 简介")
    private String briefIntroduction;

    /**
     * 详细描述
     */
    @TableField(value = "description", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = " 详细描述")
    private String description;

    /**
     * 生效时间
     */
    @TableField("effective_date")
    @NotNull(message = "生效时间不能为空")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)  //格式化前台日期参数注解
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    @ApiModelProperty(value = " 生效时间")
    private LocalDateTime effectiveDate;

    /**
     * 审核状态
     */
    @TableField("audit_status")
    @ApiModelProperty(value = " 审核状态")
    @DictTrans(transTo = "auditStatusText")
    private String auditStatus;

    /**
     * 审核状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = " 审核状态text")
    private String auditStatusText;

    /**
     * 审核备注
     */
    @TableField(value = "audit_memo", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = " 审核备注")
    private String auditMemo;

    /**
     * 是否是团队发布
     */
    @TableField("team_publish")
    @ApiModelProperty(value = " 是否是团队发布")
    private Boolean teamPublish;

    /**
     * 团队id
     */
    @ApiModelProperty(value = " 团队id")
    @TableField("team_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;


    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", updateStrategy = FieldStrategy.IGNORED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long projectId;

    @ApiModelProperty(value = "活动id")
    @TableField(value = "activity_id", updateStrategy = FieldStrategy.IGNORED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;


    /**
     * 组织编码
     */
    @ApiModelProperty(value = " 组织编码")
    @TableField("org_code")
    private String orgCode;

    /**
     * 组织编码名称
     */
    @ApiModelProperty(value = " 组织编码名称")
    @TableField(exist = false)
    private String orgName;

    /**
     * 可以审核的组织编码
     */
    @ApiModelProperty(value = " 可以审核的组织编码")
    @TableField("audit_org_code")
    private String auditOrgCode;

    /**
     * 类型
     */
    @ApiModelProperty(value = " 类型")
    @TableField("type")
    private String type;

    /**
     * 是否上架
     */
    @ApiModelProperty(value = "是否上架")
    @TableField("grounding")
    private Boolean grounding;

    /**
     * 栏目id
     */
    @ApiModelProperty(value = "栏目id")
    @NotEmpty(message = "栏目不能为空")
    @TableField(exist = false)
    private List<String> categoryIds;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(exist = false)
    private String category;

    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    @TableField(exist = false)
    private String publisher;

    /**
     * 是否置顶
     */
    @ApiModelProperty(value = "是否置顶")
    @TableField(exist = false)
    private Boolean stick;

    /**
     * 同步状态
     */
    @TableField("is_sync")
    @DictTrans(transTo = "syncText")
    @ApiModelProperty(value = "同步状态")
    private String sync;
    /**
     * 是否点赞
     */
    @ApiModelProperty(value = " 是否点赞")
    @TableField(exist = false)
    private Boolean thumbsUp;
    /**
     * 是否收藏
     */
    @ApiModelProperty(value = " 是否收藏")
    @TableField(exist = false)
    private Boolean collect;
    /**
     * 同步状态Text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "同步状态Text")
    private String syncText;

    /**
     * 同步备注
     */
    @TableField(value = "sync_remark", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 同步成功后市区返回的Id
     */
    @TableField("sync_id")
    @ApiModelProperty(value = "同步成功后市区返回的Id")
    private String syncId;

    /**
     * 自定义链接
     */
    @TableField("custom_links")
    @ApiModelProperty(value = "自定义链接")
    private String customLinks;

    /**
     * 是否启用链接
     */
    @TableField("use_custom_links")
    @ApiModelProperty(value = " 是否启用链接")
    private Boolean useCustomLinks;

    /**
     * 是否需要对接知社区
     */
    @TableField(exist = false)
    @ApiModelProperty(value = " 是否需要对接知社区")
    private Boolean dockingZSQ;
}
