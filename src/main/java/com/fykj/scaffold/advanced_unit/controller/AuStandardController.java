package com.fykj.scaffold.advanced_unit.controller;

import com.fykj.scaffold.advanced_unit.domain.entity.AuStandard;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardParams;
import com.fykj.scaffold.advanced_unit.service.IAuStandardService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.ArrayList;
import java.util.List;

/**
 * 文明单位-指标标准-测评版本
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Api(tags = "管理端-文明单位-测评标准")
@RestController
@RequestMapping("/admin/manager/advanced-unit/standard")
public class AuStandardController
        extends BaseController<IAuStandardService, AuStandard, AuStandardParams> {

    @ApiOperation("获取年度标准列表")
    @GetMapping({"/list"})
    public JsonResult<List<AuStandard>> queryList(AuStandardParams params) {
        params.setStatus(true);
        List<BaseParams.Order> orders = new ArrayList<>();
        orders.add(new BaseParams.Order("year", "desc"));
        params.setOrders(orders);
        return new JsonResult<>(baseService.list(params));
    }

    @ApiOperation("上下架")
    @PostMapping("/status/switch")
    public Result statusSwitch(@RequestParam String id) {
        baseService.updateShowStatus(id);
        return OK;
    }

}
