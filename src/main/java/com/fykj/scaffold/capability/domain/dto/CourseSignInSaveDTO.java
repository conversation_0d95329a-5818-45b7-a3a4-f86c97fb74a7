package com.fykj.scaffold.capability.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel("签到dto")
public class CourseSignInSaveDTO {

    @ApiModelProperty("课程ID")
    @NotNull(message = "课程ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long courseId;

    @ApiModelProperty("签到地点")
//    @NotNull(message = "签到经度不能为空")
    private String signInAddress;

    @ApiModelProperty("签到经度")
    @NotNull(message = "签到经度不能为空")
    private BigDecimal signInLongitude;

    @ApiModelProperty("签到纬度")
    @NotNull(message = "签到纬度不能为空")
    private BigDecimal signInLatitude;

}
