package com.fykj.scaffold.zsq_docking.log.controller;


import com.fykj.scaffold.zsq_docking.log.domain.dto.ActivityDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.dto.NewsDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.dto.ResourceDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingResourceRecord;
import com.fykj.scaffold.zsq_docking.log.domain.params.ZSQDockingResourceRecordParams;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingResourceRecordService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

@RestController
@RequestMapping("/admin/zsq_docking_resource_record")
@Api(tags = "知社区资源对接记录")
public class ZSQDockingResourceRecordController extends BaseController<IZSQDockingResourceRecordService, ZSQDockingResourceRecord, ZSQDockingResourceRecordParams> {

    @ApiOperation("获取全部方法")
    @GetMapping({"/getResourceDockingRecords"})
    public JsonResult<List<ZSQDockingResourceRecord>> getResourceDockingRecords(@RequestParam Long resourceId) {
        return new JsonResult<>(this.baseService.getResourceDockingRecords(resourceId));
    }
    @ApiOperation("失败重试")
    @GetMapping({"/retry"})
    public Result retry(@RequestParam Long resourceId, @RequestParam Long recordId) {
        baseService.retry(resourceId, recordId);
        return OK;
    }

    @ApiOperation("获取对接记录详情")
    @GetMapping({"/getDockingRecordDetail"})
    public JsonResult<ResourceDockingRecordDetailDto> getDockingRecordDetail(@RequestParam Long recordId) {
        return new JsonResult<>(baseService.getDockingRecordDetail(recordId));
    }
}
