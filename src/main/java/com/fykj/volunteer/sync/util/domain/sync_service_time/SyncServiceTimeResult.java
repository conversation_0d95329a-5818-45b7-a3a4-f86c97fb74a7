package com.fykj.volunteer.sync.util.domain.sync_service_time;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <servertime>
 *   <info>
 *     <info>
 *       <code>FAIL</code>
 *       <msg>同步失败：招募id不存在</msg>
 *       <uniCode>102327</uniCode>
 *     </info>
 *   </info>
 * </servertime>
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncServiceTimeResult {
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "servertime")
    public static class ServerTime {
        private SyncServiceTimeResultInfo info;
    }
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "info")
    public static class SyncServiceTimeResultInfo {
        private List<SyncServiceTimeResult> info;
    }
}
