package com.fykj.scaffold.race.service.impl;

import com.fykj.scaffold.race.domain.entity.ActivityPrizeGrade;
import com.fykj.scaffold.race.mapper.ActivityPrizeGradeMapper;
import com.fykj.scaffold.race.service.IActivityPrizeGradeService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * 摄影大赛-活动奖项等级表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ActivityPrizeGradeServiceImpl extends BaseServiceImpl<ActivityPrizeGradeMapper, ActivityPrizeGrade> implements IActivityPrizeGradeService {


    @Override
    public List<ActivityPrizeGrade> getPrize(Long activityId) {
        return lambdaQuery().eq(ActivityPrizeGrade::getActivityId, activityId).orderByAsc(ActivityPrizeGrade::getSequence).list();
    }

    @Override
    public List<String> getPrizeName(Long activityId) {
        List<ActivityPrizeGrade> list = lambdaQuery().eq(ActivityPrizeGrade::getActivityId, activityId).orderByAsc(ActivityPrizeGrade::getSequence).list();
        List<String> names = new ArrayList<>();
        list.forEach(item -> {
            names.add(item.getName());
        });
        return names;
    }
}