package com.fykj.scaffold.race.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 摄影大赛-活动奖项作品关联表
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("race_activity_prize")
public class ActivityPrize extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 奖项id
     */
    @TableField("grade_id")
    @ApiModelProperty(value = "奖项id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long gradeId;

    /**
     * 作品id
     */
    @TableField("photo_id")
    @ApiModelProperty(value = "作品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long photoId;

    /**
     * 照片序号，例如1，2，5
     */
    @TableField("pic_nums")
    @ApiModelProperty(value = "照片序号，例如1，2，5")
    @JsonSerialize(using = ToStringSerializer.class)
    private String picNums;


    @TableField("photo_name")
    @ApiModelProperty(value = "作品名称")
    private String photoName;


}
