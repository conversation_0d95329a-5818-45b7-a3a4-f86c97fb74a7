package com.fykj.scaffold.advanced_unit.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitIndexEvaluateCountDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandard;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardIndexUnit;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardItem;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardIndexUnitParams;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardItemParams;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardParams;
import com.fykj.scaffold.advanced_unit.service.IAuStandardIndexUnitService;
import com.fykj.scaffold.advanced_unit.service.IAuStandardItemService;
import com.fykj.scaffold.advanced_unit.service.IAuStandardService;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 文明单位-指标标准-测评版本
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Api(tags = "用户端-文明单位-测评标准")
@RestController
@Validated
@RequestMapping("/api/advanced-unit/standard")
public class StandardApi {

    @Autowired
    private IAuStandardService standardService;

    @Autowired
    private IAuStandardItemService standardItemService;

    @Autowired
    private IAuStandardIndexUnitService indexUnitService;

    @ApiOperation("获取年度标准列表")
    @GetMapping({"/list"})
    public JsonResult<List<AuStandard>> queryList(AuStandardParams params) {
        params.setStatus(true);
        List<BaseParams.Order> orders = new ArrayList<>();
        orders.add(new BaseParams.Order("year", "desc"));
        params.setOrders(orders);
        return new JsonResult<>(standardService.list(params));
    }

    @ApiOperation("获取年度标准-指标分类")
    @PostMapping({"/item/list"})
    public JsonResult<List<AuStandardItem>> page(
            @RequestBody(required = false) @Valid AuStandardItemParams params) {
        return new JsonResult<>(standardItemService.list(params));
    }

    @ApiOperation("获取年度标准-指标项-分页列表")
    @PostMapping({"/item/index/page"})
    public JsonResult<IPage<AuStandardIndexUnit>> queryIndexEvaluatePage(
            @RequestBody(required = false) @Valid AuStandardIndexUnitParams params) {
        return new JsonResult<>(indexUnitService.queryIndexEvaluatePage(params));
    }

    @ApiOperation("单位评测管理-统计")
    @PostMapping({"/count"})
    public JsonResult<AuUnitIndexEvaluateCountDTO> countIndexEvaluate(
            @RequestBody(required = false) @Valid AuStandardIndexUnitParams params) {
        return new JsonResult<>(indexUnitService.countIndexEvaluate(params));
    }

}
