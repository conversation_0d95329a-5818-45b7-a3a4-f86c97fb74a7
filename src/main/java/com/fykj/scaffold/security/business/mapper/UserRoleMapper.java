package com.fykj.scaffold.security.business.mapper;

import com.fykj.scaffold.security.business.domain.entity.SysUserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 系统角色-用户关联
 * <p>
 * Mapper 接口
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
public interface UserRoleMapper extends BaseMapper<SysUserRole> {
    /**
     * 物理删除关联
     *
     * @param userId
     */
    void deleteByUserId(long userId);

}
