package com.fykj.scaffold.big_screen.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 文明单位管理查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("文明培育管理查询参数")
public class CivilizationEnterpriseParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    /**
     * 名称
     */
    @MatchType(value = QueryType.LIKE, fieldName = "name")
    private String name;

    /**
     * 状态
     */
    @MatchType(value = QueryType.EQ, fieldName = "status")
    private Boolean status;
}
