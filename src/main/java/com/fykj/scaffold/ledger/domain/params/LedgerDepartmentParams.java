package com.fykj.scaffold.ledger.domain.params;

import fykj.microservice.core.base.BaseParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 台账责任局办-查询参数
 *
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("台账责任局办-查询参数")
public class LedgerDepartmentParams extends BaseParams {

    private static final long serialVersionUID = 2496698019947431302L;

    @ApiModelProperty("局办名称")
    @MatchType(value = QueryType.LIKE)
    private String departmentName;

    @ApiModelProperty("联系人")
    @MatchType(value = QueryType.LIKE)
    private String contactPerson;

    @ApiModelProperty("关联账号")
    @MatchType(value = QueryType.LIKE)
    private String account;
}
