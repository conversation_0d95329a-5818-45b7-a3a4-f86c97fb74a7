package com.fykj.scaffold.mall.service.impl;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsDto;
import com.fykj.scaffold.mall.domain.dto.OrderFormDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsNumChange;
import com.fykj.scaffold.mall.domain.entity.MallSelfMachine;
import com.fykj.scaffold.mall.domain.entity.MallStock;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsParams;
import com.fykj.scaffold.mall.mapper.MallScoreGoodsMapper;
import com.fykj.scaffold.mall.service.IMallScoreGoodsNumChangeService;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.mall.service.IMallSelfMachineService;
import com.fykj.scaffold.mall.service.IMallStockService;
import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.oss.IOssSaver;
import com.fykj.scaffold.support.oss.impl.LocalOssSaver;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import com.fykj.scaffold.zyz.domain.entity.SysUserAddress;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.ISysUserAddressService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.util.BeanUtil;
import fykj.microservice.core.support.util.SpringContextUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import javax.imageio.ImageIO;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static cn.hutool.core.text.StrPool.COMMA;
import static com.fykj.scaffold.mall.cons.MallCons.MALL_SCORE_GOODS_TYPE_PAD;


/**
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallScoreGoodsServiceImpl extends BaseServiceImpl<MallScoreGoodsMapper, MallScoreGoods> implements IMallScoreGoodsService {
    @Autowired
    private IMallScoreGoodsNumChangeService mallScoreGoodsNumChangeService;
    @Autowired
    private IZyzVolunteerService volunteerService;
    @Autowired
    private ISysUserAddressService userAddressService;
    @Autowired
    private IMallStockService stockService;
    @Autowired
    private IMallSelfMachineService selfMachineService;
    @Autowired
    private ISysOrgService orgService;


    /**
     * 分页查询--后端页面
     *
     * @param params
     * @return
     */
    @Override
    public IPage<MallScoreGoods> getPage(MallScoreGoodsParams params) {
        if (params == null) {
            params = new MallScoreGoodsParams();
        }
        IPage<MallScoreGoods> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public IPage<MallScoreGoods> pageForApi(MallScoreGoodsParams params) {
        if (params == null) {
            params = new MallScoreGoodsParams();
        }
        IPage<MallScoreGoods> iPage = baseMapper.pageForApi(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public boolean updateStatus(long goodsId, boolean isClearStorage) {
        MallScoreGoods goods = getById(goodsId);
        if (isClearStorage && goods.getGoodsCount() != 0) {
            changeGoodsNum(goodsId, -goods.getGoodsCount(), goods.getStatus() ? "商品下架清空库存" : "商品上架清空库存重新登记");
            goods.setGoodsCount(0);
        }
        goods.setStatus(!goods.getStatus());
        return updateById(goods);
    }

    @Override
    public void editGoodsNum(long goodsId, int goodsNum) {
        MallScoreGoods goods = getById(goodsId);
        boolean isAdd = goodsNum > 0;
        //变动商品数量
        changeGoodsNum(goodsId, goodsNum, isAdd ? "商品入库" : "商品出库");
        //联动物料库存
        MallStock stock = stockService.lambdaQuery().eq(MallStock::getSku, goods.getSku()).one();
        if (stock == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "当前商品绑定的物料不存在，请检查SKU");
        }
        if (isAdd) {
            stockService.reduceStock(stock.getId(), Math.abs(goodsNum), "商品添加库存，对应扣除物料可用库存");
        } else {
            stockService.addStock(stock.getId(), Math.abs(goodsNum), "商品返回库存至物料，对应增加物料可用库存");
        }
    }

    @Override
    public void changeGoodsNum(long goodsId, int goodsNum, String remark) {
        if (goodsNum == 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请输入具体商品变动数量");
        }
        MallScoreGoods goods = getById(goodsId);
        if (goods.getGoodsCount() + goodsNum < 0) {
            throw new BusinessException(ResultCode.FAIL, "请核对，商品数量不足！");
        }
        logMallGoodsStorageChange(goods, goodsNum, remark);
        goods.setGoodsCount(goods.getGoodsCount() + goodsNum);
        updateById(goods);
    }

    /**
     * 记录商品库存的变化
     *
     * @param goods    商品信息
     * @param goodsNum 变化的数量（用户兑换为负数，从物料库迁移过来为正数）
     * @param remark   备注
     * @return
     */
    private void logMallGoodsStorageChange(MallScoreGoods goods, int goodsNum, String remark) {
        MallScoreGoodsNumChange goodsNumChange = new MallScoreGoodsNumChange();
        goodsNumChange.setChangeBeforeNum(goods.getGoodsCount());
        goodsNumChange.setChangeNum(Math.abs(goodsNum));
        goodsNumChange.setChangeAfterNum(goods.getGoodsCount() + goodsNum);
        goodsNumChange.setGoodsId(goods.getId());
        goodsNumChange.setOperateDate(LocalDateTime.now());
        goodsNumChange.setOperateUserId((Long) Oauth2Util.getUserId());
        goodsNumChange.setOperateUserName(StringUtil.isNotEmpty(Oauth2Util.getUserId()) ? Oauth2Util.getUser().getNickName() : goods.getMachineNo());
        goodsNumChange.setRemark(remark);
        if (goodsNum < 0) {
            goodsNumChange.setChangeType(MallCons.MALL_SCORE_GOODS_NUM_CHANGE_TYPE_OUT);
        } else {
            goodsNumChange.setChangeType(MallCons.MALL_SCORE_GOODS_NUM_CHANGE_TYPE_IN);
        }
        mallScoreGoodsNumChangeService.save(goodsNumChange);
    }


    @Override
    public OrderFormDto getOrderFormDto(Long id) {
        MallScoreGoods goods = getById(id);
        if (ObjectUtil.isEmpty(goods)) {
            throw new BusinessException(ResultCode.FAIL, "当前网络拥挤，商品未找到");
        }
        if (goods.getGoodsCount() == 0) {
            throw new BusinessException(ResultCode.FAIL, "商品库存告急，请重新试一下");
        }
        OrderFormDto dto = new OrderFormDto();
        BeanUtil.copyProperties(goods, dto);
        dto.setGoodsId(id);
        dto.setOrderNo(IdUtil.getSnowflake(1L, 1L).nextIdStr());
        dto.setGoodsNum(goods.getGoodsCount());
        Long userId = (Long) Oauth2Util.getUserId();
        if (StringUtil.isEmpty(userId)) {
            throw new BusinessException(ResultCode.FAIL, "用户未处于登录状态，请重新试一下");
        }
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        BigDecimal exchangePoint = new BigDecimal("12.50");
        if (exchangePoint.compareTo(volunteer.getTotalPoint()) > -1) {
            throw new BusinessException(ResultCode.FAIL, "用户积分不足,不能兑换");
        }
        BigDecimal orgPoint = new BigDecimal(100);
        if (orgPoint.compareTo(volunteer.getTotalPoint()) > -1 && Cons.TOP_DEPT_CODE.equals(goods.getOrgCode())) {
            throw new BusinessException(ResultCode.FAIL, "用户积分不足,不能兑换协会商品");
        }
        SysUserAddress address = userAddressService.lambdaQuery()
                .eq(SysUserAddress::getUserId, userId).eq(SysUserAddress::getTacitlyApprove, true).one();
        if (ObjectUtil.isEmpty(address)) {
            List<SysUserAddress> addresses = userAddressService.lambdaQuery().eq(SysUserAddress::getUserId, userId)
                    .list();
            address = CollectionUtil.isNotEmpty(addresses) ? addresses.get(0) : new SysUserAddress();
        }
        dto.setUserAddress(address);
        return dto;
    }

    @Override
    public List<MallScoreGoodsDto> getGoodsList(MallScoreGoodsParams params) {
        return DictTransUtil.trans(baseMapper.getGoodsList(params));
    }

    @Override
    public boolean updateMallGoods(MallScoreGoods goods) {
        if (MallCons.MALL_SCORE_GOODS_TYPE_MACHINE.equals(goods.getScoreGoodsType())) {
            if (lambdaQuery().eq(MallScoreGoods::getMachineGoodsCode, goods.getMachineGoodsCode()).ne(BaseEntity::getId, goods.getId()).count() > 0) {
                throw new BusinessException(ResultCode.FAIL, "请核对，商品机器编码重复！");
            }
        }
        goods.setOrgName(orgService.getOrgName(goods.getOrgCode()));
        return updateById(goods);
    }

    @Override
    public int sumGoodsCountBySku(String sku) {
        return baseMapper.sumGoodsCountBySku(sku);
    }

    @Override
    public void saveMallGoods(MallScoreGoods goods) {
        MallStock stock = stockService.lambdaQuery().eq(MallStock::getSku, goods.getSku()).one();
        if (ObjectUtil.isEmpty(stock)) {
            throw new BusinessException(ResultCode.FAIL, "请核对，物料编码有误！");
        }
        if (stock.getInventoryQuantity() - goods.getGoodsCount() < 0) {
            throw new BusinessException(ResultCode.FAIL, "请核对，物料数量不足！");
        }
        if (MallCons.MALL_SCORE_GOODS_TYPE_MACHINE.equals(goods.getScoreGoodsType())) {
            if (lambdaQuery().eq(MallScoreGoods::getMachineGoodsCode, goods.getMachineGoodsCode()).count() > 0) {
                throw new BusinessException(ResultCode.FAIL, "请核对，商品机器编码重复！");
            }
            if (selfMachineService.lambdaQuery().eq(MallSelfMachine::getMachineNo, goods.getMachineNo()).count() <= 0) {
                throw new BusinessException(ResultCode.FAIL, "请核对，兑换机器未找到！");
            }
        }
        if (MALL_SCORE_GOODS_TYPE_PAD.equals(goods.getScoreGoodsType())) {
            String stationCodes = goods.getStationCode();
            if (StringUtil.isEmpty(stationCodes)) {
                throw new BusinessException(ResultCode.FAIL, "PAD兑换类型商品须指定兑换站点！");
            }
            List<String> stationCodeList = Arrays.asList(goods.getStationCode().split(COMMA));
            Integer goodsCount = goods.getGoodsCount() * stationCodeList.size();
            if (stock.getInventoryQuantity() - goodsCount < 0) {
                throw new BusinessException(ResultCode.FAIL, "请核对，物料数量不足！");
            }
            stationCodeList.forEach(it -> {
                MallScoreGoods stationGoods = new MallScoreGoods();
                BeanUtils.copyProperties(goods, stationGoods);
                stationGoods.setStationCode(it);
                int goodsInitNum = stationGoods.getGoodsCount();
                stationGoods.setGoodsCount(0);
                stationGoods.setOrgName(orgService.getOrgName(goods.getOrgCode()));
                save(stationGoods);
                if (goodsInitNum > 0) {
                    //商品数量变更
                    changeGoodsNum(stationGoods.getId(), goodsInitNum, "初始化库存");
                }
            });
            //库存变更
            stockService.reduceStock(stock.getId(), Math.abs(goodsCount), "商品初始化，从物料库出库");
            return;
        }
        //先置为0，通过增加商品数量方法为其添加库存
        int goodsInitNum = goods.getGoodsCount();
        goods.setGoodsCount(0);
        goods.setOrgName(orgService.getOrgName(goods.getOrgCode()));
        save(goods);
        if (goodsInitNum > 0) {
            //商品数量变更
            changeGoodsNum(goods.getId(), goodsInitNum, "初始化库存");
            //库存变更
            stockService.reduceStock(stock.getId(), Math.abs(goodsInitNum), "商品初始化，从物料库出库");
        }
    }

    @Override
    public String createQrCode(String appId, long goodsId, Boolean prod) {
        MallScoreGoods goods = getById(goodsId);
        String qrCode = goods.getStationGoodsQrcodeUrl();
        if (StringUtil.isNotEmpty(qrCode)) {
            return qrCode;
        }
        try {
            final WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            File qrCodeFile;
            if (prod) {
                qrCodeFile = wxMaService.getQrcodeService().createWxaCode("/pages_sub1/pages/ipad/points/detail?id=" + goodsId);
            } else {
                qrCodeFile = wxMaService.getQrcodeService().createWxaCode("/pages_sub1/pages/ipad/points/detail?id=" + goodsId, "trial", 430, true, null, false);
            }
            ImageIO.write(ImageIO.read(qrCodeFile), "png", qrCodeFile);
            IOssSaver ossSaver = SpringContextUtil.getBean(LocalOssSaver.class);
            String path = ossSaver.save(FileUtil.getInputStream(qrCodeFile), "二维码.png").getUrl();
            goods.setStationGoodsQrcodeUrl(path);
            updateById(goods);
            return path;
        } catch (WxErrorException | IOException e) {
            log.error("小程序二维码创建失败，请联系管理员", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "生成失败，请联系管理员!");
        }
    }

    @Override
    public IPage<MallScoreGoods> pageForApiStar(MallScoreGoodsParams params) {
        IPage<MallScoreGoods> iPage = baseMapper.pageForApiStar(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public IPage<MallScoreGoods> pageForStar(MallScoreGoodsParams params) {
        IPage<MallScoreGoods> iPage = this.pageForApiStar(params);
        DictTransUtil.trans(iPage.getRecords());
        ZyzVolunteer volunteer = null;
        BackendUserDetail userDetail = Oauth2Util.getUser();
        if (userDetail != null) {
            String userMobile = userDetail.getMobile();
            if (StringUtil.isNotEmpty(userMobile)) {
                volunteer = volunteerService.getByPhone(userMobile);
            }
        }
        if (volunteer == null) {
            return iPage;
        }
        for (MallScoreGoods g : iPage.getRecords()) {
            g.setCanExchange(canExchange(g, volunteer));
        }
        return iPage;
    }

    private boolean canExchange(MallScoreGoods goods, ZyzVolunteer volunteer) {
        //库存不足
        if (goods.getGoodsCount() - 1 < 0) {
            return false;
        }
        //总积分不满足条件
        if (volunteer.getTotalPoint().compareTo(goods.getOfflineScore()) < 0) {
            return false;
        }
        //敬请期待的商品无法兑换
        if (StringUtil.isNotEmpty(goods.getDisplayStatus()) && goods.getDisplayStatus().equals(MallCons.GOODS_DISPLAY_STATUS_PLAN)) {
            return false;
        }
        //星级要求的验证
        if (goods.getServiceLong() != null && !goods.getServiceLong().equals(BigDecimal.ZERO) && goods.getServiceLong().compareTo(volunteer.getServiceLong()) > 0) {
            return false;
        }
        //当年服务时长要求的验证
        return goods.getServiceLongThisYear() == null || goods.getServiceLongThisYear().equals(BigDecimal.ZERO) || goods.getServiceLongThisYear().compareTo(volunteer.getServiceLongThisYear()) <= 0;
    }

    @Override
    public MallScoreGoods getGoodsByThirdPartGoodsCode(String thirdPlatformCode, String thirdPlatformGoodsCode) {
        return lambdaQuery().eq(MallScoreGoods::getThirdPlatformCode, thirdPlatformCode)
                .eq(MallScoreGoods::getThirdPlatformGoodsCode, thirdPlatformGoodsCode).list().stream().findFirst().orElse(null);
    }

}
