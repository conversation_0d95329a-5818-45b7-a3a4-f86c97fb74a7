package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.mall.domain.dto.ThirdPlatformPointChangeDto;
import com.fykj.scaffold.mall.domain.entity.ZyzPointRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointRecordParams;
import fykj.microservice.core.base.IBaseService;

/**
 * 积分商城-积分历史记录
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface IZyzPointRecordService extends IService<ZyzPointRecord>, IBaseService<ZyzPointRecord> {
    /**
     * 分页查询--后端页面
     */
    IPage<ZyzPointRecord> getPage(ZyzPointRecordParams params);

    /**
     * 分页查询--后端页面
     */
    IPage<ZyzPointRecord> pageForApi(ZyzPointRecordParams params);

    /**
     * 三方平台控制加减分
     */
    void thirdPlatformChangePoint(ThirdPlatformPointChangeDto dto);
}

