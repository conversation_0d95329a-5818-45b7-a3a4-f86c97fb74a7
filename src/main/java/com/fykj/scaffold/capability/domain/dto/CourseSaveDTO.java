package com.fykj.scaffold.capability.domain.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.capability.cons.CourseCategory;
import com.fykj.scaffold.capability.cons.CourseCreditRange;
import com.fykj.scaffold.capability.cons.CourseType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel("Course Save DTO")
public class CourseSaveDTO {

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课程分类 字典CODE:" + CourseCategory.DICT_CODE)
    private String courseCategory;

    @ApiModelProperty("课程类型 字典CODE:" + CourseType.DICT_CODE)
    private String courseType;

    @ApiModelProperty("课程日期")
    private LocalDate courseDate;

    @ApiModelProperty("课程开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime courseStartTime;

    @ApiModelProperty("课程结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime courseEndTime;

    @ApiModelProperty("长期有效")
    private Boolean longTermValid;

    @ApiModelProperty("线上课程截止时间 (过期后不可学习)")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN)
    private LocalDateTime onlineCourseDeadline;

    @ApiModelProperty("课程学分 (正常签到范围内可获得学分，线上视频学习需要学习完整可获得学分)")
    private Integer courseCredits;

    @ApiModelProperty("适用学分区间 字典CODE" + CourseCreditRange.DICT_CODE)
    private String applicableCreditRange;

    @ApiModelProperty("报名人数控制")
    private Integer registrationLimit;

    @ApiModelProperty("地址 (地图地址，方便导航通过地图选择)")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

//    @ApiModelProperty("签到距离 默认500")
//    private BigDecimal checkInDistance;

    @ApiModelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("简介")
    private String description;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("顺序")
    private Integer orderNum;

//    @ApiModelProperty("置顶")
//    private Boolean top;

//    @ApiModelProperty("发布组织 (发布人所属组织)")
//    private String publishOrg;

//    @ApiModelProperty("是否对外展示 (默认不对外展示，需要设置对外展示后才可以)")
//    private Boolean outerDisplay;
//
//    @ApiModelProperty("签到二维码 (课程需要生成二维码，使用二维码签到，微信扫码后跳转到该课程详情)")
//    private String checkInQrcode;
}
