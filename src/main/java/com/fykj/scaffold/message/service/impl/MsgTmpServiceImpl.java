package com.fykj.scaffold.message.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.entity.MsgTmp;
import com.fykj.scaffold.message.mapper.MsgTmpMapper;
import com.fykj.scaffold.message.service.IMsgSendService;
import com.fykj.scaffold.message.service.IMsgTmpService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;

import java.util.Collection;
import java.util.List;

/**
 * 消息模版实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Service
public class MsgTmpServiceImpl extends BaseServiceImpl<MsgTmpMapper, MsgTmp> implements IMsgTmpService {

    @Autowired
    private IMsgSendService sendService;

    @Override
    public boolean checkCode(Long id, String code) {
        return !lambdaQuery().eq(MsgTmp::getCode, code).ne(ObjectUtil.isNotEmpty(id), MsgTmp::getId, id).exists();
    }

    @Override
    public List<MsgTmp> listOfEnable() {
        return lambdaQuery().eq(MsgTmp::getStatus, true).list();
    }

    @Override
    public MsgTmp getTmpByCode(String code) {
        MsgTmp msgTmp = lambdaQuery().eq(MsgTmp::getCode, code).one();
        if (msgTmp == null) {
            throw new BusinessException(ResultCode.FAIL, "模版不存在！");
        }
        return msgTmp;
    }

    @Override
    public boolean updateById(MsgTmp entity) {
        if (disableModify(entity.getId())) {
            throw new BusinessException(ResultCode.FAIL, "模版已被使用，无法修改！");
        }
        return super.updateById(entity);
    }

    @Override
    public boolean removeByIds(Collection<?> idList) {
        idList.forEach(it -> {
            if (disableModify((Long)it)) {
                throw new BusinessException(ResultCode.FAIL, idList.size() == 1 ? "该模版已被使用，无法删除！" : "存在模版已被使用，批量删除失败！");
            }
        });
        return super.removeByIds(idList);
    }

    private boolean disableModify(Long id) {
        return sendService.lambdaQuery().eq(MsgSend::getTmpId, id).exists();
    }
}
