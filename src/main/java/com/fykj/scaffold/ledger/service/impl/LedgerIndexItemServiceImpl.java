package com.fykj.scaffold.ledger.service.impl;

import com.fykj.scaffold.ledger.domain.dto.ItemImportDto;
import com.fykj.scaffold.ledger.domain.dto.LedgerItemDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerDepartment;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndex;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndexItem;
import com.fykj.scaffold.ledger.mapper.LedgerIndexItemMapper;
import com.fykj.scaffold.ledger.service.ILedgerDepartmentService;
import com.fykj.scaffold.ledger.service.ILedgerIndexItemService;
import com.fykj.scaffold.ledger.service.ILedgerIndexService;
import com.fykj.scaffold.message.conns.MsgCons;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.entity.MsgTmp;
import com.fykj.scaffold.message.service.IMsgSendService;
import com.fykj.scaffold.message.service.IMsgTmpService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 台账项-服务实现类
 *
 * @date 2025-05-26
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class LedgerIndexItemServiceImpl extends BaseServiceImpl<LedgerIndexItemMapper, LedgerIndexItem> implements ILedgerIndexItemService {

    @Autowired
    private ILedgerIndexService indexService;

    @Autowired
    private ILedgerDepartmentService departmentService;

    @Autowired
    private IMsgTmpService msgTmpService;

    @Autowired
    private IMsgSendService msgSendService;


    @Override
    public List<LedgerIndexItem> getByIndexId(Long indexId) {
        List<LedgerIndexItem> list = baseMapper.getByIndexId(indexId);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Map<Long, String> map = departmentService.getDepartmentMap();
        list.forEach(item -> {
            item.setDepartmentName(getDepartmentName(item.getDepartmentId(), map));
        });
        return list;
    }

    @Override
    public LedgerIndexItem get(Long id) {
        LedgerIndexItem item = super.getById(id);
        LedgerIndex index = indexService.getById(item.getIndexId());
        item.setIndexName(index.getName());
        item.setDepartmentIdList(Arrays.asList(item.getDepartmentId().split(Mark.COMMA)));
        return item;
    }

    private String getDepartmentName(String departmentId, Map<Long, String> departmentMap) {
        List<String> name = new ArrayList<>();
        Arrays.asList(departmentId.split(Mark.COMMA)).forEach(id -> {
            name.add(departmentMap.get(Long.valueOf(id)));
        });
        return String.join("、", name);
    }

    @Override
    public List<String> getIndexIdByDepartmentId(String departmentId) {
        return baseMapper.getIndexIdByDepartmentId(departmentId);
    }

    @Override
    public List<LedgerItemDto> getUserIndexItem(Long indexId) {
        Long userId = (Long) Oauth2Util.getUserId();
        LedgerDepartment department = departmentService.getByUserId(userId);
        if (department == null) {
            return Collections.emptyList();
        }
        return baseMapper.getUserIndexItem(department.getId(), indexId);
    }

    @Override
    public List<LedgerItemDto> getDepartmentReport(Long indexId, Long departmentId) {
        return baseMapper.getUserIndexItem(departmentId, indexId);
    }

    @Override
    public List<ItemImportDto> dataImport(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "上传文件为空");
        }

        List<ItemImportDto> dataList = ExcelUtil.readExcel(
                excel, ItemImportDto.class, 0);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        Map<String, Long> indexMap = indexService.getIndexNameMap();
        Map<String, Long> departmentMap = departmentService.getDepartmentNameMap();
        List<ItemImportDto> errorList = new ArrayList<>();
        List<LedgerIndexItem> itemList = new ArrayList<>();
        List<String> departmentIdList = new ArrayList<>();
        for (ItemImportDto itemImportDto : dataList) {
            String error = "";
            Long indexId = indexMap.getOrDefault(itemImportDto.getIndexName(), null);
            if (indexId == null) {
                error = error + "台账指标不存在;";
            }
            Long departmentId = departmentMap.getOrDefault(itemImportDto.getDepartmentName(), null);
            if (departmentId == null) {
                error = error + "关联局办不存在;";
            }
            if (StringUtil.isEmpty(itemImportDto.getName())) {
                error = error + "台账项名称不能为空;";
            }
            if (StringUtil.isEmpty(itemImportDto.getRelatedRequirements())) {
                error = error + "相关要求不能为空;";
            }
            if (StringUtil.isNotEmpty(error)) {
                itemImportDto.setReason(error);
                errorList.add(itemImportDto);
                continue;
            }
            LedgerIndexItem item = new LedgerIndexItem();
            item.setName(itemImportDto.getName());
            item.setRelatedRequirements(itemImportDto.getRelatedRequirements());
            item.setIndexId(indexId);
            item.setDepartmentId(String.valueOf(departmentId));
            itemList.add(item);
            departmentIdList.add(String.valueOf(departmentId));
        }
        if (!CollectionUtils.isEmpty(itemList)) {
            saveBatch(itemList);
            // 发送通知短信
            sendMsg(departmentIdList);
        }
        return errorList;
    }

    @Override
    public boolean saveItem(LedgerIndexItem entity) {
        boolean result = save(entity);
        if (!result) {
         throw new BusinessException(ResultCode.FAIL, "保存失败");
        }
        handleSendMsg(entity.getDepartmentId());
        return true;
    }

    @Override
    public boolean updateItemById(LedgerIndexItem entity) {
        boolean result = updateById(entity);
        if (!result) {
            throw new BusinessException(ResultCode.FAIL, "更新失败");
        }
        handleSendMsg(entity.getDepartmentId());
        return true;
    }

    private void handleSendMsg(String departmentId) {
        // 发送通知短信
        if (StringUtil.isEmpty(departmentId)) {
            return;
        }
        List<String> departmentIdList = Arrays.asList(departmentId.split(Mark.COMMA));
        sendMsg(departmentIdList);
        return;
    }

    private void sendMsg(List<String> departmentIdList) {
        List<String> mobileList = departmentService.getMobileByDepartmentIdList(departmentIdList);
        if (CollectionUtils.isEmpty(mobileList)) {
            log.info("未配置接收人手机号");
            return;
        }
        MsgTmp tmp = msgTmpService.getTmpByCode(MsgCons.MSG_EVALUATION_LEDGER);
        if (tmp == null || StringUtil.isEmpty(tmp.getTmp())) {
            log.info("未配置短信模板");
            return;
        }
        // 构建短信实体
        MsgSend msg = buildMsgEntity(tmp, String.join(Mark.COMMA, mobileList), tmp.getTmp());
        // 保存短信记录
        msgSendService.save(msg);
        // 发送短信
        msgSendService.sendMessage(msg.getId());
    }

    private MsgSend buildMsgEntity(MsgTmp tmp, String mobiles, String msgContent) {
        // 构建短信实体
        MsgSend entity = new MsgSend();
        entity.setTitle(tmp.getTitle());
        entity.setSelfDefine(false);
        entity.setTmpId(tmp.getId());
        entity.setSendType(MsgCons.MSG_PUSH_TYPE_SMS);
        entity.setSelfDefineMsg(msgContent);
        entity.setMsgContent(msgContent);

        // 封装接收人信息
        List<MsgRecord> receivers = Arrays.stream(mobiles.split(","))
                .map(mobile -> {
                    MsgRecord record = new MsgRecord();
                    record.setReceiverMobile(mobile);
                    return record;
                })
                .collect(Collectors.toList());

        entity.setReceivers(receivers);
        return entity;
    }
}
