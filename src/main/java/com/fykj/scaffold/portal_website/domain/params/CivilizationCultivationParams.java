package com.fykj.scaffold.portal_website.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 文明培育管理查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("文明培育管理查询参数")
public class CivilizationCultivationParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    /**
     * 名称
     */
    private String title;

    /**
     * 栏目id
     */
    private String categoryId;

    /**
     * 年份
     */
    private String year;

    /**
     * 发布日期
     */
    private String publishDate;

    /**
     * 栏目code
     */
    private String categoryCode;

    /**
     * 是否上架
     */
    private Boolean grounding;
}
