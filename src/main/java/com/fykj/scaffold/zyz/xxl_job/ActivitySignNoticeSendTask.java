package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_ACTIVITY_NOTICE_SIGN;


/**
 * 活动签到提醒
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActivitySignNoticeSendTask {

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzActivityApplyService activityApplyService;



    @XxlJob("Activity_Notice_Sign_Send_Tmp")
    public void activitySignNoticeSend() {
        XxlJobHelper.log("AVTIVITY_NOTICE_SIGN_SendTmp-Job, START!");
        LocalDateTime timeStart = LocalDateTime.now();
        LocalDateTime timeEnd = LocalDateTime.now().plusMinutes(10);
        actTimePeriodService.lambdaQuery().gt(ZyzActivityTimePeriod::getStartTime, timeStart).le(ZyzActivityTimePeriod::getStartTime, timeEnd).list().forEach(it -> {
            //获取报名表中的志愿者id
            List<ZyzActivityApply> applies = activityApplyService.lambdaQuery().eq(ZyzActivityApply::getAuditStatus, ZyzCons.ACTIVITY_APPLY_STATUS_AUDIT_SUCCESS).eq(ZyzActivityApply::getTimePeriodId, it.getId()).list();
            if (CollectionUtil.isNotEmpty(applies)) {
                List<Long> volunteerIds = applies.stream().map(ZyzActivityApply::getVolunteerId).collect(Collectors.toList());
                sendTmpMsg.sendToVolunteer(SMT_ACTIVITY_NOTICE_SIGN, volunteerIds, applies.get(0).getActivityName());
            }
        });
        XxlJobHelper.log("ACTIVITY_NOTICE_SIGN_SendTmp-Job, END!");
    }
}
