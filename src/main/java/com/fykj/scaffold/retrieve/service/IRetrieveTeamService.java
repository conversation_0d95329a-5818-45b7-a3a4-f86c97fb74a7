package com.fykj.scaffold.retrieve.service;

import com.fykj.scaffold.retrieve.domain.dto.RetrieveTeamDto;
import com.fykj.scaffold.retrieve.domain.dto.RetrieveTeamResultDto;

/**
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface IRetrieveTeamService{

    /**
     * 找回团队管理员权限信息提交
     * @param dto
     * @return
     */
    RetrieveTeamResultDto retrieveTeam(RetrieveTeamDto dto);

    /**
     * 创建小程序二维码
     * @param appId
     * @return
     */
    String createQrCode(String appId);
}
