package com.fykj.scaffold.sync.controller;

import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDep;
import com.fykj.scaffold.sync.domain.params.ZyzSyncDepParams;
import com.fykj.scaffold.sync.service.IZyzSyncDepService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.Result;

/**
 * 市平台同步职能部门
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-28
 */
@RestController
@RequestMapping("/admin/zyz/sync-dep")
public class ZyzSyncDepController extends BaseController<IZyzSyncDepService, ZyzSyncDep, ZyzSyncDepParams> {

    @AuditLog("保存职能部门")
    @PostMapping("/saveDep")
    @ApiOperation("保存职能部门")
    public Result saveDep(@RequestBody ZyzSyncDep dep) {
        baseService.saveDep(dep);
        return new Result();
    }
}
