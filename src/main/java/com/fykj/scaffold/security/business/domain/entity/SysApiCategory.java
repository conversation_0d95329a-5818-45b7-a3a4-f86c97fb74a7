package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fykj.scaffold.cms.domain.entity.CmsCategory;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 接口目录表
 * 
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_api_category")
public class SysApiCategory extends BaseEntity  {

	private static final long serialVersionUID = 1L;

	/**
	 * 名称
	 */
	@TableField("name")
    @ApiModelProperty(value = "名称")
		private String name;

	/**
	 * 排序
	 */
	@TableField("sequence")
    @ApiModelProperty(value = "排序")
		private Integer sequence;

	/**
	 * 状态（启用，未启用）
	 */
	@TableField("status")
    @ApiModelProperty(value = "状态（启用，未启用）")
		private Boolean status;

	/**
	 * 描述
	 */
	@TableField("remark")
    @ApiModelProperty(value = "描述")
		private String remark;

	/**
	 * 父节点
	 */
	@TableField("parent_id")
    @ApiModelProperty(value = "父节点")
		@JsonSerialize(using = ToStringSerializer.class)
		private Long parentId;

	/**
	 * 级别
	 */
	@TableField("level")
    @ApiModelProperty(value = "级别")
		private Integer level = 1;

	/**
	 * 下级类目列表
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = " 下级类目列表")
	private List<SysApiCategory> subCategories;
}
