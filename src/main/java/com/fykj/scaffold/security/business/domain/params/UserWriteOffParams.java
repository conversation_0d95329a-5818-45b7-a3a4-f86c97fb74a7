package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;

/**
 * 系统用户注销管理-查询参数
 *
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("系统用户注销管理-查询参数")
public class UserWriteOffParams extends BaseParams {

    private static final long serialVersionUID = 7139926812557528444L;

    @ApiModelProperty("关键字查询，模糊匹配")
    private String keyword;

    @ApiModelProperty("是否取消注销")
    private Boolean canceled;

    @ApiModelProperty("是否账号相关信息被删除")
    private Boolean removed;

    @ApiModelProperty("账号相关信息删除失败的数据")
    private Boolean removeFailed;

    @ApiModelProperty("申请注销起")
    private LocalDate applyDateStart;

    @ApiModelProperty("申请注销止")
    private LocalDate applyDateEnd;

    @ApiModelProperty("距离销户的天数")
    private Integer daysDuration;
}
