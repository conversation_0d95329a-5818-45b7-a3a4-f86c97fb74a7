package com.fykj.scaffold.security.business.controller;


import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.security.business.domain.entity.OssConfig;
import com.fykj.scaffold.security.business.service.IFileService;
import com.fykj.scaffold.security.business.service.IOssConfigService;
import com.fykj.scaffold.support.utils.ContentTypeUtil;
import com.fykj.scaffold.support.utils.ImageUtil;
import exception.BusinessException;
import fykj.microservice.core.support.util.SystemUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.HandlerMapping;
import result.ResultCode;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * 文件处理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/vpath")
@Api(tags = "文件处理")
public class DataController {

    @Autowired
    private IOssConfigService ossConfigService;

    @Autowired
    private IFileService fileService;

    @ApiOperation("文件处理")
    @GetMapping("/data/**")
    public void data(
            @ApiParam(name = "图片宽度，非等比例压缩时必填")
            @RequestParam(required = false, defaultValue = "0") int w,
            @ApiParam(name = "图片高度，非等比例压缩时必填")
            @RequestParam(required = false, defaultValue = "0") int h,
            @ApiParam(name = "是否等比例压缩，默认true")
            @RequestParam(required = false, defaultValue = "true") boolean fix,
            @ApiParam(name = "是否下载")
            @RequestParam(required = false) boolean download) {

        String filePath = getFilePath();
        if (w != 0 || h != 0) {
            filePath = fileService.getFilePath(filePath, w, h, fix);
        }
        write(new File(filePath), download);
    }

    private void write(File file, Boolean download) {
        HttpServletResponse response = SystemUtil.getResponse();
        String fileName = file.getName();
        String ext = ImageUtil.getExt(fileName);
        long gbSize = file.length() / 1024 / 1024 / 1024;
        if (gbSize > 1) {
            writeLargeFile(file, download, ext, response);
            return;
        }
        try (FileInputStream in = new FileInputStream(file);
            OutputStream out = response.getOutputStream()) {
            int length = in.available();
            byte[] data = new byte[length];
            response.setContentLength(length);
            response.setContentType((ObjectUtil.isNotEmpty(download) && download) ? "application/octet-stream" : ContentTypeUtil.getContentType(ext));
            while (in.read(data) > 0) {
                out.write(data);
            }
            out.flush();
        } catch (IOException e) {
            throw new BusinessException(ResultCode.FAIL, "读取文件失败", e);
        }
    }

    private void writeLargeFile(File file, Boolean download, String ext, HttpServletResponse response) {
        int chunkFileSize = 500 * 1024 * 1024;
        //块文件数量
        int chunkFileNum = (int) Math.ceil(file.length() * 1.0 / chunkFileSize);
        try (FileInputStream in = new FileInputStream(file); OutputStream out = response.getOutputStream()) {
            response.setContentLengthLong(file.length());
            response.setContentType((ObjectUtil.isNotEmpty(download) && download) ? "application/octet-stream" : ContentTypeUtil.getContentType(ext));
            for(int i = 0; i < chunkFileNum; i ++) {
                long remain = file.length() - (long) chunkFileSize * i;
                int length = remain >= chunkFileSize ? chunkFileSize : (int)remain;
                byte[] bytes = new byte[length];
                boolean isRead = in.read(bytes,0,length)>0;
                if (isRead) {
                    out.write(bytes);
                }
            }
            out.flush();
        }catch (IOException e) {
            throw new BusinessException(ResultCode.FAIL, "读取文件失败", e);
        }
    }

    private String getFilePath() {
        HttpServletRequest request = SystemUtil.getRequest();
        final String path = request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE).toString();
        final String bestMatchingPattern = request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE).toString();
        String arguments = new AntPathMatcher().extractPathWithinPattern(bestMatchingPattern, path);

        OssConfig conf = ossConfigService.getConfig();
        return conf.getStorageLocation() + File.separator + arguments;
    }

}
