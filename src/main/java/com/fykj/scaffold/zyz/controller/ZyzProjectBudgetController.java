package com.fykj.scaffold.zyz.controller;


import com.fykj.scaffold.zyz.domain.entity.ZyzProjectBudget;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectBudgetParams;
import com.fykj.scaffold.zyz.service.IZyzProjectBudgetService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;

/**
 * 项目预算明细
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/admin/project/budget")
@Api(tags = "项目预算明细接口")
public class ZyzProjectBudgetController extends BaseController<IZyzProjectBudgetService, ZyzProjectBudget, ZyzProjectBudgetParams> {

}
