package com.fykj.scaffold.sync.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDepTypeDict;

/**
 * 市平台职能部门类型数据字典
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface IZyzSyncDepTypeDictService extends IBaseService<ZyzSyncDepTypeDict> {
    /**
     * 同步职能部门类型
     */
    void syncDepType();

    ZyzSyncDepTypeDict getByDepTypeId(String typeId);
}

