//package com.fykj.scaffold.task.repository;
//
//import com.fykj.scaffold.task.domain.entity.CopyActivity;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//
//@Repository
//public interface  CopyActivityRepository extends ElasticsearchRepository<CopyActivity, Long> {
//
//    Page<CopyActivity> findByParameterIdAndSyncOrderByCreateDateDesc(Long parameterId,String Sync, Pageable pageable);
//    Page<CopyActivity> findByParameterIdOrderByCreateDateDesc(Long parameterId,Pageable pageable);
//    List<CopyActivity> findAllByParameterIdAndSync(Long parameterId,String sync);
//    List<CopyActivity> findAllByParameterId(Long parameterId);
//}
