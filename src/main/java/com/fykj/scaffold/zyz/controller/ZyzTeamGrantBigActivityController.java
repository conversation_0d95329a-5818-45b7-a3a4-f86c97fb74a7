package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzTeamGrantBigActivity;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamGrantBigActivityParams;
import com.fykj.scaffold.zyz.service.IZyzTeamGrantBigActivityService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import result.JsonResult;
import result.Result;

import java.util.List;


/**
 * 团队授权大型活动发布记录-接口控制器
 *
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/admin/zyz/team/grantBigActivity")
@Api(tags = "团队授权大型活动发布记录-管理接口")
public class ZyzTeamGrantBigActivityController extends BaseController<IZyzTeamGrantBigActivityService, ZyzTeamGrantBigActivity, ZyzTeamGrantBigActivityParams> {

    @ApiOperation(value = "授权团队")
    @GetMapping(value = "/teamGrant")
    public Result teamGrant(@RequestParam Long teamId, @RequestParam(required = false) Long grantId) {
        baseService.teamGrant(teamId, grantId);
        return OK;
    }

    @ApiOperation(value = "校验当前团队是否被授权")
    @GetMapping(value = "/validateGranted")
    public Result validateGranted() {
        baseService.validateGranted();
        return OK;
    }

    @ApiOperation("大型赛事活动-志愿者注册-选择团队下拉")
    @GetMapping("/getGrantedTeam")
    public JsonResult<List<ZyzTeamGrantBigActivity>> getGrantedTeam(@RequestParam(required = false) String teamName) {
        return new JsonResult<>(baseService.getGrantedTeam(teamName));
    }
}
