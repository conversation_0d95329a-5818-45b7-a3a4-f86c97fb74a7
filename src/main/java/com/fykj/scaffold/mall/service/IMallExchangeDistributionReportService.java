package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.ExchangeDRExportDto;
import com.fykj.scaffold.mall.domain.entity.MallExchangeDistributionReport;
import com.fykj.scaffold.mall.domain.params.MallExchangeDistributionReportParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-06-05
 */
public interface IMallExchangeDistributionReportService extends IBaseService<MallExchangeDistributionReport> {
    /**
     * 分页查询--后端页面
     */
    IPage<MallExchangeDistributionReport> getPage(MallExchangeDistributionReportParams params);
    /**
     * 导出
     * @param params
     * @return
     */
    List<ExchangeDRExportDto> listForPage(MallExchangeDistributionReportParams params);
}

