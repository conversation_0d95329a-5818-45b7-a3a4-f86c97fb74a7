package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.XssTag;
import com.fykj.scaffold.security.business.domain.params.XssTagParams;
import com.fykj.scaffold.security.business.mapper.XssTagMapper;
import com.fykj.scaffold.security.business.service.IXssTagService;
import com.fykj.scaffold.support.utils.XssFilterUtil;
import com.fykj.scaffold.support.wrapper.QueryWrapperBuilder;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * XSS标签白名单
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
@Service
public class XssTagServiceImpl extends BaseServiceImpl<XssTagMapper, XssTag> implements IXssTagService {

    @Override
    public boolean removeByIds(Collection<?> idList) {
        super.removeByIds(idList);
        XssFilterUtil.init();
        return true;
    }

    @Override
    public boolean removeById(Serializable id) {
        super.removeById(id);
        XssFilterUtil.init();
        return true;
    }

    @Override
    public IPage<XssTag> findByPage(XssTagParams params) {
        QueryWrapper<XssTag> queryWrapper = QueryWrapperBuilder.build(params);
        return page(params.getPage(), queryWrapper);
    }

    @Override
    public void updateInfo(XssTag info) {
        if (StringUtil.isEmpty(info.getId())) {
            throw new BusinessException(ResultCode.NOT_VALID, "id不能为空!");
        }
        checkBeforeSave(info);
        updateById(info);
        XssFilterUtil.init();
    }

    @Override
    public void saveInfo(XssTag info) {
        checkBeforeSave(info);
        save(info);
        XssFilterUtil.init();
    }

    @Override
    public String[] getTags() {
        List<XssTag> list = lambdaQuery().eq(XssTag::getStatus, true)
                .list();
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.stream()
                .map(XssTag::getTag)
                .toArray(String[]::new);
    }

    @Override
    public List<XssTag> getAttrList() {
        List<XssTag> list = lambdaQuery().eq(XssTag::getStatus, true)
                .isNotNull(XssTag::getAttribute)
                .ne(XssTag::getAttribute, Mark.EMPTY)
                .list();
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;

    }

    @Override
    public List<XssTag> getEnforcedAttrList() {
        List<XssTag> list = lambdaQuery().eq(XssTag::getStatus, true)
                .isNotNull(XssTag::getAttribute)
                .ne(XssTag::getAttribute, Mark.EMPTY)
                .isNotNull(XssTag::getEnforcedValue)
                .ne(XssTag::getEnforcedValue, Mark.EMPTY)
                .list();
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<XssTag> getProtocolList() {
        List<XssTag> list = lambdaQuery()
                .isNotNull(XssTag::getAttribute)
                .ne(XssTag::getAttribute, Mark.EMPTY)
                .eq(XssTag::getStatus, true)
                .isNotNull(XssTag::getProtocol)
                .ne(XssTag::getProtocol, Mark.EMPTY)
                .list();
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }


    private void checkBeforeSave(XssTag info) {
        if (StringUtil.isEmpty(info.getStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "状态不能为空！");
        }
        if (StringUtil.isEmpty(info.getTag())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "标签不能为空！");
        }
        if (StringUtil.isNotEmpty(info.getEnforcedValue()) && StringUtil.isEmpty(info.getAttribute())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "强制执行值需要和属性一起配置！");
        }
        if (StringUtil.isNotEmpty(info.getProtocol()) && StringUtil.isEmpty(info.getAttribute())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "协议需要和属性一起配置！");
        }
        if (StringUtil.isNotEmpty(info.getProtocol()) && StringUtil.isNotEmpty(info.getEnforcedValue())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "协议和强制执行值不能一起配置！");
        }
    }
}
