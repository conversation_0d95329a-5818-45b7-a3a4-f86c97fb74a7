package com.fykj.volunteer.sync.util.domain.sync_schedule_act_plan;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShow;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncScheduleActPlan {

    /**
     * 唯一标识
     * 必填
     */
    private String uniCode;
    /**
     * 活动预告编号
     * 必填
     */
    private String preId;
    /**
     * 活动登记编号
     * 必填
     */
    private String actpId;


    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "scheduleplan")
    public static class SchedulePlan{
        private List<SyncScheduleActPlan> info;
    }
    public String buildXml() {
        SyncScheduleActPlan.SchedulePlan plan = new SyncScheduleActPlan.SchedulePlan();
        plan.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(plan);
    }
}
