package com.fykj.scaffold.sgb_docking.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbDept;

/**
 * 苏州志愿者平台部门单位Mapper接口
 */
public interface SgbDeptMapper extends BaseMapper<SgbDept> {
    
    /**
     * 清空所有数据
     */
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
}