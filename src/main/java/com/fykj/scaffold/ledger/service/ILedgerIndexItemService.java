package com.fykj.scaffold.ledger.service;

import com.fykj.scaffold.ledger.domain.dto.ItemImportDto;
import com.fykj.scaffold.ledger.domain.dto.LedgerItemDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndexItem;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 台账项-服务类
 *
 * @date 2025-05-26
 */
public interface ILedgerIndexItemService extends IBaseService<LedgerIndexItem> {

    /**
     * 获取台账项列表
     *
     * @param indexId
     * @return
     */
    List<LedgerIndexItem> getByIndexId(Long indexId);

    /**
     * 获取指标项
     *
     * @param id
     * @return
     */
    LedgerIndexItem get(Long id);

    /**
     * 获取局办对应的指标id
     *
     * @param departmentId
     * @return
     */
    List<String> getIndexIdByDepartmentId(String departmentId);

    /**
     * 获取指标对象指标项
     *
     * @param indexId
     * @return
     */
    List<LedgerItemDto> getUserIndexItem(Long indexId);

    /**
     * 获取b部门指标对象指标项
     *
     * @param indexId
     * @param departmentId
     * @return
     */
    List<LedgerItemDto> getDepartmentReport(Long indexId, Long departmentId);

    /**
     * 导入
     *
     * @param excel
     * @return
     */
    List<ItemImportDto> dataImport(MultipartFile excel);

    /**
     * 保存指标项
     * @param entity
     * @return
     */
    boolean saveItem(LedgerIndexItem entity);

    /**
     * 更新指标项
     * @param entity
     * @return
     */
    boolean updateItemById(LedgerIndexItem entity);
}

