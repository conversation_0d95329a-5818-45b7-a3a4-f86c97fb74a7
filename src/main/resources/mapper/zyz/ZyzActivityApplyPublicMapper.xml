<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityApplyPublicMapper">

    <resultMap id="activityApplyPublicMap" type="com.fykj.scaffold.zyz.domain.entity.ZyzActivityApplyPublic" autoMapping="true">
        <!-- 只显式映射额外信息的动态字段，其他字段使用自动映射 -->
        <result column="extra_info" property="extraInfo"
                typeHandler="com.fykj.scaffold.support.utils.StrStrLinkedHashMapTypeHandler"/>
    </resultMap>

    <select id="pageOrListForApplyPublic"
            resultMap="activityApplyPublicMap">
        SELECT
        zry.*,
        zry.is_party as party,
        TIMESTAMPDIFF(YEAR,
        SUBSTRING(certificate_id, 7, 8), CURDATE()) -(DATE_FORMAT(CURDATE(), '%m%d') &lt; DATE_FORMAT(SUBSTRING(certificate_id, 7, 8), '%m%d')) AS age,
        CASE WHEN MOD(SUBSTR(certificate_id, 17, 1), 2) = 0 THEN '女' ELSE '男'END AS sexText
        FROM zyz_activity_apply_public AS zry
        left join zyz_activity AS zy on zry.activity_id = zy.id
        left join sys_org sp on sp.code= zy.publish_org_code
        WHERE zry.is_deleted = 0
        <if test="params.activityName != null and params.activityName != ''">
            AND ( zry.activity_name LIKE CONCAT('%', CONCAT(#{params.activityName}, '%'))
            OR zry.name LIKE CONCAT('%',  CONCAT(#{params.activityName}, '%'))
            OR zry.phone LIKE CONCAT('%', CONCAT(#{params.activityName}, '%')))
        </if>
        <if test="params.myOrgCode != null and params.myOrgCode != ''">
            and zy.publish_org_code =#{params.myOrgCode}
            and zy.team_publish = 0
        </if>
        <if test="params.myTeamId != null">
            AND zy.team_id = #{params.myTeamId}
            and zy.team_publish = 1
        </if>
        <if test="params.activityId != null and params.activityId != ''">
            AND  zry.activity_id = #{params.activityId}
        </if>
        <if test="params.timePeriodId != null and params.timePeriodId != ''">
            AND  zry.time_period_id = #{params.timePeriodId}
        </if>
        <if test="params.status != null and params.status != ''">
            AND  zry.status = #{params.status}
        </if>
        <if test="params.myUserId != null">
            AND  zry.creator = #{params.myUserId}
        </if>
        ORDER BY zry.create_date DESC
    </select>

    <select id="getJoinMassesNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT certificate_id)
        FROM zyz_activity_apply_public
        WHERE is_deleted = 0 AND status = 1
        <if test="params.start != null">
            AND create_date &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND create_date &lt;= #{params.end}
        </if>
    </select>

    <select id="pageMyApplyPublic" resultType="com.fykj.scaffold.zyz.domain.dto.ApplyPublicPageDto">
        SELECT
            zap.activity_id AS activityId,
            zap.id AS applyPublicId,
            zap.activity_name AS `name`,
            za.address AS address,
            zap.start_time AS startTime,
            zap.end_time AS endTime,
            CONCAT(
                DATE_FORMAT(zap.start_time, '%Y-%m-%d %H:%i'),
                '-',
                DATE_FORMAT(zap.end_time, '%H:%i')
            ) AS dateTimeRange,
            zap.status AS `status`,
            zap.create_date AS applyTime,
            IF(zap.status = 1 AND zap.start_time > NOW(), 1, 0) AS enableCancel
        FROM zyz_activity_apply_public zap
        LEFT JOIN zyz_activity za ON zap.activity_id = za.id
        WHERE zap.is_deleted = 0
        AND (zap.creator = #{params.myUserId} OR zap.phone = #{params.myMobile})
        <if test="params.activityId != null">
            AND zap.activity_id = #{params.activityId}
        </if>
        <if test="params.status != null">
            AND zap.status = #{params.status}
        </if>
        <if test="params.activityName != null and params.activityName != ''">
            AND ( zap.activity_name LIKE CONCAT('%', CONCAT(#{params.activityName}, '%'))
            OR zap.name LIKE CONCAT('%',  CONCAT(#{params.activityName}, '%'))
            OR zap.phone LIKE CONCAT('%', CONCAT(#{params.activityName}, '%')))
        </if>
        ORDER BY zap.create_date DESC
    </select>
</mapper>
