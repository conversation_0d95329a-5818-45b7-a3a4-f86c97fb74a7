package com.fykj.scaffold.portal_website.controller;


import com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation;
import com.fykj.scaffold.portal_website.domain.params.CivilizationCultivationParams;
import com.fykj.scaffold.portal_website.service.ICivilizationCultivationService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.Result;
import result.ResultCode;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/admin/portal_website/civilization_cultivation")
@Api(tags = "文明培育管理接口")
public class CivilizationCultivationController extends BaseController<ICivilizationCultivationService, CivilizationCultivation, CivilizationCultivationParams> {

    @AuditLog("文明培育上下架操作")
    @ApiOperation("文明培育上下架")
    @GetMapping({"/ground"})
    public Result ground(@RequestParam String id) {
        baseService.ground(id);
        return new Result(ResultCode.OK);
    }
}
