package com.fykj.scaffold.advanced_unit.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 2025/4/1
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "单位测评统计")
public class AuUnitIndexEvaluateCountDTO {

    @ApiModelProperty(value = "ID")
    private String orgId;

    @ApiModelProperty(value = "申报ID")
    private String applyId;

    @ApiModelProperty(value = "标准ID")
    private String standardId;

    @ApiModelProperty(value = "单位logo")
    private String unitLogo;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "统一社会信用代码（18位）")
    private String creditCode;

    @ApiModelProperty(value = "自评总分")
    private Integer selfScore = 0;

    @ApiModelProperty(value = "复评打分")
    private Integer finalScore = 0;

    @ApiModelProperty(value = "自评次数")
    private Integer selfCount = 0;

    @ApiModelProperty(value = "复评次数")
    private Integer finalCount = 0;

    @ApiModelProperty(value = "复评排名")
    private Integer finalRanking = 0;

}
