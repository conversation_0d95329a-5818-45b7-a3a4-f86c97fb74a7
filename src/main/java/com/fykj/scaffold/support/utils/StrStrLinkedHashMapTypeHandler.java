package com.fykj.scaffold.support.utils;

import com.alibaba.fastjson.TypeReference;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.LinkedHashMap;

/**
 * 2025/5/20
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(LinkedHashMap.class)
public class StrStrLinkedHashMapTypeHandler  extends LinkedHashMapTypeHandler<String, String> {

    @Override
    protected TypeReference<LinkedHashMap<String, String>> specificType() {
        return new TypeReference<LinkedHashMap<String, String>>() {};
    }

}
