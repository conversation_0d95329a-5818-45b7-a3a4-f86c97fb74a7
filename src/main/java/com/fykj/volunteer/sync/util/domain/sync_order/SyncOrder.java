package com.fykj.volunteer.sync.util.domain.sync_order;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步实践单
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncOrder {
    /**
     * 必填
     * 实践单内容
     */
   private String content;
    /**
     * 必填
     * 唯一号
     */
    private String  uniCode;
    /**
     * 必填
     * 实践阵地编号
     */
    private String  pbId;
    /**
     * 必填
     * 要求完成日期 格式2016-09-30
     */
    private String  finishDate;
    /**
     * 必填
     * 实践单对接方类型 1:实践阵地（实践中心、所、站）
     * 2:职能部门
     * 3:实践点
     * 4:组织团队
     */
    private String  dockType;
    /**
     * 必填
     * 实践单对接方编号
     */
    private String dockId;
    /**
     * 必填
     * 对接时间 格式：yyyy-mm-dd hh24:mi:ss，例如2016-09-30 13:07:56
     */
    private String  dockTime;
    /**
     * 必填
     * 完成时间 格式：yyyy-mm-dd hh24:mi:ss，例如2016-09-30 13:07:56
     */
    private String finishTime;
    /**
     * 非必填
     * 完成备注
     */
    private String  finishRemark;
    /**
     * 非必填
     * 绑定需求id N	一个需求只能被一个单绑定；多个则以英文逗号分隔，最多允许绑定10个资源；例如：101,102,103,104
     */
    private String reqId;
    /**
     * 非必填
     * 绑定活动id 一个活动计划可以被多个单绑定；多个则以英文逗号分隔，最多允许绑定10个活动；例如：101,102,103,104
     */
    private String actpId;
    /**
     * 非必填
     * 绑定资源id N	一个资源可以被多个单绑定；多个则以英文逗号分隔，最多允许绑定10个资源；例如：101,102,103,104
     */
    private String resId;

    public static class dockType {
        /**
         * 实践阵地（实践中心、所、站）
         */
        public final static String BASE = "1";

        /**
         * 职能部门
         */
        public final static String DEP = "2";
        /**
         * 实践点
         */
        public final static String SPOT = "3";
        /**
         * 组织机构或团队
         */
        public final static String ORG_OR_TEAM = "4";
    }
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "order")
    public static class Order {
        private List<SyncOrder> info;
    }
    public String buildXml() {
        SyncOrder.Order syncOrder = new SyncOrder.Order();
        syncOrder.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(syncOrder);
    }
}
