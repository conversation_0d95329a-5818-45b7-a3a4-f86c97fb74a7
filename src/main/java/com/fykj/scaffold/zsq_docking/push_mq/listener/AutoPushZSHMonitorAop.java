package com.fykj.scaffold.zsq_docking.push_mq.listener;

import fykj.microservice.core.support.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 自动推送知社区监控切面
 */
@Slf4j
@Aspect
@Component
public class AutoPushZSHMonitorAop {

    @Around(value = "execution(* org.apache.rocketmq.spring.core.RocketMQListener.onMessage(..))")
    public Object afterUpdateById(ProceedingJoinPoint joinPoint) throws Throwable {
        boolean isAutoSync = Boolean.parseBoolean(SystemUtil.getProperty("auto-push-zsh"));
        boolean isNeedMonitor = Arrays.stream(joinPoint.getSignature().getDeclaringType().getGenericInterfaces()).anyMatch(it -> it.getTypeName().equals(AutoPushZSHMonitor.class.getName()));
        try {
            if (isAutoSync || !isNeedMonitor) {
                return joinPoint.proceed();
            } else {
                log.info("系统配置关闭了自动同步功能，所以忽略此mq消息");
                return null;
            }
        } catch (Throwable e) {
            log.error("自动同步切面出错", e);
            throw e;
        }
    }
}
