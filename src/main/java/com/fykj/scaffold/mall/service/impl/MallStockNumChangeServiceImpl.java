package com.fykj.scaffold.mall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsNumChange;
import com.fykj.scaffold.mall.domain.entity.MallStockNumChange;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsNumChangeParams;
import com.fykj.scaffold.mall.domain.params.MallStockNumChangeParams;
import com.fykj.scaffold.mall.mapper.MallStockNumChangeMapper;
import com.fykj.scaffold.mall.service.IMallStockNumChangeService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 积分商城-库存变动
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallStockNumChangeServiceImpl extends BaseServiceImpl<MallStockNumChangeMapper, MallStockNumChange> implements IMallStockNumChangeService {


    @Override
    public IPage<MallStockNumChange> getPage(MallStockNumChangeParams params) {
        if (params == null) {
            params = new MallStockNumChangeParams();
        }
        IPage<MallStockNumChange> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }
}