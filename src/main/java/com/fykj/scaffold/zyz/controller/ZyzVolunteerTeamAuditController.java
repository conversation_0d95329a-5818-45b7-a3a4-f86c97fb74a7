package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamAuditDetailDto;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamAuditDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeamAudit;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerTeamAuditParams;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamAuditService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.util.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

/**
 * 志愿者与团队关系审核表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-14
 */
@RestController
@RequestMapping("/admin/zyz/volunteer/team/audit")
@Api(tags = "志愿者与团队关系审核接口")
public class ZyzVolunteerTeamAuditController extends BaseController<IZyzVolunteerTeamAuditService, ZyzVolunteerTeamAudit, ZyzVolunteerTeamAuditParams> {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ISysOrgService orgService;

    @ApiOperation("获取团队--分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<VolunteerTeamAuditDto>> getPages(@RequestBody ZyzVolunteerTeamAuditParams params) {
        IPage<VolunteerTeamAuditDto> iPage = baseService.getPages(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @ApiOperation("获取加入团队的志愿者的详情")
    @GetMapping("/getDetail")
    public JsonResult<VolunteerTeamAuditDetailDto> getDetail(Long id) {
        VolunteerTeamAuditDetailDto returnDto = new VolunteerTeamAuditDetailDto();
        ZyzVolunteerTeamAudit auditDto = baseService.getById(id);
        ZyzVolunteer volunteer = volunteerService.getVolunteerById(auditDto.getVolunteerId());
        BeanUtil.copyProperties(volunteer, returnDto);
        if (volunteer.getCertificateType().equals(ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD)) {
            returnDto.setAge(IdcardUtil.getAgeByIdCard(volunteer.getCertificateId()));
        }
        if (StringUtil.isNotEmpty(volunteer.getOrgCode())) {
            returnDto.setOrgName(orgService.getOrgName(volunteer.getOrgCode()));
        }
        DictTransUtil.trans(returnDto);
        return new JsonResult<>(returnDto);
    }

    @AuditLog("团队志愿者加入批量审核")
    @ApiOperation("批量审核")
    @GetMapping("/volunteerAudit")
    public Result volunteerAudit(@RequestParam String ids, @RequestParam(required = false) String remake, @RequestParam String auditStatus) {
        if (!baseService.volunteerAudit(ids, remake, auditStatus)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }


}
