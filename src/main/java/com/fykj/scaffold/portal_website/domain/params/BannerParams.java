package com.fykj.scaffold.portal_website.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 轮播图查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("新闻资讯查询参数")
public class BannerParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    /**
     * 标题
     */
    private String title;

    /**
     * 栏目id
     */
    private String categoryId;


    /**
     * 是否上架
     */
    private Boolean grounding;

    /**
     * 是否上架
     */
    private Boolean enabled;
}
