package com.fykj.volunteer.sync.util.domain.requirement;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <req>
 *   <info>
 *     <code>SUCCESS</code>
 *     <msg>同步成功</msg>
 *     <uniCode>sb999888</uniCode>
 *     <reqId>68ef454b459b4a3b93b0a2c793e9b9e5</reqId>
 *   </info>
 * </req>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncRequirementResult {
    /**
     * 需求编号
     */
    private String reqId;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 消息
     */
    private String msg;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "req")
    public static class Req {
        private List<SyncRequirementResult> info;
    }
}
