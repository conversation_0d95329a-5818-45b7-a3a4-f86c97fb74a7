package com.fykj.scaffold.mall.domain.dto;

import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import lombok.*;

import java.math.BigDecimal;

@Data
@Builder
public class StarGoodsExchangeDto {

    private MallScoreGoods goods;
    private ZyzVolunteer volunteer;
    private String channel;
    private String exchangeType;
    private String bizType;
    private String orderNo;
    private BigDecimal needScore;
    private String remarks;
}
