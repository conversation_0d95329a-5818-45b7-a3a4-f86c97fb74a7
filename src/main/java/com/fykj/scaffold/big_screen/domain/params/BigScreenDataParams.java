package com.fykj.scaffold.big_screen.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 大屏数据创造查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("大屏数据创造管理查询参数")
public class BigScreenDataParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    /**
     * 数据维度
     */
    @MatchType(value = QueryType.EQ, fieldName = "dimension")
    private String dimension;

    /**
     * 状态
     */
    @MatchType(value = QueryType.EQ, fieldName = "status")
    private Boolean status;
}
