package com.fykj.scaffold.security.business.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.security.business.domain.dto.EditPasswordDto;
import com.fykj.scaffold.security.business.domain.dto.PrivilegeChangeDto;
import com.fykj.scaffold.security.business.domain.dto.ResetPasswordBySmsCodeDto;
import com.fykj.scaffold.security.business.domain.dto.SaCaAccountPrivilegesDto;
import com.fykj.scaffold.security.business.domain.entity.Role;
import com.fykj.scaffold.security.business.domain.entity.SysUserExpand;
import com.fykj.scaffold.security.business.domain.entity.SysUserRole;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.domain.params.UserParams;
import com.fykj.scaffold.security.business.service.*;
import com.fykj.scaffold.security.login.util.CsrLoginUtils;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.dto.CommunityAdminImportDto;
import com.fykj.scaffold.zyz.domain.dto.TeamAdminImportDto;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 用户前端前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-16
 */
@RestController
@RequestMapping("/admin/user")
@Slf4j
public class UserController extends BaseController<IUserService, User, UserParams> {

    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private ISysUserExpandService expandService;

    @Autowired
    private ISendTmpMsg sendTmpMsgService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private IDictService dictService;

    @Autowired
    private CsrLoginUtils csrLoginUtils;

    @GetMapping("/loginUser")
    public JsonResult<BackendUserDetail> loginUser() {
        return new JsonResult<>(Oauth2Util.getUser());
    }

    @GetMapping("/checkToken")
    public Result checkToken() {
        return OK;
    }

    @ApiOperation("token换取一次性获取用户信息凭证")
    @GetMapping("/tokenToUserInfoCode")
    public Result tokenToUserInfoCode() {
        long userId = (Long) Oauth2Util.getUserId();
        String code = RandomUtil.randomString(8);
        redisService.set("USER-INFO-CODE" + code, userId, 120);
        return new JsonResult<>(code);
    }

    @ApiOperation(value = "修改密码")
    @PostMapping(value = "/editPassWord")
    public Result editPassWord(@RequestBody EditPasswordDto dto) {
        return baseService.editPassWord(dto.getUsername(), dto.getOldPassWord(), dto.getNewPassWord());
    }

    @ApiOperation(value = "强制修改密码，不需要旧密码")
    @PostMapping(value = "/forceUpdatePwd")
    public Result forceUpdatePwd(@RequestParam String username, @RequestParam String newPassWord) {
        return baseService.forceUpdatePwd(username, newPassWord);
    }

    @ApiOperation(value = "获取用户的权限")
    @GetMapping(value = "/userMenusAndActionAuthorities")
    public Result userMenusAndActionAuthorities(String type) {
        return new JsonResult<>(baseService.userMenusAndActionAuthorities(type));
    }

    @ApiOperation(value = "身份列表")
    @GetMapping(value = "/capacityList")
    public Result capacityList() {
        List<SysUserExpand> expandList = expandService.getByUserId((Long) Oauth2Util.getUserId());
        return new JsonResult<>(expandList);
    }

    @ApiOperation(value = "刷新身份")
    @GetMapping(value = "/refreshUserCapacity")
    public Result refreshUserCapacity(Long expandId) {
        if (ObjectUtil.isEmpty(expandId)) {
            return OK;
        }
        expandService.updateDefault((Long) Oauth2Util.getUserId(), expandId);
        //单刷这个客户端
        baseService.refreshUserContext(Oauth2Util.getUser().getUsername(), Oauth2Util.getRequestClientId());
        return OK;
    }

    @ApiOperation(value = "更新openid")
    @GetMapping(value = "/updateOpenId")
    public Result updateOpenId(String openId) {
        long userId = (Long) Oauth2Util.getUserId();
        baseService.updateOpenId(userId, openId);
        return OK;
    }

    @Override
    public Result get(Long id) {
        User u = baseService.getById(id);
        List<Long> roleIds = userRoleService.
                lambdaQuery()
                .eq(SysUserRole::getUserId, id)
                .list()
                .stream()
                .map(SysUserRole::getRoleId)
                .collect(Collectors.toList());
        //角色ID转角色CODE，先这样吧
        if (CollectionUtil.isNotEmpty(roleIds)) {
            List<String> roleCodes = roleService
                    .lambdaQuery()
                    .in(BaseEntity::getId, roleIds)
                    .list()
                    .stream()
                    .map(Role::getCode)
                    .collect(Collectors.toList());
            u.setRoleCodes(roleCodes);
        }
        u.setExpandList(expandService.getByUserId(id));
        return new JsonResult<>(u);
    }

    @ApiOperation(value = "重置密码")
    @PostMapping(value = "/resetPassWordOfSelect")
    public Result resetPassWord(@RequestParam Long userId) {
        return new JsonResult<>(baseService.resetPassWord(userId));
    }

    @ApiOperation("验证码重置密码")
    @PostMapping({"/resetPasswordBySmsCode"})
    public Result changePhone(@RequestBody ResetPasswordBySmsCodeDto param) {
        String newPassword = param.getNewPassword(), smsCode = param.getSmsCode();
        String mobile = Oauth2Util.getMobile();
        String realCode = redisService.get(mobile + Cons.SmsValidCodeTemplate.RESET_PASSWORD.name());
        if (StringUtil.isEmpty(newPassword)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请输入新的密码");
        }
        if (StringUtil.isEmpty(realCode) || !realCode.equals(smsCode)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "验证码错误，无法更换");
        }
        baseService.lambdaUpdate()
                .eq(BaseEntity::getId, Oauth2Util.getUserId())
                .set(User::getPassword, passwordEncoder.encode(newPassword))
                .update();
        return new Result();
    }

    @AuditLog("导入旧系统社区管理员账号")
    @ApiOperation("导入旧系统社区管理员账号")
    @PostMapping(value = "/communityAdminImport")
    public void communityAdminImport(@RequestParam("excel") MultipartFile excel) {
        List<CommunityAdminImportDto> importResult = baseService.communityAdminImport(excel);
        if (CollectionUtil.isNotEmpty(importResult)) {
            ExcelUtil.writeExcel(importResult, "社区管理员账号导入结果", CommunityAdminImportDto.class);
        }
    }

    @AuditLog("导入旧系统团队管理员账号")
    @ApiOperation("导入旧系统团队管理员账号")
    @PostMapping(value = "/teamAdminImport")
    public void teamAdminImport(@RequestParam("excel") MultipartFile excel) {
        List<TeamAdminImportDto> importResult = baseService.teamAdminImport(excel);
        if (CollectionUtil.isNotEmpty(importResult)) {
            ExcelUtil.writeExcel(importResult, "团队管理员账号导入结果", TeamAdminImportDto.class);
        }
    }

    @ApiOperation("获取用户是否强制修改密码")
    @GetMapping(value = "/getIsForceChangePassword")
    public Result getIsForceChangePassword(String userName) {
        return new JsonResult<>(baseService.lambdaQuery().eq(User::getUsername, userName).one().getForceUpdatePwd());
    }

    @ApiOperation("获取用户是否超过90天未修改密码")
    @GetMapping(value = "/getOver90DaysNotChangePassword")
    public Result getOver90DaysNotChangePassword(String userName) {
        User user = baseService.lambdaQuery().eq(User::getUsername, userName).one();
        LocalDate lastUpdatePwdDate = user.getLastUpdatePwdDate();
        LocalDate today = LocalDate.now();
        Boolean forceUpdatePwd = user.getForceUpdatePwd();
        return new JsonResult<>(!forceUpdatePwd && lastUpdatePwdDate != null && !today.isBefore(lastUpdatePwdDate.plusDays(90)));
    }

    @ApiOperation("分协会管理员账号权限分页查询")
    @PostMapping({"/pageForSubAssociationAdmin"})
    public JsonResult<IPage<User>> pageForSubAssociationAdmin(@RequestBody(required = false) UserParams params) {
        IPage<User> result = baseService.pageForSubAssociationAdmin(params);
        return new JsonResult(result);
    }

    @ApiOperation("分协会管理员账号权限导出")
    @PostMapping({"/exportForSubAssociationAdmin"})
    public void exportForSubAssociationAdmin(@RequestBody(required = false) UserParams params) {
        List<SaCaAccountPrivilegesDto> result = baseService.getListForSubAssociationAdmin(params);
        if (CollectionUtil.isEmpty(result)) {
            throw new BusinessException(ResultCode.FAIL, "暂无数据可导出！");
        }
        ExcelUtil.writeExcel(result, "账号权限表", SaCaAccountPrivilegesDto.class);
    }

    @ApiOperation("用户搜索")
    @GetMapping(value = "/searchUser")
    public JsonResult<List<User>> searchUser(@RequestParam String keyword) {
        return new JsonResult<>(baseService.lambdaQuery().and(item ->
                item.like(User::getUsername, keyword)
                        .or()
                        .like(User::getMobile, keyword)
                        .or()
                        .like(User::getName, keyword)).list());
    }

    @ApiOperation("获取用户的所及站权限")
    @GetMapping(value = "/getUserSACPrivileges")
    public JsonResult<List<String>> getUserSACPrivileges(@RequestParam Long id) {
        return new JsonResult<>(baseService.getUserSACPrivileges(id));
    }

    @ApiOperation("账号权限变更（分协会下管理账号）")
    @PostMapping({"/privilegesChange"})
    public Result privilegesChange(@RequestBody PrivilegeChangeDto data) {
        baseService.privilegesChange(data);
        return OK;
    }

    @ApiOperation("判断是否需要弹窗展示平台规则")
    @GetMapping(value = "/noticeFlag")
    public JsonResult<Boolean> noticeFlag(@RequestParam Long userId) {
        Boolean needPop = false;
        if (dictService.getByCode("management_rules").getStatus()) {
            Long value = redisService.get("noticeFlag" + userId);
            if (value != null && value != 0L) {
                needPop =false;
            } else {
                redisService.set("noticeFlag" + userId, userId, 60 * 60 * 24 * 7);
                needPop = true;
            }
        }
        return new JsonResult<>(needPop);
    }

    @ApiOperation("通过当前用户获取csr平台token")
    @GetMapping(value = "/getCurrentUserCsrToken")
    public Result getCurrentUserCsrToken() {
        String mobile = Oauth2Util.getMobile();
        if (StringUtil.isEmpty(mobile)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到用户手机号");
        }
        JSONObject oneCodeInfo = csrLoginUtils.httpRequestForOneCode(mobile);
        JSONObject objInfo = oneCodeInfo.getJSONObject("obj");
        if (objInfo == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "企业责任联盟登录信息获取异常！");
        }
        Boolean needRegister = objInfo.getBoolean("needRegister");
        String oneCode = objInfo.getString("oneCode");
        String username = objInfo.getString("username");
        if (needRegister != null && needRegister) {
            throw new BusinessException(ResultCode.NOT_FOUND, "您在企业责任联盟平台尚未注册企业，请先注册！");
        }
        if (needRegister != null && StringUtil.isNotEmpty(oneCode) && StringUtil.isNotEmpty(username)) {
            return csrLoginUtils.login(username, oneCode);
        }
        throw new BusinessException(ResultCode.BAD_REQUEST, "企业责任联盟登录信息获取异常！");
    }
}
