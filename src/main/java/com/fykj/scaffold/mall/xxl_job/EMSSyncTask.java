package com.fykj.scaffold.mall.xxl_job;

import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.ems.EmsExpressUtil;
import com.fykj.scaffold.support.ems.in.OrderSubmitDto;
import com.fykj.scaffold.support.ems.out.OrderSubmitOutDto;
import com.fykj.scaffold.zyz.service.IZyzCalenderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import utils.LocalDateTimeUtil;

import java.time.LocalDate;
import java.util.List;

/**
 * 邮政订单发送
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class EMSSyncTask {
    @Autowired
    private IMallExchangeService mallExchangeService;

    @Autowired
    private IZyzCalenderService calenderService;

    @Autowired
    private IDictService dictService;

    @XxlJob("EMS_Sync_Handler")
    public void emsSyncJobHandler() {
        XxlJobHelper.log("EMS_Sync_Handler-Job, START!");
        // 获取待同步的活动数据以及活动结束时间三天内活动同步失败的数据
        LocalDate workDate = calenderService.findNextWorkDay();
        LocalDate tomorrow = LocalDate.now();
        if (!tomorrow.isEqual(workDate)) {
            log.info("今日无需邮政发货");
            return;
        }
        List<MallExchange> exchangeList = mallExchangeService.lambdaQuery()
                .lt(MallExchange::getExchangeTime, LocalDate.now())
                .isNotNull(MallExchange::getCounty).isNotNull(MallExchange::getRecipient)
                .eq(MallExchange::getEmsStatus, MallCons.MALL_EXCHANGE_EMS_STATUS_DELIVERY)
                .isNull(MallExchange::getEmsCode)
                .list();
        Dict dict = dictService.getByCode(MallCons.SYSTEM_CONFIG_EMS_TIME);
        exchangeList.forEach(it -> {
            OrderSubmitDto orderSubmitDto = new OrderSubmitDto();
            orderSubmitDto.setLogisticsOrderNo(it.getOrderNum());
            String exceptStartTime = LocalDateTimeUtil.formatDate(tomorrow) + " " + dict.getValue();
            orderSubmitDto.setExceptStartTime(exceptStartTime);
            orderSubmitDto.setExceptEndTime(LocalDateTimeUtil.formatDateTime(LocalDateTimeUtil.parseStringToDateTime
                    (exceptStartTime, "yyyy-MM-dd HH:mm:ss").plusHours(1)));
            OrderSubmitDto.Address receiver = new OrderSubmitDto.Address();
            receiver.setProv(it.getProv());
            receiver.setCity(it.getCity());
            receiver.setCounty(it.getCounty());
            receiver.setAddress(it.getAddress());
            receiver.setName(it.getRecipient());
            receiver.setMobile(it.getRecipientPhone());
            Dict address = dictService.getByCode(it.getOrgCode());
            OrderSubmitDto.Address sender = JSONObject.parseObject(address.getValue(), OrderSubmitDto.Address.class);
            OrderSubmitDto.Delivery delivery = new OrderSubmitDto.Delivery();
            delivery.setProv(sender.getProv());
            delivery.setCity(sender.getCity());
            delivery.setCounty(sender.getCounty());
            delivery.setAddress(sender.getAddress());
            orderSubmitDto.setReceiver(receiver);
            orderSubmitDto.setSender(sender);
            orderSubmitDto.setDelivery(delivery);
            try {
                OrderSubmitOutDto result = EmsExpressUtil.orderSubmit(orderSubmitDto);
                it.setEmsCode(result.getWaybillNo());
                it.setEmsStatus(MallCons.MALL_EXCHANGE_STATUS_SENT);
                it.setEmsRemark(null);
            } catch (Exception e) {
                log.error("发货失败，订单号:{}", it.getOrderNum(), e);
                it.setEmsStatus(MallCons.MALL_EXCHANGE_EMS_STATUS_ESS_FAILURE);
                it.setEmsRemark(e.getMessage());
            }
            mallExchangeService.updateById(it);
        });
        XxlJobHelper.log("EMS_Sync_Handler-Job, END!");
    }
}
