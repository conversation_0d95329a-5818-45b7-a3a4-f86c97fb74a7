package com.fykj.scaffold.security.business.service;


import com.fykj.scaffold.security.business.domain.entity.SysDescription;
import fykj.microservice.core.base.IBaseService;

/**
 * 系统规则配置
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-14
 */
public interface ISysDescriptionService extends IBaseService<SysDescription> {
    /**
     * 保存或修改系统描述
     *
     * @param description
     */
    void saveOrUpdateDescription(SysDescription description);
}

