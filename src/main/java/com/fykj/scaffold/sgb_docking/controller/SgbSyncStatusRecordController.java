package com.fykj.scaffold.sgb_docking.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncStatusRecord;
import com.fykj.scaffold.sgb_docking.domain.params.SgbSyncStatusRecordParams;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncStatusRecordService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 数据同步状态记录-接口控制器
 *
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/admin/sgb/sync-status-record")
@Api(tags = "数据同步状态记录-管理接口")
public class SgbSyncStatusRecordController extends BaseController<ISgbSyncStatusRecordService, SgbSyncStatusRecord, SgbSyncStatusRecordParams> {

    @ApiOperation("分页查询同步状态记录（包含业务数据名称）")
    @PostMapping("/getPages")
    public JsonResult<IPage<SgbSyncStatusRecord>> getPages(@RequestBody(required = false) SgbSyncStatusRecordParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new SgbSyncStatusRecordParams();
        }
        IPage<SgbSyncStatusRecord> result = baseService.getPageWithBizName(params);
        // 字典翻译
        DictTransUtil.trans(result.getRecords());
        return new JsonResult<>(result);
    }

}
