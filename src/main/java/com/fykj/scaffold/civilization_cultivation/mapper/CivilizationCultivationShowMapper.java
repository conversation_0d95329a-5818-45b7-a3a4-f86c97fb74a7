package com.fykj.scaffold.civilization_cultivation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationShowParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文明培育展示-管理
 * <p>
 * Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface CivilizationCultivationShowMapper extends BaseMapper<CivilizationCultivationShow> {

    /**
     * 获取文明培育各类型的可筛查年份
     * @param type
     * @return
     */
    List<Integer> getYears(@Param("type") String type);

    /**
     * 文明培育分页展示
     * @param params
     * @return
     */
    IPage<CivilizationCultivationShow> pageShow(IPage<CivilizationCultivationShow> page, @Param("params")CivilizationCultivationShowParams params);

    /**
     * 文明培育分页展示(正式环境)
     * @return
     */
    IPage<CivilizationCultivationShow> pageShowProd(IPage<CivilizationCultivationShow> page);

    /**
     * 首页轮播展示
     * @param params
     * @return
     */
    List<CivilizationCultivationShow> homeBannerShow(@Param("params")CivilizationCultivationShowParams params);

    /**
     * 首页轮播展示(prod环境)
     * @return
     */
    List<CivilizationCultivationShow> homeBannerShowProd();

    /**
     * 获取指定类型下文明培育展示最大年份
     * @param type 类型
     * @return
     */
    Integer getMaxYear(@Param("type") String type);

    /**
     *
     * 文明培育展示驾驶舱数据看板
     * @param type 类型
     * @param year 年份
     * @return
     */
    List<CivilizationCultivationDashboardDto> cockpitData(@Param("type") String type, @Param("year") Integer year);

    /**
     *
     * 文明培育展示驾驶舱数据看板(prod环境)
     * @return
     */
    List<CivilizationCultivationDashboardDto> cockpitDataProd();
}
