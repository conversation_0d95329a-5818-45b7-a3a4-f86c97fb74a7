package com.fykj.scaffold.jjh_app;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;
import utils.StringUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class JJHAppUtil {

    private JJHAppUtil() {
    }

    //code换token地址
    private static final String GET_ACCESS_TOKEN_URL = "https://jjhapp.sipac.gov.cn/uias/auth/token";
    //token换用户信息
    private static final String GET_USER_INFO_URL = "https://jjhapp.sipac.gov.cn/uias/open/info/getUserInfo";
    //appid
    private static final String APP_ID = "1001417";
    //secret
    private static final String APP_SECRET = "0891f6f8-1e78-410b-aae7-b0b3e599c3cc";
    //重定向地址 fixme暂时先配置死吧
    private static final String REDIRECT_URI = "https://zyz.sipac.gov.cn/volunteer_h5/index.html#/pages_h5/pages/home/<USER>";


//    //code换token地址
//    private static final String GET_ACCESS_TOKEN_URL = "https://swan.systoon.com/uias/auth/token";
//
//    //token换用户信息
//    private static final String GET_USER_INFO_URL = "https://swan.systoon.com/uias/open/info/getUserInfo";
//    //appid
//    private static final String APP_ID = "1001781";
//    //secret
//    private static final String APP_SECRET = "4b7bf67c-b405-40e9-b339-f0f383a8cee1";
//
//    //重定向地址 fixme暂时先配置死吧
//    private static final String REDIRECT_URI = "https://sungent.fyxmt.com/volunteer_h5/index.html#/pages_h5/pages/home/<USER>";

    public static String getAccessToken(String code) {
        Map<String, Object> formMap = new HashMap<>();
        formMap.put("clientId", APP_ID);
        formMap.put("appSecret", APP_SECRET);
        formMap.put("grantType", "authorizationCode");
        formMap.put("code", code);
        formMap.put("redirectUri", REDIRECT_URI);
        String preSign = StringUtil.join(formMap
                .entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey()).map(entry -> entry.getKey() + "=\"" + entry.getValue() + "\"")
                .collect(Collectors.toList()), "&");
        String sign = MD5.create().digestHex(preSign).toUpperCase();
        formMap.remove("appSecret");
        formMap.put("sign", sign);
        HttpResponse response = HttpUtil
                .createPost(GET_ACCESS_TOKEN_URL)
                .method(Method.POST)
                .form(formMap)
                .execute();
        if (response.getStatus() != HttpStatus.HTTP_OK || !JSONValidator.from(response.body()).validate()) {
            log.error("获取token失败" + response.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "获取token失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if (!"0".equals(jsonObject.getJSONObject("meta").getString("code"))) {
            log.error("获取token失败" + response.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "获取token失败");
        }
        return jsonObject.getJSONObject("data").getString("accessToken");
    }


    public static AppUserDto getUserInfo(String accessToken) {
        HttpResponse response = HttpUtil
                .createPost(GET_USER_INFO_URL + "?accessToken=" + accessToken)
                .method(Method.GET)
                .execute();
        if (response.getStatus() != HttpStatus.HTTP_OK || !JSONValidator.from(response.body()).validate()) {
            log.error("获取用户信息失败" + response.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "获取用户信息失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if (!"0".equals(jsonObject.getJSONObject("meta").getString("code"))) {
            log.error("获取用户信息失败" + response.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "获取用户信息失败");
        }
        JSONObject data = jsonObject.getJSONObject("data");
        log.info("用户信息为：{}", data);
        return JSONObject.toJavaObject(data, AppUserDto.class);
    }
}
