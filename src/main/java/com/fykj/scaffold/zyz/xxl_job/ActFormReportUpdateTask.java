package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 更新活动报表
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActFormReportUpdateTask {

    @Autowired
    private IZyzActivityService actService;

    @XxlJob("Act_Form_Report_Update_Handler")
    public void updateActFormReportJobHandler() {
        XxlJobHelper.log("Act_Form_Report_Update_Handler-Job, START!");
        try {
            actService.updateActFormReport();
        } catch (Exception e) {
            log.error("更新活动报表数据任务失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Act_Form_Report_Update_Handler-Job, END!");
    }
}
