package com.fykj.scaffold.security.business.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.security.business.domain.dto.RoleResourceDto;
import com.fykj.scaffold.security.business.domain.entity.Resource;
import com.fykj.scaffold.security.business.domain.entity.Role;
import com.fykj.scaffold.security.business.domain.params.RoleParams;
import com.fykj.scaffold.security.business.service.IResourceService;
import com.fykj.scaffold.security.business.service.IRoleService;
import com.fykj.scaffold.security.oauth2.filter.Oauth2FilterInvocationSecurityMetadataSource;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-16
 */
@RestController
@RequestMapping("/admin/role")
public class RoleController extends BaseController<IRoleService, Role, BaseParams> {

    @Autowired
    private IResourceService resourceService;

    @Autowired
    Oauth2FilterInvocationSecurityMetadataSource oauth2FilterInvocationSecurityMetadataSource;

    @PostMapping("/saveRole")
    @ApiOperation("保存角色资源")
    public Result save(@RequestBody RoleResourceDto dto) {
        if (baseService.checkCode(dto.getId(), dto.getCode())) {
            return new Result(ResultCode.FAIL.code(), "角色编码已存在");
        }
        handleNodeMissAndSurplus(dto);
        boolean result = baseService.save(dto);
        if (result) {
            oauth2FilterInvocationSecurityMetadataSource.init();
            return new Result();
        }
        return new Result(ResultCode.FAIL);
    }

    //前端控件有点问题，如果直接选子节点，父节点会丢失。如果把子节点全去了，但是父节点还在,最好前端把控件替换了！！！！！
    private void handleNodeMissAndSurplus(RoleResourceDto dto) {
        List<Long> saveMenus = dto.getMenuIdList().stream().map(Long::parseLong).collect(Collectors.toList());
        Map<Long, Resource> allMenus = resourceService.list().stream().collect(Collectors.toMap(Resource::getId, a -> a, (k1, k2) -> k1));
        Map<Long, List<Resource>> childrenMenusMap = resourceService.list().stream()
                //处理parentId为null时map报错的问题
                .peek(it -> {
                    if (it.getParentId() == null) {
                        it.setParentId(Long.MAX_VALUE);
                    }
                }).collect(Collectors.groupingBy(Resource::getParentId));
        // 移除“孤立的父节点”
        saveMenus.removeIf(menuId -> {
            List<Resource> childrenNode = childrenMenusMap.get(menuId);
            //没有子节点，那就是正常的
            if (CollectionUtil.isEmpty(childrenNode)) {
                return false;
            }
            //如果所有子节点都没有勾选，那这个节点就应该被删除
            return childrenNode.stream().noneMatch(it -> saveMenus.contains(it.getId()));
        });
        //缺失的父节点id
        Set<Long> missParentId = new HashSet<>();
        // 补上“被遗忘”的父节点
        saveMenus.forEach(m -> {
            // 获取当前节点
            Resource node = allMenus.get(m);
            if (node == null) {
                return;
            }
            boolean hasParent = node.getParentId() != null;
            while (hasParent) {
                //一路检查是否有父节点了
                Resource parent = allMenus.get(node.getParentId());
                if (parent == null) {
                    break;
                }
                // 如果查到了父节点，并且在前端传过来的数据里没有这个父节点，就补一下
                if (!saveMenus.contains(parent.getId())) {
                    missParentId.add(parent.getId());
                }
                hasParent = parent.getParentId() != null;
            }
        });
        saveMenus.addAll(missParentId);
        dto.setMenuIdList(saveMenus.stream().map(String::valueOf).collect(Collectors.toList()));
    }

    @ApiOperation(value = "根据id 获取角色信息及菜单")
    @GetMapping(value = "/findOneById")
    public JsonResult<RoleResourceDto> findOneById(@RequestParam Long id) {
        return new JsonResult<>(baseService.findOneById(id));
    }

    @ApiOperation("分页查询")
    @PostMapping(value = "/list")
    public Result page(@RequestBody RoleParams params) {
        return new JsonResult<>(baseService.page(params));
    }

    @ApiOperation("角色树")
    @GetMapping(value = "/roleTree")
    public Result roleTree() {
        return new JsonResult<>(baseService.roleTree());
    }
}
