package com.fykj.scaffold.sync.service;

/**
 * 城市数据同步配置服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface ICityDataSyncConfigService {

    /**
     * 检查平台同步功能开关
     * 检查WMB同步功能是否开启，如果关闭则抛出异常
     * 
     * @throws exception.BusinessException 当同步功能关闭时抛出异常
     */
    void checkPlatformSyncSwitch();

    /**
     * 获取SGB同步补偿天数
     * 从字典配置中获取SGB同步补偿天数，如果配置不存在则返回默认值3天
     * 
     * @return SGB同步补偿天数，默认3天
     */
    int getSgbSyncCompensateDays();

    /**
     * 获取WMB同步补偿天数
     * 从字典配置中获取WMB同步补偿天数，如果配置不存在则返回默认值3天
     * 
     * @return WMB同步补偿天数，默认3天
     */
    int getWmbSyncCompensateDays();
}
