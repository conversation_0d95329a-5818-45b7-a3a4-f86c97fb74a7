package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleDetailParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleParams;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleDetailService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 阵地计划详情
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@RestController
@RequestMapping("/admin/zyz/activity/schedule/detail")
@Api(tags = " 阵地计划详情接口")
public class ZyzActivityScheduleDetailController extends BaseController<IZyzActivityScheduleDetailService, ZyzActivityScheduleDetail, ZyzActivityScheduleDetailParams> {

    @ApiOperation("阵地计划导出")
    @PostMapping({"/exportScheduleDetail"})
    public void exportScheduleDetail(@RequestBody(required = false) ZyzActivityScheduleParams params) {
        List<ZyzActivityScheduleDetailExportDto> result = baseService.listForScheduleDetailExport(params);
        DictTransUtil.trans(result);
        ExcelUtil.writeExcel(result, "计划详情列表", ZyzActivityScheduleDetailExportDto.class);
    }

    /**
     * xcel模版下载
     */
    @ApiOperation("excel模版下载")
    @GetMapping("/excelTemplate")
    public void excelTemplate(HttpServletResponse response) {
        downloadFile(response, "/data/excelTemplate/schedule_detail_template.xlsx");
    }

    /**
     * 批量导入发送对象
     */
    @ApiOperation("批量导入发送对象")
    @PostMapping("/detailImport")
    public Result detailImport(@RequestParam(name = "excel") MultipartFile excel, @RequestParam(required = false, name = "scheduleId") Long scheduleId) {
        if (excel == null || excel.isEmpty()) {
            return new Result(ResultCode.BAD_REQUEST.code(), "请选择要上传的文件");
        }
        return baseService.excelImport(excel, scheduleId);
    }
}
