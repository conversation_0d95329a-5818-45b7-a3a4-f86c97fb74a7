package com.fykj.scaffold.ledger.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ItemImportDto {

    @ExcelProperty(value = "台账项名称")
    private String name;

    @ExcelProperty(value = "台账指标")
    private String indexName;

    @ExcelProperty(value = "关联局办")
    private String departmentName;

    @ExcelProperty(value = "相关要求")
    private String relatedRequirements;

    @ExcelProperty(value = "导入失败原因")
    private String reason;
}
