package com.fykj.scaffold.mall.api;


import com.fykj.scaffold.mall.domain.entity.MallEmsInfo;
import com.fykj.scaffold.mall.service.IMallEmsInfoService;
import com.fykj.scaffold.support.syslog.AuditLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;
import result.ResultCode;
import static fykj.microservice.core.base.AbstractController.OK;

@RestController
@RequestMapping("/api/sys/country/area")
@Api(tags = "积分商城- 年度区级优秀志愿者地址收集表--无需登录")
public class MallEmsInfoApi {

    @Autowired
    private IMallEmsInfoService mallEmsInfoService;

    @AuditLog("填报收件人地址")
    @PostMapping("/submit")
    @ApiOperation("填报收件人地址")
    public Result submit(@RequestBody MallEmsInfo entity) {
        boolean result = this.mallEmsInfoService.submit(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }
}
