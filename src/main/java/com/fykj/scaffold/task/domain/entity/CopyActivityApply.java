package com.fykj.scaffold.task.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.utils.Desensitise;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Document(indexName = "copy_activity_apply")
public class CopyActivityApply {

    private static final long serialVersionUID = 1L;

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityApplyId;
    /**
     * 活动id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;
    /**
     * 任务id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "任务id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parameterId;
    /**
     * 同步成功后市区返回招募的Id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步成功后市区返回招募的Id")
    private String actSyncId;
    /**
     * 身份证号
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "身份证号")
    private String certificateId;

    /**
     * 活动名称
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    /**
     * 活动结束时间
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String actEndTime;
    /**
     * 组织架构
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "组织架构")
    private String orgCode;

    /**
     * 时间段id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "时间段id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long timePeriodId;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_second")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;
    /**
     * 志愿者id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "志愿者id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;

    /**
     * 报名时间
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String applyTime;

    /**
     * 时间段开始时间
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "时间段开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String timePeriodStartTime;

    /**
     * 时间段结束时间
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "时间段结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String timePeriodEndTime;

    /**
     * 审核状态（数据字典）
     */
    @Field(type = FieldType.Text)
/*
        @DictTrans(transTo = "auditStatusText")
*/
    @ApiModelProperty(value = "审核状态（数据字典）")
    private String auditStatus;
    /*
     *//**
     * 审核状态Text
     *//*
        @TableField(exist = false)
        @ApiModelProperty(value = "审核状态Text")
        private String auditStatusText;*/

    /**
     * 审核状态（数据字典）
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "审核备注")
    private String auditRemark;

    /**
     * 签到时间
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String signTime;

    /**
     * 志愿者用户名称
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "志愿者用户名称")
    private String volunteerName;

    /**
     * 志愿者联系方式
     */
    @Field(type = FieldType.Text)
    @Desensitise
    @ApiModelProperty(value = "志愿者联系方式")
    private String volunteerPhone;

    /**
     * 经度
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "维度")
    private BigDecimal latitude;

    /**
     * 距离中心多少m
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "距离中心多少m")
    private Integer signDistance;

    /**
     * 时长是否已兑换成积分
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "时长是否已兑换成积分")
    private Boolean exchange;

    /**
     * 服务时长同步状态
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "服务时长同步状态")
//        @DictTrans(transTo = "serviceLongIsSyncText")
    private String serviceLongIsSync;

//        /**
//         * 服务时长同步状态Text
//         */
//        @TableField(exist = false)
//        @ApiModelProperty(value = "同步状态Text")
//        private String serviceLongIsSyncText;

    /**
     * 服务时长同步时间
     */
    @Field(type = FieldType.Text)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "服务时长同步时间")
    private String serviceLongSyncTime;

    /**
     * 服务时长同步备注
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "服务时长同步备注")
    private String serviceLongSyncRemark;

    /**
     * 服务时长同步成功后市区返回的Id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "服务时长同步成功后市区返回的Id")
    private String serviceLongSyncId;

    /**
     * 报名同步状态
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名同步状态")
//    @DictTrans(transTo = "applyIsSyncText")
    private String applyIsSync;

//    /**
//     * 报名同步状态Text
//     */
//    @Field(type = FieldType.Text)
//    @ApiModelProperty(value = "报名同步状态Text")
//    private String applyIsSyncText;

    /**
     * 报名同步时间
     */
    @Field(type = FieldType.Text)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报名同步时间")
    private String applySyncTime;

    /**
     * 报名同步备注
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名同步备注")
    private String applySyncRemark;

    /**
     * 报名同步id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名同步id")
    private String applySyncId;

    /**
     * 是否补录
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否补录")
    private Boolean additionalRecord;

//    /**
//     * 评价时间
//     */
//    @TableField("appraise_time")
//    @ApiModelProperty(value = "评价时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime appraiseTime;
//
//    /**
//     * 评价内容
//     */
//    @TableField("appraise_content")
//    @ApiModelProperty(value = "评价内容")
//    private String appraiseContent;
//
//    /**
//     * 评价分数
//     */
//    @TableField("appraise_fraction")
//    @ApiModelProperty(value = "评价分数-活动内容")
//    private BigDecimal appraiseFraction;
//
//    /**
//     * 评价分数
//     */
//    @TableField("appraise_fraction_org")
//    @ApiModelProperty(value = "评价分数-发布组织")
//    private BigDecimal appraiseFractionOrg;
//
//
//    /**
//     * 评价分数
//     */
//    @TableField("appraise_fraction_join")
//    @ApiModelProperty(value = "评价分数-活动参与")
//    private BigDecimal appraiseFractionJoin;
//
//    /**
//     * 评价照片(多个用逗号拼接)
//     */
//    @TableField("appraise_pics")
//    @ApiModelProperty(value = "评价照片(多个用逗号拼接)")
//    private String appraisePics;
//
//    /**
//     * 是否前端展示评价内容
//     */
//    @TableField("appraise_status")
//    @ApiModelProperty(value = "是否前端展示评价内容")
//    private Boolean appraiseStatus;

    /**
     * 服务时长
     */
    @Field(type = FieldType.Double)
    @ApiModelProperty(value = "服务时长")
    private BigDecimal serviceLong;

    /**
     * 报名途径
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名途径")
    private String applyWay;

    /**
     * 是否本团队成员
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否本团队成员")
    private Boolean teamMember;

    /**
     * 发布团队id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "发布团队id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    /**
     * 是否团队发布
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否团队发布")
    private Boolean teamPublish;

    /**
     * 是否用户删除
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否用户删除")
    private Boolean userHide;

    /*        *//**
     * 是否可以同步
     *//*
        @TableField(exist = false)
        @ApiModelProperty(value = "是否可以同步")
        private Boolean enableSync;*/

}
