package com.fykj.scaffold.capability.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel("报名dto")
public class CreditsChangeDTO {

    @ApiModelProperty("课程ID")
    @NotNull(message = "课程ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long courseId;

    @ApiModelProperty("用户ID")
    @NotNull(message = "用户ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty("新学分")
    @NotNull(message = "新学分不能为空")
    private Integer credits;
}
