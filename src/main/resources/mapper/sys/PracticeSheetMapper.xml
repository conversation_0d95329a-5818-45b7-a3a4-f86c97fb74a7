<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.sync.mapper.PracticeSheetMapper">

    <select id="statisticsPage" resultType="com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto">
        SELECT
            DATE_FORMAT( sync_time, '%Y-%m' ) AS year_mon,
            count( 0 ) AS add_num,
            sum(IF( is_sync = 'sync_success', 1, 0 )) as success_num,
            sum(IF( is_sync = 'sync_failure', 1, 0 )) as fail_num
        FROM
            practice_sheet
        WHERE
            is_deleted = 0
        <if test="params.start != null and params.start != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &gt;= #{params.start}
        </if>
        <if test="params.end != null and params.end != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &lt;= #{params.end}
        </if>
        GROUP BY
            DATE_FORMAT( sync_time, '%Y-%m' )
        ORDER BY year_mon DESC
    </select>

    <select id="exportStatistics" resultType="com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto">
        SELECT
        sync_id as sync_bill_id,
        content as syncObjectName,
        IF( is_sync = 'sync_success', '成功', '失败' ) as result_name,
        sync_time,
        sync_remark as message
        FROM
            practice_sheet
        WHERE
        is_deleted = 0
        AND is_sync = 'sync_failure'
        <if test="params.yearMon != null and params.yearMon != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) = #{params.yearMon}
        </if>
        <if test="params.start != null and params.start != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &gt;= #{params.start}
        </if>
        <if test="params.end != null and params.end != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &lt;= #{params.end}
        </if>
        <if test="params.message != null and params.message != ''">
            AND sync_remark = #{params.message}
        </if>
        ORDER BY sync_time DESC
    </select>

    <select id="statisticsFailList" resultType="com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto">
        SELECT
        sync_remark as message,
        count( 0 ) AS fail_num
        FROM
        practice_sheet
        WHERE
        is_deleted = 0
        AND is_sync = 'sync_failure'
        <if test="params.start != null and params.start != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &gt;= #{params.start}
        </if>
        <if test="params.end != null and params.end != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &lt;= #{params.end}
        </if>
        GROUP BY
        sync_remark
        ORDER BY
        fail_num DESC
        LIMIT 10
    </select>
    <select id="getByYearMon" resultType="com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto">
        SELECT
            'sync_practice_sheet' AS biz_type,
            count( 0 ) AS add_num,
            sum(IF( is_sync = 'sync_success', 1, 0 )) as success_num,
            sum(IF( is_sync = 'sync_failure', 1, 0 )) as fail_num
        FROM
            practice_sheet
        WHERE
            is_deleted = 0
          AND DATE_FORMAT( sync_time, '%Y-%m' ) = #{yearMon}
    </select>
</mapper>
