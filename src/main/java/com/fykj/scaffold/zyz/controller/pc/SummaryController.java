package com.fykj.scaffold.zyz.controller.pc;

import com.fykj.scaffold.zyz.domain.dto.HomePageOrgSummaryDto;
import com.fykj.scaffold.zyz.domain.dto.HomePagePracticeRankDto;
import com.fykj.scaffold.zyz.domain.dto.HomePageVolunteerServiceDto;
import com.fykj.scaffold.zyz.service.IPcSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/pc/summary")
@Api(tags = "pc端统计接口-无需登录")
public class SummaryController {

    @Autowired
    private IPcSummaryService summaryService;

    @ApiOperation("首页实践排行榜统计")
    @GetMapping({"/home_page/practice_rank"})
    public JsonResult<List<HomePagePracticeRankDto>> hpPracticeRank(@RequestParam String dimension, @RequestParam String orgType) {
        return new JsonResult<>(summaryService.hpPracticeRank(dimension, orgType));
    }

    @ApiOperation("首页志愿服务展示统计")
    @GetMapping({"/home_page/volunteer_service"})
    public JsonResult<HomePageVolunteerServiceDto> hpVolunteerService(@RequestParam String type) {
        return new JsonResult<>(summaryService.hpVolunteerService(type));
    }

    @ApiOperation("首页组织按类型统计")
    @GetMapping({"/home_page/org_summary"})
    public JsonResult<HomePageOrgSummaryDto> orgSummary() {
        return new JsonResult<>(summaryService.orgSummary());
    }
}
