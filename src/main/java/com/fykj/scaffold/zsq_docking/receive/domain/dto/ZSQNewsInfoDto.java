package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.conns.Cons;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@ApiModel("接收志愿者新闻数据模型")
@NoArgsConstructor
@AllArgsConstructor
public class ZSQNewsInfoDto implements Serializable {

    private static final long serialVersionUID = -6078990784338066703L;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("志愿者主键")
    private Long id;

    @ApiModelProperty(value = "所属组织")
    private String orgCode;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "新闻类型,字典volunteer_news_type")
    private String newsType;

    @ApiModelProperty(value = "是否外链")
    private Boolean outsideLink;

    @ApiModelProperty(value = "外部链接")
    private String externalLink;

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "简介")
    private String brief;

    @ApiModelProperty(value = "详细描述")
    private String contentDesc;

    @ApiModelProperty(value = "志愿者-图片地址")
    private String picture;

    @ApiModelProperty(value = "审核状态，字典news_review_status")
    private String reviewStatus;

    @ApiModelProperty(value = "审核原因")
    private String reviewReason;

    @ApiModelProperty(value = "是否上架")
    private Boolean onShelf;

    @ApiModelProperty(value = "对应政务通的用户名")
    private String userId;
}
