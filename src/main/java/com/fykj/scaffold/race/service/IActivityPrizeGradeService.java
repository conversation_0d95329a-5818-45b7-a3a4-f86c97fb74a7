package com.fykj.scaffold.race.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.race.domain.entity.ActivityPrizeGrade;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 摄影大赛-活动奖项等级表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
public interface IActivityPrizeGradeService extends IService<ActivityPrizeGrade>, IBaseService<ActivityPrizeGrade> {

    /**
     * 根据活动id获取奖项
     * @param activityId
     * @return
     */
    List<ActivityPrizeGrade> getPrize(Long activityId);

    /**
     * 根据活动id获取奖项名称
     * @param activityId
     * @return
     */
    List<String> getPrizeName(Long activityId);
}

