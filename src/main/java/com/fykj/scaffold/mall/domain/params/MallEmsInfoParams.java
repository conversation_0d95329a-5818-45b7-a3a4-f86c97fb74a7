package com.fykj.scaffold.mall.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 积分商城- 年度区级优秀志愿者地址收集表
 * 查询参数
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "积分商城- 年度区级优秀志愿者地址收集表查询参数")
public class MallEmsInfoParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"volunteer_name", "volunteer_phone", "volunteer_certificate_id", "recipient_name", "recipient_phone", "recipient_certificate_id", "address", "address_detail", "remark"})
    private String keyWord;
}
