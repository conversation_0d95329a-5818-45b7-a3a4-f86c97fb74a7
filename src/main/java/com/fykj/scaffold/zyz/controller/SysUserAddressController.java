package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.SysUserAddress;
import com.fykj.scaffold.zyz.domain.params.SysUserAddressParams;
import com.fykj.scaffold.zyz.service.ISysUserAddressService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-03-29
 */
@RestController
@RequestMapping("/admin/sys/user/address")
public class SysUserAddressController extends BaseController<ISysUserAddressService, SysUserAddress, SysUserAddressParams> {

}
