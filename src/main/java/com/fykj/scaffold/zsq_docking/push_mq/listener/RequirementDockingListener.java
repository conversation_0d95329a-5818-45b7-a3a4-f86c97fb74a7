package com.fykj.scaffold.zsq_docking.push_mq.listener;


import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingMqFail;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingRequirementRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingMqFailService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingRequirementRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.event.RequirementDockingEvent;
import com.fykj.scaffold.zsq_docking.push_mq.service.IZSQDataPushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.DOCKING_RECORD_STATUS_FAIL;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.TOPIC_REQUIREMENT_DOCKING;

@Slf4j
@Service
@RocketMQMessageListener(consumerGroup = "${spring.application.name}" + TOPIC_REQUIREMENT_DOCKING, topic = TOPIC_REQUIREMENT_DOCKING)
public class RequirementDockingListener implements RocketMQListener<RequirementDockingEvent>, AutoPushZSHMonitor {

    @Autowired
    private IZSQDockingRequirementRecordService requirementDockingRecordService;

    @Autowired
    private IZSQDataPushService pushService;

    @Autowired
    private IZSQDockingMqFailService pushFailService;

    @Override
    public void onMessage(RequirementDockingEvent notice) {
        ZSQDockingMqFail fail = new ZSQDockingMqFail();
        Long dockingRecordId = notice.getDockingRecordId();
        fail.setDockingRecordId(dockingRecordId);
        fail.setMessageId(notice.getMsgId());
        fail.setContent(JSONUtil.toJsonStr(notice));
        fail.setMsgTime(notice.getMsgCreateTime());
        Long requirementId = notice.getRequirementId();
        if (requirementId == null) {
            fail.setExceptionTrace("需求 id 未获取到！");
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingRequirementRecord record = requirementDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【需求 id 未获取到】！");
                requirementDockingRecordService.updateById(record);
            }
            return;
        }
        try {
            pushService.requirementDocking(notice);
        } catch (Exception e) {
            fail.setExceptionTrace(ExceptionUtil.stacktraceToString(e));
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingRequirementRecord record = requirementDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【" + ExceptionUtil.stacktraceToString(e) + "】！");
                requirementDockingRecordService.updateById(record);
            }
        }
    }
}
