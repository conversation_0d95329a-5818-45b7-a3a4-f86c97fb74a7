package com.fykj.volunteer.sync.util.domain.sync_team;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步团队的结果
 * <org>
 *   <info>
 *     <code>SUCCESS</code>
 *     <msg>同步成功</msg>
 *     <orgName>开发测试团队</orgName>
 *     <orgId>7C8B1679CEC0444285EBF6DE06D11AEF</orgId>
 *     <uniqueCode>abababababa123</uniqueCode>
 *   </info>
 * </org>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncTeamResult {
    /**
     * 身份证
     */
    private String orgName;
    /**
     * 成功失败标志
     */
    private String orgId;
    /**
     * 第三方平台唯一键
     */
    private String uniqueCode;
    /**
     * 消息
     */
    private String msg;
    /**
     * 唯一编号
     */
    private String code;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "org")
    public static class Org {
        private List<SyncTeamResult> info;
    }
}
