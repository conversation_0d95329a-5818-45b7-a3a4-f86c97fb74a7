//package com.fykj.scaffold.task.repository;
//
//import com.fykj.scaffold.task.domain.entity.CopyVolunteer;
//import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//
//@Repository
//public interface CopyVolunteerRepository extends ElasticsearchRepository<CopyVolunteer, Long> {
//    /**
//     * 查找任务导入的所有志愿者信息
//     * @param parameterId
//     * @return
//     */
//    List<CopyVolunteer> findAllByParameterId(Long parameterId);
//}
