package com.fykj.scaffold.race.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.race.domain.dto.JsonPictureDto;
import com.fykj.scaffold.race.domain.dto.PictureDto;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 摄影大赛-获奖作品json
 *
 * <AUTHOR> @email ${email}
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "race_prize_photo_json", autoResultMap = true)
public class RacePrizePhotoJson extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 图片列表
     */
    @TableField(value = "picture_list", typeHandler = JsonPictureDto.JsonPictureTypeHandler.class)
    @ApiModelProperty(value = "图片列表")
    private List<JsonPictureDto> pictureList;


    @TableField(exist = false)
    private List<String> idList;

    @TableField(exist = false)
    private String ids;

    /**
     * 奖项id
     */
    @TableField("grade_id")
    @ApiModelProperty(value = "奖项id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long gradeId;


}
