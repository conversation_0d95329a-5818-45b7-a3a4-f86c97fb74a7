package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation;
import com.fykj.scaffold.mall.domain.params.MallPadExchangeStationParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * <AUTHOR>
 * @date 2023-11-27
 */
public interface MallPadExchangeStationMapper extends BaseMapper<MallPadExchangeStation> {

    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    @DataFilter
    IPage<MallPadExchangeStation> getPage(IPage<MallPadExchangeStation> page, @Param("params") MallPadExchangeStationParams params);

    /**
     * 分页查询--小程序页面
     * @return
     */
    IPage<MallPadExchangeStation> pageForApi(IPage<MallPadExchangeStation> page, @Param("params") MallPadExchangeStationParams params);

    /**
     * @return
     */
    @DataFilter
    List<MallPadExchangeStation> getStationsByCurrOrg();
}

