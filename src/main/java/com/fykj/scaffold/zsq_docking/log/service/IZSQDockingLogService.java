package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingLog;
import com.fykj.scaffold.zsq_docking.push_mq.event.*;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ActivityDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.NewsDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQActivityInfoDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import fykj.microservice.core.base.IBaseService;
import result.Result;

/**
 * 知社区对接推送数据信息表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-13
 */
public interface IZSQDockingLogService extends IBaseService<ZSQDockingLog> {

    /**
     * 接收知社区操作日志存储
     * @param dockingInfo
     * @param result
     * @param news
     */
    void saveNewsReceiveLog(NewsDockingDto dockingInfo, Result result, ZSQNewsInfoDto news, Long dockingRecordId);

    /**
     * 通知知社区操作日志存储
     * @param notice
     * @param result
     * @param news
     * @param dockingRecordId
     */
    void saveNewsPushLog(NewsDockingEvent notice, Object result, Object news, Long dockingRecordId);

    /**
     * 接收知社区操作日志存储
     * @param dockingInfo
     * @param result
     * @param activity
     */
    void saveActivityReceiveLog(ActivityDockingDto dockingInfo, Result result, ZSQActivityInfoDto activity, Long dockingRecordId);
    /**
     * 通知知社区操作日志存储
     * @param notice
     * @param result
     * @param activity
     * @param dockingRecordId
     */
    void saveActivityPushLog(ActivityDockingEvent notice, Object result, Object activity, Long dockingRecordId);
    /**
     * 通知知社区操作日志存储
     * @param notice
     * @param result
     * @param requirement
     * @param dockingRecordId
     */
    void saveRequirementPushLog(RequirementDockingEvent notice, Object result, Object requirement, Long dockingRecordId);
    /**
     * 通知知社区操作日志存储
     * @param notice
     * @param result
     * @param resource
     * @param dockingRecordId
     */
    void saveResourcePushLog(ResourceDockingEvent notice, Object result, Object resource, Long dockingRecordId);
    /**
     * 通知知社区操作日志存储
     * @param notice
     * @param result
     * @param belongField
     * @param dockingRecordId
     */
    void saveBelongFieldPushLog(BelongFieldDockingEvent notice, Object result, Object belongField, Long dockingRecordId);

    /**
     * 通知知社区操作日志存储
     * @param notice
     * @param result
     * @param regionAll
     * @param dockingRecordId
     */
    void saveRegionAllPushLog(RegionAllDockingEvent notice, Object result, Object regionAll, Long dockingRecordId);

    /**
     * 通知知社区操作日志存储
     * @param notice
     * @param result
     * @param sysOrg
     * @param dockingRecordId
     */
    void saveSysOrgPushLog(SysOrgDockingEvent notice, Object result, Object sysOrg, Long dockingRecordId);
}

