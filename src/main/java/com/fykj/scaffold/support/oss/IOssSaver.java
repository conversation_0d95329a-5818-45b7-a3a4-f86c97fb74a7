package com.fykj.scaffold.support.oss;

import exception.BusinessException;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;

/**
 * 对象存储模板定义接口
 *
 * <AUTHOR>
 */
public interface IOssSaver {

    /**
     * 将文件存到OSS上
     *
     * @param is       输入流
     * @param fileName 文件名
     * @return {@link FilePathDto}
     */
    FilePathDto save(InputStream is, String fileName);

    /**
     * 将文件存到OSS上
     *
     * @param file 上传文件
     * @return {@link FilePathDto}
     */
    default FilePathDto save(MultipartFile file) {
        try {
            return save(file.getInputStream(), file.getOriginalFilename());
        } catch (IOException e) {
            throw new BusinessException(ResultCode.FAIL, "读取文件失败", e);
        }
    }

     /**
     * 获取虚拟路径，并检查创建中间的文件夹
     *
     * @param storageLocation 根存储路径
     * @param ext             后缀
     * @return 虚拟路径
     */
     default String getVirtualPath(String storageLocation, String ext) {
        //获取文件分类所在文件夹，分类文件夹里每天再分一个文件夹,如果没有则先创建
        String virtualPath = ext + File.separator + LocalDate.now().toString();
//         String virtualPath = "fang_yi" + File.separator + "fang_yi";
        String folderDir = storageLocation + File.separator + virtualPath;
        File dir = new File(folderDir);
        if (dir.exists() || dir.mkdirs()) {
            return virtualPath;
        }
        throw new BusinessException(ResultCode.FAIL, "创建文件夹失败：" + virtualPath);
    }
}
