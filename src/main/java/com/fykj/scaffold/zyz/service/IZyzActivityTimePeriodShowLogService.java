package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShowLog;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 活动公示操作记录表
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-19
 */
public interface IZyzActivityTimePeriodShowLogService extends IBaseService<ZyzActivityTimePeriodShowLog> {

    /**
     * 保存活动公示审核记录
     *
     * @param activityTimePeriodShowId 活动公示id
     * @param activityTimePeriodId     小活动id
     * @param activityId               大活动id
     * @param operateType              操作类型
     * @param remark                   备注
     * @return 是否成功
     */
    boolean saveLog(Long activityTimePeriodShowId, Long activityTimePeriodId, Long activityId, String operateType, String remark);

    /**
     * 获取活动公示操作历史记录
     *
     * @param showId 活动公示id
     * @return 数据列表
     */
    List<ZyzActivityTimePeriodShowLog> getLogsByShowId(Long showId);

}

