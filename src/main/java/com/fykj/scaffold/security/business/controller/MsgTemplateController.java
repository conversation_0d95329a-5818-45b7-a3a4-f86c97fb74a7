package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.MsgTemplate;
import com.fykj.scaffold.security.business.domain.params.MsgTemplateParams;
import com.fykj.scaffold.security.business.service.IMsgTemplateService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 消息模版
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-04-19
 */
@RestController
@RequestMapping("/admin/msg_template")
public class MsgTemplateController extends BaseController<IMsgTemplateService, MsgTemplate, MsgTemplateParams> {

    @ApiOperation(value = "判断code是否重复")
    @GetMapping(value = "/validateCode")
    public JsonResult<Boolean> validateCode(@RequestParam String code, @RequestParam(required = false) Long id) {
        return new JsonResult<>(baseService.validateCode(code, id));
    }

    @ApiOperation("系统消息模版上下架")
    @GetMapping({"/ground"})
    public Result ground(@RequestParam Long id) {
        baseService.ground(id);
        return new Result(ResultCode.OK);
    }
}
