package com.fykj.scaffold.security.login.cons;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 2025/5/15
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "zwt")
public class ZwtConfig {

    private String corpId;

    private String corpSecret;

    private String agentId;

    private String accessTokenGetUrl;

    private String userIdGetUrl;

    private String userInfoGetUrl;

    private String zwtOauth2AuthorizeUrl;

    private String oauth2AuthorizeRedirectUri;

    private String validateSecretKey;

}
