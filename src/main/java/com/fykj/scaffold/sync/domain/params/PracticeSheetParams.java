package com.fykj.scaffold.sync.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 实践单
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class PracticeSheetParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"content","act_link_res_name","act_link_req_name"})
    private String key;

    @MatchType(value = QueryType.EQ, fieldName = "type")
    private String type;

    @MatchType(value = QueryType.EQ, fieldName = "is_sync")
    private String sync;
}
