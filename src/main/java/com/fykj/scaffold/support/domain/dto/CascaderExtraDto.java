package com.fykj.scaffold.support.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("级联选择模型")
public class CascaderExtraDto implements Serializable {

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Serializable value;

    @ApiModelProperty(value = "名称")
    private String label;

    @ApiModelProperty(value = "是否禁用")
    private boolean disabled = false;

    @ApiModelProperty(value = "子节点")
    private List<CascaderExtraDto> children;

    public CascaderExtraDto(Long value, String label, boolean disabled) {
        this.value = value;
        this.label = label;
        this.disabled = disabled;
    }

    public CascaderExtraDto(Long value, String label) {
        this.value = value;
        this.label = label;
    }

}
