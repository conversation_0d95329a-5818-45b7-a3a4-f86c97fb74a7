package com.fykj.scaffold.zsq_docking.log.controller;

import com.fykj.scaffold.zsq_docking.log.domain.dto.ActivityDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingActivityRecord;
import com.fykj.scaffold.zsq_docking.log.domain.params.ZSQDockingActivityRecordParams;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingActivityRecordService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

@RestController
@RequestMapping("/admin/zsq_docking_activity_record")
@Api(tags = "知社区活动对接记录")
public class ZSQDockingActivityRecordController extends BaseController<IZSQDockingActivityRecordService, ZSQDockingActivityRecord, ZSQDockingActivityRecordParams> {


    @ApiOperation("获取活动对接记录方法")
    @GetMapping({"/getActivityDockingRecords"})
    public JsonResult<List<ZSQDockingActivityRecord>> getActivityDockingRecords(@RequestParam Long activityId) {
        return new JsonResult<>(this.baseService.getActivityDockingRecords(activityId));
    }
    @ApiOperation("失败重试")
    @GetMapping({"/retry"})
    public Result retry(@RequestParam Long activityId, @RequestParam Long recordId) {
        baseService.retry(activityId, recordId);
        return OK;
    }

    @ApiOperation("获取对接记录详情")
    @GetMapping({"/getDockingRecordDetail"})
    public JsonResult<ActivityDockingRecordDetailDto> getDockingRecordDetail(@RequestParam Long recordId) {
        return new JsonResult<>(baseService.getDockingRecordDetail(recordId));
    }
}
