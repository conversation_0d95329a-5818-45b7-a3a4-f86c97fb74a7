package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import com.fykj.scaffold.security.business.domain.entity.Resource;
import com.fykj.scaffold.security.business.domain.entity.Role;
import com.fykj.scaffold.security.business.domain.entity.RoleResource;
import com.fykj.scaffold.security.business.mapper.RoleResourceMapper;
import com.fykj.scaffold.security.business.service.IResourceService;
import com.fykj.scaffold.security.business.service.IRoleResourceService;
import com.fykj.scaffold.security.business.service.IRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RoleResourceServiceImpl extends BaseServiceImpl<RoleResourceMapper, RoleResource> implements IRoleResourceService {

    @Autowired
    private IRoleService roleService;

    @Autowired
    private IResourceService resourceService;

    @Override
    public boolean exists(Long roleId, String uri) {
        return lambdaQuery().eq(RoleResource::getRoleId, roleId)
                .eq(RoleResource::getResourceUrl, uri).count() > 0;
    }

    @Override
    public boolean save(Long roleId, Long resourceId) {
        return save(build(roleId, resourceId));
    }

    @Override
    public boolean saveRoleResourceRelationship(long roleId, List<Long> resourceIds) {
        baseMapper.deleteRoleResourceByRoleId(roleId);
        List<RoleResource> rsList = resourceIds.stream().map(it -> {
            RoleResource rs = new RoleResource();
            rs.setRoleId(roleId);
            rs.setResourceId(it);
            return rs;
        }).collect(Collectors.toList());
        return saveBatch(rsList);
    }

    @Override
    public List<String> findResourceIdList(Long roleId) {
        return baseMapper.findResourceIdList(roleId);
    }

    @Override
    public List<Resource> findResourceByRoleId(long roleId) {
        List<Long> resourceIds = lambdaQuery().eq(RoleResource::getRoleId, roleId).list().stream().map(RoleResource::getResourceId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        return resourceService.lambdaQuery().in(BaseEntity::getId, resourceIds).orderByAsc(Resource::getSequence).list();
    }

    private RoleResource build(Long roleId, Long resourceId) {
        RoleResource rs = new RoleResource();
        Role role = roleService.getById(roleId);
        rs.setRoleId(roleId);
        rs.setRoleName(role.getName());
        rs.setResourceId(resourceId);
        Resource resource = resourceService.getById(resourceId);
        if (resource != null) {
            rs.setResourceName(resource.getName());
        }
        return rs;
    }
}
