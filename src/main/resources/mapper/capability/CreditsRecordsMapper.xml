<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fykj.scaffold.capability.mapper.CreditsRecordsMapper">
    <select id="getUserCredits" resultType="com.fykj.scaffold.capability.domain.dto.CreditsRecordsDTO">
        SELECT
            ccr.id AS id,
            ccr.credits AS credits,
            ccr.course_id AS courseId,
            cc.course_name AS courseName,
            ccr.user_id AS userId,
            ccr.generate_at AS generateAt,
            ccr.create_date AS createDate,
            ccr.update_date AS updateDate
        FROM cu_credits_records ccr
        LEFT JOIN cu_course cc ON cc.id = ccr.course_id
        <where>
            <if test="params.userId != null">
                AND ccr.user_id = #{params.userId}
            </if>
        </where>
        ORDER BY ccr.generate_at DESC
    </select>
</mapper>
