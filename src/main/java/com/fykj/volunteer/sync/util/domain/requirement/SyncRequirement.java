package com.fykj.volunteer.sync.util.domain.requirement;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步需求
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncRequirement {
    /**
     * 唯一号(必填)
     */
    private String uniCode;
    /**
     * 需求发布方类型(必填)
     * 1:实践阵地（实践中心、所、站）
     * 2:职能部门
     * 3:实践点
     * 4:组织团队
     */
    private String pubType;
    /**
     * 需求发布方编号，根据需求发布方类型选择对应注册接口返回值（必填）
     */
    private String pubId;
    /**
     * 服务领域代码取值范围：服务领域查询接口（必须是第二级）（必填）
     */
    private String serviceType;
    /**
     * 需求类型（必填）
     * 取值范围：
     * 1：项目/服务
     * 2：团队/专家
     * 3：阵地/场地
     */
    private String reqType;
    /**
     * 需求名称（必填）
     */
    private String title;
    /**
     * 需求描述（必填）
     */
    private String description;
    /**
     * 负责人姓名（必填）
     */
    private String contact;
    /**
     * 负责人联系电话（必填）
     */
    private String contactNo;
    /**
     * 需求logo图片（必填）
     */
    private String picLogoUrl;
    /**
     * 接口调用类型（必填）
     */
    private String saveType;
    /**
     * 需求编号(saveType为2时则此reqId为必填)
     */
    private String reqId;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "req")
    public static class Req {
        private List<SyncRequirement> info;
    }

    /**
     * 保存类型
     */
    public static class SaveType {
        /**
         * 新增
         */
        public final static String ADD = "1";

        /**
         * 修改
         */
        public final static String MODIFY = "2";
    }

    /**
     * 需求类型
     */
    public static class ReqType {
        /**
         * 1：项目/服务
         */
        public final static String PROJECT_SERVICE = "1";
        /**
         * 2：团队/专家
         */
        public final static String TEAM_PRO = "2";
        /**
         * 3：阵地/场地
         */
        public final static String AREA = "3";
    }

    /**
     * 需求发布方
     */
    public static class PubType {
        /**
         * 1:实践阵地（实践中心、所、站）
         */
        public final static String BASE = "1";
        /**
         * 2:职能部门
         */
        public final static String DEP = "2";
        /**
         * 3:实践点
         */
        public final static String SPOT = "3";
        /**
         * 4:组织团队
         */
        public final static String TEAM = "4";
    }

    public String buildXml() {
        Req req = new Req();
        req.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(req);
    }
}
