<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.dynamic.mapper.DynamicFormFieldRelationMapper">

    <delete id="removeByIds">
        DELETE
        FROM dynamic_form_field_relation
        WHERE
            id IN
            <foreach item="item" collection="idList" index="" separator="," open="(" close=")">
                #{item}
            </foreach>
    </delete>
    <delete id="removeById">
        DELETE
        FROM dynamic_form_field_relation
        WHERE id  = #{id}
    </delete>
    <delete id="removeByFormId">
        DELETE
        FROM dynamic_form_field_relation
        WHERE form_id  = #{formId}
    </delete>
</mapper>