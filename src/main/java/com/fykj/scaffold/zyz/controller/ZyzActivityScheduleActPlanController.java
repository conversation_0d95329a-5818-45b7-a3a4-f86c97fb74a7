package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivitySchedule;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleActPlan;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleActPlanParams;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleActPlanService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;

/**
 * 阵地计划（小计划）关联活动计划（大活动）
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@RestController
@RequestMapping("/admin/zyz/activity/schedule/act/plan")
@Api(tags = " 阵地计划（小计划）关联活动计划（大活动）接口")
public class ZyzActivityScheduleActPlanController extends BaseController<IZyzActivityScheduleActPlanService, ZyzActivityScheduleActPlan, ZyzActivityScheduleActPlanParams> {

    @AuditLog("保存关联")
    @ApiOperation("保存关联")
    @PostMapping("/saveScheduleActPlan")
    public Result saveScheduleActPlan(@RequestBody ZyzActivitySchedule dto) {
        baseService.saveScheduleActPlan(dto);
        return OK;
    }
}
