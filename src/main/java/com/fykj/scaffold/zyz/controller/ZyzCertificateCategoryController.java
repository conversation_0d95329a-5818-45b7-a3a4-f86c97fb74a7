package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzCertificateCategory;
import com.fykj.scaffold.zyz.domain.params.ZyzCertificateCategoryParams;
import com.fykj.scaffold.zyz.service.IZyzCertificateCategoryService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动证书-分类表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-03
 */
@RestController
@RequestMapping("/admin/zyz/certificate/category")
public class ZyzCertificateCategoryController
        extends BaseController<IZyzCertificateCategoryService, ZyzCertificateCategory, ZyzCertificateCategoryParams> {

}
