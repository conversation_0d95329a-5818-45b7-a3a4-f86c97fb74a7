<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.big_screen.mapper.CivilizationEnterpriseMapper">

    <select id="getScreenDataList" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationEnterpriseDto">
        SELECT
            name AS enterprise
        FROM civilization_enterprise
        WHERE is_deleted = 0 AND status = 1
<!--        <if test="params.start != null">-->
<!--            AND effective_time &gt;= #{params.start}-->
<!--        </if>-->
<!--        <if test="params.end != null">-->
<!--            AND effective_time &lt;= #{params.end}-->
<!--        </if>-->
        ORDER BY sequence ASC, effective_time DESC
    </select>
</mapper>
