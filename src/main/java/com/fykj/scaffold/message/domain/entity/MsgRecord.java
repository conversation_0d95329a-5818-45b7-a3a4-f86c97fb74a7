package com.fykj.scaffold.message.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("msg_record")
public class MsgRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 发送id
     */
    @TableField("send_id")
    @ApiModelProperty(value = "发送id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sendId;

    /**
     * 是否是自定义消息
     */
    @TableField("self_define")
    @ApiModelProperty(value = "是否是自定义消息")
    private Boolean selfDefine;

    /**
     * 标题
     */
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 推送方式
     */
    @TableField("send_type")
    @ApiModelProperty(value = "推送方式")
    @DictTrans
    private String sendType;

    /**
     * 推送code
     */
    @TableField("send_code")
    @ApiModelProperty(value = "推送code")
    private String sendCode;

    /**
     * 推送内容
     */
    @TableField("content")
    @ApiModelProperty(value = "推送内容")
    private String content;

    /**
     * 已读状态
     */
    @TableField("status")
    @ApiModelProperty(value = "已读状态")
    private Boolean status;

    /**
     * 推送状态
     */
    @TableField("send_status")
    @ApiModelProperty(value = "推送状态")
    private Integer sendStatus;

    /**
     * 推送者
     */
    @TableField("sender")
    @ApiModelProperty(value = "推送者")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sender;

    /**
     * 推送者姓名
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "推送者姓名")
    private String senderName;

    /**
     * 接收者(志愿者id)
     */
    @TableField("receiver")
    @ApiModelProperty(value = "接收者(志愿者id)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long receiver;

    /**
     * 接收者(志愿者名称)
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接收者(志愿者名称)")
    private String receiverVolunteerName;

    /**
     * 接收者姓名
     */
    @TableField("receiver_name")
    @ApiModelProperty(value = "接收者姓名")
    private String receiverName;

    /**
     * 接收者手机号
     */
    @TableField("receiver_mobile")
    @ApiModelProperty(value = "接收者手机号")
    private String receiverMobile;

    /**
     * 接收者是否是志愿者
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接收者是否是志愿者")
    private Boolean receiverVolunteer;

    /**
     * 推送时间
     */
    @TableField("send_time")
    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    /**
     * 推送失败原因
     */
    @TableField(value = "send_fail_reason", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "推送失败原因")
    private String sendFailReason;

    /**
     * 任务序列 id
     */
    @TableField(value = "task_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "任务序列 id")
    private String taskId;

    /**
     * 短信真实发送状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "短信真实发送状态")
    private String smsSendStatus;

    /**
     * 短信真实接收时间
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "短信真实接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime smsReceiveTime;

    /**
     * 短信返回码
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "短信返回码")
    private String smsErrorCode;
}
