package com.fykj.scaffold.support.conns;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;

/**
 * 常量类
 *
 * <AUTHOR>
 */
public interface Cons {

    /**
     * 密码加密aeskey
     */
    String PWD_DECRYPT_KEY = "aaaaaaaaaaaaaaaa";

    String[] DEFAULT_IGNORE_COPY_FILED = {"id", "version", "deleted", "creator", "updater", "createDate", "updateDate"};

    /**
     * 重置初始密码
     */
    String INIT_PSD = "123456";

    /**
     * 默认排序字段
     */
    String SORT_COLUMN_DEFAULT = "createDate";

    /**
     * 默认排序方向
     */
    String SORT_ORDER_DEFAULT = "desc";

    /**
     * 默认日期格式
     */
    String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 默认时间格式
     */
    String TIME_FORMAT = "HH:mm";

    /**
     * 默认日期时间格式
     */
    String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 文件存储方式code
     */
    String SERVER_CODE = "server-code";

    /**
     * 文件类型code
     */
    String FILE_TYPE = "fileType";

    /**
     * 文件类型-图片 code
     */
    String FILE_TYPE_IMAGE = "picture";

    /**
     * 未知文件格式
     */
    String OTHER_TYPES = "otherTypes";

    /**
     * 审核通过
     */
    String AUDIT_STATUS_PASS = "pass";

    /**
     * 审核不通过
     */
    String AUDIT_STATUS_NOT_PASS = "notPass";

    /**
     * 待审核
     */
    String AUDIT_STATUS_CHECK = "CheckPending";


    /**
     * pid（第三方接口加密参数）
     */
    String PID = "10000";

    /**
     * format（第三方接口加密参数）
     */
    String FORMAT = "json";

    /**
     * authcode(授权码，第三方接口加密参数）
     */
    String AUTHCODE = "123456";

    /**
     * 志愿者状态
     */
    String VOLUNTEER_STATUS_FREEZE = "volunteer_status_freeze";

    /**
     * 最上级组织code
     */
    String TOP_DEPT_CODE = "top_dept";

    /**
     * 5大分协会（金鸡湖，娄葑，斜塘，胜浦，唯亭）
     */
    List<String> FIVE_AREA_SUB_ASSOCIATION = Arrays.asList("jinjihu",
            "loufeng",
            "weiting",
            "shengpu",
            "xietang");

    /**
     * 团队志愿者角色--管理员
     */
    String DUTY_TYPE_ADMIN = "duty_type_admin";

    /**
     * 团队志愿者角色--普通成员
     */
    String DUTY_TYPE_COMMON = "duty_type_common";

    /**
     * 团队志愿者角色--负责人
     */
    String DUTY_TYPE_CURATOR = "duty_type_curator";

    /**
     * redis存union绑定
     */
    String REDIS_UNION_ID_BIND_KEY = "UNION_ID_BIND_KEY";

    /**
     * 用户所属-团队
     */
    String USER_PRI_TYPE_TEAM = "team";

    /**
     * 用户所属-组织
     */
    String USER_PRI_TYPE_ORG = "org";

    /**
     * 模版内容占位分隔符
     */
    String MTM_SPLIT_CHAR = "{}";

    /**
     * 模版内容，消息提示，跳转链接分隔符
     */
    String MTM_TMP_PMP_JL_SPLIT_CHAR = "&&";

    /**
     * 消息推送方式-短信推送
     */
    String MPW_SMS = "sms";

    /**
     * 消息推送方式-微信推送
     */
    String MPW_WECHAT = "wechat";

    /**
     * 微信公众号消息模版id
     */
    String TEMPLATE_ID = "IQMFi4egH8a3JpjXPZ_aTwQw-oTQHonxmhDIPIMBQbo";

    /**
     * 微信小程序id
     */
    String APP_ID = "wx29145e35261335f9";

    /**
     * 微信公众号模版消息数据key
     */
    String WECHAT_MSG_DATA_ONE_KEY = "thing2";

    /**
     * 微信公众号模版消息数据key
     */
    String WECHAT_MSG_DATA_TIME_KEY = "time3";

    /**
     * 微信公众号模版消息数据key
     */
    String WECHAT_MSG_DATA_TWO_KEY = "thing4";

    /**
     * OAuht2客户端
     */
    interface ClientList {
        /**
         * 小程序
         */
        String MINI_APP = "miniapp";

        /**
         * 管理端
         */
        String BACKEND = "client_2";

        /**
         * PC站点
         */
        String WEBSITE = "website";

        /**
         * 客户端列表
         */
        List<String> CLIENT_LIST = CollectionUtil.newArrayList(MINI_APP, BACKEND, WEBSITE);
    }

    /**
     * 角色
     */
    interface RoleCode {
        /**
         * 角色编码-系统管理员
         */
        String ROLE_CODE_ADMIN = "SYS_ADMIN";

        /**
         * 角色编码-协会管理员
         */
        String ROLE_CODE_ASSOCIATION_ADMIN = "ASSOCIATION_ADMIN";

        /**
         * 角色编码-分协会管理员
         */
        String ROLE_CODE_SUB_ASSOCIATION_ADMIN = "SUB_ASSOCIATION_ADMIN";

        /**
         * 角色编码-社区管理员
         */
        String ROLE_CODE_COMMUNITY_ADMIN = "COMMUNITY_ADMIN";

        /**
         * 角色编码-团队管理员
         */
        String ROLE_CODE_TEAM_ADMIN = "TEAM_ADMIN";

        /**
         * 角色编码-志愿者
         */
        String ROLE_CODE_VOLUNTEER = "VOLUNTEER";

        /**
         * 特殊角色
         */
        String[] SYS_EMBED_ROLE = {ROLE_CODE_ASSOCIATION_ADMIN, ROLE_CODE_SUB_ASSOCIATION_ADMIN, ROLE_CODE_COMMUNITY_ADMIN, ROLE_CODE_TEAM_ADMIN, ROLE_CODE_VOLUNTEER};

        /**
         * 管理身份角色
         */
        String[] MANAGER_CAPACITY_ROLE = {ROLE_CODE_ASSOCIATION_ADMIN, ROLE_CODE_SUB_ASSOCIATION_ADMIN, ROLE_CODE_COMMUNITY_ADMIN, ROLE_CODE_TEAM_ADMIN};

        /**
         * 管理身份角色,无团队管理员
         */
        String[] MANAGER_ROLE_NO_TEAM = {ROLE_CODE_ASSOCIATION_ADMIN, ROLE_CODE_SUB_ASSOCIATION_ADMIN, ROLE_CODE_COMMUNITY_ADMIN};


        /**
         * 角色编码-部委局办用户角色
         */
        String ROLE_CODE_DEPART_BUREAU_ADMIN = "DEPART_BUREAU_ADMIN";
    }

    /**
     * 市平台同步状态机
     */
    interface PlatformSyncState {
        /**
         * 未同步
         */
        String WAIT_SYNC = "sync_wait";
        /**
         * 同步成功
         */
        String SUCCESS = "sync_success";
        /**
         * 同步失败
         */
        String FAILURE = "sync_failure";
    }

    /**
     * 市平台同步业务码
     */
    interface PlatformSyncBiz {
        /**
         * 同步志愿者
         */
        String VOLUNTEER_SYNC = "sync_biz_volunteer";
        /**
         * 同步团队
         */
        String TEAM_SYNC = "sync_biz_team";
        /**
         * 同步加入团队
         */
        String TEAM_JOIN_SYNC = "sync_biz_join_team";
        /**
         * 同步志愿者新闻
         */
        String NEWS_SYNC = "sync_biz_news_sync";
        /**
         * 需求同步
         */
        String REQUIREMENT_SYNC = "sync_biz_requirement";
        /**
         * 资源同步
         */
        String RESOURCE_SYNC = "sync_biz_resource";
        /**
         * 同步职能部门
         */
        String DEP_SYNC = "sync_biz_dep_sync";
        /**
         * 同步实践阵地
         */
        String BASE_SYNC = "sync_biz_base_sync";
        /**
         * 同步实践点
         */
        String SPOT_SYNC = "sync_biz_spot_sync";
        /**
         * 活动同步
         */
        String ACTIVITY_SYNC = "sync_biz_activity";
        /**
         * 活动发布招募同步
         */
        String ACTIVITY_RECRUIT_SYNC = "sync_biz_activity_recruit";
        /**
         * 活动加入招募同步
         */
        String ACTIVITY_MEMBER_SYNC = "sync_biz_activity_member";
        /**
         * 服务时长同步
         */
        String SERVICE_TIME_SYNC = "sync_biz_service_time";
        /**
         * 实践单同步
         */
        String ORDER_SYNC = "sync_biz_order";
        /**
         * 活动公示同步
         */
        String ACTIVITY_SHOW_SYNC = "sync_activity_show";

        /**
         * 阵地计划同步
         */
        String ACT_SCHEDULE_SYNC = "sync_act_schedule";

        /**
         * 阵地计划同步
         */
        String SCHEDULE_ACT_PLAN_SYNC = "sync_schedule_act_plan";
    }

    /**
     * 系统关键配置
     */
    interface SystemConfig {
        /**
         * 默认同步图片
         */
        String DEFAULT_SYNC_PIC_URL = "sc_default_sync_pic";
    }


    /**
     * 组织架构类型
     */
    interface OrgLevel {
        /**
         * 协会
         */
        int ASSOCIATION = 1;
        /**
         * 分协会
         */
        int SUB_ASSOCIATION = 2;
        /**
         * 社区
         */
        int COMMUNITY = 3;
    }

    interface OrderType {
        /**
         * 需求
         */
        String REQUIREMENT = "requirement";

        /**
         * 资源
         */
        String RESOURCE = "resource";

        /**
         * 活动
         */
        String ACTIVITY = "activity";
    }

    interface SyncConstant {
        /**
         * 活动同步-orgId(造的志愿者协会的假团队)
         */
        String AS_ORG_ID = "AEDD73966D69418485280BDDB67DA427";

        /**
         * 活动同步-regionCode
         */
        String AS_REGION_CODE = "320508008144";

        /**
         * 实践单同步-pbId
         */
        String OS_PB_ID = "4bcfcceb8e814400b3948f390e214078";

        /**
         * 实践单同步-pbId
         */
        String OS_PB_NAME = "园区志愿者协会";

        /**
         * 默认联系人
         */
        String DEFAULT_CONTACT_NAME = "李嘉泽";

        /**
         * 默认联系电话
         */
        String DEFAULT_CONTACT_PHONE = "13092613546";

        /**
         * 默认身份证
         */
        String DEFAULT_ID_CARD = "32038119881122571X";

        /**
         * 默认经度
         */
        String DEFAULT_LONGITUDE = "120.66812900";

        /**
         * 默认纬度
         */
        String DEFAULT_LATITUDE = "31.31317100";
    }

    /**
     * 短信验证码常量
     */
    @ToString
    @AllArgsConstructor
    enum SmsValidCodeTemplate {
        LOGIN("登录志愿者服务平台"),
        REGISTER("注册成为一名志愿者"),
        CHANGE_PHONE("更换手机号"),
        CHECK_PHONE("找回老系统团队权限"),
        RESET_PASSWORD("重置密码"),
        PWD_FORGET_UPDATE("对忘记密码进行密码重置");
        public final String text;
    }

    /**
     * 忘记密码后密码修改数据加密key
     */
    String PFU_DECRYPT_KEY = "FengYunSciTecEnt";

    /**
     * 测试环境标识
     */
    String ENV_TEST = "test";

    /**
     * 一般开关
     */
    interface COMMON_SWITCH {
        /**
         * 开
         */
        String YES = "1";
        /**
         * 关
         */
        String NO = "0";
    }
}
