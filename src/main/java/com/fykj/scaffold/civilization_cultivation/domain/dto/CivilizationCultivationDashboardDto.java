package com.fykj.scaffold.civilization_cultivation.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("文明培育数大屏数据展示模型")
public class CivilizationCultivationDashboardDto implements Serializable {
    private static final long serialVersionUID = 6202984873231849954L;

    @ApiModelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "年份")
    private int year;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "图片地址")
    private String picture;

    @ApiModelProperty(value = "事迹/成就")
    private String achievements;
    @ApiModelProperty(value = "链接")
    private String link;


}
