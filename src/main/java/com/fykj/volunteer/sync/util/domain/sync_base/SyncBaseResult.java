package com.fykj.volunteer.sync.util.domain.sync_base;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 *
 * <pb>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>同步失败：基地所属区划不存在</msg>
 *     <name>园区文明办</name>
 *     <regionCode>3205080012</regionCode>
 *     <uniCode>1001</uniCode>
 *   </info>
 * </pb>
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncBaseResult {
    /**
     * 实践阵地编号
     */
    private String pbId;
    /**
     * 所属区域编码
     */
    private String regionCode;
    /**
     * 实践阵地名称
     */
    private String name;
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "pb")
    public static class Pb {
        private List<SyncBaseResult> info;
    }
}
