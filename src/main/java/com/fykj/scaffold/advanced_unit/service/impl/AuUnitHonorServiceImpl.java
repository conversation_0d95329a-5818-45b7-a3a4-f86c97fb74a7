package com.fykj.scaffold.advanced_unit.service.impl;

import com.fykj.scaffold.advanced_unit.domain.entity.AuUnitHonor;
import com.fykj.scaffold.advanced_unit.mapper.AuUnitHonorMapper;
import com.fykj.scaffold.advanced_unit.service.IAuUnitHonorService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 文明单位-指标标准-测评项
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AuUnitHonorServiceImpl extends BaseServiceImpl<AuUnitHonorMapper, AuUnitHonor> implements IAuUnitHonorService {

}
