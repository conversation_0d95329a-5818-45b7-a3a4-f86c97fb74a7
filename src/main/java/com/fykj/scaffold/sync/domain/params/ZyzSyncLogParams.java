package com.fykj.scaffold.sync.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 市平台信息同步日志
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ZyzSyncLogParams extends BaseParams {
    @MatchType(QueryType.EQ)
    private String bizType;

    @MatchType(QueryType.LIKE)
    private String syncObjectName;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @MatchType(value = QueryType.TIME_START, fieldName = "sync_time")
    private LocalDateTime startTime;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @MatchType(value = QueryType.TIME_END, fieldName = "sync_time")
    private LocalDateTime endTime;

    @MatchType(value = QueryType.EQ, fieldName = "resultCode")
    private String resultCode;
}
