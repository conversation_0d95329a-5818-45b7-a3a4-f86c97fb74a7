package com.fykj.scaffold.security.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class SysOrgDto {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 部门code
     */

    @ApiModelProperty(value = "部门code")
    private String code;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;


    /**
     * 层级（1协会，2分协会，3社区，4小区（预留））
     */
    @ApiModelProperty(value = "层级（1协会，2分协会，3社区，4小区（预留））")
    private Integer level;

    /**
     * 父级部门ID
     */
    @ApiModelProperty(value = "父级部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;


    private List<SysOrgDto> children = new ArrayList<>();
}
