package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> @email ${email}
 * @date 2023-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_exchange_distribution_report")
public class MallExchangeDistributionReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 年度
     */
    @TableField("year")
    @ApiModelProperty(value = "年度")
    private String year;

    /**
     * 服务时长时间段数据字典
     */
    @TableField("server_time")
    @ApiModelProperty(value = "服务时长时间段数据字典")
    private String serverTime;

    /**
     * 达标人数
     */
    @TableField("qualified_personnel_num")
    @ApiModelProperty(value = "达标人数")
    private Integer qualifiedPersonnelNum;

    /**
     * 达标人的总服务时长
     */
    @TableField("total_server_time")
    @ApiModelProperty(value = "达标人的总服务时长")
    private BigDecimal totalServerTime;

    /**
     * 达标人的总产生积分
     */
    @TableField("total_point")
    @ApiModelProperty(value = "达标人的总产生积分")
    private BigDecimal totalPoint;

    /**
     * 达标人的总兑换积分
     */
    @TableField("total_exchange_point")
    @ApiModelProperty(value = "达标人的总兑换积分")
    private BigDecimal totalExchangePoint;

    /**
     * 达标人兑换人数
     */
    @TableField("total_exchange_num")
    @ApiModelProperty(value = "达标人兑换人数")
    private Integer totalExchangeNum;


}
