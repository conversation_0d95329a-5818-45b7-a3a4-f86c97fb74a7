package com.fykj.volunteer.sync.util.domain.sync_team;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步团队信息
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncTeam {
    /**
     * 组织名称 (必填)
     */
    private String orgName;
    /**
     * 管理员身份证(必填)
     */
    private String idCode;
    /**
     * 职能部门编号(必填，注册职能部门接口返回）
     */
    private String dpId;
    /**
     * 组织所属区域代码(必填)
     */
    private String regionCode;
    /**
     * 组织简介(必填)
     */
    private String descs;
    /**
     * 负责人(必填)
     */
    private String linkName;
    /**
     * 负责人联系电话(必填)
     */
    private String linkPhone;
    /**
     * 父组织编号
     */
    private String fId;
    /**
     * 第三方平台唯一键(必填)
     */
    private String uniqueCode;
    /**
     * 组织部门logo图片(必填)
     */
    private String picLogoUrl;
    /**
     * 接口调用类型(必填)，1新增，2修改
     */
    private String saveType;
    /**
     * 组织id（saveType为2时则此orgId为必填）
     */
    private String orgId;

    /**
     * 保存类型
     */
    public static class SaveType {
        /**
         * 新增
         */
        public final static String ADD = "1";

        /**
         * 修改
         */
        public final static String MODIFY = "2";
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "org")
    public static class Org {
        private List<SyncTeam> info;
    }

    public String buildXml() {
        Org org = new Org();
        org.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(org);
    }
}
