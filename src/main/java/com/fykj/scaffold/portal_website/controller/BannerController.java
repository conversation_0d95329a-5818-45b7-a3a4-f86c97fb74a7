package com.fykj.scaffold.portal_website.controller;


import com.fykj.scaffold.portal_website.domain.entity.Banner;
import com.fykj.scaffold.portal_website.domain.params.BannerParams;
import com.fykj.scaffold.portal_website.service.IBannerService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.Result;
import result.ResultCode;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/admin/portal_website/banner")
@Api(tags = "轮播图接口")
public class BannerController extends BaseController<IBannerService, Banner, BannerParams> {

    @AuditLog("轮播图上下架操作")
    @ApiOperation("轮播图上下架")
    @GetMapping({"/ground"})
    public Result ground(@RequestParam String id) {
        baseService.ground(id);
        return new Result(ResultCode.OK);
    }
}
