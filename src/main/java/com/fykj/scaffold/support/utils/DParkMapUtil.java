package com.fykj.scaffold.support.utils;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.zyz.util.LngLonUtil;

import com.qiniu.util.StringUtils;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class DParkMapUtil {
    static String URL = "https://cim.dpark.com.cn/zmq-gateway/SIPCIM/DataExchange/civilizedPark";
    static String SIPCIM_TOKEN = "eyJhbGciOiJIUzI1NiJ9.eyJhcHBJZCI6IjgzOTg3MDAzLTNiZjItNGU3Yy04YzBmLTY5ZDNhNzgzMWRkOCIsImV4cCI6MTk2Njc4MDc5OSwiaWF0IjoxNjY4MTQ0MzQyfQ.Q0DQj4A8YzrnbZ_qA0Jl7Igl6IeITO0cJbEyh-UMEDc";
    static String SECRET = "fd7b8e67-5b11-48e2-a288-7879299e6eb1";

    /**
     * 调用文明园区地址检索接口
     *
     * @param lat
     * @param lon
     * @return
     */
    public static ZedParkResultDto sendHttpReq(double lat, double lon) {
        Map<String, Object> param = new HashMap<>();
        param.put("sipcim_token", SIPCIM_TOKEN);
        param.put("secret", SECRET);
        //高德转84
        param.put("x", lon);
        param.put("y", lat);
        HttpResponse execute = HttpUtil.createGet(URL)
                .form(param).timeout(5000).execute();
        if (HttpStatus.HTTP_OK != execute.getStatus()) {
            log.error("请求文明园区地址检索接口错误" + execute);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "请求文明园区地址检索接口失败！");
        }
        JSONObject json = JSON.parseObject(execute.body());
        if (StringUtils.isNullOrEmpty(json.getString("data"))) {
            return new ZedParkResultDto();
        }
        List<ZedParkDto> zedParkDtos = JSON.parseArray(json.getString("data"), ZedParkDto.class);
        if(zedParkDtos.isEmpty()){
            return new ZedParkResultDto();
        }
        ZedParkResultDto zedParkResultDto = new ZedParkResultDto();
        zedParkResultDto.setAddress(zedParkDtos.get(0).getLast_name());
        zedParkResultDto.setCommunityArea(zedParkDtos.get(0).getFour_code());
        zedParkResultDto.setCommunityName(zedParkDtos.get(0).getFour_name());
        zedParkResultDto.setStreetArea(zedParkDtos.get(0).getThre_code());
        zedParkResultDto.setStreetName(zedParkDtos.get(0).getThre_name());
        String coordinate84 = zedParkDtos.get(0).getCoordinate84();
        int index = coordinate84.indexOf(" ", coordinate84.indexOf(" ") + 1);
        int startIndex = coordinate84.indexOf("(");
        int endIndex = coordinate84.indexOf(")");
        double lon84 = Double.parseDouble(coordinate84.substring(startIndex + 1, index));
        double lat84 = Double.parseDouble(coordinate84.substring(index, endIndex - 1));
        zedParkResultDto.setLon84(lon84);
        zedParkResultDto.setLat84(lat84);
        //84转高德
        double[] gaudCoordinate = LngLonUtil.gps84ToGcj02(lat, lon);

        zedParkResultDto.setLon(gaudCoordinate[1]);
        zedParkResultDto.setLat(gaudCoordinate[0]);
        return zedParkResultDto;
    }

    public static void main(String[] args) {
        sendHttpReq(31.33036100, 120.72986000);
    }
}
