package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.sync.service.IZyzSyncRegionDictAllService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class RegionSyncAllTask {

    @Autowired
    private IZyzSyncRegionDictAllService regionDictAllService;

    @XxlJob("Region_Sync_All_Handler")
    public void regionAllSyncJobHandler() {
        XxlJobHelper.log("Region_Sync_All_Handler-Job, START!");
        try {
            regionDictAllService.syncRegionDictAll();
        } catch (Exception e) {
            log.error("地域同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Region_Sync_All_Handler-Job, END!");
    }
}
