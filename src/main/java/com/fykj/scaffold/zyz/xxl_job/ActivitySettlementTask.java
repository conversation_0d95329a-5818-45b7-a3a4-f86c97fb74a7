package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityApplySettleService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import utils.StringUtil;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 活动结算定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActivitySettlementTask {

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzActivityApplyService applyService;

    @Autowired
    private IZyzActivityApplySettleService settleService;

    @XxlJob("Apply_Settlement_Handler")
    public void activitySettlementJobHandler() {
        final Long pointApplyId;
        if (NumberUtil.isNumber(XxlJobHelper.getJobParam())) {
            pointApplyId = Long.valueOf(XxlJobHelper.getJobParam());
        } else {
            pointApplyId = null;
        }
        XxlJobHelper.log("Apply-Settlement-Handler-Job, START!");
        // 获取结束时间在近15天内且小于当前时间的审核通过的活动时间段id
        List<Long> actTimePeriodIds = actTimePeriodService.getNeedSettlementRecords();
        if (CollectionUtil.isEmpty(actTimePeriodIds)) {
            XxlJobHelper.log("Apply-Settlement-Handler-Job, END!");
            return;
        }
        // 获取需要结算的报名数据
        List<ZyzActivityApply> applies = applyService.getNeedSettlementRecords(actTimePeriodIds);
        if (CollectionUtil.isEmpty(applies)) {
            XxlJobHelper.log("Apply-Settlement-Handler-Job, END!");
            return;
        }
        if (pointApplyId != null) {
            applies = applies.stream().filter(it -> it.getId().equals(pointApplyId)).collect(Collectors.toList());
        }
        // 批量结算
        settleService.settleBatch(applies);
        XxlJobHelper.log("Apply-Settlement-Handler-Job, END!");
    }
}
