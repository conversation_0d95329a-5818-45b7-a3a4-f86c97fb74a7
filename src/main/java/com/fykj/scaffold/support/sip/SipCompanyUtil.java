package com.fykj.scaffold.support.sip;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectCompany;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import result.ResultCode;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 园区法人库工具类
 */
@Slf4j
public class SipCompanyUtil {

    /**
     * {
     * "code": "10001",
     * "data": {
     * "total": 1,
     * "data": [{
     * "ztrq": "2022-06-27",
     * "hyml": "",
     * "ywid": "40594628",
     * "zxrq": "",
     * "clrq": "2010-02-12",
     * "qyzch": "320594000155103",
     * "fddbrzjhm": "610103197211252495",
     * "jjlxmc": "",
     * "jyhyyjzrq": "2030-02-10",
     * "qymc": "江苏风云科技服务有限公司",
     * "jycs": "",
     * "zclx": "1100",
     * "bszh": "91320594551181055L",
     * "jyfw": "软件开发与销售；电脑软硬件销售；软件产品及其应用系统的评测技术服务；软件工程、信息系统工程建设监理服务；网络系统集成及技术服务；IT技术服务、技术培训；软件技术研发、设计及技术转让，并提供相关技术服务；增值电信业务。（依法须经批准的项目，经相关部门批准后方可开展经营活动）",
     * "qylxdm": "91",
     * "jycsxzqh": "6060",
     * "qyzs": "苏州工业园区金鸡湖大道1355号国际科技园科技广场四楼",
     * "yzbm": "215021",
     * "hymc": "软件和信息技术服务业",
     * "dw_lyb": "std_scjdj_zzly_qzjgyy2_t_corp,std_scjdj_zzly_qzjgyy2_t_corp_other",
     * "dw_timestamp": "2023-06-21 17:02:31",
     * "dw_lyjg": "市场监督管理局",
     * "jyhyyqsrq": "2010-02-12",
     * "zzjgdm": "551181055",
     * "hzrq": "2022-06-27",
     * "zczj": "20000000.000000",
     * "lxdh": "62620800",
     * "fddbrxm": "戴晔",
     * "org": "1402",
     * "qylxmc": "企业",
     * "sfyqn": "1",
     * "hydm": "I6500",
     * "cylx": "",
     * "djjg": "320594666",
     * "tyshxydm": "91320594551181055L",
     * "zczbbz": "156",
     * "dw_operation": "更新",
     * "jyzt": "01",
     * "qygm": "",
     * "dxrq": "",
     * "dw_agencykey": "26c6b2df-100f-11ee-b89b-fa163e898b16",
     * "jjlxdm": "",
     * "xydj": "A"
     * }],
     * "statusType": 1,
     * "datasource": {
     * "supplierName": "默认信息",
     * "datasourceName": "法人库-null"
     * },
     * "id": "cb6c27c06fce445a949fd5f0a2be700a",
     * "message": "请求成功",
     * "statusCode": "10001"* 	},
     * "message": "请求成功"
     * }
     */

    public static String getCorpInfo(String corpName) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("afterPrefix", "and (bszh='" + corpName + "' or qymc like '%" + corpName + "%')");
        paramMap.put("pageIndex", 0);
        paramMap.put("pageSize", 10);
        paramMap.put("timestamp", System.currentTimeMillis());
        Map<String, List<String>> headers = new HashMap<>();
        headers.put("Content-Type", CollectionUtil.newArrayList("application/json;charset=UTF-8"));
        HttpResponse httpResponse = HttpUtil.createPost("http://172.21.10.46/api/invoke/712567/12304261400414854060?timestamp=" + System.currentTimeMillis())
                .header(headers)
                .body(JSONUtil.toJsonStr(paramMap)).execute();
        if (httpResponse.getStatus() != HttpStatus.HTTP_OK || !JSONUtil.isJson(httpResponse.body())) {
            log.error("调用法人库出错,返回内容" + httpResponse.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "调用法人库出错");
        }
        //看上面 fixme
        JSONObject back = JSONObject.parseObject(httpResponse.body());
        JSONObject exception = back.getJSONObject("exception");
        if (exception != null) {
            String code = exception.getString("code");
            String message = exception.getString("message");
            throw new BusinessException(ResultCode.BAD_REQUEST, "调用法人库出错(" + code + ")：" + message);
        }
        return httpResponse.body();
    }

    public static ZyzProjectCompany getCorpInfoDetail(String creditCode) {
        String corpInfo = getCorpInfo(creditCode);
        //String corpInfo = "{ \"code\": \"10001\", \"data\": { \"total\": 1, \"data\": [{ \"ztrq\": \"2022-06-27\", \"hyml\": \"\", \"ywid\": \"40594628\", \"zxrq\": \"\", \"clrq\": \"2010-02-12\", \"qyzch\": \"320594000155103\", \"fddbrzjhm\": \"610103197211252495\", \"jjlxmc\": \"\", \"jyhyyjzrq\": \"2030-02-10\", \"qymc\": \"江苏风云科技服务有限公司\", \"jycs\": \"\", \"zclx\": \"1100\", \"bszh\": \"91320594551181055L\", \"jyfw\": \"软件开发与销售；电脑软硬件销售；软件产品及其应用系统的评测技术服务；软件工程、信息系统工程建设监理服务；网络系统集成及技术服务；IT技术服务、技术培训；软件技术研发、设计及技术转让，并提供相关技术服务；增值电信业务。（依法须经批准的项目，经相关部门批准后方可开展经营活动）\", \"qylxdm\": \"91\", \"jycsxzqh\": \"6060\", \"qyzs\": \"苏州工业园区金鸡湖大道1355号国际科技园科技广场四楼\", \"yzbm\": \"215021\", \"hymc\": \"软件和信息技术服务业\", \"dw_lyb\": \"std_scjdj_zzly_qzjgyy2_t_corp,std_scjdj_zzly_qzjgyy2_t_corp_other\", \"dw_timestamp\": \"2023-06-21 17:02:31\", \"dw_lyjg\": \"市场监督管理局\", \"jyhyyqsrq\": \"2010-02-12\", \"zzjgdm\": \"551181055\", \"hzrq\": \"2022-06-27\", \"zczj\": \"20000000.000000\", \"lxdh\": \"62620800\", \"fddbrxm\": \"戴晔\", \"org\": \"1402\", \"qylxmc\": \"企业\", \"sfyqn\": \"1\", \"hydm\": \"I6500\", \"cylx\": \"\", \"djjg\": \"320594666\", \"tyshxydm\": \"91320594551181055L\", \"zczbbz\": \"156\", \"dw_operation\": \"更新\", \"jyzt\": \"01\", \"qygm\": \"\", \"dxrq\": \"\", \"dw_agencykey\": \"26c6b2df-100f-11ee-b89b-fa163e898b16\", \"jjlxdm\": \"\", \"xydj\": \"A\" }], \"statusType\": 1, \"datasource\": { \"supplierName\": \"默认信息\", \"datasourceName\": \"法人库-null\" }, \"id\": \"cb6c27c06fce445a949fd5f0a2be700a\", \"message\": \"请求成功\", \"statusCode\": \"10001\" \t}, \"message\": \"请求成功\" }";
        log.info("当前刷新的creditCode:{},响应:{}", creditCode, corpInfo);
        Map map = JSON.parseObject(corpInfo, Map.class);
        Object success = map.get("code");
        if (!Objects.equals("10001", success)) {
            return null;
        }
        Object data = map.get("data");
        if (Objects.isNull(data)) {
            return null;
        }
        Map dataMap = JSON.parseObject(map.get("data").toString(), Map.class);
        Object data1 = dataMap.get("data");
        if (Objects.isNull(data1)) {
            return null;
        }
        List<CorpInfoVo> maps = JSONObject.parseArray(data1.toString(), CorpInfoVo.class);
        if (CollectionUtil.isEmpty(maps)) {
            return null;
        }
        ZyzProjectCompany corp = new ZyzProjectCompany();
        for (CorpInfoVo vo : maps) {
            corp.setCorpName(vo.getQymc());
            corp.setSocialCreditCode(vo.getTyshxydm());

        }
        return corp;
    }


    /**
     * 调用信用接口
     *
     * @param creditCode
     * @param corpName
     * @return
     */
    public static String getCreditInfo(String creditCode, String corpName) {
        Map<String, Object> paramMap = new TreeMap<>();
        // 审查用途，字典值：财政专项资金，成立、加入协会，荣誉表彰，医保定点，项目申报，信用贯标，信用示范，招投标，资质认定，自查，稳岗返还，其他）
        paramMap.put("apply_matter", "资质认定");
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> m = new LinkedHashMap<>();
        m.put("adm_counterpart", corpName);
        m.put("uscc", creditCode);
        list.add(m);
        paramMap.put("info", list);
        paramMap.put("apply_username", "许帆");
        paramMap.put("apply_unit", "宣统部");
        // 行业领域，字典值：生产，工程建设，招标投标，交通运输，电子商务，中介服务，文化体育旅游，医药卫生，社会保障，劳动用工，教育科研，环境保护，社会组织，互联网应用及服务，流通
        paramMap.put("industry_sector", "互联网应用及服务");
        // 审查用途详细说明
        paramMap.put("apply_matter_desc", "用于评审企业责任联盟企业入会资质");
        // 联系电话号码
        paramMap.put("mobile_phone", "13092613546");
        // email(不必输入)
        paramMap.put("email", "");
        // 申请处室
        paramMap.put("apply_office", "宣统部");
        paramMap.put("use_platform_code", "0aacb9c5ca941bc43a957282ed691498");
        // 数据时间范围，12（一年）或者 36（三年）；【12 或 36】
        paramMap.put("valid_month", 36);
        MD5 md5 = new MD5();
        StringJoiner sg = new StringJoiner("&");
        long now = System.currentTimeMillis();
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            Object o = paramMap.get(entry.getKey());
            if (Objects.equals(entry.getKey(), "info")) {
                sg.add(entry.getKey() + "=" + JSONObject.toJSONString(o));
            } else {
                sg.add(entry.getKey() + "=" + o);
            }
        }
        String nonce = UUID.randomUUID().toString();
        String msg = sg.toString();
        log.info("调用信用接口,msg:{},now:{},nonce:{}", msg, now, nonce);
        // String sign = md5.digestHex("apply_matter=其他&apply_matter_desc=新时代文明实践志愿服务平台&apply_office=宣统部&apply_unit=宣统部&apply_username=王春阳&email=<EMAIL>&industry_sector=互联网应用及服务&info=[{\"adm_counterpart\":\"江苏风云科技服务有限公司\",\"uscc\":\"91320594551181055L\"}]&mobile_phone=13456789876" + now + nonce + "0aacb9c5ca941bc43a957282ed691498");
        String sign = md5.digestHex(msg + now + nonce + "0aacb9c5ca941bc43a957282ed691498", StandardCharsets.UTF_8);
        log.info("调用信用接口,加密后的sign:{}", sign);

        Map<String, String> headers = new HashMap<>();
        headers.put("appid", "buQDw12O");
        headers.put("username", "wangyuan01");
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("timestamp", now + "");
        headers.put("nonce", nonce);
        headers.put("use_platform_code", "0aacb9c5ca941bc43a957282ed691498");
        headers.put("sign", sign);
        String result = HttpUtils.sendPost("http://172.21.10.46/api/invoke/393382/xysc?timestamp=" + System.currentTimeMillis(), headers, new JSONObject(paramMap), "utf-8");
        log.info("调用信用接口result:{}", result);
        return result;
    }

    public static String getCreditInfoDetail(String creditCode, String corpName) {
        if (StringUtils.isBlank(creditCode) || StringUtils.isBlank(corpName)) {
            return null;
        }
        String creditInfo = getCreditInfo(creditCode, corpName);
        //String creditInfo = "{\"data\":{\"code\":0,\"data\":{\"companies\":[{\"tax_date\":\"2023年04月28日\",\"environmental_protection_date\":\"2023年08月13日\",\"environmental_protection\":\"蓝色等级\",\"uscc\":\"913205947558549871\",\"remark\":\"法人库中未发现失信信息\",\"tip\":\"园区法人库中未匹配到该企业信息\",\"adm_counterpart\":\"苏州工业园区蓝天燃气热电有限公司\",\"tax_grade\":\"B\"}],\"legal_person\":[],\"report_num\":\"CS052308160003\",\"download_address\":\"http://**********:8089/open/api/auditReport/queryReviewRow?reportNumber=CS052308160003\",\"apply_num\":\"XYSCSQ02023081600003\"},\"message\":\"success\"}}";
        JSONObject jsonCreditInfo = JSONObject.parseObject(creditInfo);
        JSONObject jsonData = jsonCreditInfo.getJSONObject("data");
        if (jsonData == null) {
            return null;
        }
        Integer code = jsonData.getInteger("code");
        if (!Objects.equals(0, code)) {
            return null;
        }
        Object data = jsonData.get("data");
        if (Objects.isNull(data)) {
            return null;
        }
        Map mapData = JSON.parseObject(data.toString(), Map.class);
        JSONArray companies = (JSONArray) mapData.get("companies");
        if (CollectionUtil.isNotEmpty(companies)) {
            return (String) companies.getObject(0, Map.class).get("remark");
        }
        return null;
    }

    /**
     * 信用取企业信息，评价纵览，合规性得分，发展能力得分，合规性得分子项百分比，发展
     * 能力得分子项百分比，评价总览信息
     *
     * @param creditCode
     * @param corpName
     * @return
     */
    public static String getIndexLevel(String creditCode, String corpName) {
        Map<String, Object> paramMap = new TreeMap<>();
        // 信息主体名称
        paramMap.put("adm_counterpart", corpName);
        // 统一社会信用代码
        paramMap.put("uscc", creditCode);
        // 数据用途
        paramMap.put("data_usage", "用于评审企业责任联盟企业入会资质");
        // 查询人员姓名
        paramMap.put("person_name", "许帆");
        // 查询人员职务
        paramMap.put("person_post", "");
        // 查询人员所属部门
        paramMap.put("person_organization_name", "宣统部");
        // 查询人员所属处室
        paramMap.put("person_office", "宣统部");
        // 查询人联系电话
        paramMap.put("person_telephone", "");
        paramMap.put("use_platform_code", "0aacb9c5ca941bc43a957282ed691498");

        MD5 md5 = new MD5();
        StringJoiner sg = new StringJoiner("&");
        long now = System.currentTimeMillis();
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            Object o = paramMap.get(entry.getKey());
            if (Objects.equals(entry.getKey(), "person_post") || Objects.equals(entry.getKey(), "person_telephone")) {
                continue;
            }
            if (Objects.equals(entry.getKey(), "info")) {
                sg.add(entry.getKey() + "=" + JSONObject.toJSONString(o));
            } else {
                sg.add(entry.getKey() + "=" + o);
            }
        }
        String msg = sg.toString();
        log.info("调用评分评级接口,msg:{},now:{}", msg, now);
        String sign = md5.digestHex(msg + now + "0aacb9c5ca941bc43a957282ed691498", StandardCharsets.UTF_8);
        log.info("调用评分评级接口,加密后的sign:{}", sign);

        Map<String, String> headers = new HashMap<>();
        headers.put("appid", "buQDw12O");
        headers.put("username", "wangyuan01");
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("timestamp", now + "");
        headers.put("use_platform_code", "0aacb9c5ca941bc43a957282ed691498");
        headers.put("sign", sign);
        String result = HttpUtils.sendPost("http://172.21.10.46/api/invoke/393382/qybjpf?timestamp=" + System.currentTimeMillis(), headers, new JSONObject(paramMap), "utf-8");
        //String result = HttpUtils.sendPost("http://credittest.szggjc.cn/mod/index/level", headers, new JSONObject(paramMap), CsrConstant.UTF8);
        log.info("调用评分评级接口result:{}", result);
        return result;
    }

    public static CreditIndexLevelVo getIndexLevelDetail(String creditCode, String corpName) {
        if (StringUtils.isBlank(creditCode) || StringUtils.isBlank(corpName)) {
            return null;
        }
        String indexLevel = getIndexLevel(creditCode, corpName);
        //String indexLevel = "{ \"data\": { \"data\": { \"mod_percent\": { \"senior_integrity_percent\": \"0%\", \"social_responsibility_percent\": \"0%\", \"public_opinion_percent\": \"0%\", \"business_integrity_percent\": \"0%\", \"social_integrity_percent\": \"0%\", \"business_ability_percent\": \"0%\", \"develop_capacity_percent\": \"0%\", \"judicial_integrity_percent\": \"0%\", \"industry_indicators_percent\": \"0%\", \"honor_recognition_percent\": \"0%\" }, \"indexes\": [ { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 1000, \"name\": \"失信被执行人\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"被执行人\", \"type\": \"ASSESS\" } ], \"max_score\": 1000, \"name\": \"司法诚信\", \"type\": \"ASSESS\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 1000, \"name\": \"被行业主管部门列入联合惩戒对象名单\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"特定严重行政处罚\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"严重危害人民群众身体健康和生命安全行为\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"严重破坏市场公平竞争秩序和社会正常秩序行为\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"一般行政处罚\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"行政强制执行\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"督查检查不合格等其他失信情况\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"异常经营\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"末级评价\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"信用承诺失信\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"欠税\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"社保公积金欠缴\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"其他不良行为\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"被行业主管部门列入联合惩戒对象名单\", \"type\": \"ASSESS\" } ], \"max_score\": 1000, \"name\": \"社会诚信\", \"type\": \"ASSESS\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 1000, \"name\": \"合同履约失信\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"商事服务失信\", \"type\": \"ASSESS\" } ], \"max_score\": 1000, \"name\": \"商务诚信\", \"type\": \"ASSESS\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 1000, \"name\": \"负面舆情\", \"type\": \"ASSESS\" } ], \"max_score\": 1000, \"name\": \"舆情监督\", \"type\": \"ASSESS\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 1000, \"name\": \"高层管理人员失信\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"法定代表人刑事判决\", \"type\": \"ASSESS\" }, { \"score\": 0, \"max_score\": 1000, \"name\": \"非独立法人分公司及分支机构信用\", \"type\": \"ASSESS\" } ], \"max_score\": 1000, \"name\": \"高管诚信\", \"type\": \"ASSESS\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 20, \"name\": \"资金规模\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"经营时长\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"业务拓展\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"市场活跃度\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"经营稳定性\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"用电、用水、用气\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"单位用工\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"缴费信息\", \"type\": \"ADJUST\" } ], \"max_score\": 140, \"name\": \"经营能力\", \"type\": \"ADJUST\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 30, \"name\": \"知识产权\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 40, \"name\": \"资质许可\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"项目投资\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"获得政府财政资金支持或政策性贷款支持\", \"type\": \"ADJUST\" } ], \"max_score\": 90, \"name\": \"发展能力\", \"type\": \"ADJUST\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 120, \"name\": \"政府部门给予的表彰奖励\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 80, \"name\": \"红名单\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 70, \"name\": \"企业信用管理贯标与示范\", \"type\": \"ADJUST\" } ], \"max_score\": 200, \"name\": \"荣誉表彰\", \"type\": \"ADJUST\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 40, \"name\": \"公益活动\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"助残扶贫\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 30, \"name\": \"信用承诺\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"电力需求响应\", \"type\": \"ADJUST\" } ], \"max_score\": 70, \"name\": \"社会责任\", \"type\": \"ADJUST\" }, { \"score\": 0, \"children\": [ { \"score\": 0, \"max_score\": 50, \"name\": \"行业评价\", \"type\": \"ADJUST\" }, { \"score\": 0, \"max_score\": 20, \"name\": \"信用评级\", \"type\": \"ADJUST\" } ], \"max_score\": 50, \"name\": \"行业指标\", \"type\": \"ADJUST\" } ], \"company_score\": { \"score\": \"825.0\", \"assess_score\": 1000, \"adjust_score\": 310, \"rating_level_name\": \"A-\" }, \"company\": { \"end_date\": null, \"legal_rep_cert_num\": null, \"capital\": \"*********.000000\", \"approval_date\": null, \"address\": \"苏州工业园区苏桐路55号\", \"issuing_date\": null, \"industry_code\": null, \"uscc\": \"913205947558549871\", \"legal_rep\": null, \"institution_name\": null, \"tags\": [ \"规上企业-工业\", \"纳税信用等级评定A\", \"劳动保障信用等级评定AAAAA\" ], \"business_scope\": null, \"company_type_code\": null, \"currency\": null, \"business_reg_auth_code\": null, \"start_date\": null, \"status\": \"01\" } }, \"message\": \"success\", \"status\": 0 } }";
        JSONObject jsonObject = JSONObject.parseObject(indexLevel);
        JSONObject jsonData = jsonObject.getJSONObject("data");
        if (jsonData == null) {
            return null;
        }
        Integer code = jsonData.getInteger("status");
        if (!Objects.equals(0, code)) {
            return null;
        }
        Object data = jsonData.get("data");
        if (Objects.isNull(data)) {
            return null;
        }
        Map mapData = JSON.parseObject(data.toString(), Map.class);
        JSONObject companyScore = (JSONObject) mapData.get("company_score");
        CreditIndexLevelVo vo = new CreditIndexLevelVo();
        if (companyScore != null) {
            String score = companyScore.getString("score");
            String ratingLevelName = companyScore.getString("rating_level_name");
            vo.setScore(score);
            vo.setRatingLevelName(ratingLevelName);
        }
        return vo;
    }

    public static void main(String[] args) {
        //System.out.println(getIndexLevelDetail("913205947558549871", "江苏风云科技服务有限公司"));
    }
}

