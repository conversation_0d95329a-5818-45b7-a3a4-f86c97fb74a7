package com.fykj.scaffold.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.param.MsgRecordParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  消息记录mapper
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface MsgRecordMapper extends BaseMapper<MsgRecord> {

    /**
     * 分页查询消息记录
     * @param page
     * @param params
     * @return
     */
    IPage<MsgRecord> listOrPage(IPage<MsgRecord> page, @Param("params") MsgRecordParams params);

    /**
     * 分页查询消息记录
     * @param params
     * @return
     */
    List<MsgRecord> listOrPage(@Param("params") MsgRecordParams params);

    /**
     * 删除尚未发送的明细
     * @param sendId
     */
    void removeNotSendRecord(@Param("sendId") Long sendId);

    /**
     * 维护页分页查询
     * @param page
     * @param params
     * @return
     */
    IPage<MsgRecord> pageForMaintain(IPage<MsgRecord> page, @Param("params") MsgRecordParams params);
}
