package com.fykj.scaffold.mall.domain.params;

import fykj.microservice.core.base.BaseParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 积分商城-库存变动
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MallStockNumChangeParams extends BaseParams {
    private Long goodsId;
}
