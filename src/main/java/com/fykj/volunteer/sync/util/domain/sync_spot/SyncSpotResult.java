package com.fykj.volunteer.sync.util.domain.sync_spot;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <ps>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>同步失败：实践点所属区划不存在</msg>
 *     <uniCode>1001</uniCode>
 *   </info>
 * </ps>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncSpotResult {
    /**
     * 实践阵地编号
     */
    private String psId;
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "ps")
    public static class Ps {
        private List<SyncSpotResult> info;
    }
}
