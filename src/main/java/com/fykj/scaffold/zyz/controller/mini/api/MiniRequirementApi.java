package com.fykj.scaffold.zyz.controller.mini.api;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementParams;
import com.fykj.scaffold.zyz.service.IZyzRequirementService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.domain.params.ActivityRequirementParams;
import com.fykj.scaffold.zyz.domain.dto.ActivityByRequirementDto;
import com.fykj.scaffold.zyz.domain.params.ResourceRequirementParams;
import com.fykj.scaffold.zyz.domain.dto.ResourceByRequirementDto;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import utils.StringUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import javax.validation.Valid;

@Slf4j
@Validated
@RestController
@RequestMapping("/api/mini/requirement")
@Api(tags = "微信小程序需求接口--无需登录")
public class MiniRequirementApi {

    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzSyncBelongFieldDictService belongFieldDictService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzActivityService activityService;

    /**
     * 1.获取上架的需求 -- 分页查询
     * 3.获取详情
     */

    @ApiOperation("获取需求列表--分页查询")
    @PostMapping("/getPagesForMini")
    public JsonResult<IPage<ZyzRequirement>> getPagesForMini(@RequestBody ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        //获取上架的需求
        params.setAutoStatus(true);
        IPage<ZyzRequirement> iPage = requirementService.getPagesForMini(params);
        //姓名脱敏
        for (ZyzRequirement record : iPage.getRecords()) {
            if(StringUtil.isNotEmpty(record.getContactPerson())) {
                record.setContactPerson(StrUtil.hide(record.getContactPerson(), 1, 4));
            }
        }
        DictTransUtil.trans(iPage.getRecords());
        return new JsonResult<>(iPage);
    }


    @ApiOperation("获取需求详情")
    @GetMapping("/getRequirementById")
    public Result getRequirementById(@RequestParam Long id) {
        ZyzRequirement requirement = requirementService.getById(id);
        DictTransUtil.trans(requirement);
        return new JsonResult<>(requirement);
    }

    @ApiOperation("获取所属领域")
    @GetMapping("/getBelongFieldList")
    public Result getBelongFieldList() {
        return new JsonResult<>(belongFieldDictService.cascade());
    }

    @ApiOperation("根据需求ID分页查询活动")
    @GetMapping("/pageActivitiesByRequirementId")
    public JsonResult<IPage<ActivityByRequirementDto>> pageActivitiesByRequirementId(
            @Valid ActivityRequirementParams params) {
        IPage<ActivityByRequirementDto> page = activityService.pageByRequirementId(params);
        return new JsonResult<>(page);
    }

    @ApiOperation("根据需求ID分页查询关联资源")
    @GetMapping("/pageResourcesByRequirementId")
    public JsonResult<IPage<ResourceByRequirementDto>> pageResourcesByRequirementId(
            @Valid ResourceRequirementParams params) {
        IPage<ResourceByRequirementDto> page = requirementService.pageResourcesByRequirementId(params);
        return new JsonResult<>(page);
    }

}
