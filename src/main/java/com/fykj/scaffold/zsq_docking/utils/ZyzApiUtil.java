package com.fykj.scaffold.zsq_docking.utils;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import exception.BusinessException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.io.IOException;

/**
 * 志愿者接口调用类
 */
@UtilityClass
@Slf4j
public class ZyzApiUtil {


    /**
     * POST请求
     *
     * @param body 请求参数
     * @return 返回结果
     */
    public static Result post(String url, Object body) {
        String result = executePost(url, body);
        return JSONUtil.toBean(result, JsonResult.class);
    }

    /**
     * post执行请求
     *
     * @param body 请求参数
     * @return 返回结果
     */
    public static String executePost(String url, Object body) {
        try {
            RequestBody requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), JSON.toJSON(body).toString());
            Request request = new Request.Builder()
                    .headers(AuthHeaderUtil.authHeader())
                    .post(requestBody)
                    .url(url).build();
            Response response = new OkHttpClient().newCall(request).execute();
            String json = response.body().string();
            JSONObject obj = JSON.parseObject(json);
            if (!obj.getBoolean("success")) {
                throw new BusinessException(ResultCode.FAIL, obj.getString("msg"));
            }
            return obj.getString("obj");
        } catch (IOException e) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "系统错误，请稍侯再试:", e);
        }
    }


    /**
     * POST请求
     *
     * @param body 请求参数
     * @return 返回结果
     */
    public static JsonResult get(String url, Object body) {
        url = String.format(url, body);
        return (JsonResult) execute(url);
    }

    /**
     * GET执行请求
     *
     * @return 返回结果
     */
    public static Result execute(String url) {
        try {
            Request request = new Request.Builder()
                    .headers(AuthHeaderUtil.authHeader())
                    .get()
                    .url(url).build();
            Response response = new OkHttpClient().newCall(request).execute();
            String json = response.body().string();
            JsonResult result = JSONUtil.toBean(json, JsonResult.class);;
            if (!result.isSuccess()) {
                throw new BusinessException(ResultCode.FAIL, result.getMsg());
            }
            return result;
        } catch (IOException e) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "系统错误，请稍侯再试:", e);
        }
    }


    public static void main(String[] args) {

        String url = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/news/syncYjSaveNews?code=%s";
        Result result = get(url, "gender");
        log.info("result:{}", result);

    }

}
