package com.fykj.scaffold.dynamic.service;

import com.fykj.scaffold.dynamic.domain.entity.DynamicField;
import fykj.microservice.core.base.IBaseService;

/**
 * 字段信息-服务类
 *
 * 提供动态字段的增删改查、唯一性校验等服务接口。
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
public interface IDynamicFieldService extends IBaseService<DynamicField> {

    /**
     * 新增动态字段
     * @param entity 字段实体
     * @return 是否成功
     */
    boolean add(DynamicField entity);

    /**
     * 编辑动态字段
     * @param newEntity 新字段实体
     * @return 是否成功
     */
    boolean edit(DynamicField newEntity);

    /**
     * 删除动态字段
     * @param id 字段ID
     * @return 是否成功
     */
    boolean remove(Long id);

    /**
     * 校验字段名是否已存在
     * @param fieldName 字段名
     */
    void validateFieldNameExist(String fieldName);
}

