package com.fykj.scaffold.sgb_docking.domain.enums;

import lombok.Getter;

/**
 * 苏州志愿者平台字典类型枚举
 */
@Getter
public enum SgbDictTypeEnum {

    /**
     * 政治面貌
     */
    POLITICS_STATUS("politicsstatus", "政治面貌"),
    
    /**
     * 身份属性
     */
    VOL_IDENTITY("vol_identity", "身份属性"),
    
    /**
     * 专业特长
     */
    VOL_SKILL("vol_skill", "专业特长"),
    
    /**
     * 民族
     */
    NATION("nation", "民族"),
    
    /**
     * 文化程度
     */
    EDUCATION("education", "文化程度"),
    
    /**
     * 阵地类型
     */
    POSITION_TYPE("position_type", "阵地类型"),
    
    /**
     * 服务对象
     */
    SERVICE_TARGET("service_target", "服务对象"),
    
    /**
     * 团队类型
     */
    TEAM_CLASSIFY("team_classify", "团队类型"),
    
    /**
     * 培训对象
     */
    TRAIN_TARGET("train_target", "培训对象"),
    
    /**
     * 活动形式
     */
    ACTP_FORM("actp_form", "活动形式"),
    
    /**
     * 个人特长
     */
    SKILL("skill", "个人特长");
    
    private final String code;
    private final String name;
    
    SgbDictTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
} 