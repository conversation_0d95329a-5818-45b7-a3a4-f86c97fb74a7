package com.fykj.scaffold.task.domain.params;

import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CopyActivityParam {
    private String dates;
    private String times;
    private String groupTimes;
    private Integer activityNums;
    private Integer maxVolunteerNums;
    private Integer minVolunteerNums;
    private String customFields;
    List<ZyzVolunteer> volunteers;
    List<String> activityNames;
}
