package com.fykj.scaffold.zsq_docking.log.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.zsq_docking.cons.ActivityDataDockingTypeEnum;
import com.fykj.scaffold.zsq_docking.cons.NewsDataDockingTypeEnum;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingLog;
import com.fykj.scaffold.zsq_docking.log.mapper.ZSQDockingLogMapper;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingLogService;
import com.fykj.scaffold.zsq_docking.push_mq.event.*;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ActivityDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.NewsDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQActivityInfoDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import result.JsonResult;
import result.Result;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;

import static cn.hutool.core.text.StrPool.LF;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;


/**
 * 知社区对接推送数据信息表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2025-04-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ZSQDockingLogServiceImpl extends BaseServiceImpl<ZSQDockingLogMapper, ZSQDockingLog> implements IZSQDockingLogService {


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveNewsReceiveLog(NewsDockingDto dockingInfo, Result result, ZSQNewsInfoDto news, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_RECEIVE);
        String operation = dockingInfo.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        Long newsId = dockingInfo.getNewsId();
        dockingLog.setDockingDataId(newsId);
        String zwtUserId = dockingInfo.getZwtUserId();
        boolean success = result.isSuccess();
        String msg = result.getMsg();
        int code = result.getCode();
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("新闻 id", newsId == null ? "未知" : newsId);
        summaryObj.put("对接类型", "接收数据");
        summaryObj.put("知社区操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : NewsDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "接收并同步处理成功！" :"接收（或后续处理）异常！");
        String resStr = null;
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(result.getMsg());
        }
        String requestData;
        if (news == null) {
            requestData = JSON.toJSONString(dockingInfo);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DOCKING_INFO, dockingInfo);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, news);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        if (StringUtils.isNotEmpty(resStr)) {
            JSONObject resObj = new JSONObject(new LinkedHashMap<>());
            resObj.put("code", code);
            resObj.put("success", false);
            resObj.put("msg", msg.substring(0, msg.indexOf("【")));
            resObj.put("zsq_detail_res", JSONObject.parseObject(resStr));
            dockingLog.setDockingResponseData(resObj.toJSONString());
            super.save(dockingLog);
            return;
        }
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveNewsPushLog(NewsDockingEvent notice, Object result, Object news, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_PUSH);
        dockingLog.setDockingPushId(notice.getMsgId());
        String operation = notice.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        Long newsId = notice.getNewsId();
        dockingLog.setDockingDataId(newsId);
        String zwtUserId = notice.getZwtUserId();
        boolean success;
        String msg;
        int code;
        if (result instanceof JsonResult) {
            success = ((JsonResult<?>) result).isSuccess();
            msg = ((JsonResult<?>) result).getMsg();
            code = ((JsonResult<?>) result).getCode();
        } else {
            success = ((Result)result).isSuccess();
            msg = ((Result)result).getMsg();
            code = ((Result)result).getCode();
        }
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("新闻 id", newsId == null ? "未知" : newsId);
        summaryObj.put("对接类型", "推送数据");
        summaryObj.put("操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : NewsDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "通知成功，并且知社区数据处理成功！" :"知社区响应异常！");
        String resStr = null;
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(msg);
        }
        String requestData;
        if (news == null) {
            requestData = JSON.toJSONString(notice);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO, notice);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, news);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        if (StringUtils.isNotEmpty(resStr)) {
            JSONObject resObj = new JSONObject(new LinkedHashMap<>());
            resObj.put("code", code);
            resObj.put("success", false);
            resObj.put("msg", msg.substring(0, msg.indexOf("【")));
            resObj.put("zsq_res", JSONObject.parseObject(resStr));
            dockingLog.setDockingResponseData(resObj.toJSONString());
            super.save(dockingLog);
            return;
        }
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveActivityReceiveLog(ActivityDockingDto dockingInfo, Result result, ZSQActivityInfoDto activity, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_RECEIVE);
        String operation = dockingInfo.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        Long actId = dockingInfo.getActId();
        dockingLog.setDockingDataId(actId);
        String zwtUserId = dockingInfo.getZwtUserId();
        boolean success = result.isSuccess();
        String msg = result.getMsg();
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("活动 id", actId == null ? "未知" : actId);
        summaryObj.put("对接类型", "接收数据");
        summaryObj.put("知社区操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : NewsDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "接收并同步处理成功！" :"接收（或后续处理）异常！");
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                String resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(result.getMsg());
        }
        String requestData;
        if (activity == null) {
            requestData = JSON.toJSONString(dockingInfo);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DOCKING_INFO, dockingInfo);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, activity);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveActivityPushLog(ActivityDockingEvent notice, Object result, Object activity, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_PUSH);
        dockingLog.setDockingPushId(notice.getMsgId());
        String operation = notice.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        Long actId = notice.getActId();
        dockingLog.setDockingDataId(actId);
        String zwtUserId = notice.getZwtUserId();
        boolean success;
        String msg;
        if (result instanceof JsonResult) {
            success = ((JsonResult<?>) result).isSuccess();
            msg = ((JsonResult<?>) result).getMsg();
        } else {
            success = ((Result)result).isSuccess();
            msg = ((Result)result).getMsg();
        }
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("活动 id", actId == null ? "未知" : actId);
        summaryObj.put("对接类型", "推送数据");
        summaryObj.put("操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : ActivityDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "通知成功，并且知社区数据处理成功！" :"知社区响应异常！");
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                String resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(msg);
        }
        String requestData;
        if (activity == null) {
            requestData = JSON.toJSONString(notice);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO, notice);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, activity);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }



    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveRequirementPushLog(RequirementDockingEvent notice, Object result, Object requirement, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_PUSH);
        dockingLog.setDockingPushId(notice.getMsgId());
        String operation = notice.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        Long regId = notice.getRequirementId();
        dockingLog.setDockingDataId(regId);
        String zwtUserId = notice.getZwtUserId();
        boolean success;
        String msg;
        if (result instanceof JsonResult) {
            success = ((JsonResult<?>) result).isSuccess();
            msg = ((JsonResult<?>) result).getMsg();
        } else {
            success = ((Result)result).isSuccess();
            msg = ((Result)result).getMsg();
        }
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("需求 id", regId == null ? "未知" : regId);
        summaryObj.put("对接类型", "推送数据");
        summaryObj.put("操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : ActivityDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "通知成功，并且知社区数据处理成功！" :"知社区响应异常！");
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                String resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(msg);
        }
        String requestData;
        if (requirement == null) {
            requestData = JSON.toJSONString(notice);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO, notice);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, requirement);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveResourcePushLog(ResourceDockingEvent notice, Object result, Object resource, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_PUSH);
        dockingLog.setDockingPushId(notice.getMsgId());
        String operation = notice.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        Long resId = notice.getResourceId();
        dockingLog.setDockingDataId(resId);
        String zwtUserId = notice.getZwtUserId();
        boolean success;
        String msg;
        if (result instanceof JsonResult) {
            success = ((JsonResult<?>) result).isSuccess();
            msg = ((JsonResult<?>) result).getMsg();
        } else {
            success = ((Result)result).isSuccess();
            msg = ((Result)result).getMsg();
        }
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("资源 id", resId == null ? "未知" : resId);
        summaryObj.put("对接类型", "推送数据");
        summaryObj.put("操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : ActivityDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "通知成功，并且知社区数据处理成功！" :"知社区响应异常！");
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                String resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(msg);
        }
        String requestData;
        if (resource == null) {
            requestData = JSON.toJSONString(notice);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO, notice);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, resource);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveBelongFieldPushLog(BelongFieldDockingEvent notice, Object result, Object belongField, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_PUSH);
        dockingLog.setDockingPushId(notice.getMsgId());
        String operation = notice.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        String zwtUserId = notice.getZwtUserId();
        boolean success;
        String msg;
        if (result instanceof JsonResult) {
            success = ((JsonResult<?>) result).isSuccess();
            msg = ((JsonResult<?>) result).getMsg();
        } else {
            success = ((Result)result).isSuccess();
            msg = ((Result)result).getMsg();
        }
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("对接类型", "推送数据");
        summaryObj.put("操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : ActivityDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "通知成功，并且知社区数据处理成功！" :"知社区响应异常！");
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                String resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(msg);
        }
        String requestData;
        if (belongField == null) {
            requestData = JSON.toJSONString(notice);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO, notice);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, belongField);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveRegionAllPushLog(RegionAllDockingEvent notice, Object result, Object regionAll, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_PUSH);
        dockingLog.setDockingPushId(notice.getMsgId());
        String operation = notice.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        String zwtUserId = notice.getZwtUserId();
        boolean success;
        String msg;
        if (result instanceof JsonResult) {
            success = ((JsonResult<?>) result).isSuccess();
            msg = ((JsonResult<?>) result).getMsg();
        } else {
            success = ((Result)result).isSuccess();
            msg = ((Result)result).getMsg();
        }
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("对接类型", "推送数据");
        summaryObj.put("操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : ActivityDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "通知成功，并且知社区数据处理成功！" :"知社区响应异常！");
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                String resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(msg);
        }
        String requestData;
        if (regionAll == null) {
            requestData = JSON.toJSONString(notice);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO, notice);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, regionAll);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveSysOrgPushLog(SysOrgDockingEvent notice, Object result, Object sysOrg, Long dockingRecordId) {
        ZSQDockingLog dockingLog = new ZSQDockingLog();
        dockingLog.setDockingRecordId(dockingRecordId);
        dockingLog.setDockingType(DOCKING_TYPE_PUSH);
        dockingLog.setDockingPushId(notice.getMsgId());
        String operation = notice.getOperation();
        dockingLog.setDockingDataType(StringUtils.isEmpty(operation) ? "未知操作" : operation);
        Long orgId = notice.getOrgId();
        dockingLog.setDockingDataId(orgId);
        String zwtUserId = notice.getZwtUserId();
        boolean success;
        String msg;
        if (result instanceof JsonResult) {
            success = ((JsonResult<?>) result).isSuccess();
            msg = ((JsonResult<?>) result).getMsg();
        } else {
            success = ((Result)result).isSuccess();
            msg = ((Result)result).getMsg();
        }
        JSONObject summaryObj = new JSONObject(new LinkedHashMap<>());
        summaryObj.put("组织 id", orgId == null ? "未知" : orgId);
        summaryObj.put("对接类型", "推送数据");
        summaryObj.put("操作者（政务通 userid）", StringUtils.isEmpty(zwtUserId) ? "未知" : zwtUserId);
        summaryObj.put("操作类型", StringUtils.isEmpty(operation) ? "未知操作" : ActivityDataDockingTypeEnum.fromCode(operation).getText());
        summaryObj.put("对接结果", success ? "通知成功，并且知社区数据处理成功！" :"知社区响应异常！");
        if (!success) {
            if (msg.contains("【") && msg.contains("】")) {
                String resStr = msg.substring(msg.indexOf("【") + 1, msg.lastIndexOf("】"));
                try {
                    JSONObject resObj = JSONObject.parseObject(resStr);
                    summaryObj.put("异常响应", resObj);
                } catch (Exception e) {
                    log.error("对接日志异常响应数据转换异常！", e);
                    summaryObj.put("异常响应", resStr);
                }
            } else {
                summaryObj.put("异常原因", msg);
            }
        } else {
            if (StringUtils.isNotEmpty(msg)) {
                summaryObj.put("成功备注", msg);
            }
        }
        dockingLog.setDockingSummary(summaryObj.toJSONString());
        dockingLog.setDockingTime(LocalDateTime.now());
        dockingLog.setDockingResult(success);
        if (!success) {
            dockingLog.setDockingFailMsg(msg);
        }
        String requestData;
        if (sysOrg == null) {
            requestData = JSON.toJSONString(notice);
        } else {
            JSONObject object = new JSONObject(new LinkedHashMap<>());
            object.put(ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO, notice);
            object.put(ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA, sysOrg);
            requestData = object.toJSONString();
        }
        dockingLog.setDockingRequestData(requestData);
        dockingLog.setDockingResponseData(JSON.toJSONString(result));
        super.save(dockingLog);
    }
}
