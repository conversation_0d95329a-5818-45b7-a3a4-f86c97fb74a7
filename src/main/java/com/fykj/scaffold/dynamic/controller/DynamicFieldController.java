package com.fykj.scaffold.dynamic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.dynamic.domain.entity.DynamicField;
import com.fykj.scaffold.dynamic.domain.params.DynamicFieldParams;
import com.fykj.scaffold.dynamic.service.IDynamicFieldService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 字段信息-接口控制器
 *
 * @date 2025-05-19
 */
@RestController
@RequestMapping("/admin/dynamic/field")
@Api(tags = "动态表单-字段信息-管理接口")
public class DynamicFieldController
        extends BaseController<IDynamicFieldService, DynamicField, DynamicFieldParams> {

    @SysLogMethod("新增字段")
    @ApiOperation("新增字段")
    @PostMapping("/add")
    public Result add(@RequestBody @Validated DynamicField entity) {
        return baseService.add(entity) ? OK : new Result(result.ResultCode.FAIL);
    }

    @SysLogMethod("编辑字段")
    @ApiOperation("编辑字段")
    @PostMapping("/edit")
    public Result edit(@RequestBody @Validated DynamicField entity) {
        return baseService.edit(entity) ? OK : new Result(result.ResultCode.FAIL);
    }

    @SysLogMethod("删除字段")
    @ApiOperation("删除字段")
    @GetMapping("/remove")
    public Result remove(@RequestParam @NotNull(message = "字段ID不能为空") Long id) {
        return baseService.remove(id) ? OK : new Result(result.ResultCode.FAIL);
    }

    @ApiOperation("校验字段名是否已存在")
    @GetMapping("/validate-field-name")
    public Result validateFieldNameExist(@RequestParam @NotBlank(message = "字段名不能为空") String fieldName) {
        baseService.validateFieldNameExist(fieldName);
        return OK;
    }

    @ApiOperation("分页查询")
    @PostMapping({"/page"})
    public JsonResult<IPage<DynamicField>> list(@RequestBody DynamicFieldParams params) {
        IPage<DynamicField> result = baseService.page(params);
        DictTransUtil.trans(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("获取全部方法")
    @PostMapping({"/list"})
    public JsonResult<List<DynamicField>> all(@RequestBody DynamicFieldParams params) {
        List<DynamicField> result = baseService.list(params);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }
}
