package com.fykj.scaffold.capability.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.capability.domain.dto.*;
import com.fykj.scaffold.capability.domain.params.CourseParams;
import com.fykj.scaffold.capability.domain.params.CreditsRecordParams;
import com.fykj.scaffold.capability.service.ICourseService;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.Oauth2Util;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import javax.validation.Valid;
import java.util.List;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Validated
@RestController
@RequestMapping("/admin/customer/capability/course")
@Api(tags = "课程管理接口-用户端")
public class CourseCustomController {

    @Autowired
    private ICourseService baseService;

    @ApiOperation("能力提升-列表")
    @PostMapping("/list")
    public JsonResult<List<CourseListDTO>> listCourse(@RequestBody CourseParams params) {
        // 已结束的放出来
        // params.setOverStatus(false);
        params.setOuterDisplay(true);
        List<CourseListDTO> list = baseService.listCourse(params);
        return new JsonResult<>(list);
    }

    @ApiOperation("课程详情")
    @GetMapping("/detail")
    public JsonResult<CourseDetailDTO> detailCourseMine(@RequestParam Long id) {
        CourseDetailDTO detail = baseService.detailCourseMine(id);
        return new JsonResult<>(detail);
    }

    @ApiOperation("课程列表-分页查询-我的")
    @PostMapping("/page/mine")
    public JsonResult<IPage<CourseListDTO>> pageMyCourse(@RequestBody CourseParams params) {
        params.setUserId((Long) Oauth2Util.getUserId());
        IPage<CourseListDTO> list = baseService.pageMyCourse(params);
        return new JsonResult<>(list);
    }

    @ApiOperation("课程列表-列表查询-我的")
    @PostMapping("/list/mine")
    public JsonResult<List<CourseListDTO>> listMyCourse(@RequestBody CourseParams params) {
        params.setUserId((Long) Oauth2Util.getUserId());
        List<CourseListDTO> list = baseService.listMyCourse(params);
        return new JsonResult<>(list);
    }

    @AuditLog("课程报名")
    @ApiOperation("报名课程")
    @PostMapping("/sign-up")
    public Result signUp(@RequestBody @Valid CourseUserSaveDTO dto) {
        baseService.signUp(dto);
        return OK;
    }

    @AuditLog("取消课程报名(本人")
    @ApiOperation("取消报名")
    @PostMapping("/sign-up/cancel")
    public Result cancelSignUpAll(@RequestBody @Valid CourseUserSaveDTO dto) {
        baseService.cancelSignUp(dto.getCourseId());
        return OK;
    }

    @AuditLog("课程签到")
    @ApiOperation("课程签到")
    @PostMapping("/sign-in")
    public Result signIn(@RequestBody @Valid CourseSignInSaveDTO dto) {
        baseService.signIn(dto);
        return OK;
    }

    @ApiOperation("学分记录-分页列表-我的")
    @PostMapping("/credits/page/mine")
    public JsonResult<IPage<CreditsRecordsDTO>> pageCreditsRecords(
            @RequestBody CreditsRecordParams params) {
        params.setUserId((Long) Oauth2Util.getUserId());
        IPage<CreditsRecordsDTO> list = baseService.pageCreditsRecords(params);
        return new JsonResult<>(list);
    }

    @ApiOperation("学分记录-全部列表-我的")
    @PostMapping("/credits/list/mine")
    public JsonResult<List<CreditsRecordsDTO>> listCreditsRecords(
            @RequestBody CreditsRecordParams params) {
        params.setUserId((Long) Oauth2Util.getUserId());
        List<CreditsRecordsDTO> list = baseService.listCreditsRecords(params);
        return new JsonResult<>(list);
    }

    @ApiOperation("学分总数-我的")
    @GetMapping("/credits/total/mine")
    public JsonResult<UserCreditsDetailDTO> detailCourseMine() {
        UserCreditsDetailDTO detail = baseService.getUserCredits((Long) Oauth2Util.getUserId());
        return new JsonResult<>(detail);
    }

}
