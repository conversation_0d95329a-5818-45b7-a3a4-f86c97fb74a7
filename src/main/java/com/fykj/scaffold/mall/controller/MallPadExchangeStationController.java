package com.fykj.scaffold.mall.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation;
import com.fykj.scaffold.mall.domain.params.MallPadExchangeStationParams;
import com.fykj.scaffold.mall.service.IMallPadExchangeStationService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 *
 *
 * 前端控制器
 * <AUTHOR>
 * @date 2023-11-27
 */
@RestController
@RequestMapping("/admin/mall/pad-exchange-station")
public class MallPadExchangeStationController extends BaseController<IMallPadExchangeStationService, MallPadExchangeStation, MallPadExchangeStationParams> {

    @ApiOperation("pad兑换站点分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallPadExchangeStation>> getPages(@RequestBody MallPadExchangeStationParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @ApiOperation("获取当前登录人组织及下级组织下的pad兑换站点")
    @GetMapping("/getStationsByCurrOrg")
    public JsonResult<List<MallPadExchangeStation>> getStationsByCurrOrg() {
        return new JsonResult<>(baseService.getStationsByCurrOrg());
    }

    @ApiOperation("启用/禁用")
    @GetMapping({"/statusChange"})
    public Result statusChange(@RequestParam String id) {
        baseService.statusChange(id);
        return new Result(ResultCode.OK);
    }
}
