package com.fykj.scaffold.zyz.controller.api;


import com.fykj.scaffold.security.business.domain.dto.OrgMapDto;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.zyz.domain.dto.HomePagePracticeRankDto;
import com.fykj.scaffold.zyz.domain.dto.SpiritCockpitDto;
import com.fykj.scaffold.zyz.domain.dto.SpiritMonActDto;
import com.fykj.scaffold.zyz.domain.dto.SpiritOrgDetailDto;
import com.fykj.scaffold.zyz.domain.dto.data_screen.ReqResActTeamDataDto;
import com.fykj.scaffold.zyz.service.ICockpitCivilizationService;
import com.fykj.scaffold.zyz.service.IDataScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 项目表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/api/civilization/cockpit")
@Api(tags = "精神文明建设（文明实践）-驾驶舱接口")
public class CockpitCivilizationApi {

    @Autowired
    private ICockpitCivilizationService cockpitCivilizationService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IDataScreenService dataScreenService;

    @ApiOperation("概览")
    @GetMapping("/spiritOverview")
    public JsonResult<SpiritCockpitDto> spiritOverview() {
        return new JsonResult<>(cockpitCivilizationService.spiritOverview());
    }
    @ApiOperation("近6个月实践活动情况")
    @GetMapping("/sixMonthActOverview")
    public JsonResult<List<SpiritMonActDto>> sixMonthActOverview() {
        return new JsonResult<>(cockpitCivilizationService.sixMonthActOverview());
    }
    @ApiOperation("精神文明建设（文明实践）组织列表")
    @GetMapping("/orgList")
    public JsonResult<List<OrgMapDto>> orgList() {
        return new JsonResult<>(cockpitCivilizationService.orgList());
    }
    @ApiOperation("精神文明组织详情")
    @GetMapping("/orgSpiritDetail")
    public JsonResult<SpiritOrgDetailDto> orgSpiritDetail(@RequestParam String orgCode) {
        return new JsonResult<>(cockpitCivilizationService.orgDetail(orgCode));
    }

    @ApiOperation("文明实践组织详情")
    @GetMapping("/getByOrgId")
    public JsonResult<SysOrg> getByOrgId(@RequestParam Long orgId) {
        return new JsonResult<>(orgService.getById(orgId));
    }

    @ApiOperation("阵地活跃情况")
    @GetMapping("/rank")
    public JsonResult<List<HomePagePracticeRankDto>> rank() {
        return new JsonResult<>(cockpitCivilizationService.rank());
    }

    @ApiOperation("组织下需求/资源/活动/团队列表")
    @GetMapping("/get_req_res_act_team_list")
    public JsonResult<List<ReqResActTeamDataDto>> getReqResActTeamList(@ApiParam(name = "orgCode", value = "组织code", required = true) @RequestParam String orgCode,
                                                                       @ApiParam(name = "dimension", value = "维度:\n需求(req)/资源(res)/活动(act)/团队(team)", required = true) @RequestParam String dimension) {
        return new JsonResult<>(dataScreenService.getReqResActTeamList(orgCode, dimension, "total"));
    }
}
