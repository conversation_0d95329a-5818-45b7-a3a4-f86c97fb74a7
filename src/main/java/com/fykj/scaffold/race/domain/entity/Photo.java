package com.fykj.scaffold.race.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.race.domain.dto.PictureDto;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Desensitise;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 摄影大赛-作品表
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "race_photo", autoResultMap = true)
public class Photo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 作品名
     */
    @TableField("name")
    @ApiModelProperty(value = "作品名")
    private String name;

    /**
     * 拍摄地点
     */
    @TableField("place")
    @ApiModelProperty(value = "拍摄地点")
    private String place;

    /**
     * 审核状态
     */
    @TableField("audit_status")
    @DictTrans(transTo = "auditStatusText")
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @TableField(exist = false)
    @ApiModelProperty(value = "审核状态")
    private String auditStatusText;

    /**
     * 图片列表
     */
    @TableField(value = "picture_list", typeHandler = PictureDto.pictureListTypeHandler.class)
    @ApiModelProperty(value = "图片列表")
    private List<PictureDto> pictureList = new ArrayList<>();


    /**
     * 编号
     */
    @TableField("number")
    @ApiModelProperty(value = "编号")
    private Integer number;


    /**
     * 编号
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "编号")
    private String numberStr;

    /**
     * 选手id
     */
    @TableField("user_id")
    @ApiModelProperty(value = "选手id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 作者
     */
    @TableField("author")
    @ApiModelProperty(value = "作者")
    private String author;

    /**
     * 作者联系方式
     */
    @TableField("author_phone")
    @Desensitise
    @ApiModelProperty(value = "作者联系方式")
    private String authorPhone;


    /**
     * 活动id
     */
    @TableField("activity_id")
    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 作品描述
     */
    @TableField("description")
    @ApiModelProperty(value = "作品描述")
    private String description;

//    /**
//     * 投稿类型
//     */
//    @TableField("contribute_type")
//    @DictTrans(transTo = "contributeTypeText")
//    @ApiModelProperty(value = "投稿类型")
//    private String contributeType;
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "投稿类型")
//    private String contributeTypeText;

    /**
     * 审核备注
     */
    @TableField(value = "audit_remark", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "审核备注")
    private String auditRemark;

    /**
     * 投稿类别
     */
    @TableField("category")
    @ApiModelProperty(value = "投稿类别")
    @DictTrans(transTo = "categoryText")
    private String category;

    /**
     * 投稿类别
     */
    @TableField(exist=false)
    @ApiModelProperty(value = "投稿类别")
    private String categoryText;



    /**
     * 拍摄日期
     */
    @TableField("record_date")
    @ApiModelProperty(value = "拍摄日期")
    @JsonFormat(pattern = Cons.DATE_FORMAT)
    private LocalDate recordDate;
}
