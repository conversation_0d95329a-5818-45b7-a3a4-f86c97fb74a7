package com.fykj.scaffold.sgb_docking.controller;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbDict;
import com.fykj.scaffold.sgb_docking.domain.params.SgbDictParams;
import com.fykj.scaffold.sgb_docking.service.ISgbDictService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.beans.vo.IdTextVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 苏州志愿者平台字典数据控制器
 */
@Api(tags = "苏州志愿者平台-字典数据")
@RestController
@RequestMapping("/admin/sgb/dict")
public class SgbDictController extends BaseController<ISgbDictService, SgbDict, SgbDictParams> {
    
    /**
     * 获取所有字典类型
     * 
     * @return 字典类型列表，使用IdTextVo格式
     */
    @ApiOperation("获取所有字典类型")
    @GetMapping("/types")
    public JsonResult<List<IdTextVo>> getAllDictTypes() {
        List<IdTextVo> dictTypes = baseService.getAllDictTypes();
        return new JsonResult<>(dictTypes);
    }
    
    /**
     * 根据字典类型编码获取字典列表
     *
     * @param type 字典类型编码
     * @return 字典列表，使用IdTextVo格式（id=value, text=label）
     */
    @ApiOperation("根据字典类型编码获取字典列表")
    @GetMapping("/type")
    public JsonResult<List<IdTextVo>> getIdTextByType(@RequestParam String type) {
        return new JsonResult<>(baseService.getIdTextByType(type));
    }

    /**
     * 同步字典数据信息
     */
    @AuditLog("手动同步字典数据信息")
    @ApiOperation("同步字典数据信息")
    @PostMapping("/sync")
    public Result sync() {
        baseService.syncDict();
        return new Result();
    }
}