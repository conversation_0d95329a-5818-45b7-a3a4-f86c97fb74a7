package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 星光闪耀商品报名表
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_score_goods_apply")
public class MallScoreGoodsApply extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 兑换id
     */
    @TableField("exchange_id")
    @ApiModelProperty(value = "兑换id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    /**
     * 商品id
     */
    @TableField("goods_id")
    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long goodsId;
    /**
     * 商品名称
     */
    @TableField("goods_name")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 志愿者id
     */
    @TableField("volunteer_id")
    @ApiModelProperty(value = "志愿者id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;

    /**
     * 志愿者联系方式
     */
    @TableField("volunteer_phone")
    @ApiModelProperty(value = "志愿者联系方式")
    private String volunteerPhone;
    /**
     * 志愿者姓名
     */
    @TableField("volunteer_name")
    @ApiModelProperty(value = "志愿者姓名")
    private String volunteerName;

    /**
     * 监护人姓名
     */
    @TableField("guardian_name")
    @ApiModelProperty(value = "监护人姓名")
    private String guardianName;
    /**
     * 监护人联系方式
     */
    @TableField("guardian_phone")
    @ApiModelProperty(value = "监护人联系方式")
    private String guardianPhone;
    /**
     * 监护人证件类型
     */
    @TableField("guardian_certificate_type")
    @DictTrans(transTo = "guardianCertificateTypeText")
    @ApiModelProperty(value = "监护人证件类型")
    private String guardianCertificateType;
    /**
     * 监护人证件类型text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "监护人证件类型text")
    private String guardianCertificateTypeText;
    /**
     * 监护人证件号
     */
    @TableField("guardian_certificate_id")
    @ApiModelProperty(value = "监护人证件号")
    private String guardianCertificateId;
    /**
     * 监护人工作单位
     */
    @TableField("guardian_work_unit")
    @ApiModelProperty(value = "监护人工作单位")
    private String guardianWorkUnit;
    /**
     * 参加人姓名
     */
    @TableField("participant_name")
    @ApiModelProperty(value = "参加人姓名")
    private String participantName;
    /**
     * 参加人联系方式
     */
    @TableField("participant_phone")
    @ApiModelProperty(value = "参加人联系方式")
    private String participantPhone;
    /**
     * 参加人证件类型
     */
    @TableField("participant_certificate_type")
    @DictTrans(transTo = "participantCertificateTypeText")
    @ApiModelProperty(value = "参加人证件类型")
    private String participantCertificateType;
    /**
     * 参加人证件类型text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "参加人证件类型text")
    private String participantCertificateTypeText;
    /**
     * 参加人证件号
     */
    @TableField("participant_certificate_id")
    @ApiModelProperty(value = "参加人证件号")
    private String participantCertificateId;
    /**
     * 参加人学校
     */
    @TableField("participant_school")
    @ApiModelProperty(value = "参加人学校")
    private String participantSchool;
    /**
     * 参加人年龄
     */
    @TableField("participant_age")
    @ApiModelProperty(value = "参加人年龄")
    private Integer participantAge;
    /**
     * 参加人性别
     */
    @TableField("participant_sex")
    @ApiModelProperty(value = "参加人性别")
    @DictTrans(transTo = "participantSexText")
    private String participantSex;

    /**
     * 参加人性别
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "参加人性别")
    private String participantSexText;


}
