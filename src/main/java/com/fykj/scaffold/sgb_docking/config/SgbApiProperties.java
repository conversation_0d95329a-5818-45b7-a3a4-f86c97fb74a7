package com.fykj.scaffold.sgb_docking.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 苏州志愿者平台API配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "sgb.api")
public class SgbApiProperties {
    /**
     * API地址
     */
    private String url;
    
    /**
     * 应用密钥
     */
    private String appKey;
    
    /**
     * 应用密钥
     */
    private String secretKey;
    
    /**
     * 签名算法
     */
    private String algorithm;
} 