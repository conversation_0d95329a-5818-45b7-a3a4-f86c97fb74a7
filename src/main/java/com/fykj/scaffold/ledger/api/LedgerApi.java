package com.fykj.scaffold.ledger.api;

import com.fykj.scaffold.ledger.domain.dto.LedgerItemDto;
import com.fykj.scaffold.ledger.domain.dto.LedgerReportDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndex;
import com.fykj.scaffold.ledger.service.ILedgerDepartmentService;
import com.fykj.scaffold.ledger.service.ILedgerIndexItemService;
import com.fykj.scaffold.ledger.service.ILedgerIndexService;
import com.fykj.scaffold.ledger.service.ILedgerReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;

@RestController
@RequestMapping("/admin/ledger")
@Api(tags = "测评台账PC端接口")
public class LedgerApi {

    @Autowired
    private ILedgerDepartmentService departmentService;

    @Autowired
    private ILedgerIndexService indexService;
    @Autowired
    private ILedgerIndexItemService indexItemService;

    @Autowired
    private ILedgerReportService reportService;

    @ApiOperation(value = "校验是否有权限")
    @GetMapping(value = "/validUser")
    public JsonResult<Boolean> validUser() {
        return new JsonResult<>(departmentService.validUser());
    }
    @ApiOperation(value = "获取用户对应的局办指标树")
    @GetMapping(value = "/getUserIndexTree")
    public JsonResult<List<LedgerIndex>> getUserIndexTree() {
        return new JsonResult<>(indexService.getUserIndexTree());
    }
    @ApiOperation(value = "获取指标对象指标项")
    @GetMapping(value = "/getUserIndexItem")
    public JsonResult<List<LedgerItemDto>> getUserIndexItem(@RequestParam Long indexId) {
        return new JsonResult<>(indexItemService.getUserIndexItem(indexId));
    }

    @ApiOperation(value = "上传")
    @PostMapping(value = "/report")
    public Result report(@RequestBody @Validated LedgerReportDto report) {
        return reportService.report(report);
    }
}
