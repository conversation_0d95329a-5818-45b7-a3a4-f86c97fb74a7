package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzRequirementTemp;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementTempParams;
import com.fykj.scaffold.zyz.service.IZyzRequirementTempService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 需求临时表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-05
 */
@RestController
@RequestMapping("/admin/zyz/requirement_temp")
public class ZyzRequirementTempController extends BaseController<IZyzRequirementTempService, ZyzRequirementTemp, ZyzRequirementTempParams> {

    @SysLogMethod("需求临时数据新增")
    @ApiOperation("保存方法(返回id)")
    @PostMapping({"/saveForId"})
    public Result saveForId(@RequestBody @Validated({BaseEntity.Add.class}) ZyzRequirementTemp entity) {
        boolean result = this.baseService.saveRequirementTemp(entity);
        if (result) {
            return new JsonResult<>(String.valueOf(entity.getId()));
        }
        return new Result(ResultCode.FAIL);
    }
}
