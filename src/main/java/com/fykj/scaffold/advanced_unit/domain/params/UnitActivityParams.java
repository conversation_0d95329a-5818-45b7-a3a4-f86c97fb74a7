package com.fykj.scaffold.advanced_unit.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 文明单位-申请表
 * 查询参数
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明单位-活动-筛选参数")
public class UnitActivityParams extends BaseParams {

    @ApiModelProperty(value = "单位id")
    @NotNull(message = "单位id不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

}
