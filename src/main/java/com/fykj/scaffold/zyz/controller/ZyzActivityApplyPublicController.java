package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityApplyPublicExport;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApplyPublic;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityApplyPublicParams;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyPublicService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 活动群众报名表
 *
 * 前端控制器
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-31
 */
@RestController
@RequestMapping("/admin/zyz/activity/apply/public")
@Api(tags = "活动--群众报名接口接口")
public class ZyzActivityApplyPublicController extends BaseController<IZyzActivityApplyPublicService, ZyzActivityApplyPublic,ZyzActivityApplyPublicParams> {



    @ApiOperation("查询群众活动报名--只查询自己")
    @PostMapping({"/pagesForApplyPublic"})
    public JsonResult<IPage<ZyzActivityApplyPublic>> pagesForApplyPublic(@RequestBody(required = false) ZyzActivityApplyPublicParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzActivityApplyPublicParams();
        }
        if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(Oauth2Util.getManagerCapacity())){
            params.setMyTeamId(Oauth2Util.getTeamId());
        }else {
            params.setMyOrgCode(Oauth2Util.getOrgCode());
        }
        IPage<ZyzActivityApplyPublic> result = baseService.pagesForApplyPublic(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("查询群众活动报名--全部")
    @PostMapping({"/pagesForApplyPublicAll"})
    public JsonResult<IPage<ZyzActivityApplyPublic>> pagesForApplyPublicAll(@RequestBody(required = false) ZyzActivityApplyPublicParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzActivityApplyPublicParams();
        }
        IPage<ZyzActivityApplyPublic> result = baseService.pagesForApplyPublic(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }


    @ApiOperation("群众报名导出")
    @PostMapping({"/exportForPublic"})
    public void exportForPublic(@RequestBody(required = false) ZyzActivityApplyPublicParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzActivityApplyPublicParams();
        }
        if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(Oauth2Util.getManagerCapacity())){
            params.setMyTeamId(Oauth2Util.getTeamId());
        }else {
            params.setMyOrgCode(Oauth2Util.getOrgCode());
        }
        List<ZyzActivityApplyPublicExport> res = baseService.listForApplyPublic(params);
//        DesensitiseUtil.desensitise(res);
        ExcelUtil.writeExcel(res, "群众报名记录列表", ZyzActivityApplyPublicExport.class);
    }
    
    @ApiOperation("群众报名动态字段导出")
    @PostMapping({"/exportWithDynamicFields"})
    public void exportWithDynamicFields(@RequestBody(required = false) ZyzActivityApplyPublicParams params, HttpServletResponse response) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzActivityApplyPublicParams();
        }
        
        if (params.getActivityId() == null) {
            throw new BusinessException(ResultCode.FAIL, "活动ID不能为空");
        }
        
        // 直接调用Service层方法完成导出
        baseService.exportWithDynamicFields(params, response);
    }
}
