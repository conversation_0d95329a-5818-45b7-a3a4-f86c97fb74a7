package com.fykj.scaffold.capability.controller;

import com.fykj.scaffold.capability.domain.dto.CourseDetailDTO;
import com.fykj.scaffold.capability.service.ICourseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Validated
@RestController
@RequestMapping("/api/customer/capability/course")
@Api(tags = "课程管理接口-用户端")
public class CourseApiController {

    @Autowired
    private ICourseService baseService;

    @ApiOperation("课程详情")
    @GetMapping("/detail")
    public JsonResult<CourseDetailDTO> detailCourseMine(@RequestParam Long id) {
        CourseDetailDTO detail = baseService.detailCourseMine(id);
        return new JsonResult<>(detail);
    }
}
