package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivitySchedule;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleActPlan;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 阵地计划（小计划）关联活动计划（大活动）
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IZyzActivityScheduleActPlanService extends IBaseService<ZyzActivityScheduleActPlan> {

    /**
     * 保存计划活动关联
     *
     * @param dto 阵地计划
     */
    void saveScheduleActPlan(ZyzActivitySchedule dto);

    /**
     * 根据detailId和activityId保存ScheduleActPlan
     *
     * @param detailId   计划详情ID
     * @param activityId 活动ID
     */
    void saveScheduleActPlanByDetailIdAndActivityId(Long detailId, Long activityId);

    Long getScheduleIdByActivityId(Long activityId);

    /**
     * 获取需要同步的数据
     *
     * @return id列表
     */
    List<Long> getNeedSync();

    /**
     * 批量同步
     *
     * @param scheduleActPlanIds 阵地计划活动关联id
     */
    void syncBatch(List<Long> scheduleActPlanIds);

    /**
     * 单条同步
     *
     * @param scheduleActPlanId 阵地计划活动关联id
     * @return ZyzSyncLog 同步log
     */
    ZyzSyncLog syncOne(Long scheduleActPlanId);

    /**
     * 根据活动id删除关联
     * @param activityIdList 活动id list
     */
    void removeActivityScheduleDetailRelation(List<Long> activityIdList);
}

