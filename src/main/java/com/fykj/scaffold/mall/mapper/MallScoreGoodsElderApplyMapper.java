package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsElderApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsElderApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsElderApplyParams;
import org.apache.ibatis.annotations.Param;

/**
 * 星光闪耀适老化改造福申请表
 * <p>
 * Mapper 接口
 *
 * <AUTHOR> @email ${email}
 * @date 2024-10-16
 */
public interface MallScoreGoodsElderApplyMapper extends BaseMapper<MallScoreGoodsElderApply> {

    /**
     * 分页查询--后端页面，有权限限制
     *
     * @return
     */
    @DataFilter
    IPage<MallScoreGoodsElderApplyDto> getPage(IPage<MallScoreGoodsElderApplyDto> page, @Param("params") MallScoreGoodsElderApplyParams params);
}
