package com.fykj.scaffold.sync.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncWaringListDto;
import com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogParams;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogStatisticsParams;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.domain.dto.ProjectCompanyContributeDto;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;

/**
 * 市平台信息同步日志
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@RestController
@RequestMapping("/admin/zyz/sync-log")
public class ZyzSyncLogController extends BaseController<IZyzSyncLogService, ZyzSyncLog, ZyzSyncLogParams> {
    @Override
    public JsonResult<IPage<ZyzSyncLog>> list(@RequestBody ZyzSyncLogParams params) {
        JsonResult<IPage<ZyzSyncLog>> result = super.list(params);
        DictTransUtil.trans(result.getObj().getRecords());
        return result;
    }
    @ApiOperation("统计分页查询")
    @PostMapping("/statisticsPage")
    public JsonResult<IPage<SyncStatisticsDto>> statisticsPage(@RequestBody ZyzSyncLogStatisticsParams params) {
        return new JsonResult<>(baseService.statisticsPage(params));
    }
    @ApiOperation("失败原因分页查询")
    @PostMapping("/statisticsFailList")
    public JsonResult<List<SyncFailStatisticsDto>> statisticsFailList(@RequestBody ZyzSyncLogStatisticsParams params) {
        return new JsonResult<>(baseService.statisticsFailList(params));
    }


    @PostMapping("/exportStatistics")
    @ApiOperation("导出统计")
    public void exportStatistics(@RequestBody ZyzSyncLogStatisticsParams params) {
        List<ZyzSyncLogDto> list = baseService.exportStatistics(params);
        ExcelUtil.fillExcel(list, "zyz_sync_log_statistics.xlsx", ZyzSyncLogDto.class);
    }

    @ApiOperation("上报数据预警列表")
    @GetMapping("/statisticsFailWaringList")
    public JsonResult<List<SyncWaringListDto>> statisticsFailWaringList(@RequestParam String date) {
        return new JsonResult<>(baseService.statisticsFailWaringList(date));
    }
}
