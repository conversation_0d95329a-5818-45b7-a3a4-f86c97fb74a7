package com.fykj.scaffold.mall.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallStock;
import com.fykj.scaffold.mall.domain.params.MallStockParams;
import com.fykj.scaffold.mall.service.IMallStockService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * <p>
 * 物料信息管理前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@RestController
@RequestMapping("/admin/mall/stock")
@Api(tags = "物料信息管理接口")
public class MallStockController extends BaseController<IMallStockService, MallStock, MallStockParams> {

    @AuditLog("物料分页查询")
    @ApiOperation("物料分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallStock>> getPages(@RequestBody MallStockParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @ResponseBody
    @RequestMapping(value = "/goodsStorageIn", method = RequestMethod.POST)
    @ApiOperation(value = "入库")
    public Result goodsStorageIn(long goodsId, int goodsNum) {
        baseService.changeStock(goodsId, goodsNum);
        return OK;
    }

    @ApiOperation("保存并返回物料对象")
    @PostMapping("/saveMallStock")
    public JsonResult<MallStock> saveMallStock(@RequestBody MallStock params) {
        return new JsonResult<>(baseService.saveMallStock(params));
    }

    @ApiOperation("查询本机构下的物料")
    @GetMapping("/getStockByOrg")
    public JsonResult<List<MallStock>> getStockByOrg() {
        return new JsonResult<>(baseService.getStockByOrg());
    }

    @ApiOperation("查询本机构下的物料")
    @GetMapping("/allStock")
    public JsonResult<List<MallStock>> allStock() {
        return new JsonResult<>(baseService.lambdaQuery().orderByDesc(BaseEntity::getCreateDate).list());
    }
}
