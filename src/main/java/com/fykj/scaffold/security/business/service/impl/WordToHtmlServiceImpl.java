package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.support.oss.FilePathDto;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import constants.Mark;
import exception.BusinessException;
import fr.opensagres.poi.xwpf.converter.core.FileImageExtractor;
import fr.opensagres.poi.xwpf.converter.core.FileURIResolver;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import org.apache.commons.io.FileUtils;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.converter.WordToHtmlConverter;
import org.apache.poi.hwpf.usermodel.Picture;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.jsoup.Jsoup;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import result.ResultCode;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WordToHtmlServiceImpl {

    /**
     * 将文件放入目标文件夹
     *
     * @param file
     * @return
     * @throws IOException
     */
    public String writeFileToTargetPath(MultipartFile file) {
//        FilePathDto pass = OssSaveUtil.save(file, OssCons.OSS_LOCAL);
//        return pass.getRealPath();
        return null;
    }

    /**
     * word文件转html入口方法
     *
     * @param filePath 文件存储路径
     * @return
     */
    public String getWordToTransfer(String filePath) {
//        String html = null;
//        String path = filePath.substring(0, filePath.lastIndexOf(File.separator) + 1);
//        String filename = filePath.substring(filePath.lastIndexOf(File.separator) + 1, filePath.lastIndexOf(Mark.DOT));
//        if (filePath.toLowerCase().endsWith(".doc")) {
//            html = docToHtml(path, filePath, path + filename + ".html");
//        }
//        if (filePath.toLowerCase().endsWith(".docx")) {
//            html = docxToHtml(path, filePath, path + filename + ".html");
//        }
//        return html;
        return null;
    }

    /**
     * doc文件转html
     *
     * @param dir      文件夹
     * @param filePath 文件全路径
     * @param htmlPath html全路径
     * @return
     */
//    public String docToHtml(String dir, String filePath, String htmlPath) {
//        try (FileInputStream is = new FileInputStream(filePath);
//             HWPFDocument wordDocument = new HWPFDocument(is);
//             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
//            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
//            // disable external entities
//            factory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, Boolean.TRUE);
//
//            WordToHtmlConverter wordToHtmlConverter = new WordToHtmlConverter(
//                    factory.newDocumentBuilder().newDocument());
//            wordToHtmlConverter.setPicturesManager((content, pictureType, suggestedName, widthInches, heightInches) -> "test/" + suggestedName);
//            wordToHtmlConverter.processDocument(wordDocument);
//            List<Picture> pics = wordDocument.getPicturesTable().getAllPictures();
//            if (pics != null) {
//                pics.forEach(it -> saveImage(it, dir));
//            }
//            Document htmlDocument = wordToHtmlConverter.getDocument();
//            DOMSource domSource = new DOMSource(htmlDocument);
//            StreamResult streamResult = new StreamResult(out);
//            TransformerFactory transformerFactory = TransformerFactory.newInstance();
//            transformerFactory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
//            Transformer serializer = transformerFactory.newTransformer();
//            serializer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
//            serializer.setOutputProperty(OutputKeys.INDENT, "yes");
//            serializer.setOutputProperty(OutputKeys.METHOD, "html");
//            serializer.transform(domSource, streamResult);
//            String result = out.toString();
//            FileUtils.writeStringToFile(new File(htmlPath), result, "utf-8");
//            return sharedMethod(new File(dir), result);
//        } catch (Exception e) {
//            throw new BusinessException(ResultCode.FAIL, "文件读取失败", e);
//        }
//    }

//    private void saveImage(Picture pic, String filePath) {
//        String imagesPath = filePath + "word" + File.separator + "media";
//        File imageFile = new File(imagesPath);
//        if (!imageFile.exists()) {
//            boolean result = imageFile.mkdirs();
//            if (!result) {
//                throw new BusinessException(ResultCode.FAIL, "文件未成功创建");
//            }
//        }
//        String imagePath = imageFile + File.separator + pic.suggestFullFileName();
//        try (FileOutputStream fileOutputStream = new FileOutputStream(imagePath)) {
//            pic.writeImageContent(fileOutputStream);
//        } catch (Exception e) {
//            throw new BusinessException(ResultCode.FAIL, "图片保存失败", e);
//        }
//    }

    /**
     * docx文件转html
     *
     * @param dir
     * @param fileName
     * @param htmlName
     * @return
     * @throws IOException
     */
//    public String docxToHtml(String dir, String fileName, String htmlName) {
//        File f = new File(fileName);
//        //解析XHTML配置
//        File imageFolderFile = new File(dir);
//        XHTMLOptions options = XHTMLOptions.create().URIResolver(new FileURIResolver(imageFolderFile));
//        options.setExtractor(new FileImageExtractor(imageFolderFile));
//        options.setIgnoreStylesIfUnused(false);
//        options.setFragment(true);
//
//        //加载word文档生成XWPFDocument对象
//        try (InputStream is = new FileInputStream(f);
//             XWPFDocument document = new XWPFDocument(is);
//             ByteArrayOutputStream htmlStream = new ByteArrayOutputStream()) {
//
//            XHTMLConverter.getInstance().convert(document, htmlStream, options);
//            String result = htmlStream.toString();
//            FileUtils.writeStringToFile(new File(htmlName), result, "utf-8");
//            return sharedMethod(imageFolderFile, result);
//        } catch (Exception e) {
//            throw new BusinessException(ResultCode.FAIL, "文件读取失败", e);
//        }
//    }

    /**
     * docx和doc文件共享后续处理冗余文件方法
     *
     * @param imageFolderFile
     * @param result
     * @return
     * @throws IOException
     */
//    public String sharedMethod(File imageFolderFile, String result) {
//        File fileToImage = null;
//        for (int i = 0; i < Objects.requireNonNull(imageFolderFile.listFiles()).length; i++) {
//            if (Objects.requireNonNull(imageFolderFile.listFiles())[i].listFiles() != null) {
//                fileToImage = Objects.requireNonNull(imageFolderFile.listFiles())[i];
//                break;
//            }
//        }
//        if (fileToImage == null) {
//            return result;
//        }
//        File imageFile = Objects.requireNonNull(fileToImage.listFiles())[0];
//        File[] imageFileList = imageFile.listFiles();
//        return imagePathChange(result, Objects.requireNonNull(imageFileList));
//    }

    /**
     * 删除文件
     *
     * @param file
     * @throws IOException
     */
//    public void deleteFile(File file) throws IOException {
//        if (!file.delete()) {
//            throw new IOException("文件未删除成功");
//        }
//    }

    /**
     * 图片上传到服务器，并修改html中的图片路径
     *
     * @param content
     * @param imageList
     * @return
     * @throws IOException
     */
//    public String imagePathChange(String content, File[] imageList) {
//        List<FilePathDto> list = new ArrayList<>();
//        for (File f : imageList) {
//            try (FileInputStream fis = new FileInputStream(f)) {
//                FilePathDto pass = OssSaveUtil.save(fis, OssCons.OSS_LOCAL,
//                        f.getName());
//                list.add(pass);
//            } catch (Exception e) {
//                throw new BusinessException(ResultCode.FAIL, "读取文件失败", e);
//            }
//        }
//        return convert(content, list);
//    }

    /**
     * 修改html中的图片路径
     *
     * @param content
     * @param list
     * @return
     */
//    public String convert(String content, List<FilePathDto> list) {
//        org.jsoup.nodes.Document doc = Jsoup.parse(content);
//        Elements imgs = doc.getElementsByTag("img");
//        int i = 0;
//        for (org.jsoup.nodes.Element img : imgs) {
//            img.attr("src", list.get(i).getUrl());
//            i++;
//        }
//        return doc.toString();
//    }

}
