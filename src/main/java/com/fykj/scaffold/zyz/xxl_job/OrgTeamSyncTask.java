package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.service.IZyzOrgTeamService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class OrgTeamSyncTask {

    @Autowired
    private IZyzOrgTeamService orgTeamService;

    @XxlJob("Org_Team_Sync_Handler")
    public void orgTeamSyncJobHandler() {
        XxlJobHelper.log("Org_Team_Sync_Handler-Job, START!");
        orgTeamService.syncOrg2Team();
        List<Long> teamIds = orgTeamService.getNeedSync();
        if (CollectionUtil.isNotEmpty(teamIds)) {
            try {
                orgTeamService.syncBatch(teamIds);
            } catch (Exception e) {
                log.error("团队批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Org_Team_Sync_Handler-Job, END!");
    }
}
