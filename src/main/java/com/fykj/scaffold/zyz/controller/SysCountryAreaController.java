package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.SysCountryArea;
import com.fykj.scaffold.zyz.domain.params.SysCountryAreaParams;
import com.fykj.scaffold.zyz.service.ISysCountryAreaService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-03-29
 */
@RestController
@RequestMapping("/api/syscountryarea")
public class SysCountryAreaController extends BaseController<ISysCountryAreaService, SysCountryArea, SysCountryAreaParams> {


}
