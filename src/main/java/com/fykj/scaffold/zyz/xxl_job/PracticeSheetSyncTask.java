package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.sync.service.IPracticeSheetService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.zyz.service.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PracticeSheetSyncTask {

    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzResourceAppointmentService appointmentService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IPracticeSheetService practiceSheetService;

    @XxlJob("Practice_Sheet_Sync_Handler")
    public void practiceSheetSyncJobHandler() {
        XxlJobHelper.log("Practice_Sheet_Sync_Handler-Job, START!");
        // 获取待同步的需求实践单数据
//        List<Long> requirementIds = requirementService.getNeedPracticeSheetSync();
//        if (CollectionUtil.isNotEmpty(requirementIds)) {
//            try {
//                practiceSheetService.syncBatch(requirementIds, Cons.OrderType.REQUIREMENT);
//            } catch (Exception e) {
//                log.error("需求实践单批量同步失败，失败原因：{}", e.getMessage(), e);
//            }
//        }
        // 获取待同步的资源预约实践单数据
//        List<Long> resourceAppointmentIds = appointmentService.getNeedPracticeSheetSync();
//        if (CollectionUtil.isNotEmpty(resourceAppointmentIds)) {
//            try {
//                practiceSheetService.syncBatch(resourceAppointmentIds, Cons.OrderType.RESOURCE);
//            } catch (Exception e) {
//                log.error("资源预约实践单批量同步失败，失败原因：{}", e.getMessage(), e);
//            }
//        }
        // 获取待同步的活动实践单数据
        List<Long> actTpIds = activityService.getNeedPracticeSheetSync();
        log.info("需要同步的数量：{}", actTpIds.size());
        if (CollectionUtil.isNotEmpty(actTpIds)) {
            try {
                practiceSheetService.syncBatch(actTpIds, Cons.OrderType.ACTIVITY);
            } catch (Exception e) {
                log.error("活动实践单批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Practice_Sheet_Sync_Handler-Job, END!");
    }
}
