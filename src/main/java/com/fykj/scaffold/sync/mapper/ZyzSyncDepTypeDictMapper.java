package com.fykj.scaffold.sync.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDepTypeDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 市平台职能部门类型数据字典
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface ZyzSyncDepTypeDictMapper extends BaseMapper<ZyzSyncDepTypeDict> {
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
}
