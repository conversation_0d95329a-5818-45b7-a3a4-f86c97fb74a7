package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.dto.ApiPathDto;
import com.fykj.scaffold.security.business.domain.entity.AppApiPath;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 应用接口权限
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2021-05-28
 */
public interface IAppApiPathService extends IBaseService<AppApiPath> {

    /**
     * 保存授权
     * @param dto
     * @return
     */
    boolean savePath(ApiPathDto dto);

    /**
     * 根据appId获取授权
     * @param appId
     * @return
     */
    List<String> getApiIdListByAppId(String appId);

    /**
     * 根据appId获取授权信息
     * @param appId
     * @return
     */
    List<AppApiPath> getByAppId(String appId);
}

