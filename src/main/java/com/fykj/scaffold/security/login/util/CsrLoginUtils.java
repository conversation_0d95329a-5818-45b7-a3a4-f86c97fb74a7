package com.fykj.scaffold.security.login.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.login.cons.CsrLoginConfig;
import com.fykj.scaffold.security.login.domain.dto.CsrLoginDto;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import result.JsonResult;
import result.ResultCode;

/**
 * @Author：yangxu
 * @Date：2024/12/10 18:12
 * @Description：
 */
@Component
@Slf4j
public class CsrLoginUtils {

    @Autowired
    private CsrLoginConfig csrLoginConfig;

    public JSONObject httpRequestForOneCode(String mobile) {
        RestTemplate restTemplate = new RestTemplate();
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("phone", mobile);
        try {
            String response = restTemplate.postForObject(csrLoginConfig.getOneCodeFetchUrl(), params, String.class);
            log.info("one_code fetch response: {}", response);
            return JSON.parseObject(response);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "one_code fetch error, error reason: {" +  e.getMessage() + "}");
        }
    }

    public JsonResult<?> login(String username, String oneCode) {
        RestTemplate restTemplate = new RestTemplate();
        CsrLoginDto data = new CsrLoginDto("one_code",  username, oneCode);
        try {
            String response = restTemplate.postForObject(csrLoginConfig.getLoginUrl(), data, String.class);
            log.info("csr login response: {}", response);
            return JSON.parseObject(response, JsonResult.class);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "csr login error, error reason: {" +  e.getMessage() + "}");
        }
    }
}
