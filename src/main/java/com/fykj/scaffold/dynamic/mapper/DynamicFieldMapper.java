package com.fykj.scaffold.dynamic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.dynamic.domain.entity.DynamicField;
import org.apache.ibatis.annotations.Param;

/**
 * 字段信息-Mapper接口
 *
 * @date 2025-05-19
 */
public interface DynamicFieldMapper extends BaseMapper<DynamicField> {

    /**
     * 字段名称是否存在
     * @param fieldName 字段命
     * @return 字段名称是否存在
     */
    boolean isFieldNameExist(@Param("fieldName") String fieldName);

    /**
     * 根据字段名称查找
     * @param fieldName 字段名称
     * @return 字段信息
     */
    DynamicField getByFieldName(@Param("fieldName") String fieldName);

}
