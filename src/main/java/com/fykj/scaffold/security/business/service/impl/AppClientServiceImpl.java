package com.fykj.scaffold.security.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fykj.scaffold.security.business.domain.entity.App;
import com.fykj.scaffold.security.business.domain.entity.AppClient;
import com.fykj.scaffold.security.business.mapper.AppClientMapper;
import com.fykj.scaffold.security.business.mapper.AppMapper;
import com.fykj.scaffold.security.business.service.IAppClientService;
import com.fykj.scaffold.security.business.service.IAppService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.util.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;
import utils.UUIDUtils;

import java.net.PasswordAuthentication;
import java.util.Arrays;


/**
 * 应用客户端信息服务实现类
 *
 * <AUTHOR>
 * @date 2021-05-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AppClientServiceImpl extends ServiceImpl<AppClientMapper, AppClient> implements IAppClientService {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public AppClient getByAppId(String appId) {
        AppClient client = lambdaQuery().eq(AppClient::getClientId, appId).one();
        if (client != null) {
            client.setTypes(Arrays.asList(client.getAuthorizedGrantTypes().split(Mark.COMMA)));
        }
        return client;
    }

    @Override
    public String secretRest(String appId) {
        AppClient client = getByAppId(appId);
        if (client == null) {
            throw new BusinessException(ResultCode.FAIL, "该应用尚未保存开发信息，请确认");
        }
        String secret = UUIDUtils.generateShortUuid();
        boolean result = lambdaUpdate().eq(AppClient::getClientId, appId)
                .set(AppClient::getClientSecret, passwordEncoder.encode(secret))
                .update();
        if (!result) {
            throw new BusinessException(ResultCode.FAIL, "重置失败，请重试");
        }
        return secret;
    }

    @Override
    public boolean save(AppClient entity) {
        String appId = entity.getClientId();
        AppClient client = getByAppId(appId);
        if (client != null) {
            return lambdaUpdate().eq(AppClient::getClientId, appId)
                    .set(AppClient::getScope, entity.getScope())
                    .set(AppClient::getAuthorizedGrantTypes, entity.getAuthorizedGrantTypes())
                    .set(AppClient::getAccessTokenValidity, entity.getAccessTokenValidity())
                    .set(AppClient::getRefreshTokenValidity, entity.getRefreshTokenValidity())
                    .set(AppClient::getWebServerRedirectUri, entity.getWebServerRedirectUri())
                    .set(AppClient::getAdditionalInformation, entity.getAdditionalInformation())
                    .update();
        }
        entity.setClientSecret(passwordEncoder.encode(entity.getClientSecret()));
        return super.save(entity);
    }
}
