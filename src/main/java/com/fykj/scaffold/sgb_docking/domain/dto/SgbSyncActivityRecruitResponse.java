package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 苏州社工部发布活动招募同步响应
 */
@Data
public class SgbSyncActivityRecruitResponse {
    
    /**
     * 响应信息列表
     */
    private List<ActivityRecruitResult> outs;
    
    /**
     * 活动招募推送结果
     */
    @Data
    public static class ActivityRecruitResult {

        /**
         * 响应状态码 - SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 唯一标识
         */
        private String uniCode;

        /**
         * 活动招募id - SUCCESS时返回，发布的招募在本平台生成的唯一标识
         */
        private String actId;
    }
}
