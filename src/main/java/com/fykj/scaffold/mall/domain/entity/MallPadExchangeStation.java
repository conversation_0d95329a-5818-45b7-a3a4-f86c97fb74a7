package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_pad_exchange_station")
public class MallPadExchangeStation extends BaseEntity {

    private static final long serialVersionUID = -6583858992283749977L;

    /**
     * 站点code
     */
    @TableField("code")
    @ApiModelProperty(value = "站点code")
    private String code;

    /**
     * 站点名称
     */
    @TableField("name")
    @ApiModelProperty(value = "站点名称")
    private String name;

    /**
     * 组织机构code
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织机构code")
    private String orgCode;

    /**
     * 组织机构名称
     */
    @TableField("org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 地址
     */
    @TableField("address")
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 经度
     */
    @TableField("longitude")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @TableField("latitude")
    @ApiModelProperty(value = "维度")
    private BigDecimal latitude;

    /**
     * 负责人名称
     */
    @TableField("link_person_name")
    @ApiModelProperty(value = "负责人名称")
    private String linkPersonName;

    /**
     * 负责人联系电话
     */
    @TableField("link_phone")
    @ApiModelProperty(value = "负责人联系电话")
    private String linkPhone;

    /**
     * 状态
     */
    @TableField("status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(exist = false)
    @ApiModelProperty(value = "距离")
    private Integer distance;

    /**
     * 兑换机Logo
     */
    @TableField("station_logo")
    @ApiModelProperty(value = "站点Logo")
    private String stationLogo;

    /**
     * 是否可选择
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否可选择")
    private Boolean enableSelect;
}
