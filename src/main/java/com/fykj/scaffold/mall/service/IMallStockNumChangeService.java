package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallStockNumChange;
import com.fykj.scaffold.mall.domain.params.MallStockNumChangeParams;
import fykj.microservice.core.base.IBaseService;

/**
 * 积分商城-库存变动
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
public interface IMallStockNumChangeService extends IBaseService<MallStockNumChange> {
    /**
     * 分页查询--后端页面
     */
    IPage<MallStockNumChange> getPage(MallStockNumChangeParams params);
}

