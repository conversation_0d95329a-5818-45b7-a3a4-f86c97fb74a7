package com.fykj.scaffold.sgb_docking.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 苏州志愿者平台部门单位查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("苏州志愿者平台部门单位查询参数")
public class SgbDeptParams extends BaseParams {

    private static final long serialVersionUID = 1L;

    /**
     * 部门单位编号
     */
    @ApiModelProperty("部门单位编号")
    @MatchType(QueryType.LIKE)
    private String deptId;

    /**
     * 部门单位名称
     */
    @ApiModelProperty("部门单位名称")
    @MatchType(QueryType.LIKE)
    private String deptName;

    /**
     * 所属区划编号
     */
    @ApiModelProperty("所属区划编号")
    @MatchType(QueryType.LIKE)
    private String regionCode;
} 