package com.fykj.scaffold.sync.util;

import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.support.util.SpringContextUtil;
import fykj.microservice.core.support.util.SystemUtil;
import lombok.experimental.UtilityClass;
import utils.StringUtil;

@UtilityClass
public class SyncUtil {
    /**
     * 获取图片路径
     *
     * @param suffix
     * @return
     */
    public static String getPicPath(String suffix) {
        //用默认图
        if (StringUtil.isEmpty(suffix)) {
            Dict dict = SpringContextUtil.getBean(IDictService.class).getByCode(Cons.SystemConfig.DEFAULT_SYNC_PIC_URL);
            if (dict == null) {
                return null;
            }
            return dict.getValue();
        }
        //全路径
        if (suffix.startsWith("http")) {
            return suffix;
        }
        return SystemUtil.getProperty("httpAttachmentUrl") + suffix;
    }
}
