package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 推送活动参与人员响应
 */
@Data
public class SyncActivityMemberResponse {
    
    /**
     * 返回数据
     */
    private List<MemberResult> info;
    
    /**
     * 活动参与人员推送结果
     */
    @Data
    public static class MemberResult {
        /**
         * 响应状态码
         * SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 市平台活动ID
         */
        private String platformActivityId;
        
        /**
         * 身份证号码
         */
        private String idCode;
        
        /**
         * 处理结果：1-成功，0-失败
         */
        private String result;
        
        /**
         * 错误消息，仅当失败时有值
         */
        private String message;
    }
} 