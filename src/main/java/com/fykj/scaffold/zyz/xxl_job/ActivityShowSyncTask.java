package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollUtil;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodShowService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityShowSyncTask {

    @Autowired
    private IZyzActivityTimePeriodShowService showService;

    @XxlJob("Activity_Show_Sync_Handler")
    public void showSyncJobHandler() {
        XxlJobHelper.log("Activity_Show_Sync_Handler-Job, START!");
        // 获取待同步的公示数据以及公示审核通过时间三天内同步失败的数据
        List<Long> showsIds = showService.getNeedSync();
        if (CollUtil.isNotEmpty(showsIds)) {
            try {
                showService.syncBatch(showsIds);
            } catch (Exception e) {
                log.error("活动公示批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Activity_Show_Sync_Handler-Job, END!");
    }
}
