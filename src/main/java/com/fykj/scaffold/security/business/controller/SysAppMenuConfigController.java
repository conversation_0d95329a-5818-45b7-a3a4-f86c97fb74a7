package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.SysAppMenuConfig;
import com.fykj.scaffold.security.business.domain.params.SysAppMenuConfigParams;
import com.fykj.scaffold.security.business.service.ISysAppMenuConfigService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import constants.Mark;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import utils.StringUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/admin/sys/app-menu-config")
public class SysAppMenuConfigController extends BaseController<ISysAppMenuConfigService, SysAppMenuConfig, SysAppMenuConfigParams> {

    @ApiOperation("根据栏位获取可以展示的app列表")
    @GetMapping(value = "/getAllEnableAppByCategoryCode")
    public Result getAllEnableApp(String categoryCode) {
        List<SysAppMenuConfig> apps = baseService.lambdaQuery()
                .eq(SysAppMenuConfig::getCategoryCode, categoryCode)
                .eq(SysAppMenuConfig::getStatus, true)
                .orderByAsc(SysAppMenuConfig::getSequence).list();
        String currentManageCapacity = Oauth2Util.getManagerCapacity();
        //按照限制的身份过滤需要显是的app
        apps = apps.stream().filter(app ->
                //空的就不控制了
                StringUtil.isEmpty(app.getLimitCapacity())
                        || Arrays.asList(app.getLimitCapacity().split(Mark.COMMA)).contains(currentManageCapacity)).collect(Collectors.toList());
        return new JsonResult<>(apps);
    }

}
