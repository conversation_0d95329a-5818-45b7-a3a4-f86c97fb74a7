package com.fykj.volunteer.sync.util.domain.sync_service_time;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 上传时长
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncServiceTime {
    /**
     * 身份证编号
     */
   private String idCode;
    /**
     * 第三方系统服务时长唯一标识键
     */
    private String uniCode;
    /**
     * 活动招募编号
     */
    private String actId;
    /**
     * 服务时长记录开始时间
     * 格式’HH:mm’，actId关联的活动登记中的recordWay=2，则则必填，与serverTime二选一
     */
    private String startTime;
    /**
     * 服务时长记录结束时间
     * 格式’HH:mm’，actId关联的活动登记中的recordWay=2，则则必填，与serverTime二选一
     */
    private String  endTime;
    /**
     * 服务时长 例：5.5精度0.5
     * 与startTime、endTime二选一
     */
    private String serverTime;
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "servertime")
    public static class ServerTime {
        private List<SyncServiceTime> info;
    }
    public String buildXml() {
        SyncServiceTime.ServerTime serverTime = new SyncServiceTime.ServerTime();
        serverTime.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(serverTime);
    }
}
