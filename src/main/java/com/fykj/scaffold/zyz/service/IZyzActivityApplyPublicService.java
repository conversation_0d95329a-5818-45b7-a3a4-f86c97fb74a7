package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.ApplyPublicPageDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityApplyPublicExport;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApplyPublic;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityApplyPublicParams;
import fykj.microservice.core.base.IBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 活动群众报名表
 *
 * 服务类
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-31
 */
public interface IZyzActivityApplyPublicService extends IBaseService<ZyzActivityApplyPublic> {

    /**
     * 保存群众活动报名信息
     * @param entity
     */
    void saveActivityForPublic(ZyzActivityApplyPublic entity);


    /**
     * 取消报名
     * @param applyPublicIds
     */
    void cancelApply(List<Long> applyPublicIds, Long timePeriodId);

    /**
     * 分页查询活动报名
     * @param params
     * @return
     */
    IPage<ZyzActivityApplyPublic> pagesForApplyPublic(ZyzActivityApplyPublicParams params);


    /**
     * 群众报名导出
     * @param params
     * @return
     */
    List<ZyzActivityApplyPublicExport> listForApplyPublic(ZyzActivityApplyPublicParams params);

    /**
     * 导出带动态字段的群众报名记录
     * @param params 查询参数
     * @param response HTTP响应对象
     */
    void exportWithDynamicFields(ZyzActivityApplyPublicParams params, HttpServletResponse response);

    /**
     * 获取群众参与人次
     * @return
     */
    Integer getJoinMassesNum(DataScreenParams params);

    IPage<ApplyPublicPageDto> pageMyApplyPublic(ZyzActivityApplyPublicParams params);
}

