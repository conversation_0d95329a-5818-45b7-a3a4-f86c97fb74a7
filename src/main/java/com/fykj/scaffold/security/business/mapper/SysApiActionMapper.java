package com.fykj.scaffold.security.business.mapper;

import com.fykj.scaffold.security.business.domain.entity.SysApiAction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 接口-资源映射表
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface SysApiActionMapper extends BaseMapper<SysApiAction> {

    /**
     * 按照按钮ID删除绑定接口
     *
     * @param actionId
     */
    void deleteByActionId(@Param("actionId") Long actionId);
}
