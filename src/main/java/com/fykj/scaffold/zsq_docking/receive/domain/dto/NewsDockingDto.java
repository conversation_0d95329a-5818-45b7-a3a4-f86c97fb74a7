package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新闻对接请求参数
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewsDockingDto extends BaseDockingDto {

    private static final long serialVersionUID = -8019862830334976337L;

    /**
     * 新闻id
     */
    @ApiModelProperty(value = "新闻id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long newsId;

    /**
     * 上下架状态
     */
    @ApiModelProperty(value = "上下架状态")
    private Boolean onShelf;
}
