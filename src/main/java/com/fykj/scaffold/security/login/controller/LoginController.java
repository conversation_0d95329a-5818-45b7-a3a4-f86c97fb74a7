package com.fykj.scaffold.security.login.controller;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.fas.FasOAuth2RequestDto;
import com.fykj.scaffold.fas.FykjSupport;
import com.fykj.scaffold.fas.FykjUser;
import com.fykj.scaffold.jjh_app.AppUserDto;
import com.fykj.scaffold.jjh_app.JJHAppUtil;
import com.fykj.scaffold.onenet.OneNetSupport;
import com.fykj.scaffold.onenet.OneNetToken;
import com.fykj.scaffold.onenet.OneNetUser;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.ICaptchaService;
import com.fykj.scaffold.security.business.service.IUserRoleService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.security.business.util.ZSQSyncUserUtils;
import com.fykj.scaffold.security.login.domain.dto.*;
import com.fykj.scaffold.security.login.util.CsrLoginUtils;
import com.fykj.scaffold.security.login.util.LoginRecordUtil;
import com.fykj.scaffold.security.login.util.ZsqLoginUtil;
import com.fykj.scaffold.security.login.conns.LoginTypeCons;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.conns.Oauth2AuthorizeCons;
import com.fykj.scaffold.support.utils.AesUtil;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import com.fykj.scaffold.weixin.mp.domain.dto.WxOpenCodeLoginResultDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;
import utils.UUIDUtils;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.text.StrPool.DASHED;
import static com.fykj.scaffold.support.conns.Cons.PFU_DECRYPT_KEY;
import static com.fykj.scaffold.support.conns.Oauth2AuthorizeCons.PASSWORD;
import static com.fykj.scaffold.support.conns.Oauth2AuthorizeCons.SMS_CODE;
import static com.fykj.scaffold.zyz.conns.ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD;
import static fykj.microservice.core.base.AbstractController.OK;
import static result.ResultCode.FORBIDDEN;


/**
 * 密码模式
 * 一般用于绝对信任的系统（如自己开发的系统登录）
 */
@RestController
@RequestMapping("/fykj")
@Api(tags = "/login")
@Slf4j
public class LoginController {

    private static final String ERROR_PASSWORD_TIMES = "error_password_times";
    @Autowired
    private ClientCredentialsResourceDetails clientCredentialsResourceDetails;
    @Autowired
    private ICaptchaService captchaService;
    @Autowired
    private TokenStore tokenStore;
    @Autowired
    private IUserService userService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IZyzVolunteerService volunteerService;
    @Autowired
    private IUserRoleService userRoleService;
    @Autowired
    private WxMpService wxMpService;
    @Autowired
    private FykjSupport fykjSupport;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private CsrLoginUtils csrLoginUtils;
    @Autowired
    private ZSQSyncUserUtils zsqSyncUserUtils;
    @Autowired
    private ZsqLoginUtil zsqLoginUtil;
    @Autowired
    private LoginRecordUtil loginRecordUtil;

    @ApiOperation("用户登录")
    @PostMapping(value = "/login")
    public Result login(@RequestBody @Validated OAuth2RequestDto dto) {
        dto.setPassword(AesUtil.decrypt(dto.getPassword(), Cons.PWD_DECRYPT_KEY));
        if (StringUtil.equals(dto.getLoginType(), "password") && !captchaService.validate(dto.getUuid(), dto.getCaptcha())) {
            return new Result(ResultCode.FAIL.code(), "图片验证码不正确");
        }
        Boolean csrLogin = dto.getCsrLogin();
        if (csrLogin != null && csrLogin) {
            return csrLogin(dto);
        }
        // 连续5次输错密码后锁定
        String userName = dto.getUsername();
        failCountCheck(userName, false);

        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add(Oauth2AuthorizeCons.GRANT_TYPE, dto.getLoginType());
        formData.add(Oauth2AuthorizeCons.USERNAME, dto.getUsername());
        formData.add(Oauth2AuthorizeCons.PASSWORD, dto.getPassword());
        formData.add(Oauth2AuthorizeCons.SCOPE, String.join("", clientCredentialsResourceDetails.getScope()));
        String clientId = dto.getClientId();
        if (StringUtil.isEmpty(clientId)) {
            clientId = clientCredentialsResourceDetails.getClientId();
        }
        formData.add(Oauth2AuthorizeCons.CLIENT_ID, clientId);
        formData.add(Oauth2AuthorizeCons.CLIENT_SECRET, clientCredentialsResourceDetails.getClientSecret());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        RestTemplate restTemplate = new RestTemplate();
        try {
            String tokenResult = restTemplate.exchange(clientCredentialsResourceDetails.getAccessTokenUri(),
                    HttpMethod.POST, new HttpEntity<>(formData, headers), String.class).getBody();
            //需要将openid绑定给用户
            if (StringUtil.isNotEmpty(dto.getUnionIdBindKey())) {
                bindUnionId(dto.getUsername(), dto.getUnionIdBindKey());
            }
            //清空登录失败次数
            resetRedis(userName);
            return new JsonResult<>(JSON.parse(tokenResult));
        } catch (HttpClientErrorException e) {
            //把SpringSecurity OAuth2的错误转换成我们封装的错误
            String responseBody = e.getResponseBodyAsString();
            if (!JSONUtil.isTypeJSON(responseBody)) {
                log.error("登录失败，应该是SpringSecurity内部问题,异常信息:{}", responseBody);
                throw new BusinessException(ResultCode.BAD_REQUEST, "登录失败，请联系管理员");
            }
            OAuth2ErrorDto errorDto = JSON.parseObject(e.getResponseBodyAsString(), OAuth2ErrorDto.class);
            // 连续5次输错密码后锁定
            if ("invalid_grant".equals(errorDto.getError())) {
                String errorDescription = errorDto.getError_description();
                if ("您已申请注销账号，账号被禁用！".equals(errorDescription)) {
                    throw new BusinessException(FORBIDDEN, errorDescription);
                } else if ("用户名或密码错误".equals(errorDescription) || "验证码错误,请重新输入".equals(errorDescription)){
                    setRedisValueAfterFail(userName);
                    throw new BusinessException(ResultCode.BAD_REQUEST, errorDescription);
                } else {
                    throw new BusinessException(ResultCode.BAD_REQUEST, errorDescription);
                }
            }
            throw new BusinessException(ResultCode.BAD_REQUEST, errorDto.getError_description());
        } catch (Exception e) {
            log.error("登录失败，登录接口抛出了通用异常", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "登录失败，请联系管理员");
        }
    }


    @ApiOperation("用户登录-刷新token")
    @PostMapping(value = "/refresh_token")
    public Result refreshToken(@RequestBody @Validated Oauth2RefreshTokenDto dto) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add(Oauth2AuthorizeCons.REFRESH_TOKEN, dto.getRefreshToken());
        formData.add(Oauth2AuthorizeCons.GRANT_TYPE, Oauth2AuthorizeCons.REFRESH_TOKEN);
        String clientId = dto.getClientId();
        if (StringUtil.isEmpty(clientId)) {
            clientId = clientCredentialsResourceDetails.getClientId();
        }
        formData.add(Oauth2AuthorizeCons.CLIENT_ID, clientId);
        formData.add(Oauth2AuthorizeCons.CLIENT_SECRET, clientCredentialsResourceDetails.getClientSecret());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        RestTemplate restTemplate = new RestTemplate();
        try {
            String tokenResult = restTemplate.exchange(clientCredentialsResourceDetails.getAccessTokenUri(),
                    HttpMethod.POST, new HttpEntity<>(formData, headers), String.class).getBody();
            return new JsonResult<>(JSON.parse(tokenResult));
        } catch (HttpClientErrorException e) {
            //把SpringSecurity OAuth2的错误转换成我们封装的错误
            String responseBody = e.getResponseBodyAsString();
            if (!JSONUtil.isTypeJSON(responseBody)) {
                log.error("刷新token失败，应该是SpringSecurity内部问题,异常信息:{}", responseBody);
                throw new BusinessException(ResultCode.BAD_REQUEST, "刷新token失败，请联系管理员");
            }
            OAuth2ErrorDto errorDto = JSON.parseObject(e.getResponseBodyAsString(), OAuth2ErrorDto.class);
            throw new BusinessException(ResultCode.BAD_REQUEST, errorDto.getError_description());
        } catch (Exception e) {
            log.error("刷新token失败，刷新token接口抛出了通用异常", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "刷新token失败，请联系管理员");
        }
    }

    @ApiOperation("小程序手机号登录")
    @PostMapping("/miniPhoneLogin")
    public Result miniPhoneLogin(String appid, String jsCode, String maJsCode) {
        final WxMaService wxMaService = WxMaConfiguration.getMaService(appid);
        try {
            WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxMaService.getUserService().getNewPhoneNoInfo(jsCode);
            String phone = wxMaPhoneNumberInfo.getPurePhoneNumber();
            Result result = phoneToOneCode(phone);
            if (maJsCode != null) {
                String maOpenId = wxMaService.jsCode2SessionInfo(maJsCode).getOpenid();
                userService.lambdaUpdate().set(User::getMaOpenId, maOpenId).eq(User::getMobile, phone).update();
            }
            return result;
        } catch (WxErrorException e) {
            log.error("手机号登录失败", e);
            return new Result(-1, "手机号登录失败，请联系管理员");
        }
    }

    @ApiOperation("一网通Pc登录")
    @GetMapping("/oneNetLogin")
    public Result oneNetLogin(String ticket) {
        try {
            OneNetToken token = OneNetSupport.getToken(ticket);
            OneNetUser user = OneNetSupport.getUser(token.getToken());
            return phoneToOneCode(user.getMobile());
        } catch (Exception e) {
            log.error("一网通登录失败", e);
            return new Result(-1, "一网通登录登录失败，请联系管理员");
        }
    }

    @ApiOperation("一网通小程序登录")
    @GetMapping("/oneNetLoginForMini")
    public Result oneNetLoginForMini(String token) {
        try {
            OneNetUser user = OneNetSupport.getUser(token);
            return phoneToOneCode(user.getMobile());
        } catch (Exception e) {
            log.error("一网通登录失败", e);
            return new Result(-1, "一网通登录登录失败，请联系管理员");
        }
    }

    @ApiOperation("门户网站手机号登录")
    @PostMapping("/webPhoneLogin")
    public Result webPhoneLogin(String phone, String smsCode) {
        if (StringUtil.isEmpty(phone)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请输入正确的手机号");
        }
        //连续输错5次账号锁定
        failCountCheck(phone, true);
        final String redisLoginSmsCodeKey = phone + Cons.SmsValidCodeTemplate.LOGIN.name();
        String realCode = redisService.get(redisLoginSmsCodeKey);
        if (StringUtil.isEmpty(realCode) || !realCode.equals(smsCode)) {
            setRedisValueAfterFail(phone);
            throw new BusinessException(ResultCode.BAD_REQUEST, "验证码错误，请重新输入");
        } else {
            redisService.remove(redisLoginSmsCodeKey);
            //清空登录失败次数
            resetRedis(phone);
        }
        // 记录手机号登录日志
        User user = userService.lambdaQuery().eq(User::getMobile, phone).one();
        if (user != null) {
            loginRecordUtil.recordLogin(user.getUsername(), LoginTypeCons.PHONE_LOGIN);
        }
        return phoneToOneCode(phone);
    }

    private void failCountCheck(String account, boolean pcLogin) {
        //连续输错5次账号锁定
        String loginFailCountKey = account.concat(ERROR_PASSWORD_TIMES);
        Integer errorTimes = redisService.get(loginFailCountKey);
        if (errorTimes != null && errorTimes == 5) {
            long expireTimes = redisService.getExpire(loginFailCountKey);
            throw new BusinessException(ResultCode.BAD_REQUEST, "由于您连续5次" + (pcLogin ? "输错验证码" : "输错密码（或验证码）") + "，请" + expireTimes + "秒后重试！");
        }
    }

    private void setRedisValueAfterFail(String account) {
        String loginFailCountKey = account.concat(ERROR_PASSWORD_TIMES);
        Integer errorTimes = redisService.get(loginFailCountKey);
        if (errorTimes == null) {
            redisService.set(loginFailCountKey, 1);
        } else if (errorTimes == 4) {
            redisService.set(loginFailCountKey, 5, 300l);
        } else {
            redisService.set(loginFailCountKey, errorTimes + 1);
        }
    }

    private void resetRedis(String account) {
        String loginFailCountKey = account.concat(ERROR_PASSWORD_TIMES);
        redisService.remove(loginFailCountKey);
    }

    @ApiOperation("使用开放平台授权登录")
    @GetMapping("/openCodeLogin")
    public Result openCodeLogin(@RequestParam String appId, @RequestParam String code) {
        WxOAuth2AccessToken accessToken;
        try {
            accessToken = wxMpService.switchoverTo(appId).getOAuth2Service().getAccessToken(code);
            String unionId = accessToken.getUnionId();
            User user = userService.getByUnionId(unionId);
            //此openId还未绑定用户，需要先用普通方式登录，然后绑定，先用一个uuid代替openid返给前端，让前端带着去走登录
            if (user == null) {
                final String redisUnionIdBindKey = Cons.REDIS_UNION_ID_BIND_KEY + UUID.fastUUID();
                redisService.set(redisUnionIdBindKey, unionId, 5 * 60);
                return new JsonResult<>(WxOpenCodeLoginResultDto.Unbind(redisUnionIdBindKey));
            }
            // 记录微信登录日志
            loginRecordUtil.recordLogin(user.getUsername(), LoginTypeCons.WECHAT_LOGIN);
            return new JsonResult<>(WxOpenCodeLoginResultDto.Success(user.getUsername(), userService.genOneCode(user.getUsername())));
        } catch (WxErrorException e) {
            log.error("code换取openid失败", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "code换取openid失败");
        }
    }

    private void bindUnionId(String username, String unionIdBindKey) {
        String unionId = redisService.get(unionIdBindKey);
        if (StringUtil.isEmpty(unionId)) {
            log.error("用户:{}登录成功，尝试绑定unionId失败，未能从redis查到unionId", username);
            return;
        }
        User u = userService.lambdaQuery().eq(User::getUsername, username).one();
        userService.updateUnionId(u.getId(), unionId);
    }

    @ApiOperation("获取图片验证码")
    @GetMapping("/captcha")
    @ApiImplicitParams(@ApiImplicitParam(name = "uuid", value = "图片验证码的唯一识别码"))
    public void captcha(@RequestParam String uuid, HttpServletResponse response) throws IOException {
        response.setHeader("Cache-Control", "no-store, no-cache");
        response.setContentType("image/jpeg");
        //获取图片验证码
        BufferedImage image = captchaService.getCaptcha(uuid);
        ServletOutputStream out = response.getOutputStream();
        ImageIO.write(image, "jpg", out);
        IoUtil.close(out);
    }

    @ApiOperation("退出登录")
    @GetMapping(value = "/logout")
    public Result logout(@RequestHeader HttpHeaders headers) {
        String value = headers.getFirst("Authorization");
        if (StringUtil.isEmpty(value) || !value.toLowerCase().startsWith(OAuth2AccessToken.BEARER_TYPE.toLowerCase())) {
            return new Result(ResultCode.ERROR);
        }
        String authHeaderValue = value.substring(OAuth2AccessToken.BEARER_TYPE.length()).trim();
        int commaIndex = authHeaderValue.indexOf(',');
        if (commaIndex > 0) {
            authHeaderValue = authHeaderValue.substring(0, commaIndex);
        }
        OAuth2AccessToken oAuth2AccessToken = tokenStore.readAccessToken(authHeaderValue);
        if (oAuth2AccessToken != null) {
            tokenStore.removeAccessToken(oAuth2AccessToken);
        }
        return new Result();
    }



    @ApiOperation("数字金鸡湖app登录")
    @GetMapping("/jjhAppLogin")
    public Result jjhAppLogin(String code) {
        try {
            //默认数字金鸡湖过来的都是实名认证的用户
            //Step 1 根绝code换取token
            String token = JJHAppUtil.getAccessToken(code);
            //Step 2 根绝token获取用户信息
            AppUserDto userDto = JJHAppUtil.getUserInfo(token);
            //Step 3 根据身份证判断志愿者是否存在
            ZyzVolunteer idVolunteer = null;
            if (StringUtil.isNotEmpty(userDto.getCertNo())) {
                idVolunteer = volunteerService.lambdaQuery().eq(ZyzVolunteer::getCertificateId, userDto.getCertNo())
                        .eq(ZyzVolunteer::getWriteOff, false)
                        .one();
            }
            ZyzVolunteer phoneVolunteer = volunteerService.getByPhone(userDto.getMobile());
            Map<String, Object> retMap;
            if (StringUtil.isNotEmpty(userDto.getCertNo()) && idVolunteer == null && phoneVolunteer != null) {
                return new Result(-1, "手机号码已在志愿者平台被其他人注册");
            }
            if (idVolunteer != null) {
                //用志愿者平台的手机号码登录
                retMap = phoneToOneCodeMap(idVolunteer.getPhone());
            } else {
                //如果还没有成为志愿者，我们返回前端一个统一的数据结构，供后续其他平台的对接
                idVolunteer = new ZyzVolunteer();
                idVolunteer.setName(userDto.getCertName());
                idVolunteer.setPhone(userDto.getMobile());
                idVolunteer.setCertificateType(CERTIFICATE_TYPE_CERTIFICATE_CARD);//自然人过来的都是身份证
                idVolunteer.setCertificateId(userDto.getCertNo());
                idVolunteer.setEmail(userDto.getMail());
                idVolunteer.setCertification(true);//自然人过来的默认已经实名
                retMap = phoneToOneCodeMap(userDto.getMobile());
                retMap.put("needCertification", false);//自然人过来的默认已经实名
            }
            //添加数字金鸡湖那边的用户信息给前端便于后续的注册
            retMap.put("zyzVolunteer", idVolunteer);
            return new JsonResult<>(retMap);
        } catch (Exception e) {
            log.error("数字金鸡湖登录失败", e);
            return new Result(-1, "数字金鸡湖登录失败，请联系管理员");
        }
    }

    private JsonResult phoneToOneCode(String phone) {
        Map<String, Object> retMap = phoneToOneCodeMap(phone);
        return new JsonResult<>(retMap);
    }

    private Map<String, Object> phoneToOneCodeMap(String phone) {
        ZyzVolunteer volunteer = volunteerService.getByPhone(phone);
        User user = userService.lambdaQuery().eq(User::getMobile, phone).one();
        if (user == null) {
            user = new User();
            user.setMobile(phone);
            user.setUsername(phone);
            user.setStatus(true);
            user.setPassword(UUIDUtils.generateUuid());
            user.setName(volunteer == null ? "用户" + RandomUtil.randomString(5) : volunteer.getName());
            user.setRoleCodes(CollectionUtil.newArrayList(Cons.RoleCode.ROLE_CODE_VOLUNTEER));
            user.setForceUpdatePwd(Boolean.TRUE);
            userService.save(user);
        } else {
            userRoleService.addRole(user.getId(), Cons.RoleCode.ROLE_CODE_VOLUNTEER);
        }
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("username", user.getUsername());
        retMap.put("oneCode", userService.genOneCode(user.getUsername()));
        retMap.put("needRegister", volunteer == null);
        retMap.put("needCertification", volunteer == null || !volunteer.getCertification());
        retMap.put("needPerfect", volunteer == null || !volunteer.getPerfect());
        retMap.put("mobile", phone);
        return retMap;
    }

    @ApiOperation("根据token获取用户名")
    @GetMapping(value = "/getUserName")
    private JsonResult<String> getUserName(String token){
        OAuth2Authentication authentication = tokenStore.readAuthentication(token);
        String userName = authentication.getName();
        return new JsonResult<>(userName);
    }

    @ApiOperation("根据手机号码获取onecode ")
    @GetMapping(value = "/userNameToOneCode")
    private JsonResult userNameToOneCode(String phone) {
        Map<String, Object> retMap = phoneToOneCodeMap(phone);
        return new JsonResult<>(retMap);
    }

    @ApiOperation("FAS用户登录")
    @PostMapping(value = "/fasLogin")
    public Result fasLogin(@RequestBody @Validated FasOAuth2RequestDto dto) {

        FykjUser fykjUser = fykjSupport.getUserInfo(dto.getCode());
        if (fykjUser == null || StringUtil.isEmpty(fykjUser.getUsername())) {
            throw new BusinessException(ResultCode.FAIL, "抱歉，您没有访问此应用的权限，若需申请访问权限，请您联系应用管理员。");
        }
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add(Oauth2AuthorizeCons.GRANT_TYPE, "password");
        formData.add(Oauth2AuthorizeCons.USERNAME,"AdminFas123");
        formData.add(Oauth2AuthorizeCons.PASSWORD,"AdminFas123@#");
        formData.add(Oauth2AuthorizeCons.SCOPE, String.join("", clientCredentialsResourceDetails.getScope()));
        String clientId = dto.getClientId();
        if (StringUtil.isEmpty(clientId)) {
            clientId = clientCredentialsResourceDetails.getClientId();
        }
        formData.add(Oauth2AuthorizeCons.CLIENT_ID, clientId);
        formData.add(Oauth2AuthorizeCons.CLIENT_SECRET, clientCredentialsResourceDetails.getClientSecret());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        RestTemplate restTemplate = new RestTemplate();
        try {
            String tokenResult = restTemplate.exchange(clientCredentialsResourceDetails.getAccessTokenUri(),
                    HttpMethod.POST, new HttpEntity<>(formData, headers), String.class).getBody();

            return new JsonResult<>(JSON.parse(tokenResult));
        } catch (HttpClientErrorException e) {
            //把SpringSecurity OAuth2的错误转换成我们封装的错误
            String responseBody = e.getResponseBodyAsString();
            if (!JSONUtil.isTypeJSON(responseBody)) {
                log.error("登录失败，应该是SpringSecurity内部问题,异常信息:{}", responseBody);
                throw new BusinessException(ResultCode.BAD_REQUEST, "登录失败，请联系管理员");
            }
            OAuth2ErrorDto errorDto = JSON.parseObject(e.getResponseBodyAsString(), OAuth2ErrorDto.class);
            throw new BusinessException(ResultCode.BAD_REQUEST, errorDto.getError_description());
        } catch (Exception e) {
            log.error("登录失败，登录接口抛出了通用异常", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "登录失败，请联系管理员");
        }
    }

    @ApiOperation("忘记密码修改密码校验")
    @PostMapping(value = "/passwordForgetValidate")
    public JsonResult<String> passwordForgetValidate(@RequestBody PasswordForgetValidateDto pfvDto) {
        String username = pfvDto.getUsername();
        String mobile = pfvDto.getMobile();
        String vCode = pfvDto.getSmsVerificationCode();
        if (StringUtil.isEmpty(username) || StringUtil.isEmpty(mobile) || StringUtil.isEmpty(vCode)) {
            throw new BusinessException(ResultCode.FAIL, "校验参数异常！");
        }
        List<User> exists = userService.lambdaQuery().eq(User::getUsername, username).eq(User::getMobile, mobile).list();
        if (exists == null || exists.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "用户不存在！");
        }
        if (exists.size() > 1) {
            throw new BusinessException(ResultCode.FAIL, "当前用户名、手机号对应多个用户，请联系管理员！");
        }
        String key = mobile + Cons.SmsValidCodeTemplate.PWD_FORGET_UPDATE.name();
        if(redisService.exists(key)){
            String data = redisService.get(key);
            if(vCode.equalsIgnoreCase(data)){
                redisService.remove(key);
            }else{
                throw new BusinessException(ResultCode.FAIL, "验证码错误");
            }
        }else{
            throw new BusinessException(ResultCode.FAIL, "短信验证码已过期，请重新获取验证码！");
        }
        User user = exists.get(0);
        String uuid = java.util.UUID.randomUUID().toString();
        redisService.set(String.join(DASHED, uuid, "pf-user"), JSONObject.toJSONString(user), 150);
        return new JsonResult<>(uuid);
    }

    @ApiOperation("忘记密码修改密码")
    @PostMapping(value = "/passwordForgetUpdate")
    public Result passwordForgetUpdate(@RequestBody PasswordForgetUpdateDto pfuDto) {
        String username = pfuDto.getUsername();
        String mobile = pfuDto.getMobile();
        String encryptedData = pfuDto.getData();
        if (StringUtil.isEmpty(username) || StringUtil.isEmpty(mobile) || StringUtil.isEmpty(encryptedData)) {
            log.info("username: {}, mobile: {}, encryptedData: {}", username, mobile, encryptedData);
            throw new BusinessException(ResultCode.FAIL, "参数异常！");
        }
        String decryptData = AesUtil.decrypt(encryptedData, PFU_DECRYPT_KEY);
        JSONObject jsonObject = JSONObject.parseObject(decryptData);
        String uuid = jsonObject.getString("uuid");
        String usernameForValidate = jsonObject.getString("username");
        String mobileForValidate = jsonObject.getString("mobile");
        String newPassword = jsonObject.getString("password");
        Long timestamp = jsonObject.getLong("timestamp");
        Long currentMs = System.currentTimeMillis();
        if ((currentMs - timestamp) / 1000.00 > 30) {
            log.info("timestamp: {}, currentMs: {}, interval: {}", timestamp, currentMs, (currentMs - timestamp) / 1000.00);
            throw new BusinessException(ResultCode.FAIL, "参数异常！");
        }
        if (StringUtil.isEmpty(uuid) || StringUtil.isEmpty(usernameForValidate) || StringUtil.isEmpty(mobileForValidate) || StringUtil.isEmpty(newPassword)) {
            log.info("uuid: {}, usernameForValidate: {}, mobileForValidate: {}, newPassword：{}", uuid, usernameForValidate, mobileForValidate, newPassword);
            throw new BusinessException(ResultCode.FAIL, "参数异常！");
        }
        if (!username.equals(usernameForValidate) || !mobile.equals(mobileForValidate)) {
            log.info("username：{}, usernameForValidate: {}, mobile：{}, mobileForValidate: {}", username, usernameForValidate, mobile, mobileForValidate);
            throw new BusinessException(ResultCode.FAIL, "参数异常！");
        }
        String key = String.join(DASHED, uuid, "pf-user");
        if(redisService.exists(key)){
            String data = redisService.get(key);
            User user = JSONObject.parseObject(data, User.class);
            String usernameInRedis = user.getUsername();
            String mobileInRedis = user.getMobile();
            if (!username.equals(usernameInRedis) || !mobile.equals(mobileInRedis)) {
                log.info("username：{}, usernameInRedis: {}, mobile：{}, mobileInRedis: {}", username, usernameInRedis, mobile, mobileInRedis);
                redisService.remove(key);
                throw new BusinessException(ResultCode.FAIL, "参数异常！");
            }
            userService.lambdaUpdate().eq(User::getId, user.getId())
                    .set(User::getPassword, passwordEncoder.encode(newPassword))
                    .set(User::getLastUpdatePwdDate, LocalDate.now())
                    .update();
        }else{
            throw new BusinessException(ResultCode.FAIL, "修改异常，请在2分钟内完成密码修改！");
        }
        return OK;
    }

    private Result csrLogin(OAuth2RequestDto dto) {
        String loginType = dto.getLoginType();
        String mobile;
        if (SMS_CODE.equals(loginType)) {
            mobile = dto.getUsername();
            final String redisLoginSmsCodeKey = mobile + Cons.SmsValidCodeTemplate.LOGIN.name();
            String realCode = redisService.get(redisLoginSmsCodeKey);
            String smsCode = dto.getPassword();
            if (StringUtil.isEmpty(realCode) || !realCode.equals(smsCode)) {
                throw new InvalidGrantException("验证码错误,请重新输入");
            } else {
                redisService.remove(redisLoginSmsCodeKey);
            }
        } else if (PASSWORD.equals(loginType)) {
            String username = dto.getUsername();
            User user = userService.lambdaQuery().eq(User::getUsername, username).one();
            if (user == null) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "用户不存在");
            }
            String loginPwd = dto.getPassword();
            String userPwd = user.getPassword();
            if (!passwordEncoder.matches(loginPwd, userPwd)) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "密码错误");
            }
            mobile = user.getMobile();
        } else {
            throw new BusinessException(ResultCode.BAD_REQUEST, "仅支持从志愿者平台通过手机号验证码/用户名密码登录企业责任联盟！");
        }
        if (StringUtil.isEmpty(mobile)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到用户手机号");
        }
        JSONObject oneCodeInfo = csrLoginUtils.httpRequestForOneCode(mobile);
        JSONObject objInfo = oneCodeInfo.getJSONObject("obj");
        if (objInfo == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "企业责任联盟登录信息获取异常！");
        }
        Boolean needRegister = objInfo.getBoolean("needRegister");
        String oneCode = objInfo.getString("oneCode");
        String username = objInfo.getString("username");
        if (needRegister != null && needRegister) {
            throw new BusinessException(ResultCode.NOT_FOUND, "您在企业责任联盟平台尚未注册企业，请先注册！");
        }
        if (needRegister != null && StringUtil.isNotEmpty(oneCode) && StringUtil.isNotEmpty(username)) {
            return csrLoginUtils.login(username, oneCode);
        }
        throw new BusinessException(ResultCode.BAD_REQUEST, "企业责任联盟登录信息获取异常！");
    }


    @ApiOperation("志愿者系统用户手机号登录")
    @GetMapping("/zsqCodeToOneCode")
    public Result oneNetCodeToOneCode(@RequestParam String code) {
        String authToken = zsqLoginUtil.getAuthToken();
        // 1. 使用code获取token
        String token = zsqLoginUtil.getTokenByCode(authToken, code);
        log.info("zsqCodeToOneCode: token = {}", token);

        // 2. 使用token获取用户信息
        cn.hutool.json.JSONObject userInfo = zsqLoginUtil.getUserInfoByToken(authToken, token);
        log.info("zsqCodeToOneCode: userInfo = {}", JSON.toJSONString(userInfo));

        // 3. 根据用户信息获取oneCode
        String mobile = userInfo.getStr("mobile");
        if (StringUtil.isEmpty(mobile)) {
            throw new BusinessException(ResultCode.FAIL, "知社区用户手机号未知！");
        }
        Map<String, String> result = zsqLoginUtil.phoneToOneCodeMap(mobile);
        log.info("zsqCodeToOneCode: result = {}", JSON.toJSONString(result));

        return new JsonResult<>(result);
    }
}
