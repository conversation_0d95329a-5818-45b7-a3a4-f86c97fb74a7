package com.fykj.scaffold.mall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.ExchangeDRExportDto;
import com.fykj.scaffold.mall.domain.entity.MallExchangeDistributionReport;
import com.fykj.scaffold.mall.domain.params.MallExchangeDistributionReportParams;
import com.fykj.scaffold.mall.mapper.MallExchangeDistributionReportMapper;
import com.fykj.scaffold.mall.service.IMallExchangeDistributionReportService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2023-06-05
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallExchangeDistributionReportServiceImpl extends BaseServiceImpl<MallExchangeDistributionReportMapper, MallExchangeDistributionReport> implements IMallExchangeDistributionReportService {


    @Override
    public IPage<MallExchangeDistributionReport> getPage(MallExchangeDistributionReportParams params) {
        if (params == null) {
            params = new MallExchangeDistributionReportParams();
        }
        IPage<MallExchangeDistributionReport> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public List<ExchangeDRExportDto> listForPage(MallExchangeDistributionReportParams params) {
        if (params == null) {
            params = new MallExchangeDistributionReportParams();
        }
        return baseMapper.getPage(params).stream().map(it -> {
            ExchangeDRExportDto dto = new ExchangeDRExportDto();
            BeanUtils.copyProperties(it, dto);
            return dto;
        }).collect(Collectors.toList());
    }
}