package com.fykj.scaffold.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.capability.domain.dto.UserCreditsDetailDTO;
import com.fykj.scaffold.capability.domain.entity.UserCredits;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户学分管理 Mapper 接口
 * 表对应：cu_user_credits
 * 2025/2/26
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface UserCreditsMapper extends BaseMapper<UserCredits> {
    UserCreditsDetailDTO getUserCredits(@Param("userId") Long userId);

}
