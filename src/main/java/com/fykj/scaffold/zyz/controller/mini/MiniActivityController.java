package com.fykj.scaffold.zyz.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.MyActivityApiDto;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityParams;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/mini/activity")
@Api(tags = "微信小程序手册活动接口--需要登录")
public class MiniActivityController {
    @Autowired
    private IZyzActivityService activityService;
    @ApiOperation("分页查询我收藏的活动")
    @PostMapping({"/pageForMyApi"})
    public JsonResult<IPage<MyActivityApiDto>> pageForMyApi(@RequestBody ZyzActivityParams params) {
        IPage<MyActivityApiDto> result = activityService.pageForMyApi(params);
        return new JsonResult<>(result);
    }
}
