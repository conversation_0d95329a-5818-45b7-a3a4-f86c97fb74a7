<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzBlackListMapper">

    <select id="getMeetsRequirementsBlackList" resultType="com.fykj.scaffold.zyz.domain.dto.BlackListTaskDto">
        select volunteer_id, count(*) as count,volunteer_name as name,volunteer_phone as mobile
        from zyz_activity_apply
        where sign_time is null
          and time_period_end_time
            >#{endTime}
          AND audit_status='act_apply_audit_success'
          and is_deleted = 0
          and volunteer_id not in
            (select volunteer_id FROM zyz_black_list where cancel_time
            > now()
          AND is_deleted = 0)
        group by volunteer_id, volunteer_name, volunteer_phone
        having count >=#{count}


    </select>
    <select id="getBlackByVolunteerId" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzBlackList">
        select *
        from zyz_black_list
        where cancel_time
            > now()
          AND is_deleted = 0
          and volunteer_id = #{volunteerId}
        ORDER BY cancel_time desc limit 1
    </select>
</mapper>