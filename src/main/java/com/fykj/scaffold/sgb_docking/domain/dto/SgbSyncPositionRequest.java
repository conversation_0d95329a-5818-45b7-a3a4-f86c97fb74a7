package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 苏州社工部志愿阵地同步请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbSyncPositionRequest {

    /**
     * 唯一号 - 对接方唯一标识
     */
    private String uniCode;

    /**
     * 志愿阵地名称
     */
    private String name;

    /**
     * 阵地类型 - 字典类型 position_type
     */
    private String typeId;

    /**
     * 区划编号
     */
    private String regionCode;

    /**
     * 详情地址
     */
    private String mapAddress;

    /**
     * 腾讯坐标-经度
     */
    private String txMapLan;

    /**
     * 腾讯坐标-纬度
     */
    private String txMapLat;

    /**
     * 负责人姓名
     */
    private String contact;

    /**
     * 负责人电话
     */
    private String contactNo;

    /**
     * 工作时间
     */
    private String workTime;

    /**
     * 介绍
     */
    private String intro;

    /**
     * 阵地制度
     */
    private String systemDesc;

    /**
     * 志愿阵地logo图片 - base64编码
     */
    private String picLogoUrl;

    /**
     * 接口调用类型 - 1新增，2修改
     */
    private String saveType;

    /**
     * 阵地编号 - saveType为2时必填
     */
    private String bpId;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncPositionInfo info = new SyncPositionInfo();
        info.setInfo(this);
        return JSON.toJSONString(info);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncPositionInfo {
        private SgbSyncPositionRequest info;
    }
}
