<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityTimePeriodMapper">

    <delete id="removeByActivityId">
        delete
        from zyz_activity_time_period
        where activity_id = #{activityId}
    </delete>
    <select id="selectByIdForUpdate" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod">
        SELECT *
        FROM zyz_activity_time_period
        WHERE is_deleted = 0
          AND id = #{timePeriodId} FOR UPDATE
    </select>
    <select id="miniTimePeriodPage" resultType="com.fykj.scaffold.zyz.domain.dto.MiniActivityTimePeriodDto">
        select p.*,
               p.id                                                                               as time_period_id,
               case when(timestampdiff(MINUTE, p.end_time, now()))>= 0 then 1 else 0 end          as is_end
               <if test ="params.volunteerId != null">
              ,aa.audit_status                                                                    as apply_audit_status,
               aa.id                                                                              as apply_id,
               case when aa.sign_time is null then false else true end                            as is_sign,
               case when
                    aa.audit_status in ('act_apply_audit_success','act_apply_wait_audit') then true
                    else false end
                                                                                                  as is_attend
               </if>
               <if test ="params.volunteerId == null">
              ,false                                                                              as is_sign,
               null                                                                               as apply_id,
               false                                                                              as is_attend
               </if>
        <if test ="params.recruitTargets == 'rt_volunteer' ">
            ,case when a.recruit_num_distinguish  then p.volunteer_apply_num else p.apply_num end                            as applyNum,
            case when a.recruit_num_distinguish  then p.volunteer_recruit_num else p.recruitment_num end                            as recruitmentNum
        </if>
        <if test ="params.recruitTargets == 'rt_masses' ">
            ,case when a.recruit_num_distinguish  then p.masses_apply_num else p.apply_num end                            as applyNum,
            case when a.recruit_num_distinguish  then p.masses_recruit_num else p.recruitment_num end                            as recruitmentNum
        </if>
        from zyz_activity_time_period p
        left join zyz_activity a on p.activity_id = a.id
        <if test ="params.volunteerId != null">
            left join zyz_activity_apply aa on aa.time_period_id = p.id and aa.volunteer_id = #{params.volunteerId}
        </if>
        where p.activity_id = #{params.activityId} and p.is_deleted =false and p.web_show = true
        order by is_end asc ,p.start_time asc
    </select>

    <select id="getNeedSyncRecords" resultType="java.lang.Long">
        SELECT tp.id
        FROM zyz_activity_time_period tp
                 LEFT JOIN (SELECT id FROM zyz_activity WHERE is_deleted = 0 AND audit_status = #{actStatus}) a ON tp.activity_id = a.id
        WHERE tp.is_deleted = 0
          AND tp.end_time &lt;= #{current}
          AND tp.end_time &gt;= date_sub(now(),interval 15 day)
          AND (
                    tp.sync = #{waitSyncStatus}
                OR (
                                tp.sync = #{syncFailStatus} AND tp.end_time &gt;= #{threeDaysBeforeNow}
                        )
            )
          AND a.id IS NOT NULL
    </select>

    <select id="getSgbNeedSyncRecords" resultType="java.lang.Long">
        SELECT tp.id
        FROM zyz_activity_time_period tp
        LEFT JOIN (
            SELECT id FROM zyz_activity WHERE is_deleted = 0 AND audit_status = #{actStatus}
        ) a ON tp.activity_id = a.id
        LEFT JOIN sgb_sync_status_record ssr
                  ON ssr.business_type = 'sgb_sync_biz_activity_recruit'
                      AND ssr.business_id = tp.id
                      AND ssr.is_deleted = 0
        WHERE tp.is_deleted = 0
          AND tp.end_time &lt;= #{current}
          AND tp.end_time &gt;= #{threeDaysBeforeNow}
          AND (
                ssr.id IS NULL
                OR ssr.sync_status = 0
              )
          AND a.id IS NOT NULL
    </select>

    <select id="getNeedRecruitSyncRecords" resultType="java.lang.Long">
        SELECT tp.id
        FROM zyz_activity_time_period tp
        LEFT JOIN (SELECT id,sync_id FROM zyz_activity WHERE is_deleted = 0 AND audit_status = #{actStatus}) a ON tp.activity_id = a.id
        WHERE tp.is_deleted = 0
        <if test ="actSyncByTimePeriod !=null and actSyncByTimePeriod==true ">
            AND tp.sync_id IS NOT NULL
        </if>
        <if test ="actSyncByTimePeriod !=null and actSyncByTimePeriod==false ">
            AND a.sync_id IS NOT NULL
        </if>
          AND ( tp.end_time &lt;= #{current})
          AND tp.end_time &gt;= date_sub(now(),interval 15 day)
          AND (
            tp.recruit_sync = #{waitSyncStatus}
            OR (
                tp.recruit_sync = #{syncFailStatus} AND tp.end_time &gt;= #{threeDaysBeforeNow}
                )
            )
          AND a.id IS NOT NULL
    </select>

    <select id="getNeedSettlementRecords" resultType="java.lang.Long">
        SELECT tp.id
        FROM zyz_activity_time_period tp
        LEFT JOIN (SELECT id FROM zyz_activity WHERE is_deleted = 0 AND audit_status = #{actStatus}) a ON tp.activity_id = a.id
        WHERE tp.is_deleted = 0
          AND tp.end_time &lt;= #{current}
          AND tp.end_time &gt;= #{fifteenDaysBeforeNow}
          AND a.id IS NOT NULL
    </select>

    <select id="getNeedPSSync" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod">
        SELECT *
        FROM zyz_activity_time_period tp
        LEFT JOIN zyz_activity a ON tp.activity_id = a.id
        WHERE tp.is_deleted = 0
        AND tp.end_time &lt; NOW()
        AND tp.end_time &gt;= date_sub(NOW(), INTERVAL 15 DAY)
        AND a.docking_type LIKE CONCAT('%', CONCAT('zyz_activity_docking_type_resource', '%'))
        AND a.docking_type LIKE CONCAT('%', CONCAT('zyz_activity_docking_type_requirement', '%'))
        AND tp.sync = 'sync_success'
    </select>
</mapper>
