package com.fykj.scaffold.sync.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDictAll;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 市平台地域数据字典_所有的（苏州大市）
 *
 * Mapper 接口
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ZyzSyncRegionDictAllMapper extends BaseMapper<ZyzSyncRegionDictAll> {
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
}
