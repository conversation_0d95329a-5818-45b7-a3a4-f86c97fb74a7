package com.fykj.scaffold.zsq_docking.log.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 知社区对接推送数据信息表
 *
 * <AUTHOR> @email ${email}
 * @date 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zsq_docking_log")
public class ZSQDockingLog extends BaseEntity {

    private static final long serialVersionUID = 8246139524784161023L;

    /**
     * 数据对接记录 id"
     */
    @TableField("docking_record_id")
    @ApiModelProperty(value = "数据对接记录 id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dockingRecordId;

    /**
     * 知社区对接推送数据类型（新闻创建/新闻编辑/活动创建/活动编辑...）
     */
    @TableField("docking_type")
    @ApiModelProperty(value = "对接知社区类型（数据推送：push；数据接收：receive）")
    private String dockingType;

    /**
     * 数据mq消息id（幂等）（数据推送）
     */
    @TableField("docking_push_id")
    @ApiModelProperty(value = "数据mq消息id（幂等）（数据推送）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dockingPushId;

    /**
     * 数据类型（新闻创建/新闻编辑/活动创建/活动编辑...）
     */
    @TableField("docking_data_type")
    @ApiModelProperty(value = "数据类型（新闻创建/新闻编辑/活动创建/活动编辑...）")
    private String dockingDataType;

    /**
     * 数据唯一id
     */
    @TableField("docking_data_id")
    @ApiModelProperty(value = "数据唯一id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dockingDataId;

    /**
     * 数据概要
     */
    @TableField("docking_summary")
    @ApiModelProperty(value = "数据概要")
    private String dockingSummary;

    /**
     * 对接时间
     */
    @TableField("docking_time")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @ApiModelProperty(value = "对接时间")
    private LocalDateTime dockingTime;

    /**
     * 对接结果
     */
    @TableField("docking_result")
    @ApiModelProperty(value = "对接结果")
    private Boolean dockingResult;

    /**
     * 对接失败信息
     */
    @TableField("docking_fail_msg")
    @ApiModelProperty(value = "对接失败信息")
    private String dockingFailMsg;

    /**
     * 对接请求数据
     */
    @TableField("docking_request_data")
    @ApiModelProperty(value = "对接请求数据")
    private String dockingRequestData;

    /**
     * 对接响应数据
     */
    @TableField("docking_response_data")
    @ApiModelProperty(value = "对接响应数据")
    private String dockingResponseData;
}
