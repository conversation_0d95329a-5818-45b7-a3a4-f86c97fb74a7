package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.dto.ApiListDto;
import com.fykj.scaffold.security.business.domain.dto.CascaderDto;
import com.fykj.scaffold.security.business.domain.entity.SysApi;
import com.fykj.scaffold.security.business.domain.params.SysApiParams;
import com.fykj.scaffold.security.business.service.ISysApiService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 系统资源-API接口
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@RestController
@RequestMapping("/admin/sys_api")
public class SysApiController extends BaseController<ISysApiService, SysApi,SysApiParams> {

    @GetMapping("/bind_list")
    @ApiOperation("获取接口绑定列表")
    public JsonResult<List<ApiListDto>> bindList() {
        List<ApiListDto> apiList = baseService.bindList();
        return new JsonResult<>(apiList);
    }
}
