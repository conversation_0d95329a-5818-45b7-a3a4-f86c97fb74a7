package com.fykj.volunteer.sync.util.api;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMember;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMemberResult;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActp;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActpResult;
import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActs;
import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActsResult;
import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTime;
import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTimeResult;
import lombok.extern.slf4j.Slf4j;

import java.io.FileWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 仅供调试，不要瞎搞！
 */
@Slf4j
public class ApiWebTest {
//    public static void main(String[] args) {
//        SyncTeam syncTeam = new SyncTeam();
//        syncTeam.setOrgName("啄木团队");
//        syncTeam.setIdCode("");
//        syncTeam.setDpId("13977");
//        syncTeam.setRegionCode("320508");
//        syncTeam.setDescs("啄木鸟团队2");
//        syncTeam.setLinkName("薛伟");
//        syncTeam.setLinkPhone("15190049162");
//        syncTeam.setUniqueCode("2630499452225978369");
//        syncTeam.setPicLogoUrl(Base64.encode("https://zyz.sipac.gov.cn/Content/images/zyzqr_logo.jpg"));
//        syncTeam.setSaveType(SyncTeam.saveType.ADD);
//        SyncTeamResult result = ApiWebService.syncTeam(syncTeam);
//    }

//    public static void main(String[] args) {
//        JoinTeam join = new JoinTeam();
//        join.setIdcode("320586199508082936");
//        join.setIsfulltime("是");
//        join.setJointime(LocalDateTimeUtil.formatDate(LocalDate.now()));
//        join.setOrgid("52C1018F0E174557AF587DA228B9DC87");
//        join.setUniquecode(String.valueOf("1630519799759417346"));
//        JoinTeamResult result = ApiWebService.joinTeam(join);
//    }

//    public static void main(String[] args) {
//        SyncCms syncBase = new SyncCms();
//        syncBase.setCategoryName("园区文明办");
//        syncBase.setUniqueCode("1001");
//        syncBase.setIdcode("3205080012");
//        syncBase.setInputDate("2023-02-28 14:20:20");
//        syncBase.setKeywords("0512-66686607");
//        syncBase.setSource("文明办");
//        syncBase.setTitle("文明办");
//        syncBase.setContent("<div class=\"article_img\" style=\"box-sizing: border-box; padding: 0px; margin: 50px 0px; text-align: center;\"><img style=\"box-sizing: border-box; padding: 0px; margin: 20px 0px 0px; border: 0px; vertical-align: middle; width: 578px; height: 354px;\" src=\"http://sip-volunteer.fyxmt.com/Upload/News/1b4bf6ef6b804499b273cc9813e48131.jpg\" alt=\"\" data-bd-imgshare-binded=\"1\" /></div>\n" +
//                "<div style=\"box-sizing: border-box; padding: 0px; margin: 0px;\">\n" +
//                "<p style=\"box-sizing: border-box; padding: 0px; margin: 0px 0px 10px; text-indent: 2em;\">&nbsp;</p>\n" +
//                "<p style=\"box-sizing: border-box; padding: 0px; margin: 0px 0px 10px; text-indent: 2em;\">带着精油芳香的香薰牌，不仅气味迷人，还可安神怡情，治愈身心。为了丰富社区青少年寒假生活，近日，淞涛社区组织开展了&ldquo;巧手制香 共赴风雅&rdquo;古法香薰石牌DIY活动。</p>\n" +
//                "<p style=\"box-sizing: border-box; padding: 0px; margin: 0px 0px 10px; text-indent: 2em;\">活动一开始，志愿者老师通过讲解带着孩子们进入了熏香的历史，焚香大约早在春秋时代就开始了，到了唐朝，熏香文化与茶文化相提并论，宋词中就有&ldquo;茶经香传&rdquo;之称，由此可见香薰的悠久历史和独特魅力。随后，老师拿着香薰石膏牌的原料，向大家详细地讲解了操作步骤。接着，孩子们迫不及待的挑选了自己喜欢的石膏牌形状和香薰，搭配着五颜六色的干花，开始了古法香薰石牌的制作。近一个小时后，一份份独特的、充满创意的香薰石膏牌制作完工了。活动结束前，孩子们纷纷拿起自己亲手精心制作的香薰石膏牌合影留念，留下了一份美好的回忆。</p>\n" +
//                "<p style=\"box-sizing: border-box; padding: 0px; margin: 0px 0px 10px; text-indent: 2em;\">各色各样的香薰石膏牌，把孩子们对美好未来的憧憬和想象融在一起，以一种更深沉的方式长存。此次活动，不仅陶冶了孩子们的情操,也让他们感受到了手工制作的乐趣和传统香薰文化的魅力所在。</p>\n" +
//                "</div>");
//        syncBase.setPicLogoUrl(Base64.encode("https://zyz.sipac.gov.cn/Content/images/zyzqr_logo.jpg"));
//        syncCmsCBC(syncBase);
//    }


    //发布资源调试内容
//    public static void main(String[] args) {
//        SyncRes syncRes = new SyncRes();
//        syncRes.setPubId("342623199509178951");
//        syncRes.setUniCode("102034");
//        syncRes.setPicLogoUrl(Base64.encode("https://zyz.sipac.gov.cn/Content/images/zyzqr_logo.jpg"));
//        syncRes.setContact("薛伟");
//        syncRes.setContact("园区文明办");
//        syncRes.setPubType("2");
//        syncRes.setResDate("一年");
//        syncRes.setResName("园区文明办");
//        syncRes.setContactNo("15190049162");
//        syncRes.setResType("14");
//        syncRes.setSaveType("1");
//        syncRes(syncRes);
//    }

//    public static void main(String[] args) throws Exception{
//        FileWriter fw = new FileWriter("/Users/<USER>/Desktop/act_sync_log_Maker测试活动同步四.txt");
//        log.info("活动相关同步开始：{}", LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- 活动相关同步开始\r\n");
//        String apId = syncActTest(fw);
//        List<String> arIds = syncActRecruitTest(fw, apId);
//        syncActMemberTest(fw, arIds);
//        syncServiceTimeTest(fw, arIds);
//        log.info("活动相关同步结束：{}", LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- 活动相关同步结束");
//        fw.close();
//    }
//
//    //    活动注册调试内容
//    private static String syncActTest(FileWriter fw) throws Exception{
//        SyncActp syncActp = new SyncActp();
//        syncActp.setUniCode("123-456-789-004");
//        syncActp.setPicLogoUrl(Base64.encode("https://imgs0.zupu.cn//ueditor/2021/07/01/2b7a03f0-1aa9-45ef-8fdc-2981defd12d2.png"));
//        syncActp.setName("Maker测试活动同步四");
//        syncActp.setAddress("江苏省苏州市工业园区金鸡湖大道国际科技园3期科技广场");
//        syncActp.setOrgId(Cons.SyncConstant.AS_ORG_ID);
//        syncActp.setRecordWay(SyncActp.recordWay.TIME_SLOT);
//        syncActp.setDescs("简介：Maker测试活动同步四");
//        syncActp.setEndDate("2023-07-31");
//        syncActp.setStartTime("00:01");
//        syncActp.setStartDate("2023-06-01");
//        syncActp.setEndTime("23:59");
//        syncActp.setServiceField("78802");
//        syncActp.setServiceTime(null);
//        syncActp.setTxMapLan("120.010101");
//        syncActp.setTxMapLat("23.010101");
//        syncActp.setRegionCode(Cons.SyncConstant.AS_REGION_CODE);
//        syncActp.setLinkName("Maker");
//        syncActp.setLinkPhone("18851893119");
//        syncActp.setServiceTarget("99");
//        SyncActpResult syncActpResult = ApiWebService.syncActivity(syncActp);
//        log.info("活动同步数据：{}", JSONUtil.toJsonStr(syncActp));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动同步数据：" + JSONUtil.toJsonStr(syncActp) + "\r\n");
//        log.info("活动同步结果：{}", JSONUtil.toJsonStr(syncActpResult));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动同步结果：" + JSONUtil.toJsonStr(syncActpResult) + "\r\n");
//        log.info("活动同步结果信息：{}，活动同步id：{}", syncActpResult.getMsg(), syncActpResult.getApId());
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动同步结果信息：" + syncActpResult.getMsg() + "\r\n");
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动同步id：" + syncActpResult.getApId() + "\r\n");
//        return syncActpResult.getApId();
//    }
//
//    // 发布活动招募
//    private static List<String> syncActRecruitTest(FileWriter fw, String apId) throws Exception{
//        List<String> arId = new ArrayList<>();
//        SyncActs syncActs1 = new SyncActs();
//        syncActs1.setUniCode("123-456-789-004");
//        syncActs1.setApId(apId);
//        syncActs1.setActDate("2023-06-01");
//        syncActs1.setStartTime("00:01");
//        syncActs1.setEndTime("23:59");
//        SyncActsResult syncActsResult1 = ApiWebService.syncActivityRecruit(syncActs1);
//        log.info("活动时间段一招募同步数据：{}", JSONUtil.toJsonStr(syncActs1));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段一招募同步数据：" + JSONUtil.toJsonStr(syncActs1) + "\r\n");
//        log.info("活动时间段一招募同步结果：{}", JSONUtil.toJsonStr(syncActsResult1));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段一招募同步结果：" + JSONUtil.toJsonStr(syncActsResult1) + "\r\n");
//        log.info("活动时间段一招募同步结果信息：{}，活动时间段一招募同步id：{}", syncActsResult1.getMsg(), syncActsResult1.getActId());
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段一招募同步结果信息：" + syncActsResult1.getMsg() + "\r\n");
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段一招募同步id：" + syncActsResult1.getActId() + "\r\n");
//        arId.add(syncActsResult1.getActId());
//
//        SyncActs syncActs2 = new SyncActs();
//        syncActs2.setUniCode("234-567-891-004");
//        syncActs2.setApId(apId);
//        syncActs2.setActDate("2023-06-16");
//        syncActs2.setStartTime("00:01");
//        syncActs2.setEndTime("23:59");
//        SyncActsResult syncActsResult2 = ApiWebService.syncActivityRecruit(syncActs2);
//        log.info("活动时间段二招募同步数据：{}", JSONUtil.toJsonStr(syncActs2));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段二招募同步数据：" + JSONUtil.toJsonStr(syncActs2) + "\r\n");
//        log.info("活动时间段二招募同步结果：{}", JSONUtil.toJsonStr(syncActsResult2));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段二招募同步结果：" + JSONUtil.toJsonStr(syncActsResult2) + "\r\n");
//        log.info("活动时间段二招募同步结果信息：{}，活动时间段二招募同步id：{}", syncActsResult2.getMsg(), syncActsResult2.getActId());
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段二招募同步结果信息：" + syncActsResult2.getMsg() + "\r\n");
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段二招募同步id：" + syncActsResult2.getActId() + "\r\n");
//        arId.add(syncActsResult2.getActId());
//
//        SyncActs syncActs3 = new SyncActs();
//        syncActs3.setUniCode("345-678-912-004");
//        syncActs3.setApId(apId);
//        syncActs3.setActDate("2023-07-01");
//        syncActs3.setStartTime("00:01");
//        syncActs3.setEndTime("23:59");
//        SyncActsResult syncActsResult3 = ApiWebService.syncActivityRecruit(syncActs3);
//        log.info("活动时间段三招募同步数据：{}", JSONUtil.toJsonStr(syncActs3));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段三招募同步数据：" + JSONUtil.toJsonStr(syncActs3) + "\r\n");
//        log.info("活动时间段三招募同步结果：{}", JSONUtil.toJsonStr(syncActsResult3));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段三招募同步结果：" + JSONUtil.toJsonStr(syncActsResult3) + "\r\n");
//        log.info("活动时间段三招募同步结果信息：{}，活动时间段三招募同步id：{}", syncActsResult3.getMsg(), syncActsResult3.getActId());
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段三招募同步结果信息：" + syncActsResult3.getMsg() + "\r\n");
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段三招募同步id：" + syncActsResult3.getActId() + "\r\n");
//        arId.add(syncActsResult3.getActId());
//
//        SyncActs syncActs4 = new SyncActs();
//        syncActs4.setUniCode("456-789-123-004");
//        syncActs4.setApId(apId);
//        syncActs4.setActDate("2023-07-15");
//        syncActs4.setStartTime("00:01");
//        syncActs4.setEndTime("23:59");
//        SyncActsResult syncActsResult4 = ApiWebService.syncActivityRecruit(syncActs4);
//        log.info("活动时间段四招募同步数据：{}", JSONUtil.toJsonStr(syncActs4));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段四招募同步数据：" + JSONUtil.toJsonStr(syncActs4) + "\r\n");
//        log.info("活动时间段四招募同步结果：{}", JSONUtil.toJsonStr(syncActsResult4));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段四招募同步结果：" + JSONUtil.toJsonStr(syncActsResult4) + "\r\n");
//        log.info("活动时间段四招募同步结果信息：{}，活动时间段四招募同步id：{}", syncActsResult4.getMsg(), syncActsResult4.getActId());
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段四招募同步结果信息：" + syncActsResult4.getMsg() + "\r\n");
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段四招募同步id：" + syncActsResult4.getActId() + "\r\n");
//        arId.add(syncActsResult4.getActId());
//
//        SyncActs syncActs5 = new SyncActs();
//        syncActs5.setUniCode("567-891-234-004");
//        syncActs5.setApId(apId);
//        syncActs5.setActDate("2023-07-31");
//        syncActs5.setStartTime("00:01");
//        syncActs5.setEndTime("23:59");
//        SyncActsResult syncActsResult5 = ApiWebService.syncActivityRecruit(syncActs5);
//        log.info("活动时间段五招募同步数据：{}", JSONUtil.toJsonStr(syncActs5));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段五招募同步数据：" + JSONUtil.toJsonStr(syncActs5) + "\r\n");
//        log.info("活动时间段五招募同步结果：{}", JSONUtil.toJsonStr(syncActsResult5));
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段五招募同步结果：" + JSONUtil.toJsonStr(syncActsResult5) + "\r\n");
//        log.info("活动时间段五招募同步结果信息：{}，活动时间段五招募同步id：{}", syncActsResult5.getMsg(), syncActsResult5.getActId());
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段五招募同步结果信息：" + syncActsResult5.getMsg() + "\r\n");
//        fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + "活动时间段五招募同步id：" + syncActsResult5.getActId() + "\r\n");
//        arId.add(syncActsResult5.getActId());
//        return arId;
//    }
//
//    // 加入活动招募
//    private static void syncActMemberTest(FileWriter fw, List<String> arIds) throws Exception{
//        List<SyncActMember> syncActMemberList = new ArrayList<>();
//        SyncActMember syncActMember1 = new SyncActMember();
//        syncActMember1.setUniCode("123-456-789-004");
//        syncActMember1.setActId(arIds.get(0));
//        syncActMember1.setIdCode("32068219971002019X");
//        syncActMember1.setJoinTime("2023-05-31");
//        SyncActMember syncActMember2 = new SyncActMember();
//        syncActMember2.setUniCode("234-567-891-004");
//        syncActMember2.setActId(arIds.get(1));
//        syncActMember2.setIdCode("32068219971002019X");
//        syncActMember2.setJoinTime("2023-06-15");
//        SyncActMember syncActMember3 = new SyncActMember();
//        syncActMember3.setUniCode("345-678-912-004");
//        syncActMember3.setActId(arIds.get(2));
//        syncActMember3.setIdCode("32068219971002019X");
//        syncActMember3.setJoinTime("2023-06-30");
//        SyncActMember syncActMember4 = new SyncActMember();
//        syncActMember4.setUniCode("456-789-123-004");
//        syncActMember4.setActId(arIds.get(3));
//        syncActMember4.setIdCode("32068219971002019X");
//        syncActMember4.setJoinTime("2023-07-14");
//        SyncActMember syncActMember5 = new SyncActMember();
//        syncActMember5.setUniCode("567-891-234-004");
//        syncActMember5.setActId(arIds.get(4));
//        syncActMember5.setIdCode("32068219971002019X");
//        syncActMember5.setJoinTime("2023-07-30");
//        syncActMemberList.add(syncActMember1);
//        syncActMemberList.add(syncActMember2);
//        syncActMemberList.add(syncActMember3);
//        syncActMemberList.add(syncActMember4);
//        syncActMemberList.add(syncActMember5);
//        List<SyncActMemberResult> syncActMemberResults = ApiWebService.syncActMemberBatch(syncActMemberList);
//        int i = 1;
//        for (SyncActMemberResult it : syncActMemberResults) {
//            String prefix = i == 1 ? "报名活动时间段一" : (i == 2 ? "报名活动时间段二" : (i == 3 ? "报名活动时间段三" : (i == 4 ? "报名活动时间段四" : "报名活动时间段五")));
//            i ++;
//            SyncActMember data = syncActMemberList.stream().filter(im -> it.getUniCode().equals(im.getUniCode())).findAny().orElse(new SyncActMember());
//            log.info(prefix + "同步数据：{}", JSONUtil.toJsonStr(data));
//            fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + prefix + "同步数据：" + JSONUtil.toJsonStr(data) + "\r\n");
//            log.info(prefix + "同步结果：{}", JSONUtil.toJsonStr(it));
//            fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + prefix + "同步结果：" + JSONUtil.toJsonStr(it) + "\r\n");
//            log.info(prefix + "同步结果信息：{}", it.getMsg());
//            fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + prefix + "同步结果信息：" + it.getMsg() + "\r\n");
//        }
//    }
//
//    //服务时长上传
//    private static void syncServiceTimeTest(FileWriter fw, List<String> arIds) throws Exception{
//        List<SyncServiceTime> syncServiceTimeList = new ArrayList<>();
//        SyncServiceTime syncServiceTime1 = new SyncServiceTime();
//        syncServiceTime1.setUniCode("123-456-789-004");
//        syncServiceTime1.setIdCode("32068219971002019X");
//        syncServiceTime1.setActId(arIds.get(0));
//        syncServiceTime1.setStartTime("08:30");
//        syncServiceTime1.setEndTime("17:30");
//        syncServiceTime1.setServerTime(null);
//        SyncServiceTime syncServiceTime2 = new SyncServiceTime();
//        syncServiceTime2.setUniCode("234-567-891-004");
//        syncServiceTime2.setIdCode("32068219971002019X");
//        syncServiceTime2.setActId(arIds.get(1));
//        syncServiceTime2.setStartTime("09:30");
//        syncServiceTime2.setEndTime("18:30");
//        syncServiceTime2.setServerTime(null);
//        SyncServiceTime syncServiceTime3 = new SyncServiceTime();
//        syncServiceTime3.setUniCode("345-678-912-004");
//        syncServiceTime3.setIdCode("32068219971002019X");
//        syncServiceTime3.setActId(arIds.get(2));
//        syncServiceTime3.setStartTime("10:30");
//        syncServiceTime3.setEndTime("19:30");
//        syncServiceTime3.setServerTime(null);
//        SyncServiceTime syncServiceTime4 = new SyncServiceTime();
//        syncServiceTime4.setUniCode("456-789-123-004");
//        syncServiceTime4.setIdCode("32068219971002019X");
//        syncServiceTime4.setActId(arIds.get(3));
//        syncServiceTime4.setStartTime("11:30");
//        syncServiceTime4.setEndTime("20:30");
//        syncServiceTime4.setServerTime(null);
//        SyncServiceTime syncServiceTime5 = new SyncServiceTime();
//        syncServiceTime5.setUniCode("567-891-234-004");
//        syncServiceTime5.setIdCode("32068219971002019X");
//        syncServiceTime5.setActId(arIds.get(4));
//        syncServiceTime5.setStartTime("12:30");
//        syncServiceTime5.setEndTime("21:30");
//        syncServiceTime5.setServerTime(null);
//        syncServiceTimeList.add(syncServiceTime1);
//        syncServiceTimeList.add(syncServiceTime2);
//        syncServiceTimeList.add(syncServiceTime3);
//        syncServiceTimeList.add(syncServiceTime4);
//        syncServiceTimeList.add(syncServiceTime5);
//        List<SyncServiceTimeResult> syncServiceTimeResults = ApiWebService.syncServiceTimeBatch(syncServiceTimeList);
//        int i = 1;
//        for (SyncServiceTimeResult it : syncServiceTimeResults) {
//            String prefix = i == 1 ? "加入活动时间段一服务时长" : (i == 2 ? "加入活动时间段二服务时长" : (i == 3 ? "加入活动时间段三服务时长" : (i == 4 ? "加入活动时间段四服务时长" : "加入活动时间段五服务时长")));
//            i ++;
//            SyncServiceTime data = syncServiceTimeList.stream().filter(im -> it.getUniCode().equals(im.getUniCode())).findAny().orElse(new SyncServiceTime());
//            log.info("同步数据：{}", JSONUtil.toJsonStr(data));
//            fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + prefix + "同步数据：" + JSONUtil.toJsonStr(data) + "\r\n");
//            log.info("同步结果：{}", JSONUtil.toJsonStr(it));
//            fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + prefix + "同步结果：" + JSONUtil.toJsonStr(it) + "\r\n");
//            log.info("同步结果信息：{}", it.getMsg());
//            fw.write(LocalDateTime.now().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + " -- " + prefix + "同步结果信息：" + it.getMsg() + "\r\n");
//        }
//    }


    //活动注册调试内容
//    public static void main(String[] args) {
//        SyncActp syncRes = new SyncActp();
//        syncRes.setUniCode("102034");
//        syncRes.setPicLogoUrl(Base64.encode("https://zyz.sipac.gov.cn/Content/images/zyzqr_logo.jpg"));
//        syncRes.setName("都市社区志愿维修服务");
//        syncRes.setAddress("都市花园-西区");
//        syncRes.setOrgId("40946");
//        syncRes.setRecordWay("1");
//        syncRes.setDescs("都市社区志愿维修服务");
//        syncRes.setEndDate("2023-03-07");
//        syncRes.setStartTime("14:20");
//        syncRes.setStartDate("2023-03-07");
//        syncRes.setEndTime("18:00");
//        syncRes.setServiceField("70199");
//        syncRes.setServiceTime("4.5");
//        syncRes.setTxMapLan("120.669783");
//        syncRes.setTxMapLat("31.323902");
//        syncRes.setRegionCode("320508001001");
//        syncActp(syncRes);
//    }


    //活动注册调试内容
//    public static void main(String[] args) {
//        SyncActp syncRes = new SyncActp();
//        syncRes.setUniCode("102035");
//        syncRes.setPicLogoUrl(Base64.encode("https://zyz.sipac.gov.cn/Content/images/zyzqr_logo.jpg"));
//        syncRes.setName("都市社区志愿维修服务(测试)");
//        syncRes.setAddress("都市花园-西区(测试)");
//        syncRes.setOrgId("40985");
//        syncRes.setRecordWay("1");
//        syncRes.setDescs("都市社区志愿维修服务(测试)");
//        syncRes.setEndDate("2023-03-10");
//        syncRes.setStartTime("14:20");
//        syncRes.setStartDate("2023-03-10");
//        syncRes.setEndTime("18:00");
//        syncRes.setServiceField("77202");
//        syncRes.setServiceTime("4.5");
//        syncRes.setTxMapLan("120.669783");
//        syncRes.setTxMapLat("31.323902");
//        syncRes.setRegionCode("320508001001");
//        syncActivity(syncRes);
//    }


    //同步实践单调试内容
//    public static void main(String[] args) {
//        SyncOrder syncOrder = new SyncOrder();
//        syncOrder.setUniCode("102036");
//        syncOrder.setContent("都市社区志愿维修服务(测试)");
//        syncOrder.setPbId("102036");
//        syncOrder.setFinishDate("2023-03-11");
//        syncOrder.setDockTime("2023-03-10 12:02:34");
//        syncOrder.setDockType(SyncOrder.dockType.ORG_OR_TEAM);
//        syncOrder.setFinishTime("2023-03-10 12:02:34");
//        syncOrder.setDockId("231968");
//        syncOrder(syncOrder);
//    }

//    // 同步实践阵地调试内容
//    public static void main(String[] args) {
//        SyncBase syncBase = new SyncBase();
//        syncBase.setName("园区志愿者协会");
//        syncBase.setUniCode(String.valueOf(IdUtil.getSnowflake(1, 1).nextId()));
//        syncBase.setRegionCode(Cons.SyncConstant.AS_REGION_CODE);
//        syncBase.setType("3");
//        syncBase.setContact("志愿者协会");
//        syncBase.setContactNo("66686608");
//        syncBase.setIntro("园区志愿者协会实践阵地");
//        syncBase.setMapAddress("苏州市工业园区苏惠路98号国检大厦一楼西侧裙楼");
//        syncBase.setTxMapLan("120.668363");
//        syncBase.setTxMapLat("31.313483");
//        syncBase.setPicLogoUrl(Base64.encode("http://zyz.sipac.gov.cn/Content/index/images/nei_logo.png"));
//        syncBase.setSaveType(SyncBase.SaveType.ADD);
//        ApiWebService.syncBase(syncBase);
//    }
}
