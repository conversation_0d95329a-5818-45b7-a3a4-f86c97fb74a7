package com.fykj.scaffold.support.syslog;

import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.mq.cons.TopicCons;
import com.fykj.scaffold.mq.event.SysLogEvent;
import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.security.business.domain.entity.SysLog;
import com.fykj.scaffold.support.utils.Oauth2Util;
import fykj.microservice.core.support.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 系统日志，切面处理类
 *
 * @<NAME_EMAIL>
 */
@Slf4j
@Aspect
@Component
public class AuditLogAspect {
    @Autowired
    private RocketMQTemplate mqTemplate;

    @Pointcut("@annotation(com.fykj.scaffold.support.syslog.AuditLog)")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;

        //保存日志
        try {
            saveSysLog(point, time, result);
        } catch (Exception e) {
            log.error("切面保存审计日志失败", e);
        }

        return result;
    }

    private void saveSysLog(ProceedingJoinPoint joinPoint, long time, Object result) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        SysLog sysLog = new SysLog();
        AuditLog logMethod = method.getAnnotation(AuditLog.class);
        if (logMethod != null) {
            //注解上的描述
            sysLog.setOperation(logMethod.value());
        }

        //请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        sysLog.setMethod(className + "." + methodName + "()");

        //请求的参数
        Object[] args = joinPoint.getArgs();
        if (args.length > 0) {
            String params;
            List<Object> paramsObjs = Arrays.stream(args).filter(it -> !(it instanceof HttpServletResponse || it instanceof HttpServletRequest)).collect(Collectors.toList());
            try {
                params = JSON.toJSONString(paramsObjs);
                if (params.length() > 1000) {
                    params = params.substring(1000);
                }
            } catch (Exception e) {
                params = "无法展示请求参数";
            }
            sysLog.setParams(params);
        }

        if (result != null) {
            String resultStr;
            try {
                resultStr = JSON.toJSONString(result);
                if (resultStr.length() > 1000) {
                    resultStr = resultStr.substring(1000);
                }
            } catch (Exception e) {
                resultStr = "无法展示请求参数";
            }
            sysLog.setResult(resultStr);
        }

        //设置IP地址
        sysLog.setIp(SystemUtil.getClientIp(SystemUtil.getRequest()));

        BackendUserDetail user = Oauth2Util.getUser();
        if (user != null) {
            //用户名
            sysLog.setUsername(user.getUsername());
            sysLog.setNickname(user.getNickName());
            sysLog.setCapacityName(user.getManagerCapacityName());
        }

        sysLog.setTime(time);
        sysLog.setCreateDate(LocalDateTime.now());

        mqTemplate.sendOneWay(TopicCons.SYS_AUDIT_LOG, MessageBuilder.withPayload(SysLogEvent.builder().sysLog(sysLog).build()).build());
    }
}
