package com.fykj.scaffold.portal_website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.domain.params.NewsParams;
import com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  新闻资讯Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface NewsMapper extends BaseMapper<News> {

    /**
     * 分页查询新闻资讯
     * @param page
     * @param params
     * @return
     */
    @DataFilter(deptCodeLinkColumn = "so.code_prefix")
    IPage<News> pageForNews(Page<News> page, @Param("params") NewsParams params);

    /**
     * 获取新闻待审核数量
     * @param myOrgCode
     * @param passStatus
     * @param refuseStatus
     * @return
     */
    Integer getNewsWaitAuditNum(@Param("myOrgCode")String myOrgCode, @Param("passStatus")String passStatus, @Param("refuseStatus")String refuseStatus);

    /**
     * 分页查询新闻资讯(网页展示)
     * @param page
     * @param params
     * @return
     */
    IPage<News> pageForNewsApi(Page<News> page, @Param("params") NewsParams params);

    /**
     * 分页查询新闻资讯(网页展示)
     * @param page
     * @param params
     * @return
     */
    IPage<News> pageForMyNewsApi(Page<News> page, @Param("params") NewsParams params);

    /**
     * 获取定时任务需要同步的数据
     * @param now
     * @param threeDaysBeforeNow
     * @return
     */
    List<Long> getNeedSync(@Param("now") LocalDateTime now, @Param("threeDaysBeforeNow")LocalDateTime threeDaysBeforeNow);

    List<News> getNewsByTeam(@Param("teamId")Long teamId,@Param("projectId")Long projectId);

    /**
     * 获取组织新闻发布数量
     * @param params
     * @return
     */
    List<OrgDataFormDto> getOrgPublishNewsNum(@Param("params") OrgDataReportFormParams params);

    /**
     * 获取其他组织的新闻发布数
     * @param orgCode
     * @param start
     * @param end
     * @return
     */
    Integer getOrgSelfPublishNewsNum(@Param("orgCode")String orgCode, @Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    /**
     * 获取团队新闻发布数量
     * @param params
     * @return
     */
    List<OrgDataFormDto> getTeamPublishNewsNum(@Param("params") OrgDataReportFormParams params);

    /**
     * 获取其他组织的新闻发布数
     * @param start
     * @param end
     * @return
     */
    Integer getOtherOrgPublishNewsNum(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);
}
