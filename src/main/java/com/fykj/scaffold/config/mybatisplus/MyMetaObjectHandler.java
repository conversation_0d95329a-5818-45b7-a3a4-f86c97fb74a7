package com.fykj.scaffold.config.mybatisplus;

import com.fykj.scaffold.support.utils.Oauth2Util;
import fykj.microservice.core.configs.mybatisplus.core.handlers.AbstractMetaObjectHandler;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class MyMetaObjectHandler extends AbstractMetaObjectHandler {
    public MyMetaObjectHandler() {
        this(MyMetaObjectHandler.class);
    }

    public MyMetaObjectHandler(Class handler) {
        super(handler);
    }

    @Override
    public Serializable userId() {
        Serializable userId = Oauth2Util.getUserId();
        if (userId == null) {
            userId = UserThreadLocal.get();
        }
        return userId;
    }
}
