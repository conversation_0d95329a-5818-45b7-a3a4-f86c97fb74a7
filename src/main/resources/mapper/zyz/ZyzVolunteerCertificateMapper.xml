<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzVolunteerCertificateMapper">
    <select id="getAwardList" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerCertificate">
        SELECT
            a.id,
            a.version,
            a.is_deleted,
            a.create_date,
            a.update_date,
            a.creator,
            a.updater,
            a.volunteer_id,
            a.volunteer_name,
            a.certificate_type,
            a.certificate_id,
            a.phone,
            a.award_certificate_id,
            c.name AS award_certificate_name,
            a.receive_date,
            a.pdf_url,
            a.img_url,
            c.content,
            c.sequence,
            c.is_default_cer AS defaultCer,
            c.is_real_time_cer AS realTimeCer
        FROM
            zyz_volunteer_certificate a
                JOIN zyz_certificate c ON a.award_certificate_id = c.id
                AND c.is_deleted = 0
        WHERE
            a.is_deleted = 0 AND  phone = #{phone}
    </select>

    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerCertificate">
        SELECT
            a.id,
            a.version,
            a.is_deleted,
            a.create_date,
            a.update_date,
            a.creator,
            a.updater,
            a.volunteer_id,
            a.volunteer_name,
            a.certificate_type,
            a.certificate_id,
            a.phone,
            a.award_certificate_id,
            c.name AS award_certificate_name,
            a.receive_date,
            a.pdf_url,
            a.img_url,
            c.is_real_time_cer AS realTimeCer
        FROM
            zyz_volunteer_certificate a
                JOIN zyz_certificate c ON a.award_certificate_id = c.id
                AND c.is_deleted = 0
        WHERE
            a.is_deleted = 0
        <if test="params.key != null and params.key != ''">
            AND ( a.volunteer_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR c.name LIKE CONCAT('%',CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.awardCertificateId != null and params.awardCertificateId != ''">
            AND a.award_certificate_id = #{params.awardCertificateId}
        </if>
        <if test="params.volunteerId != null">
            AND a.volunteer_id = #{params.volunteerId}
        </if>
        <if test="params.certificateId != null and params.certificateId != ''">
            AND a.certificate_id = #{params.certificateId}
        </if>
        <if test="params.phone != null and params.phone != ''">
            AND a.phone = #{params.phone}
        </if>
        ORDER BY a.create_date DESC
    </select>
</mapper>
