<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzVolunteerTeamAuditMapper">


    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerTeamAuditDto">
        SELECT v.name as name,
               t.name as teamName,
               v.phone as phone,
               v.certificate_type as certificateType,
               v.certificate_id as certificateId,
               v.sex as sex,
               t.org_code as orgCode,
               o.name as orgName,
               a.`status` as status,
               a.id as id,
               a.create_date as applicationTime
        FROM `zyz_volunteer_team_audit` a
                 LEFT JOIN zyz_volunteer v on v.id = a.volunteer_id
                 LEFT JOIN zyz_team t on t.id = a.team_id
                 LEFT JOIN sys_org o on o.code = t.org_code
        WHERE v.is_deleted = FALSE AND v.write_off = 0
        <if test="params.name != null and params.name != ''">
            and v.name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.status != null and params.status != ''">
            and a.status = #{params.status}
        </if>
        <if test="params.phone != null and params.phone != ''">
            and v.phone = #{params.phone}
        </if>
        ORDER BY a.create_date  desc
    </select>

    <select id="getJoinTeamWaitAuditNum" resultType="java.lang.Integer">
        SELECT COUNT(a.id)
        FROM zyz_volunteer_team_audit a
        LEFT JOIN zyz_volunteer v ON v.id = a.volunteer_id
        LEFT JOIN zyz_team t ON t.id = a.team_id
        LEFT JOIN sys_org o ON o.code = t.org_code
        WHERE a.is_deleted = 0 AND v.is_deleted = 0 AND v.write_off = 0 AND a.status = #{status}
    </select>
</mapper>
