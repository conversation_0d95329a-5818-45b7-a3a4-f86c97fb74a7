package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallStock;
import com.fykj.scaffold.mall.domain.params.MallStockParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * <p>
 * 物料信息管理服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface IMallStockService extends IBaseService<MallStock> {

    /**
     * 分页查询--后端页面
     */
    IPage<MallStock> getPage(MallStockParams params);

    /**
     * 变动库存
     * @param stockId
     * @param stockNum
     */
    void changeStock(long stockId, int stockNum);

    /**
     * 出库物料
     *
     * @param stockId   物料ID
     * @param changeNum 变动数量（强制正数）
     * @param remark    备注
     */
    void reduceStock(long stockId, int changeNum, String remark);

    /**
     * 入库物料
     *
     * @param stockId   物料ID
     * @param changeNum 变动数量（强制正数）
     * @param remark    备注
     */
    void addStock(long stockId, int changeNum, String remark);

    /**
     * 增加总可用库存数量
     * @param sku
     * @param num
     */
    void addStockTotalQuantity(String sku,int num);

    /**
     * 减少总可用库存数量
     * @param sku
     * @param num
     */
    void reduceStockTotalQuantity(String sku,int num);

    /**
     * 物料库存变动
     *
     * @param stockId       商品ID
     * @param stockNum      库存变动数量，入库为正，出库为负
     * @param remark        备注
     * @return
     */
    void changeStock(long stockId, int stockNum, String remark);

    MallStock saveMallStock(MallStock mallStock);

    List<MallStock> getStockByOrg();
}
