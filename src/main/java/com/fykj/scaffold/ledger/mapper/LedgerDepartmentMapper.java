package com.fykj.scaffold.ledger.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.ledger.domain.entity.LedgerDepartment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.ledger.domain.params.LedgerDepartmentParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 台账责任局办-Mapper接口
 *
 * @date 2025-05-26
 */
public interface LedgerDepartmentMapper extends BaseMapper<LedgerDepartment> {

    /**
     * 分页查询台账责任局办
     *
     * @param page
     * @param params
     * @return
     */
    IPage<LedgerDepartment> pages(IPage<LedgerDepartment> page, @Param("params") LedgerDepartmentParams params);

    /**
     * 获取所有部门名称
     *
     * @return
     */
    List<String> getDepartmentName();

    /**
     * 获取部门下的负责人手机号
     * @param departmentIdList
     * @return
     */
    List<String> getMobileByDepartmentIdList(@Param("departmentIdList") List<String> departmentIdList);
}
