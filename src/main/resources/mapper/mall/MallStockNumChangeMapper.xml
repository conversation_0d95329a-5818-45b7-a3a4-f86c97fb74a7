<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallStockNumChangeMapper">

    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallStockNumChange">
        select v.* from mall_stock_num_change v
        where v.is_deleted=FALSE and v.stock_id=#{params.goodsId}
        order by v.operate_date desc
    </select>
</mapper>