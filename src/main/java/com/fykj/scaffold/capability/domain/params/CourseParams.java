package com.fykj.scaffold.capability.domain.params;

import com.fykj.scaffold.capability.cons.CourseType;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("课程查询参数")
public class CourseParams extends BaseParams {

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty("课程类型 字典CODE:" + CourseType.DICT_CODE)
    private String courseType;

    @ApiModelProperty("课程状态(上下架状态")
    private Boolean outerDisplay;

    @ApiModelProperty("未结束")
    private Boolean overStatus;

    @ApiModelProperty(value = "我的课程-完成情况(已完成/未完成)")
    private Boolean completeStatus;

    @ApiModelProperty(value = "用户ID")
    private Long userId;
}
