package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 同步所属领域
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BelongFieldSyncTask {

    @Autowired
    private IZyzSyncBelongFieldDictService belongFieldDictService;

    @XxlJob("Belong_Field_Sync_Handler")
    public void belongFieldSyncJobHandler() {
        XxlJobHelper.log("Belong_Field_Sync_Handler-Job, START!");
        try {
            belongFieldDictService.syncBelongFieldDict();
        } catch (Exception e) {
            log.error("领域同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Belong_Field_Sync_Handler-Job, END!");
    }
}
