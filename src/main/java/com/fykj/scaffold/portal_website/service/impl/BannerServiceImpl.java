package com.fykj.scaffold.portal_website.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.cms.domain.entity.CmsCategory;
import com.fykj.scaffold.cms.domain.entity.CmsCategoryContent;
import com.fykj.scaffold.cms.service.ICmsCategoryContentService;
import com.fykj.scaffold.cms.service.ICmsCategoryService;
import com.fykj.scaffold.portal_website.domain.entity.Banner;
import com.fykj.scaffold.portal_website.domain.params.BannerParams;
import com.fykj.scaffold.portal_website.mapper.BannerMapper;
import com.fykj.scaffold.portal_website.service.IBannerService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.fykj.scaffold.portal_website.conns.PortalWebsiteCons.BANNER_CMS_CODE;

/**
 * <p>
 * 轮播图服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class BannerServiceImpl extends BaseServiceImpl<BannerMapper, Banner> implements IBannerService {

    @Autowired
    private ICmsCategoryService categoryService;

    @Autowired
    private ICmsCategoryContentService categoryContentService;

    @Override
    public boolean save(Banner entity) {
        entity.setEnabled(Boolean.TRUE);
        super.save(entity);
        saveCategoryContent(entity);
        return true;
    }

    @Override
    public boolean updateById(Banner entity) {
        categoryContentService.removeByContentId(entity.getId(), true);
        saveCategoryContent(entity);
        entity.setEnabled(null);
        super.updateById(entity);
        return true;
    }

    private void saveCategoryContent(Banner entity) {
        CmsCategory category = categoryService.getByCode(BANNER_CMS_CODE);
        List<String> categoryIdList = entity.getCategoryIds();
        int size = categoryIdList.size();
        Long categoryId = Long.valueOf(categoryIdList.get(size - 1));
        CmsCategoryContent categoryContent = new CmsCategoryContent();
        categoryContent.setBanner(Boolean.TRUE);
        categoryContent.setCategoryId(categoryId);
        categoryContent.setCategoryCode(categoryService.getById(categoryContent.getCategoryId()).getCode());
        categoryContent.setParentId(size == 1 ? category.getId() : Long.valueOf(categoryIdList.get(size - 2)));
        categoryContent.setCategoryList(String.valueOf(category.getId()).concat(Mark.COMMA).concat(categoryIdList.stream().map(String::valueOf).collect(Collectors.joining(Mark.COMMA))));
        categoryContent.setContentId(entity.getId());
        categoryContent.setSequence(ObjectUtil.isEmpty(entity.getSequence()) ? 999 : entity.getSequence());
        categoryContentService.save(categoryContent);
    }

    @Override
    public Banner getById(Serializable id) {
        Banner banner = super.getById(id);
        List<CmsCategoryContent> categoryContents = categoryContentService.getByContentId(id, true);
        if (CollectionUtil.isEmpty(categoryContents)) {
            throw new BusinessException(ResultCode.FAIL, "数据类型获取失败！");
        }
        CmsCategoryContent categoryContent = categoryContents.get(0);
        List<String> categoryList = Arrays.asList(categoryContent.getCategoryList().split(Mark.COMMA));
        banner.setCategoryIds(categoryList.subList(1, categoryList.size()));
        return banner;
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        super.removeByIds(list);
        list.forEach(it -> {
            categoryContentService.removeByContentId(Long.valueOf(String.valueOf(it)), true);
        });
        return true;
    }

    @Override
    public IPage<Banner> page(BaseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new BannerParams();
        }
        Page<Banner> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForBanner(page, (BannerParams) params).convert(this::convert);
    }

    private Banner convert(Banner record) {
        String categories = record.getCategory();
        if (StringUtil.isNotEmpty(categories)) {
            List<String> categoryIds = Arrays.asList(categories.split(Mark.COMMA));
            List<String> categoryName = new ArrayList<>();
            categoryIds.subList(1, categoryIds.size()).forEach(it -> {
                categoryName.add(categoryService.getById(it).getName());
            });
            record.setCategory(String.join(Mark.LINE, categoryName));
        }
        return record;
    }

    @Override
    public void ground(String id) {
        Banner banner = getById(id);
        banner.setGrounding(!banner.getGrounding());
        super.updateById(banner);
    }

    @Override
    public List<Banner> pageForApi(String categoryCode) {
        CmsCategory category = categoryService.getByCode(categoryCode);
        String categoryId=null;
        if (category != null) {
            categoryId=String.valueOf(category.getId());
        }
        if(StringUtil.isNotEmpty(categoryId)) {
            return baseMapper.pageForApi(categoryId);
        }else{
            return new ArrayList<Banner>();
        }
    }
}
