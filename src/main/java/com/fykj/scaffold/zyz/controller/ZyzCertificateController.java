package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzCertificate;
import com.fykj.scaffold.zyz.domain.params.ZyzCertificateParams;
import com.fykj.scaffold.zyz.service.IZyzCertificateService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerCertificateService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.Map;

/**
 * 证书管理
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/admin/zyz/certificate")
@Api(tags = " 证书管理接口")
public class ZyzCertificateController extends BaseController<IZyzCertificateService, ZyzCertificate, ZyzCertificateParams> {

    @Autowired
    private IZyzVolunteerCertificateService zyzVolunteerCertificateService;

    @ApiOperation("证书图片预览")
    @GetMapping({"/imagePreview"})
    public JsonResult<Map<String, String>> imagePreview(@RequestParam long id) {
        Map<String, String> map = this.zyzVolunteerCertificateService.imagePreview(id);
        return new JsonResult<>(map);
    }
    
    @ApiOperation("分页查询证书列表")
    @PostMapping("/pages")
    public JsonResult<IPage<ZyzCertificate>> list(@RequestBody(required = false) ZyzCertificateParams params) {
        // 使用XML实现的分页查询
        IPage<ZyzCertificate> page = this.baseService.query(params);
        return new JsonResult<>(page);
    }

    @ApiOperation("切换状态")
    @PostMapping("/status/switch")
    public Result switchStatus(@RequestParam Long id) {
        baseService.switchStatus(id);
        return OK;
    }

}
