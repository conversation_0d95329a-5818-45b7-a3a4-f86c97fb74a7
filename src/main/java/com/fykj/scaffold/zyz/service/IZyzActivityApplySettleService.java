package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 活动表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-09
 */
public interface IZyzActivityApplySettleService extends IService<ZyzActivityApply>, IBaseService<ZyzActivityApply> {

    /**
     * 校验是否可结算
     * @param applyId
     * @return
     */
    ZyzActivityApply getAndValidateById(Long applyId);

    /**
     * 批量结算
     * @param applies
     */
    boolean settleBatch(List<ZyzActivityApply> applies);

    /**
     * 单条结算
     * @param apply
     */
    void settleOne(ZyzActivityApply apply);
}

