package com.fykj.scaffold.sync.controller;

import com.fykj.scaffold.sync.domain.entity.ZyzSyncDepTypeDict;
import com.fykj.scaffold.sync.domain.params.ZyzSyncDepTypeDictParams;
import com.fykj.scaffold.sync.service.IZyzSyncDepTypeDictService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 市平台职能部门类型数据字典
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@RestController
@RequestMapping("/admin/zyz/sync-dep-type-dict")
public class ZyzSyncDepTypeDictController extends BaseController<IZyzSyncDepTypeDictService, ZyzSyncDepTypeDict,ZyzSyncDepTypeDictParams> {

}
