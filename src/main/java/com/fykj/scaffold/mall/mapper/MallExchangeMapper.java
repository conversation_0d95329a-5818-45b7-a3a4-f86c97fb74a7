package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.dto.MallExchangeDto;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.params.MallExchangeParams;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
public interface MallExchangeMapper extends BaseMapper<MallExchange> {
    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    @DataFilter
    IPage<MallExchange> getPage(IPage<MallExchange> page, @Param("params") MallExchangeParams params);

    /**
     * 分页查询--前端页面
     * @return
     */
    IPage<MallExchange> pageForApi(IPage<MallExchange> page, @Param("params") MallExchangeParams params);

    @DataFilter
    List<MallExchangeDto> getExchangeList(@Param("params") MallExchangeParams params);

    Map<String,Object> pointCardReturnTip(String mobile);

    void updatePointCardReturnTip(String mobile);

    /**
     * 获取志愿者取消兑换后仍需清除的过期积分数
     * @param year
     * @param volunteerId
     * @return
     */
    BigDecimal getVolunteerCancelExchangeNeedClearPointRemain(@Param("year") Integer year, @Param("volunteerId") Long volunteerId);

    /**
     * 更新志愿者需要清除的积分
     * @param year
     * @param volunteerId
     * @param cancelNeedClearPointNew
     */
    void updateVolunteerNeedClearPoint(@Param("year") Integer year, @Param("volunteerId") Long volunteerId, @Param("cancelNeedClearPointNew") BigDecimal cancelNeedClearPointNew);
}
