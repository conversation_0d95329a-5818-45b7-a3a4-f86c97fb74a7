package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActReqResDockingInfoDto {



    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("资源方ID")
    private Long resId;

    @ApiModelProperty("资源方名称")
    private String resName;

    @ApiModelProperty("需求方ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long reqId;

    @ApiModelProperty("需求方名称")
    private String reqName;

    @ApiModelProperty("是否资源方创建的对接记录")
    private Boolean dockingResCreate;

    @ApiModelProperty("是否需求方创建的对接记录")
    private Boolean dockingReqCreate;

    @ApiModelProperty("活动对接需求数据模型")
    private RequirementTempSyncForZyzDto requirementTempSyncForZyzDto;

    @ApiModelProperty("活动对接资源数据模型")
    private ResourceTempSyncForZyzDto resourceTempSyncForZyzDto;

}
