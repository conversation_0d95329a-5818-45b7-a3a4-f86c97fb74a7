package com.fykj.scaffold.sgb_docking.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5工具类
 * 用于计算字符串的MD5值
 */
public class MD5Util {
    
    /**
     * 将字符串进行MD5加密
     * @param input 需要加密的字符串
     * @return MD5加密后的32位小写字符串
     */
    public static String stringToMD5(String input) {
        try {
            // 获取MD5实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 对字符串进行加密
            byte[] bytes = md.digest(input.getBytes());
            // 将字节数组转换为16进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                String hex = Integer.toHexString(b & 0xff);
                if (hex.length() == 1) {
                    sb.append("0");
                }
                sb.append(hex);
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
} 