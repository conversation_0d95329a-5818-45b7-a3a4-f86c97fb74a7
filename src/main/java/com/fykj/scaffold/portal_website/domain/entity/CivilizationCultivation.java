package com.fykj.scaffold.portal_website.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATE_FORMAT;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cms_content")
public class CivilizationCultivation extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 名称
     */
    @TableField("title")
    @NotBlank(message = "名称不能为空")
    @ApiModelProperty(value = "名称")
    private String title;

    /**
     * 大屏展示名称
     */
    @TableField("big_screen_title")
    @ApiModelProperty(value = "大屏展示名称")
    private String bigScreenTitle;

    /**
     * 图片
     */
    @TableField(value = "title_img_url", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "图片url")
    private String titleImgUrl;

    /**
     * 简介
     */
    @TableField(value = "brief_introduction", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "简介")
    private String briefIntroduction;

    /**
     * 内容
     */
    @TableField(value = "description", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "详细描述")
    private String description;

    /**
     * 发布日期
     */
    @TableField("effective_date")
    @JsonFormat(pattern = DATE_FORMAT)  //格式化前台日期参数注解
    @ApiModelProperty(value = "发布日期")
    private LocalDateTime effectiveDate;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField("type")
    private String type;

    /**
     * 是否上架
     */
    @ApiModelProperty(value = "是否上架")
    @TableField("grounding")
    private Boolean grounding;

    /**
     * 排序
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "排序")
    private Integer sequence;

    /**
     * 栏目id
     */
    @ApiModelProperty(value = "栏目id")
    @NotEmpty(message = "栏目不能为空")
    @TableField(exist = false)
    private String categoryId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(exist = false)
    private String category;

    /**
     * 自定义链接
     */
    @ApiModelProperty(value = "自定义链接")
    @TableField("custom_links")
    private String customLinks;

    /**
     * 发布日期
     */
    @ApiModelProperty(value = "发布日期")
    @TableField(exist = false)
    private String publishDate;
}
