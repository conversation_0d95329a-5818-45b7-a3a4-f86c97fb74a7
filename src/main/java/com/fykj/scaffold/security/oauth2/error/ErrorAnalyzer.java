package com.fykj.scaffold.security.oauth2.error;

import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.security.oauth2.error.exception.InvalidRefererException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.oauth2.common.DefaultThrowableAnalyzer;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.web.util.ThrowableAnalyzer;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;

import static result.ResultCode.TOKEN_ERROR_CODE;
import static result.ResultCode.TOKEN_FORBIDDEN_CODE;

/**
 * spring security统一的异常解析器
 *
 * <AUTHOR>
 */
public class ErrorAnalyzer {

    private final ThrowableAnalyzer throwableAnalyzer = new DefaultThrowableAnalyzer();

    public void translate(HttpServletRequest request, HttpServletResponse response, Exception exception) throws Exception {
        //暂时就处理两种情况，登录失效和未授权
        Throwable[] causeChain = this.throwableAnalyzer.determineCauseChain(exception);
        if (Arrays.stream(causeChain).anyMatch(it -> it instanceof InvalidTokenException || it instanceof InsufficientAuthenticationException)) {
            writeRestfulErr(response, TOKEN_ERROR_CODE.code(), "无效的token");
        } else {
            writeRestfulErr(response, TOKEN_FORBIDDEN_CODE.code(), "用户无权限访问");
        }
    }

    private void writeRestfulErr(HttpServletResponse response, int code, String errMsg) throws IOException {
        Result result = new Result(code, errMsg);
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter writer = response.getWriter();
        writer.print(JSON.toJSONString(result));
    }
}
