package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 志愿者过期积分清除定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VolunteerPointExpiredClearTask {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @XxlJob("Volunteer_Point_Expired_Clear_Handler")
    public void volunteerPointExpiredClearJobHandler() {
        XxlJobHelper.log("Volunteer-Point-Expired-Clear-Handler-Job, START!");
        volunteerService.clearExpiredPoints();
        XxlJobHelper.log("Volunteer-Point-Expired-Clear-Handler-Job, END!");
    }
}
