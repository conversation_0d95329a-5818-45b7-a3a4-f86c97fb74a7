package com.fykj.scaffold.task.service;

import com.fykj.scaffold.task.domain.entity.CopyActivity;
import com.fykj.scaffold.task.domain.entity.CopyActivityApply;
import com.fykj.scaffold.task.domain.entity.ParameterCopyActivity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CopyActivityService {
    /**
     * es保存修改活动
     *
     * @param activity
     * @return
     */
    CopyActivity saveOrUpdateCopyActivity(CopyActivity activity);

    /**
     * es批量保存修改活动
     *
     * @param list
     * @return
     */
    List<CopyActivity> saveOrUpdateCopyActivities(List<CopyActivity> list);

    /**
     * es保存修改活动申请
     *
     * @param activity
     * @return
     */
    CopyActivityApply saveOrUpdateCopyActivityApply(CopyActivityApply activity);

    /**
     * es批量保存修改活动申请
     *
     * @param list
     * @return
     */
    List<CopyActivityApply> saveOrUpdateCopyActivityApplies(List<CopyActivityApply> list);
    /**
     * 手动同步
     *
     * @param actId
     * @return
     */
    void syncAct(Long actId);
    /**
     * 同步市平台
     *
     * @param actId
     * @return
     */
    void syncActByParameterId(ParameterCopyActivity activity,Long actId);

/*    Double sumServiceTimeByDate(LocalDate date);*/
    /**
     * 自动创建活动
     *
     * @param activity
     * @return
     */
    void createActivityPlan(ParameterCopyActivity activity);
}
