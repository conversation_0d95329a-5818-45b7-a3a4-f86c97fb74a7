package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportAllDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;
import result.Result;

import java.util.List;

/**
 * 阵地计划详情
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
public interface IZyzActivityScheduleDetailService extends IBaseService<ZyzActivityScheduleDetail> {


    /**
     * 获取阵地计划详情
     *
     * @param scheduleId 计划id
     * @return 详情列表
     */
    List<ZyzActivityScheduleDetail> listForScheduleDetail(Long scheduleId);

    /**
     * 获取阵地计划详情(前台展示用
     * 排序按开始时间正序
     *
     * @param scheduleId 计划id
     * @return 详情列表
     */
    List<ZyzActivityScheduleDetail> listForScheduleDetailFrontend(Long scheduleId);

    /**
     * 阵地计划详情导出
     *
     * @param params 查询参数
     * @return 详情列表
     */
    List<ZyzActivityScheduleDetailExportDto> listForScheduleDetailExport(ZyzActivityScheduleParams params);

    /**
     * 阵地计划详情导出全部字段
     *
     * @param params 查询参数
     * @return 详情列表
     */
    List<ZyzActivityScheduleDetailExportAllDto> listForScheduleDetailExportAll(ZyzActivityScheduleParams params);

    /**
     * excel数据导入
     *
     * @param excel      文件
     * @param scheduleId 计划id
     * @return 结果
     */
    Result excelImport(MultipartFile excel, Long scheduleId);


}
