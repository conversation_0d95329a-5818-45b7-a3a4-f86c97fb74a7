package com.fykj.scaffold.ledger.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 台账上报-实体类
 *
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("ledger_report")
public class LedgerReport extends BaseEntity  {

	private static final long serialVersionUID = 175758249975039721L;

	/**
	 * 指标项id
	 */
	@TableField("index_item_id")
    @ApiModelProperty(value = "指标项id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long indexItemId;

	/**
	 * 局办id
	 */
	@TableField("department_id")
    @ApiModelProperty(value = "局办id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long departmentId;

	/**
	 * 附件地址
	 */
	@TableField("attachment_url")
    @ApiModelProperty(value = "附件地址")
	private String attachmentUrl;

	/**
	 * 附件名称
	 */
	@TableField("attachment_name")
    @ApiModelProperty(value = "附件名称")
	private String attachmentName;

	/**
	 * 上报状态
	 */
	@TableField("status")
    @ApiModelProperty(value = "上报状态")
	private Boolean status;

	/**
	 * 上报时间
	 */
	@TableField("report_date")
    @ApiModelProperty(value = "上报时间")
	@JsonFormat(pattern = DATETIME_FORMAT)
	@DateTimeFormat(pattern = DATETIME_FORMAT)
	private LocalDateTime reportDate;

}
