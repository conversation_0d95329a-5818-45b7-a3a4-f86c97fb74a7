package com.fykj.scaffold.support.msg_send;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.domain.entity.MsgTemplate;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.IMsgTemplateService;
import com.fykj.scaffold.security.business.service.ISysUserExpandService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.service.*;
import fykj.microservice.core.base.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import utils.StringUtil;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;

import static com.fykj.scaffold.message.conns.MsgCons.MSG_SEND_WHITE_LIST;
import static com.fykj.scaffold.support.conns.Cons.*;
import static com.fykj.scaffold.support.conns.Cons.RoleCode.*;
import static constants.Mark.COMMA;

/**
 * 模版消息发送实现类
 */
@Service
@Slf4j
public class SendTmpMsgImpl implements ISendTmpMsg {

    @Autowired
    private ScheduledExecutorService newSingleThreadScheduledExecutor;

    @Autowired
    private IMsgTemplateService msgTemplateService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private INewsService newsService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ISysUserExpandService userExpandService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IZyzActivityTimePeriodShowService showService;

    @Autowired
    private IZyzActivityScheduleService scheduleService;

    @Value("${send-message}")
    private Boolean isSendMsg = false;



    @Override
    public void sendToCurrentUser(String code, Object... params) {
        MsgTemplate template;
        try {
            template = msgTemplateService.getByCode(code);
        } catch (Exception e) {
            log.error("模版获取异常！", e);
            return;
        }
        if (MPW_SMS.equals(template.getPushWay())) {
            String currentUserMobile = Oauth2Util.getMobile();
            if (ObjectUtil.isEmpty(currentUserMobile)) {
                log.error("无法获取当前用户手机号！");
                return;
            }
            commonSend(code, Collections.singletonList(currentUserMobile), params);
            return;
        }
        Serializable userId = Oauth2Util.getUserId();
        if (ObjectUtil.isEmpty(userId)) {
            log.error("无法获取当前用户信息！");
            return;
        }
        User user = userService.getById(userId);
        String maOpenId = user.getMaOpenId();
        if (StringUtil.isEmpty(maOpenId)) {
            log.error("当前用户未绑定小程序openid！");
            return;
        }
        commonSend(code, Collections.singletonList(maOpenId), params);
    }

    @Override
    public void sendToPointUser(String code, List<Long> userIds, Object... params) {
        if (CollectionUtil.isEmpty(userIds)) {
            log.error("请传入接收者的用户id！");
            return;
        }
        log.info("消息推送给用户-用户id：{}", userIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        commonSendByUsers(code, userIds, params);
    }

    @Override
    public void sendToTeamLinkMan(String code, List<Long> teamIds, Object... params) {
        if (CollectionUtil.isEmpty(teamIds)) {
            log.error("请传入团队id！");
            return;
        }
        log.info("消息推送给团队联系人-团队id：{}", teamIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<ZyzTeam> teams = teamService.lambdaQuery().in(ZyzTeam::getId, teamIds).list();
        if (CollectionUtil.isEmpty(teams)) {
            log.error("未获取到团队信息！");
            return;
        }
        commonSendByPhones(code, teams.stream().map(ZyzTeam::getAdminContact).collect(Collectors.toList()), params);
    }

    @Override
    public void sendToTeamManager(String code, List<Long> teamIds, Object... params) {
        if (CollectionUtil.isEmpty(teamIds)) {
            log.error("请传入团队id！");
            return;
        }
        log.info("消息推送给团队管理员-团队id：{}", teamIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<Long> userIds = userExpandService.getUserByRoleAndOrg(Collections.singletonList(ROLE_CODE_TEAM_ADMIN), USER_PRI_TYPE_TEAM, teamIds, null);
        if (CollectionUtil.isEmpty(userIds)) {
            log.error("未获取到团队管理员！");
            return;
        }
        commonSendByUsers(code, userIds, params);
    }

    @Override
    public void sendToVolunteer(String code, List<Long> volunteerIds, Object... params) {
        if (CollectionUtil.isEmpty(volunteerIds)) {
            log.error("请传入志愿者id！");
            return;
        }
        log.info("消息推送给志愿者-志愿者id：{}", volunteerIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<ZyzVolunteer> volunteers = volunteerService.lambdaQuery().in(ZyzVolunteer::getId, volunteerIds).eq(ZyzVolunteer::getWriteOff, false).list();
        if (CollectionUtil.isEmpty(volunteers)) {
            log.error("未获取到志愿者信息！");
            return;
        }
        List<String> phones = volunteers.stream().map(ZyzVolunteer::getPhone).collect(Collectors.toList());
        commonSendByPhones(code, phones, params);
    }

    @Override
    public void sendToAuditOrg(String code, List<String> orgCodes, Object... params) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            log.error("请传入审核组织code！");
            return;
        }
        log.info("消息推送给审核组织-组织code：{}", String.join(COMMA, orgCodes));
        List<Long> userIds = userExpandService.getUserByRoleAndOrg(Arrays.asList(ROLE_CODE_COMMUNITY_ADMIN, ROLE_CODE_SUB_ASSOCIATION_ADMIN, ROLE_CODE_ASSOCIATION_ADMIN), USER_PRI_TYPE_ORG, null, orgCodes);
        if (CollectionUtil.isEmpty(userIds)) {
            log.error("未获取到审核组织管理员！");
            return;
        }
        commonSendByUsers(code, userIds, params);
    }

    @Override
    public void sendToActivityPublisher(String code, List<Long> activityIds, Object... params) {
        if (CollectionUtil.isEmpty(activityIds)) {
            log.error("请传入活动id！");
            return;
        }
        log.info("消息推送给活动发布者-活动id：{}", activityIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<ZyzActivity> activities = activityService.lambdaQuery().in(ZyzActivity::getId, activityIds).list();
        if (CollectionUtil.isEmpty(activities)) {
            log.error("未获取到活动信息！");
            return;
        }
        List<Long> creators = activities.stream().map(ZyzActivity::getCreator).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(creators)) {
            log.error("未获取到活动发布人！");
            return;
        }
        commonSendByUsers(code, creators, params);
    }

    @Override
    public void sendToRequirementPublisher(String code, List<Long> requirementIds, Object... params) {
        if (CollectionUtil.isEmpty(requirementIds)) {
            log.error("请传入需求id！");
            return;
        }
        log.info("消息推送给需求发布者-需求id：{}", requirementIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<ZyzRequirement> requirements = requirementService.lambdaQuery().in(ZyzRequirement::getId, requirementIds).list();
        if (CollectionUtil.isEmpty(requirements)) {
            log.error("未获取到需求信息！");
            return;
        }
        List<Long> creators = requirements.stream().map(ZyzRequirement::getCreator).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(creators)) {
            log.error("未获取到需求发布人！");
            return;
        }
        commonSendByUsers(code, creators, params);
    }

    @Override
    public void sendToResourcePublisher(String code, List<Long> resourceIds, Object... params) {
        if (CollectionUtil.isEmpty(resourceIds)) {
            log.error("请传入资源id！");
            return;
        }
        log.info("消息推送给资源发布者-资源id：{}", resourceIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<ZyzResource> resources = resourceService.lambdaQuery().in(ZyzResource::getId, resourceIds).list();
        if (CollectionUtil.isEmpty(resources)) {
            log.error("未获取到资源信息！");
            return;
        }
        List<Long> creators = resources.stream().map(ZyzResource::getCreator).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(creators)) {
            log.error("未获取到资源发布人！");
            return;
        }
        commonSendByUsers(code, creators, params);
    }

    @Override
    public void sendToNewsPublisher(String code, List<Long> newsIds, Object... params) {
        if (CollectionUtil.isEmpty(newsIds)) {
            log.error("请传入新闻id！");
            return;
        }
        log.info("消息推送给新闻发布者-活动id：{}", newsIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<News> newsList = newsService.lambdaQuery().in(News::getId, newsIds).list();
        if (CollectionUtil.isEmpty(newsList)) {
            log.error("未获取到新闻信息！");
            return;
        }
        List<Long> creators = newsList.stream().map(News::getCreator).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(creators)) {
            log.error("未获取到新闻发布人！");
            return;
        }
        commonSendByUsers(code, creators, params);
    }

    @Override
    public void sendToActivityShowPublisher(String code, List<Long> showIds, Object... params) {
        if (CollectionUtil.isEmpty(showIds)) {
            log.error("请传入活动公示id！");
            return;
        }
        log.info("消息推送给活动发布者-活动id：{}", showIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<ZyzActivityTimePeriodShow> shows = showService.lambdaQuery().in(ZyzActivityTimePeriodShow::getId, showIds).list();
        if (CollectionUtil.isEmpty(shows)) {
            log.error("未获取到活动公示信息！");
            return;
        }
        List<Long> creators = shows.stream().map(ZyzActivityTimePeriodShow::getCreator).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(creators)) {
            log.error("未获取到活动公示发布人！");
            return;
        }
        commonSendByUsers(code, creators, params);
    }

    @Override
    public void sendToActivitySchedulePublisher(String code, List<Long> scheduleIds, Object... params) {
        if (CollectionUtil.isEmpty(scheduleIds)) {
            log.error("请传入阵地计划id！");
            return;
        }
        log.info("消息推送给计划发布者-计划id：{}", scheduleIds.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        List<ZyzActivitySchedule> schedules = scheduleService.lambdaQuery().in(ZyzActivitySchedule::getId, scheduleIds).list();
        if (CollectionUtil.isEmpty(schedules)) {
            log.error("未获取到阵地计划信息！");
            return;
        }
        List<Long> creators = schedules.stream().map(ZyzActivitySchedule::getCreator).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(creators)) {
            log.error("未获取到阵地计划发布人！");
            return;
        }
        commonSendByUsers(code, creators, params);
    }

    public void commonSendByPhones(String code, List<String> phones, Object... params) {
        if (CollectionUtil.isEmpty(phones)) {
            log.error("未获取到手机号！");
            return;
        }
        log.info("手机号：{}", String.join(COMMA, phones));
        MsgTemplate template;
        try {
            template = msgTemplateService.getByCode(code);
        } catch (Exception e) {
            log.error("模版获取异常！", e);
            return;
        }
        String pushWay = template.getPushWay();
        if (MPW_SMS.equals(pushWay)) {
            commonSend(code, phones, params);
            return;
        }
        List<User> users = userService.lambdaQuery().in(User::getMobile, phones).list();
        if (CollectionUtil.isEmpty(users)) {
            log.error("手机号无法获取到用户信息！");
            return;
        }
        log.info("通过手机号获取到如下用户(id)：{}", users.stream().map(it -> String.valueOf(it.getId())).collect(Collectors.joining(COMMA)));
        List<String> receivers = users.stream().map(User::getMaOpenId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(receivers)) {
            log.error("无法获取用户的 openid！");
            return;
        }
        commonSend(code, receivers, params);
    }

    private void commonSendByUsers(String code, List<Long> userIds, Object... params) {
        MsgTemplate template;
        try {
            template = msgTemplateService.getByCode(code);
        } catch (Exception e) {
            log.error("模版获取异常！", e);
            return;
        }
        List<String> receivers = getReceivers(userIds, template.getPushWay());
        if (receivers != null && receivers.size() > 0) {
            commonSend(code, receivers, params);
        }
    }

    private void commonSend(String code, List<String> receivers, Object... params) {
        if (!isSendMsg ) {
            if (CollectionUtil.isEmpty(receivers)) {
                log.error("未获取到接收者信息！");
                return;
            }
            //获取白名单
            Dict dict = dictService.getByCode(MSG_SEND_WHITE_LIST);
            if (dict.getValue() == null) {
                log.warn("信息发送功能关闭！");
                return;
            }
            List<String> sendList = Arrays.stream(dict.getValue().split(COMMA)).map(String::valueOf).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(sendList)) {
                log.warn("信息发送功能关闭！");
                return;
            }
            //取发送名单与白名单交集
            receivers.retainAll(sendList);
            if (receivers.isEmpty()) {
                log.warn("信息发送功能关闭！");
                return;
            }
        }
        if (CollectionUtil.isEmpty(receivers)) {
            log.error("未获取到接收者信息！");
            return;
        }
        newSingleThreadScheduledExecutor.execute(() -> msgTemplateService.sendTmpMsg(code,receivers, params));
    }

    private List<String> getReceivers(List<Long> userIds, String type) {
        List<User> users = userService.lambdaQuery().in(BaseEntity::getId, userIds).list();
        if (CollectionUtil.isEmpty(users)) {
            return null;
        }
        return MPW_SMS.equals(type) ?
                users.stream().map(User::getMobile).filter(StringUtil::isNotEmpty).collect(Collectors.toList()) :
                users.stream().map(User::getMaOpenId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
    }
}
