package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbServiceType;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 苏州志愿者平台服务领域服务接口
 */
public interface ISgbServiceTypeService extends IBaseService<SgbServiceType> {
    
    /**
     * 同步服务领域信息
     */
    void syncServiceType();
    
    /**
     * 根据类型编号获取服务领域信息
     * @param typeId 类型编号
     * @return 服务领域信息
     */
    SgbServiceType getByTypeId(String typeId);

    /**
     * 获取服务领域树结构
     * @return 服务领域树列表
     */
    List<SgbServiceType> getServiceTypeTree();

    /**
     * 获取服务领域级联选择树结构
     * @return 级联选择树列表
     */
    List<CascaderExtraDto> getServiceTypeCascaderTree();
}