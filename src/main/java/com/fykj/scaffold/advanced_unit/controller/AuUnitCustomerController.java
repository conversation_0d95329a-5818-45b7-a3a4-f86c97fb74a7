package com.fykj.scaffold.advanced_unit.controller;

import com.fykj.scaffold.advanced_unit.domain.dto.UnitSaveDTO;
import com.fykj.scaffold.advanced_unit.service.IAuUnitService;
import com.fykj.scaffold.support.syslog.AuditLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;

import javax.validation.Valid;

/**
 * 文明单位-申请表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@RestController
@Api(tags = "用户端(需登录)-文明单位-单位信息")
@RequestMapping("/admin/customer/advanced-unit/unit")
public class AuUnitCustomerController {

    @Autowired
    private IAuUnitService baseService;

    @AuditLog("填报文明单位")
    @ApiOperation("填报文明单位")
    @PostMapping("/submit")
    public Result save(@RequestBody @Valid UnitSaveDTO dto) {
        baseService.submitUnit(dto, false);
        return new Result();
    }

}
