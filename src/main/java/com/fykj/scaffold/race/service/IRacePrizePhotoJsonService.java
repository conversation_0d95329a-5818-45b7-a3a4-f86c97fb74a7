package com.fykj.scaffold.race.service;

import com.fykj.scaffold.race.domain.entity.Photo;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.race.domain.entity.RacePrizePhotoJson;

import java.util.List;

/**
 * 摄影大赛-获奖作品json
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-06-08
 */
public interface IRacePrizePhotoJsonService extends IBaseService<RacePrizePhotoJson> {

    boolean saveJson(RacePrizePhotoJson entity);

    /**
     * 获取获取作品
     * @param gradeId
     * @return
     */
    List<Photo> getPrizePhoto(Long gradeId);


    /**
     * 获取获取作品
     * @param gradeId
     * @return
     */
    RacePrizePhotoJson getJson(Long gradeId);
}

