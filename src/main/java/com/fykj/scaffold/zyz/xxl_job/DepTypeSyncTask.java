package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.sync.service.IZyzSyncDepTypeDictService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 同步职能部门类型
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DepTypeSyncTask {

    @Autowired
    private IZyzSyncDepTypeDictService depTypeDictService;

    @XxlJob("Dept_Type_Sync_Handler")
    public void deptTypeSyncJobHandler() {
        XxlJobHelper.log("Dept_Type_Sync_Handler-Job, START!");
        try {
            depTypeDictService.syncDepType();
        } catch (Exception e) {
            log.error("职能部门同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Dept_Type_Sync_Handler-Job, END!");
    }
}
