package com.fykj.scaffold.security.oauth2.listener;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.EventListener;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.event.AbstractAuthenticationFailureEvent;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

@Component
public class MyAuthenticationFailureListener implements ApplicationListener<AbstractAuthenticationFailureEvent> {

    @EventListener
    public void onApplicationEvent(AbstractAuthenticationFailureEvent event) {
        if (event.getException().getCause() instanceof LockedException) {
            throw (LockedException)event.getException().getCause();
        }
        if (event.getException().getCause() instanceof UsernameNotFoundException) {
            throw new LockedException(event.getException().getCause().getMessage());
        }
    }
}
