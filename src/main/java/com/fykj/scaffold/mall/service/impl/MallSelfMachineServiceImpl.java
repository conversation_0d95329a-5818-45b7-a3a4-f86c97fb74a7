package com.fykj.scaffold.mall.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallSelfMachine;
import com.fykj.scaffold.mall.domain.params.MallSelfMachineParams;
import com.fykj.scaffold.mall.mapper.MallSelfMachineMapper;
import com.fykj.scaffold.mall.service.IMallSelfMachineService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.util.List;


/**
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallSelfMachineServiceImpl extends BaseServiceImpl<MallSelfMachineMapper, MallSelfMachine> implements IMallSelfMachineService {
    /**
     * 分页查询--后端页面
     *
     * @param params
     * @return
     */
    @Override
    public IPage<MallSelfMachine> getPage(MallSelfMachineParams params) {
        if (params == null) {
            params = new MallSelfMachineParams();
        }
        IPage<MallSelfMachine> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public IPage<MallSelfMachine> pageForApi(MallSelfMachineParams params) {
        if (params == null) {
            params = new MallSelfMachineParams();
        }
        IPage<MallSelfMachine> iPage = baseMapper.pageForApi(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public boolean save(MallSelfMachine machine) {
        MallSelfMachine selfMachine = lambdaQuery().eq(MallSelfMachine::getMachineNo, machine.getMachineNo()).one();
        if (selfMachine != null) {
            throw new BusinessException(ResultCode.EXIST, "机器编码重复");
        }
        return super.save(machine);
    }

    @Override
    public List<MallSelfMachine> getMachinesByCurrOrg() {
        List<MallSelfMachine> result = baseMapper.getMachinesByCurrOrg();
        String orgCode = Oauth2Util.getOrgCode();
        result.forEach(it -> {
            DictTransUtil.trans(it);
            it.setEnableSelect(StringUtil.isNotEmpty(orgCode) && orgCode.equals(it.getOrgCode()));
        });
        return result;
    }
}
