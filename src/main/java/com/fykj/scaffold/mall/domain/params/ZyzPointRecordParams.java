package com.fykj.scaffold.mall.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 积分商城-积分历史记录
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("积分流水记录查询参数")
public class ZyzPointRecordParams extends BaseParams {
    private String name;
    private String certificateId;
    private BigDecimal maxExchangeBeforePoint;
    private BigDecimal minExchangeBeforePoint;
    private BigDecimal maxExchangeAfterPoint;
    private BigDecimal minExchangeAfterPoint;
    private String type;
    private Long volunteerId;

    /**
     * 业务类型
     */
    private String bizType;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime startTime;


    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime endTime;

    /**
     * 描述
     */
    private String remark;
}
