package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.dto.CascaderDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiCategory;
import com.fykj.scaffold.security.business.domain.params.SysApiCategoryParams;
import com.fykj.scaffold.security.business.service.ISysApiCategoryService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 接口目录表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@RestController
@RequestMapping("/admin/sys_api_category")
public class SysApiCategoryController extends BaseController<ISysApiCategoryService, SysApiCategory,SysApiCategoryParams> {

    @GetMapping("/cascader")
    @ApiOperation("获取级联树")
    public JsonResult<List<CascaderDto>> cascader() {
        List<CascaderDto> categories = baseService.cascader();
        return new JsonResult<>(categories);
    }

    @GetMapping("/tree")
    @ApiOperation("获取级联树")
    public JsonResult<List<SysApiCategory>> tree() {
        List<SysApiCategory> categories = baseService.tree();
        return new JsonResult<>(categories);
    }

    @GetMapping("/treeOfAll")
    @ApiOperation("获取级联树")
    public JsonResult<SysApiCategory> treeOfAll() {
        List<SysApiCategory> categories = baseService.tree();
        SysApiCategory categoryTree = new SysApiCategory();
        categoryTree.setId(1L);
        categoryTree.setName("全部");
        categoryTree.setLevel(0);
        categoryTree.setSubCategories(categories);
        return new JsonResult<>(categoryTree);
    }

    /**
     * 设置启用/禁用
     *
     * @param id id
     */
    @SysLogMethod("栏目启用禁用")
    @ApiOperation(value = "设置启用/禁用")
    @PostMapping(value = "/setEnable")
    public Result setEnable(@RequestParam Long id) {
        if (baseService.setEnable(id)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }

    @SysLogMethod("全部列表")
    @ApiOperation("获取全部启用状态方法")
    @GetMapping({"/allOfEnable"})
    public List<SysApiCategory> allOfEnable() {
        return this.baseService.listOfEnable();
    }
}
