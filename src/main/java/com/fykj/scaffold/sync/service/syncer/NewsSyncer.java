package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.portal_website.conns.PortalWebsiteCons;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.cms.SyncCms;
import com.fykj.volunteer.sync.util.domain.cms.SyncCmsResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.LocalDateTimeUtil;

import java.time.LocalDateTime;

/**
 * 新闻同步
 */
@Component
public class NewsSyncer {

    @Autowired
    private INewsService newsService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog sync(long newsId, long syncBillId) {
        News news = newsService.getById(newsId);
        //新闻审核通过，且处于待同步状态需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(news.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }

        if (!PortalWebsiteCons.NAS_PASS.equals(news.getAuditStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此新闻还未审核通过");
        }

        //团队发布
        String publishIdCard;
        if (news.getTeamPublish()) {
            ZyzTeam newsTeam = teamService.getById(news.getTeamId());
            publishIdCard = newsTeam.getCuratorCard();
        } else {
            publishIdCard = Cons.SyncConstant.DEFAULT_ID_CARD;
        }

        SyncCms syncCms = new SyncCms();
        syncCms.setCategoryName("志愿快讯");
        syncCms.setTitle(news.getTitle());
        syncCms.setKeywords(news.getTitle());
        syncCms.setContent(news.getTitle());
        syncCms.setInputDate(LocalDateTimeUtil.formatDateTime(news.getEffectiveDate()));
        syncCms.setSource("园区志愿者平台");
        syncCms.setTitle(news.getTitle());
        syncCms.setContent(news.getDescription());
        syncCms.setIdcode(publishIdCard);
        syncCms.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(news.getTitleImgUrl())));
        syncCms.setUniqueCode(String.valueOf(news.getId()));

        SyncCmsResult syncCmsResult = ApiWebService.syncCmsCBC(syncCms);

        news.setSyncTime(LocalDateTime.now());
        news.setSyncRemark(syncCmsResult.getMsg());
        //成功
        if ("同步成功".equals(syncCmsResult.getMsg())) {
            news.setSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (syncCmsResult.getMsg().contains("数据重复")) {
            news.setSync(Cons.PlatformSyncState.SUCCESS);
            news.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            news.setSync(Cons.PlatformSyncState.FAILURE);
        }
        newsService.updateById(news);

        //记录日志
        return logService.buildSyncLog(Cons.PlatformSyncBiz.NEWS_SYNC, syncCmsResult.getCode(), syncCmsResult.getMsg()
                , syncBillId, news.getId(), news.getTitle()
                , JSONUtil.toJsonStr(syncCms), JSONUtil.toJsonStr(syncCmsResult));
    }
}
