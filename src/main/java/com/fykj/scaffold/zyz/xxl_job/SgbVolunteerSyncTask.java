package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncPushService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * SGB志愿者同步定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbVolunteerSyncTask {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ISgbSyncPushService sgbSyncPushService;

    @XxlJob("SGB_Volunteer_Sync_Handler")
    public void sgbVolunteerSyncJobHandler() {
        XxlJobHelper.log("SGB_Volunteer_Sync_Handler-Job, START!");

        // SGB志愿者同步逻辑
        List<Long> sgbVolunteerIds = volunteerService.getSgbNeedSync();
        if (CollectionUtil.isNotEmpty(sgbVolunteerIds)) {
            try {
                sgbSyncPushService.syncVolunteerBatch(sgbVolunteerIds);
            } catch (Exception e) {
                log.error("SGB志愿者批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        
        XxlJobHelper.log("SGB_Volunteer_Sync_Handler-Job, END!");
    }
}
