package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzOrgTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzOrgTeamService;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActp;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActpResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.fykj.scaffold.support.conns.Cons.*;

/**
 * 活动同步
 */
@Component
public class ActivitySyncer {

    @Autowired
    private IZyzActivityService activityService;
    @Autowired
    private IZyzSyncLogService logService;

    @Autowired
    private IZyzTeamService zyzTeamService;

    @Autowired
    private IZyzOrgTeamService zyzOrgTeamService;

    @Autowired
    private ISysOrgService orgService;

    public ZyzSyncLog sync(long activityId, long syncBillId) {
        ZyzActivity activity = activityService.getById(activityId);
        //活动审核通过，当前时间大于活动结束时间，且处于待同步状态需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(activity.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }

        if (!ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(activity.getAuditStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动还未审核通过");
        }

//        if (!activity.getEndTime().isBefore(LocalDateTime.now())) {
//            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动尚未结束");
//        }

        SyncActp syncAct = new SyncActp();
        syncAct.setUniCode(String.valueOf(activity.getId()));
        syncAct.setOrgId(calculateOrgId(activity));
        syncAct.setName(activity.getName());
        syncAct.setServiceField(activity.getBelongFieldEnd());
        if (StringUtil.isNotEmpty(activity.getActivityPlaceRegionCode())) {
            syncAct.setRegionCode(activity.getActivityPlaceRegionCode());
        } else {
            syncAct.setRegionCode(SyncConstant.AS_REGION_CODE);
        }
        if (StringUtil.isNotEmpty(activity.getActSynopsis())) {
            syncAct.setDescs(activity.getActSynopsis());
        } else {
            syncAct.setDescs(activity.getName());
        }
        syncAct.setAddress(activity.getAddress());
        syncAct.setStartDate(activity.getStartTime().format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        syncAct.setEndDate(activity.getEndTime().format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        syncAct.setStartTime(activity.getStartTime().format(DateTimeFormatter.ofPattern(TIME_FORMAT)));
        syncAct.setEndTime(activity.getEndTime().format(DateTimeFormatter.ofPattern(TIME_FORMAT)));
        syncAct.setLinkName(activity.getContactPerson());
        syncAct.setLinkPhone(activity.getContactPhone());
        syncAct.setRecordWay(SyncActp.recordWay.TIME_SLOT);
        syncAct.setServiceTime(null);
        syncAct.setTxMapLan(activity.getLongitude() != null ? activity.getLongitude().toString() : SyncConstant.DEFAULT_LONGITUDE);
        syncAct.setTxMapLat(activity.getLatitude() != null ? activity.getLatitude().toString() : SyncConstant.DEFAULT_LATITUDE);
        syncAct.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(activity.getPicture())));
        syncAct.setServiceTarget("99");

        SyncActpResult syncActpResult = ApiWebService.syncActivity(syncAct);

        activity.setSyncTime(LocalDateTime.now());
        activity.setSyncRemark(syncActpResult.getMsg());
        //成功
        if ("同步成功".equals(syncActpResult.getMsg())) {
            activity.setSyncId(syncActpResult.getApId());
            activity.setSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (syncActpResult.getMsg().contains("唯一号已存在")) {
            if (StringUtil.isNotEmpty(syncActpResult.getApId())) {
                activity.setSyncId(syncActpResult.getApId());
            }
            activity.setSync(Cons.PlatformSyncState.SUCCESS);
            activity.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            activity.setSync(Cons.PlatformSyncState.FAILURE);
        }

        // 启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.ACTIVITY_SYNC, syncActpResult.getCode(), syncActpResult.getMsg()
                , syncBillId, activity.getId(), activity.getName()
                , JSONUtil.toJsonStr(syncAct), JSONUtil.toJsonStr(syncActpResult));

        // 回写业务
        activityService.updateById(activity);

        return log;
    }

    private String calculateOrgId(ZyzActivity activity) {
        // 如果是团队活动的话用团队市区id，阵地的话，中心/所用假团队同步，社区的话同步一个同名的团队，然后用这个同步的团队市区id
        if (activity.getTeamPublish() != null && activity.getTeamPublish() && activity.getTeamId() != null) {
            Long teamId = activity.getTeamId();
            //获取团队同步后的市区id
            ZyzTeam team = zyzTeamService.lambdaQuery().eq(ZyzTeam::getId, teamId).one();
            return team == null ? SyncConstant.AS_ORG_ID : (StringUtil.isEmpty(team.getSyncId()) ? SyncConstant.AS_ORG_ID : team.getSyncId());

        }
        if (activity.getTeamPublish() != null && !activity.getTeamPublish() && activity.getPublishOrgCode() != null) {
            String publishOrgCode = activity.getPublishOrgCode();
            SysOrg org = orgService.getByCode(publishOrgCode);
            Long teamId = org == null ? null : org.getMainTeamId();
            String orgTeamSyncId = null;
            if (teamId != null) {
                ZyzTeam team = zyzTeamService.lambdaQuery().eq(ZyzTeam::getId, teamId).one();
                orgTeamSyncId = team == null ? null : (StringUtil.isEmpty(team.getSyncId()) ? null : team.getSyncId());
            }
            return orgTeamSyncId == null ? SyncConstant.AS_ORG_ID : orgTeamSyncId;
//            //获取组织同名团队的市区id
//            ZyzOrgTeam orgTeam = zyzOrgTeamService.lambdaQuery().eq(ZyzOrgTeam::getOrgCode, activity.getPublishOrgCode()).one();
//            //组织同名团队是否存在而且已经同步，否则的话还是用假团队id
//            if (orgTeam != null) {
//                return orgTeam.getSyncId() == null ? SyncConstant.AS_ORG_ID : orgTeam.getSyncId();
//            }
        }
        return SyncConstant.AS_ORG_ID;
    }
}
