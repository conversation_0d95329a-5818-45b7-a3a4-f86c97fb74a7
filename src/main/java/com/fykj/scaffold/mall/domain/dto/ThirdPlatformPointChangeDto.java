package com.fykj.scaffold.mall.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ThirdPlatformPointChangeDto {
    /**
     * 业务类型
     */
    private String thirdPlatformCode;

    /**
     * 唯一流水号
     */
    private long bizId;

    /**
     * 志愿者身份证
     */
    private String idCard;

    /**
     * 加分或减分
     */
    private String type;

    /**
     * 变动积分数（正数）,0.5刻度
     */
    private BigDecimal changePoint;

    /**
     * 备注
     */
    private String remark;

    /**
     * 签名
     */
    private String sign;

    /**
     * 商品id
     */
    private String goodsId;

}
