package com.fykj.scaffold.dynamic.controller;

import com.fykj.scaffold.dynamic.domain.entity.DynamicForm;
import com.fykj.scaffold.dynamic.domain.params.DynamicFormParams;
import com.fykj.scaffold.dynamic.service.IDynamicFormService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 模板信息-接口控制器
 *
 * @date 2025-05-19
 */
@RestController
@RequestMapping("/admin/dynamic/form")
@Api(tags = "动态表单-模板信息-管理接口")
public class DynamicFormController
        extends BaseController<IDynamicFormService, DynamicForm, DynamicFormParams> {

    @SysLogMethod("新增表单")
    @ApiOperation("新增表单")
    @PostMapping("/add")
    public Result add(@RequestBody @Validated DynamicForm form) {
        return baseService.add(form) ? OK : new Result(result.ResultCode.FAIL);
    }

    @SysLogMethod("编辑表单")
    @ApiOperation("编辑表单")
    @PostMapping("/edit")
    public Result edit(@RequestBody @Validated DynamicForm form) {
        return baseService.edit(form) ? OK : new Result(result.ResultCode.FAIL);
    }

    @ApiOperation("获取表单详情")
    @GetMapping("/detail")
    public JsonResult<DynamicForm> detail(@RequestParam @NotNull(message = "表单ID不能为空") Long id) {
        return new JsonResult<>(baseService.detail(id));
    }

    @ApiOperation("获取全部方法")
    @PostMapping({"/list"})
    public JsonResult<List<DynamicForm>> all(@RequestBody DynamicFormParams params) {
        List<DynamicForm> result = baseService.list(params);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }
}
