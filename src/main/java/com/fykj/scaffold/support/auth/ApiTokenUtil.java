package com.fykj.scaffold.support.auth;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * API Token工具类
 * 用于获取当前请求的API Token信息
 */
public class ApiTokenUtil {

    /**
     * 获取当前请求中的API Token
     * @param headerName 请求头名称
     * @return API Token值，如果不存在则返回null
     */
    public static String getCurrentToken(String headerName) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }
        
        HttpServletRequest request = attributes.getRequest();
        return request.getHeader(headerName);
    }
    
    /**
     * 获取当前请求中的API Token，使用默认的请求头名称 X-API-TOKEN
     * @return API Token值，如果不存在则返回null
     */
    public static String getCurrentToken() {
        return getCurrentToken("X-API-TOKEN");
    }
} 