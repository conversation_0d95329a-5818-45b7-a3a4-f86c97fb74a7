package com.fykj.scaffold.advanced_unit.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitImportDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.UnitAuditDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.UnitSaveDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuUnit;
import com.fykj.scaffold.advanced_unit.domain.params.AuUnitParams;
import com.fykj.scaffold.advanced_unit.domain.params.UnitActivityParams;
import com.fykj.scaffold.advanced_unit.service.IAuUnitService;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.dto.ActivityApiDto;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static constants.Mark.COMMA;

/**
 * 文明单位-申请表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@RestController
@Api(tags = "管理端-文明单位-单位信息")
@RequestMapping("/admin/manager/advanced-unit/unit")
public class AuUnitController extends BaseController<IAuUnitService, AuUnit, AuUnitParams> {

    @AuditLog("填报文明单位")
    @ApiOperation("填报文明单位")
    @PostMapping("/submit")
    public Result save(@RequestBody @Valid UnitSaveDTO dto) {
        baseService.submitUnit(dto, true);
        return OK;
    }

    @AuditLog("编辑文明单位")
    @ApiOperation("编辑文明单位")
    @PostMapping("/edit")
    public Result edit(@RequestBody @Valid AuUnit unit) {
        baseService.editUnit(unit);
        return OK;
    }

    @AuditLog("审核文明单位")
    @ApiOperation("审核文明单位")
    @PostMapping("/audit")
    public Result audit(@RequestBody @Valid UnitAuditDTO dto) {
        baseService.audit(dto);
        return OK;
    }

    @ApiOperation("分页查询")
    @PostMapping({"/page"})
    public JsonResult<IPage<AuUnit>> queryPage(
            @RequestBody(required = false) AuUnitParams params) {
        return new JsonResult<>(baseService.queryPage(params));
    }

    @ApiOperation("根据id获取")
    @GetMapping("/detail")
    public JsonResult<AuUnit> get(@RequestParam Long id) {
        return new JsonResult<>(baseService.queryDetail(id));
    }

    @ApiOperation("批量上下架")
    @PostMapping("/status/switch")
    public Result statusSwitch(@RequestParam String id) {
        baseService.updateShowStatus(Arrays.stream(id.split(COMMA)).map(Long::valueOf).collect(Collectors.toList()));
        return OK;
    }

    @ApiOperation("导入")
    @PostMapping(value = "/import")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<AuUnitImportDTO> failureList = baseService.dataImport(excel);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "unit_import_error.xlsx", AuUnitImportDTO.class);
        }
    }

    @ApiOperation("导出")
    @PostMapping("/export")
    public void exportSignUp(@RequestBody @Valid AuUnitParams params) {
        List<AuUnit> list = baseService.queryList(params);
        ExcelUtil.fillExcel(list, "unit_export.xlsx", AuUnit.class);
    }

    @ApiOperation("文明单位导入Excel模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/unit_import.xlsx");
    }


    @ApiOperation("展开活动(文明实践(举办活动))-分页查询")
    @PostMapping("/activity/page")
    public JsonResult<IPage<ActivityApiDto>> activityPage(
            @RequestBody @Valid UnitActivityParams params) {
        return new JsonResult<>(baseService.queryActivityPage(params));
    }

    @ApiOperation("单位风采(团队新闻)-分页查询")
    @PostMapping("/news/page")
    public JsonResult<IPage<News>> newsPage(
            @RequestBody @Valid UnitActivityParams params) {
        return new JsonResult<>(baseService.queryNewsPage(params));
    }
}
