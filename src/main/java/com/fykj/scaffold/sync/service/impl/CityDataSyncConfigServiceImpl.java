package com.fykj.scaffold.sync.service.impl;

import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.sgb_docking.cons.SgbCons;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.service.ICityDataSyncConfigService;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;

import java.util.Optional;

/**
 * 城市数据同步配置服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class CityDataSyncConfigServiceImpl implements ICityDataSyncConfigService {

    /**
     * 默认同步补偿天数
     */
    private static final int DEFAULT_COMPENSATE_DAYS = 3;

    @Autowired
    private IDictService dictService;

    /**
     * 检查平台同步功能开关
     * 检查WMB同步功能是否开启，如果关闭则抛出异常
     * 
     * @throws BusinessException 当同步功能关闭时抛出异常
     */
    @Override
    public void checkPlatformSyncSwitch() {
        Optional.ofNullable(dictService.getByCode(SgbCons.SyncSwitch.WMB_SYNC_SWITCH))
                .map(Dict::getValue)
                .filter(Cons.COMMON_SWITCH.YES::equals)
                .orElseThrow(() -> new BusinessException(ResultCode.FAIL, "文明办同步功能已关闭"));
    }

    /**
     * 获取SGB同步补偿天数
     * 从字典配置中获取SGB同步补偿天数，如果配置不存在则返回默认值3天
     * 
     * @return SGB同步补偿天数，默认3天
     */
    @Override
    public int getSgbSyncCompensateDays() {
        try {
            Dict dict = dictService.getByCode(SgbCons.SyncCompensateDays.SGB_SYNC_COMPENSATE_DAYS);
            if (dict != null && dict.getValue() != null) {
                return Integer.parseInt(dict.getValue());
            }
        } catch (Exception e) {
            log.warn("获取SGB同步补偿天数配置失败，使用默认值{}天: {}", DEFAULT_COMPENSATE_DAYS, e.getMessage());
        }
        return DEFAULT_COMPENSATE_DAYS;
    }

    /**
     * 获取WMB同步补偿天数
     * 从字典配置中获取WMB同步补偿天数，如果配置不存在则返回默认值3天
     * 
     * @return WMB同步补偿天数，默认3天
     */
    @Override
    public int getWmbSyncCompensateDays() {
        try {
            Dict dict = dictService.getByCode(SgbCons.SyncCompensateDays.WMB_SYNC_COMPENSATE_DAYS);
            if (dict != null && dict.getValue() != null) {
                return Integer.parseInt(dict.getValue());
            }
        } catch (Exception e) {
            log.warn("获取WMB同步补偿天数配置失败，使用默认值{}天: {}", DEFAULT_COMPENSATE_DAYS, e.getMessage());
        }
        return DEFAULT_COMPENSATE_DAYS;
    }
}
