package com.fykj.scaffold.weixin.mp.domain.dto;

import lombok.Data;

@Data
public class WxOpenCodeLoginResultDto {
    /**
     * 是否成功
     */
    private boolean success = true;
    /**
     * 用户身份唯一标识（OPENID做了一层隐藏）
     */
    private String unionIdBindKey;
    /**
     * 用户名
     */
    private String username;
    /**
     * 登录oneCode
     */
    private String oneCode;

    public static WxOpenCodeLoginResultDto Unbind(String openIdBindKey) {
        WxOpenCodeLoginResultDto dto = new WxOpenCodeLoginResultDto();
        dto.setSuccess(false);
        dto.setUnionIdBindKey(openIdBindKey);
        return dto;
    }

    public static WxOpenCodeLoginResultDto Success(String username, String oneCode) {
        WxOpenCodeLoginResultDto dto = new WxOpenCodeLoginResultDto();
        dto.setOneCode(oneCode);
        dto.setUsername(username);
        return dto;
    }
}
