package com.fykj.scaffold.sync.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 市平台地域数据字典
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ZyzSyncRegionDictParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"regionCode", "regionName"})
    private String key;
}
