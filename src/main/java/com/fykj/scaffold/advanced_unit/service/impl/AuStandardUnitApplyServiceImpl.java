package com.fykj.scaffold.advanced_unit.service.impl;

import com.fykj.scaffold.advanced_unit.domain.entity.AuStandard;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardUnitApply;
import com.fykj.scaffold.advanced_unit.mapper.AuStandardUnitApplyMapper;
import com.fykj.scaffold.advanced_unit.service.IAuStandardService;
import com.fykj.scaffold.advanced_unit.service.IAuStandardUnitApplyService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 文明单位-申请表
 *
 * 服务实现类
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AuStandardUnitApplyServiceImpl
        extends BaseServiceImpl<AuStandardUnitApplyMapper, AuStandardUnitApply>
        implements IAuStandardUnitApplyService {

    @Autowired
    private IAuStandardService standardService;


    @Override
    public void createApplyByUnitIdAndYear(Long orgId, Integer civilizationYear) {
        AuStandard standard = standardService.getByYear(civilizationYear);
        AuStandardUnitApply apply = new AuStandardUnitApply();
        apply.setOrgId(orgId);
        apply.setStandardId(standard.getId());
        baseMapper.insert(apply);
    }
}
