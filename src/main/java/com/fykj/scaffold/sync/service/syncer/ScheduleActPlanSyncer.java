package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleActPlan;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleActPlanService;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleDetailService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_schedule_act_plan.SyncScheduleActPlan;
import com.fykj.volunteer.sync.util.domain.sync_schedule_act_plan.SyncScheduleActPlanResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;

@Component
public class ScheduleActPlanSyncer {

    @Autowired
    private IZyzSyncLogService logService;
    @Autowired
    private IZyzActivityScheduleActPlanService scheduleActPlanService;
    @Autowired
    private IZyzActivityScheduleDetailService scheduleDetailService;

    @Autowired
    private IZyzActivityService activityService;

    public ZyzSyncLog sync(long scheduleActPlanId, long syncBillId) {
        ZyzActivityScheduleActPlan scheduleActPlan = scheduleActPlanService.getById(scheduleActPlanId);
        if (Cons.PlatformSyncState.SUCCESS.equals(scheduleActPlan.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动时间段活动公示已同步成功，无需再次同步");
        }

        ZyzActivity activity = activityService.getById(scheduleActPlan.getActivityId());
        if (!Cons.PlatformSyncState.SUCCESS.equals(activity.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "关联活动还未同步，请先同步活动");
        }

        ZyzActivityScheduleDetail detail = scheduleDetailService.getById(scheduleActPlan.getScheduleDetailId());
        if (StringUtil.isEmpty(detail.getPreId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "阵地计划详情还未同步，请先同步阵地计划");
        }
        //拼装请求
        SyncScheduleActPlan syncScheduleActPlan = new SyncScheduleActPlan();
        syncScheduleActPlan.setUniCode(String.valueOf(scheduleActPlan.getId()));
        syncScheduleActPlan.setPreId(detail.getPreId());
        syncScheduleActPlan.setActpId(activity.getSyncId());
        // 调用同步接口
        SyncScheduleActPlanResult syncScheduleActPlanResult = ApiWebService.syncScheduleActPlan(syncScheduleActPlan);
        // 保存返回值
        //先给一个默认值
        scheduleActPlan.setSync(Cons.PlatformSyncState.FAILURE);
        //成功
        if (syncScheduleActPlanResult.getMsg().contains("同步成功") || syncScheduleActPlanResult.getMsg().contains("数据重复") || syncScheduleActPlanResult.getMsg().contains("存在与此活动预告已绑定")) {
            scheduleActPlan.setSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        if (syncScheduleActPlanResult.getMsg().contains("数据重复") || syncScheduleActPlanResult.getMsg().contains("存在与此活动预告已绑定")) {
            scheduleActPlan.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        scheduleActPlan.setSyncTime(LocalDateTime.now());
        scheduleActPlan.setSyncRemark(syncScheduleActPlanResult.getMsg());
        // 启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.SCHEDULE_ACT_PLAN_SYNC, syncScheduleActPlanResult.getCode(), syncScheduleActPlanResult.getMsg()
                , syncBillId, scheduleActPlan.getId(), "阵地计划活动关联"
                , JSONUtil.toJsonStr(syncScheduleActPlan), JSONUtil.toJsonStr(syncScheduleActPlanResult));
        // 回写业务
        scheduleActPlanService.updateById(scheduleActPlan);

        return log;
    }
}
