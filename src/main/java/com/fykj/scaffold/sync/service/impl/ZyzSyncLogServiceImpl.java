package com.fykj.scaffold.sync.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.conns.MsgCons;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.entity.MsgTmp;
import com.fykj.scaffold.message.service.IMsgSendService;
import com.fykj.scaffold.message.service.IMsgTmpService;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncWaringListDto;
import com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogStatisticsParams;
import com.fykj.scaffold.sync.mapper.ZyzSyncLogMapper;
import com.fykj.scaffold.sync.service.IPracticeSheetService;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 市平台信息同步日志
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ZyzSyncLogServiceImpl extends BaseServiceImpl<ZyzSyncLogMapper, ZyzSyncLog> implements IZyzSyncLogService {

    @Autowired
    private IPracticeSheetService practiceSheetService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IMsgTmpService msgTmpService;

    @Autowired
    private IMsgSendService msgSendService;


    /**
     * 记录日志一定要对啊
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ZyzSyncLog buildSyncLog(String bizType, String resultCode, String msg, long syncBillId, long bizId, String objName, String reqParam, String resp) {
        ZyzSyncLog syncLog = new ZyzSyncLog();
        syncLog.setResultCode(resultCode);
        syncLog.setBizType(bizType);
        syncLog.setBizId(bizId);
        syncLog.setSyncObjectName(objName);
        syncLog.setSyncTime(LocalDateTime.now());
        syncLog.setMessage(msg);
        syncLog.setSyncBillId(syncBillId);
        syncLog.setRequestParam(reqParam);
        syncLog.setResponse(resp);
        save(syncLog);
        return syncLog;
    }

    @Override
    public IPage<SyncStatisticsDto> statisticsPage(ZyzSyncLogStatisticsParams params) {
        // 获取实践单的数据
        if (StringUtil.equals(params.getBizType(), ZyzCons.SYNC_PRACTICE_SHEET)) {
            return practiceSheetService.statisticsPage(params);
        }
        // 获取实践单之外的数据
        return baseMapper.statisticsPage(params.getPage(), params);
    }

    @Override
    public List<SyncFailStatisticsDto> statisticsFailList(ZyzSyncLogStatisticsParams params) {
        List<SyncFailStatisticsDto> list = fetchStatisticsByBizType(params);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        int rank = 1;
        for (SyncFailStatisticsDto dto : list) {
            dto.setRankNum(rank++);
            dto.setErrorMsg(cleanErrorMessage(dto.getMessage()));
        }
        return list;
    }

    private String cleanErrorMessage(String message) {
        if (StringUtil.isEmpty(message)) {
            return "";
        }
        return message.replaceAll(ZyzCons.ERROR_PREFIX_REGEX, "");
    }

    private List<SyncFailStatisticsDto> fetchStatisticsByBizType(ZyzSyncLogStatisticsParams params) {
        if (ZyzCons.SYNC_PRACTICE_SHEET.equals(params.getBizType())) {
            return practiceSheetService.statisticsFailList(params);
        }
        return baseMapper.statisticsFailList(params);
    }

    @Override
    public List<ZyzSyncLogDto> exportStatistics(ZyzSyncLogStatisticsParams params) {
        if (StringUtil.isNotEmpty(params.getYearMon())) {
            params.setStart(null);
            params.setEnd(null);
        }

        // 获取实践单的数据
        if (StringUtil.equals(params.getBizType(), ZyzCons.SYNC_PRACTICE_SHEET)) {
            return practiceSheetService.exportStatistics(params);
        }
        // 获取实践单之外的数据
        return baseMapper.exportStatistics(params);
    }

    @Override
    public List<SyncWaringListDto> statisticsFailWaringList(String date) {
        if (StringUtil.isEmpty(date)) {
            throw new BusinessException(ResultCode.FAIL, "年月不能为空");
        }

        // 解析并格式化年月
        LocalDate targetDate = LocalDateTimeUtil.parseDate(date);
        String yearMonth = LocalDateTimeUtil.format(targetDate, "yyyy-MM");

        // 判断是否为当月前的数据（短信只标记历史月的）
        boolean isPastMonth = targetDate.isBefore(LocalDate.now().withDayOfMonth(1));

        // 获取业务类型
        Dict bizTypeDict = dictService.getByCode(ZyzCons.SYNC_BIZ_TYPE);
        List<SyncWaringListDto> baseList = baseMapper.getWaringList(bizTypeDict.getId());

        // 业务类型集合，包含基础 + 补充的
        List<SyncWaringListDto> resultList = Stream.concat(
                baseList.stream(),
                Stream.of(
                        new SyncWaringListDto("sync_biz_join_team", "绑定实践点与团队关系同步"),
                        new SyncWaringListDto("sync_practice_sheet", "实践单上报同步")
                )
        ).collect(Collectors.toList());


        List<SyncStatisticsDto> dataList = Stream.concat(
                        // 获取业务类型下的年月数据
                        baseMapper.getByYearMon(yearMonth).stream(),
                        // 获取实践单年月的数据
                        Stream.of(practiceSheetService.getByYearMon(yearMonth))
        ).filter(Objects::nonNull).collect(Collectors.toList());

        // 将数据转换成Map，key为bizType
        Map<String, SyncStatisticsDto> resultMap = dataList.stream()
                .collect(Collectors.toMap(SyncStatisticsDto::getBizType, Function.identity(), (a, b) -> a));

        int rate = dictService.getIntValueByCode(ZyzCons.SSN_RATE);
        for (SyncWaringListDto waringListDto : resultList) {
            // 设置年月
            waringListDto.setYearMon(yearMonth);
            // 获取本月业务类型对应的数据
            SyncStatisticsDto dto = resultMap.getOrDefault(waringListDto.getBizType(), new SyncStatisticsDto());
            // 设置本月数据
            waringListDto.setFailNum(dto.getFailNum());
            waringListDto.setSuccessNum(dto.getSuccessNum());
            waringListDto.setReportNum(dto.getAddNum());
            // 设置本月失败率
            int failRate = dto.getAddNum() == 0 ? 0 : dto.getFailNum() * 100 / dto.getAddNum();
            waringListDto.setFailRate(failRate);
            // 设置是否需要短信提醒 短信只标记历史月的 且失败率大于rate
            waringListDto.setSms(failRate > rate && isPastMonth);
        }
        return resultList;
    }

    @Override
    public void sendMsg() {
        int rate = dictService.getIntValueByCode(ZyzCons.SSN_RATE);
        String mobile = dictService.getStringValueByCode(ZyzCons.SSN_MOBILE);
        if (StringUtil.isEmpty(rate) || StringUtil.isEmpty(mobile)) {
            log.info("未配置通知比率或者手机号");
            return;
        }

        MsgTmp tmp = msgTmpService.getTmpByCode(MsgCons.MSG_SYNC_ERROR_RATE);
        if (tmp == null || StringUtil.isEmpty(tmp.getTmp())) {
            log.info("未配置短信模板");
            return;
        }
        // 获取上个月的
        LocalDate date = LocalDate.now().minusMonths(1);
        // 获取异常的业务类型名称
        List<String> bizNames = getExceedRateWarningList(rate, date);
        if (CollectionUtils.isEmpty(bizNames)) {
            log.info("本月无需要发送短信的记录");
            return;
        }

        // 封装短信内容
        String param = LocalDateTimeUtil.format(date, "yyyy年MM月") + String.join("、", bizNames);
        String msgContent = String.format(tmp.getTmp(), param, rate + Mark.PERCENT);

        // 构建短信实体
        MsgSend msg = buildMsgEntity(tmp, mobile, msgContent);
        // 保存短信记录
        msgSendService.save(msg);
        // 发送短信
        msgSendService.sendMessage(msg.getId());
    }

    private List<String> getExceedRateWarningList(int thresholdRate, LocalDate date) {
        List<SyncWaringListDto> list = statisticsFailWaringList(date.toString());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 过滤出失败率大于阈值的记录
        return list.stream()
                .filter(dto -> dto.getFailRate() > thresholdRate)
                .map(SyncWaringListDto::getBizTypeName)
                .collect(Collectors.toList());
    }

    private MsgSend buildMsgEntity(MsgTmp tmp, String mobiles, String msgContent) {
        // 构建短信实体
        MsgSend entity = new MsgSend();
        entity.setTitle(tmp.getTitle());
        entity.setSelfDefine(false);
        entity.setTmpId(tmp.getId());
        entity.setSendType(MsgCons.MSG_PUSH_TYPE_SMS);
        entity.setSelfDefineMsg(msgContent);
        entity.setMsgContent(msgContent);

        // 封装接收人信息
        List<MsgRecord> receivers = Arrays.stream(mobiles.split(","))
                .map(mobile -> {
                    MsgRecord record = new MsgRecord();
                    record.setReceiverMobile(mobile);
                    return record;
                })
                .collect(Collectors.toList());

        entity.setReceivers(receivers);
        return entity;
    }
}