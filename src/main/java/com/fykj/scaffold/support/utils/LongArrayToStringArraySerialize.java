package com.fykj.scaffold.support.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * List<long>转List<String>
 *
 * <AUTHOR>
 * @date 2022/04/21 10:49
 */
public class LongArrayToStringArraySerialize extends JsonSerializer<List<Long>> {

    @Override
    public void serialize(List<Long> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        String[] stringArray = value.stream().map(x -> Long.toString(x)).collect(Collectors.toList()).toArray(new String[]{});
        gen.writeArray(stringArray,0,stringArray.length);
    }
}
