package com.fykj.scaffold.capability.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.capability.domain.dto.*;
import com.fykj.scaffold.capability.domain.entity.Course;
import com.fykj.scaffold.capability.domain.params.CourseParams;
import com.fykj.scaffold.capability.domain.params.CourseUserParams;
import com.fykj.scaffold.capability.service.ICourseService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import javax.validation.Valid;
import java.util.List;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Validated
@RestController
@RequestMapping("/admin/manager/capability-upgrading/course")
@Api(tags = "课程管理接口-管理端")
public class CourseManageController {

    @Autowired
    private ICourseService baseService;

    @AuditLog("新增课程")
    @ApiOperation("新增课程")
    @PostMapping("/save")
    public Result saveCourse(@RequestBody @Valid CourseSaveDTO dto) {
        Course course = baseService.saveCourse(dto);
        // 异步生产qrcode
        baseService.generateCheckInQrcode(course.getId());
        return OK;
    }

    @AuditLog("编辑课程")
    @ApiOperation("编辑课程")
    @PostMapping("/edit")
    public Result editCourse(@RequestBody @Valid CourseUpdateDTO dto) {
        baseService.editCourse(dto);
        return OK;
    }

    @AuditLog("删除课程")
    @ApiOperation("删除课程")
    @DeleteMapping("/delete")
    public Result deleteCourse(@RequestParam Long ids) {
        baseService.deleteCourse(ids);
        return OK;
    }

    @ApiOperation("课程详情")
    @GetMapping("/detail")
    public JsonResult<CourseDetailDTO> detailCourse(@RequestParam Long id) {
        CourseDetailDTO detail = baseService.detailCourse(id);
        return new JsonResult<>(detail);
    }

    @ApiOperation("获取课程签到二维码")
    @GetMapping("/qrcode")
    public JsonResult<CourseQrcodeDTO> getCourseSignInQrcode(@RequestParam Long id) {
        CourseQrcodeDTO qrcode = baseService.getCourseSignInQrcode(id);
        return new JsonResult<>(qrcode);
    }

    @AuditLog("切换上下架状态")
    @ApiOperation("上/下架")
    @PostMapping("/outer-display/switch")
    public Result switchOuterDisplay(@RequestParam Long id) {
        baseService.switchOuterDisplay(id);
        return OK;
    }

    @AuditLog("切换置顶状态")
    @ApiOperation("置顶/取消置顶")
    @PostMapping("/top/switch")
    public Result switchTop(@RequestParam Long id) {
        baseService.switchTop(id);
        return OK;
    }

    @ApiOperation("课程分页查询")
    @PostMapping("/page")
    public JsonResult<IPage<CourseListDTO>> pageCourse(@RequestBody CourseParams params) {
        IPage<CourseListDTO> page = baseService.pageCourse(params);
        return new JsonResult<>(page);
    }

    @ApiOperation("课程列表查询")
    @PostMapping("/list")
    public JsonResult<List<CourseListDTO>> listCourse(@RequestBody CourseParams params) {
        List<CourseListDTO> list = baseService.listCourse(params);
        return new JsonResult<>(list);
    }

    @AuditLog("取消课程报名（指定用户）")
    @ApiOperation("取消报名（指定用户）")
    @DeleteMapping("/sign-up/cancel")
    public Result cancelSignUp(@RequestParam Long courseId, @RequestParam Long userId) {
        baseService.cancelSignUp(courseId, userId);
        return OK;
    }

    @ApiOperation("报名分页查询")
    @PostMapping("/sign-up/page")
    public JsonResult<IPage<CourseSignUpDTO>> pageSignUp(@RequestBody @Valid CourseUserParams params) {
        IPage<CourseSignUpDTO> page = baseService.pageSignUp(params);
        return new JsonResult<>(page);
    }

    @ApiOperation("签到分页查询")
    @PostMapping("/sign-in/page")
    public JsonResult<IPage<CourseSignInDTO>> pageSignIn(@RequestBody @Valid CourseUserParams params) {
        IPage<CourseSignInDTO> page = baseService.pageSignIn(params);
        return new JsonResult<>(page);
    }

    @ApiOperation("报名列表查询")
    @PostMapping("/sign-up/list")
    public JsonResult<List<CourseSignUpDTO>> listSignUp(@RequestBody @Valid CourseUserParams params) {
        List<CourseSignUpDTO> list = baseService.listSignUp(params);
        return new JsonResult<>(list);
    }

    @ApiOperation("签到列表查询")
    @PostMapping("/sign-in/list")
    public JsonResult<List<CourseSignInDTO>> listSignIn(@RequestBody @Valid CourseUserParams params) {
        List<CourseSignInDTO> list = baseService.listSignIn(params);
        return new JsonResult<>(list);
    }

    @ApiOperation("报名列表导出")
    @PostMapping("/sign-up/export")
    public void exportSignUp(@RequestBody @Valid CourseUserParams params) {
        List<CourseSignUpDTO> list = baseService.listSignUp(params);
        ExcelUtil.fillExcel(list, "course_sign_up_export.xlsx", CourseSignUpDTO.class);
    }

    @ApiOperation("签到列表导出")
    @PostMapping("/sign-in/export")
    public void exportSignIn(@RequestBody @Valid CourseUserParams params) {
        List<CourseSignInDTO> list = baseService.listSignIn(params);
        ExcelUtil.fillExcel(list, "course_sign_in_export.xlsx", CourseSignInDTO.class);
    }

    @ApiOperation("签到列表查询")
    @PostMapping("/credits/change")
    public Result creditsChange(@RequestBody @Valid CreditsChangeDTO dto) {
        baseService.creditsChange(dto);
        return OK;
    }

}
