package com.fykj.scaffold.ledger.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseTreeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 台账指标-实体类
 *
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("ledger_index")
public class LedgerIndex extends BaseTreeEntity  {

	private static final long serialVersionUID = 286939211258807045L;

	/**
	 * 名称
	 */
	@TableField("name")
    @ApiModelProperty(value = "名称")
	private String name;

	/**
	 * 排序
	 */
	@TableField("sequence")
    @ApiModelProperty(value = "排序")
	private Integer sequence;

	/**
	 * 父节点名称
	 */
	@TableField(exist = false)
	private String parentName;

}
