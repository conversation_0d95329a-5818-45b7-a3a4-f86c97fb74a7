package com.fykj.scaffold.sync.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 市平台所属领域数据字典
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-23
 */
public interface ZyzSyncBelongFieldDictMapper extends BaseMapper<ZyzSyncBelongFieldDict> {
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
}
