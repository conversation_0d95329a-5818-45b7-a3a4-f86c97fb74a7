<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzResourceAppointmentMapper">

    <select id="pageForAudit" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResourceAppointment">
        SELECT a.*,
        r.type as resType,
        r.belong_field_name_top as resFiledNameTop,
        r.belong_field_name_end as resFiledNameEnd
        FROM `zyz_resource_appointment` a
        LEFT JOIN zyz_resource r on r.id = a.resource_id and r.is_deleted = FALSE
        LEFT JOIN sys_org so ON a.org_code = so.code
        WHERE a.is_deleted = 0
        <if test="params.key != null and params.key != ''">
            AND ( a.resource_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR a.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR a.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.needAudit != null and params.needAudit == true ">
            AND a.audit_org_code = #{params.myOrgCode} AND a.audit_status = 'res_appointment_wait_audit'
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND so.code_prefix LIKE CONCAT(#{params.orgCode},'%')
        </if>
        <if test="params.type != null and params.type != ''">
            AND r.type = #{params.type}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (r.belong_field_top = #{params.fieldId} OR r.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND a.audit_status = #{params.auditStatus}
        </if>
        <if test="params.auditStartTime != null and params.auditEndTime != null">
            AND ( a.audit_start_time &gt;= #{params.auditStartTime} AND a.audit_end_time &lt;= #{params.auditEndTime} )
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( a.appointment_end_time &lt; #{params.startTime} OR a.appointment_start_time &gt; #{params.endTime}
            )
        </if>
        ORDER BY a.apply_time DESC
    </select>

    <select id="getResAppointmentWaitAuditNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_resource_appointment
        WHERE is_deleted = 0 AND audit_status = #{status} AND audit_org_code = #{myOrgCode}
    </select>

    <select id="getAppointmentById" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResourceAppointment">
        SELECT a.*,
        r.type as resType,
        r.belong_field_name_top as resFiledNameTop,
        r.belong_field_name_end as resFiledNameEnd,
        r.belong_field_top as resFiledTop,
        r.belong_field_end as resFiledEnd,
        r.start_time as resStartTime,
        r.end_time as resEndTime ,
        r.contact_phone as resContactPhone,
        r.contact_person as resContactPerson,
        r.detail as resDetail,
        t.name as activityName,
        t.start_time as activityStartTime,
        t.end_time as activityEndTime
        FROM `zyz_resource_appointment` a
        LEFT JOIN zyz_resource r on r.id = a.resource_id and r.is_deleted = FALSE
        LEFT JOIN zyz_activity t on t.res_id = a.resource_id
        WHERE a.is_deleted = FALSE
        <if test="id != null and id != ''">
            AND a.id = #{id}
        </if>
    </select>

    <select id="getNeedPracticeSheetSync" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResourceAppointment">
        SELECT *
        FROM zyz_resource_appointment zra
        LEFT JOIN zyz_resource zr ON zra.resource_id = zr.id
        WHERE zra.is_deleted = 0 AND zr.is_deleted = 0
        AND zr.is_sync = #{syncSuccessState}
        AND zra.audit_status = #{auditSuccessState}
        <if test="psIds != null and psIds.size() > 0">
            AND zra.id NOT IN #{psIds}
        </if>
    </select>

    <select id="getAppointmentResNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_resource_appointment
        WHERE is_deleted = 0 AND audit_status = #{auditPassStatus}
        <if test="teamAppointment != null">
            AND team_publish = #{teamAppointment}
            <if test="teamAppointment == true">
                AND team_id = #{teamId}
            </if>
            <if test="teamAppointment == false">
                AND org_code = #{orgCode}
            </if>
        </if>
    </select>
</mapper>
