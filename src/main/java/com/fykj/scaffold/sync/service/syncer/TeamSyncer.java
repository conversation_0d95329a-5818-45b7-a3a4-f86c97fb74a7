package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDep;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncDepService;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.conns.SyncApiCons;
import com.fykj.volunteer.sync.util.domain.sync_team.SyncTeam;
import com.fykj.volunteer.sync.util.domain.sync_team.SyncTeamResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;

/**
 * 团队同步
 */
@Component
public class TeamSyncer {

    @Autowired
    private IZyzSyncLogService logService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzSyncDepService depService;

    @Autowired
    private IZyzVolunteerTeamService volunteerTeamService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    public ZyzSyncLog sync(long teamId, long syncBillId) {
        ZyzTeam team = teamService.getById(teamId);
        //处于待同步,且审核同步才需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(team.getIsSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }
        if (!ZyzCons.TEAM_AUDIT_STATUS_PASS.equals(team.getTeamStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此团队审核未通过，无法同步");
        }

        SysOrg teamOrg = orgService.getByCode(team.getOrgCode());
        ZyzSyncDep dep = depService.getByDepId(teamOrg.getDepId());
        if (ObjectUtil.isEmpty(dep)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到该资源发布组织的dept，无法同步该需求");
        }

        SyncTeam syncTeam = new SyncTeam();
        syncTeam.setOrgName(team.getName().trim());
        syncTeam.setIdCode(team.getAdminCard());
        syncTeam.setDpId(dep.getSyncId());
        syncTeam.setRegionCode(teamOrg.getRegionCode());
        syncTeam.setDescs(team.getIntroduction());
        syncTeam.setLinkName(team.getAdminName());
        syncTeam.setLinkPhone(team.getAdminContact());
        syncTeam.setUniqueCode(String.valueOf(teamId));
        syncTeam.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(team.getTeamPhoto())));
        if (StringUtil.isEmpty(team.getSyncId())) {
            syncTeam.setSaveType(SyncTeam.SaveType.ADD);
        } else {
            syncTeam.setSaveType(SyncTeam.SaveType.MODIFY);
            syncTeam.setOrgId(team.getSyncId());
        }
        SyncTeamResult result = ApiWebService.syncTeam(syncTeam);
        String resultCode = result.getCode();
        String msg = result.getMsg();

        team.setSyncTime(LocalDateTime.now());
        team.setSyncRemark(msg);
        boolean success = "同步成功".equals(msg) || SyncApiCons.ResultCode.SUCCESS.equals(resultCode);
        boolean exist = StringUtil.isNotEmpty(msg) && (msg.contains("该组织已上传") || msg.contains("该组织已存在"));
        boolean resSync = success || exist;
        //成功
        if (resSync) {
            team.setSyncId(result.getOrgId());
            team.setIsSync(Cons.PlatformSyncState.SUCCESS);
        }
        //其他一律失败
        else {
            team.setIsSync(Cons.PlatformSyncState.FAILURE);
        }

        //启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.TEAM_SYNC, resultCode, msg
                , syncBillId, teamId, team.getName()
                , JSONUtil.toJsonStr(syncTeam), JSONUtil.toJsonStr(result));

        //回写业务
        teamService.updateById(team);
        if (resSync) {
            ZyzVolunteer volunteer = volunteerService.getByPhone(team.getAdminContact());
            if (volunteer == null) {
                return log;
            }
            ZyzVolunteerTeam volunteerTeam = volunteerTeamService.getByTeamIdAndVolunteerId(teamId, volunteer.getId());
            if (volunteerTeam != null) {
                volunteerTeam.setIsSync(Cons.PlatformSyncState.SUCCESS);
                volunteerTeam.setSyncRemark("已与团队一同同步成功");
                volunteerTeam.setSyncTime(LocalDateTime.now());
                volunteerTeamService.updateById(volunteerTeam);
            }

        }
        return log;
    }
}
