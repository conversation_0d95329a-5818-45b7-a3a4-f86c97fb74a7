package com.fykj.scaffold.mall.xxl_job;

import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * 积分兑换退还定时任务
 *
 * <AUTHOR>
 * @date 2020/5/18
 */
@Slf4j
@Component
public class MachineExchangeTask {

    @Autowired
    private IMallExchangeService exchangeService;

    /**
     * 定时1分钟去取消订单，取消5分钟前的订单
     */
    @XxlJob("Machine_exchange_Handler")
    private void configureTasks() {
        log.info("开始自动取消订单");
        exchangeService.autoCancelExpireOrder();
    }
}
