<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityTimePeriodShowMapper">
    <select id="pagesForShow" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShow">
        SELECT
        p.id AS activityTimePeriodId,
        p.start_time AS startTime,
        p.end_time as endTime,
        p.recruit_sync_id AS recruitSyncId,
        t.id AS activityId,
        t.name AS activityName,
        CASE WHEN ISNULL(s.publish_team_id) THEN t.team_id ELSE s.publish_team_id END AS publishTeamId,
        s.*,
        u.name as creatorN<PERSON>,
        CASE WHEN ISNULL(s.pics_url) THEN 'act_show_wait_upload' ELSE s.audit_status END AS auditStatus,
        CASE WHEN ISNULL(s.publish_org_code) THEN t.publish_org_code ELSE s.publish_org_code END AS publishOrgCode,
        CASE WHEN ISNULL(s.team_publish) THEN t.team_publish ELSE s.team_publish END AS teamPublish
        FROM zyz_activity_time_period p
        LEFT JOIN `zyz_activity` t ON p.activity_id=t.id
        LEFT JOIN sys_org o on t.publish_org_code = o.`code`
        LEFT JOIN sys_org sp on t.audit_org_code=sp.code
        LEFT JOIN zyz_activity_time_period_show s on s.activity_time_period_id=p.id AND s.is_deleted=0
        LEFT JOIN sys_user u on u.id=s.creator
        WHERE t.is_deleted = FALSE AND t.audit_status='act_audit_success' AND DATE_FORMAT(p.end_time,'%Y-%m-%d') <![CDATA[ <= ]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.address
            LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.publish_org_name LIKE CONCAT('%', CONCAT(#{params.key},
            '%')))
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and t.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.myOrgCode!='top_dept' and params.myOrgCode != null and params.myOrgCode != ''">
            AND t.publish_org_code LIKE CONCAT(#{params.myOrgCode},'%')
        </if>
        <if test="params.myOrgCode != null and params.myOrgCode != '' ">
            AND ( ( t.publish_org_code !=#{params.myOrgCode} AND s.audit_status = 'act_show_audit_success') or(t.publish_org_code =#{params.myOrgCode}))
        </if>
        <if test="params.myTeamId == null">
            AND (t.team_publish= false or (t.team_publish= true AND s.audit_status = 'act_show_audit_success'))
        </if>
        <if test="params.myTeamId != null">
            AND t.team_id = #{params.myTeamId}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != '' and params.auditStatus!= 'act_show_wait_upload'">
            AND s.audit_status = #{params.auditStatus}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != '' and params.auditStatus == 'act_show_wait_upload'">
            AND s.audit_status is null
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT (s.end_time &lt; #{params.startTime} OR s.start_time &gt; #{params.endTime} )
        </if>
        ORDER BY t.start_time DESC
    </select>
    <select id="pageForAudit" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShow">
        SELECT
            s.*,
            u.name as creatorName
        FROM zyz_activity_time_period_show s
        LEFT JOIN `zyz_activity` t ON s.activity_id=t.id
        LEFT JOIN sys_user u on u.id=s.creator
        WHERE s.is_deleted = 0
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.address
            LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.publish_org_name LIKE CONCAT('%', CONCAT(#{params.key},
            '%')))
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND s.audit_status = #{params.auditStatus}
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND t.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( s.end_time &lt; #{params.startTime} OR s.start_time &gt; #{params.endTime} )
        </if>
        <if test="params.needAudit != null and params.needAudit == true ">
            AND s.audit_org_code = #{params.myOrgCode}
            AND s.audit_status = 'act_show_wait_audit'
        </if>
        ORDER BY s.create_date DESC
    </select>
    <select id="getNeedSyncRecords" resultType="java.lang.Long">
        SELECT tp.id
        FROM zyz_activity_time_period_show tp
        LEFT JOIN (SELECT id FROM zyz_activity WHERE is_deleted = 0 AND audit_status = 'act_audit_success') a ON tp.activity_id = a.id
        WHERE tp.is_deleted = 0   AND tp.audit_status = 'act_show_audit_success' AND tp.recruit_sync_id is not null AND tp.recruit_sync_id!=''
        AND tp.end_time &lt;= now()
        AND tp.end_time >= date_sub(now(),interval 15 day)
        AND (
            tp.sync = 'sync_wait'
            OR (
            tp.sync = 'sync_failure' AND tp.end_time >= date_sub(now(),interval 3 day)
            )
        )
        AND a.id IS NOT NULL
    </select>
    <select id="getTeamActInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.OrgTeamActivityShowSumReportDto">
        SELECT t.id AS teamId,
        t.name AS teamName,
        IFNULL(count(b.id),0) AS actNum,
        SUM(IF( b.audit_status!='act_show_draft' AND  b.audit_status!=null ,1,0)) AS actShowUploadNum,
        SUM(IF( b.audit_status='act_show_audit_success' ,1,0)) AS actShowUploadPassNum,
        SUM(IF( b.audit_status='act_show_wait_audit' ,1,0)) AS actShowUploadAuditProcessingNum,
        SUM(IF( b.audit_status='act_show_reject' ,1,0)) AS actShowUploadAuditRejectNum
        FROM zyz_team t LEFT JOIN  (
                SELECT p.*, a.team_id, s.audit_status FROM zyz_activity_time_period p
                INNER JOIN zyz_activity_apply ap ON p.id=ap.time_period_id and ap.is_deleted=0 and ap.audit_status = 'act_apply_audit_success' AND ap.sign_time IS NOT NULL
                INNER JOIN zyz_activity a ON ap.activity_id= a.id AND a.team_publish=1
                LEFT JOIN zyz_activity_time_period_show s ON p.id=s.activity_time_period_id
                WHERE p.is_deleted=0
                <if test="params.start != null">
                    AND ap.time_period_start_time &gt;= #{params.start}
                </if>
                <if test="params.end != null">
                    AND ap.time_period_start_time &lt;= #{params.end}
                </if>
        )b ON t.id=b.team_id
        WHERE  t.is_deleted=0 and t.team_status='team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.parentOrgCode != 'top_dept'">
                <if test=" params.all != null and  params.all==true">
                    AND t.org_code LIKE CONCAT('%', CONCAT(#{params.parentOrgCode}, '%'))
                </if>
                <if test=" params.all == null or  params.all==false">
                    AND t.org_code = #{params.parentOrgCode}
                </if>
            </if>
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
        </if>
        GROUP BY t.id
        ORDER BY actNum DESC
    </select>

    <select id="getOrgActInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.OrgTeamActivityShowSumReportDto">
        SELECT o.`code` AS orgCode,
        o.`name` AS orgName,
        o.level,
        o.sequence,
        IFNULL(count(b.id),0) AS actNum,
        SUM(IF( b.audit_status!='act_show_draft' AND b.audit_status is not null ,1,0)) AS actShowUploadNum,
        SUM(IF( b.audit_status='act_show_audit_success' ,1,0)) AS actShowUploadPassNum,
        SUM(IF( b.audit_status='act_show_wait_audit' ,1,0)) AS actShowUploadAuditProcessingNum,
        SUM(IF( b.audit_status='act_show_reject' ,1,0)) AS actShowUploadAuditRejectNum
        from sys_org o LEFT JOIN  (
            SELECT p.*, ap.org_code, s.audit_status FROM zyz_activity_time_period p
                INNER JOIN zyz_activity_apply ap ON p.id=ap.time_period_id and ap.is_deleted=0 and ap.audit_status = 'act_apply_audit_success' AND ap.sign_time IS NOT NULL AND ap.team_publish = 0
                LEFT JOIN zyz_activity_time_period_show s ON p.id=s.activity_time_period_id
            WHERE p.is_deleted=0
            <if test="params.start != null">
                AND ap.time_period_start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND ap.time_period_start_time &lt;= #{params.end}
            </if>
        )b ON  o.`code`=b.org_code
        WHERE  o.is_deleted=0
            <if test="params.parentOrgCode != 'top_dept'">
                    AND o.code LIKE CONCAT('%', CONCAT(#{params.parentOrgCode}, '%'))
            </if>
        GROUP BY o.`code`,o.`name`, o.level,o.sequence
        ORDER BY actNum DESC, o.level,o.sequence
    </select>

    <!-- 根据活动ID查询公示信息列表 -->
    <select id="pageForApi" resultType="com.fykj.scaffold.zyz.domain.dto.ActivityTimePeriodShowDto">
        SELECT
            id as id,
            content as content,
            pics_url as picsUrl
        FROM
            zyz_activity_time_period_show
        <where>
            AND is_deleted = 0
            AND auto_status = 1
            AND activity_id = #{params.activityId}
        </where>
        ORDER BY
            start_time DESC
    </select>
</mapper>
