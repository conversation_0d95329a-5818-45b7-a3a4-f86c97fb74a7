package com.fykj.scaffold.race.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 摄影大赛-活动表
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ActivityParams extends BaseParams {

    @ApiModelProperty("标题，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "name")
    private String name;
}
