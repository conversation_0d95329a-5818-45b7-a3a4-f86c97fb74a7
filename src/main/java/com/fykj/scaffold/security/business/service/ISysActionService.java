package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.entity.SysAction;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 系统资源-功能操作
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
public interface ISysActionService extends IBaseService<SysAction> {

    /**
     * 检查code是否存在
     *
     * @param code
     * @param id
     * @return
     */
    boolean isExist(String code, Long id);


    /**
     * 查询用户拥有的所有菜单
     *
     * @param userId
     * @return
     */
    List<SysAction> findActionByUserIdAndRoleIds(long userId, List<Long> roleIds);
}

