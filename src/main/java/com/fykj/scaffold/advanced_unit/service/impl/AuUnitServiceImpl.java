package com.fykj.scaffold.advanced_unit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.advanced_unit.cons.*;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitImportDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.UnitAuditDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.UnitSaveDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuUnit;
import com.fykj.scaffold.advanced_unit.domain.params.AuUnitParams;
import com.fykj.scaffold.advanced_unit.domain.params.UnitActivityParams;
import com.fykj.scaffold.advanced_unit.mapper.AuUnitMapper;
import com.fykj.scaffold.advanced_unit.service.IAuUnitService;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.domain.params.NewsParams;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.IUserRoleService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.error.exception.FieldBindException;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.dto.ActivityApiDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityParams;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 文明单位-申请表
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AuUnitServiceImpl
        extends BaseServiceImpl<AuUnitMapper, AuUnit>
        implements IAuUnitService
{

    @Autowired
    private INewsService newsService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Override
    public void submitUnit(UnitSaveDTO dto, boolean manager) {
        // 校验统一社会信用代码是否存在
        checkCreditCode(dto.getCreditCode());
        AuUnit unit = new AuUnit();
        BeanUtil.copyProperties(dto, unit, CopyOptions.create());
        initUnitSave(manager, unit);
        // 管理端新增验证联系人手机号是志愿者, 且是所选团队管理员
        if(manager) {
            Long chargeUserId = validateTelAndGetUnitUserId(dto.getRelatedTeams(), dto.getContactPhone());
            addAuRole2User(chargeUserId);
            // 设置单位所属人
            unit.setBelongUserId(chargeUserId);
        }
        save(unit);
    }

    @Override
    public void editUnit(AuUnit unit) {
        // 校验统一社会信用代码是否存在
        checkCreditCode(unit);
        // 管理端新增验证联系人手机号是志愿者, 且是所选团队管理员
        Long chargeUserId = validateTelAndGetUnitUserId(unit.getRelatedTeams(), unit.getContactPhone());
        addAuRole2User(chargeUserId);
        // 设置单位所属人
        unit.setBelongUserId(chargeUserId);
        updateById(unit);
    }

    /**
     * 初始化单元保存状态
     *
     * @param manager 是否为管理员
     * @param unit    待初始化的单元对象
     */
    private void initUnitSave(boolean manager, AuUnit unit) {
        unit.setAuditStatus(manager
                ? AdvancedUnitAuditStatus.PASS.getCode()
                : AdvancedUnitAuditStatus.PENDING.getCode());
        unit.setShowStatus(false);
    }

    @Override
    public void audit(UnitAuditDTO dto) {
        // 检验操作是否合法
        AdvancedUnitAuditOperation operation = AdvancedUnitAuditOperation.getByCode(dto.getAuditOperation());
        dto.getIds().forEach(id -> {
            // 从数据库中获取单位信息, 同时校验单位是否存在
            AuUnit unit = getById(id);
            // 检查状态
            checkAuditStatus(unit);
            // 校验统一社会信用代码是否存在
            checkCreditCode(unit.getCreditCode());
            processAudit(unit, operation.getStatusCode());
            // 审核通过给对提交用户添加文明单位默认角色
            if(AdvancedUnitAuditStatus.PASS.getCode().equals(unit.getAuditStatus()) && Objects.nonNull(unit.getCreator())) {
                addAuRole2User(unit.getCreator());
                // 设置单位所属人
                unit.setBelongUserId(unit.getCreator());
            }
            updateById(unit);
            // 发送审核通知短信
            try {
                sendTmpMsg.sendToPointUser(
                        operation.getSmsTempCode(),
                        Collections.singletonList(unit.getCreator()),
                        unit.getUnitName());
            } catch (Exception e) {
                log.error("发送短信失败, 短信模板" + operation.getSmsTempCode() + "用户ID:"+ unit.getCreator(), e);
            }
        });
    }

    /**
     * 为用户添加文明单位默认角色
     *
     * @param userId 用户ID
     */
    private void addAuRole2User(Long userId) {
        // 审核通过给对那个用户添加文明单位默认角色
        userRoleService.addRole(userId, AdvancedUnitCons.ADVANCE_UNIT_DEFAULT_ROLE_CODE);
    }

    /**
     * 检查审核状态
     *
     * @param unit 待检查的单元
     * @throws BusinessException 如果单元已审核，则抛出业务异常
     */
    private void checkAuditStatus(AuUnit unit) {
        if(!AdvancedUnitAuditStatus.PENDING.getCode().equals(unit.getAuditStatus())) {
            throw new BusinessException(ResultCode.NOT_FOUND, "该内容已审核: " + unit.getUnitName());
        }
    }

    /**
     * 处理审核逻辑
     *
     * @param unit 需要处理的审核单元
     * @param operationCode 操作代码，表示审核的状态或结果
     */
    private void processAudit(AuUnit unit, String operationCode) {
        unit.setAuditStatus(operationCode);
        unit.setAuditUser((Long) Oauth2Util.getUserId());
        unit.setAuditAt(LocalDateTime.now());
    }

    @Override
    public IPage<AuUnit> queryPage(AuUnitParams params) {
        IPage<AuUnit> page = page(params);
        DictTransUtil.trans(page.getRecords());
        return page;
    }

    @Override
    public List<AuUnit> queryList(AuUnitParams params) {
        return DictTransUtil.trans(list(params));
    }

    @Override
    public AuUnit queryDetail(Long id) {
        return DictTransUtil.trans(getById(id));
    }

    /**
     * 更新显示状态
     *
     * @param ids 要更新的记录ID
     */
    @Override
    public void updateShowStatus(List<Long> ids) {
        ids.forEach(this::updateShowStatus);
    }

    @Override
    public void updateShowStatus(Long id) {
        AuUnit unit = getById(id);
        unit.setShowStatus(Boolean.FALSE.equals(unit.getShowStatus()));
        updateById(unit);
    }

    /**
     * 校验统一社会信用代码是否存在
     */
    private void checkCreditCode(String creditCode) {
        if (isCreditCodePass(creditCode)) {
            throw new FieldBindException(AuUnit.Fields.creditCode, "该统一社会信用代码的所属企业已经通过申报");
        }
    }

    /**
     * 更新时判断统一社会信用代码重复
     * @param unit 单位
     */
    private void checkCreditCode(AuUnit unit) {
        AuUnit unit1 = getPassedByCreditCode(unit.getCreditCode());
        if (Objects.nonNull(unit1)
                && !Objects.equals(unit1.getId(), unit.getId())) {
            throw new BusinessException(ResultCode.NOT_VALID, "该统一社会信用代码的所属企业已经通过申报");
        }
    }

    /**
     * 校验统一社会信用代码是否存在
     */
    private boolean isCreditCodePass(String creditCode) {
        return lambdaQuery()
                .eq(AuUnit::getCreditCode, creditCode)
                .eq(AuUnit::getAuditStatus, AdvancedUnitAuditStatus.PASS.getCode())
                .count() > 0;
    }

    @Override
    public AuUnit getPassedByCreditCode(String creditCode) {
        return lambdaQuery()
                .eq(AuUnit::getCreditCode, creditCode)
                .eq(AuUnit::getAuditStatus, AdvancedUnitAuditStatus.PASS.getCode())
                .one();
    }

    @Override
    public List<AuUnitImportDTO> dataImport(MultipartFile excel) {
        // 验证excel
        validateExcel(excel);
        List<AuUnitImportDTO> dataList = readUnitListFromExcel(excel);
        List<AuUnitImportDTO> failList = validateAndFormatDict(dataList);
        // 验证数据合理性
        if (failList.isEmpty()) {
            // 初始化新增数据
            List<AuUnit> unitList = dataList.stream().map(data -> {
                AuUnit unit = new AuUnit();
                BeanUtil.copyProperties(data, unit, CopyOptions.create());
                initUnitSave(true, unit);
                return unit;
            }).collect(Collectors.toList());
            saveBatch(unitList);
        }
        return failList;
    }

    /**
     * 验证Excel文件
     *
     * @param excel 需要验证的Excel文件
     * @throws BusinessException 如果Excel文件为空或未上传，则抛出BusinessException异常
     */
    private void validateExcel(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "上传文件为空");
        }
    }

    /**
     * 验证并格式化单位列表
     *
     * @param unitList 单位列表
     * @return 验证失败的单位列表
     */
    private List<AuUnitImportDTO> validateAndFormatDict(List<AuUnitImportDTO> unitList) {
        List<AuUnitImportDTO> failList = new ArrayList<>();

        // 首先检查是否有重复的creditCode
        Map<String, List<AuUnitImportDTO>> creditCodeMap = new HashMap<>();
        for (AuUnitImportDTO unit : unitList) {
            creditCodeMap.computeIfAbsent(unit.getCreditCode(),
                    k -> new ArrayList<>()).add(unit);
        }

        // 检查并标记重复的creditCode
        boolean hasDuplicates = false;
        for (Map.Entry<String, List<AuUnitImportDTO>> entry : creditCodeMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                hasDuplicates = true;
                for (AuUnitImportDTO unit : entry.getValue()) {
                    unit.setReason("统一社会信用代码重复");
                    failList.add(unit);
                }
            }
        }

        // 如果存在重复creditCode，直接返回failList，不进行其他校验
        if (hasDuplicates) {
            return failList;
        }

        // 没有重复creditCode，继续进行其他校验
        Map<String, String> unitNatureMap = getDictLabel2code(AdvancedUnitNature.DICT_CODE);
        Map<String, String> honorLevelMap = getDictLabel2code(AdvancedUnitHonorLevel.DICT_CODE);
        Map<String, String> unitTypeMap = getDictLabel2code(AdvancedUnitType.DICT_CODE);

        unitList.forEach(unit -> {
            if (StrUtil.isBlank(unit.getCreditCode()) || StrUtil.isBlank(unit.getUnitName())
                    || StrUtil.isBlank(unit.getAddress()) || StrUtil.isBlank(unit.getUnitTypeName())
                    || StrUtil.isBlank(unit.getHonorLevelName()) || StrUtil.isBlank(unit.getUnitNatureName())
                    || StrUtil.isBlank(unit.getIntroduction())) {
                unit.setReason("必填项不能为空");
                failList.add(unit);
                return;
            }

            if (isCreditCodePass(unit.getCreditCode())) {
                unit.setReason("统一社会信用代码已存在");
                failList.add(unit);
                return;
            }

            String unitNature = unitNatureMap.get(unit.getUnitNatureName());
            if(StrUtil.isBlank(unitNature)){
                unit.setReason("单位性质值不正确");
                failList.add(unit);
                return;
            }
            unit.setUnitNature(unitNature);

            String honorLevel = honorLevelMap.get(unit.getHonorLevelName());
            if(StrUtil.isBlank(honorLevel)){
                unit.setReason("荣誉等级值不正确");
                failList.add(unit);
                return;
            }
            unit.setHonorLevel(honorLevel);

            String unitType = unitTypeMap.get(unit.getUnitTypeName());
            if(StrUtil.isBlank(unitType)){
                unit.setReason("单位类型值不正确");
                failList.add(unit);
                return;
            }
            unit.setUnitType(unitType);
        });

        return failList;
    }

    /**
     * 根据字典编码获取字典标签与编码的映射关系
     *
     * @param dictCode 字典编码
     * @return 包含字典标签和编码的映射关系的Map
     */
    private Map<String, String> getDictLabel2code(String dictCode) {
        return dictService.findByParentCode(dictCode)
                .stream()
                .collect(Collectors.toMap(
                        Dict::getName,
                        Dict::getCode,
                        (a, b) -> b));
    }

    @Override
    public IPage<ActivityApiDto> queryActivityPage(UnitActivityParams params) {
        Long orgId = params.getId();
        AuUnit unit = getById(orgId);
        String relatedTeams = unit.getRelatedTeams();
        if (StrUtil.isBlank(relatedTeams)) {
            return new Page<>();
        }
        ZyzActivityParams activityParams = new ZyzActivityParams();
        activityParams.setPageSize(params.getPageSize());
        activityParams.setCurrentPage(params.getCurrentPage());
        activityParams.setTeamId(relatedTeams);
        return activityService.pageForFrontend(activityParams);
    }

    @Override
    public IPage<News> queryNewsPage(UnitActivityParams params) {
        AuUnit unit = getById(params.getId());
        String relatedTeam = unit.getRelatedTeams();
        if (StrUtil.isBlank(relatedTeam)) {
            return new Page<>();
        }
        return newsService.pageForApi(new NewsParams(params).setTeamId(relatedTeam));
    }

    /**
     * 从Excel文件中读取单位列表
     *
     * @param excel 包含单位数据的Excel文件
     * @return 包含单位数据的DTO列表
     * @throws BusinessException 如果导入的数据为空，抛出业务异常
     */
    private List<AuUnitImportDTO> readUnitListFromExcel(MultipartFile excel) {
        List<AuUnitImportDTO> dataList = ExcelUtil.readExcel(
                excel, AuUnitImportDTO.class, 0, 2);
        if ("示例数据".equals(dataList.get(0).getUnitName())) {
            dataList.remove(0);
        }
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        return dataList;
    }

    /**
     * 验证联系方式是否为志愿者且为选择团队的团队管理员，并返回用户ID
     *
     * @param teamId 团队ID
     * @param tel    联系方式
     * @return 用户ID
     * @throws BusinessException 当联系方式不符合要求时抛出异常
     */
    private Long validateTelAndGetUnitUserId(String teamId, String tel) {
        // 验证联系方式是否是志愿者, 且为选择团队的团队管理员
        ZyzVolunteer volunteer = volunteerService.getByPhone(tel);
        if(Objects.isNull(volunteer)) {
            throw new BusinessException(ResultCode.FAIL, "该手机号必须是志愿者身份");
        }
//        ZyzTeam team = teamService.getById(teamId);
//        if(!Objects.equals(volunteer.getCertificateId(), team.getCuratorCard())) {
//            throw new BusinessException(ResultCode.FAIL, "该手机号必须是选择团队的团队负责人");
//        }
        // 验证志愿者是否为团队管理员
        return userService.lambdaQuery()
                .select(User::getId)
                .eq(User::getMobile, tel)
                .oneOpt()
                .map(User::getId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "该手机号必须是平台注册用户"));
    }

    @Override
    public List<AuUnit> homeBannerShow(AuUnitParams params) {
        List<AuUnit> result = list(params);
        if (result != null && !result.isEmpty()) {
            DictTransUtil.trans(result);
        }
        return result;
    }

    @Override
    public Integer getAdvancedUnitWaitAuditNum() {
        // 查询文明单位注册待审核数量
        return lambdaQuery()
                .eq(AuUnit::getAuditStatus, AdvancedUnitAuditStatus.PENDING.getCode()) // 待审核状态
                .count()
                .intValue();
    }
}
