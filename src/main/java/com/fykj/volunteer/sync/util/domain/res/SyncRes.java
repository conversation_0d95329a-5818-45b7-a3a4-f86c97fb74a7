package com.fykj.volunteer.sync.util.domain.res;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步资源
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncRes {
    /**
     * 唯一号
     */
    private String uniCode;
    /**
     * 资源发布方类型1:实践阵地（实践中心、所、站）
     * 2:职能部门
     * 3:实践点
     * 4:组织团队
     */
    private String pubType;
    /**
     * 资源发布方编号
     */
    private String pubId;
    /**
     * 资源名称
     */
    private String resName;
    /**
     * 资源类型多个以英文逗号“,”分隔，取值范围：
     * 1：项目/服务
     * 2：团队/专家
     * 3：阵地/场地
     */
    private String resType;
    /**
     * 资源提供日期 资源日期内容，例如：一年，九个月
     */
    private String resDate;
    /**
     * 资源详情
     */
    private String content;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系电话
     */
    private String contactNo;
    /**
     * 资源logo图片图片外网url地址转换为base64编码（必须带http或者https全路径）
     */
    private String picLogoUrl;
    /**
     * 详情地址
     */
    private String address;
    /**
     * 腾讯坐标-经度
     */
    private String txMapLan;
    /**
     * 腾讯坐标-纬度
     */
    private String txMapLat;
    /**
     * 接口调用类型 1新增，2修改
     */
    private String saveType;
    /**
     * 资源编号
     */
    private String resId;

    /**
     * 保存类型
     */
    public static class SaveType {
        /**
         * 新增
         */
        public final static String ADD = "1";

        /**
         * 修改
         */
        public final static String MODIFY = "2";
    }

    /**
     * 需求类型
     */
    public static class ResType {
        /**
         * 1：项目/服务
         */
        public final static String PROJECT_SERVICE = "1";
        /**
         * 2：团队/专家
         */
        public final static String TEAM_PRO = "2";
        /**
         * 3：阵地/场地
         */
        public final static String AREA = "3";
    }

    /**
     * 需求发布方
     */
    public static class PubType {
        /**
         * 1:实践阵地（实践中心、所、站）
         */
        public final static String BASE = "1";
        /**
         * 2:职能部门
         */
        public final static String DEP = "2";
        /**
         * 3:实践点
         */
        public final static String SPOT = "3";
        /**
         * 4:组织团队
         */
        public final static String TEAM = "4";
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "res")
    public static class Res {
        private List<SyncRes> info;
    }

    public String buildXml() {
        SyncRes.Res vol = new SyncRes.Res();
        vol.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(vol);
    }
}
