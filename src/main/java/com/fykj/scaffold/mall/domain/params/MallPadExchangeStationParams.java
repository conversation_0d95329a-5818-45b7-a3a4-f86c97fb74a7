package com.fykj.scaffold.mall.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * 查询参数
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("pad兑换站点查询参数")
public class MallPadExchangeStationParams extends BaseParams {

    private static final long serialVersionUID = -6630826460262903321L;

    @MatchType(value = QueryType.LIKE, fieldName = {"code", "name"})
    private String keyword;

    @MatchType(value = QueryType.LIKE, fieldName = "link_phone")
    private String linkPhone;

    @MatchType(value = QueryType.EQ, fieldName = "status")
    private Integer status;

    @MatchType(value = QueryType.EQ, fieldName = "org_code")
    private String orgCode;

    @ApiModelProperty(value = "经度/必传")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度/必传")
    private BigDecimal latitude;
}
