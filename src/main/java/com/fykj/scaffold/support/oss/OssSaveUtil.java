package com.fykj.scaffold.support.oss;

import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.support.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpStatus;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;

import java.io.IOException;
import java.io.InputStream;

/**
 * 对象存储工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class  OssSaveUtil {

    private static IOssSaver getInstance(String type) {
        return SpringContextUtil.getBean(OssCons.OSS_IMPL_BEAN_PREFIX + type);
    }


    /**
     * 对象存储保存
     *
     * @param file   输入流
     * @param type 保存位置（七牛云，#阿里云，#腾讯云，本地）#暂时不可用
     * @return 资源访问路径
     */
    public static FilePathDto save(MultipartFile file, String type) {
        IOssSaver saver = getInstance(type);
        return saver.save(file);
    }

    /**
     * 对象存储保存
     *
     * @param is   输入流
     * @param type 保存位置（七牛云，#阿里云，#腾讯云，本地）#暂时不可用
     * @return 资源访问路径
     */
    public static FilePathDto save(InputStream is, String type, String fileName) {
        IOssSaver saver = getInstance(type);
        return saver.save(is, fileName);
    }

    /**
     * 对象存储保存
     *
     * @param url 网络路径
     * @param type 保存位置（七牛云，#阿里云，#腾讯云，本地）#暂时不可用
     * @return 资源访问路径
     */
    public static FilePathDto save(String url, String type, String fileName) {
        return save(getInputStreamFromUrl(url), type, fileName);
    }

    /**
     * 获取文件后缀
     *
     * @param fileName 文件名
     * @return 文件后缀，不带点
     */
    public static String getExt(String fileName) {
        if (StringUtil.isEmpty(fileName)) {
            return Mark.EMPTY;
        }
        int index = fileName.lastIndexOf(Mark.DOT);
        if (index == -1) {
            return Mark.EMPTY;
        }
        return fileName.substring(index + 1);
    }

    private static InputStream getInputStreamFromUrl(String url) {
        try {
            Request request = new Request.Builder()
                    .header("Referer", url)
                    .get().url(url).build();
            Response response = new OkHttpClient().newCall(request).execute();
            if (response.code() != HttpStatus.SC_OK || response.body() == null) {
                throw new BusinessException(ResultCode.FAIL, "读取网络文件失败：" + url);
            }
            return response.body().byteStream();
        } catch (IOException e) {
            log.error(url);
            throw new BusinessException(ResultCode.FAIL, "读取网络文件失败:", e);
        }
    }
}
