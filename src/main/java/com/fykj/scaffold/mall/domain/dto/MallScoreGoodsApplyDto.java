package com.fykj.scaffold.mall.domain.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel("报名兑换信息")
@NoArgsConstructor
@AllArgsConstructor
public class MallScoreGoodsApplyDto {

    /**
     * 报名id
     */

    @ApiModelProperty(value = "报名id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 兑换id
     */
    @ApiModelProperty(value = "兑换id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    /**
     * 志愿者id
     */

    @ApiModelProperty(value = "志愿者id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;
    /**
     * 志愿者姓名
     */

    @ApiModelProperty(value = "志愿者姓名")
    private String volunteerName;
    /**
     * 志愿者联系方式
     */

    @ApiModelProperty(value = "志愿者联系方式")
    private String volunteerPhone;
    /**
     * 商品id
     */

    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long goodsId;
    /**
     * 商品名称
     */

    @ApiModelProperty(value = "商品名称")
    private String goodsName;


    /**
     * 监护人姓名
     */

    @ApiModelProperty(value = "监护人姓名")
    private String guardianName;
    /**
     * 监护人联系方式
     */

    @ApiModelProperty(value = "监护人联系方式")
    private String guardianPhone;
    /**
     * 监护人证件类型
     */

    @ApiModelProperty(value = "监护人证件类型")
    @DictTrans(transTo = "guardianCertificateTypeText")
    private String guardianCertificateType;

    /**
     * 监护人证件类型
     */

    @ApiModelProperty(value = "监护人证件类型")
    private String guardianCertificateTypeText;
    /**
     * 监护人证件号
     */

    @ApiModelProperty(value = "监护人证件号")
    private String guardianCertificateId;
    /**
     * 监护人工作单位
     */

    @ApiModelProperty(value = "监护人工作单位")
    private String guardianWorkUnit;
    /**
     * 参加人姓名
     */

    @ApiModelProperty(value = "参加人姓名")
    private String participantName;
    /**
     * 参加人联系方式
     */

    @ApiModelProperty(value = "参加人联系方式")
    private String participantPhone;
    /**
     * 参加人证件类型
     */

    @ApiModelProperty(value = "参加人证件类型")
    @DictTrans(transTo = "participantCertificateTypeText")
    private String participantCertificateType;

    /**
     * 参加人证件类型
     */

    @ApiModelProperty(value = "参加人证件类型")
    private String participantCertificateTypeText;
    /**
     * 参加人证件号
     */

    @ApiModelProperty(value = "参加人证件号")
    private String participantCertificateId;
    /**
     * 参加人学校
     */

    @ApiModelProperty(value = "参加人学校")
    private String participantSchool;
    /**
     * 参加人年龄
     */

    @ApiModelProperty(value = "参加人年龄")
    private Integer participantAge;
    /**
     * 参加人性别
     */

    @ApiModelProperty(value = "参加人性别")
    @DictTrans(transTo = "participantSexText")
    private String participantSex;

    /**
     * 参加人性别
     */

    @ApiModelProperty(value = "参加人性别")
    private String participantSexText;

    /**
     * 兑换时间
     */

    @ApiModelProperty(value = "兑换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exchangeTime;

    /**
     * 兑换积分（锁定积分）
     */

    @ApiModelProperty(value = "兑换积分（锁定积分）")
    private BigDecimal exchangePoint;

    /**
     * 兑换之前的积分
     */

    @ApiModelProperty(value = "兑换之前的积分")
    private BigDecimal exchangeBeforeScore;

    /**
     * 兑换之后的积分
     */

    @ApiModelProperty(value = "兑换之后的积分")
    private BigDecimal exchangeAfterScore;

    /**
     * 服务时长
     */
    @ApiModelProperty(value = "服务时长")
    private BigDecimal serviceLong;
}
