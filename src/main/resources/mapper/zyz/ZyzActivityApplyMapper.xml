<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityApplyMapper">
    <select id="pageOrListForAudit" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply">
        SELECT
        zry.*,
        zry.is_exchange AS exchange,
        zry.is_team_member AS teamMember,
        zry.is_user_hide AS userHide,
        v.certificate_id,
        v.sex
        FROM zyz_activity_apply AS zry
        JOIN zyz_volunteer AS v on v.id=zry.volunteer_id
        left join sys_org sp on sp.code=zry.org_code
        WHERE zry.is_deleted = 0
        <if test="params.key != null and params.key != ''">
            AND ( zry.activity_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zry.volunteer_name LIKE CONCAT('%',
            CONCAT(#{params.key}, '%'))
            OR zry.volunteer_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.myOrgCode != null and params.myOrgCode != ''">
            and zry.org_code =#{params.myOrgCode}
            and zry.team_publish = 0
        </if>
        <if test="params.myTeamId != null">
            AND zry.team_id = #{params.myTeamId}
            and zry.team_publish = 1
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND zry.audit_status = #{params.auditStatus}
        </if>
        <if test="params.activityId != null and params.activityId != ''">
            AND zry.activity_id = #{params.activityId}
        </if>
        <if test="params.timePeriodId != null and params.timePeriodId != ''">
            AND zry.time_period_id = #{params.timePeriodId}
        </if>
        <if test="params.teamMember != null ">
            AND zry.is_team_member = #{params.teamMember}
        </if>
        <if test="params.applyStartTime != null and params.applyEndTime != null">
            AND ( zry.apply_time &gt;= #{params.applyStartTime} AND zry.apply_time &lt;= #{params.applyEndTime} )
        </if>
        ORDER BY zry.apply_time DESC
    </select>

    <select id="getActApplyWaitAuditNum" resultType="java.lang.Integer">
        SELECT COUNT(aa.id)
        FROM zyz_activity_apply AS aa
        LEFT JOIN zyz_activity a ON a.id = aa.activity_id
        LEFT JOIN sys_org so on so.code = a.publish_org_code
        WHERE a.is_deleted = 0 AND aa.is_deleted = 0 AND aa.audit_status = #{status}
        <if test="myOrgCode != null and myOrgCode != ''">
            and aa.org_code =#{myOrgCode}
            and aa.team_publish = 0
        </if>
        <if test="myTeamId != null">
            AND aa.team_id = #{myTeamId}
            and aa.team_publish = 1
        </if>
    </select>

    <select id="pageForAppraise" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply">
        SELECT
        zry.*,
        zry.is_exchange AS exchange,
        zry.is_team_member AS teamMember,
        zry.is_user_hide AS userHide
        FROM zyz_activity_apply AS zry

        left join sys_org sp on sp.code=zry.org_code
        WHERE zry.is_deleted = 0 and zry.appraise_time is not null
        <if test="params.key != null and params.key != ''">
            AND ( zry.activity_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zry.volunteer_name LIKE CONCAT('%',
            CONCAT(#{params.key}, '%'))
            OR zry.volunteer_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR zry.appraise_content LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.appraiseStatus != null ">
            AND zry.appraise_status = #{params.appraiseStatus}
        </if>
        <if test="params.teamMember != null ">
            AND zry.is_team_member = #{params.teamMember}
        </if>
        ORDER BY zry.appraise_time DESC
    </select>


    <select id="pageOrListForServiceLong" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply">
        SELECT
        zry.*,
        zry.is_exchange AS exchange,
        zry.is_team_member AS teamMember,
        zry.is_user_hide AS userHide
        FROM zyz_activity_apply AS zry
        left join sys_org sp on sp.code=zry.org_code
        WHERE zry.is_deleted = 0 and zry.audit_status= 'act_apply_audit_success'
        <if test="params.key != null and params.key != ''">
            AND ( zry.activity_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zry.volunteer_name LIKE CONCAT('%',
            CONCAT(#{params.key}, '%'))
            OR zry.volunteer_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( zry.time_period_start_time &lt; #{params.startTime} OR zry.time_period_end_time &gt;
            #{params.endTime} )
        </if>
        ORDER BY zry.sign_time DESC
    </select>
    <select id="getApplyByActivityId" resultType="com.fykj.scaffold.zyz.domain.dto.ZyzActivityApplyLongDto">
        SELECT
        zv.name as volunteerName,
        zv.phone as volunteerPhone,
        aa.apply_time as applyTime,
        aa.is_team_member as teamMember,
        aa.audit_status as auditStatus,
        -- CASE WHEN aa.sign_time IS NULL THEN aa.time_period_start_time ELSE aa.sign_time END as serviceStartTime,
        aa.time_period_start_time as serviceStartTime,
        aa.time_period_end_time as serviceEndTime,
        aa.sign_time as signTime,
        aa.service_long as serviceLong
        FROM `zyz_activity_apply` aa
        LEFT JOIN zyz_volunteer zv on aa.volunteer_id = zv.id
        left join zyz_activity za on aa.activity_id = za.id
        WHERE aa.is_deleted = false
        <if test="activityId != null">
            AND aa.activity_id = #{activityId}
        </if>
        ORDER BY aa.apply_time DESC
    </select>
    <select id="pageForVolunteerServiceLong" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply">
        SELECT
        aa.*,
        aa.is_exchange AS exchange,
        aa.is_team_member AS teamMember,
        aa.is_user_hide AS userHide
        FROM `zyz_activity_apply` aa
        WHERE aa.is_deleted = false
        <if test="params.volunteerId != null">
            AND aa.volunteer_id = #{params.volunteerId}
        </if>
        <if test="params.key != null and params.key != ''">
            AND ( aa.activity_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT (aa.time_period_end_time &lt; #{params.startTime} OR aa.time_period_start_time &gt;
            #{params.endTime} )
        </if>
        ORDER BY aa.sign_time DESC
    </select>
    <select id="listForVolunteerServiceLong"
            resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerServiceLongExportDto">
        SELECT
        aa.volunteer_name as volunteerName,
        aa.service_long as serviceLong,
        aa.sign_time as signTime,
        za.end_time as endTime,
        za.name as name
        FROM `zyz_activity_apply` aa
        LEFT JOIN zyz_activity za ON za.id = aa.activity_id
        WHERE aa.is_deleted = FALSE
        <if test="params.volunteerId != null">
            AND aa.volunteer_id = #{params.volunteerId}
        </if>
        <if test="params.key != null and params.key != ''">
            AND ( za.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT (za.end_time &lt; #{params.signTime} OR aa.sign_time &gt; #{params.endTime} )
        </if>
        ORDER BY aa.sign_time DESC
    </select>

    <select id="getApplyRecordById" resultType="com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto">
        SELECT zaa.id                   AS applyId,
               zaa.apply_time           AS applyTime,
               zaa.sign_time            AS signTime,
               zaa.apply_is_sync        AS applySync,
               zaa.service_long_is_sync AS serviceLongSync,
               zaa.audit_status         AS applyAuditStatus,
               zv.name                  AS volunteerName,
               zv.certificate_id        AS volunteerCertId,
               zaa.activity_name        AS actName,
               zatp.recruit_sync_id     AS actRecruitSyncId,
               zatp.start_time          AS actStartTime,
               zatp.end_time            AS actEndTime
        FROM zyz_activity_apply zaa
                 LEFT JOIN zyz_activity_time_period zatp ON zaa.time_period_id = zatp.id
                 LEFT JOIN zyz_volunteer zv ON zaa.volunteer_id = zv.id
        WHERE zaa.is_deleted = 0
          AND zatp.is_deleted = 0
          AND zv.is_deleted = 0 AND zv.write_off = 0
          AND zaa.id = #{applyId}
    </select>

    <select id="getNeedMemberSync" resultType="com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto">
        SELECT
        zaa.id AS applyId,
        zaa.apply_time AS applyTime,
        zaa.sign_time AS signTime,
        zaa.apply_is_sync AS applySync,
        zaa.service_long_is_sync AS serviceLongSync,
        zaa.audit_status AS applyAuditStatus,
        zv.name AS volunteerName,
        zv.certificate_id AS volunteerCertId,
        zaa.activity_name AS actName,
        zatp.recruit_sync_id AS actRecruitSyncId,
        zatp.start_time AS actStartTime,
        zatp.end_time AS actEndTime
        FROM zyz_activity_apply zaa
        LEFT JOIN zyz_activity_time_period zatp ON zaa.time_period_id = zatp.id
        LEFT JOIN zyz_volunteer zv ON zaa.volunteer_id = zv.id
        WHERE zaa.is_deleted = 0 AND zatp.is_deleted = 0 AND zv.is_deleted = 0 AND zv.write_off = 0 AND zatp.recruit_sync_id IS NOT NULL
        <if test="actTimePeriodId != null">
            AND zatp.id = #{actTimePeriodId}
        </if>
        AND zaa.audit_status = #{auditSuccessState}
        AND zatp.end_time &lt;= NOW()
        AND zatp.end_time &gt;= date_sub(now(),interval 15 day)
        AND (
        zaa.apply_is_sync = #{waitSyncState}
        OR
        (zatp.end_time &gt;= #{failStartTime} AND zaa.apply_is_sync = #{failSyncState})
        )
    </select>

    <select id="getSgbNeedMemberSync" resultType="java.lang.Long">
        SELECT zaa.id
        FROM zyz_activity_apply zaa
        LEFT JOIN zyz_activity_time_period zatp ON zaa.time_period_id = zatp.id
        LEFT JOIN zyz_volunteer zv ON zaa.volunteer_id = zv.id
        LEFT JOIN sgb_sync_status_record ssr_recruit
            ON ssr_recruit.business_type = 'sgb_sync_biz_activity_recruit'
                AND ssr_recruit.business_id = zatp.id
                AND ssr_recruit.is_deleted = 0
        LEFT JOIN sgb_sync_status_record ssr_member
            ON ssr_member.business_type = 'sgb_sync_biz_activity_member'
                AND ssr_member.business_id = zaa.id
                AND ssr_member.is_deleted = 0
        WHERE
            AND zaa.is_deleted = 0
            AND zatp.is_deleted = 0
            AND zv.is_deleted = 0
            AND zv.write_off = 0
            AND ssr_recruit.sync_status = 1
            AND zatp.end_time &lt;= NOW()
            AND zatp.end_time &gt;= #{failStartTime}
            <if test="actTimePeriodId != null">
                AND zatp.id = #{actTimePeriodId}
            </if>
            AND zaa.audit_status = #{auditSuccessState}
            AND (
                ssr_member.id IS NULL
                OR
                ssr_member.sync_status = 0
            )
    </select>

    <select id="getNeedServiceTimeSync" resultType="com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto">
        SELECT
        zaa.id AS applyId,
        zaa.apply_time AS applyTime,
        zaa.sign_time AS signTime,
        zaa.apply_is_sync AS applySync,
        zaa.service_long_is_sync AS serviceLongSync,
        zaa.audit_status AS applyAuditStatus,
        zv.name AS volunteerName,
        zv.certificate_id AS volunteerCertId,
        zaa.activity_name AS actName,
        zatp.recruit_sync_id AS actRecruitSyncId,
        zatp.start_time AS actStartTime,
        zatp.end_time AS actEndTime
        FROM zyz_activity_apply zaa
        LEFT JOIN zyz_activity_time_period zatp ON zaa.time_period_id = zatp.id
        LEFT JOIN zyz_volunteer zv ON zaa.volunteer_id = zv.id
        WHERE zaa.is_deleted = 0 AND zatp.is_deleted = 0 AND zv.is_deleted = 0 AND zv.write_off = 0 AND zaa.sign_time IS NOT NULL
        <if test="actTimePeriodId != null">
            AND zatp.id = #{actTimePeriodId}
        </if>
        AND zaa.audit_status = #{auditSuccessState}
        AND zaa.apply_is_sync = #{successSyncState}
        AND zatp.end_time &lt;= NOW()
        AND zatp.end_time &gt;= date_sub(now(),interval 15 day)
        AND (
        zaa.service_long_is_sync = #{waitSyncState}
        OR
        (zatp.end_time &gt;= #{failStartTime} AND zaa.service_long_is_sync = #{failSyncState})
        )
    </select>

    <select id="getSgbNeedServiceTimeSync" resultType="java.lang.Long">
        SELECT zaa.id
        FROM zyz_activity_apply zaa
        LEFT JOIN zyz_activity_time_period zatp ON zaa.time_period_id = zatp.id
        LEFT JOIN zyz_volunteer zv ON zaa.volunteer_id = zv.id
        LEFT JOIN sgb_sync_status_record ssr_member
            ON ssr_member.business_type = 'sgb_sync_biz_activity_member'
                AND ssr_member.business_id = zaa.id
                AND ssr_member.is_deleted = 0
        LEFT JOIN sgb_sync_status_record ssr_service
            ON ssr_service.business_type = 'sgb_sync_biz_service_time'
                AND ssr_service.business_id = zaa.id
                AND ssr_service.is_deleted = 0
        WHERE
            zaa.is_deleted = 0
            AND zatp.is_deleted = 0
            AND zv.is_deleted = 0
            AND zv.write_off = 0
            AND zaa.sign_time IS NOT NULL
            <if test="actTimePeriodId != null">
                AND zatp.id = #{actTimePeriodId}
            </if>
            AND zaa.audit_status = #{auditSuccessState}
            AND ssr_member.sync_status = 1
            AND zatp.end_time &lt;= NOW()
            AND zatp.end_time &gt;= #{failStartTime}
            AND (
                ssr_service.id IS NULL
                OR
                ssr_service.sync_status = 0
            )
    </select>

    <select id="myActApplyPage" resultType="com.fykj.scaffold.zyz.domain.dto.ApplyPageDto">
        SELECT
        a.id AS activityId,
        aa.id AS applyId,
        a.picture AS picture,
        a.name AS name,
        a.address AS address,
        <if test="params.status != null and params.status != ''">
            #{params.status} AS statusCode,
        </if>
        <if test="params.status == null or params.status == ''">
            CASE
            WHEN ap.start_time &gt; NOW() THEN 'aap_not_start'
            WHEN ap.start_time &lt;= NOW() AND ap.end_time &gt;= NOW() THEN 'aap_ing'
            ELSE 'aap_finished'
            END AS statusCode,
        </if>
        ap.start_time AS startTime,
        ap.end_time AS endTime,
        ap.sign_time AS signTime,
        CASE
        WHEN ap.end_time &gt;= NOW() THEN TRUE
        ELSE FALSE
        END AS enableCancel
        FROM zyz_activity_apply aa
        inner join zyz_activity_time_period ap on ap.id = aa.time_period_id
        inner JOIN zyz_activity a ON aa.activity_id = a.id
        WHERE aa.is_deleted = 0
        AND a.is_deleted = 0
        AND ap.is_deleted = false
        AND aa.volunteer_id = #{params.volunteerId}
        AND aa.audit_status = 'act_apply_audit_success'
        AND aa.is_user_hide = false
        <if test="params.key != null and params.key != ''">
            AND (a.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR a.address LIKE CONCAT('%', CONCAT(#{params.key},
            '%')))
        </if>
        <if test="params.status != null and params.status != ''">
            <if test="params.status == 'aap_not_start'">
                AND ap.start_time &gt; NOW()
            </if>
            <if test="params.status == 'aap_ing'">
                AND ap.start_time &lt;= NOW() AND ap.end_time &gt;= NOW()
            </if>
            <if test="params.status == 'aap_finished'">
                AND ap.end_time &lt; NOW()
            </if>
        </if>
        ORDER BY aa.apply_time DESC
    </select>

    <select id="myApplyPage" resultType="com.fykj.scaffold.zyz.domain.dto.ApplyPageDto">
        SELECT
        a.id AS activityId,
        aa.id AS applyId,
        a.picture AS picture,
        a.name AS name,
        a.address AS address,
        ap.start_time AS startTime,
        ap.end_time AS endTime,
        <if test="params.status != null and params.status != ''">
            #{params.status} AS statusCode,
        </if>
        aa.audit_remark AS failReason
        FROM zyz_activity_apply aa
        inner JOIN zyz_activity a ON aa.activity_id = a.id
        inner join zyz_activity_time_period ap on ap.id = aa.time_period_id
        WHERE aa.is_deleted = 0
        AND a.is_deleted = 0
        AND aa.volunteer_id = #{params.volunteerId}
        AND aa.is_user_hide = false
        <if test="params.status == 'ap_wait_audit'">
            AND aa.audit_status = 'act_apply_wait_audit' AND NOW() &lt; ap.start_time
        </if>
        <if test="params.status == 'ap_audit_success'">
            AND aa.audit_status = 'act_apply_audit_success'
        </if>
        <if test="params.status == 'ap_audit_reject'">
            AND aa.audit_status = 'act_apply_reject'
        </if>
        <if test="params.status == 'ap_cancelled'">
            AND aa.audit_status = 'act_apply_cancelled'
        </if>
        <if test="params.status == 'ap_lose_effective'">
            AND aa.audit_status = 'act_apply_wait_audit' AND NOW() &gt;= ap.start_time
        </if>
        ORDER BY aa.apply_time DESC
    </select>

    <select id="myServiceData" resultType="com.fykj.scaffold.zyz.domain.dto.ApplyPageDto">
        SELECT
        aa.activity_id AS activityId,
        aa.id AS applyId,
        aa.activity_name AS name,
        aa.apply_time AS applyTime,
        aa.sign_time AS signTime,
        aa.service_long AS serviceLong
        FROM zyz_activity_apply aa
        WHERE aa.is_deleted = 0
        AND aa.volunteer_id = #{params.volunteerId}
        AND aa.is_exchange = 1
        AND aa.is_user_hide = false
        <if test="params.status != null and params.status != ''">
            <if test="params.status == 'today'">
                AND DATE_FORMAT(aa.sign_time, '%Y-%m-%d') = DATE_FORMAT(NOW(), '%Y-%m-%d')
            </if>
            <if test="params.status == 'this_month'">
                AND DATE_FORMAT(aa.sign_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
            </if>
            <if test="params.status == 'this_year'">
                AND DATE_FORMAT(aa.sign_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
            </if>
        </if>
        ORDER BY aa.sign_time DESC
    </select>

    <select id="myActList" resultType="com.fykj.scaffold.zyz.domain.dto.DifferentDimensionActNumDto">
        SELECT ifnull(count(*), 0)                                                          as myActNum,
               ifnull(sum(if(ap.start_time &gt; NOW(), 1, 0)), 0)                           as notStartNum,
               ifnull(sum(if(ap.start_time &lt;= NOW() AND ap.end_time >= NOW(), 1, 0)), 0) as ingNum,
               ifnull(sum(if(ap.end_time &lt; NOW(), 1, 0)), 0)                             as endNum
        from zyz_activity_apply aa
                 inner join zyz_activity_time_period ap on aa.time_period_id = ap.id
        WHERE aa.is_deleted = 0
          AND ap.is_deleted = false
          AND ap.web_show = true
          AND aa.volunteer_id = #{volunteerId}
          AND aa.audit_status = 'act_apply_audit_success'
          AND aa.is_user_hide = false
    </select>

    <select id="getActTotalServiceTime" resultType="java.math.BigDecimal">
        SELECT
        SUM(service_long)
        FROM
        zyz_activity_apply
        WHERE
        is_deleted = 0
        AND activity_id IN (
        SELECT
        id
        FROM
        zyz_activity
        WHERE
        is_deleted = 0
        <if test="teamPublish != null">
            AND team_publish = #{teamPublish}
            <if test="teamPublish == true">
                AND team_id = #{teamId}
            </if>
            <if test="teamPublish == false">
                AND publish_org_code = #{orgCode}
            </if>
        </if>
        AND audit_status = 'act_audit_success'
        )
        AND audit_status = 'act_apply_audit_success'
        AND sign_time IS NOT NULL
    </select>

    <!--    <select id="getVolunteerOrgSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.VolunteerOrgSumDto">-->
    <!--        SELECT-->
    <!--            o.code_prefix AS orgCodePrefix,-->
    <!--            COUNT(aa.volunteer_id) AS num-->
    <!--        FROM (-->
    <!--            SELECT-->
    <!--                volunteer_id-->
    <!--            FROM zyz_activity_apply-->
    <!--            WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success'-->
    <!--            GROUP BY volunteer_id-->
    <!--            ) AS aa-->
    <!--        LEFT JOIN zyz_volunteer v ON aa.volunteer_id = v.id AND v.is_deleted = 0-->
    <!--        LEFT JOIN sys_org o ON v.org_code = o.code-->
    <!--        GROUP BY v.org_code-->
    <!--    </select>-->

    <select id="getVolunteerOrgSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.VolunteerOrgSumDto">
        SELECT o.code_prefix          AS orgCodePrefix,
               COUNT(aa.volunteer_id) AS num
        FROM (
                 SELECT volunteer_id
                 FROM zyz_activity_apply
                 WHERE is_deleted = 0
                   AND audit_status = 'act_apply_audit_success'
                   AND DATE_FORMAT(time_period_start_time, '%Y') = #{params.year}
                 GROUP BY volunteer_id
             ) AS aa
                 LEFT JOIN zyz_volunteer v ON aa.volunteer_id = v.id AND v.is_deleted = 0 AND v.write_off = 0
                 LEFT JOIN sys_org o ON v.org_code = o.code
        GROUP BY v.org_code
    </select>


    <select id="getActTotalServiceTimeThisYearForTeam" resultType="com.fykj.scaffold.zyz.domain.dto.TeamActServiceLongDto">
        SELECT
            IFNULL(SUM(service_long),0) as serviceLongThisYear,
            team_id as teamId
        FROM
            zyz_activity_apply
        WHERE is_deleted = 0
        AND audit_status = 'act_apply_audit_success'
        AND sign_time IS NOT NULL
        AND team_publish = true
        <if test="teamIds.size()>0">
            AND team_id in
            <foreach collection="teamIds" item="item" index="index"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND YEAR(time_period_start_time) = YEAR(CURDATE())
        GROUP BY team_id
    </select>

    <select id="getServiceLongForBlockchain"
            resultType="com.fykj.scaffold.zyz.domain.dto.block_chain.ActivityApplyLongBlockChainDto">
        SELECT id                     as id,
               volunteer_name         as volunteerName,
               volunteer_phone        as volunteerPhone,
               activity_name          as activityName,
               time_period_start_time as startTime,
               time_period_end_time   as endTime,
               service_long           as serviceLong,
               sign_time              as signTime
        FROM zyz_activity_apply
        where is_deleted = 0
              and is_exchange = true
        <if test="params.startTime != null ">
            AND sign_time &gt;= #{params.startTime}
        </if>
        <if test=" params.endTime != null">
            AND  sign_time &lt; #{params.endTime}
        </if>
    </select>

    <select id="getActTimePeriodNum" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply">
        SELECT
            org_code,
            time_period_id
        FROM
        zyz_activity_apply
        WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
        <if test="inOrgCodes != null and inOrgCodes.size() > 0">
            AND org_code IN
            <foreach collection="inOrgCodes" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="rLikeOrgCode != null and rLikeOrgCode != ''">
            AND org_code LIKE CONCAT(#{rLikeOrgCode}, '%')
        </if>
        GROUP BY time_period_id
    </select>

    <select id="getTimePeriodStats" resultType="com.fykj.scaffold.zyz.domain.dto.ActivityTimePeriodStatDto">
        SELECT
            aa.activity_id AS activityId,
            aa.time_period_id AS timePeriodId,
            DATE(tp.start_time) AS activityDate,
            TIME(tp.start_time) AS startTime,
            TIME(tp.end_time) AS endTime,
            COUNT(DISTINCT aa.volunteer_id) AS totalVolunteerCount,
            COUNT(DISTINCT IF(aa.sign_time IS NOT NULL, aa.volunteer_id, NULL)) AS servicedVolunteerCount,
            IFNULL(SUM(aa.service_long), 0) AS totalServiceLong
        FROM
            zyz_activity_apply aa
        JOIN
            zyz_activity_time_period tp ON aa.time_period_id = tp.id
        WHERE
            aa.is_deleted = 0
            AND aa.audit_status = 'act_apply_audit_success'
            AND aa.sign_time IS NOT NULL
            AND aa.service_long IS NOT NULL
        <if test="params.activityId != null">
            AND aa.activity_id = #{params.activityId}
        </if>
        GROUP BY
            aa.time_period_id,
            aa.activity_id,
            DATE(tp.start_time),
            TIME(tp.start_time),
            TIME(tp.end_time)
        ORDER BY
            activityDate ASC,
            startTime ASC
    </select>

    <select id="getVolunteerServiceLongDetails" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerServiceLongDetailDto">
        SELECT
            aa.volunteer_id AS volunteerId,
            aa.volunteer_name AS volunteerName,
            aa.service_long AS serviceLong
        FROM
            zyz_activity_apply aa
        WHERE
            aa.is_deleted = 0
            AND aa.audit_status = 'act_apply_audit_success'
            AND aa.sign_time IS NOT NULL
            AND aa.service_long IS NOT NULL
        <if test="params.activityId != null">
            AND aa.activity_id = #{params.activityId}
        </if>
        <if test="params.timePeriodId != null">
            AND aa.time_period_id = #{params.timePeriodId}
        </if>
        ORDER BY
            aa.volunteer_name ASC
    </select>

    <select id="getVolunteerServiceRecords" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerServiceRecordDto">
        SELECT
            aa.id,
            aa.activity_id as activityId,
            aa.volunteer_id as volunteerId,
            aa.volunteer_name as volunteerName,
            aa.volunteer_phone as volunteerPhone,
            aa.activity_name as activityName,
            aa.service_long as serviceLong,
            aa.sign_time as signTime,
            aa.apply_time as applyTime,
            aa.time_period_start_time as timePeriodStartTime,
            aa.time_period_end_time as timePeriodEndTime,
            a.address,
            t.name as teamName,
            aa.audit_status as auditStatus
        FROM
            zyz_activity_apply aa
        LEFT JOIN zyz_activity a ON aa.activity_id = a.id AND a.is_deleted = 0
        LEFT JOIN zyz_team t ON a.team_id = t.id AND t.is_deleted = 0
        LEFT JOIN zyz_volunteer v ON aa.volunteer_id = v.id AND v.is_deleted = 0
        WHERE
            aa.is_deleted = 0
            AND aa.sign_time IS NOT NULL
            <if test="params.phone != null and params.phone != ''">
                AND aa.volunteer_phone = #{params.phone}
            </if>
            <if test="params.startTime != null">
                AND aa.sign_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND aa.sign_time &lt;= #{params.endTime}
            </if>
        ORDER BY
            aa.sign_time DESC
    </select>

    <select id="getActivityPeriodStats" resultType="com.fykj.scaffold.zyz.domain.dto.ActivityPeriodStatDto">
        SELECT
            aa.activity_id AS activityId,
            aa.time_period_id AS timePeriodId,
            aa.activity_name AS activityName,
            tp.start_time AS startTime,
            tp.end_time AS endTime,
            COUNT(DISTINCT aa.volunteer_id) AS participantCount,
            IFNULL(SUM(aa.service_long), 0) AS totalServiceLong
        FROM
            zyz_activity_apply aa
        JOIN
            zyz_activity_time_period tp ON aa.time_period_id = tp.id
        JOIN
            zyz_activity a ON aa.activity_id = a.id
        WHERE
            aa.is_deleted = 0
            AND tp.is_deleted = 0
            AND a.is_deleted = 0
            AND aa.audit_status = 'act_apply_audit_success'
            <if test="params.activityName != null and params.activityName != ''">
                AND aa.activity_name LIKE CONCAT('%', #{params.activityName}, '%')
            </if>
            <if test="params.startTime != null">
                AND tp.start_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND tp.end_time &lt;= #{params.endTime}
            </if>
        GROUP BY
            aa.activity_id,
            aa.time_period_id,
            aa.activity_name,
            tp.start_time,
            tp.end_time
        ORDER BY
            tp.start_time DESC, aa.activity_id DESC
    </select>
</mapper>
