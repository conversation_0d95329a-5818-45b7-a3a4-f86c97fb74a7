package com.fykj.scaffold.mall.service;

import com.fykj.scaffold.mall.domain.dto.StockReplenishPlanDto;
import com.fykj.scaffold.mall.domain.entity.MallStockReplenishPlan;
import com.fykj.scaffold.mall.domain.params.MallStockReplenishPlanParams;
import fykj.microservice.core.base.IBaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 物料进补计划管理服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface IMallStockReplenishPlanService extends IBaseService<MallStockReplenishPlan> {

    /**
     * 获取补货计划列表
     * @param params
     * @return
     */
    List<StockReplenishPlanDto> getPlanList(MallStockReplenishPlanParams params);

    /**
     * 通过id获取进补计划单
     * @param id
     * @return
     */
    MallStockReplenishPlan getReplenishPlanById(Long id);

    /**
     * 保存或更新草稿
     * @param entity
     * @return
     */
    boolean saveOrUpdateDraft(MallStockReplenishPlan entity);

    /**
     * 提交
     * @param entity
     * @return
     */
    boolean submit(MallStockReplenishPlan entity);

    /**
     * 仅传id提交
     * @param id
     * @return
     */
    boolean submitById(Long id);

    /**
     * 补货计划生成
     * @param type
     * @param budgetAmount
     * @param year
     * @param machineId
     * @return
     */
    MallStockReplenishPlan generatePlan(String type, BigDecimal budgetAmount, String year, Long machineId);
}
