package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.security.business.domain.entity.Role;
import com.fykj.scaffold.security.business.domain.entity.SysUserExpand;
import com.fykj.scaffold.security.business.domain.entity.SysUserRole;
import com.fykj.scaffold.security.business.mapper.UserRoleMapper;
import com.fykj.scaffold.security.business.service.IRoleService;
import com.fykj.scaffold.security.business.service.ISysUserExpandService;
import com.fykj.scaffold.security.business.service.IUserRoleService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.conns.Cons;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 系统角色-用户关联
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UserRoleServiceImpl extends BaseServiceImpl<UserRoleMapper, SysUserRole> implements IUserRoleService {

    @Autowired
    private IRoleService roleService;

    @Autowired
    private ISysUserExpandService expandService;

    @Autowired
    private IUserService userService;

    @Override
    public List<Role> getByUserId(long userId) {
        List<Long> roleIds = lambdaQuery().eq(SysUserRole::getUserId, userId).list().stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return roleService.lambdaQuery().in(Role::getId, roleIds).list();
    }

    @Override
    public void saveUserRoleRelationShip(long userId, List<String> roleCodes) {
        baseMapper.deleteByUserId(userId);
        List<SysUserRole> list = new ArrayList<>();
        roleCodes.forEach(roleCode -> {
            SysUserRole userRole = new SysUserRole();
            Role role = roleService.getRoleByCode(roleCode);
            if (role == null) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "角色不存在");
            }
            userRole.setRoleId(role.getId());
            userRole.setUserId(userId);
            list.add(userRole);
        });
        saveBatch(list);
    }

    @Override
    public void addRole(long userId, String roleCode) {
        List<String> roles = getByUserId(userId).stream().map(Role::getCode).collect(Collectors.toList());
        if (roles.contains(roleCode)) {
            return;
        }
        roles.add(roleCode);
        saveUserRoleRelationShip(userId, roles);
    }

    @Override
    public void removeRole(long userId, String roleCode) {
        List<String> roles = getByUserId(userId).stream().map(Role::getCode).collect(Collectors.toList());
        if (!roles.contains(roleCode)) {
            return;
        }
        roles.removeIf(it -> it.equals(roleCode));
        saveUserRoleRelationShip(userId, roles);
    }

    @Override
    public void addTeamAdminRole(long userId, long teamId) {
        addRole(userId, Cons.RoleCode.ROLE_CODE_TEAM_ADMIN);
        List<SysUserExpand> expandList = expandService.getByUserId(userId);
        if (expandList.stream().anyMatch(it -> it.getTeamId() != null && teamId == it.getTeamId())) {
            return;
        }
        SysUserExpand userExpand = new SysUserExpand();
        userExpand.setUserId(userId);
        userExpand.setTeamId(teamId);
        userExpand.setRoleCode(Cons.RoleCode.ROLE_CODE_TEAM_ADMIN);
        expandList.add(userExpand);
        expandService.saveExpands(userId, expandList);
        userService.refreshUserContext(userId);
    }

    @Override
    public void removeTeamAdminRole(long userId, long teamId) {
        List<SysUserExpand> expandList = expandService.getByUserId(userId);
        if (expandList.stream().noneMatch(it -> it.getTeamId() != null && teamId != it.getTeamId())) {
            removeRole(userId, Cons.RoleCode.ROLE_CODE_TEAM_ADMIN);
        }
        boolean isRemove = expandList.removeIf(it -> it.getTeamId() != null && teamId == it.getTeamId());
        if (isRemove) {
            expandService.saveExpands(userId, expandList);
        }
        userService.forceOfflineUserByUserId(userId);
    }
}
