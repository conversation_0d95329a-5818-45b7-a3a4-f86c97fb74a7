package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallSelfMachine;
import com.fykj.scaffold.mall.domain.params.MallSelfMachineParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
public interface IMallSelfMachineService extends IBaseService<MallSelfMachine> {
    /**
     * 分页查询--后端页面
     */
    IPage<MallSelfMachine> getPage(MallSelfMachineParams params);

    IPage<MallSelfMachine> pageForApi(MallSelfMachineParams params);

    /**
     * 获取当前登录人组织及下级组织下的兑换机
     *
     * @return
     */
    List<MallSelfMachine> getMachinesByCurrOrg();
}

