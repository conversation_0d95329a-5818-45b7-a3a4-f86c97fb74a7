package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.SysConvertOrgName;
import com.fykj.scaffold.zyz.domain.params.SysConvertOrgNameParams;
import com.fykj.scaffold.zyz.service.ISysConvertOrgNameService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-03-20
 */
@RestController
@RequestMapping("/admin/sys/convert/org/name")
public class SysConvertOrgNameController extends BaseController<ISysConvertOrgNameService, SysConvertOrgName, SysConvertOrgNameParams> {

}
