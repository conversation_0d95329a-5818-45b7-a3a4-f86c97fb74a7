package com.fykj.scaffold.support.auth;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * API Token自动配置类
 * 用于启用API Token功能
 */
@Configuration
@EnableConfigurationProperties(ApiTokenProperties.class)
@ConditionalOnProperty(prefix = "api.token", name = "enabled", havingValue = "true", matchIfMissing = true)
public class ApiTokenAutoConfiguration {

    @Bean
    public ApiTokenAspect apiTokenAspect(ApiTokenProperties apiTokenProperties) {
        return new ApiTokenAspect();
    }
} 