package com.fykj.scaffold.portal_website.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 培训视频查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("培训视频查询参数")
public class VideoCategoryParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    /**
     * 名称
     */
    private String title;

    /**
     * 类型
     */
    private String type;

    /**
     * 类型
     */
    private Boolean grounding;

    /**
     * 栏目id
     */
    private String categoryId;

    /**
     * 栏目code
     */
    private String categoryCode;

    /**
     * 区域code
     */
    private String district;
}
