<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzTeamMapper">

    <select id="getMyPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeam">
        SELECT
            zv.name as volunteerName,
            zt.name as `name`,
            zt.team_photo as  teamPhoto,
            zt.team_number as  teamN<PERSON>ber,
            zt.admin_name as  contact<PERSON><PERSON>,
            zt.admin_contact as contact<PERSON>hone,
            zv.`status` as volunteerStatus,
            so.`name` as orgName
        FROM zyz_volunteer_team zvt
                 LEFT JOIN zyz_team zt on zvt.team_id = zt.id
                 LEFT JOIN sys_org so on zt.org_code = so.code
                 LEFT JOIN zyz_volunteer zv on zvt.volunteer_id = zv.id
        WHERE zvt.is_deleted = false
        <if test="params.teamName != null and params.teamName != ''">
            and zt.name LIKE CONCAT('%',#{params.teamName},'%')
        </if>
        <if test="params.volunteerId != null and params.volunteerId != ''">
            and zvt.volunteer_id = #{params.volunteerId}
        </if>
    </select>
    <select id="getMyPagesForApi" resultType="com.fykj.scaffold.zyz.domain.dto.MyTeamDto">
        SELECT
        zt.id as id,
        ad.id as auditId,
        zt.name as `name`,
        zt.team_photo as  teamPhoto,
        zt.team_number as  teamNumber,
        zt.admin_name as  contactPerson,
        zt.admin_contact as contactPhone,
        ad.`status` as volunteerStatus,
        so.`name` as orgName,
        ad.remark as remark,
        zvt.join_time as joinTime
        FROM zyz_volunteer_team_audit ad
        left join zyz_volunteer_team zvt on ad.volunteer_id=zvt.volunteer_id and ad.team_id=zvt.team_id
        LEFT JOIN zyz_team zt on ad.team_id = zt.id
        LEFT JOIN sys_org so on zt.org_code = so.code
        WHERE ad.is_deleted = false and ad.volunteer_id = #{params.volunteerId}
        <if test="params.teamName != null and params.teamName != ''">
            and zt.name LIKE CONCAT('%',#{params.teamName},'%')
        </if>
       order by ad.create_date  desc
    </select>
    <select id="getExportList" resultType="com.fykj.scaffold.zyz.domain.dto.TeamExportDto">
        SELECT
        zv.name as volunteerName,
        zt.name as `name`,
        zt.team_number as  teamNumber,
        zt.admin_name as  contactPerson,
        zt.admin_contact as contactPhone,
        so.`name` as orgName
        FROM zyz_volunteer_team zvt
        LEFT JOIN zyz_team zt on zvt.team_id = zt.id
        LEFT JOIN sys_org so on zt.org_code = so.code
        LEFT JOIN zyz_volunteer zv on zvt.volunteer_id = zv.id
        WHERE zvt.is_deleted = false
        <if test="params.teamName != null and params.teamName != ''">
            and zt.name LIKE CONCAT('%',#{params.teamName},'%')
        </if>
        <if test="params.volunteerId != null and params.volunteerId != ''">
            and zvt.volunteer_id = #{params.volunteerId}
        </if>
    </select>

    <select id="getPassTeamPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeam">
        SELECT
        t.*,
        so.name as orgName
        FROM
        zyz_team t
        LEFT JOIN sys_org so on t.org_code = so.code
        WHERE
        t.is_deleted = FALSE
        AND t.team_status = 'team_audit_pass'
        <if test="params.teamNo != null and params.teamNo != ''">
            and t.team_no = #{params.teamNo}
        </if>
        <if test="params.status != null">
            and t.status = #{params.status}
        </if>
        <if test="params.admin != null and params.admin != ''">
            and (t.admin_name LIKE CONCAT('%',#{params.admin},'%') OR t.admin_contact LIKE CONCAT('%',#{params.admin},'%'))
        </if>
        <if test="params.curator != null and params.curator != ''">
            and (t.curator_name LIKE CONCAT('%',#{params.curator},'%') OR t.curator_contact LIKE CONCAT('%',#{params.curator},'%'))
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and t.org_code = #{params.orgCode}
        </if>
        <if test="params.name != null and params.name != ''">
            and t.name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.isEmphasis != null and params.isEmphasis != ''">
            and t.is_emphasis = #{params.isEmphasis}
        </if>
        ORDER BY t.create_date  desc
    </select>
    <select id="getTeamList" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeam">
        SELECT
        t.*,
        aa.livelyCount as livelyCountThisYear,
        aa.serviceLongThisYear as serviceLongThisYear,
        so.name as orgName
        FROM
        zyz_team t
        LEFT JOIN sys_org so on t.org_code = so.code
        LEFT JOIN (
        SELECT
        vt.team_id,
        sum(IF( v.is_lively = TRUE, 1, 0 )) AS livelyCount,
        SUM(service_long_this_year) as serviceLongThisYear
        FROM
        zyz_volunteer_team vt
        LEFT JOIN zyz_volunteer v ON v.id = vt.volunteer_id
        GROUP BY vt.team_id
        ) aa ON aa.team_id = t.id
        WHERE
        t.is_deleted = FALSE
        AND t.team_status = 'team_audit_pass'
        <if test="params.teamNo != null and params.teamNo != ''">
            and t.team_no = #{params.teamNo}
        </if>
        <if test="params.status != null and params.status != ''">
            and t.status = #{params.status}
        </if>
        <if test="params.admin != null and params.admin != ''">
            and (t.admin_name LIKE CONCAT('%',#{params.admin},'%') OR t.admin_contact LIKE CONCAT('%',#{params.admin},'%'))
        </if>
        <if test="params.curator != null and params.curator != ''">
            and (t.curator_name LIKE CONCAT('%',#{params.curator},'%') OR t.curator_contact LIKE CONCAT('%',#{params.curator},'%'))
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and t.org_code = {params.orgCode}
        </if>
        <if test="params.name != null and params.name != ''">
            and t.name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.isEmphasis != null and params.isEmphasis != ''">
            and t.is_emphasis = #{params.isEmphasis}
        </if>
        ORDER BY t.create_date  desc
    </select>
    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeam">
        SELECT
        t.*,
        so.name as orgName,
        so_audit.name as auditOrgName
        FROM zyz_team t
        LEFT JOIN sys_org so on t.org_code = so.code
        LEFT JOIN sys_org so_audit on t.audit_org_code = so_audit.code
        WHERE t.is_deleted = FALSE
        <if test="params.teamNo != null and params.teamNo != ''">
            and t.team_no = #{params.teamNo}
        </if>
        <if test="params.status != null">
            and t.status = #{params.status}
        </if>
        <if test="params.auditType != null">
          <if test="params.auditType == 0">
              AND (t.audit_type = 0 OR t.audit_type IS NULL)
          </if>
            <if test="params.auditType == 1">
                AND (t.audit_type = 1)
            </if>
        </if>
        <if test="params.needAudit != null and params.needAudit == true ">
            AND ((t.audit_org_code = #{params.myOrgCode} AND t.team_status = 'team_audit_waiting')
            Or (t.audit_type = 1 AND t.info_change_audit_status = 'team_audit_waiting' AND t.org_code = #{params.myOrgCode}))
        </if>
        <if test="params.admin != null and params.admin != ''">
            and (t.admin_name LIKE CONCAT('%',#{params.admin},'%') OR t.admin_contact LIKE CONCAT('%',#{params.admin},'%'))
        </if>
        <if test="params.teamStatus != null and params.teamStatus != ''">
            and (t.team_status = #{params.teamStatus} OR t.info_change_audit_status = #{params.teamStatus})
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and t.org_code LIKE CONCAT(#{params.orgCode},'%')
        </if>
        <if test="params.curator != null and params.curator != ''">
            and (t.curator_name LIKE CONCAT('%',#{params.curator},'%') OR t.curator_contact LIKE CONCAT('%',#{params.curator},'%'))
        </if>
        <if test="params.name != null and params.name != ''">
            and t.name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.isEmphasis != null and params.isEmphasis != ''">
            and t.is_emphasis = #{params.isEmphasis}
        </if>
        <if test="params.startCreateDate != null and params.startCreateDate  !=''">
            and date(t.create_date) &gt;= #{params.startCreateDate}
        </if>
        <if test="params.endCreateDate != null and params.endCreateDate  !=''">
            and date(t.create_date) &lt;= #{params.endCreateDate}
        </if>
        ORDER BY t.create_date  desc
    </select>


    <select id="getTeamCount" resultType="com.fykj.scaffold.zyz.domain.dto.TeamCountDto">
        SELECT
            vt.team_id,
            sum(IF( v.is_lively = TRUE, 1, 0 )) AS livelyCountThisYear,
            SUM(service_long_this_year) as serviceLongThisYear
        FROM
            zyz_volunteer_team vt
        LEFT JOIN zyz_volunteer v ON v.id = vt.volunteer_id
        WHERE
            <if test="teamIds.size()>0">
                vt.team_id in
                <foreach collection="teamIds" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and  vt.is_deleted = false
        GROUP BY vt.team_id
    </select>
    <select id="getActivityCount" resultType="com.fykj.scaffold.zyz.domain.dto.TeamCountDto">
        SELECT
        team_id as teamId,
        COUNT(*) as activityCountThisYear
        FROM
        zyz_activity
        where
            <if test="teamIds.size()>0">
                team_id in
                <foreach collection="teamIds" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        and is_deleted = false
        and year(start_time) = year(current_date())
        GROUP BY team_id
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.zyz.domain.dto.TeamApiDto">
        select t.id,t.name as teamName,t.team_number as teamNum,
               t.team_photo as logo,
               t.is_emphasis as isEmphasis,
               t.services as services,
               IFNULL(tja.actJoinNum, 0) AS teamJoinActNum
        from zyz_team t
        LEFT JOIN sys_org so on t.org_code = so.code
        LEFT JOIN (
            SELECT team_id, COUNT(id) AS actJoinNum
            FROM zyz_activity
            WHERE is_deleted = 0 AND audit_status = 'act_audit_success' AND team_publish = 1
            GROUP BY team_id
        ) tja ON t.id = tja.team_id
        where t.is_deleted = FALSE and t.team_status='team_audit_pass' and t.status=true
        <if test="params.fieldId !=null and params.fieldId !=''">
          AND t.belong_fields LIKE CONCAT('%',#{params.fieldId},'%')
        </if>
        <if test="params.teamName != null and params.teamName != ''">
            and t.name LIKE CONCAT('%',#{params.teamName},'%')
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            <if test="params.orgCode != 'top_dept'">
                AND so.code_prefix LIKE CONCAT(#{params.orgCode},'%')
            </if>
            <if test="params.orgCode == 'top_dept'">
                AND so.code_prefix not LIKE CONCAT('top_dept,loufeng','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,xietang','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,weiting','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,shengpu','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,jinjihu','%')
            </if>
        </if>
        <if test="params.isEmphasis != null and params.isEmphasis != ''">
            and t.is_emphasis = #{params.isEmphasis}
        </if>
        <choose>
            <when test="params.orderByNum==true">
                order by t.team_number
            </when>
            <when test="params.orderByNum==false">
                order by t.team_number desc
            </when>
            <when test="params.orderByActive != null and params.orderByActive == true">
                order by teamJoinActNum DESC
            </when>
            <otherwise>
               order by t.founded desc
            </otherwise>
        </choose>
    </select>
    <select id="getMyTeamPagesForApi" resultType="com.fykj.scaffold.zyz.domain.dto.MyTeamDto">
        SELECT
            zt.id as id,
            zt.name as `name`,
            zt.team_photo as  teamPhoto,
            zt.admin_name as  contactPerson,
            zt.admin_contact as contactPhone,
            zvt.join_time as joinTime
        FROM zyz_volunteer_team zvt
                 LEFT JOIN zyz_team zt on zvt.team_id = zt.id
          where zvt.is_deleted = false
            and status = true
            and zvt.volunteer_id = #{params.volunteerId}
       order by zvt.join_time desc
    </select>

    <select id="getNeedSync" resultType="java.lang.Long">
        SELECT zt.id
        FROM zyz_team zt
        INNER JOIN (
            SELECT team_id,
                   MAX(operate_time) AS time
            FROM zyz_team_audit_log
            WHERE audit_status = 'team_audit_pass'
            GROUP BY team_id
        ) AS nt ON zt.id = nt.team_id
        WHERE zt.is_deleted = 0
          AND zt.team_status = 'team_audit_pass'
          AND nt.time &lt;= #{now}
          AND (zt.is_sync = 'sync_wait' OR (zt.is_sync = 'sync_failure' AND nt.time &gt;= #{threeDaysBeforeNow}))
    </select>

    <select id="getSgbNeedSync" resultType="java.lang.Long">
        SELECT zt.id
        FROM zyz_team zt
        INNER JOIN (
            SELECT team_id,
                   MAX(operate_time) AS time
            FROM zyz_team_audit_log
            WHERE audit_status = 'team_audit_pass'
            GROUP BY team_id
        ) AS nt ON zt.id = nt.team_id
        LEFT JOIN sgb_sync_status_record ssr
                  ON ssr.business_type = 'sgb_sync_biz_team'
                      AND ssr.business_id = zt.id
                      AND ssr.is_deleted = 0
        WHERE zt.is_deleted = 0
          AND zt.team_status = 'team_audit_pass'
          AND nt.time &lt;= #{now}
          AND (
                ssr.id IS NULL
                OR (ssr.sync_status = 0 AND nt.time &gt;= #{threeDaysBeforeNow})
              )
    </select>

    <select id="getTeamNumByStatusAndOrgCode" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_team
        WHERE is_deleted = 0
          AND ((team_status = #{status} AND audit_org_code = #{orgCode})
            OR (audit_type = 1 AND info_change_audit_status = #{status} AND org_code = #{orgCode}))
    </select>

    <select id="getNameById" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeam">
        SELECT id, name
        FROM zyz_team
        WHERE id IN (<foreach item="item" collection="teamIds" index="" separator=",">#{item}</foreach>)
    </select>

    <select id="sumNumByOrg" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto">
        SELECT
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            org_code AS orgCode,
        </if>
        COUNT(id) AS sumNum
        FROM zyz_team
        WHERE is_deleted = 0 AND team_status = 'team_audit_pass' and status = 1
        <if test="params.startDate != null">
            AND founded &gt;= #{params.startDate}
        </if>
        <if test="params.endDate != null">
            AND founded &lt;= #{params.endDate}
        </if>
        <if test="params.ignoreOrgCodes != null and params.ignoreOrgCodes.length > 0">
            <if test="params.ignoreOrgCodes.length == 1">
                AND org_code != #{params.ignoreOrgCodes[0]}
            </if>
            <if test="params.ignoreOrgCodes.length > 1">
                AND org_code NOT IN
                <foreach item="item" collection="params.ignoreOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            GROUP BY org_code
        </if>
    </select>

    <select id="getScreenDataList" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.ReqResActTeamDataDto">
        SELECT
            name,
            COUNT(DISTINCT v.id) AS volunteerNum,
            SUM(IFNULL(v.service_long, 0)) AS serviceDuration
        FROM zyz_team t
        LEFT JOIN (SELECT team_id, volunteer_id FROM zyz_volunteer_team WHERE is_deleted = 0) vt ON t.id = vt.team_id
        LEFT JOIN (SELECT id, service_long FROM zyz_volunteer WHERE is_deleted = 0) v ON vt.volunteer_id = v.id
        WHERE t.is_deleted = 0 AND team_status = 'team_audit_pass'
        <if test="params.startDate != null">
            AND t.founded &gt;= #{params.startDate}
        </if>
        <if test="params.endDate != null">
            AND t.founded &lt;= #{params.endDate}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND t.org_code = #{params.orgCode}
        </if>
        GROUP BY t.id
        ORDER BY t.founded DESC
        LIMIT 100
    </select>

    <select id="getOrgTeamInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            SUM(IFNULL(nt.teamNum, 0)) AS teamNum
        FROM sys_org o
        LEFT JOIN (
            SELECT
                o.code_prefix,
                COUNT(t.id) AS teamNum
            FROM zyz_team t
            LEFT JOIN sys_org o ON t.org_code = o.code
            WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                AND t.org_code LIKE CONCAT(#{params.parentOrgCode}, '%')
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                AND t.org_code != 'top_dept'
            </if>
            <if test="params.start != null">
                AND t.founded &gt;= #{params.startDate}
            </if>
            <if test="params.end != null">
                AND t.founded &lt;= #{params.endDate}
            </if>
            GROUP BY o.code_prefix
        ) nt ON nt.code_prefix LIKE CONCAT(o.code_prefix, '%')
        <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
            WHERE o.code IN
            <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY o.code
        ORDER BY o.sequence
    </select>

    <select id="getOrgSelfTeamSum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM zyz_team
        WHERE is_deleted = 0 AND team_status = 'team_audit_pass' AND org_code = #{orgCode}
        <if test="start != null">
            AND founded &gt;= #{start}
        </if>
        <if test="end != null">
            AND founded &lt;= #{end}
        </if>
    </select>

    <select id="getOtherOrgTeamSum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM zyz_team
        WHERE is_deleted = 0 AND team_status = 'team_audit_pass'
        AND org_code NOT LIKE 'jinjihu%'
        AND org_code NOT LIKE 'loufeng%'
        AND org_code NOT LIKE 'xietang%'
        AND org_code NOT LIKE 'shengpu%'
        AND org_code NOT LIKE 'weiting%'
        AND org_code != 'top_dept'
        <if test="start != null">
            AND founded &gt;= #{start}
        </if>
        <if test="end != null">
            AND founded &lt;= #{end}
        </if>
    </select>
    <select id="pageForFrontend" resultType="com.fykj.scaffold.zyz.domain.dto.TeamApiDto">
        select
            t.id,
            t.name as teamName,
            t.team_number as teamNum,
            t.team_photo as logo,
            t.is_emphasis as isEmphasis,
            t.services as services,
            IFNULL(SUM(v.service_long), 0) as serviceLong
        FROM zyz_team t
        LEFT JOIN sys_org so ON t.org_code = so.code
        LEFT JOIN zyz_volunteer_team vt ON vt.team_id = t.id
        LEFT JOIN zyz_volunteer v ON v.id = vt.volunteer_id
        <where>
            AND t.is_deleted = FALSE
            AND t.team_status='team_audit_pass'
            AND t.status = TRUE
            <if test="params.orgCode != null and params.orgCode != ''">
                AND so.code_prefix LIKE CONCAT(#{params.orgCode},'%')
            </if>
        </where>
        GROUP BY t.id, t.name, t.team_number, t.team_photo, t.is_emphasis, t.services
        ORDER BY serviceLong DESC
    </select>

    <select id="getTeamStat" resultType="com.fykj.scaffold.zyz.domain.dto.TeamStatDto">
        SELECT 
            t.id as teamId,
            t.name as teamName,
            COUNT(DISTINCT vt.volunteer_id) as volunteerCount,
            IFNULL(SUM(v.service_long), 0) as totalServiceLong,
            (SELECT COUNT(id) FROM zyz_activity WHERE team_id = #{teamId} AND is_deleted = 0) as activityCount
        FROM 
            zyz_team t
        LEFT JOIN 
            zyz_volunteer_team vt ON t.id = vt.team_id AND vt.is_deleted = 0
        LEFT JOIN 
            zyz_volunteer v ON vt.volunteer_id = v.id AND v.is_deleted = 0
        WHERE 
            t.id = #{teamId}
            AND t.is_deleted = 0
        GROUP BY 
            t.id, t.name
    </select>
</mapper>
