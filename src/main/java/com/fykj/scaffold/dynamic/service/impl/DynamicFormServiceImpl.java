package com.fykj.scaffold.dynamic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fykj.scaffold.dynamic.domain.entity.DynamicField;
import com.fykj.scaffold.dynamic.domain.entity.DynamicFieldSnapshot;
import com.fykj.scaffold.dynamic.domain.entity.DynamicForm;
import com.fykj.scaffold.dynamic.domain.entity.DynamicFormFieldRelation;
import com.fykj.scaffold.dynamic.mapper.DynamicFormMapper;
import com.fykj.scaffold.dynamic.service.IDynamicFieldService;
import com.fykj.scaffold.dynamic.service.IDynamicFieldSnapshotService;
import com.fykj.scaffold.dynamic.service.IDynamicFormFieldRelationService;
import com.fykj.scaffold.dynamic.service.IDynamicFormService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import result.ResultCode;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 模板信息-服务实现类
 *
 * @date 2025-05-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DynamicFormServiceImpl extends BaseServiceImpl<DynamicFormMapper, DynamicForm> implements IDynamicFormService {

    @Autowired
    private IDynamicFormFieldRelationService formFieldRelationService;

    @Autowired
    private IDynamicFieldService fieldService;

    @Autowired
    private IDynamicFieldSnapshotService fieldSnapshotService;

    @Override
    public boolean add(DynamicForm form) {
        // 1. 保存表单基本信息
        boolean saveResult = save(form);
        if (!saveResult) {
            return false;
        }
        
        // 2. 检查并保存表单字段关联关系
        List<DynamicField> fields = form.getFields();
        if (CollectionUtils.isEmpty(fields)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "表单字段不能为空");
        }
        
        // 3. 构建并保存表单字段关联关系
        return saveRelations(form, fields);
    }

    @Override
    public boolean edit(DynamicForm form) {
        // 1. 更新表单基本信息
        boolean updateResult = updateById(form);
        if (!updateResult) {
            return false;
        }

        // 2. 检查并验证表单字段
        List<DynamicField> fields = form.getFields();
        if (CollectionUtils.isEmpty(fields)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "表单字段不能为空");
        }

        // 3. 删除原有的表单字段关联关系
        formFieldRelationService.removeByFormId(form.getId());

        // 4. 构建并保存新的表单字段关联关系
        return saveRelations(form, fields);
    }

    private boolean saveRelations(DynamicForm form, List<DynamicField> fields) {
        List<DynamicFormFieldRelation> relations = fields.stream()
                .map(field -> {
                    DynamicFormFieldRelation relation = new DynamicFormFieldRelation();
                    relation.setFormId(form.getId());
                    relation.setFieldId(field.getId());
                    relation.setModuleCode(form.getModuleCode());
                    relation.setRequireFlag(field.getRequireFlag());
                    relation.setFieldOrder(field.getFieldOrder());
                    return relation;
                })
                .collect(Collectors.toList());

        return formFieldRelationService.saveBatch(relations);
    }

    @Override
    public DynamicForm detail(Serializable formId) {
        // form信息
        DynamicForm form = getById(formId);
        List<DynamicField> fields = getRelatedFieldsByFormId(formId);
        DictTransUtil.trans(fields);
        form.setFields(fields);
        return form;
    }

    @Override
    public Long generateFormFieldSnapshot(Serializable formId) {
        // fields
        List<DynamicField> fields = getRelatedFieldsByFormId(formId);
        // 生成snapshot id
        long snapshotId = IdWorker.getId();
        // 构建dynamicFieldSnapshots
        List<DynamicFieldSnapshot> dynamicFieldSnapshots = BeanUtil.copyToList(
                fields, DynamicFieldSnapshot.class,
                CopyOptions.create().setIgnoreProperties("id"));
        // 赋值snapshot id
        dynamicFieldSnapshots.forEach(fieldSnapshot -> fieldSnapshot.setSnapshotId(snapshotId));
        fieldSnapshotService.saveBatch(dynamicFieldSnapshots);
        return snapshotId;
    }

    private List<DynamicField> getRelatedFieldsByFormId(Serializable formId) {
        // fieldId -> relation
        LinkedHashMap<Long, DynamicFormFieldRelation> fieldId2Relation = formFieldRelationService.lambdaQuery()
                .eq(DynamicFormFieldRelation::getFormId, formId)
                .orderByAsc(DynamicFormFieldRelation::getFieldOrder)
                .list().stream()
                .collect(Collectors.toMap(
                        DynamicFormFieldRelation::getFieldId, Function.identity(),
                        (o, n) -> o,
                        LinkedHashMap::new));
        // fields
        List<DynamicField> fields = fieldService.listByIds(fieldId2Relation.keySet());
        // 补充fields字段
        fields.forEach(field -> {
            DynamicFormFieldRelation relation = fieldId2Relation.get(field.getId());
            field.setRequireFlag(relation.getRequireFlag());
            field.setFieldOrder(relation.getFieldOrder());
        });
        return fields;
    }

}
