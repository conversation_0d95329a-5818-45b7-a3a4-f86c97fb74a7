package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 字典数据查询响应实体
 */
@Data
public class DictResponse {
    
    /**
     * 响应信息列表
     */
    private List<DictInfo> info;
    
    /**
     * 字典数据信息实体
     */
    @Data
    public static class DictInfo {
        /**
         * 响应状态码
         * SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 字典类型
         */
        private String type;
        
        /**
         * 字典名称
         */
        private String label;
        
        /**
         * 字典值
         */
        private String value;
        
        /**
         * 字典描述
         */
        private String describe;
    }
} 