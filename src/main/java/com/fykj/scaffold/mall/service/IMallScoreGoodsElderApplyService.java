package com.fykj.scaffold.mall.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsElderApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsElderApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsElderApplyParams;
import fykj.microservice.core.base.IBaseService;

/**
 * 星光闪耀适老化改造福申请表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2024-10-16
 */
public interface IMallScoreGoodsElderApplyService extends IBaseService<MallScoreGoodsElderApply> {

    /**
     * 新增/修改报名信息
     *
     * @param apply 报名信息
     * @return 是否成功
     */
    Boolean saveOrUpdateApply(MallScoreGoodsElderApply apply);

    /**
     * 分页查询--后端页面
     */
    IPage<MallScoreGoodsElderApplyDto> getPage(MallScoreGoodsElderApplyParams params);

    /**
     * 获取报名信息
     * @param exchangeId
     * @return
     */
    MallScoreGoodsElderApply getApplyInfo(Long exchangeId);
}

