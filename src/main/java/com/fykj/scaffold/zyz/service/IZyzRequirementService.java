package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.dto.MyDockingExportDto;
import com.fykj.scaffold.zyz.domain.dto.RequirementDockingExportDto;
import com.fykj.scaffold.zyz.domain.dto.RequirementExportDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzRequirementForUpdateSyncDto;
import com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto;
import com.fykj.scaffold.zyz.domain.dto.ResourceByRequirementDto;
import com.fykj.scaffold.zyz.domain.dto.data_screen.NumPercentSumDto;
import com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto;
import com.fykj.scaffold.zyz.domain.dto.data_screen.ReqResActTeamDataDto;
import com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import com.fykj.scaffold.zyz.domain.params.ResourceRequirementParams;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementParams;
import fykj.microservice.core.base.IBaseService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 需求表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-02-23
 */
public interface IZyzRequirementService extends IService<ZyzRequirement>, IBaseService<ZyzRequirement> {

    /**
     * 分页查询需求对接
     * @param params
     * @return
     */
    IPage<ZyzRequirement> pagesForDocking(ZyzRequirementParams params);

    /**
     * 导出列表查询需求对接
     * @param params
     * @return
     */
    List<RequirementDockingExportDto> listForDocking(ZyzRequirementParams params);

    /**
     * 分页查询我的对接
     * @param params
     * @return
     */
    IPage<ZyzRequirement> pagesForMyDocking(ZyzRequirementParams params);

    /**
     * 导出列表查询我的对接
     * @param params
     * @return
     */
    List<MyDockingExportDto> listForMyDocking(ZyzRequirementParams params);

    /**
     * 分页查询需求审核
     * @param params
     * @return
     */
    IPage<ZyzRequirement> pagesForAudit(ZyzRequirementParams params);

    /**
     * 获取待审核的需求数量
     * @return
     */
    Integer getReqWaitAuditNum();


    /**
     * 需求对接
     * @param id
     * @param contactPerson
     * @param contactPhone
     */
    void docking(String id, String contactPerson, String contactPhone);

    /**
     * 需求管理  --分页查询
     * @param params
     * @return
     */
    IPage<ZyzRequirement> getPages(ZyzRequirementParams params);

    /**
     * 保存需求
     * @param entity
     * @return
     */
    void saveRequirement(ZyzRequirement entity);

    /**
     * 提交
     * @param id
     * @return
     */
    boolean render(Long id);

    /**
     * 上下架
     * @param id
     * @return
     */
    boolean updateAutoStatus(Long id);
    /**
     * 导出需求列表查询
     * @param params
     * @return
     */
    List<RequirementExportDto> getList(ZyzRequirementParams params);

    boolean renderByRequirement(ZyzRequirement entity);

    /**
     * 需求审核
     * @param idList
     * @param status
     * @param remark
     */
    void audit(List<Long> idList, Boolean status, String remark);

    /**
     * 评价
     * @param id 需求Id
     * @param evaluateRemark 评价内容
     * @param evaluateStar 评价星级
     * @return
     */
    boolean updateRemark(Long id, String evaluateRemark, BigDecimal evaluateStar);

    /**
     * 获取允许对接的需求
     * @param actId
     * @param teamId
     * @return
     */
    List<ZyzRequirement> getEnableDocking(Long actId, Long teamId);

    /**
     * 获取需要进行实践单同步的需求id
     * @return
     */
    List<Long> getNeedPracticeSheetSync();

    IPage<ZyzRequirement> getPagesForMini(ZyzRequirementParams params);

    /**
     * 获取每天定时任务需要同步的数据
     * @return
     */
    List<Long> getNeedSync();

    /**
     * 批量同步
     * @param requirementIds
     */
    void syncBatch(List<Long> requirementIds);

    /**
     * 根据id获取需求
     * @param id
     * @return
     */
    ZyzRequirement getRequirementById(Long id);

    /**
     * 获取时间段内创建的需求
     * @param start
     * @param end
     * @param teamPublish
     * @param orgCode
     * @param teamId
     * @return
     */
    List<ZyzRequirement> getRecordsBetweenDateRange(LocalDateTime start, LocalDateTime end, Boolean teamPublish, String orgCode, Long teamId);

    /**
     * 获取组织/团队需求对接数量
     * @param teamDocking
     * @param orgCode
     * @param teamId
     * @return
     */
    Integer getDockingReqNum(Boolean teamDocking, String orgCode, Long teamId);

    /**
     * 获取组织/团队发布需求数量
     * @param teamPublish
     * @param orgCode
     * @param teamId
     * @return
     */
    Integer getPublishReqNum(Boolean teamPublish, String orgCode, Long teamId);

    /**
     * 按组织分组统计数量
     * @param params
     * @return
     */
    List<OrgSumDto> sumNumByOrg(DataScreenParams params);

    /**
     * 组织下资源需求类型占比统计
     * @param params
     * @return
     */
    List<NumPercentSumDto> sumReqResTypePercent(DataScreenParams params);

    /**
     * 获取大屏数据列表
     * @param params
     * @return
     */
    List<ReqResActTeamDataDto> getScreenDataList(DataScreenParams params);

    /**
     * 获取组织需求数据统计
     * @param params
     * @return
     */
    List<OrgDataFormDto> getOrgReqInfoSum(OrgDataReportFormParams params);

    /**
     * 获取团队需求数据统计
     * @param params
     * @return
     */
    List<OrgDataFormDto> getTeamReqInfoSum(OrgDataReportFormParams params);

    /**
     * 获取组织本级需求数据统计
     * @param orgCode
     * @param start
     * @param end
     * @return
     */
    Integer getOrgSelfReqInfoSum(String orgCode, LocalDateTime start, LocalDateTime end);

    /**
     * 获取其他组织需求数据统计
     * @param start
     * @param end
     * @return
     */
    Integer getOtherOrgReqInfoSum(LocalDateTime start, LocalDateTime end);

    /**
     * 获取需求(修改并同步时使用)
     * @param id
     * @return
     */
    ZyzRequirementForUpdateSyncDto getRequirementForUpdateSync(Long id);

    /**
     * 管理员修改(重新同步使用)
     * @param data
     */
    void updateForReSync(ZyzRequirementForUpdateSyncDto data);

    /**
     * 管理员修改后重新同步
     * @param id
     */
    void reSyncForAdmin(Long id);

    IPage<ZyzRequirement> pageForApi(ZyzRequirementParams params);

    /**
     * 根据需求ID分页查询关联资源
     *
     * @param params 分页查询参数
     * @return 资源分页列表
     */
    IPage<ResourceByRequirementDto> pageResourcesByRequirementId(ResourceRequirementParams params);
}
