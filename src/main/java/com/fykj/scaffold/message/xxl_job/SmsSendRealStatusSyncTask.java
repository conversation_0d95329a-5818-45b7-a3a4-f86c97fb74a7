package com.fykj.scaffold.message.xxl_job;

import com.fykj.scaffold.message.service.IMsgRecordRealSendStatusService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 短信真实发送状态同步任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SmsSendRealStatusSyncTask {

    @Autowired
    private IMsgRecordRealSendStatusService msgRealSendStatusService;

    @XxlJob("Msg_Send_Real_Status_Sync_Handler")
    public void realSendStatusSyncJobHandler() {
        XxlJobHelper.log("Msg_Send_Real_Status_Sync_Handler-Job, START!");
        // 每 5 分钟调用同步短信发送状态接口，一次拿1000个，接口两次不会拿到同一条数据
        msgRealSendStatusService.syncRealSendStatus();
        XxlJobHelper.log("Msg_Send_Real_Status_Sync_Handler-Job, END!");
    }
}
