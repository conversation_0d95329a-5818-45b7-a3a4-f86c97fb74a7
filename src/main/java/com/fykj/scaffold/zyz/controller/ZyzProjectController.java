package com.fykj.scaffold.zyz.controller;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.entity.ZyzProject;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectParams;
import com.fykj.scaffold.zyz.service.IZyzProjectService;
import com.fykj.scaffold.zyz.util.WordUtil;
import constants.Mark;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static constants.Mark.COMMA;

/**
 * 项目表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/admin/project")
@Api(tags = "项目接口")
public class ZyzProjectController extends BaseController<IZyzProjectService, ZyzProject, ZyzProjectParams> {

    @AuditLog("保存项目草稿")
    @ApiOperation("保存项目草稿")
    @PostMapping("/saveProject")
    public Result saveProject(@RequestBody ZyzProject entity) {
        baseService.saveProject(entity);
        return OK;
    }

    @AuditLog("项目-删除")
    @Override
    public Result removeByIds(String ids) {
        return super.removeByIds(ids);
    }

    @AuditLog("修改项目草稿")
    @ApiOperation("修改项目草稿")
    @PostMapping("/updateProject")
    public Result updateProject(@RequestBody ZyzProject entity) {
        baseService.updateProject(entity);
        return OK;
    }


    @ApiOperation(value = "获取项目通过id")
    @GetMapping(value = "/getProjectById")
    public Result getProjectById(Long id) {
        ZyzProject res = baseService.getProjectById(id);
        return new JsonResult<>(res);
    }

    @ApiOperation("获取项目--分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzProject>> getPages(@RequestBody ZyzProjectParams params) {
        IPage<ZyzProject> iPage = baseService.getPages(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @AuditLog("草稿提交")
    @ApiOperation("提交")
    @GetMapping("/renderById")
    public Result renderById(Long id) {
        if (!baseService.render(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("保存项目草稿并提交")
    @ApiOperation("提交")
    @PostMapping("/renderByProject")
    public Result renderByResource(@RequestBody ZyzProject entity) {
        if (!baseService.renderByProject(entity)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("修改项目草稿并提交")
    @ApiOperation("提交")
    @PostMapping("/renderByUpdateProject")
    public Result renderByUpdateProject(@RequestBody ZyzProject entity) {
        if (!baseService.renderByUpdateProject(entity)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }


    @AuditLog("项目审核")
    @ApiOperation("项目审核")
    @GetMapping("/audit")
    public Result audit(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.audit(idList, status, remark);
        return OK;
    }

    /**
     * 员工Excel模板下载
     */
    @ApiOperation("月报模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/docxTemplate/project-report.docx");
    }


    @AuditLog("上传月报")
    @ApiOperation("导入")
    @PostMapping(value = "/importReport")
    public Result importReport(@RequestBody ZyzProject entity) {
        baseService.importReport(entity);
        return OK;
    }


    @AuditLog("撤回")
    @ApiOperation("撤回")
    @GetMapping("/recall")
    public Result recall(Long id, Boolean isFinish) {
        baseService.recall(id, isFinish);
        return OK;
    }

    @AuditLog("结项总结")
    @ApiOperation("结项总结")
    @PostMapping("/finishSave")
    public Result finishSave(@RequestBody ZyzProject entity) {
        baseService.finishSave(entity);
        return OK;
    }

    @AuditLog("项目结项审核")
    @ApiOperation("项目结项审核")
    @GetMapping("/finishAudit")
    public Result finishAudit(@RequestParam String id, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(id.split(COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.finishAudit(idList, status, remark);
        return OK;
    }

    @AuditLog("项目执行")
    @ApiOperation("项目执行")
    @PostMapping("/execute")
    public Result execute(@RequestBody ProjectExecuteDto dto) {
        baseService.execute(dto);
        return OK;
    }

    @AuditLog("项目申请书下载")
    @ApiOperation("项目申请书下载")
    @GetMapping("/projectApplicationForm")
    public void projectApplicationForm(Long projectId) {
        //模板文件的路径templatePath
        String templatePath = "data/docxTemplate/project_form.docx";
        //导出word
        ProjectFormDto formData = baseService.getProjectFormData(projectId);
        WordUtil.exportWord(templatePath, "项目申请书.docx", BeanUtil.beanToMap(formData));
    }


    @AuditLog("导出")
    @PostMapping("/exportProject")
    @ApiOperation("导出公益伙伴参与计划")
    public void exportProject(@RequestBody ZyzProjectParams params) {
        List<ProjectExportDto> list = baseService.getList(params);
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "project-export.xlsx", ProjectExportDto.class);
    }

    @AuditLog("项目中止")
    @ApiOperation("项目中止")
    @GetMapping("/stop")
    public Result stop(@RequestParam String ids, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.stop(idList, remark);
        return OK;
    }

    @AuditLog("对接启动审核")
    @ApiOperation("对接启动审核")
    @GetMapping("/dockingAuditStart")
    public Result dockingAuditStart(Long id) {
        baseService.dockingAuditStart(id);
        return OK;
    }

    @AuditLog("对接审核撤回")
    @ApiOperation("对接审核撤回")
    @GetMapping("/dockingAuditRecall")
    public Result dockingAuditRecall(Long id) {
        baseService.dockingAuditRecall(id);
        return OK;
    }

    @AuditLog("项目对接审核")
    @ApiOperation("项目对接审核")
    @GetMapping("/dockingAudit")
    public Result dockingAudit(@RequestParam String id, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(id.split(COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.dockingAudit(idList, status, remark);
        return OK;
    }

    @ApiOperation("批量下载")
    @GetMapping("downloadResultFile")
    public void downloadResultFile(HttpServletRequest request, HttpServletResponse response, String ids, Integer type) {
        baseService.downloadResultFile(request, response, ids, type);
    }

    @ApiOperation("项目报表管理查询")
    @PostMapping("/getProjectSumReport")
    public JsonResult<Map<String, List<ProjectSumReportDto>>> getProjectSumReport(@RequestBody ZyzProjectParams params) {
        Map<String, List<ProjectSumReportDto>> map = baseService.getProjectSumReport(params);
        return new JsonResult<>(map);
    }

    @AuditLog("项目报表管理导出")
    @PostMapping("/projectSumReportExport")
    @ApiOperation("项目报表管理导出")
    public void getProjectSumReportExport(@RequestBody ZyzProjectParams params) {
        List<ProjectSumReportDto> list = new ArrayList<>();
        Map<String, List<ProjectSumReportDto>> map = baseService.getProjectSumReport(params);
        for (Map.Entry<String, List<ProjectSumReportDto>> entry : map.entrySet()) {
            //添加类别行
            ProjectSumReportDto newItem = new ProjectSumReportDto();
            newItem.setProjectName(entry.getKey());
            list.add(newItem);
            //添加head行
            ProjectSumReportDto headItem = new ProjectSumReportDto();
            headItem.setProjectName("项目名称");
            headItem.setProjectSummary("项目概况");
            headItem.setFundsRequiredDesc("项目需求");
            headItem.setApplyUnitName("申报单位");
            headItem.setName("实施团队");
            headItem.setContactName("联系方式");
            headItem.setAmount("对接金额");
            headItem.setCorpName("对接企业");
            headItem.setServices("团队服务内容");
            headItem.setIntroduction("团队介绍");
            headItem.setFounded("成立时间/\n" + "是否民政局注册");
            headItem.setProjectCode("项目编号");
            headItem.setAuditStatusText("项目状态");
            list.add(headItem);
            list.addAll(entry.getValue());
            //添加空行
            ProjectSumReportDto emptyItem = new ProjectSumReportDto();
            list.add(emptyItem);
        }
        ExcelUtil.fillExcel(list, "project-report-manager-export.xlsx", ProjectSumReportDto.class);
    }

    @AuditLog("导出")
    @PostMapping("/exportProjectNew")
    @ApiOperation("导出公益伙伴参与计划")
    public void exportProjectNew(@RequestBody ZyzProjectParams params) {
        List<ProjExportDto> list = baseService.getListForExport(params);
        ExcelUtil.fillExcel(list, "proj-export.xlsx", ProjExportDto.class);
    }


    @AuditLog("修改前端是否展示")
    @ApiOperation("修改前端是否展示")
    @GetMapping("/displayStatus")
    public Result displayStatus(@RequestParam String ids, @RequestParam Boolean status) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.displayStatus(idList, status);
        return OK;
    }

    @AuditLog("获取团队提交的项目列表")
    @ApiOperation("获取团队提交的项目列表")
    @GetMapping("/getTeamProjectList")
    public JsonResult<List<ZyzProject>> getTeamProjectList() {
        return new JsonResult<>(baseService.getTeamProjectList());
    }


}
