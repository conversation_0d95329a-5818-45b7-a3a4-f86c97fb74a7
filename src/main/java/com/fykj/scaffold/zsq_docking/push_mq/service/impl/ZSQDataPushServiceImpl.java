package com.fykj.scaffold.zsq_docking.push_mq.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.zsq_docking.push_mq.aop.DockingZSQNoticeLog;
import com.fykj.scaffold.zsq_docking.push_mq.event.*;
import com.fykj.scaffold.zsq_docking.push_mq.service.IZSQDataPushService;
import com.fykj.scaffold.zsq_docking.utils.ZSQHttpRequestUtils;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.util.StringUtils;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import static cn.hutool.core.text.StrPool.LF;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.ZSQ_API_RES_OBJ;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.ZSQ_API_RES_SUCCESS;

/**
 * 知社区数据推送服务接口实现
 *
 * @Author：yangxu
 * @Date：2025/4/21 16:29
 */
@Slf4j
@Service
@Transactional
public class ZSQDataPushServiceImpl implements IZSQDataPushService {

    @Autowired
    private ZSQHttpRequestUtils httpRequestUtils;

    private List<String> eventBaseValidate(NewsDockingEvent notice) {
        List<String> baseValidateFailMsgList = new ArrayList<>();
        String operation = notice.getOperation();
        if (StringUtils.isEmpty(operation)) {
            baseValidateFailMsgList.add("消息参数{{operation}}丢失！");
        }
        String zwtUserId = notice.getZwtUserId();
        if (StringUtils.isEmpty(zwtUserId)) {
            baseValidateFailMsgList.add("消息参数{{zwtUserId}}丢失！");
        }
        return baseValidateFailMsgList;
    }

    @Override
    @DockingZSQNoticeLog
    public Result newsDocking(NewsDockingEvent notice) {
        List<String> baseValidateFailMsgList = eventBaseValidate(notice);
        Long newsId = notice.getNewsId();
        if (newsId == null) {
            baseValidateFailMsgList.add("消息参数{{newsId}}丢失！");
        }
        if (!baseValidateFailMsgList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, String.join(LF, baseValidateFailMsgList));
        }
        JSONObject responseData;
        try {
            responseData = httpRequestUtils.newsDockingPush(notice);
        } catch (Exception e) {
            return Result.get(false, "新闻操作通知知社区接口调用异常，异常原因：" + e.getMessage() + "！");
        }
        if (responseData == null) {
            return Result.get(false, "新闻操作通知知社区接口响应为空！");
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        Object obj = responseData.get(ZSQ_API_RES_OBJ);
        if (success == null) {
            return obj == null ?
                    Result.get(false, "新闻操作通知知社区接口响应异常！【" + JSON.toJSONString(responseData) + "】") :
                    new JsonResult<>(ResultCode.FAIL.code(), "新闻操作通知知社区接口响应异常！【" + JSON.toJSONString(responseData) + "】", obj);
        }
        if (!success) {
            return obj == null ?
                    Result.get(false, "新闻操作通知知社区接口响应显示数据同步操作失败！【" + JSON.toJSONString(responseData) + "】") :
                    new JsonResult<>(ResultCode.FAIL.code(), "新闻操作通知知社区接口响应显示数据同步操作失败！【" + JSON.toJSONString(responseData) + "】", obj);
        }
        if (obj == null) {
            return Result.get(true);
        }
        return new JsonResult<>(obj);
    }

    @Override
    @DockingZSQNoticeLog
    public Result activityDocking(ActivityDockingEvent notice) {
        List<String> baseValidateFailMsgList = commonEventBaseValidate(notice);
        Long activityId = notice.getActId();
        if (activityId == null) {
            baseValidateFailMsgList.add("消息参数{{actId}}丢失！");
        }
        if (!baseValidateFailMsgList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, String.join(LF, baseValidateFailMsgList));
        }
        JSONObject responseData;
        try {
            responseData = httpRequestUtils.activityDockingPush(notice);
        } catch (Exception e) {
            return Result.get(false, "活动操作通知知社区接口调用异常，异常原因【" + e.getMessage() + "】！");
        }
        if (responseData == null) {
            return Result.get(false, "活动操作通知知社区接口响应为空！");
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        Object obj = responseData.getBoolean(ZSQ_API_RES_OBJ);
        if (success == null) {
            return obj == null ?
                    Result.get(false, "活动操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "活动操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (!success) {
            return obj == null ?
                    Result.get(false, "活动操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "活动操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (obj == null) {
            return Result.get(true);
        }
        return new JsonResult<>(obj);
    }

    @Override
    @DockingZSQNoticeLog
    public Result resourceDocking(ResourceDockingEvent notice) {
        List<String> baseValidateFailMsgList = commonEventBaseValidate(notice);
        Long resourceId = notice.getResourceId();
        if (resourceId == null) {
            baseValidateFailMsgList.add("消息参数{{resourceId}}丢失！");
        }
        if (!baseValidateFailMsgList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, String.join(LF, baseValidateFailMsgList));
        }
        JSONObject responseData;
        try {
            responseData = httpRequestUtils.resourceDockingPush(notice);
        } catch (Exception e) {
            return Result.get(false, "资源操作通知知社区接口调用异常，异常原因【" + e.getMessage() + "】！");
        }
        if (responseData == null) {
            return Result.get(false, "资源操作通知知社区接口响应为空！");
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        Object obj = responseData.getBoolean(ZSQ_API_RES_OBJ);
        if (success == null) {
            return obj == null ?
                    Result.get(false, "资源操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "资源操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (!success) {
            return obj == null ?
                    Result.get(false, "资源操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "资源操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (obj == null) {
            return Result.get(true);
        }
        return new JsonResult<>(obj);
    }

    @Override
    @DockingZSQNoticeLog
    public Result requirementDocking(RequirementDockingEvent notice) {
        List<String> baseValidateFailMsgList = commonEventBaseValidate(notice);
        Long requirementId = notice.getRequirementId();
        if (requirementId == null) {
            baseValidateFailMsgList.add("消息参数{{requirementId}}丢失！");
        }
        if (!baseValidateFailMsgList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, String.join(LF, baseValidateFailMsgList));
        }
        JSONObject responseData;
        try {
            responseData = httpRequestUtils.requirementDockingPush(notice);
        } catch (Exception e) {
            return Result.get(false, "需求操作通知知社区接口调用异常，异常原因【" + e.getMessage() + "】！");
        }
        if (responseData == null) {
            return Result.get(false, "需求操作通知知社区接口响应为空！");
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        Object obj = responseData.getBoolean(ZSQ_API_RES_OBJ);
        if (success == null) {
            return obj == null ?
                    Result.get(false, "需求操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "需求操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (!success) {
            return obj == null ?
                    Result.get(false, "需求操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "需求操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (obj == null) {
            return Result.get(true);
        }
        return new JsonResult<>(obj);
    }


    private <T> List<String> commonEventBaseValidate(T object) {
        List<String> baseValidateFailMsgList = new ArrayList<>();
        if (object == null) {
            baseValidateFailMsgList.add("消息对象不能为空！");
            return baseValidateFailMsgList;
        }

        validateField(object, "operation", baseValidateFailMsgList);
        validateField(object, "zwtUserId", baseValidateFailMsgList);

        return baseValidateFailMsgList;
    }

    private <T> void validateField(T object, String fieldName, List<String> errorList) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(object);

            if (value == null || (value instanceof String && StringUtils.isEmpty((String) value))) {
                errorList.add(String.format("消息参数【%s】丢失！", fieldName));
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            errorList.add(String.format("字段【%s】不存在或不可访问！", fieldName));
        }
    }

    @Override
    @DockingZSQNoticeLog
    public Result sysOrgDocking(SysOrgDockingEvent notice) {
        List<String> baseValidateFailMsgList = commonEventBaseValidate(notice);
        Long orgId = notice.getOrgId();
        if (orgId == null) {
            baseValidateFailMsgList.add("消息参数{{orgId}}丢失！");
        }
        if (!baseValidateFailMsgList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, String.join(LF, baseValidateFailMsgList));
        }
        JSONObject responseData;
        try {
            responseData = httpRequestUtils.sysOrgDockingPush(notice);
        } catch (Exception e) {
            return Result.get(false, "组织操作通知知社区接口调用异常，异常原因【" + e.getMessage() + "】！");
        }
        if (responseData == null) {
            return Result.get(false, "组织操作通知知社区接口响应为空！");
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        Object obj = responseData.getBoolean(ZSQ_API_RES_OBJ);
        if (success == null) {
            return obj == null ?
                    Result.get(false, "组织操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "组织操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (!success) {
            return obj == null ?
                    Result.get(false, "组织操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "组织操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (obj == null) {
            return Result.get(true);
        }
        return new JsonResult<>(obj);
    }

    @Override
    @DockingZSQNoticeLog
    public Result belongFieldDocking(BelongFieldDockingEvent notice) {
        List<String> baseValidateFailMsgList = commonEventBaseValidate(notice);
        if (!baseValidateFailMsgList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, String.join(LF, baseValidateFailMsgList));
        }
        JSONObject responseData;
        try {
            responseData = httpRequestUtils.belongFieldDockingPush(notice);
        } catch (Exception e) {
            return Result.get(false, "所属领域操作通知知社区接口调用异常，异常原因【" + e.getMessage() + "】！");
        }
        if (responseData == null) {
            return Result.get(false, "所属领域操作通知知社区接口响应为空！");
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        Object obj = responseData.getBoolean(ZSQ_API_RES_OBJ);
        if (success == null) {
            return obj == null ?
                    Result.get(false, "所属领域操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "所属领域操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (!success) {
            return obj == null ?
                    Result.get(false, "所属领域操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "所属领域操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (obj == null) {
            return Result.get(true);
        }
        return new JsonResult<>(obj);
    }

    @Override
    @DockingZSQNoticeLog
    public Result regionAllDocking(RegionAllDockingEvent notice) {
        List<String> baseValidateFailMsgList = commonEventBaseValidate(notice);
        if (!baseValidateFailMsgList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, String.join(LF, baseValidateFailMsgList));
        }
        JSONObject responseData;
        try {
            responseData = httpRequestUtils.regionAllDockingPush(notice);
        } catch (Exception e) {
            return Result.get(false, "活动所属组织操作通知知社区接口调用异常，异常原因【" + e.getMessage() + "】！");
        }
        if (responseData == null) {
            return Result.get(false, "活动所属组织操作通知知社区接口响应为空！");
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        Object obj = responseData.getBoolean(ZSQ_API_RES_OBJ);
        if (success == null) {
            return obj == null ?
                    Result.get(false, "活动所属组织操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "活动所属组织操作通知知社区接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (!success) {
            return obj == null ?
                    Result.get(false, "活动所属组织操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！") :
                    new JsonResult<>(ResultCode.FAIL.code(), "活动所属组织操作通知知社区接口响应显示数据同步操作失败，接口响应【" + JSON.toJSONString(responseData) + "】！", obj);
        }
        if (obj == null) {
            return Result.get(true);
        }
        return new JsonResult<>(obj);
    }

}
