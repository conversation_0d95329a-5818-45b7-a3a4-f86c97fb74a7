package com.fykj.scaffold.dynamic.service;

import com.fykj.scaffold.dynamic.domain.entity.DynamicForm;
import fykj.microservice.core.base.IBaseService;

import java.io.Serializable;

/**
 * 模板信息-服务类
 *
 * 提供动态表单的管理、详情获取、字段快照生成等服务接口。
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
public interface IDynamicFormService extends IBaseService<DynamicForm> {

    /**
     * 添加动态表单
     * @param form 表单信息，包含字段列表
     * @return 是否成功
     */
    boolean add(DynamicForm form);

    boolean edit(DynamicForm form);

    /**
     * 获取表单详情
     * @param id 表单id
     * @return 表单详情
     */
    DynamicForm detail(Serializable id);

    /**
     * 生成动态字段快照
     * @param formId 动态表单ID
     * @return 快照ID
     */
    Long generateFormFieldSnapshot(Serializable formId);

}

