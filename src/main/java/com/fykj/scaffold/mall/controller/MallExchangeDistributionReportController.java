package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.ExchangeDRExportDto;
import com.fykj.scaffold.mall.domain.entity.MallExchangeDistributionReport;
import com.fykj.scaffold.mall.domain.params.MallExchangeDistributionReportParams;
import com.fykj.scaffold.mall.service.IMallExchangeDistributionReportService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/admin/mall/exchange-distribution-report")
public class MallExchangeDistributionReportController extends BaseController<IMallExchangeDistributionReportService, MallExchangeDistributionReport, MallExchangeDistributionReportParams> {

    @ApiOperation("历年兑换分布分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallExchangeDistributionReport>> getPages(@RequestBody MallExchangeDistributionReportParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @AuditLog("导出历年兑换分布分页")
    @ApiOperation("导出")
    @PostMapping({"/export"})
    public void export(@RequestBody(required = false) MallExchangeDistributionReportParams params) {
        List<ExchangeDRExportDto> res = baseService.listForPage(params);
        ExcelUtil.writeExcel(res, "历年兑换分布列表", ExchangeDRExportDto.class);
    }
}
