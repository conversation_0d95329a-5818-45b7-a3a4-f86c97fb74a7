package com.fykj.scaffold.message.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.message.domain.dto.MsgSendReceiverImportResponseDto;
import com.fykj.scaffold.message.domain.dto.ReceiverImportDto;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.entity.MsgTmp;
import com.fykj.scaffold.message.domain.param.MsgSendParams;
import com.fykj.scaffold.message.mapper.MsgSendMapper;
import com.fykj.scaffold.message.service.IMsgRecordService;
import com.fykj.scaffold.message.service.IMsgSendService;
import com.fykj.scaffold.message.service.IMsgTmpService;
import com.fykj.scaffold.security.business.domain.entity.SysOss;
import com.fykj.scaffold.security.business.service.ISysOssService;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.support.utils.Url2MultipartFileUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.google.common.collect.Lists;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;

import static com.fykj.scaffold.message.conns.MsgCons.*;
import static constants.Mark.COMMA;

/**
 * 消息发送实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MsgSendServiceImpl extends BaseServiceImpl<MsgSendMapper, MsgSend> implements IMsgSendService {

    @Autowired
    private IMsgRecordService recordService;

    @Autowired
    private IMsgTmpService tmpService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ScheduledExecutorService newSingleThreadScheduledExecutor;

    @Autowired
    private ISysOssService ossService;

    @Override
    public boolean save(MsgSend entity) {
        checkReceiverRepeat(entity);
        entity.setStatus(MSG_SEND_STATUS_WAIT);
        return handleEntity(entity) && handleReceiverRecords(entity);
    }

    @Override
    public boolean updateById(MsgSend entity) {
        if (MSG_SEND_STATUS_ING.equals(entity.getStatus())) {
            throw new BusinessException(ResultCode.FAIL, "该推送记录正在执行，无法修改！");
        }
        Boolean receiverOverLimit = entity.getReceiverOverLimit();
        if (ObjectUtil.isNotEmpty(receiverOverLimit) && receiverOverLimit) {
            return handleEntity(entity);
        }
        checkReceiverRepeat(entity);
        handleEntity(entity);
        recordService.removeNotSendRecord(entity.getId());
        handleReceiverRecords(entity);
        return true;
    }

    private void checkReceiverRepeat(MsgSend entity) {
        List<MsgRecord> records = entity.getReceivers();
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        Set<String> mobileSet = new HashSet<>();
        records.forEach(it -> {
            String mobile = it.getReceiverMobile();
            if (mobileSet.contains(mobile)) {
                throw new BusinessException(ResultCode.FAIL, "保存失败，存在手机号码相同的接受人，请核实并去除重复！");
            }
            mobileSet.add(mobile);
        });
    }

    private boolean handleEntity(MsgSend entity) {
        String sendCodeSuffix = MSG_SEND_CODE_GENERATE_SELF_DEFINE_SUFFIX;
        if (!entity.getSelfDefine() && StringUtil.isEmpty(entity.getMsgContent())) {
            MsgTmp tmp = tmpService.getById(entity.getTmpId());
            entity.setMsgContent(tmp.getTmp());
            entity.setPageUrl(tmp.getPageUrl());
            sendCodeSuffix = tmp.getCode();
        }
        if (StringUtil.isEmpty(entity.getSendCode())) {
            entity.setSendCode(LocalDateTime.now().format(DateTimeFormatter.ofPattern(MSG_SEND_CODE_GENERATE_DATETIME_FORMAT))
                    .concat(Mark.UNDER_LINE.concat(sendCodeSuffix)));
        }
        if (ObjectUtils.isEmpty(entity.getId())) {
            return super.save(entity);
        }
        return super.updateById(entity);
    }

    private boolean handleReceiverRecords (MsgSend entity) {
        List<MsgRecord> receivers = entity.getReceivers();
        String receiverExcelFilePath = entity.getReceiverExcelFilePath();
        if (StringUtil.isNotEmpty(receiverExcelFilePath)) {
            MultipartFile file = Url2MultipartFileUtil.transfer(receiverExcelFilePath, "接收者列表.xlsx");
            List<ReceiverImportDto> dataList = ExcelUtil.readExcel(file, ReceiverImportDto.class, 0, 2);
            receivers.addAll(handleImportData(dataList, entity.getSiteUse()));
        }
        if (CollectionUtils.isEmpty(receivers)) {
            return true;
        }
        List<MsgRecord> records = new ArrayList<>();
        receivers.forEach(it -> {
            String sendType = entity.getSendType();
//            if (StringUtil.isNotEmpty(it.getId())) {
//                sendType = it.getSendType();
//            }
            recordService.handleEverySendTypeData(sendType, it, entity, records);
        });
        return recordService.saveBatch(records);
    }


    @Override
    public boolean removeByIds(Collection<?> idList) {
        idList.forEach(it -> {
            if (!MSG_SEND_STATUS_WAIT.equals(super.getById((Long)it).getStatus())) {
                throw new BusinessException(ResultCode.FAIL, idList.size() == 1 ? "该推送记录正在执行或执行完，无法删除！" : "存在推送记录正在执行或执行完，批量删除失败！");
            }
        });
        idList.forEach(it -> recordService.removeNotSendRecord((Long)it));
        return super.removeByIds(idList);
    }

    @Override
    public MsgSend getById(Serializable id) {
        MsgSend result = super.getById(id);
        result.setReceiverOverLimit(Boolean.FALSE);
        long count = recordService.lambdaQuery().eq(MsgRecord::getSendId, id)
                .and(item->item.eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_WAIT)
                        .or()
                        .eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_FAIL)).count();
        if (count > 500 || (MSG_PUSH_TYPE_SMS.equals(result.getSendType()) && count > 200)) {
            result.setReceiverOverLimit(Boolean.TRUE);
            return result;
        }
        List<MsgRecord> records = recordService.lambdaQuery().eq(MsgRecord::getSendId, id)
                        .and(item->item.eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_WAIT)
                        .or()
                        .eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_FAIL)).list();
        if (MSG_PUSH_TYPE_SMS.equals(result.getSendType())) {
            result.setReceivers(records);
            return result;
        }
        Map<String, MsgRecord> recordsDistinct = new HashMap<>();
        records.forEach(it -> {
            String mobile = it.getReceiverMobile();
            handleMsgRecord(it);
            if (!recordsDistinct.containsKey(mobile)) {
                recordsDistinct.put(mobile, it);
                return;
            }
            recordsDistinct.get(mobile).setSendType(recordsDistinct.get(mobile).getSendType().concat(COMMA).concat(it.getSendType()));
        });
        records = Lists.newArrayList(recordsDistinct.values());
        if (records.size() > 200) {
            result.setReceiverOverLimit(Boolean.TRUE);
            return result;
        }
        result.setReceivers(records);
        return result;
    }

    private void handleMsgRecord(MsgRecord record) {
        Long receiver = record.getReceiver();
        if (ObjectUtil.isEmpty(receiver)) {
            return;
        }
        ZyzVolunteer volunteer = volunteerService.getByPhone(record.getReceiverMobile());
        if (ObjectUtil.isEmpty(volunteer)) {
            record.setReceiver(null);
            record.setReceiverVolunteerName("未找到志愿者！");
            return;
        }
        record.setReceiver(volunteer.getId());
        record.setReceiverVolunteerName(volunteer.getName());
    }

    @Override
    public IPage<MsgSend> pageForMsgSend(MsgSendParams params) {
        if (ObjectUtils.isEmpty(params)) {
            params = new MsgSendParams();
        }
        Page<MsgSend> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        IPage<MsgSend> result= baseMapper.pageForMsgSend(page, params);
        List<Long> sendIds = result.getRecords().stream().map(MsgSend::getId).collect(Collectors.toList());
        List<MsgRecord> totalSend = baseMapper.totalSend(sendIds);
        for(MsgSend it : result.getRecords()) {
            List<MsgRecord> exist = totalSend.stream().filter(im -> im.getSendId().equals(it.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(exist)) {
                it.setTotal(exist.size());
                it.setSuccess((int)exist.stream().filter(im -> MSG_PUSH_STATUS_SUCCESS.equals(im.getSendStatus())).count());
                it.setFail((int)exist.stream().filter(im -> MSG_PUSH_STATUS_FAIL.equals(im.getSendStatus())).count());
            }
        }
        DictTransUtil.trans(result.getRecords());
        return result;
    }

    @Override
    public Boolean sendMessage(Long sendId) {
        List<MsgRecord> records = recordService.lambdaQuery()
                        .eq(MsgRecord::getSendId, sendId)
                        .and(item->item.eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_WAIT)
                        .or()
                        .eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_FAIL)).list();
        if (CollectionUtils.isEmpty(records)) {
            throw new BusinessException(ResultCode.FAIL, "该推送批次未查询到接收者信息！");
        }
        lambdaUpdate().eq(MsgSend::getId, sendId).set(MsgSend::getSendTime, LocalDateTime.now()).set(MsgSend::getStatus, MSG_SEND_STATUS_ING).update();
        recordService.lambdaUpdate().eq(MsgRecord::getSendId, sendId).eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_FAIL).set(MsgRecord::getSendStatus, MSG_PUSH_STATUS_WAIT).update();
        newSingleThreadScheduledExecutor.execute(()-> {
//            try {
//                Thread.sleep(30000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
            recordService.sendMessage(sendId);
        });
        return true;
    }

    @Override
    public Result excelImport(MultipartFile excel, Boolean siteUse, Long sendId) {
        SysOss oss;
        try {
            oss = ossService.upload(excel, OssCons.OSS_LOCAL, false, null, null);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "Excel上传失败！");
        }
        List<ReceiverImportDto> dataList = ExcelUtil.readExcel(excel, ReceiverImportDto.class, 0, 2);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件");
        }
        Set<String> mobileSet = ObjectUtil.isEmpty(sendId) ? new HashSet<>() : recordService.lambdaQuery().eq(MsgRecord::getSendId, sendId).list().stream().map(MsgRecord::getReceiverMobile).collect(Collectors.toSet());
        dataList.forEach(it -> {
            String mobile = it.getMobile();
            if (mobileSet.contains(mobile)) {
                throw new BusinessException(ResultCode.FAIL, "导入失败，表中手机号码存在相同或跟同一批次已维护数据相同，请先去除重复！");
            }
            mobileSet.add(mobile);
        });
        List<MsgRecord> receiverList = handleImportData(dataList, siteUse);
        MsgSendReceiverImportResponseDto result = new MsgSendReceiverImportResponseDto();
        result.setOssObj(oss);
        boolean overLimit = receiverList.size() > 200;
        result.setReceiverOverLimit(overLimit);
        result.setReceiverList(overLimit ? null : receiverList);
        return new JsonResult<>(ResultCode.OK.code(), "成功上传" + receiverList.size() + "条接收者信息！", result);
    }

    private List<MsgRecord> handleImportData(List<ReceiverImportDto> dataList, Boolean siteUse) {
        List<MsgRecord> result = new ArrayList<>();
        dataList.forEach(it -> {
            MsgRecord record = new MsgRecord();
            record.setReceiverName(it.getName());
            String mobile = StrUtil.replaceChars(it.getMobile(), IMPORT_REPLACE_EMPTY_STR_CHAR_ARRAY, Strings.EMPTY);
            if (StringUtil.isEmpty(mobile)) {
                result.add(record);
                return;
            }
            record.setReceiverMobile(mobile);
            if (siteUse) {
                ZyzVolunteer volunteer;
                try {
                    volunteer = volunteerService.getByPhone(record.getReceiverMobile());
                    record.setReceiver(volunteer.getId());
                    record.setReceiverVolunteerName(volunteer.getName());
                } catch (Exception e) {
                    record.setReceiver(null);
                    record.setReceiverVolunteerName(e.getMessage());
                }
            }
            result.add(record);
        });
        return result;
    }

    @Override
    public void receiverMaintainSubmit(String filePath, Long sendId) {
        List<MsgRecord> failSendRecords = recordService.lambdaQuery().eq(MsgRecord::getSendId, sendId).eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_FAIL).list();
        failSendRecords.forEach(it -> {
            it.setStatus(Boolean.FALSE).setSendStatus(MSG_PUSH_STATUS_WAIT).setSender((Long) Oauth2Util.getUserId()).setSendFailReason(null).setSendTime(null);
        });
        recordService.updateBatchById(failSendRecords);
        if (StringUtil.isEmpty(filePath)) {
            return;
        }
        MsgSend send = super.getById(sendId);
        MultipartFile file = Url2MultipartFileUtil.transfer(filePath, "接收者列表.xlsx");
        List<ReceiverImportDto> dataList = ExcelUtil.readExcel(file, ReceiverImportDto.class, 0, 2);
        List<MsgRecord> records = new ArrayList<>();
        handleImportData(dataList, send.getSendType().contains(MSG_PUSH_TYPE_SITE)).forEach(it -> {
            String sendType = send.getSendType();
//            if (StringUtil.isNotEmpty(it.getId())) {
//                sendType = it.getSendType();
//            }
            recordService.handleEverySendTypeData(sendType, it, send, records);
        });
        recordService.saveBatch(records);
    }
}
