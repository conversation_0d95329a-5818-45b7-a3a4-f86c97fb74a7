package com.fykj.scaffold.zyz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.domain.entity.SysOss;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.ISysOssService;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ScheduleDetailImportDto;
import com.fykj.scaffold.zyz.domain.dto.ScheduleImportResponseDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportAllDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleParams;
import com.fykj.scaffold.zyz.mapper.ZyzActivityScheduleDetailMapper;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleDetailService;
import com.fykj.scaffold.zyz.util.BeanUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.LocalDateTimeUtil;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 阵地计划详情
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzActivityScheduleDetailServiceImpl extends BaseServiceImpl<ZyzActivityScheduleDetailMapper, ZyzActivityScheduleDetail> implements IZyzActivityScheduleDetailService {

    @Autowired
    private ISysOssService ossService;

    @Autowired
    private IDictService dictService;

    @Override
    public List<ZyzActivityScheduleDetail> listForScheduleDetail(Long scheduleId) {
        return baseMapper.listForScheduleDetail(scheduleId);
    }

    @Override
    public List<ZyzActivityScheduleDetail> listForScheduleDetailFrontend(Long scheduleId) {
        return baseMapper.listForScheduleDetailFrontend(scheduleId);
    }

    @Override
    public List<ZyzActivityScheduleDetailExportDto> listForScheduleDetailExport(ZyzActivityScheduleParams params) {
        return baseMapper.listForScheduleDetailExport(params);
    }

    @Override
    public List<ZyzActivityScheduleDetailExportAllDto> listForScheduleDetailExportAll(ZyzActivityScheduleParams params) {
        return baseMapper.listForScheduleDetailExportAll(params);
    }

    @Override
    public Result excelImport(MultipartFile excel, Long scheduleId) {
        SysOss oss;
        try {
            oss = ossService.upload(excel, OssCons.OSS_LOCAL, false, null, null);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "Excel上传失败！");
        }
        List<ScheduleDetailImportDto> dataList = ExcelUtil.readExcel(excel, ScheduleDetailImportDto.class, 0, 2).stream().distinct().collect(Collectors.toList());
        if(CollUtil.isEmpty(dataList) || Objects.isNull(dataList.get(0)) || Objects.isNull(dataList.get(0).getName())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "无法从Excel解析出数据！");
        }
        if (dataList.get(0).getName().contains("示例数据")) {
            dataList.remove(0);
        }
        List<ZyzActivityScheduleDetail> detailList = handleImportData(dataList, scheduleId);
        ScheduleImportResponseDto result = new ScheduleImportResponseDto();
        result.setOssObj(oss);
        boolean overLimit = detailList.size() > 200;
        result.setOverLimit(overLimit);
        result.setDetailList(overLimit ? null : detailList);
        return new JsonResult<>(ResultCode.OK.code(), "成功上传" + detailList.size() + "条接收者信息！", result);
    }

    private List<ZyzActivityScheduleDetail> handleImportData(List<ScheduleDetailImportDto> dataList, Long scheduleId) {
        List<ZyzActivityScheduleDetail> result = new ArrayList<>();
        List<ZyzActivityScheduleDetail> dbList = lambdaQuery().eq(ZyzActivityScheduleDetail::getScheduleId, scheduleId).list();
        String[] dictTypeArray = new String[]{ZyzCons.SCHEDULE_TARGETS_TYPE, ZyzCons.SCHEDULE_ACTIVITY_TYPE};
        Map<String, List<Dict>> dictMap = dictService.getDictMap(dictTypeArray);
        dataList.forEach(it -> {
            ZyzActivityScheduleDetail detailRecord = new ZyzActivityScheduleDetail();
            BeanUtil.copyPropertiesIgnoreNull(it, detailRecord);
            detailRecord.setStartTime(convertDateTime(it.getStartTime()));
            detailRecord.setEndTime(convertDateTime(it.getEndTime()));
            detailRecord.setTargets(getDictValue(dictMap, it.getTargets(), ZyzCons.SCHEDULE_TARGETS_TYPE));
            detailRecord.setActivityType(getDictValue(dictMap, it.getActivityType(), ZyzCons.SCHEDULE_ACTIVITY_TYPE));
            ZyzActivityScheduleDetail dbSameItem = dbList.stream().filter(x -> x.getName().equals(detailRecord.getName())
                    && x.getStartTime().equals(detailRecord.getStartTime())
                    && x.getEndTime().equals(detailRecord.getEndTime())
                    && x.getActivityType().equals(detailRecord.getActivityType())
                    && x.getSubjects().equals(detailRecord.getSubjects())
                    && x.getTargets().equals(detailRecord.getTargets())
                    && x.getLinkMan().equals(detailRecord.getLinkMan())
                    && x.getLinkPhone().equals(detailRecord.getLinkPhone())
                    && x.getAddress().equals(detailRecord.getAddress())
            ).findAny().orElse(null);
            if (dbSameItem == null) {
                result.add(detailRecord);
            }
        });
        return result;
    }

    private String getDictValue(Map<String, List<Dict>> dictMap, String excelVal, String type) {
        if (StringUtil.isEmpty(excelVal)) {
            return "";
        }
        Dict dict = dictMap.get(type).stream().filter(it -> excelVal.equals(it.getName())).findAny().orElse(null);
        if (dict == null) {
            return "";
        }
        return dict.getCode();
    }

    private LocalDateTime convertDateTime(String datetime) {
        if (StringUtil.isEmpty(datetime)) {
            return null;
        }
        try {
            return LocalDateTimeUtil.parseStringToDateTime(datetime, "yyyy-MM-dd HH:mm:ss");
        } catch (Exception ex) {
            log.error("日期处理失败" + ex.getMessage());
        }
        return null;

    }
}
