package com.fykj.volunteer.sync.util.domain.cms;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncCms {

    /**
     * 栏目id对应的中文名称（必填，参靠枚举类）
     */
    private String categoryName;
    /**
     * 标题（必填）
     */
    private String title;
    /**
     * 关键字（必填）
     */
    private String keywords;
    /**
     * 内容（必填）
     */
    private String content;
    /**
     * 新闻logo图片（必填，图片外网url地址转换为base64编码（必须带http或者https全路径）
     */
    private String picLogoUrl;
    /**
     * 发布时间（必填，格式:2015-08-09 13:23:45）
     */
    private String inputDate;
    /**
     * 来源（必填）
     */
    private String source;
    /**
     * 发布人身份证（必填）
     */
    private String idcode;
    /**
     * 唯一标识（必填）
     */
    private String uniqueCode;


    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "cms")
    public static class Cms {
        private List<SyncCms> info;
    }

    /**
     * 目录中文
     */
    public static class CategoryType {
        /**
         * 个人风采
         */
        public static String SYNC_CATEGORYNAME_GRFC = "个人风采";
        /**
         * 活动风采
         */
        public static String SYNC_CATEGORYNAME_HDFC = "活动风采";
        /**
         * 通知公告
         */
        public static String SYNC_CATEGORYNAME_TZGG = "通知公告";
        /**
         * 志愿快讯
         */
        public static String SYNC_CATEGORYNAME_ZYKX = "志愿快讯";
        /**
         * 志愿视频
         */
        public static String SYNC_CATEGORYNAME_ZYSP = "志愿视频";
        /**
         * 组织风采
         */
        public static String SYNC_CATEGORYNAME_ZZFC = "组织风采";
    }

    public String buildXml() {
        Cms cms = new Cms();
        cms.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(cms);
    }
}
