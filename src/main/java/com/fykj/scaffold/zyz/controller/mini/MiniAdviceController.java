package com.fykj.scaffold.zyz.controller.mini;

import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.dto.ZyzSugAdviceDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzSugAdvice;
import com.fykj.scaffold.zyz.domain.params.ZyzSugAdviceParams;
import com.fykj.scaffold.zyz.service.IZyzSugAdviceService;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * 内容设置-接口控制器
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@RestController
@RequestMapping("/admin/mini/advice")
@Api(tags = "微信小程序意见建议")
public class MiniAdviceController {

    @Autowired
    private IZyzSugAdviceService adviceService;

    @ApiOperation("意见建议 - 我的建议列表")
    @PostMapping({"/advice/findMyAdviceList"})
    public JsonResult<List<ZyzSugAdvice>> findMyAdviceList(@RequestBody ZyzSugAdviceParams params) {
        return new JsonResult<>(adviceService.findMyAdviceList(params));
    }

    @ApiOperation("意见建议 - 我的建议详情")
    @GetMapping({"/advice/findAdviceDetail"})
    public Result findAdviceDetail(@RequestParam Long id) {
        ZyzSugAdvice advice = adviceService.getById(id);
        Long userId = (Long) Oauth2Util.getUserId();
        if (userId.equals(advice.getUserId())) {
            DesensitiseUtil.desensitise(advice);
            DictTransUtil.trans(advice);
            return new JsonResult<>(advice);
        }
       return new Result(ResultCode.FAIL.code(), "无权查看");
    }

    @ApiOperation("意见建议 - 我的建议新增")
    @PostMapping({"/advice/save"})
    public Result saveAdvice(@RequestBody @Validated ZyzSugAdviceDto entity) {
        ZyzSugAdvice advice = new ZyzSugAdvice();
        BeanUtils.copyProperties(entity, advice);
        advice.setUserId((Long) Oauth2Util.getUserId());
        boolean result = adviceService.save(advice);
        return result ? OK : new Result(ResultCode.FAIL);
    }

}
