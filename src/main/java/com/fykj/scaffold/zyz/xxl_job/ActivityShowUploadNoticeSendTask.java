package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.support.conns.SysMsgTmpCons;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityShowUploadNoticeSendTask {

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzActivityService activityService;


    @XxlJob("Activity_Notice_Show_Upload_Send_Tmp")
    public void activitySignNoticeSend() {
        XxlJobHelper.log("ACTIVITY_SHOW_UPLOAD_NOTICE_SendTmp-Job, START!");
        LocalDateTime timeStart = LocalDateTime.now().plusMinutes(0);
        LocalDateTime timeEnd = LocalDateTime.now().plusMinutes(10);
        actTimePeriodService.lambdaQuery().gt(ZyzActivityTimePeriod::getEndTime, timeStart).le(ZyzActivityTimePeriod::getEndTime,timeEnd).list().forEach(it -> {
            //获取报名表中的志愿者id
            ZyzActivity activity = activityService.getById(it.getActivityId());
            if (activity != null && activity.getAuditStatus().equals(ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS)) {
                List<Long> activityIds = new ArrayList<>();
                activityIds.add(activity.getId());
                sendTmpMsg.sendToActivityPublisher(SysMsgTmpCons.SMT_ACTIVITY_SHOW_UPLOAD_NOTICE, activityIds, "请点击该通知上传活动图片并提交审核");
            }
        });
        XxlJobHelper.log("ACTIVITY_SHOW_UPLOAD_NOTICE_SendTmp-Job, END!");
    }
}
