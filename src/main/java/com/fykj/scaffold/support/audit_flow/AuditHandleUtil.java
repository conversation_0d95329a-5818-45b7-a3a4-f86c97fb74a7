package com.fykj.scaffold.support.audit_flow;

import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.audit_flow.cons.FlowCons;
import com.fykj.scaffold.support.audit_flow.handler.AppointLevelNumHandler;
import com.fykj.scaffold.support.audit_flow.handler.AppointOrgHandler;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import static com.fykj.scaffold.portal_website.conns.PortalWebsiteCons.*;
import static com.fykj.scaffold.zyz.conns.ZyzCons.*;

@Component
public class AuditHandleUtil {

    @Autowired
    private AppointLevelNumHandler appointLevelNumHandler;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzTeamService teamService;
    @Autowired
    private AppointOrgHandler appointOrgHandler;

    /**
     * 需求提交时获取审核流转
     *
     * @param input
     * @return
     */
    public AuditHandleResult requirementFlow(AuditFlowInput input) {
        input.setAuditLevel(1);
        AuditFlowOutput output = appointLevelNumHandler.handle(input);
        return dealCommonResult(output, REQUIREMENT_STATUS_RAS_WAIT_AUDIT, REQUIREMENT_STATUS_RAS_REJECT, REQUIREMENT_STATUS_RAS_DOCKING);
    }

    /**
     * 注册团队审核
     *
     * @param input
     * @return
     */
    public AuditHandleResult teamFlow(AuditFlowInput input) {
        if (input.getPass() == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "无法初始化团队提交流程，请联系管理员检查");
        }
        input.setAuditLevel(3);
        AuditFlowOutput output = appointLevelNumHandler.handle(input);
        return dealCommonResult(output, TEAM_AUDIT_STATUS_CHECK, TEAM_AUDIT_STATUS_NOT_PASS, TEAM_AUDIT_STATUS_PASS);
    }

    /**
     * 活动提交审核
     *
     * @param input
     * @return
     */
    public AuditHandleResult activityFlow(AuditFlowInput input, boolean addRecord) {
        if (addRecord) {
            input.setAuditLevel(3);
        } else {
            //只有社区下的团队发布上审两级
            if (input.getTeamPublish()) {
                ZyzTeam team = teamService.getTeamById(input.getPublishTeamId(), null);
                SysOrg belongOrg = orgService.getByCode(team.getOrgCode());
                if (Cons.OrgLevel.COMMUNITY == belongOrg.getLevel()) {
                    input.setAuditLevel(2);
                } else {
                    input.setAuditLevel(1);
                }
            } else {
                input.setAuditLevel(1);
            }
        }
        AuditFlowOutput output = appointLevelNumHandler.handle(input);
        return dealCommonResult(output, ACTIVITY_STATUS_WAIT_AUDIT, ACTIVITY_STATUS_REJECT, ACTIVITY_STATUS_AUDIT_SUCCESS);
    }

    public AuditHandleResult bigActivityFlow(AuditFlowInput input) {
        input.setAppointLevel(1);
        AuditFlowOutput output = appointOrgHandler.handle(input);
        return dealCommonResult(output, ACTIVITY_STATUS_WAIT_AUDIT, ACTIVITY_STATUS_REJECT, ACTIVITY_STATUS_AUDIT_SUCCESS);
    }

    /**
     * 资源提交时获取审核流转
     *
     * @param input
     * @return
     */
    public AuditHandleResult resourceFlow(AuditFlowInput input) {
        input.setAuditLevel(1);
        AuditFlowOutput output = appointLevelNumHandler.handle(input);
        return dealCommonResult(output, RESOURCE_STATUS_WAIT_AUDIT, RESOURCE_STATUS_REJECT, RESOURCE_STATUS_AUDIT_SUCCESS);
    }

    /**
     * 新闻资讯提交时获取审核流转
     *
     * @param input
     * @return
     */
    public AuditHandleResult newsFlow(AuditFlowInput input) {
        input.setAuditLevel(1);
        //只有社区下的团队发布上审两级
        if (input.getTeamPublish()) {
            ZyzTeam team = teamService.getTeamById(input.getPublishTeamId(), null);
            SysOrg belongOrg = orgService.getByCode(team.getOrgCode());
            if (Cons.OrgLevel.COMMUNITY == belongOrg.getLevel()) {
                input.setAuditLevel(2);
            }
        }
        AuditFlowOutput output = appointLevelNumHandler.handle(input);
        //新闻的比较特别，自己重写
        if (output.getStatus() == FlowCons.State.NEXT) {
            return dealNewsWaitAudit(output);
        }
        return dealCommonResult(output, null, NAS_REFUSE, NAS_PASS);
    }


    private AuditHandleResult dealCommonResult(AuditFlowOutput output, String waitStatus, String refuseCode, String passCode) {
        AuditHandleResult result = new AuditHandleResult();
        int status = output.getStatus();
        result.setFinish(true);
        if (status == FlowCons.State.ROLLBACK) {
            result = new AuditHandleResult(refuseCode, output.getOrgCode(), true);
        }
        if (status == FlowCons.State.FINISH) {
            result = new AuditHandleResult(passCode, output.getOrgCode(), true);
        }
        if (status == FlowCons.State.NEXT) {
            String auditOrgCode = output.getOrgCode();
            if (StringUtil.isEmpty(auditOrgCode)) {
                throw new BusinessException(ResultCode.SERVICE_FAIL, "未获取到下一级审核组织！");
            }
            result = new AuditHandleResult(waitStatus, output.getOrgCode(), false);
        }
        return result;
    }

    private AuditHandleResult dealNewsWaitAudit(AuditFlowOutput output) {
        AuditHandleResult result = new AuditHandleResult();
        String auditOrgCode = output.getOrgCode();
        if (StringUtil.isEmpty(auditOrgCode)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "未获取到下一级审核组织！");
        }
        Integer level = orgService.getOrgLevel(auditOrgCode);
        result.setOrgCode(auditOrgCode);
        result.setFinish(false);
        if (level == Cons.OrgLevel.ASSOCIATION) {
            result.setStatus(NAS_ASSOCIATION_WAIT_CHECK);
        }
        if (level == Cons.OrgLevel.SUB_ASSOCIATION) {
            result.setStatus(NAS_SUB_ASSOCIATION_WAIT_CHECK);
        }
        if (level == Cons.OrgLevel.COMMUNITY) {
            result.setStatus(NAS_COMMUNITY_WAIT_CHECK);
        }
        return result;
    }
    public AuditHandleResult activityShowFlow(AuditFlowInput input) {
        //只有社区下的团队发布上审两级
        input.setAuditLevel(calculateLevel(input));
        AuditFlowOutput output = appointLevelNumHandler.handle(input);
        return dealCommonResult(output, ACTIVITY_SHOW_STATUS_WAIT_AUDIT, ACTIVITY_SHOW_STATUS_REJECT, ACTIVITY_SHOW_STATUS_AUDIT_SUCCESS);
    }

    public AuditHandleResult activityScheduleFlow(AuditFlowInput input) {
        //只有社区下的团队发布上审两级
        input.setAuditLevel(calculateLevel(input));
        AuditFlowOutput output = appointLevelNumHandler.handle(input);
        return dealCommonResult(output, ACTIVITY_SCHEDULE_STATUS_WAIT_AUDIT, ACTIVITY_SCHEDULE_STATUS_REJECT, ACTIVITY_SCHEDULE_STATUS_AUDIT_SUCCESS);
    }

    private int calculateLevel(AuditFlowInput input) {
        int level = 1;
        if (input.getTeamPublish()) {
            ZyzTeam team = teamService.getTeamById(input.getPublishTeamId(), null);
            SysOrg belongOrg = orgService.getByCode(team.getOrgCode());
            if (Cons.OrgLevel.COMMUNITY == belongOrg.getLevel()) {
                level = 2;
            }
        }
        return level;
    }
}
