<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.security.business.mapper.RoleMapper">

    <select id="export" resultType="com.fykj.scaffold.security.business.mapper.RoleMapper">
        SELECT
        `code`,
        `name`,
        `sequence`,
        (CASE `status`
        WHEN 1 THEN '启用'
        ELSE '未启用' END
        ) AS statusName,
        create_date as createTime
        FROM
        sys_role
        WHERE is_deleted = 0
        <trim prefix="AND" prefixOverrides="AND | OR">
            <if test="params.name != null and params.name != ''">
                AND (
                name like CONCAT('%', CONCAT(#{params.name}, '%'))
                OR code like CONCAT('%',  CONCAT(#{params.name}, '%'))
                )
            </if>
        </trim>
    </select>
</mapper>
