package com.fykj.scaffold.sgb_docking.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.sgb_docking.domain.dto.ServiceTypeResponse;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbServiceType;
import com.fykj.scaffold.sgb_docking.mapper.SgbServiceTypeMapper;
import com.fykj.scaffold.sgb_docking.service.ISgbServiceTypeService;
import com.fykj.scaffold.sgb_docking.util.SgbApiUtil;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 苏州志愿者平台服务领域服务实现
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SgbServiceTypeServiceImpl extends BaseServiceImpl<SgbServiceTypeMapper, SgbServiceType>
        implements ISgbServiceTypeService {

    @Override
    public SgbServiceType getByTypeId(String typeId) {
        return lambdaQuery().eq(SgbServiceType::getTypeId, typeId).one();
    }

    @Override
    public void syncServiceType() {
        try {
            // 构造请求参数
            JSONObject params = new JSONObject();
            String jsonParams = params.toJSONString();
            
            // 调用API接口获取服务领域信息
            String response = SgbApiUtil.sendRequest("getSgbServiceType", jsonParams);
            log.info("查询服务领域信息响应: {}", response);
            
            // 解析响应数据
            ServiceTypeResponse serviceTypeResponse = JSON.parseObject(response, ServiceTypeResponse.class);
            if (serviceTypeResponse == null || CollectionUtil.isEmpty(serviceTypeResponse.getInfo())) {
                log.error("查询服务领域信息响应数据为空");
                throw new BusinessException(ResultCode.SERVICE_FAIL, "查询服务领域信息响应数据为空");
            }
            
            // 转换为实体对象列表
            List<SgbServiceType> serviceTypes = serviceTypeResponse.getInfo().stream()
                .filter(it -> "SUCCESS".equals(it.getCode()))
                .map(it -> {
                    SgbServiceType serviceType = new SgbServiceType();
                    serviceType.setTypeId(it.getTypeId());
                    serviceType.setTypeName(it.getTypeName());
                    serviceType.setFatherId(it.getFatherId());
                    serviceType.setNodePath(it.getNodePath());
                    return serviceType;
                })
                .collect(Collectors.toList());
                
            if (CollectionUtil.isEmpty(serviceTypes)) {
                log.info("没有服务领域信息需要同步");
                return;
            }
            
            // 先清空数据库中的数据，再保存新数据
            baseMapper.clearAll();
            saveBatch(serviceTypes);
            
            log.info("同步服务领域信息成功，共同步{}条数据", serviceTypes.size());
        } catch (Exception e) {
            log.error("同步服务领域信息失败", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "同步服务领域信息失败");
        }
    }

    @Override
    public List<SgbServiceType> getServiceTypeTree() {
        // 获取所有服务领域数据
        List<SgbServiceType> allServiceTypes = list();
        if (CollectionUtil.isEmpty(allServiceTypes)) {
            return new ArrayList<>();
        }

        // 找出根节点（fatherId为null或"0"的节点）
        List<SgbServiceType> rootNodes = allServiceTypes.stream()
                .filter(serviceType -> StringUtil.isEmpty(serviceType.getFatherId())
                        || "0".equals(serviceType.getFatherId()))
                .collect(Collectors.toList());

        // 构建父子关系映射
        Map<String, List<SgbServiceType>> parentChildrenMap = allServiceTypes.stream()
                .filter(serviceType -> StringUtil.isNotEmpty(serviceType.getFatherId())
                        && !"0".equals(serviceType.getFatherId()))
                .collect(Collectors.groupingBy(SgbServiceType::getFatherId));

        // 为每个根节点构建子树
        rootNodes.forEach(rootNode -> buildChildrenNode(rootNode, parentChildrenMap));

        return rootNodes;
    }

    @Override
    public List<CascaderExtraDto> getServiceTypeCascaderTree() {
        // 复用现有的树结构构建逻辑
        List<SgbServiceType> serviceTypeTree = getServiceTypeTree();

        // 转换为 CascaderExtraDto 格式
        return serviceTypeTree.stream()
                .map(this::convertToCascaderDto)
                .collect(Collectors.toList());
    }

    /**
     * 将 SgbServiceType 转换为 CascaderExtraDto
     * @param serviceType 服务领域实体
     * @return 级联选择DTO
     */
    private CascaderExtraDto convertToCascaderDto(SgbServiceType serviceType) {
        CascaderExtraDto cascaderDto = new CascaderExtraDto();

        // 设置基本属性
        cascaderDto.setValue(serviceType.getTypeId());
        cascaderDto.setLabel(serviceType.getTypeName());

        // 递归转换子节点
        if (CollectionUtil.isNotEmpty(serviceType.getChildren())) {
            List<CascaderExtraDto> childrenDto = serviceType.getChildren().stream()
                    .map(this::convertToCascaderDto)
                    .collect(Collectors.toList());
            cascaderDto.setChildren(childrenDto);
        }

        return cascaderDto;
    }

    /**
     * 递归构建子节点
     * @param parentNode 父节点
     * @param parentChildrenMap 父子关系映射
     */
    private void buildChildrenNode(SgbServiceType parentNode, Map<String, List<SgbServiceType>> parentChildrenMap) {
        String parentTypeId = parentNode.getTypeId();
        List<SgbServiceType> children = parentChildrenMap.get(parentTypeId);
        parentNode.setChildren(children);
        if (CollectionUtil.isNotEmpty(children)) {
            children.forEach(child -> buildChildrenNode(child, parentChildrenMap));
        }
    }
} 