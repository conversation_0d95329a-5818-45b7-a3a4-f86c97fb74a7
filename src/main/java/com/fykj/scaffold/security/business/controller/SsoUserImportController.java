package com.fykj.scaffold.security.business.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.domain.dto.SsoUserImportDto;
import com.fykj.scaffold.security.business.domain.dto.ZsqUserSyncDto;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 2025/5/8
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/admin/sso/user/import")
@Api(hidden = true)
public class SsoUserImportController {

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;

    @ApiOperation("导入")
    @PostMapping(value = "/zwt")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<SsoUserImportDto> failureList = ssoUserMappingService.importSsoUser(excel, SsoCons.SsoProvider.ZWT.name());
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.writeExcel(failureList, null, SsoUserImportDto.class);
        }
    }

    @ApiOperation("同步知社区用户")
    @GetMapping(value = "/syncZsqUser")
    public void syncZsqUser(@RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) {
        List<ZsqUserSyncDto> syncList = ssoUserMappingService.syncZsqUser(startDate, endDate);
        if (CollectionUtil.isNotEmpty(syncList)) {
            ExcelUtil.writeExcel(syncList, "知社区用户同步记录", ZsqUserSyncDto.class);
        }
    }

}
