package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.entity.MsgTemplate;
import fykj.microservice.core.base.IBaseService;

import java.util.List;


/**
 * 消息模版
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-19
 */
public interface IMsgTemplateService extends IBaseService<MsgTemplate> {

    /**
     * 校验code是否重复
     * @param code
     * @param id
     * @return
     */
    Boolean validateCode(String code, Long id);

    /**
     * 系统消息模版上下架
     * @param id
     */
    void ground(Long id);

    /**
     * 获取
     * @param code
     * @return
     */
    MsgTemplate getByCode(String code);

    /**
     * 发送模版消息
     * @param code 系统配置的模版消息code
     * @param receiver 接受者列表（短信模版消息传接收者手机号列表，微信公众号消息传接收者小程序openid列表）
     * @param params 系统配置的模版消息参数，按照模版内容，消息提示的参数顺序依次传入
     */
    void sendTmpMsg(String code , List<String> receiver, Object... params);
}

