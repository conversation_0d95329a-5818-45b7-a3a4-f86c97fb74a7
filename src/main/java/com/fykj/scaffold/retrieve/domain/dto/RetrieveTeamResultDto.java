package com.fykj.scaffold.retrieve.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieve;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RetrieveTeamResultDto {

    /**
     * 找回结果code
     */
    private Integer retrieveResultCode;

    /**
     * 找回者手机号
     */
    private String phone;

    /**
     * 志愿者id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;

    /**
     * 类型
     */
    private String type;

    /**
     * 密码
     */
    private String password;

    /**
     * 团队名称
     */
    private List<ZyzTeamRetrieve> teams = new ArrayList<>();
}
