package com.fykj.scaffold.zsq_docking.cons;

/**
 * @Author：yangxu
 * @Date：2025/4/22 16:42
 * @Description：
 */
public class ZSQDockingCons {

    /**
     * 本项目新闻对接接收接口
     */
    public static final String SELF_NEWS_RECEIVE_API = "http://localhost:9090/api/zsq/receive/news/docking";


    /**
     * 本项目新闻对接接收接口
     */
    public static final String SELF_ACTIVITY_RECEIVE_API = "http://localhost:9090/api/zsq/receive/activity/docking";

    /**
     * 对接记录状态-进行中
     */
    public static final Integer DOCKING_RECORD_STATUS_PENDING = 0;

    /**
     * 对接记录状态-对接成功
     */
    public static final Integer DOCKING_RECORD_STATUS_SUCCESS = 1;

    /**
     * 对接记录状态-对接失败
     */
    public static final Integer DOCKING_RECORD_STATUS_FAIL = 2;

    /**
     * 新闻类型-新闻
     */
    public static final String NEWS_TYPE_XW = "XW_NEWS";

    /**
     * 新闻类型-公告
     */
    public static final String NEWS_TYPE_GG = "GG_NEWS";

    /**
     * MQ-TOPIC-新闻对接
     */
    public static final String TOPIC_NEWS_DOCKING = "NEWS_DOCKING";

    /**
     * MQ-TOPIC-活动对接
     */
    public static final String TOPIC_ACTIVITY_DOCKING = "ACTIVITY_DOCKING";

    /**
     * MQ-TOPIC-资源对接
     */
    public static final String TOPIC_RESOURCE_DOCKING = "RESOURCE_DOCKING";

    /**
     * MQ-TOPIC-需求对接
     */
    public static final String TOPIC_REQUIREMENT_DOCKING = "REQUIREMENT_DOCKING";

    /**
     * MQ-TOPIC-组织对接
     */
    public static final String TOPIC_SYS_ORG_DOCKING = "SYS_ORG_DOCKING";

    /**
     * MQ-TOPIC-组织对接
     */
    public static final String TOPIC_BELONG_FIELD_DOCKING = "BELONG_FIELD_DOCKING";

    /**
     * MQ-TOPIC-组织对接
     */
    public static final String TOPIC_REGION_ALL_DOCKING = "BELONG_REGION_ALL_DOCKING";

    /**
     * 对接类型-推送
     */
    public static final String DOCKING_TYPE_PUSH = "push";

    /**
     * 对接类型-接收
     */
    public static final String DOCKING_TYPE_RECEIVE = "receive";

    /**
     * 知社区接口响应数据- obj
     */
    public static final String ZSQ_API_RES_OBJ = "obj";

    /**
     * 知社区接口响应数据- success
     */
    public static final String ZSQ_API_RES_SUCCESS = "success";

    /**
     * 知社区接口响应数据- success
     */
    public static final String ZSQ_API_RES_MSG = "msg";

    /**
     * 知社区新闻/活动日志记录请求参数- notice
     */
    public static final String ZSQ_DOCKING_PUSH_PARAM_NOTICE = "notice";

    /**
     * 知社区新闻/活动日志记录请求参数- dockingRecord
     */
    public static final String ZSQ_DOCKING_RECEIVE_LOG_PARAM_DOCKING_RECORD = "dockingRecord";

    /**
     * 知社区新闻/活动日志记录请求参数- dockingInfo
     */
    public static final String ZSQ_DOCKING_RECEIVE_LOG_PARAM_DOCKING_INFO = "dockingInfo";

    /**
     * 知社区新闻/活动日志记录请求参数- noticeInfo
     */
    public static final String ZSQ_DOCKING_PUSH_LOG_PARAM_NOTICE_INFO = "noticeInfo";

    /**
     * 知社区新闻/活动日志记录请求参数- data
     */
    public static final String ZSQ_DOCKING_RECEIVE_LOG_PARAM_DATA = "data";

    /**
     * 知社区新闻对接接收处理服务
     */
    public static final String ZSQ_DOCKING_RECEIVE_DEAL_SERVICE_NEWS = "com.fykj.scaffold.zsq_docking.receive.service.impl.NewsReceiveFromZSQServiceImpl";

    /**
     * 知社区活动对接接收处理服务
     */
    public static final String ZSQ_DOCKING_RECEIVE_DEAL_SERVICE_ACTIVITY = "com.fykj.scaffold.zsq_docking.receive.service.impl.ActivityReceiveFromZSQServiceImpl";

    /**
     * 知社区新闻对接通知方法
     */
    public static final String ZSQ_NEWS_DOCKING_PUSH_NOTICE_METHOD = "newsDocking";

    /**
     * 知社区活动对接通知方法
     */
    public static final String ZSQ_ACTIVITY_DOCKING_PUSH_NOTICE_METHOD = "activityDocking";

    /**
     * 知社区需求对接方法
     */
    public static final String ZSQ_REQUIREMENT_DOCKING_PUSH_METHOD = "requirementDocking";

    /**
     * 知社区资源对接方法
     */
    public static final String ZSQ_RESOURCE_DOCKING_PUSH_METHOD = "resourceDocking";
    /**
     * 知社区所属领域对接方法
     */
    public static final String ZSQ_BELONG_FIELD_DOCKING_PUSH_METHOD = "belongFieldDocking";

    /**
     * 知社区所有组织对接方法
     */
    public static final String ZSQ_REGION_ALL_DOCKING_PUSH_METHOD = "regionAllDocking";
    /**
     * 知社区组织对接方法
     */
    public static final String ZSQ_SYS_ORG_DOCKING_PUSH_METHOD = "sysOrgAllDocking";

    /**
     * 开发环境-通过id获取知社区新闻信息接口
     */
//    public static final String ZSQ_NEWS_DETAIL_FETCH_DEV = "http://*************:9090/community_api/api/news/getZyzNewsDetail";
    public static final String ZSQ_NEWS_DETAIL_FETCH_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/news/getZyzNewsDetail";

    /**
     * 测试环境-通过id获取知社区新闻信息接口
     */
    public static final String ZSQ_NEWS_DETAIL_FETCH_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/news/getZyzNewsDetail";

    /**
     * 正式环境-通过id获取知社区新闻信息接口
     */
    public static final String ZSQ_NEWS_DETAIL_FETCH_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/news/getZyzNewsDetail";

    /**
     * 开发环境-通过id获取知社区活动信息接口
     */
    public static final String ZSQ_ACTIVITY_DETAIL_FETCH_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/getZyzActDetail";

    /**
     * 测试环境-通过id获取知社区活动信息接口
     */
    public static final String ZSQ_ACTIVITY_DETAIL_FETCH_TEST ="https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/getZyzActDetail";

    /**
     * 正式环境-通过id获取知社区活动信息接口
     */
    public static final String ZSQ_ACTIVITY_DETAIL_FETCH_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/getZyzActDetail";

    /**
     * 开发环境-知社区新闻对接接口
     */
//    public static final String ZSQ_NEWS_DOCKING_URL_DEV = "http://*************:9090/community_api/api/news/getZyzNewsNotice";
    public static final String ZSQ_NEWS_DOCKING_URL_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/news/getZyzNewsNotice";

    /**
     * 测试环境-知社区新闻对接接口
     */
    public static final String ZSQ_NEWS_DOCKING_URL_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/news/getZyzNewsNotice";

    /**
     * 正式环境-知社区新闻对接接口
     */
    public static final String ZSQ_NEWS_DOCKING_URL_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/news/getZyzNewsNotice";

    /**
     * 开发环境-知社区活动对接接口
     */
    public static final String ZSQ_ACTIVITY_DOCKING_URL_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/getZyzActNotice";

    /**
     * 测试环境-知社区活动对接接口
     */
    public static final String ZSQ_ACTIVITY_DOCKING_URL_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/getZyzActNotice";

    /**
     * 正式环境-知社区活动对接接口
     */
    public static final String ZSQ_ACTIVITY_DOCKING_URL_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/getZyzActNotice";

    /**
     * 开发环境-知社区资源对接接口
     */
    public static final String ZSQ_RESOURCE_DOCKING_URL_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzSaveActResource";

    /**
     * 测试环境-知社区资源对接接口
     */
    public static final String ZSQ_RESOURCE_DOCKING_URL_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzSaveActResource";

    /**
     * 正式环境-知社区资源对接接口
     */
    public static final String ZSQ_RESOURCE_DOCKING_URL_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzSaveActResource";

    /**
     * 开发环境-知社区需求对接接口
     */
    public static final String ZSQ_REQUIREMENT_DOCKING_URL_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzSaveActRequirement";

    /**
     * 测试环境-知社区需求对接接口
     */
    public static final String ZSQ_REQUIREMENT_DOCKING_URL_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzSaveActRequirement";

    /**
     * 正式环境-知社区需求对接接口
     */
    public static final String ZSQ_REQUIREMENT_DOCKING_URL_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzSaveActRequirement";
    /**
     * 开发环境-知社区组织对接接口
     */
    public static final String ZSQ_SYS_ORG_DOCKING_URL_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/org/getZyzOrgNotice";

    /**
     * 测试环境-知社区组织对接接口
     */
    public static final String ZSQ_SYS_ORG_DOCKING_URL_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/org/getZyzOrgNotice";

    /**
     * 正式环境-知社区组织对接接口
     */
    public static final String ZSQ_SYS_ORG_DOCKING_URL_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/org/getZyzOrgNotice";

    /**
     * 开发环境-知社区所属领域对接接口
     */
    public static final String ZSQ_BELONG_FIELD_DOCKING_URL_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzBelongField";

    /**
     * 测试环境-知社区组织对接接口
     */
    public static final String ZSQ_BELONG_FIELD_DOCKING_URL_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzBelongField";

    /**
     * 正式环境-知社区组织对接接口
     */
    public static final String ZSQ_BELONG_FIELD_DOCKING_URL_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzBelongField";

    /**
     * 开发环境-知社区所有社区对接接口
     */
    public static final String ZSQ_REGION_ALL_DOCKING_URL_DEV = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzBelongRegion";

    /**
     * 测试环境-知社区所有社区对接接口
     */
    public static final String ZSQ_REGION_ALL_DOCKING_URL_TEST = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzBelongRegion";

    /**
     * 正式环境-知社区所有社区对接接口
     */
    public static final String ZSQ_REGION_ALL_DOCKING_URL_PROD = "https://zwyyone.sipac.gov.cn/ebus/fengyuntech/zsq/zyz/act/syncZyzBelongRegion";
    /**
     * 开发环境-通过id获取知社区新闻信息接口
     */
    public static final String ZSQ_BELONG_FIELD_DICT_FETCH_DEV = "http://*************:9090/community_api/api/news/getZyzNewsDetail";


    /**
     * 新闻数据对接类型
     */
    public static class NewsDataDockingType {

        /**
         * 对接类型-新闻新增
         */
        public static final String DT_CREATE = "CREATE";

        /**
         * 对接类型-新闻修改
         */
        public static final String DT_UPDATE = "UPDATE";

        /**
         * 对接类型-新闻删除
         */
        public static final String DT_DELETE = "DELETE";

        /**
         * 对接类型-新闻上/下架
         */
        public static final String DT_ON_OFF = "ON_OFF";

        /**
         * 对接类型-新闻审核
         */
        public static final String DT_AUDIT = "AUDIT";
    }

    public static class ActivityDataDockingType {

        /**
         * 对接类型-活动新增
         */
        public static final String DT_CREATE = "CREATE";

        /**
         * 对接类型-活动修改
         */
        public static final String DT_UPDATE = "UPDATE";

        /**
         * 对接类型-活动删除
         */
        public static final String DT_DELETE = "DELETE";

        /**
         * 对接类型-活动上/下架
         */
        public static final String DT_ON_OFF = "ON_OFF";

        /**
         * 对接类型-活动审核
         */
        public static final String DT_AUDIT = "AUDIT";


        /**
         * 对接类型-撤回
         */
        public static final String DT_WITHDRAW = "WITHDRAW";
    }
}
