package com.fykj.scaffold.message.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("msg_send")
public class MsgSend extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 是否自定义
     */
    @TableField("self_define")
    @ApiModelProperty(value = "是否自定义")
    private Boolean selfDefine;

    /**
     * 模版id
     */
    @TableField("tmp_id")
    @ApiModelProperty(value = "模版id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tmpId;

    /**
     * 自定义消息
     */
    @TableField("self_define_msg")
    @ApiModelProperty(value = "自定义消息")
    private String selfDefineMsg;

    /**
     * 消息标题
     */
    @TableField("title")
    @ApiModelProperty(value = "消息标题")
    private String title;

    /**
     * 推送方式
     */
    @TableField(value = "send_type")
    @ApiModelProperty(value = "推送方式")
    @DictTrans(transTo = "sendTypeText")
    private String sendType;

    /**
     * 推送方式text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "推送方式text")
    private String sendTypeText;

    /**
     * 推送状态
     */
    @TableField("status")
    @ApiModelProperty(value = "推送状态")
    private Integer status;

    /**
     * 短信推送code
     */
    @TableField("send_code")
    @ApiModelProperty(value = "短信推送code")
    private String sendCode;

    /**
     * 推送时间
     */
    @TableField("send_time")
    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    /**
     * 接收者
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接收者")
    private List<MsgRecord> receivers = new ArrayList<>();

    /**
     * 是否使用站内信
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否使用站内信")
    private Boolean siteUse;

    /**
     * 接收者是否超出限制
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接收者是否超出限制")
    private Boolean receiverOverLimit;

    /**
     * 接收者文件路径
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接收者文件路径")
    private String receiverExcelFilePath;

    /**
     * 推送总数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "推送总数")
    private Integer total;

    /**
     * 已推送数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "已推送数")
    private Integer done;

    /**
     * 推送成功数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "推送成功数")
    private Integer success;

    /**
     * 推送失败数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "推送失败数")
    private Integer fail;

    /**
     * 消息体
     */
    @ApiModelProperty(value = "消息体")
    @TableField(exist = false)
    private String msgContent;

    /**
     * 跳转链接
     */
    @ApiModelProperty(value = "跳转链接")
    @TableField(exist = false)
    private String pageUrl;
}
