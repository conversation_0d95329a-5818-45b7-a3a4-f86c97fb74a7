package com.fykj.scaffold.support.audit_flow;


import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import result.ResultCode;
import utils.StringUtil;

public abstract class AuditFlowHandleInstance {

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzTeamService teamService;

    public abstract AuditFlowOutput handle(AuditFlowInput input);

    public SysOrg getParentOrg(Boolean isTeam, Long teamId, String orgCode) {
        // 由团队id获取挂靠组织
        if (isTeam) {
            return getOrgByTeam(teamId);
        }
        // 有组织code获取父级组织
        return getParentOrgByOrg(orgCode);
    }

    public SysOrg getOrgByTeam(Long teamId) {
        if (ObjectUtil.isEmpty(teamId)) {
            throw new BusinessException(ResultCode.FAIL, "团队id未知！");
        }
        ZyzTeam team = teamService.getById(teamId);
        if (ObjectUtil.isEmpty(team)) {
            throw new BusinessException(ResultCode.FAIL, "发布者所在团队不存在！");
        }
        String orgCode = team.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "发布者所在团队未挂靠任何组织！");
        }
        SysOrg org = orgService.getByCode(orgCode);
        if (ObjectUtil.isEmpty(org)) {
            throw new BusinessException(ResultCode.FAIL, "组织编码有误或发布者所在组织不存在！");
        }
        return org;
    }

    public SysOrg getParentOrgByOrg(String orgCode) {
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "组织编码未知！");
        }
        SysOrg org = orgService.getByCode(orgCode);
        if (ObjectUtil.isEmpty(org)) {
            throw new BusinessException(ResultCode.FAIL, "组织编码有误或发布者所在组织不存在！");
        }
        Long parentOrgId = org.getParentId();
        if (ObjectUtil.isEmpty(parentOrgId)) {
            return null;
        }
        SysOrg parentOrg = orgService.getById(parentOrgId);
        if (ObjectUtil.isEmpty(parentOrg)) {
            throw new BusinessException(ResultCode.FAIL, "父级组织id有误或父级组织不存在！");
        }
        return parentOrg;
    }

    public AuditFlowOutput teamPublishDeal(AuditFlowInput input, Integer appointLevel, Boolean oneByOne) {
        Integer level;
        AuditFlowOutput output = new AuditFlowOutput();
        SysOrg teamOrg = getOrgByTeam(input.getPublishTeamId());
        level = orgService.getOrgLevel(teamOrg);
        String orgCode = teamOrg.getCode();
        // 团队挂靠组织层级高于或等于指定审核层级时，直接提交待挂靠组织审核
        if (level <= appointLevel) {
            output.setStatus(0);
            output.setOrgCode(orgCode);
            return output;
        }
        if (oneByOne) {
            output.setStatus(0);
            output.setOrgCode(orgCode);
            return output;
        }
        // 团队挂靠组织层级低于指定审核层级时，循环获取到该挂靠组织的上级指定层级，提交待审核
        return aboveOrgAuditDeal(output, level, appointLevel, orgCode);
    }

    public AuditFlowOutput orgPublishDeal(AuditFlowInput input, Integer appointLevel, Boolean oneByOne) {
        Integer level;
        AuditFlowOutput output = new AuditFlowOutput();
        String orgCode = input.getPublishOrgCode();
        SysOrg org = orgService.getByCode(orgCode);
        if (org == null) {
            throw new BusinessException(ResultCode.FAIL, "组织编码有误或发布者所在组织不存在！");
        }
        level = orgService.getOrgLevel(org);
        // 组织层级高于或等于指定审核层级时，直接提交审核通过
        if (level <= appointLevel) {
            output.setStatus(1);
            return output;
        }
        if (oneByOne) {
            Long parentId = org.getParentId();
            SysOrg parentOrg = orgService.getById(parentId);
            if (parentOrg == null) {
                throw new BusinessException(ResultCode.FAIL, "父级组织编码有误或发布者父级所在组织不存在！");
            }
            output.setStatus(0);
            output.setOrgCode(parentOrg.getCode());
            return output;
        }
        // 组织层级低于指定审核层级时，循环获取到该组织的上级指定层级，提交待审核
        return aboveOrgAuditDeal(output, level, appointLevel, orgCode);
    }

    private AuditFlowOutput aboveOrgAuditDeal(AuditFlowOutput output, Integer level, Integer appointLevel, String orgCode) {
        int i = 0, interval = level - appointLevel;
        SysOrg appointLevelOrg = null;
        while (i++ < interval) {
            appointLevelOrg = getParentOrgByOrg(orgCode);
            if (ObjectUtil.isEmpty(appointLevelOrg)) {
                throw new BusinessException(ResultCode.FAIL, "该团队挂靠组织的上" + i + "级组织不存在！");
            }
            orgCode = appointLevelOrg.getCode();
        }
        if (appointLevelOrg == null) {
            throw new BusinessException(ResultCode.FAIL, "该团队挂靠组织的上" + interval + "级组织不存在！");
        }
        output.setStatus(0);
        output.setOrgCode(appointLevelOrg.getCode());
        return output;
    }
}
