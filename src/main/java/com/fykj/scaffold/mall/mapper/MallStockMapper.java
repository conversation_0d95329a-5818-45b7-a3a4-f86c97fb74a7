package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.entity.MallStock;
import com.fykj.scaffold.mall.domain.params.MallStockParams;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  物料信息管理Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface MallStockMapper extends BaseMapper<MallStock> {
    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    @DataFilter
    IPage<MallStock> getPage(IPage<MallStock> page, @Param("params") MallStockParams params);

}
