package com.fykj.scaffold.message.conns;

public class MsgCons {

    public static final char[] IMPORT_REPLACE_EMPTY_STR_CHAR_ARRAY = {' ', '\'', '\u00A0'} ;

    public static final String MSG_SEND_CODE_GENERATE_DATETIME_FORMAT = "yyyyMMddHHmmss";

    public static final String MSG_SEND_CODE_GENERATE_SELF_DEFINE_SUFFIX = "self_define";

    /**
     * 消息推送方式-短信推送
     */
    public static final String MSG_PUSH_TYPE_SMS = "mpt_sms";

    /**
     * 消息推送方式-站内信推送
     */
    public static final String MSG_PUSH_TYPE_SITE = "mpt_site";

    /**
     * 消息推送状态-待推送
     */
    public static final Integer MSG_PUSH_STATUS_WAIT = 0;

    /**
     * 消息推送状态-推送成功
     */
    public static final Integer MSG_PUSH_STATUS_SUCCESS = 1;

    /**
     * 消息推送状态-推送失败
     */
    public static final Integer MSG_PUSH_STATUS_FAIL = 2;

    /**
     * 消息推送统计维度-总计
     */
    public static final String MSG_PUSH_SUMMARY_DIMENSION_TOTAL = "total";

    /**
     * 消息推送统计维度-完成
     */
    public static final String MSG_PUSH_SUMMARY_DIMENSION_DONE = "done";

    /**
     * 消息推送统计维度-成功
     */
    public static final String MSG_PUSH_SUMMARY_DIMENSION_SUCCESS = "success";

    /**
     * 消息推送统计维度-失败
     */
    public static final String MSG_PUSH_SUMMARY_DIMENSION_FAIL = "fail";

    /**
     * 消息发送状态（msg_send表）-待发送
     */
    public static final Integer MSG_SEND_STATUS_WAIT = 0;

    /**
     * 消息发送状态（msg_send表）-正在发送
     */
    public static final Integer MSG_SEND_STATUS_ING = 1;

    /**
     * 消息发送状态（msg_send表）-已完成
     */
    public static final Integer MSG_SEND_STATUS_DONE = 2;

    /**
     * 消息发送 redis key前缀
     */
    public static final String MSG_SEND_REDIS_KEY_PREFIX = "MSG_SEND_";

    /**
     * 消息真实发送状态 -发送成功
     */
    public static final String MSG_REAL_SEND_STATUS_SUCCESS = "10";

    /**
     * 消息真实发送状态 -发送失败
     */
    public static final String MSG_REAL_SEND_STATUS_FAILED = "20";

    /**
     * 信息发送白名单--数据字典值
     */
    public static final  String MSG_SEND_WHITE_LIST = "MSG_SEND_WHITE_LIST";

    /**
     * 上报错误率--模板编码
     */
    public static final  String MSG_SYNC_ERROR_RATE = "sync_error_rate";
    /**
     * 测评台账上传提醒--模板编码
     */
    public static final  String MSG_EVALUATION_LEDGER = "evaluation_ledger";
}
