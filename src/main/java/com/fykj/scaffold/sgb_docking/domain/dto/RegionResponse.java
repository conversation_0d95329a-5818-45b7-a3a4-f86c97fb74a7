package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 区划查询响应实体
 */
@Data
public class RegionResponse {
    
    /**
     * 响应信息列表
     */
    private List<RegionInfo> info;
    
    /**
     * 区划信息实体
     */
    @Data
    public static class RegionInfo {
        /**
         * 响应状态码
         * SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 区划编号
         */
        private String regionCode;
        
        /**
         * 区划名称
         */
        private String regionName;
        
        /**
         * 父级区划编号
         */
        private String parentCode;
    }
} 