<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.race.mapper.PhotoMapper">

    <select id="getPage" resultType="com.fykj.scaffold.race.domain.entity.Photo">
        SELECT rp.*,
               rp.picture_list as pictureList,
               su.`name` as userName,
               su.mobile  as userPhone
        From race_photo rp
                 LEFT JOIN sys_user su on rp.user_id = su.id
        WHERE rp.is_deleted = FALSE
        <if test="param.activityId != null and param.activityId !=''">
            AND rp.activity_id = #{param.activityId}
        </if>
        <if test="param.auditStatus != null  and param.auditStatus !=''">
            and rp.audit_status = #{param.auditStatus}
        </if>
        order by rp.number
    </select>
    <select id="getMaxNumber" resultType="java.lang.Integer">
        SELECT max(number)
        FROM race_photo
        WHERE is_deleted = FALSE
    </select>
</mapper>