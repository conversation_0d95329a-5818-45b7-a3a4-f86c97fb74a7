package com.fykj.scaffold.support.utils;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import lombok.experimental.UtilityClass;
import org.springframework.util.CollectionUtils;
import utils.ClassUtil;
import utils.StringUtil;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 敏感数据脱敏工具类，暂时简单弄弄，身份证和手机号自动打码，其他的一律中间打4位
 */
@UtilityClass
public class DesensitiseUtil {

    public static <T> T desensitise(T source, String... ignoreFields) {
        if (ignoreFields == null) {
            ignoreFields = new String[]{};
        }
        if (source == null) {
            return null;
        }
        Class<?> targetClass = source.getClass();
        List<Field> formatterFields = getFields(targetClass, ignoreFields);
        doFormatter(formatterFields, source);
        return source;
    }

    public static <T> List<T> desensitise(List<T> sources, String... ignoreFields) {
        if (CollectionUtils.isEmpty(sources)) {
            return sources;
        }
        List<Field> formatterFields = getFields(sources.get(0).getClass(), ignoreFields);
        sources.forEach((target) -> doFormatter(formatterFields, target));
        return sources;
    }

    private static List<Field> getFields(Class<?> targetClass, String[] ignoreFields) {
        Field[] declaredFields = targetClass.getDeclaredFields();
        List<String> ignoreNames = Arrays.asList(ignoreFields);
        return Arrays
                .stream(declaredFields)
                .filter((field) -> field.isAnnotationPresent(Desensitise.class) && !ignoreNames.contains(field.getName()))
                .collect(Collectors.toList());
    }

    private static void doFormatter(List<Field> fields, Object target) {
        fields.forEach((it) -> {
            String value = ClassUtil.getFieldValue(target, it);
            if (StringUtil.isEmpty(value)) {
                return;
            }
            Desensitise annotation = it.getAnnotation(Desensitise.class);
            if (annotation == null) {
                return;
            }
            String castValue = value;
            //手机号
            if (PhoneUtil.isMobile(value)) {
                castValue = PhoneUtil.hideBetween(value).toString();
            }
            //身份证
            else if (IdcardUtil.isValidCard(value)) {
                castValue = IdcardUtil.hide(value, 6, 10);
            }
            ClassUtil.setFieldValue(target, it, castValue);
        });
    }
}
