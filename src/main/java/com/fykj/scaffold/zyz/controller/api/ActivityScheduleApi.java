package com.fykj.scaffold.zyz.controller.api;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ActivityApiDto;
import com.fykj.scaffold.zyz.domain.dto.TeamApiDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzResourceNearbyCountDTO;
import com.fykj.scaffold.zyz.domain.dto.ZyzResourceOrgCountDTO;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityParams;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementParams;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceParams;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import com.fykj.scaffold.zyz.service.*;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.ResultCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static com.fykj.scaffold.zyz.conns.ZyzCons.REQUIREMENT_STATUS_RAS_DOCKING;

/**
 * 阵地计划
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@RestController
@RequestMapping("/api/zyz/activity/schedule")
@Api(tags = "客户端-阵地计划")
public class ActivityScheduleApi  {

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzActivityScheduleService activityScheduleService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private IZyzRequirementService requirementService;

    @GetMapping("/getOrgTreeByCode")
    @ApiOperation("获取所有文明实践所树")
    public JsonResult<List<SysOrg>> getOrgTreeByParentId() {
        String myTopDept = Cons.TOP_DEPT_CODE;
        List<SysOrg> categories = orgService.getOrgTreeOnlyContainFiveSubAssociation(
                myTopDept, true);
        clearSensitiveInformation(categories);
        return new JsonResult<>(categories);
    }

    @ApiOperation("阵地计划-列表")
    @GetMapping({"/listForSchedule"})
    @ApiImplicitParams({
//            @ApiImplicitParam(name = "scheduleType", value = "阵地类型", required = true),
            @ApiImplicitParam(name = "scheduleTypeData", value = "阵地计划数值", example = "202504"),
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<List<ZyzActivityScheduleDetail>> pagesForSchedule(
            String scheduleType,
            @RequestParam String scheduleTypeData,
            @RequestParam String orgCode) {
        // 现在默认只查月度计划
        if (StrUtil.isBlank(scheduleType)) {
            scheduleType = ZyzCons.ACTIVITY_SCHEDULE_TYPE_MONTH;
        }
        List<ZyzActivityScheduleDetail> result = activityScheduleService
                .getDetailListByScheduleTypeAndScheduleTypeDataAndPublishOrgCode(
                        scheduleType, scheduleTypeData, orgCode);
        return new JsonResult<>(result);
    }

    @ApiOperation("开展活动--分页查询")
    @GetMapping("/pagesForActivity")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<IPage<ActivityApiDto>> pagesForActivity(
            @RequestParam String orgCode,
            BaseParams baseParams) {
        // 获取本级及以下的活动，按活动时间最新在最前面展示
        // 获取本级及以下部门code list
        IPage<ActivityApiDto> result = activityService.pageForFrontend(
                new ZyzActivityParams(baseParams)
                        .setPublishOrgCodeList(getSubOrgCodeList(orgCode)));
        DictTransUtil.trans(result.getRecords());
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("相关团队--分页查询")
    @GetMapping("/pagesForTeam")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<IPage<TeamApiDto>> pagesForTeam(
            @RequestParam String orgCode,
            BaseParams baseParams) {
        ZyzTeamParams params = new ZyzTeamParams(baseParams);
        // 发布单位
        String orgCodePrefix = Optional.ofNullable(orgService.getByCode(orgCode))
                .map(SysOrg::getCodePrefix)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "组织不存在"));
        params.setOrgCode(orgCodePrefix);
        IPage<TeamApiDto> page = teamService.pageForFrontend(params);
        DictTransUtil.trans(page.getRecords());
        DesensitiseUtil.desensitise(page.getRecords());
        return new JsonResult<>(page);
    }

    @ApiOperation("相关资源--分页查询")
    @GetMapping("/pagesForResource")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<IPage<ZyzResource>> pagesForResource(
            @RequestParam String orgCode,
            BaseParams baseParams) {
        IPage<ZyzResource> result = resourceService.pageForApi(
                new ZyzResourceParams(baseParams)
                        // 获取本级及以下部门code list
                        .setPublishOrgCodeList(getSubOrgCodeList(orgCode)));
        DictTransUtil.trans(result.getRecords());
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("相关需求--分页查询")
    @GetMapping("/pagesForRequirement")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<IPage<ZyzRequirement>> pagesForRequirement(
            @RequestParam String orgCode,
            BaseParams baseParams) {
        IPage<ZyzRequirement> result = requirementService.pageForApi(
                new ZyzRequirementParams(baseParams)
                        // 获取本级及以下部门code list
                        .setPublishOrgCodeList(getSubOrgCodeList(orgCode)));
        DictTransUtil.trans(result.getRecords());
        // 相关需求，对接状态有待对接和已对接，没有审核通过
        result.getRecords().stream()
                .filter(item -> REQUIREMENT_STATUS_RAS_DOCKING.equals(item.getStatus()))
                .forEach(item -> item.setStatusText("待对接"));
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("附近资源-资源分类统计")
    @GetMapping("/countNearbyResource")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "longitude", value = "经度"),
            @ApiImplicitParam(name = "latitude", value = "纬度"),
            @ApiImplicitParam(name = "distance", value = "距离(单位: 公里"),
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<List<ZyzResourceNearbyCountDTO>> countNearbyResource(
            @RequestParam BigDecimal longitude,
            @RequestParam BigDecimal latitude,
            @RequestParam BigDecimal distance,
            @RequestParam String orgCode) {
        List<ZyzResourceNearbyCountDTO> result = resourceService.countNearbyForApi(
                new ZyzResourceParams()
                        // 获取本级及以下部门code list
                        .setPublishOrgCodeList(getSubOrgCodeList(orgCode))
                        .setNearbyParams(longitude, latitude, distance));
        return new JsonResult<>(result);
    }

    @ApiOperation("附近资源-资源列表")
    @GetMapping("/listNearbyResource")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "longitude", value = "经度"),
            @ApiImplicitParam(name = "latitude", value = "纬度"),
            @ApiImplicitParam(name = "distance", value = "距离(单位: 公里"),
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<List<ZyzResource>> listNearbyResource(
            @RequestParam BigDecimal longitude,
            @RequestParam BigDecimal latitude,
            @RequestParam BigDecimal distance,
            @RequestParam String orgCode) {
        List<ZyzResource> result = resourceService.listNearbyForApi(
                new ZyzResourceParams()
                        // 获取本级及以下部门code list
                        .setPublishOrgCodeList(getSubOrgCodeList(orgCode))
                        .setNearbyParams(longitude, latitude, distance));
        return new JsonResult<>(result);
    }

    @ApiOperation("附近资源-站点相关统计(服务次数,场地,专家个数")
    @GetMapping("/countOrg")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<ZyzResourceOrgCountDTO> countOrgForApi(
            @RequestParam BigDecimal longitude,
            @RequestParam BigDecimal latitude,
            @RequestParam BigDecimal distance,
            @RequestParam String orgCode) {
        ZyzResourceOrgCountDTO result = resourceService.countOrgForApi(new ZyzResourceParams()
                // 获取本级及以下部门code list
                .setPublishOrgCode(orgCode)
                .setNearbyParams(longitude, latitude, distance));
        return new JsonResult<>(result);
    }

    @GetMapping("/listNearbyOrg")
    @ApiOperation("获取目标实践阵地为中心x公里内的下属阵地")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "distance", value = "距离(单位: 公里"),
            @ApiImplicitParam(name = "orgCode", value = "组织code")
    })
    public JsonResult<List<SysOrg>> listNearbyOrg(
            @RequestParam BigDecimal distance,
            @RequestParam String orgCode
    ) {
        List<SysOrg> nearbyOrgs = orgService.listNearbyOrg(orgCode, distance);
        clearSensitiveInformation(nearbyOrgs);
        return new JsonResult<>(nearbyOrgs);
    }

    private List<String> getSubOrgCodeList(String orgCode) {
        // 获取本级及以下部门code list
        return orgService.getSubOrgCodeList(orgCode, true);
    }

    private void clearSensitiveInformation(List<SysOrg> childrenList) {
        if (!CollectionUtils.isEmpty(childrenList)) {
            childrenList.forEach(item -> {
                clearSensitiveInformation(item);
                clearSensitiveInformation(item.getChildren());
            });
        }
    }

    private void clearSensitiveInformation(SysOrg item) {
        item.setLinkIdCard(null);
    }
}
