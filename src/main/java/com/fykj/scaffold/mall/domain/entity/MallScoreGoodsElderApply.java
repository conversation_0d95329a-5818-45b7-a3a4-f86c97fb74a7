package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 星光闪耀适老化改造福申请表
 *
 * <AUTHOR> @email ${email}
 * @date 2024-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_score_goods_elder_apply")
public class MallScoreGoodsElderApply extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 兑换表id
     */
    @TableField("exchange_id")
    @ApiModelProperty(value = "兑换表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品id
     */
    @TableField("goods_id")
    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long goodsId;


    /**
     * 志愿者id
     */
    @TableField("volunteer_id")
    @ApiModelProperty(value = "志愿者id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;


    /**
     * 志愿者身份证号
     */
    @TableField("volunteer_certificate_id")
    @ApiModelProperty(value = "志愿者身份证号")
    private String volunteerCertificateId;

    /**
     * 志愿者证件类型（身份证还是护照等等)
     */
    @TableField("volunteer_certificate_type")
    @DictTrans(transTo = "volunteerCertificateTypeText")
    @ApiModelProperty(value = "志愿者证件类型（身份证还是护照等等)")
    private String volunteerCertificateType;

    /**
     * 志愿者证件类型（身份证还是护照等等)
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "志愿者证件类型text（身份证还是护照等等)")
    private String volunteerCertificateTypeText;

    /**
     * 志愿者联系方式
     */
    @TableField("volunteer_phone")
    @ApiModelProperty(value = "志愿者联系方式")
    private String volunteerPhone;
    /**
     * 志愿者姓名
     */
    @TableField("volunteer_name")
    @ApiModelProperty(value = "志愿者姓名")
    private String volunteerName;


    /**
     * 志愿者户籍所在地
     */
    @TableField("volunteer_address")
    @ApiModelProperty(value = "志愿者户籍所在地")
    private String volunteerAddress;

    /**
     * 老年人姓名
     */
    @TableField("elder_name")
    @ApiModelProperty(value = "老年人姓名")
    private String elderName;

    /**
     * 老年人联系方式
     */
    @TableField("elder_phone")
    @ApiModelProperty(value = "老年人联系方式")
    private String elderPhone;

    /**
     * 老年人户籍所在地
     */
    @TableField("elder_address")
    @ApiModelProperty(value = "老年人户籍所在地")
    private String elderAddress;

    /**
     * 老年人证件号
     */
    @TableField("elder_certificate_id")
    @ApiModelProperty(value = "老年人证件号")
    private String elderCertificateId;

    /**
     * 备注
     */
    @TableField("remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;


}
