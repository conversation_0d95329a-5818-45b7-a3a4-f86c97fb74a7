package com.fykj.scaffold.sgb_docking.controller;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbRegion;
import com.fykj.scaffold.sgb_docking.domain.params.SgbRegionParams;
import com.fykj.scaffold.sgb_docking.service.ISgbRegionService;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 苏州志愿者平台区划信息控制器
 */
@Api(tags = "苏州志愿者平台-区划信息")
@RestController
@RequestMapping("/admin/sgb/region")
public class SgbRegionController extends BaseController<ISgbRegionService, SgbRegion, SgbRegionParams> {

    /**
     * 获取区划树结构
     *
     * @return 区划树列表
     */
    @ApiOperation("获取区划树结构")
    @GetMapping("/tree")
    public JsonResult<List<SgbRegion>> getRegionTree() {
        return new JsonResult<>(baseService.getRegionTree());
    }

    /**
     * 获取区划级联选择树结构
     *
     * @return 级联选择树列表
     */
    @ApiOperation("获取区划级联选择树结构")
    @GetMapping("/cascader-tree")
    public JsonResult<List<CascaderExtraDto>> getRegionCascaderTree() {
        return new JsonResult<>(baseService.getRegionCascaderTree());
    }

    /**
     * 同步区划信息
     */
    @AuditLog("手动同步区划信息")
    @ApiOperation("同步区划信息")
    @PostMapping("/sync")
    public Result sync() {
        baseService.syncRegion();
        return new Result();
    }
} 