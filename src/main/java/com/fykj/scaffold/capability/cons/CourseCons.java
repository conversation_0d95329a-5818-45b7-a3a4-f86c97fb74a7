package com.fykj.scaffold.capability.cons;

import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@UtilityClass
public class CourseCons {

    public static final BigDecimal DEFAULT_SIGN_IN_DISTANCE = new BigDecimal("500.00");

    public final static WxMaCodeLineColor WXA_CODE_LINE_COLOR = new WxMaCodeLineColor("0","0","0");

}
