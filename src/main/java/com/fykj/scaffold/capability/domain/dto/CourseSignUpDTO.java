package com.fykj.scaffold.capability.domain.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.capability.cons.CourseType;
import com.fykj.scaffold.support.utils.Desensitise;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class CourseSignUpDTO {

    @ApiModelProperty(value = "志愿者ID")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long volunteerId;

    @ApiModelProperty(value = "课程ID")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long courseId;

    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long userId;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课程类型 字典CODE:" + CourseType.DICT_CODE)
    @DictTrans(transTo = "courseTypeName")
    private String courseType;

    @ApiModelProperty("课程类型")
    private String courseTypeName;

    /**
     * 志愿者姓名
     */
    @ApiModelProperty(value = "志愿者姓名")
    private String name;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 身份证号
     */
    @Desensitise
    @ApiModelProperty(value = "身份证号")
    private String certificateId;

    /**
     * 组织架构CODE（社区层级）
     */
    @ApiModelProperty(value = "组织架构CODE（社区层级）")
    private String orgCode;

    @ApiModelProperty(value = "上级组织")
    private String orgName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 报名时间
     */
    @ApiModelProperty(value = "报名时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime signUpAt;

    @ApiModelProperty(value = "团队/企业名称")
    private String teamOrEntName;

    @ApiModelProperty(value = "职务")
    private String position;

}
