<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzProjectCompanyMapper">
    <select id="exportCompanyContribute" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectCompanyContributeDto">
        SELECT c.corp_name           AS corp_name,
               c.corp_type           AS corp_type,
               c.social_credit_code  AS social_credit_code,
               p.project_name        AS project_name,
               p.type_name           AS project_type_name,
               p.team_name           AS team_name,
               p.project_summary     AS project_summary,
               p.project_demand_type AS project_demand_type,
               d.amount              AS amount,
               d.docking_time        AS docking_time,
               p.year                AS `year`
        FROM zyz_project_docking d
                 INNER JOIN zyz_project_company c ON c.id = d.corp_id
                 INNER JOIN zyz_project p ON p.id = d.project_id
        WHERE d.is_deleted = FALSE
          AND c.is_deleted = FALSE
          AND p.is_deleted = FALSE
          <if test="year != null">
              and p.year = #{year}
          </if>
          order by d.docking_time
    </select>

    <select id="getCompanyDockingList" resultType="com.fykj.scaffold.zyz.domain.dto.CompanyDockingExportDto">
        SELECT
        c.*,
        p.year,
        p.project_code,
        p.project_name,
        p.team_name ,
        d.project_demand_type ,
        d.amount
        FROM zyz_project_docking d
        INNER JOIN zyz_project_company c ON c.id = d.corp_id
        INNER JOIN zyz_project p ON p.id = d.project_id
        WHERE d.is_deleted = FALSE
        AND c.is_deleted = FALSE
        AND p.is_deleted = FALSE
        <if test="year != null">
            AND p.year = #{year}
        </if>
        ORDER BY d.docking_time
    </select>

    <select id="bindProjectPage" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectCompanyBindPageDto">
        SELECT
            d.id as docking_id,
            c.id as company_id,
            p.project_name,
            p.year,
            p.project_code
        FROM
            zyz_project_docking d
        INNER JOIN zyz_project_company c on c.id = d.corp_id
        INNER JOIN zyz_project p on p.id = d.project_id
        where d.is_deleted = false and c.is_deleted = false and p.is_deleted = false
            and c.id = #{params.companyId}
        <if test="params.projectName != null and params.projectName != ''">
            and p.project_name like concat('%',#{params.projectName},'%')
        </if>
        order by p.year desc
    </select>

    <select id="cockpitCorpList" resultType="com.fykj.scaffold.security.business.domain.dto.IdNameDto">
        SELECT
            id as id,
            corp_name AS name
        FROM
            zyz_project_company
        WHERE
            is_deleted = 0
          and status = 1
          AND love_enterprise_years LIKE  concat('%',#{year},'%')
    </select>
    <select id="countLoveCorp" resultType="java.lang.Integer">
        SELECT
            ifnull( count( 0 ), 0 )
        FROM
            zyz_project_company
        WHERE
            is_deleted = 0
          AND STATUS = 1
          AND love_enterprise_years IS NOT NULL
          AND love_enterprise_years != ''
    </select>
    <select id="loveCorpByCreditCode" resultType="java.lang.Integer">
        SELECT
            ifnull( count( 0 ), 0 )
        FROM
            zyz_project_company
        WHERE
            is_deleted = 0
          AND STATUS = 1
          and social_credit_code = #{creditCode}
          AND love_enterprise_years IS NOT NULL
          AND love_enterprise_years != ''
    </select>
</mapper>