package com.fykj.scaffold.sync.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SyncWaringListDto implements Serializable {

    @ApiModelProperty(value = "年月")
    private String yearMon;

    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @ApiModelProperty(value = "业务类型名称")
    private String bizTypeName;

    @ApiModelProperty(value = "上报数据量")
    private int reportNum = 0;

    @ApiModelProperty(value = "上报失败数据量")
    private int failNum = 0;

    @ApiModelProperty(value = "上报成功数据量")
    private int successNum = 0;

    @ApiModelProperty(value = "上报错误率")
    private int failRate = 0;

    @ApiModelProperty(value = "短信通知")
    private Boolean sms = false;

    public SyncWaringListDto(final String bizType, final String bizTypeName) {
        this.bizType = bizType;
        this.bizTypeName = bizTypeName;
    }
}
