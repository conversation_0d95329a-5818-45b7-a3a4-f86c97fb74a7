package com.fykj.scaffold.zsq_docking.push_mq.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 15:11
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
@ApiModel("接收志愿者活动所在社区")
public class ActZyzSyncBelongRegionDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 地域名
     */
    @TableField("region_name")
    @ApiModelProperty(value = "地域名")
    private String regionName;
    /**
     * 地域code
     */
    @TableField("region_code")
    @ApiModelProperty(value = "地域code")
    private String regionCode;

    /**
     * 父类code
     */
    @TableField("parent_code")
    @ApiModelProperty(value = "父类code")
    private String parentCode;
    /**
     * 组织架构链
     */
    @TableField("code_link")
    @ApiModelProperty(value = "组织架构链")
    private String codeLink;
}