package com.fykj.scaffold.message.domain.param;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date: 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("消息发送查询参数")
public class MsgSendParams extends BaseParams {

    @ApiModelProperty("模糊匹配")
    private String keyword;

    @ApiModelProperty("消息发送方式模糊搜索")
    private String sendType;

    @ApiModelProperty("模版id")
    private Long tmpId;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("是否自定义")
    private Boolean selfDefine;
}
