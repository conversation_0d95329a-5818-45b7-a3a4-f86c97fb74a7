package com.fykj.scaffold.security.login.util;

import com.fykj.scaffold.security.business.domain.entity.SysLog;
import com.fykj.scaffold.security.business.service.ISysLogService;
import fykj.microservice.core.support.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 登录记录工具类
 * 用于记录不同方式的登录日志
 *
 * <AUTHOR>
 * @date 2025-01-31
 */
@Component
@Slf4j
public class LoginRecordUtil {

    @Autowired
    private ISysLogService sysLogService;

    /**
     * 记录登录日志
     *
     * @param username 用户名
     * @param loginType 登录方式（使用LoginTypeCons中的常量）
     */
    public void recordLogin(String username, String loginType) {
        try {
            SysLog sysLog = new SysLog();
            sysLog.setUsername(username);
            sysLog.setOperation(loginType);
            sysLog.setMethod("login");
            sysLog.setTime(0L);
            sysLog.setIp(SystemUtil.getClientIp(SystemUtil.getRequest()));
            sysLogService.save(sysLog);
            log.info("记录登录日志成功: username={}, loginType={}", username, loginType);
        } catch (Exception e) {
            log.error("记录登录日志失败: username={}, loginType={}", username, loginType, e);
            // 不抛出异常，避免影响正常登录流程
        }
    }
}
