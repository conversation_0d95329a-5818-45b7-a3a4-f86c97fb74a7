package com.fykj.scaffold.sgb_docking.controller;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbServiceType;
import com.fykj.scaffold.sgb_docking.domain.params.SgbServiceTypeParams;
import com.fykj.scaffold.sgb_docking.service.ISgbServiceTypeService;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 苏州志愿者平台服务领域控制器
 */
@Api(tags = "苏州志愿者平台-服务领域")
@RestController
@RequestMapping("/admin/sgb/service-type")
public class SgbServiceTypeController extends BaseController<ISgbServiceTypeService, SgbServiceType, SgbServiceTypeParams> {

    /**
     * 获取服务领域树结构
     *
     * @return 服务领域树列表
     */
    @ApiOperation("获取服务领域树结构")
    @GetMapping("/tree")
    public JsonResult<List<SgbServiceType>> getServiceTypeTree() {
        return new JsonResult<>(baseService.getServiceTypeTree());
    }

    /**
     * 获取服务领域级联选择树结构
     *
     * @return 级联选择树列表
     */
    @ApiOperation("获取服务领域级联选择树结构")
    @GetMapping("/cascader-tree")
    public JsonResult<List<CascaderExtraDto>> getServiceTypeCascaderTree() {
        return new JsonResult<>(baseService.getServiceTypeCascaderTree());
    }

    /**
     * 同步服务领域信息
     */
    @AuditLog("手动同步服务领域信息")
    @ApiOperation("同步服务领域信息")
    @PostMapping("/sync")
    public Result sync() {
        baseService.syncServiceType();
        return new Result();
    }
} 