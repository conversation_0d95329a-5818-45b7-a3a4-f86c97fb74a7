package com.fykj.scaffold.weixin.mp.service;

import com.fykj.scaffold.weixin.mp.domain.entity.WechatMpAccount;
import fykj.microservice.core.base.IBaseService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
public interface IWechatMpAccountService extends IBaseService<WechatMpAccount> {

    /**
     * 从页面修改
     *
     * @param account 账号
     * @return 是否成功
     */
    boolean updateByIdFromPage(WechatMpAccount account);

    /**
     * 重写save 添加code校验
     *
     * @param account 实体
     * @return 是否成功
     */
    boolean save(WechatMpAccount account);
}
