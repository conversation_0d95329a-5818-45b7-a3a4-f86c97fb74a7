package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 系统资源-功能操作
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_action")
public class SysAction extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 资源编码
     */
    @TableField("action_code")
    @ApiModelProperty(value = "资源编码")
    private String actionCode;

    /**
     * 资源名称
     */
    @TableField("action_name")
    @ApiModelProperty(value = "资源名称")
    private String actionName;

    /**
     * 资源描述
     */
    @TableField("action_desc")
    @ApiModelProperty(value = "资源描述")
    private String actionDesc;

    /**
     * 资源父节点
     */
    @TableField("menu_id")
    @ApiModelProperty(value = "资源父节点")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long menuId;

    /**
     * 优先级 越小越靠前
     */
    @TableField("priority")
    @ApiModelProperty(value = "优先级 越小越靠前")
    private Integer priority;

    /**
     * 状态:0-无效 1-有效
     */
    @TableField("status")
    @ApiModelProperty(value = "状态:0-无效 1-有效")
    private Boolean status;

    /**
     * 保留数据0-否 1-是 不允许删除
     */
    @TableField("is_persist")
    @ApiModelProperty(value = "保留数据0-否 1-是 不允许删除")
    private Boolean persist;

    /**
     * 服务名称
     */
    @TableField("service_id")
    @ApiModelProperty(value = "服务名称")
    private String serviceId;

    /**
     * 绑定的api_id
     */
    @TableField("bind_api_id")
    @ApiModelProperty(value = "绑定的api_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bindApiId;


}
