package com.fykj.scaffold.race.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.race.domain.dto.PhotoImportDto;
import com.fykj.scaffold.race.domain.entity.ActivityPrize;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.zyz.domain.dto.VolunteerImportDto;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 摄影大赛-活动奖项作品关联表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-01-10
 */
public interface IActivityPrizeService extends IService<ActivityPrize>, IBaseService<ActivityPrize> {


    /**
     * 根据奖项获奖作品
     * @param prizeId
     * @return
     */
    List<Photo> getPhotosByPrize(Long prizeId);

    /**
     * 上传excel
     * @param excel
     * @return
     */
    List<PhotoImportDto> uploadExcel(MultipartFile excel, Long activityId);
}

