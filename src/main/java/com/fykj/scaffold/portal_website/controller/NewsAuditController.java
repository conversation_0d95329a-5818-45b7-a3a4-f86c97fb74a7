package com.fykj.scaffold.portal_website.controller;


import com.fykj.scaffold.portal_website.domain.entity.NewsAudit;
import com.fykj.scaffold.portal_website.domain.params.NewsAuditParams;
import com.fykj.scaffold.portal_website.service.INewsAuditService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/admin/portal_website/news_audit")
@Api(tags = "新闻资讯审核接口")
public class NewsAuditController extends BaseController<INewsAuditService, NewsAudit, NewsAuditParams> {

}
