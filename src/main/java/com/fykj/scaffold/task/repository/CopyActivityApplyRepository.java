//package com.fykj.scaffold.task.repository;
//
//import com.fykj.scaffold.task.domain.entity.CopyActivityApply;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//
//@Repository
//public interface CopyActivityApplyRepository extends ElasticsearchRepository<CopyActivityApply, Long> {
//    /**
//     * 根据活动id找所有报名数据
//     * @param actId
//     * @return
//     */
//    List<CopyActivityApply> findByActivityId(Long actId);
//
//    /**
//     * 根据任务id找所有报名数据
//     * @param parameterId
//     * @return
//     */
//    List<CopyActivityApply> findByParameterId(Long parameterId);
//
//    /**
//     * 分页根据活动id和查询条件为同步状态查找所有报名数据
//     * @param activityId
//     * @param serviceSync
//     * @param applySync
//     * @param pageable
//     * @return
//     */
//    Page<CopyActivityApply> findByActivityIdAndApplyIsSyncAndServiceLongIsSyncOrderByCreateTimeDesc(Long activityId, String serviceSync,String applySync, Pageable pageable);
//    /**
//     * 分页根据活动id查找所有报名数据
//     * @param activityId
//     * @param pageable
//     * @return
//     */
//    Page<CopyActivityApply> findByActivityIdOrderByCreateTimeDesc(Long activityId,Pageable pageable);
//}
