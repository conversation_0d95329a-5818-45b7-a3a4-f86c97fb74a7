package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.service.IZyzRequirementService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 需求同步补偿
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RequirementSyncTask {

    @Autowired
    private IZyzRequirementService requirementService;

    @XxlJob("Requirement_Sync_Handler")
    public void requirementSyncJobHandler() {
        XxlJobHelper.log("Requirement_Sync_Handler-Job, START!");
        // 获取待同步的需求数据以及需求审核通过时间三天内需求同步失败的数据
        List<Long> requirementIds = requirementService.getNeedSync();
        if (CollectionUtil.isNotEmpty(requirementIds)) {
            try {
                requirementService.syncBatch(requirementIds);
            } catch (Exception e) {
                log.error("需求批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Requirement_Sync_Handler-Job, END!");
    }
}
