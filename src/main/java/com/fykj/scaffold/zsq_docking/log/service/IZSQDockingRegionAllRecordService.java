package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.dto.RegionAllDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingRegionAllRecord;
import fykj.microservice.core.base.IBaseService;
import result.Result;

import java.util.List;

public interface IZSQDockingRegionAllRecordService extends IBaseService<ZSQDockingRegionAllRecord> {

    /**
     * 获取所有组织对接记录列表
     *
     * @return
     */
    List<ZSQDockingRegionAllRecord> getRegionAllDockingRecords();

    /**
     * 失败重试
     *
     * @param recordId
     */
    void retry(Long recordId);

    /**
     * 获取对接记录详情
     *
     * @param recordId
     * @return
     */
    RegionAllDockingRecordDetailDto getDockingRecordDetail(Long recordId);

    /**
     * 更新对接记录
     *
     * @param record
     * @param result
     */
    void updateDockingRecord(ZSQDockingRegionAllRecord record, Result result);

    /**
     * 更新对接记录
     *
     * @param recordId
     * @param result
     */
    void updateDockingRecord(Long recordId, Result result);


}
