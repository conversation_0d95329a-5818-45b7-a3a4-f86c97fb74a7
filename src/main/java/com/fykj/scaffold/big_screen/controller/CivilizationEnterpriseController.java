package com.fykj.scaffold.big_screen.controller;


import com.fykj.scaffold.big_screen.domain.entity.CivilizationEnterprise;
import com.fykj.scaffold.big_screen.domain.params.CivilizationEnterpriseParams;
import com.fykj.scaffold.big_screen.service.impl.CivilizationEnterpriseServiceImpl;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.Result;
import result.ResultCode;


/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@RestController
@RequestMapping("/admin/civilization_enterprise")
@Api(tags = "文明单位")
public class CivilizationEnterpriseController extends BaseController<CivilizationEnterpriseServiceImpl, CivilizationEnterprise, CivilizationEnterpriseParams> {

    @AuditLog("文明单位禁启用操作")
    @ApiOperation("文明单位禁启用")
    @GetMapping({"/statusChange"})
    public Result statusChange(@RequestParam Long id) {
        baseService.statusChange(id);
        return new Result(ResultCode.OK);
    }
}
