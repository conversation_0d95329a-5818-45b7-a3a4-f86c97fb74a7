package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.MyDockingExportDto;
import com.fykj.scaffold.zyz.domain.dto.RequirementDockingExportDto;
import com.fykj.scaffold.zyz.domain.dto.RequirementExportDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzRequirementForUpdateSyncDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementParams;
import com.fykj.scaffold.zyz.service.IZyzRequirementService;
import constants.Mark;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import io.swagger.annotations.Api;
/**
 * 需求表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-23
 */
@RestController
@RequestMapping("/admin/zyz/requirement")
@Api(tags = "需求接口")
public class ZyzRequirementController extends BaseController<IZyzRequirementService, ZyzRequirement, ZyzRequirementParams> {


    @AuditLog("需求-删除")
    @Override
    public Result removeByIds(String ids) {
        return super.removeByIds(ids);
    }

    @ApiOperation("获取需求列表--分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzRequirement>> getMyPages(@RequestBody ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        IPage<ZyzRequirement> iPage = baseService.getPages(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @ApiOperation("需求列表导出")
    @PostMapping({"/exportRequirement"})
    public void exportRequirement(@RequestBody(required = false) ZyzRequirementParams params) {
        List<RequirementExportDto> res = baseService.getList(params);
        ExcelUtil.writeExcel(res, "需求列表", RequirementExportDto.class);
    }


    @ApiOperation("需求对接分页查询")
    @PostMapping({"/pagesForDocking"})
    public JsonResult<IPage<ZyzRequirement>> pagesForDocking(@RequestBody(required = false) ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        IPage<ZyzRequirement> result = this.baseService.pagesForDocking(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("需求对接导出")
    @PostMapping({"/exportForDocking"})
    public void exportForDocking(@RequestBody(required = false) ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        List<RequirementDockingExportDto> res = baseService.listForDocking(params);
        ExcelUtil.writeExcel(res, "需求对接列表", RequirementDockingExportDto.class);
    }

    @ApiOperation("我的对接分页查询")
    @PostMapping({"/pagesForMyDocking"})
    public JsonResult<IPage<ZyzRequirement>> pagesForMyDocking(@RequestBody(required = false) ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        IPage<ZyzRequirement> result = this.baseService.pagesForMyDocking(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }


    @AuditLog("我的对接导出")
    @ApiOperation("我的对接导出")
    @PostMapping({"/exportForMyDocking"})
    public void exportForMyDocking(@RequestBody(required = false) ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        List<MyDockingExportDto> res = baseService.listForMyDocking(params);
        ExcelUtil.writeExcel(res, "我的需求对接列表", MyDockingExportDto.class);
    }

    @ApiOperation("需求审核分页查询")
    @PostMapping({"/pagesForAudit"})
    public JsonResult<IPage<ZyzRequirement>> pagesForAudit(@RequestBody(required = false) ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        IPage<ZyzRequirement> result = this.baseService.pagesForAudit(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("获取需求")
    @GetMapping("getRequirementById")
    public Result getRequirementById(@RequestParam Long id) {
        ZyzRequirement requirement = this.baseService.getRequirementById(id);
        return new JsonResult<>(requirement);
    }


    @AuditLog("保存草稿")
    @ApiOperation("保存草稿")
    @PostMapping("/saveRequirement")
    public Result saveRequirement(@RequestBody ZyzRequirement entity) {
        baseService.saveRequirement(entity);
        return OK;
    }

    @AuditLog("保存草稿并提交")
    @ApiOperation("提交")
    @PostMapping("/renderByRequirement")
    public Result renderByRequirement(@RequestBody ZyzRequirement entity) {
        if (!baseService.renderByRequirement(entity)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("草稿提交")
    @ApiOperation("提交")
    @GetMapping("/renderById")
    public Result renderById(Long id) {
        if (!baseService.render(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("审核通过需求上下架")
    @ApiOperation("上下架")
    @GetMapping("/updateAutoStatus")
    public Result updateAutoStatus(Long id) {
        if (!baseService.updateAutoStatus(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("需求对接")
    @ApiOperation("需求对接")
    @GetMapping("/docking")
    public Result docking(@RequestParam String id, @RequestParam String contactPerson, @RequestParam String contactPhone) {
        baseService.docking(id, contactPerson, contactPhone);
        return OK;
    }

    @AuditLog("需求审核")
    @ApiOperation("需求审核")
    @GetMapping("/audit")
    public Result audit(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.audit(idList, status, remark);
        return OK;
    }


    @AuditLog("评价")
    @ApiOperation("评价")
    @GetMapping("/updateRemark")
    public Result updateRemark(Long id, BigDecimal evaluateStar, String evaluateRemark) {
        if (!baseService.updateRemark(id, evaluateRemark, evaluateStar)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ApiOperation("获取可以对接的需求列表")
    @GetMapping("/getEnableDocking")
    public JsonResult<List<ZyzRequirement>> getEnableDocking(@RequestParam(required = false) Long actId, @RequestParam(required = false) Long teamId) {
        return new JsonResult<>(baseService.getEnableDocking(actId, teamId));
    }

    @ApiOperation("获取需求(修改并同步时使用)")
    @GetMapping("/getRequirementForUpdateSync")
    public Result getRequirementForUpdateSync(@RequestParam Long id) {
        return new JsonResult<>(this.baseService.getRequirementForUpdateSync(id));
    }

    @ApiOperation("管理员修改(重新同步使用)")
    @PostMapping("/updateForReSync")
    public Result updateForReSync(@RequestBody ZyzRequirementForUpdateSyncDto data) {
        this.baseService.updateForReSync(data);
        return OK;
    }

    @ApiOperation("管理员修改后重新同步")
    @GetMapping("/reSyncForAdmin")
    public Result reSyncForAdmin(@RequestParam Long id) {
        this.baseService.reSyncForAdmin(id);
        return OK;
    }
}
