package com.fykj.scaffold.race.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 摄影大赛-活动奖项等级表
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("race_activity_prize_grade")
public class ActivityPrizeGrade extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 奖项名称
     */

    @TableField("name")
    @ApiModelProperty(value = "奖项名称")
    private String name;

    /**
     * 排序
     */

    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;

    /**
     * 活动id
     */
    @TableField("activity_id")
    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

}
