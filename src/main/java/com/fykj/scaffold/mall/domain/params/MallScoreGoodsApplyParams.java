package com.fykj.scaffold.mall.domain.params;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 星光闪耀商品报名表
 * 查询参数
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "星光闪耀商品报名表查询参数")
public class MallScoreGoodsApplyParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"goods_name", "volunteer_name", "guardian_name", "guardian_phone", "guardian_certificate_id", "guardian_work_unit", "participant_name", "participant_phone", "participant_certificate_id", "participant_school"})
    private String keyWord;


    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime startTime;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime endTime;
}
