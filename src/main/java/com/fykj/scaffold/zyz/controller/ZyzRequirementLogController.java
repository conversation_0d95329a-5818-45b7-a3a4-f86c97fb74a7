package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirementLog;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementLogParams;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementParams;
import com.fykj.scaffold.zyz.service.IZyzRequirementLogService;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;
import io.swagger.annotations.Api;
/**
 * 需求操作记录表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-02-24
 */
@RestController
@RequestMapping("/admin/zyz/requirement/log")
@Api(tags = "需求操作记录接口")
public class ZyzRequirementLogController extends BaseController<IZyzRequirementLogService, ZyzRequirementLog,ZyzRequirementLogParams> {


    @ApiOperation("查询需求日志")
    @GetMapping({"/getLogsByRequirementId"})
    public JsonResult<List<ZyzRequirementLog>> getLogsByRequirementId(Long requirementId) {
        List<ZyzRequirementLog> result = this.baseService.getLogsByRequirementId(requirementId);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }

}
