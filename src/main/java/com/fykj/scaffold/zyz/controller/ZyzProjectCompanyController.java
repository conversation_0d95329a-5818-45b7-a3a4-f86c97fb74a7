package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.sip.SipCompanyUtil;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.dto.CompanyDockingExportDto;
import com.fykj.scaffold.zyz.domain.dto.ProjectCompanyBindPageDto;
import com.fykj.scaffold.zyz.domain.dto.ProjectCompanyContributeDto;
import com.fykj.scaffold.zyz.domain.dto.ProjectCompanyImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectCompany;
import com.fykj.scaffold.zyz.domain.params.ProjectCompanyBindParams;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectCompanyParams;
import com.fykj.scaffold.zyz.service.IZyzProjectCompanyService;
import com.fykj.scaffold.zyz.service.IZyzProjectDockingService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公益伙伴计划-项目资助企业
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-24
 */
@Slf4j
@RestController
@RequestMapping("/admin/project/company")
@Api(tags = "公益伙伴计划-项目资助企业接口")
public class ZyzProjectCompanyController extends BaseController<IZyzProjectCompanyService, ZyzProjectCompany, ZyzProjectCompanyParams> {

    @Autowired
    private IZyzProjectDockingService dockingService;

    @AuditLog("导出公益伙伴参与计划")
    @GetMapping("/exportCompanyContribute")
    @ApiOperation("导出公益伙伴参与计划")
    public void exportCompanyContribute(Integer year) {
        List<ProjectCompanyContributeDto> list = baseService.exportCompanyContribute(year);
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "company_contribute_template.xlsx", ProjectCompanyContributeDto.class);
    }


    @ApiOperation("获取项目公司列表")
    @GetMapping("/getCompanyList")
    public JsonResult<List<ZyzProjectCompany>> getCompanyList() {
        return new JsonResult<>(baseService.getCompanyList());
    }

    @SysLogMethod("新增")
    @ApiOperation("保存方法")
    @PostMapping({"/saveCompany"})
    public JsonResult<ZyzProjectCompany> saveCompany(@RequestBody @Validated({BaseEntity.Add.class}) ZyzProjectCompany entity) {
//        try {
//            ZyzProjectCompany corpInfoDetail = SipCompanyUtil.getCorpInfoDetail(entity.getSocialCreditCode());
//            if (corpInfoDetail != null) {
//                entity.setCorpName(corpInfoDetail.getCorpName());
//            }
//        } catch (Exception ex) {
//        }
        ZyzProjectCompany result = this.baseService.saveCompany(entity);
        return new JsonResult<>(result);
    }

    @ApiOperation("获取园区法人库中企业信息")
    @GetMapping("/getCompanyInfo")
    public JsonResult<ZyzProjectCompany> getCompanyInfo(@RequestParam(name = "corpName") String corpName) {
        ZyzProjectCompany crop = SipCompanyUtil.getCorpInfoDetail(corpName);
        return new JsonResult<>(crop);
    }

    @AuditLog("导出公益伙伴对接列表")
    @GetMapping("/exportCompanyDockingList")
    @ApiOperation("导出公益伙伴对接列表")
    public void exportCompanyDockingList(Integer year) {
        List<CompanyDockingExportDto> list = baseService.getCompanyDockingList(year);
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "company_docking_template.xlsx", CompanyDockingExportDto.class);
    }

    @ApiOperation("置顶、取消置顶")
    @GetMapping({"/changeTopDisplay"})
    public Result changeTopDisplay(Long id, Boolean topDisplay) {
        ZyzProjectCompany company = baseService.getById(id);
        company.setTopDisplay(topDisplay);
        baseService.updateById(company);
        return new Result();
    }

    @ApiOperation("上下架")
    @GetMapping({"/changeStatus"})
    public Result changeStatus(Long id, Boolean status) {
        ZyzProjectCompany company = baseService.getById(id);
        company.setStatus(status);
        baseService.updateById(company);
        return new Result();
    }

    @ApiOperation("爱心企业关联项目查询")
    @PostMapping("/bindProjectPage")
    public Result propertyPagesForRecord(@RequestBody(required = false) ProjectCompanyBindParams params) {
        if (params == null) {
            params = new ProjectCompanyBindParams();
        }
        IPage<ProjectCompanyBindPageDto> result = baseService.bindProjectPage(params);
        DictTransUtil.trans(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("取消关联")
    @GetMapping("/removeBind")
    public Result removeBind(Long dockingId) {
        dockingService.removeById(dockingId);
        return OK;
    }

    /**
     * 导入模板下载
     */
    @ApiOperation("导入模板下载")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        downloadFile(response, "/data/excelTemplate/project_company_import_template.xlsx");
    }

    @ApiOperation("批量导入发送对象")
    @PostMapping("/excelImport")
    public Result excelImport(@RequestParam(name = "excel") MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请选择要上传的文件");
        }
        List<ProjectCompanyImportDto> dataList = ExcelUtil.readExcel(excel, ProjectCompanyImportDto.class, 0, 2);
        if (CollectionUtil.isEmpty(dataList) || dataList.size() == 1) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "无数据，请完善数据");
        }
        dataList = CollectionUtil.sub(dataList, 1, dataList.size());
        List<ProjectCompanyImportDto> failureList = baseService.excelImport(dataList);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "project_company_import_failure.xlsx", ProjectCompanyImportDto.class);
        }
        return OK;
    }
}
