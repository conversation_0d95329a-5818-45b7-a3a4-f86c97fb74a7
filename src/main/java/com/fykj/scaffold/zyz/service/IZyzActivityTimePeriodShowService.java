package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.zyz.domain.dto.ActivityTimePeriodShowDto;
import com.fykj.scaffold.zyz.domain.dto.OrgTeamActivityShowSumReportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShow;
import com.fykj.scaffold.zyz.domain.params.ActivityTimePeriodShowApiParams;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityTimePeriodShowParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 活动-时段表-公示信息
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface IZyzActivityTimePeriodShowService extends IBaseService<ZyzActivityTimePeriodShow> {

    /**
     * 活动公示维护--分页查询
     *
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<ZyzActivityTimePeriodShow> pagesForShow(ZyzActivityTimePeriodShowParams params);

    /**
     * 活动公示审核--分页查询
     *
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<ZyzActivityTimePeriodShow> pagesForAudit(ZyzActivityTimePeriodShowParams params);


    /**
     * 获取活动公示详情
     *
     * @param periodId 小活动id
     * @param id       公示id
     * @return 公示详情
     */
    ZyzActivityTimePeriodShow getDetail(Long periodId, Long id);

    /**
     * 保存/修改-保存为草稿
     *
     * @param entity 活动公示
     */
    void saveOrUpdateToDraft(ZyzActivityTimePeriodShow entity);

    /**
     * 保存活动公示并提交
     *
     * @param entity 活动公示
     * @return 是否成功
     */
    boolean render(ZyzActivityTimePeriodShow entity);

    /**
     * 提交（by id)
     *
     * @param id 公示id
     * @return 是否成功
     */
    boolean renderById(Long id);

    /**
     * 撤回
     *
     * @param id 展示的id
     * @return 是否成功
     */
    boolean recall(long id);

    /**
     * 活动公示审核
     *
     * @param idList 公示id列表
     * @param status 状态
     * @param remark 备注
     */
    void audit(List<Long> idList, Boolean status, String remark);

    /**
     * 上下架
     *
     * @param id 公示id
     * @return 是否成功
     */
    boolean updateAutoStatus(Long id);

    /**
     * 获取需要同步的数据
     *
     * @return id列表
     */
    List<Long> getNeedSync();

    /**
     * 批量同步
     *
     * @param showIds 公示ids
     */
    void syncBatch(List<Long> showIds);

    /**
     * 单条同步
     *
     * @param showId 公示id
     * @return ZyzSyncLog 同步log
     */
    ZyzSyncLog syncOne(Long showId);

    /**
     * 获取团队活动公示上传情况
     *
     * @param params 查询参数
     * @return 分页数据
     */
    IPage<OrgTeamActivityShowSumReportDto> getTeamActInfoSum(OrgDataReportFormParams params);

    /**
     * 获取组织活动公示上传情况
     *
     * @param params 查询参数
     * @return 分页数据
     */
    IPage<OrgTeamActivityShowSumReportDto> getOrgActInfoSum(OrgDataReportFormParams params);

    /**
     * 根据活动ID查询公示信息列表
     * @param params 查询参数
     * @return 公示信息列表
     */
    List<ActivityTimePeriodShowDto> pageForApi(ActivityTimePeriodShowApiParams params);

    /**
     * 获取活动图片公示待审核数量
     * @return 待审核数量
     */
    Integer getActivityPhotoPublicWaitAuditNum();
}

