package com.fykj.scaffold.capability.domain.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel("Course Detail DTO")
public class CourseQrcodeDTO {

    @ApiModelProperty("课程ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课程日期")
    private LocalDate courseDate;

    @ApiModelProperty("课程开始时间 (起始时间)")
    private LocalTime courseStartTime;

    @ApiModelProperty("课程结束时间 (结束时间)")
    private LocalTime courseEndTime;

    @ApiModelProperty("课程签到时间范围 (起始时间)")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN)
    private LocalDateTime signInStartAt;

    @ApiModelProperty("课程结束时间 (结束时间)")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN)
    private LocalDateTime signInEndAt;

    @ApiModelProperty("签到二维码 (课程需要生成二维码，使用二维码签到，微信扫码后跳转到该课程详情)")
    private String checkInQrcode;

}
