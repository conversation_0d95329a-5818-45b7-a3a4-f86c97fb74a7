package com.fykj.scaffold.message.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import fykj.microservice.core.support.excel.annotation.BoolConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-06-05
 */
@Data
public class MsgRecordExportDto{

    private static final long serialVersionUID = 1L;

    /**
     * 推送批次
     */
    @ApiModelProperty(value = "推送批次")
    @ExcelProperty("推送批次")
    private String sendCode;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型")
    @ExcelProperty("消息类型")
    @BoolConvert(trueText = "自定义消息", falseText = "模版消息")
    private Boolean selfDefine;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    @ExcelProperty("消息标题")
    private String title;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    @ExcelProperty("消息内容")
    private String content;

    /**
     * 推送方式
     */
    @ApiModelProperty(value = "推送方式")
    @ExcelProperty("推送方式")
    private String sendType;

    /**
     * 消息状态
     */
    @ApiModelProperty(value = "消息状态")
    @ExcelProperty("消息状态")
    @BoolConvert(trueText = "已读", falseText = "未读")
    private Boolean status;

    /**
     * 推送时间
     */
    @ApiModelProperty(value = "推送时间")
    @ExcelProperty("推送时间")
    private LocalDateTime sendTime;

    /**
     * 推送者
     */
    @ApiModelProperty(value = "推送者")
    @ExcelProperty("推送者")
    private String senderName;

    /**
     * 接收者
     */
    @ApiModelProperty(value = "接收者")
    @ExcelProperty("接收者")
    private String receiverName;

    /**
     * 接收者手机号
     */
    @ApiModelProperty(value = "接收者手机号")
    @ExcelProperty("接收者手机号")
    private String receiverMobile;

    /**
     * 是否为志愿者
     */
    @ApiModelProperty(value = "是否为志愿者")
    @ExcelProperty("是否为志愿者")
    @BoolConvert
    private Boolean receiverVolunteer;

    /**
     * 推送状态
     */
    @ApiModelProperty(value = "推送状态")
    @ExcelProperty("推送状态")
    private String sendStatus;

    /**
     * 推送失败原因
     */
    @ApiModelProperty(value = "推送失败原因")
    @ExcelProperty("推送失败原因")
    private String sendFailReason;

    /**
     * 短信任务序列 id
     */
    @ApiModelProperty(value = "短信任务序列 id")
    @ExcelProperty("短信任务序列 id")
    private String smsTaskId;

    /**
     * 短信真实发送状态
     */
    @ApiModelProperty(value = "短信真实发送状态")
    @ExcelProperty("短信真实发送状态")
    private String smsSendStatus;

    /**
     * 短信真实接收时间
     */
    @ApiModelProperty(value = "短信真实接收时间")
    @ExcelProperty("短信真实接收时间")
    private LocalDateTime smsReceiveTime;

    /**
     * 短信发送状态码
     */
    @ApiModelProperty(value = "短信发送状态码")
    @ExcelProperty("短信发送状态码")
    private String smsErrorCode;
}
