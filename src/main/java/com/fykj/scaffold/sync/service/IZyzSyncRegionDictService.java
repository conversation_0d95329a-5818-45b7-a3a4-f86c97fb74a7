package com.fykj.scaffold.sync.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDict;

/**
 * 市平台地域数据字典
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface IZyzSyncRegionDictService extends IBaseService<ZyzSyncRegionDict> {
    /**
     * 同步地域
     */
    void syncRegionDict();

    /**
     * 根据行政区划code获取
     *
     * @return
     */
    ZyzSyncRegionDict getByRegionCode(String regionCode);
}

