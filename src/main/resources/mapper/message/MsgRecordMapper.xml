<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.message.mapper.MsgRecordMapper">

    <select id="listOrPage" resultType="com.fykj.scaffold.message.domain.entity.MsgRecord">
        SELECT
            mr.id AS id,
            mr.send_code AS sendCode,
            CASE WHEN mr.send_status != 1 THEN NULL ELSE mr.status END AS status,
            mr.title AS title,
            mr.content AS content,
            mr.send_time AS sendTime,
            mr.sender AS sender,
            mr.receiver AS receiver,
            mr.receiver_mobile AS receiverMobile,
            CASE WHEN mr.receiver_name IS NULL OR mr.receiver_name = '' THEN mr.receiver_mobile ELSE mr.receiver_name END AS receiverName,
            mr.send_type AS sendType,
            mr.send_status AS sendStatus,
            mr.send_fail_reason AS sendFailReason,
            mr.self_define AS selfDefine,
            mr.task_id AS taskId,
            CASE WHEN mr.task_id IS NULL OR mr.task_id = '' THEN NULL
                 WHEN mrrss.task_id IS NULL THEN '未知'
                 ELSE mrrss.status END AS smsSendStatus,
            mrrss.receive_time AS smsReceiveTime,
            mrrss.error_code AS smsErrorCode
        FROM
            msg_record mr
        LEFT JOIN (SELECT task_id, status, receive_time, error_code FROM msg_record_real_send_status WHERE is_deleted = 0 GROUP BY task_id) AS mrrss ON mr.task_id = mrrss.task_id
        WHERE
            mr.is_deleted = 0
        <if test="params.sendId != null">
            AND mr.send_id = #{params.sendId}
        </if>
        <if test="params.sendId == null and params.keyword != null and params.keyword != ''">
            AND (mr.title LIKE CONCAT('%',#{params.keyword},'%') OR mr.content LIKE CONCAT('%',#{params.keyword},'%')
                OR mr.send_code LIKE CONCAT('%',#{params.keyword},'%'))
        </if>
        <if test="params.sendType != null and params.sendType != ''">
            AND mr.send_type = #{params.sendType}
        </if>
        <if test="params.status != null">
            AND mr.send_status = 1 AND mr.status = #{params.status}
        </if>
        <if test="params.sendStatus != null">
            AND mr.send_status = #{params.sendStatus}
        </if>
        <if test="params.selfDefine != null">
            AND mr.self_define = #{params.selfDefine}
        </if>
        ORDER BY mr.send_time DESC, mr.create_date DESC
    </select>

    <delete id="removeNotSendRecord">
        DELETE FROM msg_record WHERE (send_status = 0 OR send_status = 2) AND send_id = #{sendId}
    </delete>

    <select id="pageForMaintain" resultType="com.fykj.scaffold.message.domain.entity.MsgRecord">
        SELECT
            mr.*,
            GROUP_CONCAT(mr.send_type SEPARATOR ',') AS sendType,
            CASE WHEN zv.id IS NULL THEN '未找到志愿者！' ELSE zv.name END AS receiverVolunteerName
        FROM msg_record mr
        LEFT JOIN zyz_volunteer zv ON mr.receiver = zv.id
        WHERE mr.is_deleted = 0 AND (mr.send_status = 0 OR mr.send_status = 2)
        AND mr.send_id = #{params.sendId}
        GROUP BY mr.receiver_mobile
        ORDER BY mr.update_date DESC
    </select>
</mapper>
