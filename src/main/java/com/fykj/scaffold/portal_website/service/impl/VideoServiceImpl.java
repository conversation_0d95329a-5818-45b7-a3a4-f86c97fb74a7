package com.fykj.scaffold.portal_website.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.portal_website.domain.entity.Video;
import com.fykj.scaffold.portal_website.domain.params.VideoParams;
import com.fykj.scaffold.portal_website.mapper.VideoMapper;
import com.fykj.scaffold.portal_website.service.IVideoService;
import com.fykj.scaffold.support.oss.FilePathDto;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;

import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 视频资源服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class VideoServiceImpl extends BaseServiceImpl<VideoMapper, Video> implements IVideoService {

    @Value("${httpAttachmentUrl}")
    private String httpAttachmentUrl;

    @Override
    public void upload(MultipartFile file, String serverCode, String id) {
        Video video = super.getById(id);
        video.setPath(getPath(file, serverCode).getUrl());
        video.setSuccess(true);
        super.updateById(video);
    }

    /**
     * 获取文件访问路径
     *
     * @param file       上传文件兑现
     * @param serverCode 存储服务器编码
     * @return 文件访问路径
     */
    private FilePathDto getPath(MultipartFile file, String serverCode) {
        return OssSaveUtil.save(file, serverCode);
    }

    @Override
    public void updateStatus(String id, Boolean status, String failReason) {
        List<String> ids = Arrays.asList(id.split(Mark.COMMA));
        List<Video> videos = baseMapper.getForUpdate(ids);
        List<Video> needUpdate = new ArrayList<>();
        if (CollectionUtil.isEmpty(videos)) {
            return;
        }
        videos.forEach(it -> {
            if (ObjectUtil.isNotEmpty(it.getSuccess()) && it.getSuccess()) {
                return;
            }
            it.setSuccess(status);
            it.setFailReason(failReason);
            needUpdate.add(it);
        });
        if (CollectionUtil.isEmpty(needUpdate)) {
            return;
        }
        super.updateBatchById(needUpdate);
    }

    @Override
    public IPage<Video> page(BaseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new VideoParams();
        }
        Page<Video> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForVideo(page, (VideoParams)params).convert(DictTransUtil::trans);
    }

    @Override
    public Video getVideoInfo(String id) {
        Video video = super.getById(id);
        Long fileSize = video.getFileSize();
        if (ObjectUtil.isEmpty(fileSize)) {
            video.setFileSizeText("未知");
            return video;
        }
        if (fileSize > 1073741824) {
            video.setFileSizeText((Math.round((fileSize / 1073741824.0) * 100) / 100.0) + "GB");
        } else if (fileSize > 1048576) {
            video.setFileSizeText((Math.round((fileSize / 1048576.0) * 100) / 100.0) + "MB");
        } else if (fileSize > 1024) {
            video.setFileSizeText((Math.round((fileSize / 1024.0) * 100) / 100.0) + "KB");
        } else {
            video.setFileSizeText(fileSize + "B");
        }
        return video;
    }

    @Override
    public void updateName(String id, String name) {
        if (StringUtil.isEmpty(name)) {
            throw new BusinessException(ResultCode.FAIL, "修改名称时名称不能为空！");
        }
        lambdaUpdate().eq(Video::getId, id).set(Video::getName, name).update();
    }
}
