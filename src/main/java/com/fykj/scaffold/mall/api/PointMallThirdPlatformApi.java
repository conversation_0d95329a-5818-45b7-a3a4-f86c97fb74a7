package com.fykj.scaffold.mall.api;

import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.mall.domain.dto.MallThirdPlatformVolunteerInfoDto;
import com.fykj.scaffold.mall.domain.dto.ThirdPlatformPointChangeDto;
import com.fykj.scaffold.mall.service.IZyzPointRecordService;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.utils.AesUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.util.ServiceLongTool;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.LocalDateTimeUtil;
import utils.StringUtil;

/**
 * 团队微信前端接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/zyz/point-mall/third-platform")
@Api(tags = "小程序--积分商城第三方平台对接统一入口")
public class PointMallThirdPlatformApi {

    @Autowired
    private IZyzPointRecordService pointRecordService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IDictService dictService;

    @ApiOperation(value = "变动积分")
    @PostMapping(value = "/changePoint")
    public Result changePoint(@RequestBody ThirdPlatformPointChangeDto dto) {
        pointRecordService.thirdPlatformChangePoint(dto);
        return new Result();
    }

    @ApiOperation(value = "查询志愿者信息")
    @GetMapping(value = "/queryVolunteerInfo")
    public Result queryVolunteerInfo(String phone, String idCard, String platformCode) {
        ZyzVolunteer volunteer;
        if (StringUtil.isNotEmpty(phone)) {
            volunteer = volunteerService.getByPhone(phone);
        } else if (StringUtil.isNotEmpty(idCard)) {
            volunteer = volunteerService.lambdaQuery().eq(ZyzVolunteer::getCertificateId, idCard).eq(ZyzVolunteer::getWriteOff, false).one();
        } else {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请输入正确手机号或身份证");
        }
        if (volunteer == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "无法查询到志愿者信息");
        }
        MallThirdPlatformVolunteerInfoDto dto = new MallThirdPlatformVolunteerInfoDto();
        BeanUtils.copyProperties(volunteer, dto);
        DictTransUtil.trans(dto);
        dto.setStar(ServiceLongTool.serviceLongToStar(volunteer.getServiceLong()));
        dto.setFounded(LocalDateTimeUtil.formatDateTime(volunteer.getFounded()));
        Dict platformDict = dictService.getByCode(platformCode);
        return new JsonResult<>(AesUtil.encrypt(JSONUtil.toJsonStr(dto), platformDict.getValue()));
    }
}
