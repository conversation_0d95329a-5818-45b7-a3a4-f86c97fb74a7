package com.fykj.scaffold.zyz.controller;


import com.fykj.scaffold.zyz.domain.entity.ZyzProjectActivityPlan;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectActivityPlanParams;
import com.fykj.scaffold.zyz.service.IZyzProjectActivityPlanService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;

/**
 * 活动计划安排
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/admin/project/activity/plan")
@Api(tags = "活动计划安排接口")
public class ZyzProjectActivityPlanController extends BaseController<IZyzProjectActivityPlanService, ZyzProjectActivityPlan, ZyzProjectActivityPlanParams> {

}
