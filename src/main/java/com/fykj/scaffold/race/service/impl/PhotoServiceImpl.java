package com.fykj.scaffold.race.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.race.cons.Constants;
import com.fykj.scaffold.race.domain.dto.PhotoDto;
import com.fykj.scaffold.race.domain.entity.Activity;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.domain.params.PhotoParams;
import com.fykj.scaffold.race.mapper.PhotoMapper;
import com.fykj.scaffold.race.service.IPhotoService;
import com.fykj.scaffold.security.business.domain.entity.SysRoleAction;
import com.fykj.scaffold.security.business.service.IOssConfigService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.support.utils.Oauth2Util;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import utils.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.SysMsgTmpCons.RACE_PHOTO_AUDIT_NO_PASS;


/**
 * 摄影大赛-作品表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PhotoServiceImpl extends BaseServiceImpl<PhotoMapper, Photo> implements IPhotoService {
    @Autowired
    private IUserService userService;
    @Autowired
    private RaceActivityServiceImpl activityService;
    @Autowired
    private IOssConfigService ossConfigService;

    @Autowired
    private ISendTmpMsg sendTmpMsg;


    @Override
    public IPage<Photo> getPages(PhotoParams params) {
        if (StringUtil.isEmpty(params.getActivityId())) {
            Activity activityByEnable = activityService.getActivityByEnable();
            params.setActivityId(activityByEnable.getId());
        }
        IPage<Photo> page = super.page(params);
        DictTransUtil.trans(page.getRecords());
        return page;
    }




    @Override
    public IPage<Photo> getPagesForWeb(PhotoParams params) {
        if (params == null) {
            params = new PhotoParams();
        }
        BaseParams.Order order = new BaseParams.Order();
        order.setColumn("number");
        order.setSort("asc");
        List<BaseParams.Order> orders = new ArrayList<>();
        orders.add(order);
        params.setOrders(orders);
        IPage<Photo> iPage = super.page(params);
        iPage.getRecords().forEach(item -> {
            item.setNumberStr(StringUtil.getValue(item.getNumber()));
        });
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public boolean updateStatus(String ids, Boolean status, String auditRemark) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<String> sendPhotos = new ArrayList<>();
        idList.forEach(item -> {
            Photo photo = getById(item);
            photo.setAuditStatus(status ? Constants.AUDIT_STATUS_PASS : Constants.AUDIT_STATUS_FAIL);
            photo.setAuditRemark(auditRemark);
            sendPhotos.add(photo.getAuthorPhone());
            updateById(photo);
            if (status) {
                updateNumber(photo);
            }
        });
        if (!status) {
           sendTmpMsg.commonSendByPhones(RACE_PHOTO_AUDIT_NO_PASS,sendPhotos,auditRemark);
        }
        return true;
    }


    @Override
    public List<Photo> getPhoto(Long activityId) {
        List<Photo> res = lambdaQuery().eq(Photo::getAuditStatus, Constants.AUDIT_STATUS_PASS).eq(Photo::getActivityId, activityId).orderByAsc(Photo::getNumber).list();
        res.forEach(item -> {
            item.setNumberStr(StringUtil.getValue(item.getNumber()));
        });
        return res;
    }

    @Override
    public List<Photo> getMyPhoto() {
        List<Photo> list = lambdaQuery().eq(Photo::getUserId, Oauth2Util.getUserId()).list();
        list.forEach(item -> {
            item.setNumberStr(StringUtil.getValue(item.getNumber()));
        });
        return DictTransUtil.trans(list);
    }


    private void updateNumber(Photo entity) {
        int maxNumber = baseMapper.getMaxNumber();
        entity.setNumber(maxNumber + 1);
        updateById(entity);
    }

    @Override
    public boolean save(Photo entity) {
        entity.setAuditStatus(Constants.AUDIT_STATUS_CHECK_PENDING);
        entity.setUserId(Objects.requireNonNull(Oauth2Util.getUser()).getId());
//        entity.setAuthor(Oauth2Util.getUser().getNickName());
        entity.setAuthorPhone(Oauth2Util.getMobile());
        return super.save(entity);
    }
    @Override
    public List<PhotoDto> getPhotoList(Long activityId) {
        List<PhotoDto> returnList = new ArrayList<>();

        PhotoParams params = new PhotoParams();
        params.setPageSize(-1);
        params.setActivityId(activityId);
        params.setAuditStatus("pass");
        BaseParams.Order order = new BaseParams.Order();
        order.setColumn("number");
        order.setSort("asc");
        List<BaseParams.Order> orders = new ArrayList<>();
        orders.add(order);
        params.setOrders(orders);
        IPage<Photo> iPage = super.page(params);
        iPage.getRecords().forEach(item -> {
            item.setNumberStr(StringUtil.getValue(item.getNumber()));
        });
        DictTransUtil.trans(iPage.getRecords());
        List<SysRoleAction> list = new ArrayList<>();
        iPage.getRecords().forEach(it -> {
            PhotoDto bean = new PhotoDto();
            BeanUtils.copyProperties(it, bean);
            returnList.add(bean);
        });
        return returnList;
    }

}
