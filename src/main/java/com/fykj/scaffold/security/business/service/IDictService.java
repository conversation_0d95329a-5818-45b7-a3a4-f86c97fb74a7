package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.entity.Dict;
import fykj.microservice.cache.support.DictCacheService;
import fykj.microservice.core.base.IBaseService;
import fykj.microservice.core.beans.vo.IdTextVo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据字典服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public interface IDictService extends IBaseService<Dict>, DictCacheService {

    /**
     * 查询指定编码的数据字典对象
     *
     * @param code 指定字典编码
     * @return 字典对象
     */
    Dict getByCode(String code);

    /**
     * 查询指定编码的数据字典对象
     *
     * @param code 指定字典编码
     * @param throwEx 是否抛出异常
     * @return 字典对象
     */
    Dict getByCode(String code, Boolean throwEx);

    /**
     * 查询指定类别code的数据字典列表
     *
     * @param code 数据字典类型编码
     * @return 数据字典列表
     */
    List<Dict> findByParentCode(String code);

    /**
     * 下拉列表获取
     *
     * @param type 下拉列表类型
     * @return 下拉列表
     */
    List<IdTextVo> idTextVoList(String type);

    /**
     * 获取所有顶级数据
     *
     * @return
     */
    List<IdTextVo> findTopDict();

    /**
     * 获取所有顶级数据 code-text
     *
     * @return
     */
    List<IdTextVo> findTopDictCode();

    /**
     * code是否存在
     *
     * @param id
     * @param code
     * @return
     */
    boolean checkCodeExists(Serializable id, String code);

    /**
     * 获取字典树
     *
     * @return 资源树
     */
    List<Dict> tree();

    /**
     * 获取字典树
     *
     * @return 资源树
     */
    List<Dict> tree(String parentCode, boolean includeRoot);


    /**
     * 根据value模糊查询
     *
     * @param value
     * @return
     */
    String findFileTypeByValue(String value);

    /**
     * 获取Oss存储允许上传后缀列表
     *
     * @return 后缀列表
     */
    List<String> getOssExtList();

    /**
     * 临时用的批量新增数据字典
     *
     * @param parentId
     * @param text
     */
    void batchAdd(long parentId, String text);


    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    Dict get(Long id);

    /**
     * 获取字典map
     * @return
     */
    Map<String, List<Dict>> getDictMap(String[] dictArray);

    /**
     * 跟进code获取value的int型
     *
     * @param code
     * @return
     */
    int getIntValueByCode(String code);

    /**
     * 跟进code获取value的String型
     *
     * @param code
     * @return
     */
    String getStringValueByCode(String code);
}
