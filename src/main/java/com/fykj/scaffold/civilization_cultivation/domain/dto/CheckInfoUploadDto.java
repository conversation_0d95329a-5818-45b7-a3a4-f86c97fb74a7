package com.fykj.scaffold.civilization_cultivation.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

/**
 * @Author：yangxu
 * @Date：2025/5/16 11:22
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckInfoUploadDto implements Serializable {

    private static final long serialVersionUID = 8886582744240465431L;

    @ApiModelProperty(value = "文件列表")
    private List<MultipartFile> files;

    @ApiModelProperty(value = "文件列表")
    private Long id;
}
