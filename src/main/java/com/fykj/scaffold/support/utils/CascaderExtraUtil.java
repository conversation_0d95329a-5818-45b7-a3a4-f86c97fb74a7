package com.fykj.scaffold.support.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static constants.Mark.COMMA;

public class CascaderExtraUtil {

    /**
     * 将逗号分隔的字符串转换为级联选择器需要的路径列表
     * 使用迭代方式替代递归，提升性能和可读性
     *
     * @param valueStr 逗号分隔的值字符串
     * @param treeData 级联选择器的树形数据
     * @return 路径列表，每条路径是一个 Serializable 值的列表
     */
    public static List<List<Serializable>> convertStringToCascaderPaths(
            String valueStr,
            List<CascaderExtraDto> treeData
    ) {
        return Optional.ofNullable(valueStr)
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(CascaderExtraUtil::parseTargetValues)
                .filter(CollUtil::isNotEmpty)
                .map(targets -> findAllPaths(treeData, targets))
                .orElse(Collections.emptyList());
    }

    /**
     * 解析目标值字符串，去重去空
     *
     * @param valueStr 逗号分隔的值字符串
     * @return 目标值集合
     */
    private static Set<String> parseTargetValues(String valueStr) {
        return Arrays.stream(valueStr.split(COMMA))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
    }

    /**
     * 使用迭代方式查找所有目标值的路径
     * 一次遍历找到所有目标，提升性能
     *
     * @param treeData 树形数据
     * @param targets  目标值集合
     * @return 找到的路径列表，按目标值在集合中的顺序返回
     */
    private static List<List<Serializable>> findAllPaths(
            List<CascaderExtraDto> treeData,
            Set<String> targets
    ) {
        if (treeData == null || targets.isEmpty()) {
            return Collections.emptyList();
        }

        Map<String, List<Serializable>> foundPaths = new HashMap<>();
        Deque<NodeWithPath> queue = new ArrayDeque<>();

        // 初始化队列，将根节点加入
        treeData.stream()
                .filter(Objects::nonNull)
                .forEach(node -> queue.offer(new NodeWithPath(node, Collections.singletonList(node.getValue()))));

        // 迭代查找，直到队列为空或找到所有目标
        while (!queue.isEmpty() && foundPaths.size() < targets.size()) {
            NodeWithPath current = queue.poll();
            CascaderExtraDto node = current.node;
            List<Serializable> path = current.path;

            // 检查当前节点是否为目标值
            Optional.ofNullable(node.getValue())
                    .map(Object::toString)
                    .filter(targets::contains)
                    .ifPresent(nodeValue -> foundPaths.put(nodeValue, new ArrayList<>(path)));

            // 将子节点加入队列
            Optional.ofNullable(node.getChildren())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(child -> {
                        List<Serializable> newPath = new ArrayList<>(path);
                        newPath.add(child.getValue());
                        queue.offer(new NodeWithPath(child, newPath));
                    });
        }

        // 按目标值的原始顺序返回找到的路径
        return targets.stream()
                .map(foundPaths::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 将逗号分隔的字符串转换为对应的 label 文本，用逗号拼接
     *
     * @param valueStr 逗号分隔的值字符串
     * @param treeData 级联选择器的树形数据
     * @return 逗号拼接的 label 文本
     */
    public static String convertStringToCascaderLabels(
            String valueStr,
            List<CascaderExtraDto> treeData
    ) {
        if (StrUtil.isBlank(valueStr) || CollUtil.isEmpty(treeData)) {
            return null;
        }

        // 使用 flatMap 扁平化处理树形结构，构建 typeId 到 label 的映射
        Map<String, String> typeIdToLabelMap = flattenTree(treeData).stream()
                .filter(node -> node.getValue() != null)
                .collect(Collectors.toMap(
                        node -> node.getValue().toString(),
                        CascaderExtraDto::getLabel,
                        (v1, v2) -> v1 // 如果有重复的 key，保留第一个
                ));

        // 解析目标值字符串，获取所有的 typeId，并映射为对应的 label
        return Arrays.stream(valueStr.split(COMMA))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(typeIdToLabelMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(COMMA));
    }

    /**
     * 将树形结构扁平化为节点列表
     *
     * @param treeData 树形数据
     * @return 扁平化后的节点列表
     */
    private static List<CascaderExtraDto> flattenTree(List<CascaderExtraDto> treeData) {
        return treeData.stream()
                .flatMap(node -> Stream.concat(
                        Stream.of(node),
                        Optional.ofNullable(node.getChildren())
                                .map(CascaderExtraUtil::flattenTree)
                                .orElse(Collections.emptyList())
                                .stream()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 节点与路径的组合类，用于迭代查找
     */
    private static class NodeWithPath {
        final CascaderExtraDto node;
        final List<Serializable> path;

        NodeWithPath(CascaderExtraDto node, List<Serializable> path) {
            this.node = node;
            this.path = path;
        }
    }
}

