package com.fykj.scaffold.task.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 活动批量创建参数表
 * 查询参数
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "活动批量创建参数表查询参数")
public class ParameterCopyActivityParams extends BaseParams {

    private String sync;
    @ApiModelProperty(value = "任务id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parameterId;

    @MatchType(value = QueryType.LIKE, fieldName = {"name"})
    private String key;
}
