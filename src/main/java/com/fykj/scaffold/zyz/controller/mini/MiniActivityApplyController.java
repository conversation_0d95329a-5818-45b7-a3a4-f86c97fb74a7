package com.fykj.scaffold.zyz.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.ApplyAppraiseDto;
import com.fykj.scaffold.zyz.domain.dto.ApplyPageDto;
import com.fykj.scaffold.zyz.domain.dto.ApplyPublicPageDto;
import com.fykj.scaffold.zyz.domain.dto.DifferentDimensionActNumDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityApplyPublicParams;
import com.fykj.scaffold.zyz.domain.params.ZyzApplyParams;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyPublicService;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityAppraiseService;
import exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/mini/activity_apply")
@Api(tags = "微信小程序活动报名相关接口--需要登录")
public class MiniActivityApplyController {
    @Autowired
    private IZyzActivityApplyService baseService;

    @Autowired
    private IZyzActivityAppraiseService appraiseService;

    @Autowired
    private IZyzActivityApplyPublicService applyPublicService;

    @ApiOperation("活动报名（活动详情-我要报名）")
    @GetMapping({"/client_apply"})
    public Result apply(@RequestParam Long timePeriodId) {
        baseService.apply(timePeriodId);
        return OK;
    }

    @ApiOperation("取消（我的活动-取消报名）")
    @GetMapping({"/apply_cancel"})
    public Result applyCancel(@RequestParam Long applyId, @RequestParam(required = false) String cancelRemark) {
        baseService.applyCancel(applyId, cancelRemark);
        return OK;
    }

    @ApiOperation("我的活动报名分页（我的活动）")
    @PostMapping({"/my_act_apply"})
    public JsonResult<IPage<ApplyPageDto>> myActApplyPage(@RequestBody ZyzApplyParams params) {
        return new JsonResult<>(baseService.myActApplyPage(params));
    }

    @ApiOperation("我的报名分页（我的报名）")
    @PostMapping({"/my_apply"})
    public JsonResult<IPage<ApplyPageDto>> myApplyPage(@RequestBody ZyzApplyParams params) {
        return new JsonResult<>(baseService.myApplyPage(params));
    }

    @ApiOperation("我的群众报名分页（我的群众报名）")
    @PostMapping({"/my_public_apply"})
    public JsonResult<IPage<ApplyPublicPageDto>> myPublicApplyPage(
            @RequestBody ZyzActivityApplyPublicParams params) {
        return new JsonResult<>(applyPublicService.pageMyApplyPublic(params));
    }

    @ApiOperation("签到前置检查（我的活动-签到前置检查）")
    @GetMapping({"/apply_sign_check"})
    public JsonResult<Integer> applySignCheck(@RequestParam Long applyId, @RequestParam BigDecimal longitude, @RequestParam BigDecimal latitude) {
        return new JsonResult<>(baseService.applySignCheck(applyId, longitude, latitude));
    }

    @ApiOperation("签到（我的活动-报名签到）")
    @GetMapping({"/apply_sign"})
    public Result applySign(@RequestParam Long applyId, @RequestParam BigDecimal longitude, @RequestParam BigDecimal latitude) {
        baseService.applySign(applyId, longitude, latitude);
        return OK;
    }

    @ApiOperation("是否展示评价按钮 -- 小程序")
    @GetMapping("/show_appraise")
    public Result isShow(Long applyId) {
        ZyzActivityApply apply = baseService.getById(applyId);
        //判断活动结束时间是否在当前时间前
        if (apply == null) {
           throw new BusinessException(ResultCode.DATA_EXPIRED,"报名信息不存在");
        }
        if (apply.getTimePeriodEndTime().compareTo(LocalDateTime.now()) > 0) {
            return new JsonResult<>(new Result(-1, "活动未结束"));
        }
        if (apply.getSignTime() == null) {
            return new JsonResult<>(new Result(-2, "该志愿者未完成活动，无法评价"));
        }

        return new Result();
    }

    @ApiOperation("评价（我的活动-报名评价）")
    @PostMapping({"/apply_appraise"})
    public Result applyAppraise(@RequestBody ApplyAppraiseDto appraise) {
        appraiseService.applyAppraise(appraise);
        return OK;
    }

    @ApiOperation("删除（我的报名-报名删除）")
    @GetMapping({"/apply_remove"})
    public Result applyRemove(@RequestParam Long applyId) {
        baseService.applyRemove(applyId);
        return OK;
    }

    @ApiOperation("小程序-我的-服务时长积分数据")
    @PostMapping("/my_service_data")
    public JsonResult<IPage<ApplyPageDto>> myServiceData(@RequestBody ZyzApplyParams params) {
        return new JsonResult<>(baseService.myServiceData(params));
    }

    @ApiOperation("活动数量（我的/未开始/进行中/已结束）")
    @GetMapping({"/act_num_by_different_dimension"})
    public JsonResult<DifferentDimensionActNumDto> actNumByDifferentDimension() {
        return new JsonResult<>(baseService.actNumByDifferentDimension());
    }
}
