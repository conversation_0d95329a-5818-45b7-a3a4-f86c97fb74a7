package com.fykj.scaffold.portal_website.api;

import com.fykj.scaffold.portal_website.domain.entity.Banner;
import com.fykj.scaffold.portal_website.service.IBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 轮播图
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/portal_website/banner")
@Api(tags = "pc轮播图--无需登录")
public class BannerApi {
    @Autowired
    private IBannerService bannerService;

    @ApiOperation("分页查询轮播图")
    @GetMapping({"/pages"})
    public JsonResult<List<Banner>> pageForNews(@RequestParam String categoryCode) {
        List<Banner> result = bannerService.pageForApi(categoryCode);
        return new JsonResult<>(result);
    }
}
