package com.fykj.scaffold.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.capability.domain.dto.CourseSignInDTO;
import com.fykj.scaffold.capability.domain.dto.CourseSignUpDTO;
import com.fykj.scaffold.capability.domain.entity.CourseUser;
import com.fykj.scaffold.capability.domain.params.CourseUserParams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程用户管理 Mapper 接口
 * 表对应：cu_course_user
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface CourseUserMapper extends BaseMapper<CourseUser> {

    List<CourseSignUpDTO> querySignUp(IPage<CourseSignUpDTO> page, @Param("params") CourseUserParams params);

    List<CourseSignUpDTO> querySignUp(@Param("params") CourseUserParams params);

    List<CourseSignInDTO> querySignIn(IPage<CourseSignInDTO> page, @Param("params") CourseUserParams params);

    List<CourseSignInDTO> querySignIn(@Param("params") CourseUserParams params);

}
