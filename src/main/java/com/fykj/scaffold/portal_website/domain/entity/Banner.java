package com.fykj.scaffold.portal_website.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cms_banner")
public class Banner extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 标题
     */
    @TableField("title")
    @NotBlank(message = "标题不能为空")
    @ApiModelProperty(value = " 标题")
    private String title;

    /**
     * 图片
     */
    @TableField(value = "image_url", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = " 图片url")
    private String imageUrl;

    /**
     * 链接
     */
    @TableField(value = "link", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = " 链接")
    private String link;

    /**
     * 是否启用
     */
    @TableField(value = "enabled")
    @ApiModelProperty(value = " 是否启用")
    private Boolean enabled;

    /**
     * 开始时间
     */
    @TableField(value = "start_time", updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = Cons.DATE_FORMAT)  //格式化前台日期参数注解
    @DateTimeFormat(pattern = Cons.DATE_FORMAT)
    @ApiModelProperty(value = " 开始时间")
    private LocalDate startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time", updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = Cons.DATE_FORMAT)  //格式化前台日期参数注解
    @DateTimeFormat(pattern = Cons.DATE_FORMAT)
    @ApiModelProperty(value = " 结束时间")
    private LocalDate endTime;

    /**
     * 是否上架
     */
    @ApiModelProperty(value = "是否上架")
    @TableField("grounding")
    private Boolean grounding;

    /**
     * 栏目id
     */
    @ApiModelProperty(value = "栏目id")
    @NotEmpty(message = "栏目不能为空")
    @TableField(exist = false)
    private List<String> categoryIds;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(exist = false)
    private String category;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;
}
