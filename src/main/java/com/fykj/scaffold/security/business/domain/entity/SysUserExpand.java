package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> @email ${email}
 * @date 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_user_expand")
public class SysUserExpand extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 角色类型
     */
    @TableField("role_code")
    @ApiModelProperty(value = "角色类型")
    private String roleCode;

    @TableField(exist = false)
    private String roleName;

    /**
     * 组织code（一般是分协会，社区）
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织code（一般是分协会，社区）")
    private String orgCode;

    /**
     * 团队ID
     */
    @TableField("team_id")
    @ApiModelProperty(value = "团队ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    @TableField(exist = false)
    private String deptName;

    /**
     * 是否默认
     */
    @TableField("is_default_setting")
    @ApiModelProperty(value = "是否默认")
    private Boolean defaultSetting;

    @TableField(exist = false)
    private String capacityFullName;

    @TableField(exist = false)
    @ApiModelProperty(value = "团队信息是否需要完善")
    private Boolean teamNeedPerfectFlag;

}
