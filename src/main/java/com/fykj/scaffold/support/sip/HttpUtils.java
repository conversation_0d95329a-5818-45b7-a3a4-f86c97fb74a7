package com.fykj.scaffold.support.sip;;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.util.Map;

/**
 * @ClassName HttpUtils
 * <AUTHOR>
 * @Date 2023/5/9 8:52
 * @Description
 * @Version 1.0
 */
@Slf4j
public class HttpUtils {
    // 默认字符集
    private static final String encoding = "utf-8";

    public static String sendPost(String url, Map<String, String> headers, JSONObject data, String encoding) {
        long now = System.currentTimeMillis();
        log.info("进入post请求方法,now:{}...", now);
        log.info("请求入参：URL= " + url);
        log.info("请求入参：headers=" + JSON.toJSONString(headers));
        log.info("请求入参：data=" + JSON.toJSONString(data));
        // 创建Client
        CloseableHttpClient client = HttpClients.createDefault();
        // 创建HttpPost对象
        HttpPost httpPost = new HttpPost();
        try {
            // 设置请求地址
            httpPost.setURI(new URI(url));
            // 设置请求头
            if (headers != null) {
                Header[] allHeader = new BasicHeader[headers.size()];
                int i = 0;
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    allHeader[i] = new BasicHeader(entry.getKey(), entry.getValue());
                    i++;
                }
                httpPost.setHeaders(allHeader);
            }
            // 设置实体
            if (data != null) {
                httpPost.setEntity(new StringEntity(JSON.toJSONString(data), encoding));
            }
            // 发送请求,返回响应对象
            CloseableHttpResponse response = client.execute(httpPost);
            log.info("进入post请求成功,耗时:{}...", System.currentTimeMillis() - now);
            return parseData(response);
        } catch (Exception e) {
            log.error("发送post请求失败", e);
        } finally {
            httpPost.releaseConnection();
        }
        return null;
    }

    /**
     * 解析response
     *
     * @param response
     * @return
     * @throws Exception
     */
    public static String parseData(CloseableHttpResponse response) throws Exception {
        // 获取响应状态
        int status = response.getStatusLine().getStatusCode();
        if (status == org.apache.http.HttpStatus.SC_OK) {
            // 获取响应数据
            return EntityUtils.toString(response.getEntity(), encoding);
        } else {
            log.error("响应失败，状态码：" + response.toString());
            log.error("响应失败，状态码：" + status);
        }
        return null;
    }
}
