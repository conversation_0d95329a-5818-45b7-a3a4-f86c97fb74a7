package com.fykj.scaffold.sync.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 市平台地域数据字典_所有的（苏州大市）
 * 查询参数
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "市平台地域数据字典_所有的（苏州大市）查询参数")
public class ZyzSyncRegionDictAllParams extends BaseParams {

}
