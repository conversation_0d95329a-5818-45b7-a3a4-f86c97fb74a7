<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.AnnualAccountSumMapper">

    <select id="sumVolunteerNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0
        <if test="star != null and star == true">
            <if test="fiveStar != null and fiveStar == true">
                AND service_long &gt;= 1500
            </if>
            <if test="fiveStar != null and fiveStar == false">
                AND service_long &gt;= 100
            </if>
        </if>
    </select>

    <select id="sumActNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_activity
        WHERE is_deleted = 0 AND audit_status = 'act_audit_success'
        <if test="start != null">
            AND start_time &gt;= #{start}
        </if>
        <if test="fields != null and fields.size() > 0">
            AND belong_field_top IN
            <foreach item="item" collection="fields" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="sumServiceTime" resultType="java.math.BigDecimal">
        SELECT
        <if test="thisYear == false">
            SUM(IFNULL(service_long, 0))
        </if>
        <if test="thisYear == true">
            SUM(IFNULL(service_long_this_year, 0))
        </if>
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0
    </select>

    <select id="sumTeamNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_team
        WHERE is_deleted = 0 AND team_status = 'team_audit_pass'
    </select>
    <select id="sumActServiceLong" resultType="java.math.BigDecimal">
        SELECT sum(service_long)
        FROM zyz_activity_apply p
        JOIN zyz_activity a on p.activity_id=a.id AND a.is_deleted = 0
        WHERE p.is_deleted = 0 AND p.sign_time is not null
        <if test="start != null">
            AND p.time_period_start_time &gt;= #{start}
        </if>
        <if test="fields != null and fields.size() > 0">
            AND a.belong_field_top IN
            <foreach item="item" collection="fields" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
