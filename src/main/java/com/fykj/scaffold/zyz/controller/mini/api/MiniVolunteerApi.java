package com.fykj.scaffold.zyz.controller.mini.api;

import cn.hutool.crypto.SecureUtil;
import com.fykj.scaffold.portal_website.domain.dto.VideoCategoryDto;
import com.fykj.scaffold.portal_website.service.IVideoCategoryService;
import com.fykj.scaffold.security.business.service.ICaptchaService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamGrantBigActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzTeamGrantBigActivityService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/mini/volunteer")
@Api(tags = "微信小程序志愿者接口--无需登录")
public class MiniVolunteerApi {

    @Autowired
    private IVideoCategoryService videoCategoryService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ICaptchaService captchaService;

    @Autowired
    private IZyzTeamGrantBigActivityService teamGrantBigActivityService;

    //实名认证二次校验的url
    final static String URL = "https://api.weixin.qq.com/cityservice/face/identify/getinfo";

    //密钥
    final static String KEY = "f3e2f1d0e1d0f2e3";


    @Autowired
    private RedisService redisService;

    private final static String REDIS_FACE_IDENTIFY_RESULT_KEY = "REDIS_FACE_IDENTIFY_RESULT_KEY";

    @ApiOperation("获取培训视频--小程序端")
    @GetMapping("/getTrainVideo")
    public JsonResult<List<VideoCategoryDto>> getTrainVideoForMini() {
        return new JsonResult<>(videoCategoryService.getTrainVideoForMini());
    }


    @ApiOperation("根据经纬度获取所属社区")
    @GetMapping("/getOrgCodeByLocation")
    public Result getOrgCodeByLocation(BigDecimal longitude, BigDecimal latitude) {
        return new JsonResult<>(volunteerService.getOrgByLonLat(longitude.doubleValue(), latitude.doubleValue()));
    }

    @ApiOperation("根据经纬度获取所属社区市区编码")
    @GetMapping("/getRegionCodeByLocation")
    public Result getRegionCodeByLocation(BigDecimal longitude, BigDecimal latitude) {
        return new JsonResult<>(volunteerService.getRegionCodeByLonLat(longitude.doubleValue(), latitude.doubleValue()));
    }
    @ApiOperation("step2.PC使用小程序能力人脸识别，小程序人脸通过后，把加密的识别结果放到redis，PC轮询来取")
    @GetMapping("/pcFaceIdentify")
    public Result pcFaceIdentify(String verifyResult, String requestUid) {
        //把人脸核验串放到redis中，等前端来取走
        redisService.set(REDIS_FACE_IDENTIFY_RESULT_KEY + requestUid, verifyResult, 5 * 60);
        return new Result();
    }


    @ApiOperation("验证志愿者证书")
    @GetMapping("/verifyVolunteerCertification")
    public Result verifyVolunteerCertification(String name, String certificationId, String certificationType, String captchaUuid, String captcha) {
        if (!captchaService.validate(captchaUuid, captcha)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "验证码错误，请重新输入");
        }
        return new JsonResult<>(volunteerService.lambdaQuery().eq(ZyzVolunteer::getName, name)
                .eq(ZyzVolunteer::getCertificateId, certificationId)
                .eq(ZyzVolunteer::getCertificateType, certificationType)
                .eq(ZyzVolunteer::getWriteOff, false)
                .exists());
    }

    @ApiOperation("公文 -- 获取志愿者信息")
    @GetMapping("/getVolunteerInfo")
    public Result getVolunteerInfo(String phone, String certificationId, String nonceStr) {
        if (!SecureUtil.md5(phone + certificationId + KEY).equals(nonceStr)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "非法请求");
        }
        ZyzVolunteer volunteer = volunteerService.lambdaQuery().eq(ZyzVolunteer::getPhone, phone).eq(ZyzVolunteer::getWriteOff, false).one();
        if (volunteer == null) {
            throw new BusinessException(ResultCode.DATA_EXPIRED, "志愿者不存在");
        }
        if (!volunteer.getCertificateId().equals(certificationId)) {
            throw new BusinessException(ResultCode.DATA_EXPIRED, "志愿者身份信息不正确");
        }
        return new JsonResult<>(volunteer);

    }

    @ApiOperation("大型赛事活动-志愿者注册-选择团队下拉")
    @GetMapping("/getGrantedTeam")
    public JsonResult<List<ZyzTeamGrantBigActivity>> getGrantedTeam(@RequestParam(required = false) String teamName) {
        return new JsonResult<>(teamGrantBigActivityService.getGrantedTeam(teamName));
    }

}

