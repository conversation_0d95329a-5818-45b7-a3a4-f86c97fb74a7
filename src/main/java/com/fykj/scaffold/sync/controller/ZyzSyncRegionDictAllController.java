package com.fykj.scaffold.sync.controller;

import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDictAll;
import com.fykj.scaffold.sync.domain.params.ZyzSyncRegionDictAllParams;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictAllService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 市平台地域数据字典_所有的（苏州大市）
 *
 * 前端控制器
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/admin/zyz/sync/region/dict/all")
@Api(tags = " 市平台地域数据字典_所有的（苏州大市）接口")
public class ZyzSyncRegionDictAllController extends BaseController<IZyzSyncRegionDictAllService, ZyzSyncRegionDictAll,ZyzSyncRegionDictAllParams> {

}
