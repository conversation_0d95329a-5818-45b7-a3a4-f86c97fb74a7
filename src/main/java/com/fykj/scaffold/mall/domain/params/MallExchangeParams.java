package com.fykj.scaffold.mall.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("兑换记录查询参数")
public class MallExchangeParams extends BaseParams {
    private String keyWord;
    @MatchType(value = QueryType.EQ, fieldName = "status")
    private String status;

    @MatchType(value = QueryType.EQ, fieldName = "org_code")
    private String orgCode;
    @MatchType(value = QueryType.EQ, fieldName = "exchange_type")
    private String exchangeType;

    private String machineNo;

    private String stationCode;

    private String emsStatus;

    private Long userId;

    private String orderNum;


    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime startTime;


    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime endTime;
}
