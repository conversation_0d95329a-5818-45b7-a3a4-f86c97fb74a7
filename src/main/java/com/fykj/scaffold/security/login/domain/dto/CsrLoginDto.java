package com.fykj.scaffold.security.login.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：yangxu
 * @Date：2025/5/29 14:36
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CsrLoginDto implements Serializable {

    private static final long serialVersionUID = -2179577610302109624L;

    private String loginType;

    private String username;

    private String password;
}
