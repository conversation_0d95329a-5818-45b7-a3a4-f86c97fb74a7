package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 部门单位查询响应实体
 */
@Data
public class DeptResponse {
    
    /**
     * 响应信息列表
     */
    private List<DeptInfo> info;
    
    /**
     * 部门单位信息实体
     */
    @Data
    public static class DeptInfo {
        /**
         * 响应状态码
         * SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 部门单位编号
         */
        private String depId;
        
        /**
         * 部门单位名称
         */
        private String depName;
        
        /**
         * 所属区划代码
         */
        private String regionCode;
    }
} 