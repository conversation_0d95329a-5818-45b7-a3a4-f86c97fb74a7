package com.fykj.scaffold.support.prop_compare;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class PropCompareUtil {

    public static <T> boolean judgePropInfoChange(T oldObj, T newObj){
        if (ObjectUtil.isEmpty(oldObj) || ObjectUtil.isEmpty(newObj)) {
            throw new BusinessException(ResultCode.FAIL, "比较的两个对象须均不为空！");
        }
        List<Field> fieldList = getNeedCompareFields(oldObj.getClass());
        if (CollectionUtil.isEmpty(fieldList)) {
            return false;
        }
        boolean flag = false;
        for(Field field : fieldList) {
            try {
                field.setAccessible(true);
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);
                if (!ObjectUtil.equals(oldValue, newValue)) {
                    flag = true;
                    break;
                }
            } catch (Exception e) {
                throw new BusinessException(ResultCode.FAIL, "属性【" + field.getName() + "】值获取异常！");
            }
        }
        return flag;
    }

    public static <T> List<PropChangeInfo> getChangePropInfo(T oldObj, T newObj){
        if (ObjectUtil.isEmpty(oldObj) || ObjectUtil.isEmpty(newObj)) {
            throw new BusinessException(ResultCode.FAIL, "比较的两个对象须均不为空！");
        }
        List<Field> fieldList = getNeedCompareFields(oldObj.getClass());
        if (CollectionUtil.isEmpty(fieldList)) {
            return null;
        }
        List<PropChangeInfo> changePropsInfo = new ArrayList<>();
        for(Field field : fieldList) {
            try {
                field.setAccessible(true);
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);
                if (!ObjectUtil.equals(oldValue, newValue)) {
                    PropChangeInfo info = new PropChangeInfo();
                    info.setPropName(field.getName());
                    info.setPropRemark(field.getAnnotation(PropCompare.class).name());
                    info.setOldValue(oldValue);
                    info.setNewValue(newValue);
                    changePropsInfo.add(info);
                }
            } catch (Exception e) {
                throw new BusinessException(ResultCode.FAIL, "属性【" + field.getName() + "】值获取异常！");
            }
        }
        return changePropsInfo;
    }

    private static <T> List<Field> getNeedCompareFields(Class<T> tClass) {
        Field[] fields = tClass.getDeclaredFields();
        return Arrays.stream(fields)
                .filter(field -> ObjectUtil.isNotEmpty(field.getAnnotation(PropCompare.class)))
                .collect(Collectors.toList());
    }
}
