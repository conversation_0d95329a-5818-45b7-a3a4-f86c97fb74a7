package com.fykj.scaffold.support.oss.impl;

import com.fykj.scaffold.support.minio.MinioService;
import com.fykj.scaffold.support.oss.FilePathDto;
import com.fykj.scaffold.support.oss.IOssSaver;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import com.fykj.scaffold.support.utils.ContentTypeUtil;
import constants.Mark;
import io.minio.ObjectWriteResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Minio对象存储实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component(OssCons.OSS_IMPL_BEAN_PREFIX + OssCons.OSS_MINIO)
public class MinioOssSaver implements IOssSaver {

    @Autowired
    private MinioService minioService;

    @Override
    public FilePathDto save(InputStream is, String filename) {
        String ext = OssSaveUtil.getExt(filename);
        String filepath = buildFilepathAndDir(ext);
        String contentType = ContentTypeUtil.getContentType(ext);
        ObjectWriteResponse response = minioService.uploadFile(filepath, is, contentType);
        log.info("minio上传文件{}", response);
        return buildFilePathDto(filepath);
    }

    @Override
    public FilePathDto save(MultipartFile file) {
        String ext = OssSaveUtil.getExt(file.getOriginalFilename());
        String filepath = buildFilepathAndDir(ext);
        ObjectWriteResponse response = minioService.uploadFile(file, filepath);
        log.info("minio上传文件{}", response);
        return buildFilePathDto(filepath);
    }

    private FilePathDto buildFilePathDto(String filepath) {
        String baseUrl = minioService.getBasisUrl();
        FilePathDto result = new FilePathDto();
        result.setUrl(baseUrl + filepath);
        result.setRealPath(baseUrl + filepath);
        return result;
    }


    private String buildFilepathAndDir(String ext) {
        minioService.initMinioClient();
        String virtualPath = ext + MinioService.SEPARATOR + LocalDate.now();
        return virtualPath + MinioService.SEPARATOR + UUID.randomUUID() + Mark.DOT + ext;
    }

}
