package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * SSO用户与平台用户关联关系-实体类
 * Date: 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sso_user_mapping")
@ApiModel("SSO用户与平台用户关联关系-实体类")
public class SsoUserMapping extends BaseEntity {

	private static final long serialVersionUID = 5316899435396877341L;

	@TableField("sso_provider")
    @ApiModelProperty(value = "SSO提供商(如:wechat_work,dingtalk等)")
	private String ssoProvider;

	@TableField("user_id")
    @ApiModelProperty(value = "SSO系统中的用户唯一标识(如企业微信的userid)")
	private String userId;

	@TableField("platform_user_id")
    @ApiModelProperty(value = "平台用户ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long platformUserId;

	@TableField("last_login_time")
    @ApiModelProperty(value = "最后一次通过SSO登录的时间")
	@JsonFormat(pattern = DATETIME_FORMAT)
	@DateTimeFormat(pattern = DATETIME_FORMAT)
	private LocalDateTime lastLoginTime;

	@TableField("raw_user_info")
    @ApiModelProperty(value = "原始用户数据(完整JSON)")
	private String rawUserInfo;

}
