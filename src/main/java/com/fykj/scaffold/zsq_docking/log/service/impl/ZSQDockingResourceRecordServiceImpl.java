package com.fykj.scaffold.zsq_docking.log.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.zsq_docking.log.domain.dto.ResourceDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingLog;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingMqFail;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingResourceRecord;
import com.fykj.scaffold.zsq_docking.log.mapper.ZSQDockingResourceRecordMapper;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingLogService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingMqFailService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingResourceRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.event.ResourceDockingEvent;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ResourceDockingDto;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import joptsimple.internal.Strings;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import result.Result;
import result.ResultCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;

@Service
@Transactional(rollbackFor = Exception.class)
public class ZSQDockingResourceRecordServiceImpl extends BaseServiceImpl<ZSQDockingResourceRecordMapper, ZSQDockingResourceRecord> implements IZSQDockingResourceRecordService {

    @Autowired
    private IZSQDockingLogService dockingLogService;

    @Autowired
    private IZSQDockingMqFailService dockingMqFailService;

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Override
    public List<ZSQDockingResourceRecord> getResourceDockingRecords(Long resourceId) {
        return lambdaQuery().eq(ZSQDockingResourceRecord::getResourceId, resourceId)
                .orderByDesc(ZSQDockingResourceRecord::getDockingTime)
                .list();
    }

    public void retry(Long resourceId, Long recordId) {
        ZSQDockingResourceRecord last = lambdaQuery().eq(ZSQDockingResourceRecord::getResourceId, resourceId)
                .orderByDesc(ZSQDockingResourceRecord::getDockingTime)
                .last("limit 1")
                .one();
        if (last == null || !recordId.equals(last.getId())) {
            throw new BusinessException(ResultCode.FAIL, "仅能对资源数据的最会一条对接记录进行失败重试！");
        }
        if (!DOCKING_RECORD_STATUS_FAIL.equals(last.getDockingStatus())) {
            throw new BusinessException(ResultCode.FAIL, "资源数据的最会一条对接记录对接成功，无需重试！");
        }
        String dockingDataStr = last.getDockingData();
        JSONObject dockingDataObj;
        try {
            dockingDataObj = StringUtils.isEmpty(dockingDataStr) ? null : JSONObject.parseObject(dockingDataStr);
        } catch (Exception e) {
            log.error("知社区资源对接重试时获取对接数据异常!", e);
            dockingDataObj = null;
        }
        if (dockingDataObj == null) {
            throw new BusinessException(ResultCode.FAIL, "对接重试时获取原始对接数据异常，无法进行重试！");
        }
        String dockingType = last.getDockingType();
        if (StringUtils.isEmpty(dockingType)) {
            throw new BusinessException(ResultCode.FAIL, "对接重试时未获取到对接类型，无法进行重试！");
        }
        if (!DOCKING_TYPE_PUSH.equals(dockingType) && !DOCKING_TYPE_RECEIVE.equals(dockingType)) {
            throw new BusinessException(ResultCode.FAIL, "对接重试时获取到未知的对接类型，无法进行重试！");
        }
        ZSQDockingResourceRecord dockingRecord = new ZSQDockingResourceRecord();
        dockingRecord.setResourceId(resourceId);
        dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_PENDING);
        dockingRecord.setDockingTime(LocalDateTime.now());
        dockingRecord.setRetry(Boolean.TRUE);
        dockingRecord.setDockingType(dockingType);
        dockingRecord.setDockingOperation(last.getDockingOperation());
        super.save(dockingRecord);
        Long dockingRecordId = dockingRecord.getId();
        if (DOCKING_TYPE_PUSH.equals(dockingType)) {
            ResourceDockingEvent event;
            try {
                event = dockingDataObj.toJavaObject(ResourceDockingEvent.class);
                if (event == null) {
                    throw new BusinessException(ResultCode.FAIL, "知社区对接（推送类型）重试时原始对接数据为空，无法进行重试！");
                }
            } catch (Exception e) {
                throw new BusinessException(ResultCode.FAIL, "知社区对接（推送类型）重试时原始对接数据异常，无法进行重试！");
            }
            event.setDockingRecordId(dockingRecordId);
            String zwtUserId = event.getZwtUserId();
            Serializable zyzUserId = event.getZyzUserId();
            if (StringUtils.isEmpty(zwtUserId) && zyzUserId != null) {
                zwtUserId = ssoUserMappingService.getSSOUserIdByProviderAndZYZUserId(SsoCons.SsoProvider.ZWT.name(), (Long) zyzUserId);
                if (StringUtils.isNotEmpty(zwtUserId)) {
                    event.setZwtUserId(zwtUserId);
                }
            }
            if (StringUtils.isEmpty(zwtUserId)) {
                dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                dockingRecord.setDockingMsg("操作人" + (zyzUserId == null ? Strings.EMPTY : "{" + zyzUserId + "}") + "未获取政务通 userId ！");
                dockingRecord.setDockingData(JSONObject.toJSONString(event));
                super.updateById(dockingRecord);
                return;
            }
            dockingRecord.setDockingData(JSONObject.toJSONString(event));
            super.updateById(dockingRecord);
            mqTemplate.send(TOPIC_RESOURCE_DOCKING, MessageBuilder.withPayload(event).build());
            return;
        }
        ResourceDockingDto dockingInfo;
        try {
            dockingInfo = dockingDataObj.toJavaObject(ResourceDockingDto.class);
            if (dockingInfo == null) {
                throw new BusinessException(ResultCode.FAIL, "知社区对接（接收类型）重试时原始对接数据为空，无法进行重试！");
            }
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "知社区对接（接收类型）重试时原始对接数据异常，无法进行重试！");
        }
        dockingInfo.setDockingRecordId(dockingRecordId);
        dockingRecord.setDockingData(JSONObject.toJSONString(dockingInfo));
        super.updateById(dockingRecord);

    }

    @Override
    public ResourceDockingRecordDetailDto getDockingRecordDetail(Long recordId) {
        ResourceDockingRecordDetailDto result = new ResourceDockingRecordDetailDto();
        ZSQDockingResourceRecord record = super.getById(recordId);
        BeanUtil.copyProperties(record, result);
        String dockingDataStr = record.getDockingData();
        result.setDockingDataStr(dockingDataStr);
        try {
            JSONObject dockingDataObj = JSON.parseObject(dockingDataStr);
            result.setDockingDataObj(dockingDataObj);
        } catch (Exception e) {
            log.error("知社区资源对接记录详情-原始对接数据对象转换异常！", e);
            result.setDockingDataObj(null);
        }
        ZSQDockingLog dockingLog = dockingLogService.lambdaQuery().eq(ZSQDockingLog::getDockingRecordId, recordId).one();
        ZSQDockingMqFail dockingMqFail = dockingMqFailService.lambdaQuery().eq(ZSQDockingMqFail::getDockingRecordId, recordId).one();
        if (dockingLog != null) {
            JSONObject dockingLogObj = new JSONObject(new ConcurrentHashMap<>());
            dockingLogObj.put("日志id", dockingLog.getId());
            if (DOCKING_TYPE_PUSH.equals(record.getDockingType())) {
                dockingLogObj.put("MQ消息id", dockingLog.getDockingPushId());
            }
            dockingLogObj.put("日志时间", dockingLog.getDockingTime() != null ? dockingLog.getDockingTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT)) : null);
            dockingLogObj.put("对接结果", dockingLog.getDockingResult() ? "对接成功" : "对接失败");
            String dockingSummaryStr = dockingLog.getDockingSummary();
            try {
                JSONObject dockingSummaryObj = JSONObject.parseObject(dockingSummaryStr, Feature.OrderedField);
                dockingLogObj.put("日志内容", dockingSummaryObj);
            } catch (Exception e) {
                log.error("知社区资源对接日志详情-日志内容对象转换异常！", e);
                dockingLogObj.put("日志内容", dockingSummaryStr);
            }
            String dockingRequestDataStr = dockingLog.getDockingRequestData();
            try {
                JSONObject dockingRequestDataObj = JSONObject.parseObject(dockingRequestDataStr, Feature.OrderedField);
                dockingLogObj.put("对接元数据", dockingRequestDataObj);
            } catch (Exception e) {
                log.error("知社区资源对接日志详情-对接元数据对象转换异常！", e);
                dockingLogObj.put("对接元数据", dockingRequestDataStr);
            }
            String dockingResponseDataStr = dockingLog.getDockingResponseData();
            try {
                JSONObject dockingResponseDataObj = JSONObject.parseObject(dockingResponseDataStr, Feature.OrderedField);
                dockingLogObj.put("对接响应数据", dockingResponseDataObj);
            } catch (Exception e) {
                log.error("知社区资源对接日志详情-对接响应数据对象转换异常！", e);
                dockingLogObj.put("对接响应数据", dockingResponseDataStr);
            }
            result.setDockingLog(dockingLogObj);
        }
        if (dockingMqFail != null && DOCKING_TYPE_PUSH.equals(record.getDockingType())) {
            JSONObject dockingMqFailObj = new JSONObject();
            dockingMqFailObj.put("MQ消费失败日志id", dockingMqFail.getId());
            dockingMqFailObj.put("MQ消息id", dockingMqFail.getMessageId());
            dockingMqFailObj.put("MQ消费失败时间", dockingMqFail.getMsgTime());
            String contentStr = dockingMqFail.getContent();
            try {
                JSONObject contentObj = JSONObject.parseObject(contentStr);
                dockingMqFailObj.put("MQ消费数据", contentObj);
            } catch (Exception e) {
                log.error("知社区资源对接MQ消费失败日志详情-MQ消费数据对象转换异常！", e);
                dockingMqFailObj.put("MQ消费数据", contentStr);
            }
            dockingMqFailObj.put("MQ消费失败原因", dockingMqFail.getExceptionTrace());
            result.setDockingMqFailLog(dockingMqFailObj);
        }
        SerializeConfig config = new SerializeConfig();
        config.put(Long.class, ToStringSerializer.instance);
        return JSON.parseObject(JSON.toJSONString(result, config), ResourceDockingRecordDetailDto.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateDockingRecord(ZSQDockingResourceRecord record, Result result) {
        if (record == null) {
            log.error("知社区资源对接-资源接收记录更新失败，失败原因：接收记录为 NULL ！");
            return;
        }
        if (result == null) {
            record.setDockingMsg("知社区资源对接-资源接收记录更新，对接结果获取为 NULL ！");
            super.updateById(record);
            return;
        }
        record.setDockingStatus(result.isSuccess() ? DOCKING_RECORD_STATUS_SUCCESS : DOCKING_RECORD_STATUS_FAIL);
        record.setDockingMsg(result.getMsg());
        super.updateById(record);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateDockingRecord(Long recordId, Result result) {
        ZSQDockingResourceRecord record = super.getById(recordId);
        updateDockingRecord(record, result);
    }
}
