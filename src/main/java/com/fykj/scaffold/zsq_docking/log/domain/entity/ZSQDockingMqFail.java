package com.fykj.scaffold.zsq_docking.log.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 知社区对接推送mq消费失败记录表
 *
 * <AUTHOR> @email ${email}
 * @date 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zsq_docking_mq_fail")
public class ZSQDockingMqFail extends BaseEntity {

    private static final long serialVersionUID = 9212634362781250021L;

    /**
     * 对接记录 id
     */
    @TableField("docking_record_id")
    @ApiModelProperty(value = "对接记录 id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dockingRecordId;

    /**
     * mq消息id
     */
    @TableField("message_id")
    @ApiModelProperty(value = "mq消息id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long messageId;

    /**
     * 消息时间
     */
    @TableField("msg_time")
    @ApiModelProperty(value = "消息时间")
    private LocalDateTime msgTime;

    /**
     * 消息体
     */
    @TableField("content")
    @ApiModelProperty(value = "消息体")
    private String content;

    /**
     * 异常堆栈
     */
    @TableField("exception_trace")
    @ApiModelProperty(value = "异常堆栈")
    private String exceptionTrace;
}
