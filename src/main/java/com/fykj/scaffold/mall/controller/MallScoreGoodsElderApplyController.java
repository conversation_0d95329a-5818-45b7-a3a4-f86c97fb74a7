package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsElderApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsElderApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsElderApplyParams;
import com.fykj.scaffold.mall.service.IMallScoreGoodsElderApplyService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 星光闪耀适老化改造福申请表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2024-10-16
 */
@RestController
@RequestMapping("/admin/mall/score/goods/elder/apply")
@Api(tags = "适老化改造报名表接口")
public class MallScoreGoodsElderApplyController extends BaseController<IMallScoreGoodsElderApplyService, MallScoreGoodsElderApply, MallScoreGoodsElderApplyParams> {

    @AuditLog("报名记录分页查询")
    @ApiOperation("报名记录分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallScoreGoodsElderApplyDto>> getPages(@RequestBody MallScoreGoodsElderApplyParams params) {
        IPage<MallScoreGoodsElderApplyDto> page = baseService.getPage(params);
        return new JsonResult<>(page);
    }


    @AuditLog("提交记录导出数据到Excel")
    @PostMapping("/exportApply")
    @ApiOperation("提交记录导出数据到Excel")
    public void exportApply(@RequestBody MallScoreGoodsElderApplyParams params) {
        params.setPageSize(-1);
        IPage<MallScoreGoodsElderApplyDto> page = baseService.getPage(params);
        List<MallScoreGoodsElderApplyDto> result = page.getRecords();
        DictTransUtil.trans(result);
        ExcelUtil.fillExcel(result, "elder-apply-export.xlsx", MallScoreGoodsElderApplyDto.class);
    }

    @AuditLog("提交")
    @PostMapping("/apply")
    @ApiOperation("提交")
    public Result apply(@RequestBody MallScoreGoodsElderApply entity) {
        boolean result = this.baseService.saveOrUpdateApply(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @AuditLog("获取报名信息")
    @GetMapping("/getApplyInfo")
    @ApiOperation("获取报名信息")
    public Result getApplyInfo(@RequestParam Long exchangeId) {
        return new JsonResult<>(baseService.getApplyInfo(exchangeId));
    }
}
