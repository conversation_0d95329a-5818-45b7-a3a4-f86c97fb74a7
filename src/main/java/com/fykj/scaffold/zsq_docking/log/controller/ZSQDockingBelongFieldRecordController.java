package com.fykj.scaffold.zsq_docking.log.controller;


import com.fykj.scaffold.zsq_docking.log.domain.dto.BelongFieldDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.dto.NewsDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingBelongFieldRecord;
import com.fykj.scaffold.zsq_docking.log.domain.params.ZSQDockingBelongFieldRecordParams;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingBelongFieldRecordService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

@RestController
@RequestMapping("/admin/zsq_docking_belong_field_record")
@Api(tags = "知社区所属领域对接记录")
public class ZSQDockingBelongFieldRecordController extends BaseController<IZSQDockingBelongFieldRecordService, ZSQDockingBelongFieldRecord, ZSQDockingBelongFieldRecordParams> {


    @ApiOperation("获取所属领域对接记录方法")
    @GetMapping({"/getBelongFieldDockingRecords"})
    public JsonResult<List<ZSQDockingBelongFieldRecord>> getBelongFieldDockingRecords() {
        return new JsonResult<>(this.baseService.getBelongFieldDockingRecords());
    }
    @ApiOperation("失败重试")
    @GetMapping({"/retry"})
    public Result retry( @RequestParam Long recordId) {
        baseService.retry( recordId);
        return OK;
    }

    @ApiOperation("获取对接记录详情")
    @GetMapping({"/getDockingRecordDetail"})
    public JsonResult<BelongFieldDockingRecordDetailDto> getDockingRecordDetail(@RequestParam Long recordId) {
        return new JsonResult<>(baseService.getDockingRecordDetail(recordId));
    }
}
