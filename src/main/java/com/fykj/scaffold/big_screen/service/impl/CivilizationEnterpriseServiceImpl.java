package com.fykj.scaffold.big_screen.service.impl;

import com.fykj.scaffold.big_screen.domain.entity.CivilizationEnterprise;
import com.fykj.scaffold.big_screen.mapper.CivilizationEnterpriseMapper;
import com.fykj.scaffold.big_screen.service.ICivilizationEnterpriseService;
import com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationEnterpriseDto;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;
/**
 * <p>
 * 风采服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class CivilizationEnterpriseServiceImpl extends BaseServiceImpl<CivilizationEnterpriseMapper, CivilizationEnterprise> implements ICivilizationEnterpriseService {


    @Override
    public void statusChange(Long id) {
        CivilizationEnterprise ce = getById(id);
        ce.setStatus(!ce.getStatus());
        super.updateById(ce);
    }

    @Override
    public List<CivilizationEnterpriseDto> getScreenDataList(DataScreenParams params) {
        return baseMapper.getScreenDataList(params);
    }
}
