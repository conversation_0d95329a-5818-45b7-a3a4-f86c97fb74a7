package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallExchangeDistributionReport;
import com.fykj.scaffold.mall.domain.params.MallExchangeDistributionReportParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-06-05
 */
public interface MallExchangeDistributionReportMapper extends BaseMapper<MallExchangeDistributionReport> {
    IPage<MallExchangeDistributionReport> getPage(IPage<MallExchangeDistributionReport> page, @Param("params") MallExchangeDistributionReportParams params);
    List<MallExchangeDistributionReport> getPage(@Param("params") MallExchangeDistributionReportParams params);

}
