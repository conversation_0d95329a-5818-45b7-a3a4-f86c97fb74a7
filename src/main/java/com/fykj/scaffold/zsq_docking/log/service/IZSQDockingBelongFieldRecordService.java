package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.dto.BelongFieldDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingBelongFieldRecord;
import fykj.microservice.core.base.IBaseService;
import result.Result;

import java.util.List;

public interface IZSQDockingBelongFieldRecordService extends IBaseService<ZSQDockingBelongFieldRecord> {

    /**
     * 所属领域对接记录查询
     *
     * @return
     */
    List<ZSQDockingBelongFieldRecord> getBelongFieldDockingRecords();
    /**
     * 失败重试
     * @param recordId
     */
    void retry( Long recordId);
    /**
     * 获取对接记录详情
     * @param recordId
     * @return
     */
    BelongFieldDockingRecordDetailDto getDockingRecordDetail(Long recordId);
    /**
     * 更新对接记录
     *
     * @param record
     * @param result
     */
    void updateDockingRecord(ZSQDockingBelongFieldRecord record, Result result);

    /**
     * 更新对接记录
     *
     * @param recordId
     * @param result
     */
    void updateDockingRecord(Long recordId, Result result);


}
