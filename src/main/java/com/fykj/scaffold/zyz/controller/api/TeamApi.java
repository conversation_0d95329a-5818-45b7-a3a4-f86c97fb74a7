package com.fykj.scaffold.zyz.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.TeamApiDto;
import com.fykj.scaffold.zyz.domain.dto.TeamStatDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamType;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzTeamTypeService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.beans.vo.IdTextVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 团队微信前端接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/zyz/team")
@Api(tags = "小程序--团队无需登录前端接口")
public class TeamApi {
    @Autowired
    private IZyzTeamService teamService;
    @Autowired
    private IZyzTeamTypeService teamTypeService;
    @Autowired
    private ISysOrgService orgService;
    @Autowired
    private IZyzVolunteerService volunteerService;
    @Autowired
    private IZyzVolunteerTeamService volunteerTeamService;

    @ApiOperation("分页查询团队")
    @PostMapping({"/pages"})
    public JsonResult<IPage<TeamApiDto>> pageForNews(@RequestBody ZyzTeamParams params) {
        IPage<TeamApiDto> result = teamService.pageForApi(params);
        return new JsonResult<>(result);
    }

    @ApiOperation(value = "获取团队类型列表")
    @GetMapping(value = "/teamTypeList")
    public Result teamTypeList() {
        List<ZyzTeamType> typeList = teamTypeService.getList();
        return new JsonResult<>(typeList);
    }

    @ApiOperation(value = "获取团队详情通过id")
    @GetMapping(value = "/getTeamById")
    public Result getTeamById(Long id, int activityNum) {
        ZyzTeam team = teamService.getTeamByIdForApi(id, activityNum);
        DictTransUtil.trans(team);
        DesensitiseUtil.desensitise(team);
        return new JsonResult<>(team);
    }

    @ApiOperation(value = "获取知社区接口修改志愿者组织机构code")
    @GetMapping(value = "/getOrgForSIP")
    public Result getOrgForSIP() {
        volunteerService.checkOrgCode();
        return new Result();
    }

    @ApiOperation(value = "获取团队统计信息")
    @GetMapping(value = "/getTeamStat")
    public JsonResult<TeamStatDto> getTeamStat(@RequestParam Long teamId) {
        TeamStatDto stat = teamService.getTeamStat(teamId);
        return new JsonResult<>(stat);
    }

//    @ApiOperation("获取团队志愿者--列表")
//    @GetMapping("/getTeamMembers")
//    public JsonResult<List<VolunteerTeamDto>> getTeamMembers(@RequestParam Long teamId) {
//        List<VolunteerTeamDto> result = volunteerTeamService.getTeamMembers(teamId);
//        return new JsonResult<>(result);
//    }

    @ApiOperation("团队-下拉框数据获取")
    @GetMapping({"/selectList"})
    public JsonResult<List<IdTextVo>> selectList() {
        List<IdTextVo> result = teamService.listIdTextVo();
        return new JsonResult<>(result);
    }

}
