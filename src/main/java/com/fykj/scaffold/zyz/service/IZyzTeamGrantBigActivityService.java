package com.fykj.scaffold.zyz.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamGrantBigActivity;

import java.util.List;

/**
 * 团队授权大型活动发布记录-服务类
 *
 * @date 2025-05-08
 */
public interface IZyzTeamGrantBigActivityService extends IBaseService<ZyzTeamGrantBigActivity> {

    /**
     * 授权团队
     * @param teamId
     */
    void teamGrant(Long teamId, Long grantId);

    /**
     * 校验当前团队是否被授权
     */
    void validateGranted();

    /**
     * 获取授权团队列表
     * @param teamName
     * @return
     */
    List<ZyzTeamGrantBigActivity> getGrantedTeam(String teamName);
}

