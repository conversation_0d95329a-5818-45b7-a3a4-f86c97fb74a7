package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.zyz.domain.dto.BadgeImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadge;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 勋章信息-服务类
 *
 * @date 2025-05-09
 */
public interface IZyzBadgeService extends IBaseService<ZyzBadge> {

    /**
     * 根据id切换勋章的置顶状态
     * @param id 勋章ID
     */
    void switchTopStatus(Long id);

    /**
     * 根据id切换勋章的上架/下架状态
     * @param id 勋章ID
     */
    void switchStatus(Long id);
    
    /**
     * 保存勋章并创建交易发布
     * @param badge 勋章信息
     * @return 是否保存成功
     */
    boolean saveAndCreateListing(ZyzBadge badge);
    
    /**
     * 更新勋章并处理交易发布
     * @param badge 勋章信息
     * @return 是否更新成功
     */
    boolean updateAndProcessListing(ZyzBadge badge);

    /**
     * 批量授予勋章
     * @param excel Excel文件
     * @param badgeId 勋章ID
     * @return 导入失败的记录列表
     */
    List<BadgeImportDto> batchAwardBadge(MultipartFile excel, Long badgeId);

    /**
     * 批量授予勋章（通过志愿者ID列表）
     * @param badgeId 勋章ID
     * @param volunteerIds 志愿者ID列表
     * @return 授予结果信息
     */
    String batchAwardBadgeByIds(Long badgeId, List<Long> volunteerIds);
}

