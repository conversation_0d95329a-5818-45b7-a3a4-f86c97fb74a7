package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;

/**
 * 苏州社工部加入组织同步请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbSyncOrgMemberRequest {

    /**
     * 身份证编号
     */
    private String idcode;

    /**
     * 组织id - 注册组织接口返回
     */
    private String orgid;

    /**
     * 加入时间 - 格式: yyyy-mm-dd
     */
    private String jointime;

    /**
     * 是否专职 - 是,否
     */
    private String isfulltime;

    /**
     * 个人补充说明
     */
    private String description;

    /**
     * 唯一标识
     */
    private String uniquecode;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncOrgMemberInfo info = new SyncOrgMemberInfo();
        info.setInfos(Collections.singletonList(this));
        return JSON.toJSONString(info);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncOrgMemberInfo {
        private java.util.List<SgbSyncOrgMemberRequest> infos;
    }
}
