package com.fykj.scaffold.mall.service.impl;

import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsApplyParams;
import com.fykj.scaffold.mall.mapper.MallScoreGoodsApplyMapper;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.fykj.scaffold.mall.service.IMallScoreGoodsApplyService;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;


/**
 * 星光闪耀商品报名表
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallScoreGoodsApplyServiceImpl extends BaseServiceImpl<MallScoreGoodsApplyMapper, MallScoreGoodsApply> implements IMallScoreGoodsApplyService {


    @Autowired
    private IMallExchangeService mallExchangeService;
    @Autowired
    private IMallScoreGoodsService mallScoreGoodsService;

    @Override
    public IPage<MallScoreGoodsApplyDto> getPage(MallScoreGoodsApplyParams params) {
        IPage<MallScoreGoodsApplyDto> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public Boolean saveOrUpdateApply(MallScoreGoodsApply apply) {
        //验证并赋值商品id
        MallExchange exchange = applyCheck(apply);
        apply.setGoodsName(exchange.getGoodsName());
        apply.setVolunteerId(exchange.getExchangeVolunteerId());
        apply.setVolunteerName(exchange.getExchangeUserName());
        apply.setVolunteerPhone(exchange.getExchangeUserLinkPhone());
        if (apply.getParticipantCertificateType().equals(ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD)) {
            apply.setParticipantAge(IdcardUtil.getAgeByIdCard(apply.getParticipantCertificateId()));
            apply.setParticipantSex(IdcardUtil.getGenderByIdCard(apply.getParticipantCertificateId()) == 1 ? "m" : "f");
        }
        return super.saveOrUpdate(apply);
    }

    private MallExchange applyCheck(MallScoreGoodsApply apply) {
        if (StringUtil.isEmpty(apply.getExchangeId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请填写兑换id");
        }
        if (apply.getGuardianCertificateType().equals(ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD) && !IdcardUtil.isValidCard(apply.getGuardianCertificateId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "监护人身份证校验异常！");
        }
        if (apply.getParticipantCertificateType().equals(ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD) && !IdcardUtil.isValidCard(apply.getParticipantCertificateId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "参加人身份证校验异常！");
        }
        MallExchange exchange = mallExchangeService.getById(apply.getExchangeId());
        apply.setGoodsId(exchange.getExchangeGoodsId());
        MallScoreGoods goods = mallScoreGoodsService.getById(exchange.getExchangeGoodsId());
        if (goods.getNeedApply() == null || !goods.getNeedApply()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "商品无需报名！");
        }
        boolean isExist = lambdaQuery()
                .eq(MallScoreGoodsApply::getExchangeId, apply.getExchangeId())
                .ne(apply.getId() != null, BaseEntity::getId, apply.getId()).exists();
        if (isExist) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "已经报名");
        }
        return exchange;
    }

    @Override
    public MallScoreGoodsApply getApplyInfo(Long exchangeId) {
        MallScoreGoodsApply result = lambdaQuery()
                .eq(MallScoreGoodsApply::getExchangeId, exchangeId)
                .one();
        DictTransUtil.trans(result);
        return result;
    }
}
