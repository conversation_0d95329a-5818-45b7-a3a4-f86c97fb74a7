package com.fykj.scaffold.zyz.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.dto.AddPointRecordDto;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgeGiftDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgePurchaseDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgeTradePublishDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgeTradePublishInfoDto;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeInstanceParams;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceDetailVO;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceVO;
import com.fykj.scaffold.zyz.mapper.ZyzBadgeInstanceMapper;
import com.fykj.scaffold.zyz.service.*;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 勋章实例-服务实现类
 *
 * @date 2025-05-09
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ZyzBadgeInstanceServiceImpl extends BaseServiceImpl<ZyzBadgeInstanceMapper, ZyzBadgeInstance>
        implements IZyzBadgeInstanceService {

    @Autowired
    private IZyzVolunteerService volunteerService;
    
    @Autowired
    private IZyzBadgeService badgeService;
    
    @Autowired
    private IZyzBadgeTradeListingService badgeTradeListingService;
    
    @Autowired
    private IZyzBadgeTransactionService badgeTransactionService;

    /**
     * 查询当前登录用户的勋章列表
     * @return 勋章实例列表（含勋章基础信息）
     */
    @Override
    public List<ZyzBadgeInstanceVO> listMyBadges() {
        ZyzVolunteer volunteer = getCurrentVolunteer();
        // 使用自定义SQL查询勋章实例及基础信息
        return baseMapper.getMyBadgesWithBadgeInfo(volunteer.getId());
    }

    /**
     * 分页查询当前登录用户的勋章列表
     * @param params 查询参数
     * @return 分页勋章实例列表（含勋章基础信息）
     */
    @Override
    public IPage<ZyzBadgeInstanceVO> pageMyBadges(ZyzBadgeInstanceParams params) {
        // 获取当前登录用户手机号
        ZyzVolunteer volunteer = getCurrentVolunteer();

        // 设置查询参数中的志愿者ID
        params.setOwnerId(volunteer.getId());
        
        // 如果recycle字段为null，设置默认值为false
        if (params.getRecycle() == null) {
            params.setRecycle(false);
        }

        // 调用通用分页查询方法
        return pageBadges(params);
    }

    /**
     * 通用分页查询勋章列表
     * @param params 查询参数
     * @return 分页勋章实例列表（含勋章基础信息）
     */
    @Override
    public IPage<ZyzBadgeInstanceVO> pageBadges(ZyzBadgeInstanceParams params) {
        // 如果recycle字段为null，设置默认值为false
        if (params.getRecycle() == null) {
            params.setRecycle(false);
        }

        // 使用自定义SQL进行分页查询
        Page<ZyzBadgeInstanceVO> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageMyBadgesWithBadgeInfo(page, params);
    }

    /**
     * 获取当前志愿者
     * @return 当前志愿者信息
     */
    private ZyzVolunteer getCurrentVolunteer() {
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        if (volunteer == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "未找到当前用户信息");
        }
        return volunteer;
    }

    /**
     * 发布勋章交易
     *
     * @param dto 勋章交易发布数据
     * @return 交易发布记录
     */
    @Override
    public ZyzBadgeTradeListing publishBadgeTrade(ZyzBadgeTradePublishDto dto) {
        // 1. 获取当前登录用户的志愿者信息
        ZyzVolunteer volunteer = getCurrentVolunteer();
        
        // 2. 获取勋章信息
        ZyzBadge badge = badgeService.getById(dto.getBadgeId());
        if (badge == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章不存在");
        }
        
        // 3. 验证费率是否一致
        int badgeFeeRate = badge.getTransactionFeeRate() != null ?
                badge.getTransactionFeeRate() : 0;
        if (badgeFeeRate != dto.getFeeRate()) {
            throw new BusinessException(ResultCode.FAIL, "费率与系统设置不一致，当前费率为" + badgeFeeRate + "%");
        }
        
        // 4. 使用服务计算费率后积分
        int calculatedPointsAfterFee = badgeTradeListingService.calculatePointsAfterFee(
                dto.getPointsBeforeFee(), dto.getFeeRate());
        
        // 5. 查询用户是否拥有此勋章实例
        ZyzBadgeInstance instance = getUserBadgeInstances(dto.getBadgeId(), volunteer.getId());
        if (Objects.isNull(instance)) {
            throw new BusinessException(ResultCode.FAIL, "您没有可交易的此类勋章");
        }

        // 5.5 验证勋章是否已发布交易
        boolean isListed = checkBadgeInstanceListed(instance.getId());
        if (isListed) {
            throw new BusinessException(ResultCode.FAIL, "此勋章正在交易发布中");
        }

        // 6. 创建交易发布记录
        ZyzBadgeTradeListing tradeListing = new ZyzBadgeTradeListing();
        tradeListing.setBadgeId(dto.getBadgeId());
        tradeListing.setBadgeInstanceId(instance.getId());
        tradeListing.setSellerId(volunteer.getId());
        tradeListing.setSellerName(volunteer.getName());
        tradeListing.setPointsBeforeFee(dto.getPointsBeforeFee());
        tradeListing.setPlatformFeeRate(dto.getFeeRate());
        tradeListing.setPointsAfterFee(calculatedPointsAfterFee);
        // 设置状态为发布中 0
        tradeListing.setStatus(ZyzCons.BadgeTradeListingStatus.IN_SHELF.getCode());
        // 设置来源为个人勋章 false
        tradeListing.setFromPlatform(false);
        // 保存交易发布记录
        badgeTradeListingService.save(tradeListing);
        
        log.debug("用户发布勋章交易成功，用户ID：{}，勋章ID：{}，勋章实例ID：{}", 
                volunteer.getId(), dto.getBadgeId(), instance.getId());
        
        return tradeListing;
    }

    /**
     * 编辑勋章交易
     *
     * @param dto 勋章交易发布数据
     * @return 交易发布记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ZyzBadgeTradeListing editBadgeTrade(ZyzBadgeTradePublishDto dto) {
        // 1. 获取当前登录用户的志愿者信息
        ZyzVolunteer volunteer = getCurrentVolunteer();

        // 2. 通过badgeId查找用户的发布中交易记录
        ZyzBadgeInstance instance = getUserBadgeInstances(dto.getBadgeId(), volunteer.getId());
        if (instance == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "您没有此勋章或勋章已被回收");
        }

        ZyzBadgeTradeListing tradeListing = badgeTradeListingService.lambdaQuery()
                .eq(ZyzBadgeTradeListing::getBadgeInstanceId, instance.getId())
                .eq(ZyzBadgeTradeListing::getStatus, ZyzCons.BadgeTradeListingStatus.IN_SHELF.getCode())
                .one();

        if (tradeListing == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "该勋章没有发布中的交易记录");
        }

        // 3. 获取勋章信息并验证费率
        ZyzBadge badge = badgeService.getById(dto.getBadgeId());
        if (badge == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章不存在");
        }

        int badgeFeeRate = badge.getTransactionFeeRate() != null ?
                badge.getTransactionFeeRate() : 0;
        if (badgeFeeRate != dto.getFeeRate()) {
            throw new BusinessException(ResultCode.FAIL, "费率与系统设置不一致，当前费率为" + badgeFeeRate + "%");
        }

        // 4. 计算新的费率后积分
        int calculatedPointsAfterFee = badgeTradeListingService.calculatePointsAfterFee(
                dto.getPointsBeforeFee(), dto.getFeeRate());

        // 5. 更新交易记录
        tradeListing.setPointsBeforeFee(dto.getPointsBeforeFee());
        tradeListing.setPlatformFeeRate(dto.getFeeRate());
        tradeListing.setPointsAfterFee(calculatedPointsAfterFee);
        badgeTradeListingService.updateById(tradeListing);

        log.debug("用户编辑勋章交易成功，用户ID：{}，交易ID：{}，勋章ID：{}",
                volunteer.getId(), tradeListing.getId(), dto.getBadgeId());

        return tradeListing;
    }

    @Override
    public ZyzBadgeInstance getUserBadgeInstances(Long badgeId, Long volunteerId) {
        // 查询用户是否拥有此勋章实例
        return lambdaQuery()
                .eq(ZyzBadgeInstance::getBadgeId, badgeId)
                .eq(ZyzBadgeInstance::getOwnerId, volunteerId)
                .eq(ZyzBadgeInstance::getRecycle, false)
                .one();
    }

    @Override
    public ZyzBadgeTradePublishInfoDto getTradePublishInfo(Long badgeId, Integer pointsBeforeFee) {
        // 1. 获取勋章信息
        ZyzBadge badge = badgeService.getById(badgeId);
        if (badge == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章不存在");
        }
        
        // 2. 创建DTO并填充数据
        ZyzBadgeTradePublishInfoDto dto = new ZyzBadgeTradePublishInfoDto();
        Map.Entry<Integer, Integer> listingPointRange = badgeTradeListingService.getListingPointRange(badgeId);
        dto.setBadgeId(badgeId);
        // 市场最高价和最低价
        dto.setListingMinPoints(listingPointRange.getKey());
        dto.setListingMaxPoints(listingPointRange.getValue());

        // 确定费率前积分，如果传入值不为空则使用传入值，否则使用勋章默认所需积分
        Integer actualPointsBeforeFee = pointsBeforeFee != null ? 
                pointsBeforeFee : badge.getPointsRequired();
        dto.setPointsBeforeFee(actualPointsBeforeFee);
        
        // 设置费率
        int feeRate = badge.getTransactionFeeRate() != null ?
                badge.getTransactionFeeRate() : 0;
        dto.setFeeRate(feeRate);
        
        // 3. 计算费率后积分
        int pointsAfterFee = badgeTradeListingService.calculatePointsAfterFee(
                actualPointsBeforeFee, feeRate);
        dto.setPointsAfterFee(pointsAfterFee);
        
        return dto;
    }

    /**
     * 获取当前用户的勋章实例详情
     * 
     * @param badgeId 勋章ID
     * @return 勋章实例详情，包含实例ID和是否已上架
     */
    @Override
    public ZyzBadgeInstanceDetailVO getMyBadgeInstanceDetail(Long badgeId) {
        // 1. 获取当前用户的志愿者信息
        ZyzVolunteer volunteer = getCurrentVolunteer();
        
        // 2. 获取用户的勋章实例
        ZyzBadgeInstance instance = getUserBadgeInstances(badgeId, volunteer.getId());
        if (instance == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "您没有此勋章");
        }
        
        // 3. 获取勋章基本信息
        ZyzBadge badge = badgeService.getById(badgeId);
        if (badge == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章不存在");
        }
        
        // 4. 创建返回结果并赋值勋章基本信息
        ZyzBadgeInstanceDetailVO detailVO = new ZyzBadgeInstanceDetailVO();
        
        // 复制勋章基本属性
        BeanUtils.copyProperties(badge, detailVO);
        
        // 5. 设置勋章实例特有属性
        detailVO.setBadgeInstanceId(instance.getId());
        detailVO.setBadgeId(badgeId);
        
        // 6. 查询该勋章实例是否已上架（发布中的交易）
        ZyzBadgeTradeListing tradeListing = badgeTradeListingService.lambdaQuery()
                .eq(ZyzBadgeTradeListing::getBadgeInstanceId, instance.getId())
                .eq(ZyzBadgeTradeListing::getStatus, ZyzCons.BadgeTradeListingStatus.IN_SHELF.getCode())
                .one();
        
        // 7. 设置是否已上架及交易发布ID
        detailVO.setIsListed(tradeListing != null);
        if (tradeListing != null) {
            detailVO.setListingId(tradeListing.getId());
        }

        // 计算税后积分数量
        detailVO.setPointsAfterFee(badgeTradeListingService.calculatePointsAfterFee(
                badge.getPointsRequired(), badge.getTransactionFeeRate()));
        
        return detailVO;
    }

    /**
     * 检查勋章实例是否已上架
     * 
     * @param instanceId 勋章实例ID
     * @return 是否已上架
     */
    @Override
    public boolean checkBadgeInstanceListed(Long instanceId) {
        return badgeTradeListingService.lambdaQuery()
                .eq(ZyzBadgeTradeListing::getBadgeInstanceId, instanceId)
                .eq(ZyzBadgeTradeListing::getStatus, ZyzCons.BadgeTradeListingStatus.IN_SHELF.getCode())
                .exists();
    }
    
    /**
     * 兑换勋章获取积分
     * 
     * @param badgeId 勋章ID
     * @return 是否兑换成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean exchangeForPoints(Long badgeId) {
        // 1. 获取当前登录用户
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        if (volunteer == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "未找到当前用户信息");
        }
        
        // 2. 查找用户的勋章实例
        ZyzBadgeInstance instance = getUserBadgeInstances(badgeId, volunteer.getId());
        if (instance == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "您没有此勋章");
        }
        
        // 3. 验证勋章是否已回收
        if (Boolean.TRUE.equals(instance.getRecycle())) {
            throw new BusinessException(ResultCode.FAIL, "此勋章已经被回收，无法再次兑换");
        }
        
        // 4. 验证勋章是否上架中
        boolean isListed = checkBadgeInstanceListed(instance.getId());
        if (isListed) {
            throw new BusinessException(ResultCode.FAIL, "此勋章正在交易发布中，请先取消交易后再兑换");
        }
        
        // 5. 获取勋章信息
        ZyzBadge badge = badgeService.getById(badgeId);
        if (badge == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章不存在");
        }
        
        // 6. 计算获得的积分和费率
        Integer pointsRequired = badge.getPointsRequired();
        if (pointsRequired == null || pointsRequired <= 0) {
            throw new BusinessException(ResultCode.FAIL, "勋章兑换积分配置有误");
        }
        int afterFeePoints = badgeTradeListingService.calculatePointsAfterFee(
                pointsRequired, badge.getTransactionFeeRate());
        
        // 7. 将积分授予志愿者
        AddPointRecordDto pointDto = AddPointRecordDto.builder()
                .volunteerId(volunteer.getId())
                .changePoint(new BigDecimal(afterFeePoints))
                .bizId(instance.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_BADGE_EXCHANGE)
                .remark("兑换勋章「" + badge.getName() + "」获得积分")
                .build();
        volunteerService.addPoint(pointDto);
        
        // 8. 生成勋章交易流水
        ZyzBadgeTransaction transaction = new ZyzBadgeTransaction();
        transaction.setBadgeId(badgeId);
        transaction.setBadgeInstanceId(instance.getId());
        transaction.setOperationType(ZyzCons.BadgeOperationType.RECYCLE.getCode());
        transaction.setFromId(volunteer.getId());
        transaction.setFromName(volunteer.getName());
        // 系统回收，没有具体接收人
        transaction.setToId(0L);
        transaction.setToName(ZyzCons.BADGE_PLATFORM_NAME);
        transaction.setPointsBeforeFee(pointsRequired);
        transaction.setPlatformFeeRate(badge.getTransactionFeeRate());
        transaction.setPointsAfterFee(afterFeePoints);
        badgeTransactionService.save(transaction);
        
        // 9. 更新勋章实例为已回收
        instance.setRecycle(true);
        updateById(instance);
        
        log.debug("用户 {} 将勋章 {} 兑换为 {} 积分", volunteer.getName(), badge.getName(), pointsRequired);
        
        return true;
    }

    /**
     * 赠送勋章给其他志愿者
     *
     * @param giftDto 赠送勋章参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void giftBadge(ZyzBadgeGiftDto giftDto) {
        // 1. 获取当前登录用户
        ZyzVolunteer fromVolunteer = getCurrentVolunteer();
        
        // 2. 查找接收者志愿者信息
        ZyzVolunteer toVolunteer = volunteerService.getById(giftDto.getToVolunteerId());
        if (toVolunteer == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "接收赠送的志愿者不存在");
        }
        
        // 3. 检查是否赠送给自己
        if (fromVolunteer.getId().equals(toVolunteer.getId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "不能赠送勋章给自己");
        }
        
        // 4. 获取勋章信息
        ZyzBadge badge = badgeService.getById(giftDto.getBadgeId());
        if (badge == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章不存在");
        }
        
        // 5. 查找发送者的勋章实例
        ZyzBadgeInstance badgeInstance = getUserBadgeInstances(giftDto.getBadgeId(), fromVolunteer.getId());
        if (badgeInstance == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "您没有此勋章，无法赠送");
        }
        
        // 6. 验证勋章是否已回收
        if (Boolean.TRUE.equals(badgeInstance.getRecycle())) {
            throw new BusinessException(ResultCode.FAIL, "此勋章已经被回收，无法赠送");
        }
        
        // 7. 验证勋章是否上架中
        boolean isListed = checkBadgeInstanceListed(badgeInstance.getId());
        if (isListed) {
            throw new BusinessException(ResultCode.FAIL, "此勋章正在交易发布中，请先取消交易后再赠送");
        }
        
        // 8. 生成勋章交易流水
        ZyzBadgeTransaction transaction = new ZyzBadgeTransaction();
        transaction.setBadgeId(giftDto.getBadgeId());
        transaction.setBadgeInstanceId(badgeInstance.getId());
        transaction.setOperationType(ZyzCons.BadgeOperationType.GIFT.getCode());
        transaction.setFromId(fromVolunteer.getId());
        transaction.setFromName(fromVolunteer.getName());
        transaction.setToId(toVolunteer.getId());
        transaction.setToName(toVolunteer.getName());
        // 赠送不涉及积分
        transaction.setPointsBeforeFee(0);
        transaction.setPlatformFeeRate(0);
        transaction.setPointsAfterFee(0);
        badgeTransactionService.save(transaction);
        
        // 9. 更新勋章实例所有者
        badgeInstance.setOwnerId(toVolunteer.getId());
        updateById(badgeInstance);
        
        log.debug("用户 {} 将勋章 {} 赠送给了用户 {}", fromVolunteer.getName(), badge.getName(), toVolunteer.getName());
    }

    /**
     * 购买勋章
     * 
     * @param purchaseDto 购买勋章参数
     * @return 是否购买成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean purchaseBadge(ZyzBadgePurchaseDto purchaseDto) {
        // 1. 获取当前用户信息
        ZyzVolunteer buyer = getCurrentVolunteer();
        
        // 2. 获取上架记录信息
        ZyzBadgeTradeListing listing = badgeTradeListingService.getById(purchaseDto.getListingId());
        if (listing == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "交易记录不存在");
        }
        
        // 3. 验证上架状态
        if (!Objects.equals(listing.getStatus(), ZyzCons.BadgeTradeListingStatus.IN_SHELF.getCode())) {
            throw new BusinessException(ResultCode.FAIL, "该勋章交易已完成或已取消");
        }
        
        // 4. 获取勋章信息
        ZyzBadge badge = badgeService.getById(listing.getBadgeId());
        if (badge == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章不存在");
        }
        
        // 5. 验证买方与卖方不是同一人
        if (buyer.getId().equals(listing.getSellerId())) {
            throw new BusinessException(ResultCode.FAIL, "不能购买自己发布的勋章");
        }

        // 5.5 验证该buyer是否已经有这个种类的勋章
        ZyzBadgeInstance existingBadge = getUserBadgeInstances(listing.getBadgeId(), buyer.getId());
        if (existingBadge != null) {
            throw new BusinessException(ResultCode.FAIL, "您已经拥有此种类的勋章，无法重复购买");
        }
        
        // 6. 检查买方积分是否足够
        AddPointRecordDto buyerPointDto = AddPointRecordDto.builder()
                .volunteerId(buyer.getId())
                .changePoint(new BigDecimal(listing.getPointsBeforeFee()))
                .bizId(listing.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_BADGE_PURCHASE)
                .remark("购买勋章「" + badge.getName() + "」扣除积分")
                .build();
                
        // 扣除买方积分
        volunteerService.reducePoint(buyerPointDto);
        
        // 7. 判断是平台发布还是个人发布的勋章
        if (Boolean.TRUE.equals(listing.getFromPlatform())) {
            // 平台发布的勋章处理逻辑
            return handlePlatformBadgePurchase(listing, badge, buyer);
        } else {
            // 个人发布的勋章处理逻辑
            return handlePersonalBadgePurchase(listing, badge, buyer);
        }
    }

    /**
     * 处理平台发布的勋章购买
     */
    private boolean handlePlatformBadgePurchase(ZyzBadgeTradeListing listing, ZyzBadge badge, 
                                              ZyzVolunteer buyer) {
        // 1. 为买方生成新的勋章实例
        ZyzBadgeInstance newInstance = new ZyzBadgeInstance();
        newInstance.setBadgeId(badge.getId());
        newInstance.setOwnerId(buyer.getId());
        newInstance.setRecycle(false);
        save(newInstance);
        
        // 2. 生成交易流水记录
        ZyzBadgeTransaction transaction = new ZyzBadgeTransaction();
        transaction.setBadgeId(badge.getId());
        transaction.setBadgeInstanceId(newInstance.getId());
        transaction.setBizId(listing.getId());
        transaction.setOperationType(ZyzCons.BadgeOperationType.TRADE.getCode());
        transaction.setToId(listing.getSellerId());
        transaction.setToName(ZyzCons.BADGE_PLATFORM_NAME);
        transaction.setFromId(buyer.getId());
        transaction.setFromName(buyer.getName());
        transaction.setPointsBeforeFee(listing.getPointsBeforeFee());
        transaction.setPlatformFeeRate(listing.getPlatformFeeRate());
        transaction.setPointsAfterFee(listing.getPointsAfterFee());
        badgeTransactionService.save(transaction);
        
        log.debug("用户从平台购买勋章成功，用户ID：{}，勋章ID：{}，勋章实例ID：{}", 
                buyer.getId(), badge.getId(), newInstance.getId());
        
        return true;
    }

    /**
     * 处理个人发布的勋章购买
     */
    private boolean handlePersonalBadgePurchase(ZyzBadgeTradeListing listing, ZyzBadge badge, 
                                              ZyzVolunteer buyer) {
        // 1. 获取卖方信息
        ZyzVolunteer seller = volunteerService.getVolunteerById(listing.getSellerId());
        if (seller == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "卖方用户不存在");
        }
        
        // 2. 获取勋章实例
        ZyzBadgeInstance instance = getById(listing.getBadgeInstanceId());
        if (instance == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "勋章实例不存在");
        }
        
        // 3. 验证勋章实例的所有权
        if (!instance.getOwnerId().equals(seller.getId())) {
            throw new BusinessException(ResultCode.FAIL, "卖方不再拥有此勋章");
        }
        
        // 4. 给卖方增加积分
        AddPointRecordDto sellerPointDto = AddPointRecordDto.builder()
                .volunteerId(seller.getId())
                .changePoint(new BigDecimal(listing.getPointsAfterFee()))
                .bizId(listing.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_BADGE_SELL)
                .remark("出售勋章「" + badge.getName() + "」获得积分")
                .build();
        volunteerService.addPoint(sellerPointDto);
        
        // 5. 修改勋章实例所有权
        instance.setOwnerId(buyer.getId());
        updateById(instance);
        
        // 6. 修改上架状态为已完成
        listing.setStatus(ZyzCons.BadgeTradeListingStatus.COMPLETED.getCode());
        listing.setBuyerId(buyer.getId());
        listing.setBuyerName(buyer.getName());
        listing.setCompleteDate(LocalDateTime.now());
        badgeTradeListingService.updateById(listing);
        
        // 7. 生成交易流水记录
        ZyzBadgeTransaction transaction = new ZyzBadgeTransaction();
        transaction.setBadgeId(badge.getId());
        // 储存业务ID
        transaction.setBizId(listing.getId());
        transaction.setBadgeInstanceId(instance.getId());
        transaction.setOperationType(ZyzCons.BadgeOperationType.TRADE.getCode());
        transaction.setFromId(seller.getId());
        transaction.setFromName(seller.getName());
        transaction.setToId(buyer.getId());
        transaction.setToName(buyer.getName());
        transaction.setPointsBeforeFee(listing.getPointsBeforeFee());
        transaction.setPlatformFeeRate(listing.getPlatformFeeRate());
        transaction.setPointsAfterFee(listing.getPointsAfterFee());
        badgeTransactionService.save(transaction);
        
        log.debug("用户购买个人发布勋章成功，买方ID：{}，卖方ID：{}，勋章ID：{}，勋章实例ID：{}", 
                buyer.getId(), seller.getId(), badge.getId(), instance.getId());
        
        return true;
    }

    /**
     * 取消勋章交易
     *
     * @param listingId 交易上架ID
     * @return 是否取消成功
     */
    @Override
    public boolean cancelBadgeTrade(Long listingId) {
        // 1. 获取当前登录用户的志愿者信息
        ZyzVolunteer volunteer = getCurrentVolunteer();
        
        // 2. 查询交易上架记录
        ZyzBadgeTradeListing listing = badgeTradeListingService.getById(listingId);
        if (listing == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "交易记录不存在");
        }
        
        // 3. 验证是否为发布状态
        if (!Objects.equals(listing.getStatus(), ZyzCons.BadgeTradeListingStatus.IN_SHELF.getCode())) {
            throw new BusinessException(ResultCode.FAIL, "该交易已完成或已取消，无法进行取消操作");
        }
        
        // 4. 验证是否为当前用户的交易
        if (!Objects.equals(listing.getSellerId(), volunteer.getId())) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "您无权取消此交易");
        }
        
        // 5. 取消交易
        badgeTradeListingService.cancelListing(listingId);
        
        log.debug("用户取消勋章交易成功，用户ID：{}，交易ID：{}", volunteer.getId(), listingId);
        
        return true;
    }
}
