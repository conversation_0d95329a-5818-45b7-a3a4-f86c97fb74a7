<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzVolunteerMapper">
    <delete id="deleteByVolunteerId">
        DELETE
        FROM zyz_volunteer
        WHERE id = #{id}
    </delete>

    <delete id="deleteDirtyByVolunteerId">
        DELETE
        FROM zyz_volunteer_dirty
        WHERE id = #{id}
    </delete>

    <select id="getServiceLongCount" resultType="java.math.BigDecimal">
        SELECT SUM(service_long)
        FROM zyz_volunteer v left join sys_org o on o.code = v.org_code
        WHERE v.is_deleted = FALSE
        <include refid="query_sql"></include>
    </select>

    <sql id="query_sql">
        <if test="params.name != null and params.name != ''">
            and v.name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.certificateType != null and params.certificateType != ''">
            and v.certificate_type = #{params.certificateType}
        </if>
        <if test="params.certificateId != null and params.certificateId != ''">
            and v.certificate_id = #{params.certificateId}
        </if>
        <if test="params.phone != null and params.phone != ''">
            and v.phone = #{params.phone}
        </if>
        <if test="params.isSync != null and params.isSync != ''">
            and v.is_sync = #{params.isSync}
        </if>
        <if test="params.orgCodeLink != null and params.orgCodeLink != ''">
            and o.code_prefix LIKE CONCAT(#{params.orgCodeLink},'%')
        </if>
        <if test="params.isLively != null and params.isLively != ''">
            and v.is_lively = #{params.isLively}
        </if>
        <!--        <if test="params.volunteerStatus != null and params.volunteerStatus != ''">-->
        <!--            and volunteer_status = #{params.volunteerStatus}-->
        <!--        </if>-->
        <if test="params.skill != null and params.skill != ''">
            and v.skill = #{params.skill}
        </if>
        <if test="params.industryCate != null and params.industryCate != ''">
            and v.industry_cate = #{params.industryCate}
        </if>
        <if test="params.isParty != null and params.isParty != ''">
            and v.is_party = #{params.isParty}
        </if>
        <if test="params.foundedStartDate != null ">
            and date(v.founded) >= #{params.foundedStartDate}
        </if>
        <if test="params.foundedEndDate != null ">
            and #{params.foundedEndDate} >= date(v.founded)
        </if>
        <if test="params.writeOff != null">
            and v.write_off = #{params.writeOff}
        </if>
        <if test="params.writeOff == null">
            and v.write_off = 0
        </if>
    </sql>

    <select id="getVolunteersExist" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer">
        SELECT id,
               certificate_id,
               phone,
               name
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0
    </select>

    <select id="getVolunteerInfoList" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerInfoDto">
        SELECT
            v.id,
            v.name,
            v.phone,
            v.certificate_type as certificateType,
            v.certificate_id as certificateId,
            v.sex,
            v.email,
            v.service_long as serviceLong,
            v.service_long_this_year as serviceLongThisYear,
            v.total_point as totalPoint,
            v.org_code as orgCode,
            o.name as orgName,
            v.status,
            v.founded,
            v.is_party as isParty,
            v.is_certification as certification,
            v.is_lively as lively
        FROM
            zyz_volunteer v
            LEFT JOIN sys_org o ON v.org_code = o.code AND o.is_deleted = 0
        WHERE
            v.is_deleted = 0
            AND v.write_off = 0
            <if test="params.name != null and params.name != ''">
                AND v.name LIKE CONCAT('%', #{params.name}, '%')
            </if>
            <if test="params.phone != null and params.phone != ''">
                AND v.phone LIKE CONCAT('%', #{params.phone}, '%')
            </if>
            <if test="params.certificateId != null and params.certificateId != ''">
                AND v.certificate_id LIKE CONCAT('%', #{params.certificateId}, '%')
            </if>
        ORDER BY
            v.create_date DESC
    </select>

    <select id="getSgbNeedSync" resultType="java.lang.Long">
        SELECT v.id
        FROM zyz_volunteer v
        LEFT JOIN sgb_sync_status_record ssr
                  ON ssr.business_type = 'sgb_sync_biz_volunteer'
                      AND ssr.business_id = v.id
                      AND ssr.is_deleted = 0
        WHERE v.is_deleted = 0
          AND v.write_off = 0
          AND v.create_date &lt;= #{now}
          AND (
                ssr.id IS NULL
                OR (ssr.sync_status = 0 AND v.create_date &gt;= #{threeDaysBeforeNow})
              )
    </select>

    <select id="getPage" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer">
        SELECT v.*,
        o.code_prefix
        FROM zyz_volunteer v
        left join sys_org o on o.code = v.org_code
        WHERE v.is_deleted = FALSE
        <include refid="query_sql"></include>
        order by v.founded desc
    </select>
    <select id="getDirtyVolunteer" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer">
        select *
        from zyz_volunteer_dirty
        WHERE is_deleted = FALSE
          and certificate_id = #{certificateId}
    </select>
    <select id="getRadarMapCount" resultType="com.fykj.scaffold.zyz.domain.dto.RadarMapDto">
        SELECT IFNULL(COUNT(*), 0)                   as activityCount,
               SUM(if(apply_time is not null, 1, 0)) as applyCount,
               SUM(if(sign_time is not null, 1, 0))  as signCount
        FROM `zyz_activity_apply`
        WHERE is_deleted = FALSE
          and apply_time BETWEEN DATE_SUB(NOW(), INTERVAL 12 MONTH) AND NOW()
          and volunteer_id = #{volunteerId}
    </select>
    <select id="getActivityCountInMonth" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT
                     IF(sign_time BETWEEN DATE_SUB(NOW(), INTERVAL 12 MONTH) AND NOW(), DATE_FORMAT(sign_time, '%Y-%m'),
                        NULL))
        FROM `zyz_activity_apply`
        WHERE is_deleted = FALSE
          and volunteer_id = #{volunteerId}
    </select>

    <select id="getTeamCount" resultType="java.lang.Integer">
        SELECT
        IFNULL (COUNT(*),0)
        FROM `zyz_volunteer_team`
        WHERE
        is_deleted = FALSE
        and join_time BETWEEN DATE_SUB(NOW(), INTERVAL 12 MONTH) AND NOW()
        <if test="volunteer_id != null ">
            and volunteer_id = #{volunteerId}
        </if>
    </select>

    <select id="getMonthActivityTeamCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT IF(submit_time BETWEEN DATE_SUB(NOW(), INTERVAL 12 MONTH) AND NOW(),
        DATE_FORMAT(submit_time, '%Y-%m'), NULL))
        FROM zyz_activity
        WHERE is_deleted = FALSE
        and team_publish =true
        and team_id = (SELECT za.team_id
        FROM `zyz_activity_apply` aa
        left JOIN zyz_activity za on aa.activity_id = za.id
        WHERE
        aa. is_deleted = FALSE
        <if test="volunteer_id != null ">
            and volunteer_id = #{volunteerId}
        </if>
        and za.team_publish = true
        ORDER BY aa.apply_time
        LIMIT 1)
    </select>

    <select id="getActivityTeamCount" resultType="java.lang.Integer">
        SELECT IFNULL (COUNT(*),0)
        FROM `zyz_activity`
        WHERE
        submit_time BETWEEN DATE_SUB(NOW(), INTERVAL 12 MONTH) AND NOW()
        and is_deleted = FALSE
        and team_id = (SELECT za.team_id
        FROM `zyz_activity_apply` aa
        left JOIN zyz_activity za on aa.activity_id = za.id
        WHERE
        aa. is_deleted = FALSE
        <if test="volunteer_id != null ">
            and volunteer_id = #{volunteerId}
        </if>
        and za.team_publish = true
        ORDER BY aa.apply_time
        LIMIT 1)
    </select>

    <update id="refreshVolunteerLively">
        UPDATE zyz_volunteer
        SET is_lively = #{status}
        WHERE is_deleted = 0 AND write_off = 0
        <if test="status == true">
            AND id in (
            SELECT volunteer_id FROM zyz_activity_apply WHERE is_deleted = 0 AND apply_time >= #{time} AND sign_time IS NOT NULL
            )
        </if>
    </update>

    <select id="volunteerInfoSummary" resultType="com.fykj.scaffold.zyz.domain.dto.HomePageVolunteerServiceDto">
        SELECT
        COUNT(id) AS volunteerNum,
        SUM(IF(is_lively IS NULL OR is_lively = 0, 0, 1)) AS activeVolunteerNum,
        SUM(IF(service_long IS NULL, 0, service_long)) AS volunteerServiceTime
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0
        <if test="type != null and type == 'curr_year_new'">
            AND founded >= #{thisYearFirstDay}
        </if>
    </select>

    <select id="getVolunteerNumByOrgCodes" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0
        <if test="orgCodes != null and orgCodes.size() > 0">
            AND org_code IN
            <foreach item="item" collection="orgCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="active != null and active == true">
            AND is_lively = 1
        </if>
    </select>

    <select id="getNewRegisterVolunteerNumByOrgCodes" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0 AND DATE(founded) = DATE(NOW())
        <if test="orgCodes != null and orgCodes.size() > 0">
            AND org_code IN
            <foreach item="item" collection="orgCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getVolunteerOrgSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.VolunteerOrgSumDto">
        SELECT o.code_prefix AS orgCodePrefix,
               COUNT(v.id)   AS num
        FROM zyz_volunteer v
                 LEFT JOIN sys_org o ON v.org_code = o.code
        WHERE v.is_deleted = 0 AND v.write_off = 0
        GROUP BY v.org_code
    </select>

    <select id="sumNumByOrg" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto">
        SELECT
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            org_code AS orgCode,
        </if>
        COUNT(id) AS sumNum,
        IFNULL(SUM(IFNULL(service_long, 0)), 0) AS serviceDurationTotal
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0
        <if test="params.start != null">
            AND founded &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND founded &lt;= #{params.end}
        </if>
        <if test="params.ignoreOrgCodes != null and params.ignoreOrgCodes.length > 0">
            <if test="params.ignoreOrgCodes.length == 1">
                AND org_code != #{params.ignoreOrgCodes[0]}
            </if>
            <if test="params.ignoreOrgCodes.length > 1">
                AND org_code NOT IN
                <foreach item="item" collection="params.ignoreOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            GROUP BY org_code
        </if>
    </select>
    <select id="getVolunteerForBlockchain"
            resultType="com.fykj.scaffold.zyz.domain.dto.block_chain.VolunteerBlockChainDto">
        SELECT z.name,
               z.email,
               z.service_long,
               z.certificate_type,
               d.name as certificateTypeText,
               z.certificate_id,
               z.phone,
               z.country,
               c.name as countryText,
               z.address,
               z.full_address,
               z.nation,
               n.name as nationText,
               z.school,
               z.education,
               e.name as educationText,
               z.industry_cate,
               i.name as industryCateText,
               z.skill,
               s.name as skillText
        from zyz_volunteer z
                 left join sys_dict d on z.certificate_type = d.code
                 left join sys_dict c on c.code = z.country
                 left join sys_dict n on n.code = z.nation
                 left join sys_dict e on e.code = z.education
                 left join sys_dict i on i.code = z.industry_cate
                 left join sys_dict s on s.code = z.skill
        where  z.is_deleted = 0 AND z.write_off = 0
        <if test="params.startTime != null ">
            AND z.founded &gt;= #{params.startTime}
        </if>
        <if test=" params.endTime != null">
            AND  z.founded &lt; #{params.endTime}
        </if>
    </select>

    <select id="getDictForBlockChain" resultType="com.fykj.scaffold.zyz.domain.dto.DictVolunteerBlockChainDto">
        select p.code as parentCode, s.code, s.name, s.sequence
        from sys_dict s
                 left join sys_dict p on s.parent_id = p.id and p.is_deleted = false
        where s.parent_id in (select id
                              from sys_dict
                              where code in
                                    ('certificate_type', 'country_list', 'nation', 'education', 'industry_cate', 'skill'))
          and s.is_deleted = false
        order by p.code, s.sequence
    </select>

    <select id="getOrgRegisterSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            SUM(IFNULL(nt.volunteerNum, 0)) AS registerNum
        FROM sys_org o
        LEFT JOIN (
            SELECT
                o.code_prefix,
                COUNT(v.id) AS volunteerNum
            FROM zyz_volunteer v
            LEFT JOIN sys_org o ON v.org_code = o.code
            WHERE v.is_deleted = 0 AND v.write_off = 0
            <if test="params.end != null">
                AND founded &lt;= #{params.end}
            </if>
            GROUP BY o.code_prefix
        ) nt ON nt.code_prefix LIKE CONCAT(o.code_prefix, '%')
        <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
            WHERE o.code IN
            <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY o.code
        ORDER BY o.sequence
    </select>

    <select id="getOrgSelfRegisterSum" resultType="java.lang.Integer">
        SELECT
            COUNT(v.id)
        FROM zyz_volunteer v
        LEFT JOIN sys_org o ON v.org_code = o.code
        WHERE v.is_deleted = 0 AND v.write_off = 0 AND o.code_prefix LIKE CONCAT(#{codePrefix}, '%')
        <if test="end != null">
            AND founded &lt;= #{end}
        </if>
    </select>

    <select id="getOtherOrgRegisterNum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM zyz_volunteer
        WHERE is_deleted = 0 AND write_off = 0
        AND org_code NOT LIKE 'jinjihu%'
        AND org_code NOT LIKE 'loufeng%'
        AND org_code NOT LIKE 'xietang%'
        AND org_code NOT LIKE 'shengpu%'
        AND org_code NOT LIKE 'weiting%'
        <if test="end != null">
            AND founded &lt;= #{end}
        </if>
    </select>

    <select id="getNeedClearExpiredPointRecords" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerPointClearDto">
        SELECT
            id AS id,
            total_point AS totalPoint,
            this_year_achieve_point AS thisYearAchievePoint
        FROM view_volunteer_point
        WHERE total_point > this_year_achieve_point
    </select>
</mapper>
