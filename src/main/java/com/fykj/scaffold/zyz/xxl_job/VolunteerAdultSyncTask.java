package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fykj.scaffold.zyz.conns.ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD;


/**
 * 志愿者成年字段刷新
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VolunteerAdultSyncTask {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @XxlJob("Volunteer_Adult_Sync_Handler")
    public void volunteerSyncJobHandler() {
        XxlJobHelper.log("Volunteer_Adult_Sync_Handler-Job, START!");
        try {
            volunteerService.updateIsAdult();
        } catch (Exception e) {
            log.error("更新志愿者是否成年任务失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Volunteer_Adult_Sync_Handler-Job, END!");
    }
}
