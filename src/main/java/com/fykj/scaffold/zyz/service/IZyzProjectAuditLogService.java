package com.fykj.scaffold.zyz.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectAuditLog;

import java.util.List;

/**
 * 项目操作记录表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-06-01
 */
public interface IZyzProjectAuditLogService extends IBaseService<ZyzProjectAuditLog> {

    /**
     * 根据项目id获取项目操作记录
     * @param projectId
     * @return
     */
    List<ZyzProjectAuditLog> getLogListByProjectId(Long projectId,String auditType);


    /**
     * 根据项目id和类型保存审核记录
     * @param projectId
     * @return
     */
    void saveLogByProjectIdAndType(Long projectId,String auditType,String remark,String operateType);
}

