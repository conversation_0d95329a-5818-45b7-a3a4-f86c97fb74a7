package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 接口-资源映射表
 * 
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_api_action")
public class SysApiAction extends BaseEntity  {

	private static final long serialVersionUID = 1L;

	/**
	 * 接口id
	 */
	@TableField("api_id")
    @ApiModelProperty(value = "接口id")
		@JsonSerialize(using = ToStringSerializer.class)
		private Long apiId;

	/**
	 * 资源id
	 */
	@TableField("action_id")
    @ApiModelProperty(value = "按钮id")
		@JsonSerialize(using = ToStringSerializer.class)
		private Long actionId;


}
