package com.fykj.scaffold.zyz.controller.mini;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.mq.cons.TopicCons;
import com.fykj.scaffold.mq.event.VolunteerSyncEvent;
import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.FaceIdentifyResultDto;
import com.fykj.scaffold.zyz.domain.dto.MiniVolunteerDto;
import com.fykj.scaffold.zyz.domain.dto.RadarMapDto;
import com.fykj.scaffold.zyz.domain.dto.VolunteerSearchDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerCertificate;
import com.fykj.scaffold.zyz.service.IZyzVolunteerCertificateService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.support.util.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;
import utils.UUIDUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@RestController
@RequestMapping("/admin/mini/volunteer")
@Api(tags = "微信小程序志愿者接口--需要登录")
public class MiniVolunteerController {

    //实名认证二次校验的url
    final static String URL = "https://api.weixin.qq.com/cityservice/face/identify/getinfo";
    private final static String REDIS_FACE_IDENTIFY_RESULT_KEY = "REDIS_FACE_IDENTIFY_RESULT_KEY";
    @Autowired
    private IZyzVolunteerService volunteerService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ISysOrgService orgService;
    @Autowired
    private RocketMQTemplate mqTemplate;
    @Autowired
    private IZyzVolunteerCertificateService volunteerCertificateService;
    @Value("${spring.profiles.active:''}")
    private String env;

    @ApiOperation("step1.生成小程序人脸识别二维码")
    @GetMapping("/generateFaceIdentifyQrCode")
    public Result generateFaceIdentifyQrCode(String appId, String name, String idCard) {
        String requestUid = UUIDUtils.generateUuid();
        final WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
        File qrCodeFile;
        try {
            if ("dev".equals(env) || "test".equals(env)) {
                qrCodeFile = wxMaService.getQrcodeService().createWxaCode("/pages_sub/pages/user/face-pc?requestUid="
                        + requestUid + "&name=" + name + "&idCard=" + idCard, "trial", 430, true, null, false);
            } else {
                qrCodeFile = wxMaService.getQrcodeService().createWxaCode("/pages_sub/pages/user/face-pc?requestUid="
                        + requestUid + "&name=" + name + "&idCard=" + idCard);
            }
//            qrCodeFile = wxMaService.getQrcodeService().createWxaCode("/pages_sub/pages/user/face-pc?requestUid="
//                    + requestUid + "&name=" + name + "&idCard=" + idCard);
        } catch (WxErrorException e) {
            log.error("生成人脸识别二维码失败", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "生成认证二维码失败");
        }
        Map<String, Object> map = new HashMap<>();
        byte[] fileData = FileUtil.readBytes(qrCodeFile);
        String base64Str = Base64.encode(fileData);
        map.put("requestUid", requestUid);
        map.put("img", base64Str);
        return new JsonResult<>(map);
    }

    @ApiOperation("step3.查询用户是否在小程序端完成了人脸识别")
    @GetMapping("/queryPcFaceIdentifyResult")
    public Result pcFaceIdentify(String requestUid) {
        return new JsonResult<>(redisService.get(REDIS_FACE_IDENTIFY_RESULT_KEY + requestUid));
    }

    @ApiOperation("二次校验接口")
    @GetMapping("/faceIdentify")
    public Result faceIdentify(String appId, String verifyResult, String name, String idCard) {
        if (identify(appId, verifyResult)) {
            volunteerService.faceIdentifySuccess(name, idCard);
        }
        return new Result();
    }

    private boolean identify(String appId, String verifyResult) {
        final WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
        Map<String, Object> param = new HashMap<>();
        param.put("verify_result", verifyResult);
        String post;
        try {
            post = wxMaService.post(URL, JSON.toJSON(param));
            FaceIdentifyResultDto faceIdentifyResultDto = JSON.parseObject(post, FaceIdentifyResultDto.class);
            if (faceIdentifyResultDto.getErrcode() != 0 || faceIdentifyResultDto.getIdentify_ret() != 0) {
                throw new BusinessException(ResultCode.DATA_EXPIRED, "二次校验失败，请重新认证！");
            }
            //修改实名状态
            return true;
        } catch (WxErrorException e) {
            log.error("二次校验接口调取失败!", e);
            throw new BusinessException(ResultCode.DATA_EXPIRED, "二次校验接口调取失败");
        }
    }

    @ApiOperation("获取登录账号志愿者详情--小程序端")
    @GetMapping("/getVolunteerForMini")
    public JsonResult<ZyzVolunteer> getVolunteerForMini() {
        BackendUserDetail userDetail = Oauth2Util.getUser();
        if (userDetail == null || StringUtil.isEmpty(userDetail.getMobile())) {
            throw new BusinessException(ResultCode.FAIL, "无法查询到当前登录人的手机号信息，请联系管理员");
        }
        ZyzVolunteer volunteer = volunteerService.getByPhone(userDetail.getMobile());
        if (volunteer != null && StringUtil.isNotEmpty(volunteer.getOrgCode())) {
            SysOrg streetOrg = orgService.getParentByCode(volunteer.getOrgCode());
            volunteer.setStreetOrgCode(streetOrg != null ? streetOrg.getCode() : "");
        }
        volunteerService.formatCascaderData(volunteer);
        DictTransUtil.trans(volunteer);
        return new JsonResult<>(volunteer);
    }


    @ApiOperation("修改志愿者 -- 小程序")
    @PostMapping("/updateVolunteer")
    public Result updateVolunteer(@RequestBody MiniVolunteerDto dto) {
        ZyzVolunteer volunteer = volunteerService.getVolunteerById(dto.getId());
        //所属社区发生了变化
        if (!volunteer.getOrgCode().equals(dto.getOrgCode())) {
            //今年改过了
            if (volunteer.getOrgCodeLastModifyDate() != null && LocalDate.now().getYear() == volunteer.getOrgCodeLastModifyDate().getYear()) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "无法修改所属区域，每个自然年只能修改一次");
            }
            if (ObjectUtil.isNotEmpty(dto.getLon()) && ObjectUtil.isNotEmpty(dto.getLat())) {
                volunteer.setLongitude(dto.getLon());
                volunteer.setLatitude(dto.getLat());
            }
            volunteer.setOrgCodeLastModifyDate(LocalDate.now());
        }
        BeanUtil.copyProperties(dto, volunteer);
        volunteer.setPerfect(true);
        if (!volunteerService.updateById(volunteer)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return new Result();
    }

    @ApiOperation("新注册志愿者")
    @PostMapping("/newRegisterVolunteer")
    public Result newRegisterVolunteer(@RequestBody MiniVolunteerDto dto) {
        //如果是身份证，则校验实名认证结果
        if (ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD.equals(dto.getCertificateType())) {
            identify(dto.getAppId(), dto.getVerifyResult());
        }
        long volunteerId = volunteerService.newRegisterVolunteer(dto);
        //发送同步mq
        mqTemplate.send(TopicCons.VOLUNTEER_SYNC, MessageBuilder.withPayload(VolunteerSyncEvent.builder().volunteerId(volunteerId).build()).build());
        return new Result();
    }

    @ApiOperation("新注册已实名的志愿者")
    @PostMapping("/registerCertificationedVolunteer")
    public Result registerCertificationedVolunteer(@RequestBody MiniVolunteerDto dto) {
        volunteerService.newRegisterVolunteer(dto);
        return new Result();
    }

    @ApiOperation("实名认证之前志愿者信息验证")
    @PostMapping("/registerCheck")
    public Result registerCheck(@RequestBody ZyzVolunteer entity) {
      volunteerService.registerCheck(entity);
        return new Result();
    }

    @ApiOperation("雷达图")
    @GetMapping({"/getRadarMap"})
    public JsonResult<RadarMapDto> getRadarMap() {
        RadarMapDto result = volunteerService.getRadarMap();
        result.setStar(getStar());
        result.setStarPercentage(BigDecimal.valueOf(result.getStar()).divide(BigDecimal.valueOf(5), RoundingMode.CEILING));
        return new JsonResult<>(result);
    }

    @ApiOperation("更换手机号")
    @GetMapping({"/changePhone"})
    public Result changePhone(String newPhone, String smsCode) {
        String realCode = redisService.get(newPhone + Cons.SmsValidCodeTemplate.CHANGE_PHONE.name());
        if (StringUtil.isEmpty(newPhone)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请输入正确的手机号");
        }
        if (StringUtil.isEmpty(realCode) || !realCode.equals(smsCode)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "验证码错误，无法更换");
        }
        volunteerService.changePhone(Oauth2Util.getMobile(), newPhone);
        return new Result();
    }

    private Integer getStar() {
        ZyzVolunteer volunteer = volunteerService.lambdaQuery()
                .eq(ZyzVolunteer::getPhone, Oauth2Util.getMobile())
                .eq(ZyzVolunteer::getWriteOff, false).one();
        if (ObjectUtil.isEmpty(volunteer)) {
            throw new BusinessException(ResultCode.DATA_EXPIRED, "当前志愿者未绑定！");
        }
        if (volunteer.getServiceLong().compareTo(BigDecimal.valueOf(100)) < 0) {
            return 0;
        }
        //一星级 100
        if (volunteer.getServiceLong().compareTo(BigDecimal.valueOf(300)) < 0) {
            return 1;
        }
        //二星级 300
        if (volunteer.getServiceLong().compareTo(BigDecimal.valueOf(600)) < 0) {
            return 2;
        }
        //三星级 600
        if (volunteer.getServiceLong().compareTo(BigDecimal.valueOf(1000)) < 0) {
            return 3;
        }
        //四星级 1000
        if (volunteer.getServiceLong().compareTo(BigDecimal.valueOf(1500)) < 0) {
            return 4;
        }
        //五星级 1500
        return 5;
    }

    @ApiOperation("查看是否有旧数据")
    @GetMapping("/hasOldVolunteer")
    public Result hasOldVolunteer(String certificateId) {

        // 正常表数据判断
        ZyzVolunteer volunteer = volunteerService.lambdaQuery().eq(ZyzVolunteer::getCertificateId, certificateId).eq(ZyzVolunteer::getWriteOff, false).one();
        if (ObjectUtil.isNotEmpty(volunteer)) {
            return new JsonResult<>(new Result(1, "正常表里有数据"));
        }
        //脏表数据判断
        ZyzVolunteer volunteerOfDirtyTable = volunteerService.getVolunteerOfDirtyTable(certificateId);
        if (ObjectUtil.isNotEmpty(volunteerOfDirtyTable)) {
            return new JsonResult<>(new Result(2, "脏表里有数据"));
        }
        // 无数据
        return new JsonResult<>(new Result(-1, "无旧数据"));
    }

    //获取志愿者星级
    @ApiOperation("获取志愿者星级--小程序端")
    @GetMapping("/getVolunteerStar")
    public JsonResult<Integer> getVolunteerStar() {
        return new JsonResult<>(getStar());
    }

    //获取志愿者证书列表
    @ApiOperation("获取志愿者证书列表")
    @GetMapping("/getAwardedCertificateList")
    public JsonResult<List<ZyzVolunteerCertificate>> getAwardedCertificateList() {
        List<ZyzVolunteerCertificate> list = volunteerCertificateService.getAwardedCertificateList(Oauth2Util.getMobile());
        return new JsonResult<>(list);
    }

    @ApiOperation("生成证书pdf")
    @GetMapping({"/generateCertificatePdfById"})
    public JsonResult<ZyzVolunteerCertificate> generateCertificatePdfById(@RequestParam Long id) {
        return new JsonResult<>(this.volunteerCertificateService.generateCertificatePdfById(id));
    }

    @ApiOperation("根据关键词搜索志愿者(姓名或手机号全等")
    @GetMapping("/searchVolunteer")
    public JsonResult<List<VolunteerSearchDto>> searchVolunteer(
            @ApiParam("姓名或手机号") @Valid @NotBlank(message = "姓名不能为空") @RequestParam String keyword) {
        List<VolunteerSearchDto> volunteers = volunteerService.searchVolunteerWithMask(keyword);
        return new JsonResult<>(volunteers);
    }

    @ApiOperation("验证姓名、手机号和志愿者ID是否匹配")
    @GetMapping("/verifyVolunteer")
    public Result verifyVolunteer(
            @ApiParam("姓名") @Valid @NotBlank(message = "姓名不能为空") @RequestParam String name,
            @ApiParam("电话") @Valid @NotBlank(message = "电话不能为空") @RequestParam  String phone,
            @ApiParam("志愿者ID") @Valid @NotNull(message = "志愿者ID不能为空") @RequestParam Long volunteerId) {
        boolean matched = volunteerService.verifyVolunteer(name, phone, volunteerId);
        if (matched) {
            return new Result();
        } else {
            return new Result(ResultCode.FAIL.code(), "姓名或手机号与志愿者信息不匹配");
        }
    }
}

