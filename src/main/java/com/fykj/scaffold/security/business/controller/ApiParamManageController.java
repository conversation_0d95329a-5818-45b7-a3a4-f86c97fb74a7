package com.fykj.scaffold.security.business.controller;


import com.fykj.scaffold.security.business.domain.entity.ApiParamManage;
import com.fykj.scaffold.security.business.domain.params.ApiParamManageParams;
import com.fykj.scaffold.security.business.service.IApiParamManageService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/apiParam")
@Api(tags = "接口管理")
public class ApiParamManageController extends BaseController<IApiParamManageService, ApiParamManage, ApiParamManageParams> {

    @ApiOperation("根据接口id查询参数列表")
    @GetMapping(value = "/getParamListByUrlId")
    public JsonResult<List<ApiParamManage>> getParamListByUrlId(@RequestParam Long urlId) {
        List<ApiParamManage> apiParamList = baseService.getParamByUrlId(urlId);
        return new JsonResult<>(apiParamList);
    }
}
