package com.fykj.scaffold.advanced_unit.cons;

import com.fykj.scaffold.support.conns.SysMsgTmpCons;
import exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import result.ResultCode;

import java.util.Arrays;

/**
 * 2025/3/12
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
@AllArgsConstructor
public enum AdvancedUnitAuditOperation {
    PASS("PASS", AdvancedUnitAuditStatus.PASS.getCode(), SysMsgTmpCons.ADVANCE_AUDIT_PASS),
    REJECT("REJECT", AdvancedUnitAuditStatus.REJECT.getCode(), SysMsgTmpCons.ADVANCE_UNIT_AUDIT_REJECT)
    ;

    private final String code;

    private final String statusCode;

    private final String smsTempCode;

    public final static String DICT_CODE = "AU_AUDIT_OPERATION";

    public static AdvancedUnitAuditOperation getByCode(String code) {
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ResultCode.FAIL, "传入错误的操作值"));
    }
}
