package com.fykj.scaffold.support.auth;

import exception.BusinessException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import result.ResultCode;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * API Token验证切面
 * 用于拦截带有ApiToken注解的方法，并验证token
 */
@Aspect
public class ApiTokenAspect {

    private static final Logger logger = LoggerFactory.getLogger(ApiTokenAspect.class);

    @Autowired
    private ApiTokenProperties apiTokenProperties;

    /**
     * 环绕通知，拦截带有ApiToken注解的方法
     */
    @Around("@annotation(com.fykj.scaffold.support.auth.ApiToken) || @within(com.fykj.scaffold.support.auth.ApiToken)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 如果未启用token验证，直接放行
        if (!apiTokenProperties.isEnabled()) {
            return joinPoint.proceed();
        }

        // 获取方法上的ApiToken注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ApiToken methodAnnotation = method.getAnnotation(ApiToken.class);

        // 获取类上的ApiToken注解
        ApiToken classAnnotation = method.getDeclaringClass().getAnnotation(ApiToken.class);

        // 如果方法上的注解存在且不需要验证，直接放行
        if (methodAnnotation != null && !methodAnnotation.required()) {
            return joinPoint.proceed();
        }

        // 如果类上的注解存在且不需要验证，且方法上没有注解，直接放行
        if (classAnnotation != null && !classAnnotation.required() && methodAnnotation == null) {
            return joinPoint.proceed();
        }

        // 获取请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            logger.error("无法获取请求上下文");
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取请求上下文");
        }

        HttpServletRequest request = attributes.getRequest();
        
        // 获取请求头中的token
        String token = request.getHeader(apiTokenProperties.getHeaderName());
        
        // 如果token为空，返回401
        if (token == null || token.isEmpty()) {
            logger.warn("请求头中缺少token: {}", request.getRequestURI());
            throw new BusinessException(ResultCode.UNAUTHORIZED, "未提供有效的访问令牌");
        }
        
        // 验证token是否在配置的keys中
        if (!apiTokenProperties.getKeys().containsKey(token)) {
            logger.warn("无效的token: {}, URI: {}", token, request.getRequestURI());
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无效的访问令牌");
        }
        
        // token验证通过，继续执行原方法
        String clientName = apiTokenProperties.getKeys().get(token);
        logger.debug("API访问验证通过: 客户端[{}], URI: {}", clientName, request.getRequestURI());
        
        return joinPoint.proceed();
    }
} 