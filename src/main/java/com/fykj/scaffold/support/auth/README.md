# API Token验证功能

## 功能说明

API Token验证功能用于对外部接口进行访问控制，通过检查请求头中的token是否在配置的白名单中来决定是否允许访问。

## 使用方法

### 1. 配置API Token

在`application.yml`或`application-xxx.yml`中添加如下配置：

```yaml
api:
  token:
    # 是否启用token验证，默认为true
    enabled: true
    # token请求头名称，默认为X-API-TOKEN
    header-name: X-API-TOKEN
    # token映射，key为token值(uuid)，value为名称
    keys:
      "550e8400-e29b-41d4-a716-************": "移动端应用"
      "6ba7b810-9dad-11d1-80b4-00c04fd430c8": "Web管理后台"
```

### 2. 在Controller上使用注解

#### 在类级别使用

```java
@RestController
@RequestMapping("/api/example")
@ApiToken // 在类级别添加注解，该控制器下的所有方法都需要验证token
public class ExampleController {
    // ...
}
```

#### 在方法级别使用

```java
@RestController
@RequestMapping("/api/example")
public class ExampleController {

    @GetMapping("/required")
    @ApiToken // 该方法需要验证token
    public JsonResult<String> required() {
        // ...
    }

    @GetMapping("/not-required")
    // 不添加注解，该方法不需要验证token
    public JsonResult<String> notRequired() {
        // ...
    }
}
```

#### 在类级别添加，但某些方法不需要验证

```java
@RestController
@RequestMapping("/api/example")
@ApiToken // 在类级别添加注解，该控制器下的所有方法默认都需要验证token
public class ExampleController {

    @GetMapping("/required")
    // 不添加注解，使用类级别的设置，该方法需要验证token
    public JsonResult<String> required() {
        // ...
    }

    @GetMapping("/not-required")
    @ApiToken(required = false) // 在方法级别覆盖类级别的注解设置，该方法不需要验证token
    public JsonResult<String> notRequired() {
        // ...
    }
}
```

### 3. 客户端调用

客户端在调用API时，需要在请求头中添加token：

```
X-API-TOKEN: 550e8400-e29b-41d4-a716-************
```

### 4. 在代码中获取当前请求的API Token

```java
import com.fykj.scaffold.support.auth.ApiTokenUtil;

// ...

String token = ApiTokenUtil.getCurrentToken();
// 或者指定请求头名称
String token = ApiTokenUtil.getCurrentToken("Custom-Token-Header");
```

## 错误处理

当token验证失败时，会抛出`BusinessException`异常，状态码为`401`，错误信息为：
- 未提供token：`未提供有效的访问令牌`
- token无效：`无效的访问令牌`

这些异常会被`GlobalExceptionHandler`捕获并返回给客户端。 