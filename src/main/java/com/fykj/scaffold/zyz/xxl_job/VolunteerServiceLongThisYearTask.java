package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 志愿者本年服务时长清空
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VolunteerServiceLongThisYearTask {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @XxlJob("Volunteer_Service_Long_This_Year_Handler")
    public void volunteerSyncJobHandler() {
        XxlJobHelper.log("Volunteer_Service_Long_This_Year_Handler-Job, START!");
        // 获取待同步的志愿者数据以及志愿者创建时间三天内志愿者同步失败的数据
            try {
                volunteerService.clearVolunteerServiceLongThisYear();
            } catch (Exception e) {
                log.error("志愿者本年服务时长清空失败，失败原因：{}", e.getMessage(), e);
            }
        XxlJobHelper.log("Volunteer_Service_Long_This_Year_Handler-Job, END!");
    }
}
