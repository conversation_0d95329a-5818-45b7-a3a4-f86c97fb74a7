package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTime;
import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTimeResult;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 志愿者加入活动招募的服务时长同步
 */
@Component
@Slf4j
public class ServiceTimeSyncer {

    @Autowired
    private IZyzActivityApplyService applyService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog syncOne(ActSyncRecordDto applyRecord) {
        SyncServiceTime syncServiceTime = generateParams(applyRecord);

        SyncServiceTimeResult syncServiceTimeResult = ApiWebService.syncServiceTime(syncServiceTime);

        return dealSyncResult(syncServiceTimeResult, syncServiceTime, applyRecord);
    }

    public void syncBatch(List<ActSyncRecordDto> applyRecords) {
        if (CollectionUtil.isEmpty(applyRecords)) {
            return;
        }
        List<SyncServiceTime> serviceTimeList = new ArrayList<>();
        applyRecords.forEach(it -> {
            serviceTimeList.add(generateParams(it));
        });

        List<SyncServiceTimeResult> syncServiceTimeResults = ApiWebService.syncServiceTimeBatch(serviceTimeList);
        syncServiceTimeResults.forEach(it -> {
            transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(@NonNull TransactionStatus transactionStatus) {
                    try {
                        ActSyncRecordDto applyRecord = applyRecords.stream().filter(im -> it.getUniCode().equals(String.valueOf(im.getApplyId()))).findAny().orElse(new ActSyncRecordDto());
                        SyncServiceTime params = serviceTimeList.stream().filter(im -> it.getUniCode().equals(im.getUniCode())).findAny().orElse(new SyncServiceTime());
                        dealSyncResult(it, params, applyRecord);
                    } catch (Exception e) {
                        transactionStatus.setRollbackOnly();
                        log.error("id为[{}]的活动报名的服务时长同步异常！异常原因：{}", it.getUniCode(), e.getMessage(), e);
                    }
                }
            });
        });
    }

    private SyncServiceTime generateParams(ActSyncRecordDto applyRecord) {
        SyncServiceTime syncServiceTime = new SyncServiceTime();
        syncServiceTime.setUniCode(String.valueOf(applyRecord.getApplyId()));
        syncServiceTime.setIdCode(applyRecord.getVolunteerCertId());
//        syncServiceTime.setIdCode("320586199508082000");
        syncServiceTime.setActId(applyRecord.getActRecruitSyncId());
//        syncServiceTime.setActId(null);
        LocalDateTime start = applyRecord.getActStartTime().isBefore(applyRecord.getSignTime()) ? applyRecord.getSignTime() : applyRecord.getActStartTime();
        // 秒不为0，分钟进位；只保留到分钟，要不然和市区的计算方式会有差异，比如12:00:30，我们传给市区是12:01，
        if (start.getSecond() != 0) {
            start = start.plusMinutes(1);
        }
        syncServiceTime.setStartTime(start.format(DateTimeFormatter.ofPattern(Cons.TIME_FORMAT)));
        syncServiceTime.setEndTime(applyRecord.getActEndTime().format(DateTimeFormatter.ofPattern(Cons.TIME_FORMAT)));
        syncServiceTime.setServerTime(null);
        return syncServiceTime;
    }

    private ZyzSyncLog dealSyncResult(SyncServiceTimeResult result, SyncServiceTime params, ActSyncRecordDto applyRecord) {
        ZyzActivityApply apply = applyService.getById(applyRecord.getApplyId());
        apply.setServiceLongSyncTime(LocalDateTime.now());
        apply.setServiceLongSyncRemark(result.getMsg());
        //成功
        if ("同步成功".equals(result.getMsg())) {
            apply.setServiceLongIsSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (result.getMsg().contains("已同步过")) {
            apply.setServiceLongIsSync(Cons.PlatformSyncState.SUCCESS);
            apply.setServiceLongSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            apply.setServiceLongIsSync(Cons.PlatformSyncState.FAILURE);
        }
        String startTime = applyRecord.getActStartTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        String endTime = applyRecord.getActEndTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        // 启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.SERVICE_TIME_SYNC, result.getCode(), result.getMsg()
                , IdUtil.getSnowflake(1, 1).nextId(), apply.getId(), applyRecord.getVolunteerName() + "在【" + applyRecord.getActName() + "（" + startTime + "--" + endTime + "）" + "】活动中的服务时长"
                , JSONUtil.toJsonStr(params), JSONUtil.toJsonStr(result));

        // 回写业务
        applyService.updateById(apply);

        return log;
    }
}
