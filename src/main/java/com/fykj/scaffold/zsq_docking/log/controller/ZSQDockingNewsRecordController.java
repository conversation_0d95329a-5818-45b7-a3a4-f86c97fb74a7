package com.fykj.scaffold.zsq_docking.log.controller;

import com.fykj.scaffold.zsq_docking.log.domain.dto.NewsDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import com.fykj.scaffold.zsq_docking.log.domain.params.ZSQDockingNewsRecordParams;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingNewsRecordService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 知社区新闻对接记录
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/admin/zsq_docking_news_record")
@Api(tags = "知社区新闻对接记录")
public class ZSQDockingNewsRecordController extends BaseController<IZSQDockingNewsRecordService, ZSQDockingNewsRecord, ZSQDockingNewsRecordParams> {

    @ApiOperation("获取全部方法")
    @GetMapping({"/getNewsDockingRecords"})
    public JsonResult<List<ZSQDockingNewsRecord>> getNewsDockingRecords(@RequestParam Long newsId) {
        return new JsonResult<>(baseService.getNewsDockingRecords(newsId));
    }

    @ApiOperation("失败重试")
    @GetMapping({"/retry"})
    public Result retry(@RequestParam Long newsId, @RequestParam Long recordId) {
        baseService.retry(newsId, recordId);
        return OK;
    }

    @ApiOperation("获取对接记录详情")
    @GetMapping({"/getDockingRecordDetail"})
    public JsonResult<NewsDockingRecordDetailDto> getDockingRecordDetail(@RequestParam Long recordId) {
        return new JsonResult<>(baseService.getDockingRecordDetail(recordId));
    }
}
