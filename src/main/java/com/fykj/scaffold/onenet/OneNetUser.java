package com.fykj.scaffold.onenet;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 一网通办用户信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OneNetUser implements Serializable {
    private static final long serialVersionUID = 8660921317822219420L;

    /**
     * 用户唯一ID
     */
    private String uuid;

    /**
     * 用户登录名
     */
    private String loginname;

    /**
     * 用户姓名（已实名的用户，姓名才具有真实性）
     */
    private String name;

    /**
     * 证件类型（默认为1）
     * 1身份证
     * 3港澳通行证
     * 4台湾通行证
     * 5外籍人士永久居留证
     */
    private String paperstype;

    /**
     * 证件号码
     */
    private String papersnumber;

    /**
     * 手机号码
     */
    private String mobile;
}
