package com.fykj.scaffold.advanced_unit.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 文明单位-指标标准-测评指标
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("au_standard_index")
public class AuStandardIndex extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 测评版本ID
     */
    @NotNull
    @TableField("standard_id")
    @ApiModelProperty(value = "测评版本ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardId;

    /**
     * 测评项ID
     */
    @NotNull
    @TableField("item_id")
    @ApiModelProperty(value = "测评项ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;

    /**
     * 测评项名称
     */
    @TableField("name")
    @ApiModelProperty(value = "测评项名称")
    private String name;

    /**
     * 测评方法及内容
     */
    @TableField("content")
    @ApiModelProperty(value = "测评方法及内容")
    private String content;

    /**
     * 分值
     */
    @TableField("score")
    @ApiModelProperty(value = "分值")
    private Integer score;

    /**
     * 顺序
     */
    @TableField("order_num")
    @ApiModelProperty(value = "顺序")
    private Integer orderNum;

    /**
     * 爬取ID
     */
    @TableField("spider_id")
    @ApiModelProperty(value = "原始ID")
    private String spiderId;
}
