package com.fykj.scaffold.sync.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 市平台信息同步日志
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_sync_log")
public class ZyzSyncLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 幂等id
     */
    @TableField("sync_bill_id")
    @ApiModelProperty(value = "幂等id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long syncBillId;

    /**
     * 同步业务类型（志愿者/团队/加入团队/活动...）
     */
    @DictTrans
    @TableField("biz_type")
    @ApiModelProperty(value = "同步业务类型（志愿者/团队/加入团队/活动...）")
    private String bizType;

    /**
     * 业务表id
     */
    @TableField("biz_id")
    @ApiModelProperty(value = "业务表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bizId;

    /**
     * 同步对象
     */
    @TableField("sync_object_name")
    @ApiModelProperty(value = "同步对象")
    private String syncObjectName;

    /**
     * 是否成功
     */
    @TableField("result_code")
    @ApiModelProperty(value = "是否成功")
    private String resultCode;

    /**
     * 市平台返回的消息
     */
    @TableField("message")
    @ApiModelProperty(value = "市平台返回的消息")
    private String message;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @ApiModelProperty(value = "同步时间")
    private LocalDateTime syncTime;

    /**
     * 业务编码
     */
    @TableField("sp_code")
    @ApiModelProperty(value = "业务编码")
    private String spCode;

    /**
     * 请求体
     */
    @TableField("request_param")
    @ApiModelProperty(value = "请求体")
    private String requestParam;

    /**
     * 响应
     */
    @TableField("response")
    @ApiModelProperty(value = "响应")
    private String response;


}
