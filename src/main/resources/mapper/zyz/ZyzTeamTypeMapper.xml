<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzTeamTypeMapper">

    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeamType">
        SELECT
            t.*,
            aa.teamCount
        FROM
            `zyz_team_type` t
        LEFT JOIN (
                SELECT
                    r.type_id,
                    COUNT( t.id ) AS teamCount
                FROM
                    zyz_type_team_relevance r
                        LEFT JOIN zyz_team t ON t.is_deleted = FALSE
                        AND r.team_id = t.id
                WHERE
                    r.is_deleted = FALSE
                GROUP BY
                    type_id
            ) aa ON aa.type_id = t.id
        WHERE
            t.is_deleted = FALSE
        <if test="params.name != null and params.name != ''">
            and t.name LIKE CONCAT('%',#{params.name},'%')
        </if>
        ORDER BY t.create_date  desc
    </select>
</mapper>