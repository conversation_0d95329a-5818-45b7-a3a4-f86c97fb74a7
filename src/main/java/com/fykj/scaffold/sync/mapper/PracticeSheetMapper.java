package com.fykj.scaffold.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto;
import com.fykj.scaffold.sync.domain.entity.PracticeSheet;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogStatisticsParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  实践单Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface PracticeSheetMapper extends BaseMapper<PracticeSheet> {


    /**
     * 分页查询资源审核
     * @param page
     * @param params
     * @return
     */
    IPage<SyncStatisticsDto> statisticsPage(IPage<SyncStatisticsDto> page, @Param("params") ZyzSyncLogStatisticsParams params);

    /**
     * 根据年月查询统计信息
     *
     * @param yearMon
     * @return
     */
    SyncStatisticsDto getByYearMon(@Param("yearMon") String yearMon);


    /**
     * 导出
     *
     * @param params
     * @return
     */
    List<ZyzSyncLogDto> exportStatistics(@Param("params") ZyzSyncLogStatisticsParams params);



    List<SyncFailStatisticsDto> statisticsFailList(@Param("params") ZyzSyncLogStatisticsParams params);

}
