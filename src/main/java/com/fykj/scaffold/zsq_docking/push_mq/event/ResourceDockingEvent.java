package com.fykj.scaffold.zsq_docking.push_mq.event;

import com.fykj.scaffold.zsq_docking.push_mq.dto.ResourceSyncForZyzDto;
import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceDockingEvent extends BaseDockingMqEvent {

    /**
     * 对接记录 id
     */
    private Long dockingRecordId;

    /**
     * 资源 id
     */
    private Long resourceId;

    /**
     * 消息 id
     */
    private String operation;
    /**
     * 消息 id
     */
    private String zwtUserId;
    /**
     * 上/下架
     */
    private Boolean onShelf;
    /**
     * 资源信息
     */
    private ResourceSyncForZyzDto  resourceSyncForZyzDto;

    /**
     * 志愿者平台用户 id
     */
    private Serializable zyzUserId;
}
