package com.fykj.scaffold.security.login.controller;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.config.WmyqSsoAuthProperties;
import com.fykj.scaffold.support.utils.ApiThirdAuthUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.ResultCode;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 2025/2/20
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@Api(tags = "文明园区单点登录相关")
@RequestMapping("/admin/sso/wmyq")
public class SsoController {

    @Autowired
    private WmyqSsoAuthProperties ssoAuthProperties;

    @ApiOperation("获取文明园区登录信息")
    @PostMapping(value = "/login")
    public JsonResult<Object> login() {
        String mobile = Oauth2Util.getMobile();
        return new JsonResult<>(ResultCode.OK, postWmyqOkHttp(
                mobile, ssoAuthProperties.getLoginApi()));
    }

    @ApiOperation("获取文明园区菜单")
    @PostMapping(value = "/resource")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupType", value = "0:后台菜单/1:app菜单", required = true, paramType = "query")
    })
    public JsonResult<Object> resource(@RequestParam Integer groupType) {
        String mobile = Oauth2Util.getMobile();
        return new JsonResult<>(ResultCode.OK, getWmyqOkHttp(
                mobile, ssoAuthProperties.getResourceApi(), Collections.singletonMap("groupType", groupType)));
    }

    private final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            // 单一主机, 减少重复握手
            .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
            .build();

    public Object postWmyqOkHttp(String mobile, String api) {
        // 构造请求body，json格式数据
        Map<String, Object> body = Collections.singletonMap("mobile", mobile);
        // 生成签名参数
        Map<String, Object> form = ApiThirdAuthUtil.generateAuthParams(
                ssoAuthProperties.getKey(), ssoAuthProperties.getSecret());
        // 组装请求url，利用 HttpUrl.Builder 自动拼接 query 参数
        String baseUrl = ssoAuthProperties.getDomain() + api;
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(baseUrl)).newBuilder();
        form.forEach((key, value) -> urlBuilder.addQueryParameter(key, value.toString()));
        String url = urlBuilder.build().toString();

        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = RequestBody.create(mediaType, JSON.toJSONString(body));

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            return getObjectFromResponse(response);
        } catch (IOException e) {
            log.error("请求失败: URL={}, 错误信息={}", url, e.getMessage());
            throw new BusinessException(ResultCode.ERROR, "第三方服务连接异常");
        }
    }

    public Object getWmyqOkHttp(String mobile, String api, Map<String, Object> queryMap) {
        // 生成签名参数
        Map<String, Object> form = ApiThirdAuthUtil.generateAuthParams(
                ssoAuthProperties.getKey(), ssoAuthProperties.getSecret());
        form.putAll(queryMap);
        form.put("mobile", mobile);
        // 组装请求url
        String baseUrl = ssoAuthProperties.getDomain() + api;
        HttpUrl.Builder urlBuilder = HttpUrl.parse(baseUrl).newBuilder();
        form.forEach((key, value) -> urlBuilder.addQueryParameter(key, value.toString()));
        String url = urlBuilder.build().toString();

        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            return getObjectFromResponse(response);
        } catch (IOException e) {
            log.error("请求失败: URL={}, 错误信息={}", url, e.getMessage());
            throw new BusinessException(ResultCode.ERROR, "第三方服务连接异常");
        }
    }

    private Object getObjectFromResponse(Response response) throws IOException {
        if (!response.isSuccessful()) {
            throw new BusinessException(ResultCode.ERROR, "获取文明园区信息失败");
        }
        String responseBodyStr = Objects.requireNonNull(response.body()).string();
        try {
            JSONObject responseObj = JSON.parseObject(responseBodyStr);
            if (responseObj.containsKey("code") && "0".equals(responseObj.getString("code"))) {
                return responseObj.getJSONObject("obj");
            } else {
                throw new BusinessException(ResultCode.ERROR, responseObj.getString("msg"));
            }
        } catch (Exception e) {
            // 若响应不是 JSON 格式，则直接返回字符串
            return responseBodyStr;
        }
    }

    public Object postWmyq(String mobile, String api) {
        // 生成请求body
        Map<String, Object> body = Collections.singletonMap("mobile", mobile);
        // 生成签名form
        Map<String, Object> form = ApiThirdAuthUtil.generateAuthParams(
                ssoAuthProperties.getKey(), ssoAuthProperties.getSecret());
        // 组装请求url
        String url = HttpUtil.urlWithForm(ssoAuthProperties.getDomain() + api, form,
                CharsetUtil.CHARSET_UTF_8, true);
        // 发起请求
        HttpRequest request = HttpUtil.createPost(url);
        request.body(JSON.toJSONString(body));
        return getObjectFromRequest(request);
    }

    public Object getWmyq(String mobile, String api, Map<String, Object> queryMap) {
        // 生成签名form
        Map<String, Object> form = ApiThirdAuthUtil.generateAuthParams(
                ssoAuthProperties.getKey(), ssoAuthProperties.getSecret());
        form.putAll(queryMap);
        form.put("mobile", mobile);
        // 组装请求url
        String url = HttpUtil.urlWithForm(ssoAuthProperties.getDomain() + api, form,
                CharsetUtil.CHARSET_UTF_8, true);
        // 发起请求
        HttpRequest request = HttpUtil.createGet(url);
        return getObjectFromRequest(request);
    }

    private Object getObjectFromRequest(HttpRequest request) {
        request.header("Content-Type", "application/json; charset=utf-8");
        request.setConnectionTimeout(30000);
        request.setReadTimeout(30000);
        try (HttpResponse response = request.execute()) {
            if (!response.isOk()) {
                throw new BusinessException(ResultCode.ERROR, "获取文明园区信息失败");
            }
            String responseBodyStr = response.body();
            if(JSONUtil.isTypeJSON(responseBodyStr)) {
                JSONObject responseObj = JSON.parseObject(responseBodyStr);
                if(responseObj.containsKey("code") && "0".equals(responseObj.getString("code"))) {
                    return responseObj.getJSONObject("obj");
                }else {
                    throw new BusinessException(ResultCode.ERROR, responseObj.getString("msg"));
                }
            }
            return responseBodyStr;
        } catch (IORuntimeException e) {
            log.error("请求失败: URL={}, 错误信息={}", request.getUrl(), e.getMessage());
            throw new BusinessException(ResultCode.ERROR, "第三方服务连接异常");
        }
    }
}
