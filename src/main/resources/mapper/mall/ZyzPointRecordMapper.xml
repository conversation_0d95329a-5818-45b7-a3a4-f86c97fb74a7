<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.ZyzPointRecordMapper">
    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.ZyzPointRecord">
        select v.* from zyz_point_record v left join
            sys_org o on v.org_code=o.code
        where v.is_deleted=FALSE
        <if test="params.name != null and params.name != ''">
            and v.volunteer_name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.certificateId != null and params.certificateId != ''">
            and v.certificate_id LIKE CONCAT('%',#{params.certificateId},'%')
        </if>
        <if test="params.maxExchangeBeforePoint != null and params.maxExchangeBeforePoint != ''">
            and v.exchange_before_point &lt;= #{params.maxExchangeBeforePoint}
        </if>
        <if test="params.minExchangeBeforePoint != null and params.minExchangeBeforePoint != ''">
            and v.exchange_before_point &gt;= #{params.minExchangeBeforePoint}
        </if>
        <if test="params.maxExchangeAfterPoint != null and params.maxExchangeAfterPoint != ''">
            and v.exchange_after_point &lt;= #{params.maxExchangeAfterPoint}
        </if>
        <if test="params.minExchangeAfterPoint != null and params.minExchangeAfterPoint != ''">
            and v.exchange_after_point &gt;= #{params.minExchangeAfterPoint}
        </if>
        <if test="params.bizType != null and params.bizType != ''">
            and v.biz_type LIKE CONCAT('%',#{params.bizType},'%')
        </if>
        <if test="params.startTime != null  ">
            AND v.exchange_time <![CDATA[ >= ]]> #{params.startTime}
        </if>
        <if test="params.endTime != null  ">
            AND v.exchange_time  <![CDATA[ <= ]]> #{params.endTime}
        </if>
        <if test="params.remark != null and params.remark != ''">
            and v.remark LIKE CONCAT('%',#{params.remark},'%')
        </if>
        order by v.exchange_time desc
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.mall.domain.entity.ZyzPointRecord">
        select v.* from zyz_point_record v
        where v.is_deleted=FALSE and v.volunteer_id=#{params.volunteerId}
        <if test="params.name != null and params.name != ''">
            and v.volunteer_name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.certificateId != null and params.certificateId != ''">
            and v.certificate_id LIKE CONCAT('%',#{params.certificateId},'%')
        </if>
        <if test="params.type != null and params.type != ''">
            and v.type = #{params.type}
        </if>
        <if test="params.maxExchangeBeforePoint != null and params.maxExchangeBeforePoint != ''">
            and v.exchange_before_point &lt;= #{params.maxExchangeBeforePoint}
        </if>
        <if test="params.minExchangeBeforePoint != null and params.minExchangeBeforePoint != ''">
            and v.exchange_before_point &gt;= #{params.minExchangeBeforePoint}
        </if>
        <if test="params.maxExchangeAfterPoint != null and params.maxExchangeAfterPoint != ''">
            and v.exchange_after_point &lt;= #{params.maxExchangeAfterPoint}
        </if>
        <if test="params.minExchangeAfterPoint != null and params.minExchangeAfterPoint != ''">
            and v.exchange_after_point &gt;= #{params.minExchangeAfterPoint}
        </if>
        order by v.exchange_time desc
    </select>
</mapper>