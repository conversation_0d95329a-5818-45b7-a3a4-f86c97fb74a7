package com.fykj.scaffold.sgb_docking.task;

import com.fykj.scaffold.sgb_docking.service.ISgbRegionService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 苏州志愿者平台区划信息同步任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbRegionSyncTask {

    @Autowired
    private ISgbRegionService sgbRegionService;

    /**
     * 区划信息同步任务
     */
    @XxlJob("SGB_Region_Sync_Handler")
    public void regionSyncJobHandler() {
        XxlJobHelper.log("SGB_Region_Sync_Handler-Job, START!");
        try {
            sgbRegionService.syncRegion();
        } catch (Exception e) {
            log.error("苏州志愿者平台区划信息同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("SGB_Region_Sync_Handler-Job, END!");
    }
} 