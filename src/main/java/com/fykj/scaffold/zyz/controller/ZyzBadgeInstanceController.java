package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeInstance;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeInstanceParams;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceVO;
import com.fykj.scaffold.zyz.service.IZyzBadgeInstanceService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 勋章实例-接口控制器
 *
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/admin/zyz/badge/instance")
@Api(tags = "勋章实例-管理接口")
public class ZyzBadgeInstanceController extends BaseController<IZyzBadgeInstanceService, ZyzBadgeInstance, ZyzBadgeInstanceParams> {

    /**
     * 分页查询勋章列表
     * 
     * @param params 查询参数
     * @return 分页勋章实例列表（含勋章基础信息）
     */
    @ApiOperation("分页查询勋章列表")
    @PostMapping("/pageBadges")
    public JsonResult<IPage<ZyzBadgeInstanceVO>> pageBadges(@RequestBody(required = false) ZyzBadgeInstanceParams params) {
        IPage<ZyzBadgeInstanceVO> result = baseService.pageBadges(params);
        return new JsonResult<>(result);
    }
}
