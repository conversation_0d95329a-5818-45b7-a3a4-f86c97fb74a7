package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 苏州社工部组织同步请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbSyncTeamRequest {

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 管理员身份证 - saveType=1时必填
     */
    private String idCode;

    /**
     * 部门编号 - 2.3部门单位接口返回
     */
    private String dpId;

    /**
     * 组织所属区域代码
     */
    private String regionCode;

    /**
     * 组织简介
     */
    private String descs;

    /**
     * 负责人
     */
    private String linkName;

    /**
     * 负责人联系电话
     */
    private String linkPhone;

    /**
     * 父组织编号 - 组织注册接口返回
     */
    private String fId;

    /**
     * 第三方平台唯一键
     */
    private String uniqueCode;

    /**
     * 组织部门logo图片 - base64编码
     */
    private String picLogoUrl;

    /**
     * 团队性质 - 1民政注册登记 2备案组织 3单位团队 4其他
     */
    private String attribute;

    /**
     * 所属单位 - 团队性质为3单位团队时必填
     */
    private String orgDept;

    /**
     * 机构代码证书扫描件地址 - 团队性质为1民政注册登记必填
     */
    private String certNoUrl;

    /**
     * 是否社会工作服务机构 - 0否 1是
     */
    private String sfSocailOrg;

    /**
     * 服务区域 - 最多5个值，多个则以英文逗号分割
     */
    private String fwRegion;

    /**
     * 服务领域 - 最多5个值，多个则以英文逗号分割
     */
    private String serviceTypes;

    /**
     * 服务对象 - 最多5个值，多个则以英文逗号分割
     */
    private String serviceTarget;

    /**
     * 团队类型 - 最多5个值，多个则以英文逗号分割
     */
    private String teamClassify;

    /**
     * 是否成立党组织 - 0否 1是
     */
    private String sfPartyOrg;

    /**
     * 接口调用类型 - 1新增，2修改
     */
    private String saveType;

    /**
     * 组织id - saveType为2时必填
     */
    private String orgId;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncTeamInfo info = new SyncTeamInfo();
        info.setInfo(this);
        return JSON.toJSONString(info);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncTeamInfo {
        private SgbSyncTeamRequest info;
    }
}
