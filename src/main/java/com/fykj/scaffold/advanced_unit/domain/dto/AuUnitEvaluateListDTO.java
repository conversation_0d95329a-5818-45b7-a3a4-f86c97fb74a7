package com.fykj.scaffold.advanced_unit.domain.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 2025/3/28
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AuUnitEvaluateListDTO {

    @ApiModelProperty(value = "ID")
    private String orgId;

    @ApiModelProperty(value = "申报ID")
    private String applyId;

    @ApiModelProperty(value = "标准ID")
    private String standardId;

    @ApiModelProperty(value = "单位logo")
    private String unitLogo;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "统一社会信用代码（18位）")
    private String creditCode;

    @ApiModelProperty(value = "自评总分")
    private Integer selfScore;

    @ApiModelProperty(value = "复评打分")
    private Integer finalScore;

    @ApiModelProperty(value = "自评次数")
    private Integer selfCount;

    @ApiModelProperty(value = "复评次数")
    private Integer finalCount;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime updateDate;

}
