package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.dto.PointContributePersonDto;
import com.fykj.scaffold.mall.domain.entity.ZyzPointContributeRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointContributeRecordParams;
import fykj.microservice.core.base.BaseParams;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分商城-积分捐赠记录
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface ZyzPointContributeRecordMapper extends BaseMapper<ZyzPointContributeRecord> {
    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    @DataFilter
    IPage<ZyzPointContributeRecord> getPageOrList(IPage<ZyzPointContributeRecord> page, @Param("params") ZyzPointContributeRecordParams params);

    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    @DataFilter
    List<ZyzPointContributeRecord> getPageOrList( @Param("params") ZyzPointContributeRecordParams params);

    /**
     * 分页查询--捐赠名单展示
     * @param page
     * @return
     */
    IPage<PointContributePersonDto> pageForPointContributePerson(Page<PointContributePersonDto> page);

    /**
     * 获取总捐赠积分
     * @return
     */
    BigDecimal getTotalContributePoint();

    /**
     * 获取总捐赠人数
     * @return
     */
    Integer getTotalContributePerson();
}
