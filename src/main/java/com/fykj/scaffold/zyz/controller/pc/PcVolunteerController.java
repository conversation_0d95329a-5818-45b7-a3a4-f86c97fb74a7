package com.fykj.scaffold.zyz.controller.pc;

import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import utils.StringUtil;

@Slf4j
@RestController
@RequestMapping("/admin/pc/volunteer")
@Api(tags = "PC志愿者接口--需要登录")
public class PcVolunteerController {

    @Autowired
    private IUserService userService;

    @ApiOperation("是否绑定openId")
    @GetMapping("/isBindOpenId")
    public Result isBindOpenId() {
        long userId = (long) Oauth2Util.getUserId();
        User u = userService.getById(userId);
        return new JsonResult<>(StringUtil.isNotEmpty(u.getUnionId()));
    }

    @ApiOperation("解除绑定")
    @GetMapping("/unbindOpenId")
    public Result unbindOpenId() {
        userService.lambdaUpdate().eq(BaseEntity::getId, Oauth2Util.getUserId()).set(User::getUnionId, null).update();
        userService.refreshUserContext(Oauth2Util.getUser().getUsername());
        return new Result();
    }

}

