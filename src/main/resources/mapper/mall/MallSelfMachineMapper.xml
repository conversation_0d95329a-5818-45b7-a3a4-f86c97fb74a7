<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallSelfMachineMapper">
    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallSelfMachine">
        select v.*,so.name as orgName from mall_self_machine v left join   sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.machineNo != null and params.machineNo != ''">
            and v.machine_no LIKE CONCAT('%',#{params.machineNo},'%')
        </if>
        <if test="params.linkPhone != null and params.linkPhone != ''">
            and v.link_phone LIKE CONCAT('%',#{params.linkPhone},'%'
        </if>
        <if test="params.status != null and params.status != ''">
            and v.status = #{params.status}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and so.code_prefix LIKE CONCAT(#{params.orgCode},'%')
        </if>
        order by v.create_date desc
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.mall.domain.entity.MallSelfMachine">
        select v.*,so.name as orgName
        <if test="params.latitude != null and params.latitude != '' and params.longitude != null and params.longitude != '' ">
            ,(6378.138*2*ASIN(SQRT(POW(SIN((#{params.latitude}*PI()/180-v.latitude*PI()/180)/2),2)
            + COS(#{params.latitude}*PI()/180) * COS(v.latitude*PI()/180) *
            POW(SIN((#{params.longitude}*PI()/180-v.longitude*PI()/180)/2),2)))*1000) as distance
        </if>
        from mall_self_machine v left join   sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.machineNo != null and params.machineNo != ''">
            and v.machine_no LIKE CONCAT('%',#{params.machineNo},'%')
        </if>
        <if test="params.linkPhone != null and params.linkPhone != ''">
            and v.link_phone LIKE CONCAT('%',#{params.linkPhone},'%'
        </if>
        <if test="params.status != null and params.status != ''">
            and v.status = #{params.status}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and so.code_prefix LIKE CONCAT(#{params.orgCode},'%')
        </if>
        <if test="params.latitude != null and params.latitude != '' and params.longitude != null and params.longitude != '' ">
         order by distance
        </if>
        <if test="params.latitude == null or params.latitude == '' or params.longitude == null or params.longitude == '' ">
            order by v.create_date desc
        </if>
    </select>

    <select id="getMachinesByCurrOrg" resultType="com.fykj.scaffold.mall.domain.entity.MallSelfMachine">
        SELECT
            msm.*,
            so.name AS orgName
        FROM mall_self_machine msm
        LEFT JOIN sys_org so ON msm.org_code = so.code
        WHERE msm.is_deleted = 0
        ORDER BY msm.create_date DESC
    </select>
</mapper>
