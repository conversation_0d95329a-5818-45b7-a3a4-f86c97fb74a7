package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzCalendar;
import com.fykj.scaffold.zyz.domain.params.ZyzCalenderParams;
import com.fykj.scaffold.zyz.service.IZyzCalenderService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.time.LocalDate;

/**
 * 志愿者与团队关系表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-13
 */
@RestController
@RequestMapping("/admin/zyz/calendar")
public class ZyzCalendarController extends BaseController<IZyzCalenderService, ZyzCalendar, ZyzCalenderParams> {



    @ApiOperation("设置工作日")
    @GetMapping("/setWorkDay")
    public Result setWorkDay(String date) {
        baseService.setWorkDay(LocalDate.parse(date));
        return OK;
    }

    @ApiOperation("找到最近的工作日")
    @GetMapping("/findNextWorkDay")
    public Result findNextWorkDay() {
        return new JsonResult<>(baseService.findNextWorkDay());
    }

}

