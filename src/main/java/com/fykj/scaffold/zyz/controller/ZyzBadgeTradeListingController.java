package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTradeListing;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeTradeListingParams;
import com.fykj.scaffold.zyz.service.IZyzBadgeTradeListingService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 勋章交易发布-接口控制器
 *
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/admin/zyz/badge/trade-listing")
@Api(tags = "勋章交易发布-管理接口")
public class ZyzBadgeTradeListingController
        extends BaseController<IZyzBadgeTradeListingService, ZyzBadgeTradeListing, ZyzBadgeTradeListingParams> {

    @Autowired
    private IZyzBadgeTradeListingService badgeTradeListingService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    public JsonResult<Page<ZyzBadgeTradeListing>> page(@RequestBody ZyzBadgeTradeListingParams params) {
        params.setStatus(0);
        return new JsonResult<>(badgeTradeListingService.pageQuery(params));
    }

}
