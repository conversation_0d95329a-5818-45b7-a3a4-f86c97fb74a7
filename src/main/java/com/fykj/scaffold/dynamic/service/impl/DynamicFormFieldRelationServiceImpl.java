package com.fykj.scaffold.dynamic.service.impl;

import com.fykj.scaffold.dynamic.domain.entity.DynamicFormFieldRelation;
import com.fykj.scaffold.dynamic.mapper.DynamicFormFieldRelationMapper;
import com.fykj.scaffold.dynamic.service.IDynamicFormFieldRelationService;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;


/**
 * 模板-字段关联-服务实现类
 *
 * @date 2025-05-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DynamicFormFieldRelationServiceImpl extends BaseServiceImpl<DynamicFormFieldRelationMapper, DynamicFormFieldRelation> implements IDynamicFormFieldRelationService {

    @Override
    public boolean removeByIds(Collection<?> idList) {
        return baseMapper.removeByIds(idList);
    }

    @Override
    public boolean removeById(Serializable id) {
        return baseMapper.removeById(id);
    }

    @Override
    public boolean removeByFormId(Serializable formId) {
        return baseMapper.removeByFormId(formId);
    }
}
