package com.fykj.scaffold.security.business.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.security.business.domain.entity.User;
import fykj.microservice.cache.support.DictTrans;
import lombok.*;
import org.springframework.security.core.GrantedAuthority;

import java.util.*;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class BackendUserDetail extends org.springframework.security.core.userdetails.User {

    private static final long serialVersionUID = 4374424667663347893L;

    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 姓名
     */
    private String nickName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 角色关键字
     */
    private List<Long> roleIds = new ArrayList<>();

    /**
     * 角色code集合
     */
    private List<String> roleCodes = new ArrayList<>();

    /**
     * 管理账号身份
     */
    private String managerCapacity;

    /**
     * 管理账号身份
     */
    private String managerCapacityName;

    /**
     * 组织代码
     */
    private String orgCode;

    /**
     * 团队id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    /**
     * 组织代码链接
     */
    private String orgCodePrefix;

    /**
     * 是否需要选择身份（当为多个身份时需要）
     */
    private boolean needSelectCapacity = false;

    /**
     * 是否可以微信快捷登录
     */
    private boolean canWechatFastLogin = false;

    /**
     * expand表id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long expandId;


    public BackendUserDetail(String username, String password,
                             Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
    }

    public BackendUserDetail(String username, String password, boolean enabled,
                             boolean accountNonExpired, boolean credentialsNonExpired,
                             boolean accountNonLocked,
                             Collection<? extends GrantedAuthority> authorities) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired,
                accountNonLocked, authorities);
    }

    public List<Long> getRoleIds() {
        return Collections.unmodifiableList(roleIds);
    }

    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }

    public List<String> getRoleCodes() {
        return Collections.unmodifiableList(roleCodes);
    }

    public void setRoleCodes(List<String> roleCodes) {
        this.roleCodes = roleCodes;
    }

    /**
     * 是否拥有此角色
     *
     * @param roleCode 角色代码
     * @return 是否
     */
    public boolean isHasRole(String roleCode) {
        return this.roleCodes.contains(roleCode);
    }

}
