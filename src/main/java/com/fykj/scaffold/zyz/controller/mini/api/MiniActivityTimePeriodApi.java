package com.fykj.scaffold.zyz.controller.mini.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.MiniActivityTimePeriodDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityTimePeriodParams;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/mini/activity-time-period")
@Api(tags = "微信小程序活动时间段接口--不需要登录")
public class MiniActivityTimePeriodApi {
    @Autowired
    private IZyzActivityTimePeriodService timePeriodService;

    @ApiOperation("时间段分页")
    @PostMapping({"/timePeriodPages"})
    public JsonResult<IPage<MiniActivityTimePeriodDto>> timePeriodPages(@RequestBody ZyzActivityTimePeriodParams params) {
        params.setLogin(false);
        IPage<MiniActivityTimePeriodDto> result = timePeriodService.miniTimePeriodPage(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("时间段分页")
    @GetMapping({"/getTimePeriodByActivityId"})
    public JsonResult<List<ZyzActivityTimePeriod>> getTimePeriodByActivityId(Long activityId) {
        List<ZyzActivityTimePeriod> result = timePeriodService.getTimePeriodByActivityId(activityId);
        return new JsonResult<>(result);
    }

}
