package com.fykj.scaffold.support.oss.impl;

import com.fykj.scaffold.security.business.domain.entity.OssConfig;
import com.fykj.scaffold.security.business.service.IOssConfigService;
import com.fykj.scaffold.support.oss.FilePathDto;
import com.fykj.scaffold.support.oss.IOssSaver;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import constants.Mark;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import result.ResultCode;
import utils.StringUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

@Slf4j
@Component(OssCons.OSS_IMPL_BEAN_PREFIX + OssCons.OSS_LOCAL)
public class LocalOssSaver implements IOssSaver {

    @Value("${spring.profiles.active:''}")
    private String env;
    @Value("${oss.local.path:'~'}")
    private String localPath;

    @Autowired
    private IOssConfigService ossConfigService;

    @Override
    public FilePathDto save(InputStream is, String fileName) {
        OssConfig config = ossConfigService.getConfig();
        checkConf(config);
        String ext = OssSaveUtil.getExt(fileName);
        String virtualPath = getVirtualPath(config.getStorageLocation(), ext);
        String targetFile = virtualPath + File.separator + UUID.randomUUID().toString() + Mark.DOT + ext;
        File destFile = new File(config.getStorageLocation() + File.separator + targetFile);
        writeFile(destFile, is);
        targetFile = targetFile.replaceAll("\\\\", "/");
        FilePathDto result = new FilePathDto();
        result.setUrl(config.getUrl() + Mark.SLASH + targetFile);
        result.setRealPath(destFile.getPath());
        return result;
    }


    /**
     * 写文件
     *
     * @param destFile 目标文件夹
     * @param is       输入流
     */
    private void writeFile(File destFile, InputStream is) {
        try (FileOutputStream fos = new FileOutputStream(destFile)) {
            FileCopyUtils.copy(is, fos);
        } catch (IOException e) {
            log.error("文件保存失败io异常", e);
            throw new BusinessException(ResultCode.ERROR, "文件保存失败io异常");
        }
    }

    /**
     * 校验配置是否完善
     *
     * @param config 配置
     */
    private void checkConf(OssConfig config) {
        if (config == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "无OSS配置，请先初始化OSS配置");
        }
        if (StringUtil.isEmpty(config.getUrl()) || StringUtil.isEmpty(config.getStorageLocation())) {
            throw new BusinessException(ResultCode.NOT_VALID, "本地存储的OSS配置不完整，请完善后再试");
        }
    }
}
