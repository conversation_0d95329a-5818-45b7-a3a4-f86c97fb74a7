package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_self_machine")
public class MallSelfMachine extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 机器编号
     */
    @TableField("machine_no")
    @ApiModelProperty(value = "机器编号")
    private String machineNo;

    /**
     * 组织机构code
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织机构code")
    private String orgCode;

    /**
     * 组织机构名称
     */
    @TableField("org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 地址
     */
    @TableField("address")
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 经度
     */
    @TableField("longitude")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @TableField("latitude")
    @ApiModelProperty(value = "维度")
    private BigDecimal latitude;

    /**
     * 负责人名称
     */
    @TableField("link_person_name")
    @ApiModelProperty(value = "负责人名称")
    private String linkPersonName;

    /**
     * 负责人联系电话
     */
    @TableField("link_phone")
    @ApiModelProperty(value = "负责人联系电话")
    private String linkPhone;

    /**
     * 状态数据字典值
     */
    @TableField("status")
    @ApiModelProperty(value = "状态数据字典值")
    @DictTrans(transTo = "statusText")
    private String status;
    @TableField(exist = false)
    @ApiModelProperty(value = "状态类型Text")
    private String statusText;
    /**
     * 备注
     */
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(exist = false)
    @ApiModelProperty(value = "距离")
    private Integer distance;
    /**
     * 兑换机Logo
     */
    @TableField("machine_logo")
    @ApiModelProperty(value = "兑换机Logo")
    private String machineLogo;

    /**
     * 是否可选择
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否可选择")
    private Boolean enableSelect;
}
