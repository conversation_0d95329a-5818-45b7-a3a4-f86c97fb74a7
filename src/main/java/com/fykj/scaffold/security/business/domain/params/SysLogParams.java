package com.fykj.scaffold.security.business.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 系统日志查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("系统日志查询参数")
public class SysLogParams extends BaseParams {
    private static final long serialVersionUID = 2910158825212804441L;

    @ApiModelProperty("关键字查询，模糊匹配用户名/操作名")
    @MatchType(value = QueryType.LIKE, fieldName = {"username", "operation"})
    private String key;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @MatchType(value = QueryType.TIME_START, fieldName = "createDate")
    private LocalDateTime startDate;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @MatchType(value = QueryType.TIME_END, fieldName = "createDate")
    private LocalDateTime endDate;
}
