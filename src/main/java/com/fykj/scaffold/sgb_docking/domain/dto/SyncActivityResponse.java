package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 推送活动信息响应
 */
@Data
public class SyncActivityResponse {
    
    /**
     * 返回数据
     */
    private List<ActivityResult> info;
    
    /**
     * 活动推送结果
     */
    @Data
    public static class ActivityResult {
        /**
         * 响应状态码
         * SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 活动ID
         */
        private String activityId;
        
        /**
         * 市平台生成的活动ID，仅当推送成功时有值
         */
        private String platformActivityId;
        
        /**
         * 处理结果：1-成功，0-失败
         */
        private String result;
        
        /**
         * 错误消息，仅当失败时有值
         */
        private String message;
    }
} 