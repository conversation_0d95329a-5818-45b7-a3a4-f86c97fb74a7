package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 物料进补计划管理实体
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_stock_replenish_plan")
public class MallStockReplenishPlan extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 进补计划类型
     */
    @TableField("type")
    @ApiModelProperty(value = "进补计划类型")
    private String type;

    /**
     * 发布者所属组织code
     */
    @TableField("publish_org_code")
    @ApiModelProperty(value = "发布者所属组织code")
    private String publishOrgCode;

    /**
     * 发布者所属组织名称
     */
    @TableField("publish_org_name")
    @ApiModelProperty(value = "发布者所属组织名称")
    private String publishOrgName;

    /**
     * 计划年度
     */
    @TableField("plan_year")
    @ApiModelProperty(value = "计划年度")
    private String planYear;

    /**
     * 预算金额
     */
    @TableField("budget_amount")
    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;

    /**
     * 实际金额
     */
    @TableField("actual_amount")
    @ApiModelProperty(value = "实际金额")
    private BigDecimal actualAmount;

    /**
     * 是否提交
     */
    @TableField("submit")
    @ApiModelProperty(value = "是否提交")
    private Boolean submit;

    /**
     * 兑换机id
     */
    @TableField("machine_id")
    @ApiModelProperty(value = "兑换机id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long machineId;

    /**
     * 兑换机编号
     */
    @TableField("machine_no")
    @ApiModelProperty(value = "兑换机编号")
    private String machineNo;

    /**
     * 计划详单
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "计划详单")
    private List<MallStockReplenishPlanDetail> detailList = new ArrayList<>();
}
