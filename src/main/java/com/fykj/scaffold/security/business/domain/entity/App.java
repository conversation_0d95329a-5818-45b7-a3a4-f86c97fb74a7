package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2021-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_app")
public class App extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 英文名称
     */
    @TableField("code")
    @ApiModelProperty(value = "英文名称")
    private String code;

    /**
     * 应用名称
     */
    @TableField("name")
    @ApiModelProperty(value = "应用名称")
    private String name;

    /**
     * AppId
     */
    @TableField("app_id")
    @ApiModelProperty(value = "AppId")
    private String appId;

    /**
     * 应用类型（数据字典code）
     */
    @DictTrans(transTo = "typeText")
    @TableField("type")
    @ApiModelProperty(value = "应用类型（数据字典code）")
    private String type;

    /**
     * 状态（启用，未启用）
     */
    @TableField("status")
    @ApiModelProperty(value = "状态（启用，未启用）")
    private Boolean status;

    /**
     * 官网地址
     */
    @TableField("website")
    @ApiModelProperty(value = "官网地址")
    private String website;

    /**
     * 开发者
     */
    @TableField("author")
    @ApiModelProperty(value = "开发者")
    private String author;

    /**
     * 应用图标
     */
    @TableField("icon")
    @ApiModelProperty(value = "应用图标")
    private String icon;

    /**
     * 描述
     */
    @TableField("remark")
    @ApiModelProperty(value = "描述")
    private String remark;


    /**
     * 客户端信息
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "客户端信息")
    private AppClient clientInfo;

    @TableField(exist = false)
    @ApiModelProperty(value = "应用类型")
    private String typeText;

    @TableField(exist = false)
    @ApiModelProperty(value = "应用接口id")
    private List<String> apiIdList;

}
