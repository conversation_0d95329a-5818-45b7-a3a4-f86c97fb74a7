package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.security.business.domain.dto.NavDto;
import com.fykj.scaffold.security.business.domain.dto.PrivilegeChangeDto;
import com.fykj.scaffold.security.business.domain.dto.SaCaAccountPrivilegesDto;
import com.fykj.scaffold.security.business.domain.entity.*;
import com.fykj.scaffold.security.business.domain.params.UserParams;
import com.fykj.scaffold.security.business.mapper.UserMapper;
import com.fykj.scaffold.security.business.service.*;
import com.fykj.scaffold.security.oauth2.access.MyUserDetailsServiceImpl;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.dto.CommunityAdminImportDto;
import com.fykj.scaffold.zyz.domain.dto.TeamAdminImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.util.SpringContextUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.Result;
import result.ResultCode;
import utils.StringUtil;
import utils.UUIDUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.Cons.RoleCode.*;
import static constants.Mark.COMMA;


/**
 * <p>
 * 用户服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserService {


    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private IResourceService resourceService;

    @Autowired
    private ISysActionService actionService;

    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private ISysUserExpandService expandService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzVolunteerTeamService volunteerTeamService;

    @Autowired
    private ISysWeakPwService sysWeakPwService;

    @Override
    public String getNameById(Long id) {
        return getById(id).getUsername();
    }


    @Override
    public String resetPassWord(Long id) {
        User user = getById(id);
        String pass = RandomUtil.randomString(5) + RandomUtil.randomString("@#$%^&", 1) + RandomUtil.randomStringUpper(5);
        user.setPassword(passwordEncoder.encode(pass));
        user.setLastUpdatePwdDate(LocalDate.now());
        super.updateById(user);
        return pass;
    }

    @Override
    public Result editPassWord(String username, String oldPassWord, String newPassWord) {
        User user = lambdaQuery().eq(User::getUsername, username).one();
        if (user == null || !passwordEncoder.matches(oldPassWord, user.getPassword())) {
            return new Result(ResultCode.FAIL.code(), "请输入正确的旧密码");
        }
        if(sysWeakPwService.isWeakPassword(newPassWord)){
            return new Result(ResultCode.FAIL.code(),"密码属于弱口令，请重新输入");
        }
        user.setPassword(passwordEncoder.encode(newPassWord));
        user.setLastUpdatePwdDate(LocalDate.now());
        if (super.updateById(user)) {
            return new Result();
        }
        return new Result(ResultCode.FAIL);
    }

    @Override
    public Result forceUpdatePwd(String username, String newPassWord) {
        User user = lambdaQuery().eq(User::getUsername, username).one();
        if(sysWeakPwService.isWeakPassword(newPassWord)){
            return new Result(ResultCode.FAIL.code(),"密码属于弱口令，请重新输入");
        }
        user.setPassword(passwordEncoder.encode(newPassWord));
        user.setForceUpdatePwd(false);
        user.setLastUpdatePwdDate(LocalDate.now());
        if (super.updateById(user)) {
            return new Result();
        }
        return new Result(ResultCode.FAIL);
    }


    @Override
    public IPage<User> page(BaseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new UserParams();
        }
        Page<User> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.findByPage(page, (UserParams) params);
    }

    @Override
    public boolean save(User user) {
        //验证用户名重复
        if (usernameExists(user.getUsername())) {
            throw new BusinessException(ResultCode.FAIL, "用户名已存在");
        }
        if (userPhoneExists(user.getMobile())) {
            throw new BusinessException(ResultCode.FAIL, "手机号已存在");
        }
        if(sysWeakPwService.isWeakPassword(user.getPassword())){
            throw new BusinessException(ResultCode.FAIL,"密码属于弱口令，请重新输入");
        }
        if (StringUtil.isNotEmpty(user.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        user.setManagerAccount(user.getRoleCodes().stream().anyMatch(it -> !it.equals(Cons.RoleCode.ROLE_CODE_VOLUNTEER)));
        user.setLastUpdatePwdDate(LocalDate.now());
        boolean ret = super.save(user);
        userRoleService.saveUserRoleRelationShip(user.getId(), user.getRoleCodes());
        expandService.saveExpands(user.getId(), user.getExpandList());
        refreshUserContext(user.getUsername());
        return ret;
    }

    @Override
    public boolean updateById(User user) {
        //验证用户名重复
        if (usernameExists(user.getUsername(), user.getId())) {
            throw new BusinessException(ResultCode.FAIL, "用户名已存在");
        }
        if (userPhoneExists(user.getMobile(), user.getId())) {
            throw new BusinessException(ResultCode.FAIL, "手机号已存在");
        }
        if(sysWeakPwService.isWeakPassword(user.getPassword())){
            throw new BusinessException(ResultCode.FAIL,"密码属于弱口令，请重新输入");
        }
        User entity = getById(user.getId());
        user.setPassword(entity.getPassword());
        user.setManagerAccount(user.getRoleCodes().stream().anyMatch(it -> !it.equals(Cons.RoleCode.ROLE_CODE_VOLUNTEER)));
        user.setMaOpenId(entity.getMaOpenId());
        userRoleService.saveUserRoleRelationShip(user.getId(), user.getRoleCodes());
        expandService.saveExpands(user.getId(), user.getExpandList());
        refreshUserContext(user.getUsername());
        return super.updateById(user);
    }

    private boolean usernameExists(String username, Long myId) {
        return lambdaQuery().eq(User::getUsername, username)
                .ne(myId != null, BaseEntity::getId, myId)
                .count() > 0;
    }

    private boolean usernameExists(String username) {
        return usernameExists(username, null);
    }

    private boolean userPhoneExists(String userPhone, Long myId) {
        return lambdaQuery().eq(User::getMobile, userPhone)
                .ne(myId != null, BaseEntity::getId, myId)
                .count() > 0;
    }

    @Override
    public boolean userPhoneExists(String userPhone) {
        return userPhoneExists(userPhone, null);
    }

    @Override
    public NavDto userMenusAndActionAuthorities(String type) {
        BackendUserDetail userDetail = Oauth2Util.getUser();
        if (userDetail == null) {
            throw new BusinessException(ResultCode.FAIL, "请先登录");
        }
        List<Resource> resources;
        List<String> pems;
        // 超管直接看全部
        if (Oauth2Util.isAdmin()) {
            resources = resourceService.lambdaQuery().eq(Resource::getType, type).eq(Resource::getStatus, Boolean.TRUE).orderByAsc(Resource::getSequence).list();
            pems = actionService.list().stream().map(SysAction::getActionCode).collect(Collectors.toList());
        } else {
            List<Role> userRoles = userRoleService.getByUserId((Long) Oauth2Util.getUserId());
            List<Long> roleIds = userRoles.stream().filter(it -> {
                        boolean isSysEmbedRole = Arrays.asList(Cons.RoleCode.SYS_EMBED_ROLE).contains(it.getCode());
                        boolean isCurrentCapacityRole = it.getCode().equals(Oauth2Util.getManagerCapacity());
                        //普通用户可见菜单=普通权限+当前切换的身份的权限
                        return !isSysEmbedRole || isCurrentCapacityRole;
                    }).map(BaseEntity::getId)
                    .collect(Collectors.toList());
            resources = CollectionUtil.isEmpty(roleIds) ? new ArrayList<>() : resourceService.findResourceByUserIdAndRoleIds(userDetail.getId(), type, roleIds);
            pems = CollectionUtil.isEmpty(roleIds) ? new ArrayList<>() : actionService.findActionByUserIdAndRoleIds(userDetail.getId(), roleIds).stream().map(SysAction::getActionCode).collect(Collectors.toList());
        }
        NavDto dto = new NavDto();
        dto.setMenuList(buildTree(resources));
        dto.setPermissions(pems);
        return dto;
    }

    @Autowired
    @Lazy
    private TokenStore tokenStore;

    @Override
    public void refreshUserContext(String username) {
        Cons.ClientList.CLIENT_LIST.forEach(client -> refreshUserContext(username, client));
    }

    @Override
    public void refreshUserContext(Long userId) {
        User user = lambdaQuery().eq(User::getId, userId).one();
        if (ObjectUtil.isNotEmpty(user)) {
            Cons.ClientList.CLIENT_LIST.forEach(client -> refreshUserContext(user.getUsername(), client));
        }
    }

    @Override
    public void refreshUserContext(String username, String clientId) {
        tokenStore.findTokensByClientIdAndUserName(clientId, username).forEach(oAuth2AccessToken -> {
            OAuth2Authentication currentOAuth2Authentication = tokenStore.readAuthentication(oAuth2AccessToken.getValue());
            if (currentOAuth2Authentication == null) {
                return;
            }
            try {
                BackendUserDetail userDetail = (BackendUserDetail) ((MyUserDetailsServiceImpl) SpringContextUtil.getBean("myUserDetailsServiceImpl")).loadUserByUsername(username);
                UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(userDetail, null, userDetail.getAuthorities());
                usernamePasswordAuthenticationToken.eraseCredentials();
                OAuth2Authentication newAuth = new OAuth2Authentication(currentOAuth2Authentication.getOAuth2Request(), usernamePasswordAuthenticationToken);
                tokenStore.removeAccessToken(oAuth2AccessToken);
                tokenStore.storeAccessToken(oAuth2AccessToken, newAuth);
            } catch (Exception e) {
                tokenStore.removeAccessToken(oAuth2AccessToken);
            }
        });
    }

    @Override
    public void forceOfflineUser(String username) {
        Cons.ClientList.CLIENT_LIST.forEach(client -> tokenStore.findTokensByClientIdAndUserName(client, username)
                .forEach(item -> tokenStore.removeAccessToken(item)));
    }

    @Override
    public void forceOfflineUserByPhone(String phone) {
        User user = lambdaQuery().eq(User::getMobile, phone).one();
        if (ObjectUtil.isNotEmpty(user)) {
            forceOfflineUser(user.getUsername());
        }
    }

    @Override
    public void forceOfflineUserByUserId(Long userId) {
        User user = lambdaQuery().eq(User::getId, userId).one();
        if (ObjectUtil.isNotEmpty(user)) {
            forceOfflineUser(user.getUsername());
        }
    }

    @Override
    public void updateOpenId(long userId, String openId) {
        lambdaUpdate().eq(User::getOpenId, openId).set(User::getOpenId, null).update();
        User user = getById(userId);
        user.setOpenId(openId);
        lambdaUpdate().eq(BaseEntity::getId, user.getId()).set(User::getOpenId, openId).update();
    }

    @Override
    public void updateUnionId(long userId, String unionId) {
        lambdaUpdate().eq(User::getUnionId, unionId).set(User::getUnionId, null).update();
        User user = getById(userId);
        user.setUnionId(unionId);
        lambdaUpdate().eq(BaseEntity::getId, user.getId()).set(User::getUnionId, unionId).update();
    }

    @Override
    public User getByOpenId(String openId) {
        return lambdaQuery().eq(User::getOpenId, openId).one();
    }

    @Override
    public User getByUnionId(String unionId) {
        return lambdaQuery().eq(User::getUnionId, unionId).one();
    }

    @Override
    public String genOneCode(String username) {
//        String oneCode = new RandomValueStringGenerator(32).generate();
        String oneCode = UUIDUtils.generateUuid();
        redisService.set("ONE_CODE" + username, oneCode, 60 * 5);
        return oneCode;
    }

    @Override
    public List<CommunityAdminImportDto> communityAdminImport(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请上传文件！");
        }
        List<CommunityAdminImportDto> dataList = ExcelUtil.readExcel(excel, CommunityAdminImportDto.class, 0);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        List<User> accounts = new ArrayList<>();
        dataList.forEach(it -> {
            it.setAdminOneImportEnable(Boolean.TRUE);
            it.setAdminTwoImportEnable(Boolean.TRUE);
            if (StringUtil.isNotEmpty(it.getAdminOnePhone())) {
                it.setAdminOnePhone(it.getAdminOnePhone().trim().replaceAll("\\s*", ""));
            }
            if (StringUtil.isNotEmpty(it.getAdminOneCertId())) {
                it.setAdminOneCertId(it.getAdminOneCertId().trim().replaceAll("\\s*", ""));
            }
            if (StringUtil.isNotEmpty(it.getAdminTwoPhone())) {
                it.setAdminTwoPhone(it.getAdminTwoPhone().trim().replaceAll("\\s*", ""));
            }
            if (StringUtil.isNotEmpty(it.getAdminTwoCertId())) {
                it.setAdminTwoCertId(it.getAdminTwoCertId().trim().replaceAll("\\s*", ""));
            }
            // 基础校验
            communityAdminAccountImportBaseValidate(it);
            if (!it.getAdminOneImportEnable() && !it.getAdminTwoImportEnable()) {
                return;
            }
            // 生成管理员账号
            generateCommunityAdminAccounts(accounts, it);
        });
        if (CollectionUtil.isEmpty(accounts)) {
            return dataList;
        }
        accounts.forEach(this::saveOrUpdate);
        return dataList;
    }

    @Override
    public List<TeamAdminImportDto> teamAdminImport(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请上传文件！");
        }
        List<TeamAdminImportDto> dataList = ExcelUtil.readExcel(excel, TeamAdminImportDto.class, 0);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        List<User> accounts = new ArrayList<>();
        dataList.forEach(it -> {
            it.setAdminImportEnable(Boolean.TRUE);
            if (StringUtil.isNotEmpty(it.getAdminPhone())) {
                it.setAdminPhone(it.getAdminPhone().trim().replaceAll("\\s*", ""));
            }
            if (StringUtil.isNotEmpty(it.getAdminCertId())) {
                it.setAdminCertId(it.getAdminCertId().trim().replaceAll("\\s*", ""));
            }
            // 基础校验
            teamAdminAccountImportBaseValidate(it);
            if (!it.getAdminImportEnable()) {
                return;
            }
            // 生成管理员账号
            generateTeamAdminAccounts(accounts, it);
        });
        if (CollectionUtil.isEmpty(accounts)) {
            return dataList;
        }
        accounts.forEach(this::saveOrUpdateTeamAdminAccount);
        return dataList;
    }

    private void communityAdminAccountImportBaseValidate(CommunityAdminImportDto record) {
        StringBuffer adminOneFailReason = new StringBuffer(Strings.EMPTY), adminTwoFailReason = new StringBuffer(Strings.EMPTY);
        String a1Phone = record.getAdminOnePhone(), a2Phone = record.getAdminTwoPhone(), a1CertId = record.getAdminOneCertId(), a2CertId = record.getAdminTwoCertId();
        boolean a1VPF = validatePhoneFormat(a1Phone, adminOneFailReason); // 校验管理员 1 手机号格式
        boolean a2VPF = validatePhoneFormat(a2Phone, adminTwoFailReason); // 校验管理员 2 手机号格式
        boolean a1VCF = validateCertIdFormat(a1CertId, adminOneFailReason); // 校验管理员 1 身份证号格式
        boolean a2VCF = validateCertIdFormat(a2CertId, adminTwoFailReason); // 校验管理员 2 身份证号格式
//        boolean a1a2VC = validateAdminInfoConflict(a1Phone, a2Phone, a1CertId, a2CertId); // 管理员 1 和管理员 2 手机号身份证号信息冲突
//        if (!a1a2VC) {
//            adminOneFailReason.append("管理员1和2手机号身份证号信息冲突！");
//            adminTwoFailReason.append("管理员1和2手机号身份证号信息冲突！");
//        }
        boolean a1VPC = validatePhoneCertIdUnique(a1Phone, a1CertId, adminOneFailReason); // 校验管理员 1 手机号身份证号的对应关系
        boolean a2VPC = validatePhoneCertIdUnique(a2Phone, a2CertId, adminTwoFailReason); // 校验管理员 2 手机号身份证号的对应关系
        boolean vc = validateCommunity(record);
        if (!vc) {
            adminOneFailReason.append("社区信息有误！");
            adminTwoFailReason.append("社区信息有误！");
        }
        record.setAdminOneFailReason(adminOneFailReason.toString());
        record.setAdminTwoFailReason(adminTwoFailReason.toString());
        record.setAdminOneImportEnable(a1VPF && a1VCF && vc);
        record.setAdminTwoImportEnable(a2VPF && a2VCF && vc);
    }

    private void teamAdminAccountImportBaseValidate(TeamAdminImportDto record) {
        StringBuffer failReason = new StringBuffer(Strings.EMPTY);
        String phone = record.getAdminPhone(), certId = record.getAdminCertId();
        boolean vPF = validatePhoneFormat(phone, failReason); // 校验管理员手机号格式
        boolean vCF = validateCertIdFormat(certId, failReason); // 校验管理员身份证号格式
        boolean vPC = validatePhoneCertIdUnique(phone, certId, failReason); // 校验管理员手机号身份证号的对应关系
        if (!vPF || !vCF) {
            record.setFailReason(failReason.toString());
            record.setAdminImportEnable(Boolean.FALSE);
            return;
        }
        boolean vt = validateTeam(record);
        if (!vt) {
            failReason.append("团队信息有误！");
        }
        record.setFailReason(failReason.toString());
        record.setAdminImportEnable(vt);
    }

    private boolean validatePhoneFormat(String phone, StringBuffer reason) {
        if (StringUtil.isEmpty(phone)) {
            reason.append("手机号不能为空！");
            return false;
        }
        if (!PhoneUtil.isMobile(phone)) {
            reason.append("手机号格式有误！");
            return false;
        }
        return true;
    }

    private boolean validateCertIdFormat(String certId, StringBuffer reason) {
        if (StringUtil.isEmpty(certId)) {
            reason.append("身份证号号不能为空！");
            return false;
        }
        if (!IdcardUtil.isValidCard(certId)) {
            reason.append("身份证号格式有误！");
            return false;
        }
        return true;
    }

    private boolean validateAdminInfoConflict(String a1Phone, String a2Phone, String a1CertId, String a2CertId) {
        if (StringUtil.isNotEmpty(a1Phone) && StringUtil.isNotEmpty(a2Phone) && StringUtil.isNotEmpty(a1CertId) && StringUtil.isNotEmpty(a2CertId)) {
            return (a1Phone.equals(a2Phone) && a1CertId.equals(a2CertId)) || (!a1Phone.equals(a2Phone) && !a1CertId.equals(a2CertId));
        }
        return true;
    }

    private boolean validatePhoneCertIdUnique(String phone, String certId, StringBuffer reason) {
        if (StringUtil.isEmpty(phone) || StringUtil.isEmpty(certId)) {
            return false;
        }
        boolean exist1 = volunteerService.lambdaQuery().eq(ZyzVolunteer::getPhone, phone).ne(ZyzVolunteer::getCertificateId, certId).eq(ZyzVolunteer::getWriteOff, false).exists();
        boolean exist2 = volunteerService.lambdaQuery().eq(ZyzVolunteer::getCertificateId, certId).ne(ZyzVolunteer::getPhone, phone).eq(ZyzVolunteer::getWriteOff, false).exists();
        if (exist1) {
            reason.append("存在手机号一样但身份证号不一样的志愿者数据！");
        }
        if (exist2) {
            reason.append("存在身份证号一样但手机号不一样的志愿者数据！");
        }
        return !exist1 && !exist2;
    }

    private boolean validateTeamAdminVolunteerInfo(String phone, String certId, StringBuffer reason) {
        boolean flag = true;
        List<ZyzVolunteer> exists = volunteerService.lambdaQuery().eq(ZyzVolunteer::getPhone, phone).eq(ZyzVolunteer::getWriteOff, false).list();
        if (CollectionUtil.isEmpty(exists)) {
            flag = false;
            reason.append("手机号尚未注册成为志愿者！");
        }
        if (exists.size() > 1) {
            flag = false;
            reason.append("存在手机号一样但身份证号不一样的志愿者数据！");
        }
        if (!certId.equals(exists.get(0).getCertificateId())) {
            flag = false;
            reason.append("手机号所注册的志愿者身份证不匹配！");
        }
        return flag;
    }

    private boolean validateCommunity(CommunityAdminImportDto record) {
        String community = record.getCommunity();
        if (StringUtil.isEmpty(community)) {
            return false;
        }
        if (!community.contains("新时代文明实践站")) {
            community = community.concat("新时代文明实践站");
        }
        SysOrg org = orgService.lambdaQuery().eq(SysOrg::getName, community).one();
        if (ObjectUtil.isEmpty(org)) {
            return false;
        }
        record.setCommunityCode(org.getCode());
        return true;
    }

    private boolean validateTeam(TeamAdminImportDto record) {
        String teamName = record.getTeamName();
        if (StringUtil.isEmpty(teamName)) {
            return false;
        }
        if (teamName.indexOf("1") == 0 || teamName.indexOf("2") == 0 || teamName.indexOf("3") == 0
            || teamName.indexOf("4") == 0 || teamName.indexOf("5") == 0 || teamName.indexOf("6") == 0
                || teamName.indexOf("7") == 0 || teamName.indexOf("8") == 0 || teamName.indexOf("9") == 0
                || teamName.indexOf("10") == 0 || teamName.indexOf("N") == 0) {
            teamName = teamName.substring(1);
        }
        if (teamName.indexOf(".") == 0 || teamName.indexOf("、") == 0) {
            teamName = teamName.substring(1);
        }
        if (teamName.lastIndexOf("】") == teamName.length() - 1) {
            int preIndex = teamName.lastIndexOf("【");
            if (preIndex > 0) {
                teamName = teamName.substring(0, preIndex);
            }
        }
        try {
            ZyzTeam team = teamService.lambdaQuery().eq(ZyzTeam::getName, teamName).one();
            if (ObjectUtil.isEmpty(team)) {
                return false;
            }
            Long teamId = team.getId();
            // 团队存在更新团队数据
            teamService.lambdaUpdate().eq(ZyzTeam::getId, teamId)
                        .set(ZyzTeam::getCivilRegistration, "是".equals(record.getCivilAffairsDeptRegister()) ? Boolean.TRUE : Boolean.FALSE)
                        .set(ZyzTeam::getCuratorName, record.getCuratorName())
                        .set(ZyzTeam::getCuratorJob, record.getCuratorPosition())
                        .set(ZyzTeam::getCuratorContact, record.getCuratorPhone())
                        .set(ZyzTeam::getCuratorCard, record.getCuratorCertId())
                        .set(ZyzTeam::getCuratorPartyMember, "是".equals(record.getCuratorPM()) ? Boolean.TRUE : Boolean.FALSE)
                        .set(ZyzTeam::getAdminName, record.getAdminName())
                        .set(ZyzTeam::getAdminJob, record.getAdminPosition())
                        .set(ZyzTeam::getAdminContact, record.getAdminPhone())
                        .set(ZyzTeam::getAdminCard, record.getAdminCertId())
                        .set(ZyzTeam::getAdminPartyMember, "是".equals(record.getAdminPM()) ? Boolean.TRUE : Boolean.FALSE)
                        .update();
            record.setTeamId(teamId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void generateCommunityAdminAccounts(List<User> generatedAccounts, CommunityAdminImportDto record) {
        String type = Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN, orgCode = record.getCommunityCode();
        if (record.getAdminOneImportEnable()) {
            try {
                User account = generateAccount(generatedAccounts, type, record.getAdminOnePhone(), record.getAdminOneCertId(), record.getAdminOneName(), orgCode, null);
                record.setNewSysAdminOneAccount(account.getUsername());
                record.setNewSysAdminOnePassword(account.getPassword());
            } catch (Exception e) {
                record.setAdminOneImportEnable(Boolean.FALSE);
                record.setAdminOneFailReason("管理员账号创建失败：" + e.getMessage());
            }
        }
        if (record.getAdminTwoImportEnable()) {
            try {
                User account = generateAccount(generatedAccounts, type, record.getAdminTwoPhone(), record.getAdminTwoCertId(), record.getAdminTwoName(), orgCode, null);
                record.setNewSysAdminTwoAccount(account.getUsername());
                record.setNewSysAdminTwoPassword(account.getPassword());
            } catch (Exception e) {
                record.setAdminTwoImportEnable(Boolean.FALSE);
                record.setAdminTwoFailReason("管理员账号创建失败：" + e.getMessage());
            }
        }
    }

    private void generateTeamAdminAccounts(List<User> generatedAccounts, TeamAdminImportDto record) {
        String type = Cons.RoleCode.ROLE_CODE_TEAM_ADMIN;
        Long teamId = record.getTeamId();
        try {
            User account = generateAccount(generatedAccounts, type, record.getAdminPhone(), record.getAdminCertId(), record.getAdminName(), null, teamId);
            record.setNewSysAdminAccount(account.getUsername());
            record.setNewSysAdminPassword(account.getPassword());
        } catch (Exception e) {
            record.setAdminImportEnable(Boolean.FALSE);
            record.setFailReason("管理员账号创建失败：" + e.getMessage());
        }
    }

    private User generateAccount(List<User> generatedAccounts, String type, String phone, String certId, String name, String orgCode, Long teamId) {
        List<User> exists = lambdaQuery().eq(User::getMobile, phone).list();
        User generated = generatedAccounts.stream().filter(it -> phone.equals(it.getMobile())).findAny().orElse(null);
        if (exists.size() > 1) {
            throw new BusinessException(ResultCode.FAIL, "系统存在多个该手机号对应的用户！");
        }
        // 本次导入生成过该手机账号
        if (generated != null) {
            dealGeneratedAccount(generated, type, orgCode, teamId);
            generatedAccounts.add(generated);
            return generated;
        }
        // 系统中存在的该手机账号
        if (CollectionUtil.isNotEmpty(exists)) {
            User account = exists.get(0);
            dealExistAccount(account, type, orgCode, teamId);
            account.setCertId(certId);
            generatedAccounts.add(account);
            return account;
        }
        // 系统中不存在该手机账号且本次导入也尚未生成过该手机账号
        User account = new User();
        account.setUsername(phone);
        account.setName(StringUtil.isEmpty(name) ? name : name.trim().replaceAll("\\s*", ""));
        account.setPassword(RandomUtil.randomString(5) + RandomUtil.randomString("@#$%^&", 1) + RandomUtil.randomStringUpper(5));
        account.setMobile(phone);
        account.setCertId(certId);
        if (ObjectUtil.isNotEmpty(teamId)) {
            account.setTeamIds(new ArrayList<>(Collections.singletonList(teamId)));
        }
        account.setStatus(Boolean.TRUE);
        SysUserExpand userExpand = generateUserExpand(type, orgCode, teamId);
        if (ObjectUtil.isNotEmpty(userExpand)) {
            account.setExpandList(new ArrayList<>(Collections.singletonList(userExpand)));
        }
        account.setRoleCodes(new ArrayList<>(Collections.singletonList(type)));
        account.setForceUpdatePwd(Boolean.TRUE);
        generatedAccounts.add(account);
        return account;
    }

    private void dealGeneratedAccount(User account, String type, String orgCode, Long teamId) {
        List<String> roleCodes = account.getRoleCodes();
        if (!roleCodes.contains(type)) {
            roleCodes.add(type);
            account.setRoleCodes(roleCodes);
        }
        if (ObjectUtil.isNotEmpty(teamId)) {
            List<Long> teamIds = account.getTeamIds();
            if (!teamIds.contains(teamId)) {
                teamIds.add(teamId);
                account.setTeamIds(teamIds);
            }
        }
        SysUserExpand newExpand = generateUserExpand(type, orgCode, teamId);
        if (newExpand == null) {
            return;
        }
        List<SysUserExpand> expandListExist = account.getExpandList();
        if (CollectionUtil.isEmpty(expandListExist)) {
            account.setExpandList(new ArrayList<>(Collections.singletonList(newExpand)));
            return;
        }
        account.setExpandList(dealNewExists(expandListExist, newExpand));
    }

    private void dealExistAccount(User account, String type, String orgCode, Long teamId) {
        account.setPassword("非新建账号！");
        List<Long> roleIds = userRoleService.lambdaQuery().eq(SysUserRole::getUserId, account.getId()).list()
                .stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        //角色ID转角色CODE，先这样吧
        List<String> roleCodes = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(roleIds)) {
            roleCodes = roleService.lambdaQuery().in(BaseEntity::getId, roleIds).list()
                    .stream().map(Role::getCode).collect(Collectors.toList());
        }
        if (!roleCodes.contains(type)) {
            roleCodes.add(type);
        }
        account.setRoleCodes(roleCodes);
        if (ObjectUtil.isNotEmpty(teamId)) {
            account.setTeamIds(new ArrayList<>(Collections.singletonList(teamId)));
        }
        List<SysUserExpand> existExpands = expandService.getByUserId(account.getId());
        SysUserExpand newExpand = generateUserExpand(type, orgCode, teamId);
        if (newExpand == null) {
            account.setExpandList(existExpands);
            return;
        }
        if (CollectionUtil.isEmpty(existExpands)) {
            account.setExpandList(new ArrayList<>(Collections.singletonList(newExpand)));
            return;
        }
        account.setExpandList(dealNewExists(existExpands, newExpand));
    }

    private SysUserExpand generateUserExpand(String type, String orgCode, Long teamId) {
        SysUserExpand userExpand = new SysUserExpand();
        if (Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN.equals(type)) {
            userExpand.setRoleCode(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
            userExpand.setOrgCode(orgCode);
            return userExpand;
        }
        if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(type)) {
            userExpand.setRoleCode(Cons.RoleCode.ROLE_CODE_TEAM_ADMIN);
            userExpand.setTeamId(teamId);
            return userExpand;
        }
        return null;
    }

    private List<SysUserExpand> dealNewExists(List<SysUserExpand> existExpands, SysUserExpand newExpand) {
        String orgCode = newExpand.getOrgCode();
        Long teamId = newExpand.getTeamId();
        if (StringUtil.isEmpty(orgCode) && ObjectUtil.isEmpty(teamId)) {
            return existExpands;
        }
        if (ObjectUtil.isNotEmpty(teamId)) {
            if (existExpands.stream().anyMatch(it -> teamId.equals(it.getTeamId()))) {
                return existExpands;
            }
            existExpands.add(newExpand);
            return existExpands;
        }
        if (existExpands.stream().anyMatch(it -> orgCode.equals(it.getOrgCode()))) {
            return existExpands;
        }
        existExpands.add(newExpand);
        return existExpands;
    }

    private void saveOrUpdateTeamAdminAccount(User user) {
        saveOrUpdate(user);
        Long userId = user.getId();
        List<Long> teamIds = user.getTeamIds();
        if (CollectionUtil.isEmpty(teamIds)) {
            return;
        }
        String phone = user.getMobile(), certId = user.getCertId();
        ZyzVolunteer volunteer = volunteerService.lambdaQuery().eq(ZyzVolunteer::getPhone, phone).eq(ZyzVolunteer::getCertificateId, certId).eq(ZyzVolunteer::getWriteOff, false).one();
        if (ObjectUtil.isNotEmpty(volunteer)) {
            Long volunteerId = volunteer.getId();
            teamIds.forEach(it -> {
                volunteerTeamService.teamAdminRefresh(it, userId, volunteerId);
            });
        }
    }

    @Override
    public IPage<User> pageForSubAssociationAdmin(UserParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new UserParams();
        }
        String capacity = Oauth2Util.getManagerCapacity();
        if (StringUtil.isEmpty(capacity) || !ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            throw new BusinessException(ResultCode.FAIL, "您没有权限查看账号权限列表！");
        }
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "无法获取您当前的登录组织！");
        }
        List<SysOrg> subOrgList = orgService.getSubOrg(orgCode, true);
        params.setOrgCodes(subOrgList.stream().map(SysOrg::getCode).collect(Collectors.toList()));
        Page<User> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageOrListForSubAssociationAdmin(page, params);
    }

    @Override
    public List<SaCaAccountPrivilegesDto> getListForSubAssociationAdmin(UserParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new UserParams();
        }
        String capacity = Oauth2Util.getManagerCapacity();
        if (StringUtil.isEmpty(capacity) || !ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            throw new BusinessException(ResultCode.FAIL, "您没有权限导出账号权限列表！");
        }
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "无法获取您当前的登录组织！");
        }
        List<SysOrg> subOrgList = orgService.getSubOrg(orgCode, true);
        params.setOrgCodes(subOrgList.stream().map(SysOrg::getCode).collect(Collectors.toList()));
        List<User> users = baseMapper.pageOrListForSubAssociationAdmin(params);
        if (CollectionUtil.isEmpty(users)) {
            return null;
        }
        List<SaCaAccountPrivilegesDto> result = new ArrayList<>();
        users.forEach(it -> {
            dealSaCaAccountPrivilegesData(it, result);
        });
        return result;
    }

    private void dealSaCaAccountPrivilegesData(User data, List<SaCaAccountPrivilegesDto> result) {
        List<String> roleCodes = Arrays.asList(data.getRoleCodesStr().split(COMMA));
        roleCodes.forEach(it -> {
            if (ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(it)) {
                SaCaAccountPrivilegesDto dto = new SaCaAccountPrivilegesDto();
                BeanUtils.copyProperties(data, dto);
                dto.setRole("分协会管理员");
                dto.setOrg(data.getSaNames());
                result.add(dto);
                return;
            }
            List<String> communityNames = Arrays.asList(data.getCommunityNames().split(COMMA));
            communityNames.forEach(im -> {
                SaCaAccountPrivilegesDto dto = new SaCaAccountPrivilegesDto();
                BeanUtils.copyProperties(data, dto);
                dto.setRole("社区管理员");
                dto.setOrg(im);
                result.add(dto);
            });
        });
    }

    @Override
    public List<String> getUserSACPrivileges(Long id) {
        String capacity = Oauth2Util.getManagerCapacity();
        if (StringUtil.isEmpty(capacity) || !ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            return null;
        }
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
           return null;
        }
        List<SysOrg> subOrgList = orgService.getSubOrg(orgCode, true);
        if (CollectionUtil.isEmpty(subOrgList)) {
            return null;
        }
        List<SysUserExpand> userExpands = expandService.lambdaQuery().eq(SysUserExpand::getUserId, id)
                .in(SysUserExpand::getOrgCode, subOrgList.stream().map(SysOrg::getCode).collect(Collectors.toList()))
                .list();
        if (CollectionUtil.isEmpty(userExpands)) {
            return null;
        }
        return userExpands.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
    }

    @Override
    public void privilegesChange(PrivilegeChangeDto data) {
        String capacity = Oauth2Util.getManagerCapacity();
        if (StringUtil.isEmpty(capacity) || !ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            throw new BusinessException(ResultCode.FAIL, "您没有权限进行账号授权的变更-您不是分协会管理员！");
        }
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "您没有权限进行账号授权的变更-无法获取当前管理员组织！");
        }
        List<SysOrg> subOrgList = orgService.getSubOrg(orgCode, true);
        if (CollectionUtil.isEmpty(subOrgList)) {
            throw new BusinessException(ResultCode.FAIL, "您没有权限进行账号授权的变更-您当前所属组织没有下属组织！");
        }
        List<String> orgCodeList = subOrgList.stream().map(SysOrg::getCode).collect(Collectors.toList());
        Long userId = data.getId();
        List<SysUserExpand> existExpands = expandService.getByUserId(data.getId());
        List<SysUserExpand> afterRemoveExpands = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(existExpands)) {
            existExpands.forEach(it -> {
                String expandOrg = it.getOrgCode();
                if (!StringUtil.isEmpty(expandOrg) && orgCodeList.contains(expandOrg)) {
                    return;
                }
                afterRemoveExpands.add(it);
            });
        }
        List<SysUserExpand> needGrantExpands = new ArrayList<>();
        List<String> needGrantPrivileges = data.getPrivileges();
        if (CollectionUtil.isNotEmpty(needGrantPrivileges)) {
            needGrantPrivileges.forEach(it -> {
                SysUserExpand userExpand = new SysUserExpand();
                userExpand.setRoleCode(orgCode.equals(it) ? ROLE_CODE_SUB_ASSOCIATION_ADMIN : ROLE_CODE_COMMUNITY_ADMIN);
                userExpand.setOrgCode(it);
                needGrantExpands.add(userExpand);
            });
        }
        dealUser(userId, orgCode, needGrantExpands, afterRemoveExpands);
    }

    /**
     * 根据手机号获取用户
     *
     * @param mobile 手机号
     * @return 用户
     */
    @Override
    public User getByMobile(String mobile) {
        return lambdaQuery()
                .eq(User::getMobile, mobile)
                .one();
    }

    private void dealUser(Long userId, String orgCode, List<SysUserExpand> needGrant, List<SysUserExpand> otherExpands) {
        User user = super.getById(userId);
        List<Long> roleIds = userRoleService.lambdaQuery().eq(SysUserRole::getUserId, userId).list()
                .stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        List<String> roleCodes = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(roleIds)) {
            roleCodes = roleService.lambdaQuery().in(BaseEntity::getId, roleIds).list()
                    .stream().map(Role::getCode).collect(Collectors.toList());
        }
        List<SysUserExpand> finalExpands = new ArrayList<>();
        finalExpands.addAll(needGrant);
        finalExpands.addAll(otherExpands);
        List<String> finalRoleCodes = new ArrayList<>();
        if (CollectionUtil.isEmpty(finalExpands)) {
            user.setExpandList(finalExpands);
            if (CollectionUtil.isEmpty(roleCodes)) {
                user.setRoleCodes(finalRoleCodes);
                updateById(user);
                return;
            }
            roleCodes.forEach(it -> {
                if(!Arrays.asList(MANAGER_CAPACITY_ROLE).contains(it)) {
                    finalRoleCodes.add(it);
                }
            });
            user.setRoleCodes(finalRoleCodes);
            updateById(user);
            return;
        }
        boolean isSA = false, isCA = false;
        Set<String> roleCodesSet = new HashSet<>(roleCodes);
        for (SysUserExpand it : finalExpands) {
            String oc = it.getOrgCode();
            if (StringUtil.isEmpty(oc)) {
                continue;
            }
            SysOrg org = orgService.getByCode(oc);
            if (ObjectUtil.isEmpty(org)) {
                continue;
            }
            if (org.getLevel() == 2) {
                roleCodesSet.add(ROLE_CODE_SUB_ASSOCIATION_ADMIN);
                isSA = true;
            }
            if (org.getLevel() == 3) {
                roleCodesSet.add(ROLE_CODE_COMMUNITY_ADMIN);
                isCA = true;
            }
            if (isSA && isCA) {
                break;
            }
        }
        if (!isSA) {
            roleCodesSet.remove(ROLE_CODE_SUB_ASSOCIATION_ADMIN);
        }
        if (!isCA) {
            roleCodesSet.remove(ROLE_CODE_COMMUNITY_ADMIN);
        }
        user.setExpandList(finalExpands);
        user.setRoleCodes(new ArrayList<>(roleCodesSet));
        updateById(user);
    }

    @Override
    public User getWithRoleAndExpandByMobile(String mobile) {
        User u = getByMobile(mobile);
        if(Objects.nonNull(u)) {
            List<Long> roleIds = userRoleService.
                    lambdaQuery()
                    .eq(SysUserRole::getUserId, u.getId())
                    .list()
                    .stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(roleIds)) {
                List<String> roleCodes = roleService
                        .lambdaQuery()
                        .in(BaseEntity::getId, roleIds)
                        .list()
                        .stream()
                        .map(Role::getCode)
                        .collect(Collectors.toList());
                u.setRoleCodes(roleCodes);
            }
            u.setExpandList(expandService.getByUserId(u.getId()));
        }
        return u;
    }

    @Override
    public void userWriteOffRemove(List<Long> ids) {
        baseMapper.userWriteOffRemove(ids);
    }
}
