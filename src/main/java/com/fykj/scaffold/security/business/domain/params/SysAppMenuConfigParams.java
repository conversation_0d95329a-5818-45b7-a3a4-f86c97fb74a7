package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SysAppMenuConfigParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"name", "code"})
    private String keyword;

    @MatchType(value = QueryType.EQ, fieldName = {"categoryCode"})
    private String categoryCode;
}
