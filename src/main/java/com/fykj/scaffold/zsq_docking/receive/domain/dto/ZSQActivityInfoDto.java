package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("接收志愿者活动数据模型")
@NoArgsConstructor
@AllArgsConstructor
public class ZSQActivityInfoDto implements Serializable {

    @JsonIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actId;

    @JsonIgnore
    private String actName;

    /**
     * 是否补录数据
     */
    private Boolean addRecord;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyEndTime;

    @ApiModelProperty(value = "对应政务通的用户名")
    private String userId;

    /**
     * 活动基本信息
     */
    private ActBaseInfoDockingDto baseInfo;

    /**
     * 活动对接信息
     */
    private ActReqResDockingInfoDto dockingInfo;

    /**
     * 活动分时段数据
     */
    private List<ZyzActivityTimePeriod> actTimePeriods = new ArrayList<>();

    /**
     * 上下架状态
     */
    @ApiModelProperty(value = "上下架状态")
    private Boolean onShelf;

}
