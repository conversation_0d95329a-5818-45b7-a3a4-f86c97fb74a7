<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.portal_website.mapper.NewsMapper">

    <select id="pageForNews" resultType="com.fykj.scaffold.portal_website.domain.entity.News">
        SELECT
            cc.id AS id,
            cc.title AS title,
            cc.title_img_url AS titleImgUrl,
            ccc.category_list AS category,
            cc.audit_status AS auditStatus,
            cc.effective_date AS effectiveDate,
            CASE WHEN cc.team_publish = TRUE THEN zt.name ELSE so.name END AS publisher,
            cc.audit_memo AS auditMemo,
            ccc.is_stick AS stick,
            cc.grounding AS grounding,
            cc.org_code AS orgCode,
            cc.audit_org_code AS auditOrgCode,
            cc.team_id AS teamId,
            cc.is_sync AS sync,
            cc.sync_time AS syncTime,
            cc.sync_remark AS syncRemark,
            CASE WHEN cc.team_publish = TRUE THEN FALSE
                ELSE IF(so.level = 2 OR so.level = 3, TRUE, FALSE) END AS dockingZSQ
        FROM cms_content cc
        INNER JOIN cms_category_content ccc on cc.id = ccc.content_id
        <if test="params.stick != null">
            AND ccc.is_stick = #{params.stick}
        </if>
        AND ccc.is_deleted = 0 AND ccc.is_banner = 0
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        LEFT JOIN sys_org so ON cc.org_code = so.code
        LEFT JOIN zyz_team zt ON cc.team_id = zt.id
        WHERE cc.type = 'NEWS' AND cc.is_deleted = 0
        <if test="params.title != null and params.title != ''">
            AND cc.title LIKE CONCAT('%', CONCAT(#{params.title}, '%'))
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND cc.org_code = #{params.orgCode}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND cc.audit_status = #{params.auditStatus}
        </if>
        <if test="params.grounding != null">
            AND cc.grounding = #{params.grounding}
        </if>
        <if test="params.publisher != null and params.publisher != ''">
            AND (so.name LIKE CONCAT('%', CONCAT(#{params.publisher}, '%')) OR zt.name LIKE CONCAT('%', CONCAT(#{params.publisher}, '%')))
        </if>
        <if test="params.needAudit != null and params.needAudit == true ">
            AND cc.audit_org_code = #{params.currentOrgCode} AND cc.audit_status != 'nas_pass' AND cc.audit_status != 'nas_refuse'
        </if>
        <if test="params.sync != null and params.sync != ''">
            AND cc.is_sync = #{params.sync}
        </if>
        ORDER BY cc.create_date DESC, cc.effective_date DESC
    </select>

    <select id="getNewsWaitAuditNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM cms_content
        WHERE is_deleted = 0 AND audit_org_code = #{myOrgCode} AND audit_status != #{passStatus} AND audit_status != #{refuseStatus}
    </select>

    <select id="pageForNewsApi" resultType="com.fykj.scaffold.portal_website.domain.entity.News">
        SELECT
        cc.id AS id,
        cc.title AS title,
        cc.title_img_url AS titleImgUrl,
        cc.brief_introduction AS briefIntroduction,
        CASE WHEN cc.team_publish = TRUE THEN zt.name ELSE so.name END AS publisher,
        cc.effective_date AS effectiveDate,
        cc.custom_links,
        cc.use_custom_links,
        ccc.category_list AS category
        FROM cms_content cc
        INNER JOIN cms_category_content ccc on cc.id = ccc.content_id
        <if test="params.stick != null">
            AND ccc.is_stick = #{params.stick}
        </if>
        AND ccc.is_deleted = 0
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        <if test="params.categoryIds != null and params.categoryIds.size() > 0">
            AND
            <foreach item="item" collection="params.categoryIds" index="index" separator="OR" open="(" close=")">
                ccc.category_list Like CONCAT('%', CONCAT(#{item}, '%'))
            </foreach>
        </if>
        LEFT JOIN sys_org so ON cc.org_code = so.code
        LEFT JOIN zyz_team zt ON cc.team_id = zt.id
        WHERE cc.type = 'NEWS' AND cc.grounding = 1 AND cc.audit_status = 'nas_pass' AND cc.is_deleted = 0
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( cc.effective_date &lt; #{params.startTime} OR cc.effective_date &gt; #{params.endTime} )
        </if>
        <if test="params.title != null and params.title != ''">
            AND cc.title LIKE CONCAT('%', CONCAT(#{params.title}, '%'))
        </if>
        <if test="params.key != null and params.key != ''">
            AND ( cc.title LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR cc.brief_introduction LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            <if test="params.orgCode != 'top_dept'">
                AND so.code_prefix LIKE CONCAT(#{params.orgCode},'%')
            </if>
            <if test="params.orgCode == 'top_dept'">
                AND so.code_prefix not LIKE CONCAT('top_dept,loufeng','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,xietang','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,weiting','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,shengpu','%')
                AND so.code_prefix not LIKE CONCAT('top_dept,jinjihu','%')
            </if>
            and so.code_prefix LIKE CONCAT(#{params.orgCode},'%')
        </if>
        <if test="params.teamId != null and params.teamId != ''">
            AND cc.team_id = #{params.teamId}
        </if>
        <if test="params.stick != null and params.stick == true">
            ORDER BY ccc.is_stick DESC, cc.effective_date DESC
        </if>
        <if test="params.orders == null or params.stick == null">
            order by cc.effective_date DESC
        </if>
    </select>
    <select id="pageForMyNewsApi" resultType="com.fykj.scaffold.portal_website.domain.entity.News">
        SELECT
        cc.id AS id,
        cc.title AS title,
        cc.title_img_url AS titleImgUrl,
        cc.brief_introduction AS briefIntroduction,
        cc.effective_date AS effectiveDate
        FROM cms_content cc inner join
        cms_user_op_log l on cc.id=l.cms_cate_cont_id and
        l.source='cms_op_user_log_source_cms' and l.user_id=#{params.userId}
        and l.type='cms_user_op_type_collect' and l.is_deleted = 0
        LEFT JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0
        WHERE cc.type = 'NEWS' AND cc.grounding = 1 AND cc.audit_status = 'nas_pass' AND cc.is_deleted = 0
        <if test="params.title != null and params.title != ''">
            AND cc.title LIKE CONCAT('%', CONCAT(#{params.title}, '%'))
        </if>
        ORDER BY l.create_date DESC
    </select>

    <select id="getNeedSync" resultType="java.lang.Long">
        SELECT c.id
        FROM cms_content c
        INNER JOIN (
            SELECT content_id, MAX(create_date) AS time
            FROM cms_audit_record
            WHERE pass = 1
            GROUP BY content_id
        ) AS nt ON c.id = nt.content_id
        WHERE c.is_deleted = 0
          AND c.audit_status = 'nas_pass'
          AND nt.time &lt;= #{now}
          AND (c.is_sync = 'sync_wait' OR (c.is_sync = 'sync_failure' AND nt.time &gt;= #{threeDaysBeforeNow}))
    </select>

    <select id="getNewsByTeam" resultType="com.fykj.scaffold.portal_website.domain.entity.News">
        SELECT
        cc.id AS id,
        cc.title AS title,
        cc.title_img_url AS titleImgUrl,
        cc.brief_introduction AS briefIntroduction,
        cc.effective_date AS effectiveDate
        FROM cms_content cc
        LEFT JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0
        WHERE cc.is_deleted = 0 AND cc.type = 'NEWS' AND cc.audit_status = 'nas_pass' AND cc.grounding = 1
        AND cc.team_publish = 1
        AND cc.team_id = #{teamId}
          AND (cc.project_id = #{projectId} OR cc.project_id IS NULL)
        ORDER BY ccc.is_stick DESC, cc.effective_date DESC
    </select>

    <select id="getOrgPublishNewsNum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            SUM(IFNULL(nt.num, 0)) AS publishNewsNum
        FROM sys_org o
        LEFT JOIN (
            SELECT
                o.code_prefix,
                COUNT(cc.id) AS num
            FROM cms_content cc
            LEFT JOIN sys_org o ON cc.org_code = o.code
            WHERE cc.is_deleted = 0 AND cc.type = 'NEWS' AND cc.audit_status = 'nas_pass'
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                AND cc.org_code LIKE CONCAT(#{params.parentOrgCode}, '%')
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                AND cc.org_code != 'top_dept'
            </if>
            <if test="params.start != null">
                AND cc.create_date &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND cc.create_date &lt;= #{params.end}
            </if>
            GROUP BY o.code_prefix
        ) nt ON nt.code_prefix LIKE CONCAT(o.code_prefix, '%')
        <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
            WHERE o.code IN
            <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY o.code
        ORDER BY o.sequence
    </select>

    <select id="getOrgSelfPublishNewsNum" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS num
        FROM cms_content
        WHERE is_deleted = 0 AND type = 'NEWS' AND audit_status = 'nas_pass' AND org_code = #{orgCode}
        <if test="start != null">
            AND create_date &gt;= #{start}
        </if>
        <if test="end != null">
            AND create_date &lt;= #{end}
        </if>
    </select>

    <select id="getTeamPublishNewsNum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            t.id AS teamId,
            t.name AS teamName,
            IFNULL(nt.num, 0) AS publishNewsNum
        FROM zyz_team t
        LEFT JOIN (
            SELECT
                cc.team_id,
                COUNT(cc.id) AS num
            FROM cms_content cc
            LEFT JOIN sys_org o ON cc.org_code = o.code
            WHERE cc.is_deleted = 0 AND cc.type = 'NEWS' AND cc.audit_status = 'nas_pass' AND cc.team_publish = 1
            <if test="params.start != null">
                AND cc.create_date &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND cc.create_date &lt;= #{params.end}
            </if>
            GROUP BY cc.team_id
        ) nt ON t.id = nt.team_id
        WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
            <if test="params.teamIdList != null and params.teamIdList.size > 0">
                AND t.id IN
                <foreach item="item" collection="params.teamIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>

    <select id="getOtherOrgPublishNewsNum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM cms_content
        WHERE is_deleted = 0 AND type = 'NEWS' AND audit_status = 'nas_pass'
        AND org_code NOT LIKE 'jinjihu%'
        AND org_code NOT LIKE 'loufeng%'
        AND org_code NOT LIKE 'xietang%'
        AND org_code NOT LIKE 'shengpu%'
        AND org_code NOT LIKE 'weiting%'
        AND org_code != 'top_dept'
        <if test="start != null">
            AND create_date &gt;= #{start}
        </if>
        <if test="end != null">
            AND create_date &lt;= #{end}
        </if>
    </select>
</mapper>
