package com.fykj.scaffold.mall.controller;

import com.fykj.scaffold.mall.domain.entity.MallEmsInfo;
import com.fykj.scaffold.mall.domain.params.MallEmsInfoParams;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsApplyParams;
import com.fykj.scaffold.mall.service.IMallEmsInfoService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 积分商城- 年度区级优秀志愿者地址收集表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@RestController
@RequestMapping("/admin/mall/ems/info")
@Api(tags = "积分商城-年度区级优秀志愿者地址收集表接口")
public class MallEmsInfoController extends BaseController<IMallEmsInfoService, MallEmsInfo, MallEmsInfoParams> {

    @AuditLog("邮寄地址导出数据到Excel")
    @PostMapping("/exportInfo")
    @ApiOperation("邮寄地址导出数据到Excel")
    public void exportInfo(@RequestBody MallScoreGoodsApplyParams params) {
        List<MallEmsInfo> list = baseService.list(params);
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "goods-ems-info-export.xlsx", MallEmsInfo.class);
    }
}
