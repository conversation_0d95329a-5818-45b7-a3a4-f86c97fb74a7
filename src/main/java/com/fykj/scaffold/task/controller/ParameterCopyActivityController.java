//package com.fykj.scaffold.task.controller;
//
//import cn.hutool.core.thread.ThreadUtil;
//import com.fykj.scaffold.task.domain.dto.TaskMessingDto;
//import com.fykj.scaffold.task.domain.dto.TaskVolunteerImportDto;
//import com.fykj.scaffold.task.domain.entity.CopyActivity;
//import com.fykj.scaffold.task.domain.entity.CopyActivityApply;
//import com.fykj.scaffold.task.domain.entity.CopyVolunteer;
//import com.fykj.scaffold.task.domain.entity.ParameterCopyActivity;
//import com.fykj.scaffold.task.domain.params.AutoActivityApplyParam;
//import com.fykj.scaffold.task.domain.params.ParameterCopyActivityParams;
//import com.fykj.scaffold.task.service.CopyActivityService;
//import com.fykj.scaffold.task.service.IParameterCopyActivityService;
//import fykj.microservice.core.base.BaseController;
//import fykj.microservice.core.support.excel.ExcelUtil;
//import fykj.microservice.core.support.util.BeanUtil;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.Page;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//import result.JsonResult;
//import result.Result;
//import result.ResultCode;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 活动批量创建参数表
// * <p>
// * 前端控制器
// *
// * <AUTHOR>
// * @date 2023-09-25
// */
//@RestController
//@RequestMapping("/admin/parameter/copy/activity")
//@Api(tags = " 活动批量创建参数表接口")
//public class ParameterCopyActivityController extends BaseController<IParameterCopyActivityService, ParameterCopyActivity, ParameterCopyActivityParams> {
//    @Autowired
//    private CopyActivityService activityService;
//
//    /**
//     * 导入模板下载
//     *
//     * @param response
//     */
//    @ApiOperation("导入模板下载")
//    @GetMapping("/downloadTemplate")
//    public void downloadTemplate(HttpServletResponse response) {
//        downloadFile(response, "/data/excelTemplate/volunteer-import-template.xlsx");
//    }
//
//    /**
//     * volunteer信息录入导入excel
//     *
//     * @param excel
//     * @return
//     */
//    @ApiOperation(value = "volunteer信息录入导入excel")
//    @PostMapping(value = "/uploadExcel")
//    public Result uploadExcel(@RequestParam(value = "excel") MultipartFile excel) {
//        if (excel == null || excel.isEmpty()) {
//            return new Result(ResultCode.BAD_REQUEST.code(), "请选择要上传的文件");
//        }
//        List<TaskVolunteerImportDto>  excelList = ExcelUtil.readExcel(excel, TaskVolunteerImportDto.class);
//        List<CopyVolunteer> volunteers = new ArrayList<>();
//        excelList.forEach(it -> {
//            CopyVolunteer volunteer = new CopyVolunteer();
//            BeanUtil.copyProperties(it, volunteer);
//            volunteers.add(volunteer);
//        });
//        return new JsonResult<>(volunteers);
//    }
//
//    @ApiOperation("ES分页查询活动")
//    @PostMapping("/getPages")
//    public JsonResult<Page<CopyActivity>> getPages(@RequestBody ParameterCopyActivityParams params) {
//        return new JsonResult<>(baseService.getPage(params));
//    }
//
//    @ApiOperation("ES分页查询活动招募")
//    @PostMapping("/getApplyPages")
//    public JsonResult<Page<CopyActivityApply>> getApplyPages(@RequestBody AutoActivityApplyParam params) {
//        return new JsonResult<>(baseService.getApplyPage(params));
//    }
//
//    @ApiOperation("手动同步")
//    @GetMapping("/syncAct")
//    public Result syncAct(@RequestParam Long actId) {
//        activityService.syncAct(actId);
//        return OK;
//    }
//
//    @ApiOperation("同步市平台")
//    @GetMapping("/syncActByParameterId")
//    public Result syncActByParameterId(@RequestParam Long parameterId) {
//        ParameterCopyActivity activity= baseService.getById(parameterId);
//        activity.setSyncTaskStatus("task_doing");
//        baseService.updateById(activity);
//        ThreadUtil.execAsync(() ->  activityService.syncActByParameterId(activity,parameterId));
//        return OK;
//    }
//    @ApiOperation("查看实际的同步任务数据")
//    @GetMapping("/queryByParameterId")
//    public JsonResult<TaskMessingDto> queryByParameterId(@RequestParam Long parameterId) {
//
//        return  new JsonResult<>(baseService.queryByParameterId(parameterId));
//    }
//}
