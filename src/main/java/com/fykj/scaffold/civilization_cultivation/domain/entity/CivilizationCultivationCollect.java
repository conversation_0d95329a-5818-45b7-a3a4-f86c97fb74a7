package com.fykj.scaffold.civilization_cultivation.domain.entity;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CheckAttachmentDto;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 文明培育征集-管理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "civilization_cultivation_collect", autoResultMap = true)
public class CivilizationCultivationCollect extends BaseEntity {

    private static final long serialVersionUID = -5512115791931170031L;

    /**
     * 文明培育类型（道德模范CCT_DDMF、时代新人CCT_SDXR、先进典型CCT_XJDX、园区好人CCT_YQHR）
     */
    @TableField("type")
    @ApiModelProperty(value = "文明培育类型（道德模范CCT_DDMF、时代新人CCT_SDXR、先进典型CCT_XJDX、园区好人CCT_YQHR）")
    @DictTrans(transTo = "typeText")
    private String type;

    /**
     * 文明培育类型text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "文明培育类型text")
    private String typeText;

    /**
     * 姓名
     */
    @TableField("name")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 联系电话
     */
    @TableField("phone")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 年份
     */
    @TableField("year")
    @ApiModelProperty(value = "年份")
    private Integer year;

    /**
     * 推荐组织（先进典型）
     */
    @TableField("recommend_org")
    @ApiModelProperty(value = "推荐组织（先进典型）")
    private String recommendOrg;

    /**
     * 所属街道code
     */
    @TableField("belong_street_code")
    @ApiModelProperty(value = "所属街道code")
    private String belongStreetCode;

    /**
     * 所属街道名称
     */
    @TableField("belong_street_name")
    @ApiModelProperty(value = "所属街道名称")
    private String belongStreetName;

    /**
     * 所属社区code
     */
    @TableField("belong_community_code")
    @ApiModelProperty(value = "所属社区code")
    private String belongCommunityCode;

    /**
     * 所属社区名称
     */
    @TableField("belong_community_name")
    @ApiModelProperty(value = "所属社区名称")
    private String belongCommunityName;

    /**
     * 地址（道德模范、时代新人、先进典型）
     */
    @TableField("address")
    @ApiModelProperty(value = "地址（道德模范、时代新人、先进典型）")
    private String address;

    /**
     * 经度（道德模范、时代新人、先进典型）
     */
    @TableField("longitude")
    @ApiModelProperty(value = "经度（道德模范、时代新人、先进典型）")
    private BigDecimal longitude;

    /**
     * 纬度（道德模范、时代新人、先进典型）
     */
    @TableField("latitude")
    @ApiModelProperty(value = "纬度（道德模范、时代新人、先进典型）")
    private BigDecimal latitude;

    /**
     * 事迹/成就
     */
    @TableField("achievements")
    @ApiModelProperty(value = "事迹/成就")
    private String achievements;

    /**
     * 推荐理由（道德模范、时代新人、园区好人）
     */
    @TableField("recommend_reason")
    @ApiModelProperty(value = "推荐理由（道德模范、时代新人、园区好人）")
    private String recommendReason;

    /**
     * 图片(多张逗号拼接)
     */
    @TableField("picture")
    @ApiModelProperty(value = "图片(多张逗号拼接)")
    private String picture;

    /**
     * 图片列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "图片列表")
    private List<String> pictureList;

    /**
     * 状态(待处理/已撤回/待核实/已核实/已驳回)
     */
    @TableField("status")
    @DictTrans(transTo = "statusText")
    @ApiModelProperty(value = "状态(待处理/已撤回/待核实/已核实/已驳回)")
    private String status;

    /**
     * 状态(待处理/已撤回/待核实/已核实/已驳回)
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "状态text(待处理/已撤回/待核实/已核实/已驳回)")
    private String statusText;

    /**
     * 是否上下架
     */
    @TableField("on_shelve")
    @ApiModelProperty(value = "是否上下架")
    private Boolean onShelve;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;

    /**
     * 核实附件
     */
    @TableField(value = "check_attachments", typeHandler = CheckAttachmentDto.AttachmentListTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "核实附件")
    private List<CheckAttachmentDto> checkAttachments = new ArrayList<>();

    /**
     * 是否能撤回
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否能撤回")
    private Boolean enableCallback;

    /**
     * 是否能删除
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否能删除")
    private Boolean enableRemove;

    /**
     * 是否能编辑
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否能编辑")
    private Boolean enableEdit;
}
