package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.support.utils.MessagesApiUtil;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDictAll;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictAllService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ActivityExportDto;
import com.fykj.scaffold.zyz.domain.dto.ActivityMemberImportDto;
import com.fykj.scaffold.zyz.domain.dto.act_detail.ActDetailInfoDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityParams;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleActPlanService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.fykj.scaffold.zyz.xxl_job.ActivitySyncTask;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 活动表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-09
 */
@RestController
@RequestMapping("/admin/zyz/activity")
@Api(tags = "活动接口")
@Slf4j
public class ZyzActivityController extends BaseController<IZyzActivityService, ZyzActivity, ZyzActivityParams> {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ActivitySyncTask syncTask;

    @Autowired
    private IZyzActivityTimePeriodService activityTimePeriodService;

    @Value("${act-sync-by-time-period}")
    private Boolean actSyncByTimePeriod = false;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IZyzSyncRegionDictAllService regionDictAllService;

    @Autowired
    private IZyzActivityScheduleActPlanService scheduleActPlanService;

    @Override
    @GetMapping
    public Result get(Long id) {
        ZyzActivity activity = baseService.getById(id);
        baseService.convertResAndReqName(activity);
        return new JsonResult<>(activity);
    }

    @ApiOperation("获取活动同步是否按活动时间段同步")
    @GetMapping("/getActSyncByTimePeriod")
    public JsonResult<Boolean> getActSyncByTimePeriod() {
        return new JsonResult<>(actSyncByTimePeriod);
    }

    /**
     * 员工Excel模板下载
     */
    @ApiOperation("员工Excel模板下载")
    @GetMapping("/memberImportTemplate")
    public void template(HttpServletResponse response) {
        downloadFile(response, "/data/excelTemplate/activity_member_import_template.xlsx");
    }


    @AuditLog("活动-删除")
    @Override
    public Result removeByIds(String ids) {
        boolean result = baseService.removeActivities(ids);
        return result ? OK : new Result(ResultCode.FAIL);
    }


    @AuditLog("通过Excel批量导入志愿者信息")
    @ApiOperation("导入")
    @PostMapping(value = "/memberImport")
    public void dataImport(@RequestParam("excel") MultipartFile excel
            , @RequestParam("activityId") Long activityId
            , @RequestParam("timePeriodId") Long timePeriodId) {
        //上锁，这个功能只能一个人用，否则会数据错乱
        try {
            //获取锁
            boolean res = redisTemplate.opsForValue().setIfAbsent(ZyzCons.ACTIVITY_MEMBER_IMPORT_REDIS_LOCK, "", 30 * 60, TimeUnit.SECONDS);
            if (!res) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "当前正在导入，请勿同时操作");
            }
            List<ActivityMemberImportDto> failureList = baseService.memberImport(activityId, timePeriodId, excel);
            if (CollectionUtil.isNotEmpty(failureList)) {
                ExcelUtil.fillExcel(failureList, "activity_member_fill_template.xlsx", ActivityMemberImportDto.class);
            }
        } finally {
            //释放锁
            redisService.remove(ZyzCons.ACTIVITY_MEMBER_IMPORT_REDIS_LOCK);
        }
    }

    @ApiOperation("获取当前导入进度")
    @GetMapping("/getImportProgress")
    public Result getImportProgress() {
        return new JsonResult<>(redisService.get(ZyzCons.ACTIVITY_MEMBER_IMPORT_REDIS_LOCK));
    }

    @ApiOperation("推送同领域活跃志愿者消息")
    @GetMapping("/sendTagMessage")
    public Result sendTagMessage(long id) {
        //临时使用
        ZyzActivity activity = baseService.getById(id);
        String content = "%s发布了%s活动，如您感兴趣，可打开志愿服务小程序参与报名";
        content = String.format(content, activity.getPublishOrgName(), activity.getName());
        MessagesApiUtil.sendShortMessage(content, dictService.getByCode("temp-tag-message").getValue());
        return OK;
    }


    @ApiOperation("活动审核分页查询")
    @PostMapping({"/pagesForAudit"})
    public JsonResult<IPage<ZyzActivity>> pagesForAudit(@RequestBody(required = false) ZyzActivityParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzActivityParams();
        }
        IPage<ZyzActivity> result = this.baseService.pagesForAudit(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @AuditLog("活动审核")
    @ApiOperation("活动审核")
    @GetMapping("/audit")
    public Result audit(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.audit(idList, status, remark);
        return OK;
    }

    @ApiOperation("活动管理分页查询")
    @PostMapping({"/pagesForActivity"})
    public JsonResult<IPage<ZyzActivity>> pagesForActivity(@RequestBody(required = false) ZyzActivityParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzActivityParams();
        }
        IPage<ZyzActivity> result = this.baseService.pagesForActivity(params);
        DictTransUtil.trans(result.getRecords());
        result.getRecords().forEach(it -> {
            it.setEnableSync(!it.getStartTime().isAfter(LocalDateTime.now()) && ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(it.getAuditStatus()) && !actSyncByTimePeriod);
            baseService.convertResAndReqName(it);
        });
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @AuditLog("新增保存为草稿")
    @ApiOperation("新增保存为草稿")
    @PostMapping("/saveToDraft")
    public Result saveToDraft(@RequestBody ActDetailInfoDto actDetailInfo) {
        baseService.saveOrUpdateToDraft(actDetailInfo);
        return OK;
    }

    @AuditLog("修改保存为草稿")
    @ApiOperation("修改保存为草稿")
    @PostMapping("/updateToDraft")
    public Result updateToDraft(@RequestBody ActDetailInfoDto actDetailInfo) {
        baseService.saveOrUpdateToDraft(actDetailInfo);
        return OK;
    }

    @AuditLog("活动提交")
    @ApiOperation("活动提交")
    @PostMapping("/render")
    public Result render(@RequestBody ActDetailInfoDto actDetailInfo) {
        baseService.render(actDetailInfo);
        return OK;
    }

    @AuditLog("活动提交")
    @ApiOperation("活动提交(by id)")
    @GetMapping("/renderById")
    public Result render(@RequestParam("id") Long id) {
        baseService.renderById(id,false);
        return OK;
    }

    @ApiOperation("活动列表导出")
    @PostMapping({"/exportActivity"})
    public void exportActivity(@RequestBody(required = false) ZyzActivityParams params) {
        List<ActivityExportDto> res = baseService.listForActivity(params);

        ExcelUtil.writeExcel(res, "活动列表", ActivityExportDto.class);
    }


    @AuditLog("活动撤回")
    @RequestMapping(value = "/recall")
    @ApiOperation(value = "撤回")
    public Result recall(long id) {
        if (!baseService.recall(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ApiOperation("活动详情")
    @GetMapping("/getDetailForEdit")
    public Result getDetailForEdit(Long id) {
        return new JsonResult<>(baseService.getDetail(id));
    }


    @AuditLog("活动对接需求或者资源")
    @ApiOperation("活动对接需求或者资源")
    @GetMapping("/dockResOrReq")
    public Result dockResOrReq(@RequestParam Long id,
                               @RequestParam(required = false) Long resId,
                               @RequestParam(required = false) Long reqId,
                               @RequestParam String dockingType,
                               @RequestParam(required = false) Boolean dockingResCreate,
                               @RequestParam(required = false) Boolean dockingReqCreate) {
        if (!baseService.dockResOrReq(id, reqId, resId, dockingType, dockingResCreate, dockingReqCreate)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }


    @AuditLog("活动上下架")
    @ApiOperation("上下架")
    @GetMapping("/updateWebShow")
    public Result updateWebShow(Long id) {

        baseService.updateWebShow(id);
        return OK;
    }

    @ApiOperation("批量上下架")
    @GetMapping("/updateWebShowBatch")
    public Result updateWebShowBatch(String ids) {
        baseService.updateWebShowBatch(ids);
        return OK;
    }

    @ApiOperation("活动同步（单条）")
    @GetMapping("/syncOne")
    public Result syncOne(@RequestParam Long id) {
        return new JsonResult<>(baseService.syncOne(id));
    }

    @ApiOperation("活动打包同步-测试")
    @GetMapping("/packageSync")
    public Result packageSync() {
        syncTask.activitySyncJobHandler();
        return new Result(ResultCode.OK);
    }

    @ApiOperation(value = "创建小程序二维码")
    @GetMapping(value = "/createQrCode")
    public Result createQrCode(@RequestParam long activityId, @RequestParam String appId) {
        return new JsonResult<>(baseService.createQrCode(appId, activityId));
    }

    @ApiOperation(value = "创建活动报名链接")
    @GetMapping(value = "/createApplyLink")
    public Result createLink(@RequestParam long activityId, @RequestParam String appId) {
        return new JsonResult<>(baseService.createLink(appId, activityId));
    }

    @ApiOperation("当前团队活动查询")
    @GetMapping({"/listForTeamActivity"})
    public JsonResult<List<ZyzActivity>> listForTeamActivity(@RequestParam Long projectId) {
        List<ZyzActivity> result = this.baseService.listForTeamActivity(projectId);
        return new JsonResult<>(result);
    }

    @ApiOperation("获取项目关联的活动")
    @GetMapping({"/listForProjectActivity"})
    public JsonResult<List<ZyzActivity>> listForProjectActivity(@RequestParam Long projectId) {
        List<ZyzActivity> result = this.baseService.listForProjectActivity(projectId);
        return new JsonResult<>(result);
    }

    @ApiOperation("修改活动签到距离")
    @GetMapping({"/changeLocationDistance"})
    public Result changeLocationDistance(@RequestParam Long actId, @RequestParam Integer distance) {
        baseService.changeLocationDistance(actId, distance);
        return OK;
    }

    @ApiOperation("活动详情")
    @GetMapping("/getDetail")
    public Result getDetail(Long id) {
        ZyzActivity result = baseService.getById(id);
        baseService.convertResAndReqName(result);
        if (result == null) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        ZyzSyncRegionDictAll regionDict = regionDictAllService.lambdaQuery().eq(ZyzSyncRegionDictAll::getRegionCode, result.getActivityPlaceRegionCode()).one();
        if (regionDict != null) {
            result.setActivityPlaceRegionName(regionDict.getRegionName());
        }
        result.setActivityTimes(activityTimePeriodService.getListByActivityId(id));
        result.setScheduleDetailId(scheduleActPlanService.getScheduleIdByActivityId(id));
        return new JsonResult<>(result);
    }

    @ApiOperation("修改置顶状态")
    @GetMapping("/updateTopStatus")
    public Result updateTopStatus(@RequestParam Long id) {
        baseService.updateTopStatus(id);
        return OK;
    }
}
