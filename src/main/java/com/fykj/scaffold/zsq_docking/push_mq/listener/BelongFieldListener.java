package com.fykj.scaffold.zsq_docking.push_mq.listener;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingBelongFieldRecord;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingMqFail;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingBelongFieldRecordService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingMqFailService;
import com.fykj.scaffold.zsq_docking.push_mq.event.BelongFieldDockingEvent;
import com.fykj.scaffold.zsq_docking.push_mq.service.IZSQDataPushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.DOCKING_RECORD_STATUS_FAIL;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.TOPIC_BELONG_FIELD_DOCKING;

@Slf4j
@Service
@RocketMQMessageListener(consumerGroup = "${spring.application.name}" + TOPIC_BELONG_FIELD_DOCKING, topic = TOPIC_BELONG_FIELD_DOCKING)
public class BelongFieldListener implements RocketMQListener<BelongFieldDockingEvent>, AutoPushZSHMonitor {

    @Autowired
    private IZSQDockingBelongFieldRecordService belongFieldRecordService;

    @Autowired
    private IZSQDataPushService pushService;

    @Autowired
    private IZSQDockingMqFailService pushFailService;

    @Override
    public void onMessage(BelongFieldDockingEvent notice) {
        ZSQDockingMqFail fail = new ZSQDockingMqFail();
        Long dockingRecordId = notice.getDockingRecordId();
        fail.setDockingRecordId(dockingRecordId);
        fail.setMessageId(notice.getMsgId());
        fail.setContent(JSONUtil.toJsonStr(notice));
        fail.setMsgTime(notice.getMsgCreateTime());
        try {
            pushService.belongFieldDocking(notice);
        } catch (Exception e) {
            fail.setExceptionTrace(ExceptionUtil.stacktraceToString(e));
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingBelongFieldRecord record = belongFieldRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【" + ExceptionUtil.stacktraceToString(e) + "】！");
                belongFieldRecordService.updateById(record);
            }
        }
    }
}
