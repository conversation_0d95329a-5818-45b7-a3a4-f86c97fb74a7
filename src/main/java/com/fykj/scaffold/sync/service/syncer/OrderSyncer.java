package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.sync.domain.entity.PracticeSheet;
import com.fykj.scaffold.sync.service.IPracticeSheetService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.service.*;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_order.SyncOrder;
import com.fykj.volunteer.sync.util.domain.sync_order.SyncOrderResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.fykj.scaffold.support.conns.Cons.OrderType.*;
import static com.fykj.scaffold.support.conns.Cons.SyncConstant.OS_PB_ID;

/**
 * 实践单同步
 */
@Component
public class OrderSyncer {

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzResourceAppointmentService resourceAppointmentService;

    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IPracticeSheetService practiceSheetService;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog sync(long orderId, long syncBillId, String type) {
        SyncOrder syncOrder = new SyncOrder();
        syncOrder.setPbId(OS_PB_ID);
        syncOrder.setUniCode(String.valueOf(orderId));
        PracticeSheet sheet = practiceSheetService.lambdaQuery().eq(PracticeSheet::getUniqueCode, syncOrder.getUniCode()).one();
        if (ObjectUtil.isEmpty(sheet)) {
            sheet = new PracticeSheet();
        }
        sheet.setType(type);
        sheet.setUniqueCode(String.valueOf(orderId));
        sheet.setPbId(OS_PB_ID);
        if (REQUIREMENT.equals(type)) {
            requirementOrder(orderId, syncOrder, sheet);
        }
        if (RESOURCE.equals(type)) {
            resourceOrder(orderId, syncOrder, sheet);
        }
        if (ACTIVITY.equals(type)) {
            activityOrder(orderId, syncOrder, sheet);
        }
        SyncOrderResult syncOrderResult = ApiWebService.syncOrder(syncOrder);

        sheet.setSyncTime(LocalDateTime.now());
        sheet.setSyncRemark(syncOrderResult.getMsg());
        //成功
        if ("同步成功".equals(syncOrderResult.getMsg())) {
            sheet.setSyncId(syncOrderResult.getOrderId());
            sheet.setSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (syncOrderResult.getMsg().contains("数据重复")) {
            if (StringUtil.isNotEmpty(syncOrderResult.getOrderId())) {
                sheet.setSyncId(syncOrderResult.getOrderId());
            }
            sheet.setSync(Cons.PlatformSyncState.SUCCESS);
            sheet.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            sheet.setSync(Cons.PlatformSyncState.FAILURE);
        }
        practiceSheetService.saveOrUpdate(sheet);

        //记录日志
        return logService.buildSyncLog(Cons.PlatformSyncBiz.ORDER_SYNC, syncOrderResult.getCode(), syncOrderResult.getMsg()
                , syncBillId, orderId
                , (REQUIREMENT.equals(type) ? "需求【" : (RESOURCE.equals(type) ? "资源【" : "活动【")) + syncOrder.getContent() + "】实践单"
                , JSONUtil.toJsonStr(syncOrder), JSONUtil.toJsonStr(syncOrderResult));
    }

    private void requirementOrder(Long id, SyncOrder syncOrder, PracticeSheet sheet) {
        ZyzRequirement requirement = requirementService.getById(id);
        if (!Cons.PlatformSyncState.SUCCESS.equals(requirement.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此需求还未同步成功");
        }
        syncOrder.setContent(requirement.getName());
        syncOrder.setFinishDate(requirement.getEndTime().format(DateTimeFormatter.ofPattern(Cons.DATE_FORMAT)));
        syncOrder.setDockType(requirement.getDockingTeam() ? SyncOrder.dockType.ORG_OR_TEAM : SyncOrder.dockType.BASE);
        syncOrder.setDockId(getDockId(requirement.getDockingTeam(), requirement.getTeamId(), requirement.getPublishOrgCode()));
        syncOrder.setDockTime(requirement.getDockingTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
        syncOrder.setFinishTime(requirement.getEndTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
        syncOrder.setFinishRemark(null);
        syncOrder.setReqId(String.valueOf(requirement.getId()));
        syncOrder.setActpId(null);
        syncOrder.setResId(null);
        sheet.setContent(syncOrder.getContent());
        sheet.setLinkId(requirement.getId());
        sheet.setFinishDate(requirement.getEndTime().toLocalDate());
        sheet.setDockType(syncOrder.getDockType());
        sheet.setDockId(syncOrder.getDockId());
        sheet.setDockTime(requirement.getDockingTime());
        sheet.setFinishTime(requirement.getEndTime());
        sheet.setFinishRemark(syncOrder.getFinishRemark());
    }

    private void resourceOrder(Long id, SyncOrder syncOrder, PracticeSheet sheet) {
        ZyzResourceAppointment appointment = resourceAppointmentService.getById(id);
        ZyzResource resource = resourceService.getById(appointment.getResourceId());
        if (!Cons.PlatformSyncState.SUCCESS.equals(resource.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此资源还未同步成功");
        }
        syncOrder.setContent("资源【" + resource.getName() + "】在" + appointment.getAppointmentStartTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + "至" + appointment.getAppointmentEndTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)) + "的预约");
        syncOrder.setFinishDate(appointment.getAppointmentEndTime().format(DateTimeFormatter.ofPattern(Cons.DATE_FORMAT)));
        syncOrder.setDockType(appointment.getTeamPublish() ? SyncOrder.dockType.ORG_OR_TEAM : SyncOrder.dockType.BASE);
        syncOrder.setDockId(getDockId(appointment.getTeamPublish(), appointment.getTeamId(), appointment.getOrgCode()));
        syncOrder.setDockTime(appointment.getApplyTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
        syncOrder.setFinishTime(appointment.getAppointmentEndTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
        syncOrder.setFinishRemark(null);
        syncOrder.setReqId(null);
        syncOrder.setActpId(null);
        syncOrder.setResId(String.valueOf(resource.getId()));
        sheet.setContent(syncOrder.getContent());
        sheet.setLinkId(appointment.getId());
        sheet.setFinishDate(appointment.getAppointmentEndTime().toLocalDate());
        sheet.setDockType(syncOrder.getDockType());
        sheet.setDockId(syncOrder.getDockId());
        sheet.setDockTime(appointment.getApplyTime());
        sheet.setFinishTime(appointment.getAppointmentEndTime());
        sheet.setFinishRemark(syncOrder.getFinishRemark());
    }

    private void activityOrder(Long id, SyncOrder syncOrder, PracticeSheet sheet) {
        ZyzActivityTimePeriod actTimePeriod = actTimePeriodService.getById(id);
        if (ObjectUtil.isEmpty(actTimePeriod)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未找到id为【" + id + "】的分时段活动！");
        }
        ZyzActivity activity = activityService.getById(actTimePeriod.getActivityId());
        if (ObjectUtil.isEmpty(activity)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未找到id为【" + id + "】的活动！");
        }
        sheet.setActLinkResId(activity.getResId());
        sheet.setActLinkReqId(activity.getReqId());
        Long resId = activity.getResId(), reqId = activity.getReqId();
        if (ObjectUtil.isEmpty(resId) || ObjectUtil.isEmpty(reqId)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动未同时对接资源和需求！");
        }
        if (!Cons.PlatformSyncState.SUCCESS.equals(actTimePeriod.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此分时段活动尚未同步成功！");
        }
        if (StringUtil.isEmpty(actTimePeriod.getSyncId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到此活动同步id！");
        }
        ZyzRequirement requirement = requirementService.getById(reqId);
        if (ObjectUtil.isEmpty(requirement)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未找到id为【" + reqId + "】的需求！");
        }
        sheet.setActLinkReqName(requirement.getName());
        String reqSyncId = requirement.getSyncId();
        if (!Cons.PlatformSyncState.SUCCESS.equals(requirement.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动对接的需求尚未同步成功！");
        }
        if (StringUtil.isEmpty(reqSyncId)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到此活动对接的需求的同步id！");
        }
        ZyzResource resource = resourceService.getById(resId);
        if (ObjectUtil.isEmpty(resource)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未找到id为【" + resId + "】的资源！");
        }
        sheet.setActLinkResName(resource.getName());
        String resSyncId = resource.getSyncId();
        if (!Cons.PlatformSyncState.SUCCESS.equals(resource.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动预约的资源尚未同步成功！");
        }
        if (StringUtil.isEmpty(resSyncId)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到此活动预约的资源的同步id！");
        }
        syncOrder.setContent(activity.getName());
        syncOrder.setFinishDate(actTimePeriod.getEndTime().format(DateTimeFormatter.ofPattern(Cons.DATE_FORMAT)));
//        syncOrder.setDockType(activity.getTeamPublish() ? SyncOrder.dockType.ORG_OR_TEAM : SyncOrder.dockType.BASE);
        syncOrder.setDockType(SyncOrder.dockType.BASE);
//        syncOrder.setDockId(getDockId(activity.getTeamPublish(), activity.getTeamId(), activity.getPublishOrgCode()));
        syncOrder.setDockId(OS_PB_ID);
        syncOrder.setDockTime(actTimePeriod.getStartTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
        syncOrder.setFinishTime(actTimePeriod.getEndTime().format(DateTimeFormatter.ofPattern(Cons.DATETIME_FORMAT)));
        syncOrder.setFinishRemark(null);
        syncOrder.setReqId(reqSyncId);
        syncOrder.setActpId(actTimePeriod.getSyncId());
        syncOrder.setResId(resSyncId);
        sheet.setContent(syncOrder.getContent());
        sheet.setLinkId(actTimePeriod.getId());
        sheet.setFinishDate(actTimePeriod.getEndTime().toLocalDate());
        sheet.setDockType(syncOrder.getDockType());
        sheet.setDockId(syncOrder.getDockId());
        sheet.setDockTime(actTimePeriod.getStartTime());
        sheet.setFinishTime(actTimePeriod.getEndTime());
        sheet.setFinishRemark(syncOrder.getFinishRemark());
    }

    private String getDockId(Boolean team, Long teamId, String orgCode) {
        if (team) {
            return teamService.getById(teamId).getSyncId();
        } else {
            return orgService.getByCode(orgCode).getSyncId();
        }
    }
}
