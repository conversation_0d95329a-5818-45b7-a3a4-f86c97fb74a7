package com.fykj.scaffold.advanced_unit.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 文明单位-指标标准-测评项
 * 查询参数
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明单位-荣誉-筛选参数")
public class AuUnitHonorParams extends BaseParams {

    @ApiModelProperty(value = "单位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    @NotNull(message = "单位ID不能为空")
    private Long orgId;

    @ApiModelProperty(value = "荣誉名称")
    @MatchType(QueryType.LIKE)
    private String name;

}
