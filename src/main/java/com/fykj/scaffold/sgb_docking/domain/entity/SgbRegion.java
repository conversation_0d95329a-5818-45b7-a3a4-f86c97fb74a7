package com.fykj.scaffold.sgb_docking.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 苏州志愿者平台区划信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("苏州志愿者平台区划信息")
@TableName("sgb_region")
public class SgbRegion extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 区划编号
     */
    @TableField("region_code")
    @ApiModelProperty("区划编号")
    private String regionCode;

    /**
     * 区划名称
     */
    @TableField("region_name")
    @ApiModelProperty("区划名称")
    private String regionName;

    /**
     * 父级区划编号
     */
    @TableField("parent_code")
    @ApiModelProperty("父级区划编号")
    private String parentCode;

    /**
     * 子节点列表
     */
    @TableField(exist = false)
    @ApiModelProperty("子节点列表")
    private List<SgbRegion> children;

}