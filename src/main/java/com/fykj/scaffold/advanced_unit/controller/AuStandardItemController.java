package com.fykj.scaffold.advanced_unit.controller;

import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardItem;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardItemParams;
import com.fykj.scaffold.advanced_unit.service.IAuStandardItemService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 文明单位-指标标准-测评项
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@RestController
@Api(tags = "管理端-文明单位-测评分类")
@RequestMapping("/admin/manager/advanced-unit/item")
public class AuStandardItemController
        extends BaseController<IAuStandardItemService, AuStandardItem, AuStandardItemParams> {

    @ApiOperation("不分页查询")
    @PostMapping({"/list"})
    public JsonResult<List<AuStandardItem>> queryList(
            @RequestBody(required = false) AuStandardItemParams params) {
        List<AuStandardItem> result = baseService.list(params);
        return new JsonResult<>(result);
    }

}
