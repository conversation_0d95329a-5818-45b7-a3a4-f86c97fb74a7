package com.fykj.scaffold.portal_website.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.cms.domain.entity.CmsUserOpLog;
import com.fykj.scaffold.cms.domain.params.CmsUserOpLogParams;
import com.fykj.scaffold.cms.service.ICmsUserOpLogService;
import com.fykj.scaffold.portal_website.domain.dto.CheckOpDto;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.domain.params.NewsParams;
import com.fykj.scaffold.portal_website.service.INewsService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-22
 */
@RestController
@RequestMapping("/admin/cms/user/op/log")
public class CmsUserOpLogControllerApi extends BaseController<ICmsUserOpLogService, CmsUserOpLog, CmsUserOpLogParams> {
    @Autowired
    private INewsService newsService;

    @ApiOperation("收藏点赞/取消收藏点赞")
    @GetMapping({"/newsUserOperate"})
    public Result newsUserOperate(@RequestParam Long id, @RequestParam boolean cancel, @RequestParam String type, @RequestParam String source) {
        if (!baseService.newsUserOperate(id, cancel, type, source)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ApiOperation("校验点赞查询状态")
    @GetMapping({"/checkOp"})
    public JsonResult<CheckOpDto> checkOp(@RequestParam Long id, @RequestParam String source) {
        CheckOpDto result = newsService.checkOp(id, source);
        return new JsonResult<>(result);
    }

    @ApiOperation("分页查询我收藏的新闻公告")
    @PostMapping({"/pageForNews"})
    public JsonResult<IPage<News>> pageForNews(@RequestBody NewsParams params) {
        IPage<News> result = newsService.pageForMyNewsApi(params);
        return new JsonResult<>(result);
    }
}
