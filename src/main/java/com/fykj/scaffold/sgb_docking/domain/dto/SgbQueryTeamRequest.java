package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 苏州社工部根据组织名称查询组织ID请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbQueryTeamRequest {

    /**
     * 组织名称
     */
    private String teamName;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        return JSON.toJSONString(this);
    }
}
