package com.fykj.scaffold.support.utils;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

/**
 * 2025/2/20
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@UtilityClass
public class ApiThirdAuthUtil {

    /**
     * 生成接口调用所需的认证参数
     *
     * @param key    客户端的key，对应服务端配置
     * @param secret 客户端的secret，对应服务端配置
     * @return 包含 key、timestamp、sign 的Map参数集合
     */
    public static Map<String, Object> generateAuthParams(String key, String secret) {
        // 获取当前时间戳（秒级），确保为10位数字
        long timestamp = System.currentTimeMillis() / 1000;
        // 根据规则生成签名：MD5(key + secret + timestamp)
        String sign = DigestUtil.md5Hex(key + secret + timestamp);

        // 将参数封装到Map中
        Map<String, Object> params = new HashMap<>();
        params.put("key", key);
        params.put("timestamp", String.valueOf(timestamp));
        params.put("sign", sign);
        return params;
    }

}
