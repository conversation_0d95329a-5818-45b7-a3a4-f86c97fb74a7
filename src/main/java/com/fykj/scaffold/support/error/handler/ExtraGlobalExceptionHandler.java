package com.fykj.scaffold.support.error.handler;

import com.fykj.scaffold.support.error.exception.FieldBindException;
import exception.BusinessException;
import fykj.microservice.core.support.GlobalExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import result.ValidResult;
import utils.StringUtil;

/**
 * 全局异常处理
 * <br/>
 * 补充 BindException
 * <br/>
 * 2025/2/24
 * @see GlobalExceptionHandler GlobalExceptionHandler
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ExtraGlobalExceptionHandler {

    @ExceptionHandler({BindException.class})
    public Result handleException(BindException exception) {
        BindingResult result = exception.getBindingResult();
        ValidResult validResult = new ValidResult();
        result.getFieldErrors().forEach((it) -> validResult.addResult(it.getField(), it.getDefaultMessage()));
        return new JsonResult<>(ResultCode.NOT_VALID, validResult);
    }

    /**
     * 处理自定义的 FieldBindException，将内部的 ValidResult 返回给客户端
     */
    @ExceptionHandler(FieldBindException.class)
    public Result handleFieldBindException(FieldBindException ex) {
        return new JsonResult<>(ResultCode.NOT_VALID, ex.getValidResult());
    }

    /**
     * 覆盖BusinessException处理，不打印warn级别的exception详细log，解决日志里无用warn日志过多的问题
     */
    @ExceptionHandler({BusinessException.class})
    public Result handleException(BusinessException exception) {
        String message = exception.getMessage();
        int code = exception.getHttpCode().code();
        log.warn("BusinessException: {}", message);
        log.debug("BusinessException: ", exception);
        return StringUtil.isEmpty(message) ? new Result(exception.getHttpCode()) : new Result(code, message);
    }
}
