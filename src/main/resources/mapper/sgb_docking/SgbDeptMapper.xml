<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.sgb_docking.mapper.SgbDeptMapper">

    <!-- 查询结果映射 -->
    <resultMap id="BaseResultMap" type="com.fykj.scaffold.sgb_docking.domain.entity.SgbDept">
        <id column="id" property="id" />
        <result column="dept_id" property="deptId" />
        <result column="parent_id" property="parentId" />
        <result column="dept_name" property="deptName" />
        <result column="is_deleted" property="deleted" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 清空所有数据 -->
    <update id="clearAll">
        TRUNCATE TABLE sgb_dept
    </update>

</mapper> 