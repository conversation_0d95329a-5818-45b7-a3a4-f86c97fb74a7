package com.fykj.scaffold.mall.api;


import com.fykj.scaffold.zyz.domain.entity.SysCountryArea;
import com.fykj.scaffold.zyz.service.ISysCountryAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-02-05
 */
@RestController
@RequestMapping("/api/sys/country/area")
@Api(tags = "pc商城收货地址--无需登录")
public class CountryAreaApi {

    @Autowired
    private ISysCountryAreaService sysCountryAreaService;

    @GetMapping("/getOrgTreeOfAll")
    @ApiOperation("获取级联树")
    public JsonResult<List<SysCountryArea>> getOrgTreeOfAll() {
        return new JsonResult<>(sysCountryAreaService.getAreaTree());
    }

}

