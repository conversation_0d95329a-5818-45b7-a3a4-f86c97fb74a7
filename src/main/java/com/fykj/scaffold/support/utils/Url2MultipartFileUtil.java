package com.fykj.scaffold.support.utils;

import exception.BusinessException;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import result.ResultCode;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class Url2MultipartFileUtil {

    public static MultipartFile transfer(String url, String fileName){
        HttpURLConnection httpUrl = null;
        MultipartFile multipartFile;
        try {
            httpUrl = (HttpURLConnection) new URL(url).openConnection();
            httpUrl.connect();
            multipartFile = getMultipartFile(httpUrl.getInputStream(), fileName);
        } catch (IOException e) {
            throw new BusinessException(ResultCode.FAIL, "url解析失败，抛出url解析异常、原因:", e);
        } finally {
            assert httpUrl != null;
            httpUrl.disconnect();
        }
        return multipartFile;
    }

    private static MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }

    private static FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead;
        byte[] buffer = new byte[8192];
        OutputStream os;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            inputStream.close();
        } catch (IOException e) {
            throw new IllegalArgumentException("文件上传失败");
        }
        return item;
    }
}
