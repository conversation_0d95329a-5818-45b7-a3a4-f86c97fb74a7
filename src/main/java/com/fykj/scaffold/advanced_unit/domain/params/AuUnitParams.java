package com.fykj.scaffold.advanced_unit.domain.params;

import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitAuditStatus;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitHonorLevel;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitNature;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文明单位-申请表
 * 查询参数
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明单位筛选参数")
public class AuUnitParams extends BaseParams {

    @ApiModelProperty(value = "单位名称")
    @MatchType(QueryType.LIKE)
    private String unitName;

    @ApiModelProperty(value = "统一社会信用代码")
    @MatchType(QueryType.LIKE)
    private String creditCode;

    @ApiModelProperty(value = "审核状态 字典CODE: " + AdvancedUnitAuditStatus.DICT_CODE)
    @MatchType(QueryType.EQ)
    private String auditStatus;

    @ApiModelProperty(value = "荣誉等级 字典CODE: " + AdvancedUnitHonorLevel.DICT_CODE)
    @MatchType(QueryType.EQ)
    private String honorLevel;

    @ApiModelProperty(value = "规模")
    @MatchType(QueryType.LIKE)
    private String scale;

    @ApiModelProperty(value = "单位性质（党政机关/事业单位等） 字典CODE: " + AdvancedUnitNature.DICT_CODE)
    @MatchType(QueryType.EQ)
    private String unitNature;

    @ApiModelProperty(value = "联系人")
    @MatchType(QueryType.LIKE)
    private String contactPerson;

    @ApiModelProperty(value = "上下架")
    @MatchType(QueryType.EQ)
    private Boolean showStatus;

    @ApiModelProperty(value = "ids")
    @MatchType(value = QueryType.IN, fieldName = "id")
    private List<Long> ids;

}
