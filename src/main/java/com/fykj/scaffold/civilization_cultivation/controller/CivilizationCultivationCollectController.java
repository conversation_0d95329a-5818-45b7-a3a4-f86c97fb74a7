package com.fykj.scaffold.civilization_cultivation.controller;

import com.fykj.scaffold.civilization_cultivation.domain.dto.CheckInfoUploadDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationCollectParams;
import com.fykj.scaffold.civilization_cultivation.service.ICivilizationCultivationCollectService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;

import java.util.Arrays;

/**
 * 文明培育征集-管理
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@RestController
@RequestMapping("/admin/civilization_cultivation_collect")
@Api(tags = "文明培育征集-管理接口")
public class CivilizationCultivationCollectController extends BaseController<ICivilizationCultivationCollectService, CivilizationCultivationCollect, CivilizationCultivationCollectParams> {

    @ApiOperation("征集新增/修改")
    @PostMapping({"/collectSaveOrUpdate"})
    public Result collectSaveOrUpdate(@RequestBody CivilizationCultivationCollect collectInfo) {
        baseService.collectSaveOrUpdate(collectInfo);
        return OK;
    }

    @ApiOperation("转发街道核实")
    @GetMapping({"/transferToStreetCheck"})
    public Result transferToStreetCheck(@RequestParam Long id) {
        baseService.transferToStreetCheck(id);
        return OK;
    }

    @ApiOperation("街道管理员分页查询核实数据")
    @PostMapping({"/pageForStreetCheck"})
    public Result pageForStreetCheck(@RequestBody CivilizationCultivationCollectParams params) {
        return new JsonResult<>(baseService.pageForStreetCheck(params));
    }

    @ApiOperation("核实退回")
    @GetMapping({"/checkRefuse"})
    public Result checkRefuse(@RequestParam Long id) {
        baseService.checkRefuse(id);
        return OK;
    }

    @ApiOperation("核实信息删除")
    @GetMapping({"/checkInfoRemove"})
    public Result checkInfoRemove(@RequestParam Long collectId, @RequestParam String checkInfoId) {
        baseService.checkInfoRemove(collectId, checkInfoId);
        return OK;
    }

    @ApiOperation("核实信息上传")
    @PostMapping(value = "/checkInfoUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result checkInfoUpload(@RequestPart("files") MultipartFile[] files,
                                  @RequestParam("id") Long id) {
        CheckInfoUploadDto uploadDto = new CheckInfoUploadDto();
        uploadDto.setFiles(Arrays.asList(files));
        uploadDto.setId(id);
        baseService.checkInfoUpload(uploadDto);
        return OK;
    }

    @ApiOperation("获取核实信息")
    @GetMapping({"/getCheckInfo"})
    public Result getCheckInfo(@RequestParam Long id) {
        return new JsonResult<>(baseService.getCheckInfo(id));
    }

}
