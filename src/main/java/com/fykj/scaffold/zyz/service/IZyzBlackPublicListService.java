package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.BannedVolunteerSubmitDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzBlackPublicList;
import com.fykj.scaffold.zyz.domain.params.ZyzBlackPublicListParams;
import fykj.microservice.core.base.IBaseService;

/**
 * 群众黑名单
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IZyzBlackPublicListService extends IBaseService<ZyzBlackPublicList> {

    /**
     * 分页获取群众黑名单
     *
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<ZyzBlackPublicList> pageForList(ZyzBlackPublicListParams params);

    /**
     * 解禁黑名单
     *
     * @param id 黑名单表id
     * @return 是否成功
     */
    boolean recall(long id);

    /**
     * 拉入黑名单
     *
     * @param dto 黑名单id和解禁时间
     * @return 是否成功
     */
    boolean black(BannedVolunteerSubmitDto dto);

    /**
     * 新增/修改黑名单
     *
     * @param blackList 黑名单实体类
     * @return 是否成功
     */
    boolean saveBlackList(ZyzBlackPublicList blackList);

    /**
     * 根据手机号码查找黑名单
     * @param mobile 手机号码
     * @return 黑名单记录
     */

    ZyzBlackPublicList getBlackByMobile(String mobile);
}

