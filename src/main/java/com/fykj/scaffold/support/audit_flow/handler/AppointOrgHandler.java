package com.fykj.scaffold.support.audit_flow.handler;

import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.support.audit_flow.AuditFlowHandleInstance;
import com.fykj.scaffold.support.audit_flow.AuditFlowInput;
import com.fykj.scaffold.support.audit_flow.AuditFlowOutput;
import exception.BusinessException;
import org.springframework.stereotype.Component;
import result.ResultCode;

/**
 * 指定某审核级审核处理引擎
 */
@Component
public class AppointOrgHandler extends AuditFlowHandleInstance {

    @Override
    public AuditFlowOutput handle(AuditFlowInput input) {
        // 提交实体的时候返回审核流转情况
        if (ObjectUtil.isEmpty(input.getPass())) {
            return submitHandle(input);
        }
        // 审核操作的时候返回审核流转情况
        return auditHandle(input);
    }

    private AuditFlowOutput submitHandle(AuditFlowInput input) {
        if (ObjectUtil.isEmpty(input.getTeamPublish())) {
            throw new BusinessException(ResultCode.FAIL, "是否是团队发布未知！");
        }
        Integer appointLevel = input.getAppointLevel();
        if (ObjectUtil.isEmpty(appointLevel)) {
            throw new BusinessException(ResultCode.FAIL, "审核指定级未知！");
        }
        // 处理团队提交的数据
        if (input.getTeamPublish()) {
            return teamPublishDeal(input, appointLevel, false);
        }
        // 处理组织提交的数据
        return orgPublishDeal(input, appointLevel, false);
    }

    private AuditFlowOutput auditHandle(AuditFlowInput input) {
        AuditFlowOutput output = new AuditFlowOutput();
        output.setStatus(input.getPass() ? 1 : 2);
        return output;
    }
}
