package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.entity.SysWeakPw;
import fykj.microservice.core.base.IBaseService;

/**
 * 用户弱口令列表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2025-06-25
 */
public interface ISysWeakPwService extends IBaseService<SysWeakPw> {

    /**
     * 判断密码是否为弱口令
     * @param pw
     * @return
     */
    Boolean isWeakPassword(String pw);
}

