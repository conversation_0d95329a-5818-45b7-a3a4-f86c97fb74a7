package com.fykj.scaffold.sync.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.ImageUtil;
import com.fykj.scaffold.support.utils.RegexUtils;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDep;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDepTypeDict;
import com.fykj.scaffold.sync.mapper.ZyzSyncDepMapper;
import com.fykj.scaffold.sync.service.IZyzSyncDepService;
import com.fykj.scaffold.sync.service.IZyzSyncDepTypeDictService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;


/**
 * 市平台同步职能部门
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzSyncDepServiceImpl extends BaseServiceImpl<ZyzSyncDepMapper, ZyzSyncDep> implements IZyzSyncDepService {

    @Autowired
    private IZyzSyncDepTypeDictService typeDictService;

    @Override
    public ZyzSyncDep getByDepId(String depId) {
        return lambdaQuery().eq(ZyzSyncDep::getDepId, depId).one();
    }

    @Override
    public IPage<ZyzSyncDep> page(BaseParams params) {
        return super.page(params).convert(it -> {
            DictTransUtil.trans(it);
            ZyzSyncDepTypeDict typeDict = typeDictService.getByDepTypeId(it.getDepTypeId());
            it.setDepTypeName(typeDict != null ? typeDict.getDepTypeName() : "");
            return it;
        });
    }

    @Override
    public void saveDep(ZyzSyncDep dep) {
        basicCheck(dep);
        dep.setIsSync(Cons.PlatformSyncState.WAIT_SYNC);
        save(dep);
    }

    private void basicCheck(ZyzSyncDep entity) {
        //验证图片，有些文件后缀名是图片，但是其实并不是真的图片
        ImageUtil.checkPic(entity.getPicLogoUrl());
        //经纬度必填
        if (entity.getTxMapLan() == null || entity.getTxMapLat() == null) {
            throw new BusinessException(ResultCode.FAIL, "请点击地图定位，获取经纬度");
        }
        //验证联系电话
        if (!RegexUtils.isMobileExact(entity.getContactNo()) && !RegexUtils.isTel(entity.getContactNo())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "联系电话格式错误,须为手机号或区号+8位座机号例如051288888888或0512-88888888");
        }
    }
}
