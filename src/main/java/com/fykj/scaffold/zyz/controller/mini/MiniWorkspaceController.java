package com.fykj.scaffold.zyz.controller.mini;

import com.fykj.scaffold.cms.domain.entity.CmsCategory;
import com.fykj.scaffold.cms.service.ICmsCategoryContentService;
import com.fykj.scaffold.cms.service.ICmsCategoryService;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.service.*;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/admin/mini/workspace")
@Api(tags = "小程序工作台接口")
public class MiniWorkspaceController {

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private INewsService newsService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzTypeTeamRelevanceService teamRelevanceService;

    @Autowired
    private ICmsCategoryContentService categoryContentService;

    @Autowired
    private ICmsCategoryService categoryService;

    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private IZyzResourceAppointmentService appointmentService;

    @Autowired
    private IZyzSyncBelongFieldDictService belongFieldDictService;

    @ApiOperation("获取活动详情")
    @GetMapping("/getActivityDetail")
    public Result getActivityDetail(Long activityId) {
        ZyzActivity activity = activityService.getById(activityId);
        activity.setAuditOrgCodeName(orgService.getOrgName(activity.getAuditOrgCode()));
        DictTransUtil.trans(activity);
        activityService.convertResAndReqName(activity);
        return new JsonResult<>(activity);
    }

    @ApiOperation("获取新闻详情")
    @GetMapping("/getNewsDetail")
    public Result getNewsDetail(Long newsId) {
        News news = newsService.getById(newsId);
        DictTransUtil.trans(news);
        if (news.getTeamPublish()) {
            news.setPublisher(teamService.getById(news.getTeamId()).getName());
        } else {
            news.setPublisher(orgService.getOrgName(news.getOrgCode()));
        }
        Optional<CmsCategory> opCC = categoryContentService
                .getByContentId(news.getId(), false)
                .stream()
                .map(it -> categoryService.getByCode(it.getCategoryCode()))
                .findFirst();
        news.setCategory(opCC.isPresent() ? opCC.get().getName() : "");
        return new JsonResult<>(news);
    }

    @ApiOperation("获取团队详情")
    @GetMapping("/getTeamDetail")
    public Result getTeamDetail(Long teamId, @RequestParam(required = false) Boolean infoChangeAudit) {
        ZyzTeam team = teamService.getTeamById(teamId, infoChangeAudit);
        DictTransUtil.trans(team);
        return new JsonResult<>(team);
    }

    @ApiOperation("获取需求详情")
    @GetMapping("/getRequirementDetail")
    public Result getRequirementDetail(Long requirementId) {
        ZyzRequirement requirement = requirementService.getById(requirementId);
        requirement.setAuditOrgName(orgService.getOrgName(requirement.getAuditOrgCode()));
        DictTransUtil.trans(requirement);
        return new JsonResult<>(requirement);
    }

    @ApiOperation("获取资源详情")
    @GetMapping("/getResourceDetail")
    public Result getResourceDetail(Long resourceId) {
        ZyzResource resource = resourceService.getById(resourceId);
        resource.setAuditOrgName(orgService.getOrgName(resource.getAuditOrgCode()));
        DictTransUtil.trans(resource);
        return new JsonResult<>(resource);
    }

    @ApiOperation("获取资源详情")
    @GetMapping("/getResourceAppointmentDetail")
    public Result getResourceAppointmentDetail(Long appointmentId) {
        ZyzResourceAppointment appointment = appointmentService.getById(appointmentId);
        DictTransUtil.trans(appointment);
        return new JsonResult<>(appointment);
    }

    @ApiOperation("获取活动报名审核的统计数量")
    @GetMapping("/getActivityApplyAuditCount")
    public Result getActivityApplyAuditCount(String key) {

        return new JsonResult<>(activityService.getActivityApplyAuditCount(key));
    }
}
