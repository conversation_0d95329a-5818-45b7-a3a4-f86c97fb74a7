package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;

/**
 * 苏州社工部加入活动招募同步请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbSyncActivityMemberRequest {

    /**
     * 唯一标识
     */
    private String uniCode;

    /**
     * 活动招募编号 - 发布招募接口中返回获取
     */
    private String actId;

    /**
     * 身份证编号
     */
    private String idCode;

    /**
     * 加入时间 - 格式：2016-09-30
     */
    private String joinTime;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncActivityMemberInfo menbers = new SyncActivityMemberInfo();
        menbers.setMenbers(Collections.singletonList(this));
        return JSON.toJSONString(menbers);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncActivityMemberInfo {
        private java.util.List<SgbSyncActivityMemberRequest> menbers;
    }
}
