package com.fykj.scaffold.civilization_cultivation.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationImportDto;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationShowExportDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationShowParams;
import com.fykj.scaffold.civilization_cultivation.service.ICivilizationCultivationShowService;
import com.fykj.scaffold.mall.domain.dto.ExchangeDRExportDto;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.Result;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文明培育展示-管理
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@RestController
@RequestMapping("/admin/civilization_cultivation_show")
@Api(tags = "文明培育展示-管理接口")
public class CivilizationCultivationShowController extends BaseController<ICivilizationCultivationShowService, CivilizationCultivationShow, CivilizationCultivationShowParams> {

    @ApiOperation("新增/修改")
    @PostMapping({"/saveOrUpdate"})
    public Result saveOrUpdate(@RequestBody CivilizationCultivationShow showInfo) {
        baseService.showInfoSaveOrUpdate(showInfo);
        return OK;
    }

    @ApiOperation("上/下架")
    @GetMapping({"/onShelveChange"})
    public Result onShelveChange(@RequestParam Long id) {
        baseService.onShelveChange(id);
        return OK;
    }

    @ApiOperation("文明培育Excel模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/civilization_cultivation_template.xlsx");
    }

    @ApiOperation("导入")
    @PostMapping(value = "/dataImport")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<CivilizationCultivationImportDto> failureList = baseService.dataImport(excel);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "civilization_cultivation_fill_template.xlsx", CivilizationCultivationImportDto.class);
        }
    }

    @ApiOperation("导出")
    @PostMapping({"/export"})
    public void export(@RequestBody(required = false) CivilizationCultivationShowParams params) {
        List<CivilizationCultivationShowExportDto> res = baseService.export(params);
        ExcelUtil.writeExcel(res, "文明培育展示数据列表", CivilizationCultivationShowExportDto.class);
    }
}
