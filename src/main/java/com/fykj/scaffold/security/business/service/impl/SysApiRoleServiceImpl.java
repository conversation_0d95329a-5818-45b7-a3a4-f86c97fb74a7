package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.domain.dto.ActionOrRoleApiPathDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiAction;
import com.fykj.scaffold.security.business.domain.entity.SysApiRole;
import com.fykj.scaffold.security.business.mapper.SysApiRoleMapper;
import com.fykj.scaffold.security.business.service.ISysApiRoleService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 接口-角色映射表
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysApiRoleServiceImpl extends BaseServiceImpl<SysApiRoleMapper, SysApiRole> implements ISysApiRoleService {

    @Override
    public List<String> roleApi(Long roleId) {
        return lambdaQuery().eq(SysApiRole::getRoleId, roleId).list().stream().map(s->String.valueOf(s.getApiId())).collect(Collectors.toList());
    }

    @Override
    public boolean savePath(ActionOrRoleApiPathDto dto) {
        Long roleId = Long.valueOf(dto.getBindId());
        baseMapper.deleteByRoleId(roleId);
        if (CollectionUtils.isEmpty(dto.getApiIdList())) {
            return true;
        }
        List<SysApiRole> list = dto.getApiIdList().stream().map(it -> {
            SysApiRole roleApi = new SysApiRole();
            roleApi.setRoleId(roleId);
            roleApi.setApiId(Long.valueOf(it));
            return roleApi;
        }).collect(Collectors.toList());
        return saveBatch(list);
    }
}