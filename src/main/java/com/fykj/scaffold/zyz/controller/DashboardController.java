package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.dto.dashboard_sum.*;
import com.fykj.scaffold.zyz.service.IDashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 后台管理工作台接口
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-06-20
 */
@RestController
@RequestMapping("/admin/dashboard")
@Slf4j
@Api(tags = "管理平台工作台接口")
public class DashboardController {

    @Autowired
    private IDashboardService dashboardService;

    @ApiOperation("标签统计")
    @GetMapping("/tag_sum")
    public JsonResult<TagSumDto> tagSum() {
        return new JsonResult<>(dashboardService.tagSum());
    }

    @ApiOperation("近30天资源、需求、活动柱状统计")
    @GetMapping("/last_30_days_bar_sum")
    public JsonResult<List<Last30DaysBarSumDto>> last30DaysBarSum() {
        return new JsonResult<>(dashboardService.last30DaysBarSum());
    }

    @ApiOperation("本月累计资源、需求、活动数量统计")
    @GetMapping("/this_month_res_req_act_sum")
    public JsonResult<ResReqActSumDto> thisMonthResReqActSum() {
        return new JsonResult<>(dashboardService.thisMonthResReqActSum());
    }

    @ApiOperation("分协会累计数据统计（按分协会志愿者数倒序rank）")
    @GetMapping("/sub_association_data_sum")
    public JsonResult<List<SubAssociationDataSumDto>> subAssociationDataSum() {
        return new JsonResult<>(dashboardService.subAssociationDataSum());
    }

    @ApiOperation("一级所属领域数据量占比统计（top10）")
    @GetMapping("/top_belong_field_data_sum")
    public JsonResult<List<TopBelongFieldDataSumDto>> topBelongFieldDataSum() {
        return new JsonResult<>(dashboardService.topBelongFieldDataSum());
    }

    @ApiOperation("核心指标数据（不包含协会）")
    @GetMapping("/core_indicator_data_sum")
    public JsonResult<CoreIndicatorDataSumDto> coreIndicatorDataSum() {
        return new JsonResult<>(dashboardService.coreIndicatorDataSum());
    }
}
