package com.fykj.scaffold.support.error.exception;

import lombok.Getter;
import result.ValidResult;

/**
 * 字段校验异常(自定义, 适配前端code=303时的校验
 * 2025/4/24
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class FieldBindException extends RuntimeException {

    /**
     * 校验结果，包含一个或多个字段错误
     */
    @Getter
    private final ValidResult validResult;

    /**
     * 使用单字段与消息创建异常
     * @param field   字段名
     * @param message 错误消息
     */
    public FieldBindException(String field, String message) {
        super("FieldBindException: " + field + " -> " + message);
        ValidResult vr = new ValidResult();
        vr.addResult(field, message);
        this.validResult = vr;
    }

    /**
     * 使用已有的 ValidResult 创建异常，可包含多条字段错误
     * @param validResult 校验结果对象
     */
    public FieldBindException(ValidResult validResult) {
        super("FieldBindException with multiple field errors");
        this.validResult = validResult;
    }

}