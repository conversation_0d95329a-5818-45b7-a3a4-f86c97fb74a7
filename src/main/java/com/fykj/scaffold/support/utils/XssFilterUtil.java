package com.fykj.scaffold.support.utils;

import com.fykj.scaffold.security.business.domain.entity.XssTag;
import com.fykj.scaffold.security.business.service.IXssTagService;
import fykj.microservice.core.support.util.SpringContextUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/29
 */
public class XssFilterUtil {

    /**
     * whitelist 里有四种粒度的白名单属性
     * <p>
     * 1. tag(标签):
     * 比如设置 img 标签，<img src="www.baidu.com"> 只会保留 <img>
     * <p>
     * 2. attribute(属性):
     * 需要设置 tag 和 attribute, 比如设置 img、src; <img src="www.baidu.com"> 会完全保留
     * <p>
     * 3. enforcedAttribute(强制执行属性)：
     * 需要设置 tag、attribute、enforcedValue,
     * 比如设置：img、src、www.baidu.com
     * 任何img标签（如：<img alt="无图">）都会变为<img src="www.baidu.com">
     * <p>
     * 4. protocol(协议): 不同于上述标签，
     * 设置protocol不仅要设置 tag、attribute、protocol，还需要单独设置tag、attribute,
     * 比如设置：img、src 和 img、src、https;
     * <img src="www.baidu.com">会变成<img>，<img src="https:XXX">会完全保留
     */
    private static final Safelist WHITELIST = new Safelist();

    static {
        initWhiteList();
    }

    /**
     * 配置过滤化参数,不对代码进行格式化
     */
    private static final Document.OutputSettings OUTPUT_SETTINGS = new Document.OutputSettings().prettyPrint(false);

    public static void init() {
        initWhiteList();
    }

    private static void initWhiteList() {
        IXssTagService service = SpringContextUtil.getBean(IXssTagService.class);
        //tag数组
        String[] tags = service.getTags();
        // 1.tag(标签)
        WHITELIST.addTags(tags);

        // 2. attribute(属性)
        List<XssTag> attrList = service.getAttrList();
        for (XssTag tag : attrList) {
            WHITELIST.addAttributes(tag.getTag(), tag.getAttribute());
        }

        // 3.enforcedAttribute(强制执行属性)
        List<XssTag> enforcedAttrList = service.getEnforcedAttrList();
        for (XssTag tag : enforcedAttrList) {
            WHITELIST.addEnforcedAttribute(tag.getTag(), tag.getAttribute(),
                    tag.getEnforcedValue());
        }

        // 4.protocol(协议)
        List<XssTag> protocols = service.getProtocolList();
        for (XssTag tag : protocols) {
            WHITELIST.addProtocols(tag.getTag(), tag.getAttribute(), tag.getProtocol());
        }
    }

    public static String clean(String content) {
        return Jsoup.clean(content, "", WHITELIST, OUTPUT_SETTINGS);
    }

}
