package com.fykj.scaffold.advanced_unit.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 文明单位-指标标准-测评版本
 * 查询参数
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明单位-标准-筛选参数")
public class AuStandardParams extends BaseParams {

    @ApiModelProperty(value = "测评大类名称")
    @MatchType(QueryType.LIKE)
    private String name;


    @ApiModelProperty(value = "状态")
    @MatchType(QueryType.EQ)
    private Boolean status;

}
