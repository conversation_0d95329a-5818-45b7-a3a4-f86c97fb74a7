package com.fykj.scaffold.zsq_docking.log.domain.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * @Author：yangxu
 * @Date：2025/5/6 14:41
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("对接记录详情")
public class NewsDockingRecordDetailDto implements Serializable {

    private static final long serialVersionUID = -6707307939809188119L;

    /**
     * 新闻 id
     */
    @ApiModelProperty(value = "新闻 id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long newsId;

    /**
     * 记录 id
     */
    @ApiModelProperty(value = "记录 id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long recordId;

    /**
     * 对接类型
     */
    @ApiModelProperty(value = "对接类型")
    private String dockingType;

    /**
     * 对接操作类型
     */
    @ApiModelProperty(value = "对接操作类型")
    private String dockingOperation;

    /**
     * 对接状态
     */
    @ApiModelProperty(value = "对接状态")
    private Integer dockingStatus;

    /**
     * 对接时间
     */
    @ApiModelProperty(value = "对接时间")
    @JsonFormat(pattern = DATETIME_FORMAT)
    private LocalDateTime dockingTime;

    /**
     * 对接详细信息
     */
    @ApiModelProperty(value = "对接详细信息")
    private String dockingMsg;

    /**
     * 对接原始数据Str
     */
    @ApiModelProperty(value = "对接原始数据Str")
    private String dockingDataStr;

    /**
     * 对接原始数据Obj
     */
    @ApiModelProperty(value = "对接原始数据Obj")
    private JSONObject dockingDataObj;

    /**
     * 是否为重试记录
     */
    @ApiModelProperty(value = "是否为重试记录")
    private Boolean retry;

    /**
     * 对接日志
     */
    @ApiModelProperty(value = "对接日志")
    private JSONObject dockingLog;

    /**
     * 对接日志(MQ消费失败日志)
     */
    @ApiModelProperty(value = "对接日志(MQ消费失败日志)")
    private JSONObject dockingMqFailLog;
}
