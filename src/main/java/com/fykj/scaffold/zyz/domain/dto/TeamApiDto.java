package com.fykj.scaffold.zyz.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TeamApiDto {
    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "id")
    private long id;
    /**
     * 团队名称
     */

    @ApiModelProperty(value = "团队名称")
    private String teamName;
    /**
     * 团队类型
     */

    @ApiModelProperty(value = "团队类型")
    private String teamType;
    /**
     * 团队人数
     */

    @ApiModelProperty(value = "团队人数")
    private Integer teamNum;
    /**
     * 团队照片
     */

    @ApiModelProperty(value = "团队照片")
    private String logo;

    @ApiModelProperty(value = "服务内容")
    private String services;

    @ApiModelProperty(value = "团队创建的活动数量")
    private Integer teamJoinActNum;

    /**
     * 协会重点
     */
    @TableField("is_emphasis")
    @DictTrans(transTo = "emphasisText")
    @ApiModelProperty(value = "协会重点")
    private String isEmphasis;

    @TableField(exist = false)
    @ApiModelProperty(value = "协会重点Text")
    private String emphasisText;

    @ApiModelProperty(value = "服务时长")
    private BigDecimal serviceLong;

    @ApiModelProperty(value = "团队联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

}
