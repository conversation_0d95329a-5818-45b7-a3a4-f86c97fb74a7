package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import com.fykj.scaffold.support.xss.annotation.XssFilterAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 用户查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("用户查询参数")
public class UserParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    @ApiModelProperty("关键字查询，模糊匹配用户名、手机号、邮箱")
    @MatchType(value = QueryType.LIKE, fieldName = {"name", "username", "mobile", "email"})
    private String keyword;

    @ApiModelProperty("账号，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "username")
    @XssFilterAttribute
    private String username;

    @ApiModelProperty("姓名，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "name")
    private String name;

    @ApiModelProperty("手机号，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "mobile")
    private String mobile;

    @ApiModelProperty("激活状态，精确查询")
    @MatchType
    private Boolean status;

    @ApiModelProperty("是否管理账户")
    @MatchType(value = QueryType.EQ, fieldName = "is_manager_account")
    private Boolean managerAccount;

    @ApiModelProperty("管理员类型")
    private String adminType;

    @ApiModelProperty("组织 code列表")
    private List<String> orgCodes;

    @ApiModelProperty("社区")
    private String community;

    @ApiModelProperty("用户角色身份")
    private List<String> roleCodes;
}
