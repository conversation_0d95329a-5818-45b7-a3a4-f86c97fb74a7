<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.security.business.mapper.SysActionMapper">
	<select id="findActionByUserIdAndRoleIds" resultType="com.fykj.scaffold.security.business.domain.entity.SysAction">
		SELECT *
			FROM sys_action a
		WHERE a.id IN (
			SELECT
			    ra.action_id
			FROM sys_role_action ra
			INNER JOIN sys_user_role ur ON ur.role_id = ra.role_id
			WHERE user_id = #{userId}
				AND ur.is_deleted = FALSE
				AND ra.is_deleted = FALSE
				and ra.role_id in
				(<foreach item="item" collection="roleIds" index="" separator=",">#{item}</foreach>)
			)
			and a.is_deleted = false
			and a.status = true
	</select>
</mapper>
