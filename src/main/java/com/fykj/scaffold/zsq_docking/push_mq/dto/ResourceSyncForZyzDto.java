package com.fykj.scaffold.zsq_docking.push_mq.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-18 10:43
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
@ApiModel("接收志愿者活动资源模型")
public class ResourceSyncForZyzDto implements Serializable {
    @ApiModelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 资源名称
     */
    @TableField("name")
    @ApiModelProperty(value = "资源名称")
    private String name;

    /**
     * 发布组织code
     */
    @TableField("publish_org_code")
    @ApiModelProperty(value = "发布组织code")
    private String publishOrgCode;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;
    /**
     * 发布组织
     */
    @TableField("publish_org_name")
    @ApiModelProperty(value = "发布组织")
    private String publishOrgName;

    /**
     * 是否是团队维护
     */
    @TableField("team_publish")
    @ApiModelProperty(value = "是否是团队维护")
    private Boolean teamPublish;

    /**
     * 团队id
     */
    @TableField("team_id")
    @ApiModelProperty(value = "团队id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    /**
     * 联系人
     */
    @TableField("contact_person")
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 资源地址
     */
    @TableField("address")
    @ApiModelProperty(value = "资源地址")
    private String address;

    /**
     * 经度
     */
    @TableField("longitude")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * 资源类型
     */
    @TableField("type")
    @ApiModelProperty(value = "资源类型")
    private String type;

    /**
     * 所属领域一级
     */
    @TableField("belong_field_top")
    @ApiModelProperty(value = "所属领域一级")
    private String belongFieldTop;

    /**
     * 所属领域名称一级
     */
    @TableField("belong_field_name_top")
    @ApiModelProperty(value = "所属领域名称一级")
    private String belongFieldNameTop;

    /**
     * 所属领域二级
     */
    @TableField("belong_field_end")
    @ApiModelProperty(value = "所属领域二级")
    private String belongFieldEnd;

    /**
     * 所属领域二级名称
     */
    @TableField("belong_field_name_end")
    @ApiModelProperty(value = "所属领域二级名称")
    private String belongFieldNameEnd;

    /**
     * 预约次数
     */
    @TableField("appointment_num")
    @ApiModelProperty(value = "预约次数")
    private Integer appointmentNum;

    /**
     * 是否自动上架
     */
    @TableField("is_auto_enable")
    @ApiModelProperty(value = "是否自动上架")
    private Boolean autoEnable;

    /**
     * 提供开始时间
     */
    @TableField("start_time")
    @ApiModelProperty(value = "提供开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 提供结束时间
     */
    @TableField("end_time")
    @ApiModelProperty(value = "提供结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 图片
     */
    @TableField("picture")
    @ApiModelProperty(value = "图片")
    private String picture;

    /**
     * 资源详情
     */
    @TableField("detail")
    @ApiModelProperty(value = "资源详情")
    private String detail;

    /**
     * 是否允许同一时间预约
     */
    @TableField("allow_appointment_same_time")
    @ApiModelProperty(value = "是否允许同一时间预约")
    private Boolean allowAppointmentSameTime;

    @ApiModelProperty(value = "政务通用户id")
    private String zwtUserId;

    @ApiModelProperty(value = "审核状态")
    private String status;
}
