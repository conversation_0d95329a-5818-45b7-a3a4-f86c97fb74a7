package com.fykj.scaffold.sync.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 市平台地域数据字典
 * <p>
 * Mapper 接口
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
public interface ZyzSyncRegionDictMapper extends BaseMapper<ZyzSyncRegionDict> {
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
}
