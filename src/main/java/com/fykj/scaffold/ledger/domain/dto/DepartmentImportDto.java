package com.fykj.scaffold.ledger.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class DepartmentImportDto {

    @ExcelProperty(value = "局办名称")
    private String departmentName;

    @ExcelProperty(value = "联系人")
    private String contactPerson;

    @ExcelProperty(value = "联系方式")
    private String contact;

    @ExcelProperty(value = "关联账号手机号")
    private String mobile;

    @ExcelProperty(value = "导入失败原因")
    private String reason;
}
