package com.fykj.scaffold.ledger.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.ledger.domain.dto.ItemImportDto;
import com.fykj.scaffold.ledger.domain.dto.LedgerItemDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndexItem;
import com.fykj.scaffold.ledger.domain.params.LedgerIndexItemParams;
import com.fykj.scaffold.ledger.service.ILedgerIndexItemService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 台账项-接口控制器
 *
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/admin/ledger/indexItem")
@Api(tags = "台账项-管理接口")
public class LedgerIndexItemController extends BaseController<ILedgerIndexItemService, LedgerIndexItem, LedgerIndexItemParams> {


    @ApiOperation("根据id获取")
    @GetMapping("getByIndexId")
    public Result getByIndexId(@RequestParam Long indexId) {
        return new JsonResult<>(this.baseService.getByIndexId(indexId));
    }

    @ApiOperation("根据id获取")
    @GetMapping
    @Override
    public Result get(@RequestParam Long id) {
        return new JsonResult<>(this.baseService.get(id));
    }

    @ApiOperation("导出")
    @GetMapping({"/exportExcel"})
    public void exportForPublic(@RequestParam Long indexId) {
        List<LedgerIndexItem> list = baseService.getByIndexId(indexId);
        ExcelUtil.fillExcel(list, "ledger_index_item.xlsx", LedgerIndexItem.class);
    }

    @ApiOperation(value = "获取部门指标对象指标项")
    @GetMapping(value = "/getDepartmentReport")
    public JsonResult<List<LedgerItemDto>> getDepartmentReport(@RequestParam Long indexId,@RequestParam Long departmentId) {
        return new JsonResult<>(baseService.getDepartmentReport(indexId, departmentId));
    }

    @ApiOperation("指标项导入Excel模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/ledger_index_item_template.xlsx");
    }

    @ApiOperation("导入")
    @PostMapping(value = "/import")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<ItemImportDto> failureList = baseService.dataImport(excel);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "ledger_index_item_error.xlsx", ItemImportDto.class);
        }
    }

    @SysLogMethod("新增")
    @ApiOperation("保存方法")
    @PostMapping({"/save"})
    public Result save(@RequestBody @Validated({BaseEntity.Add.class}) LedgerIndexItem entity) {
        boolean result = this.baseService.saveItem(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @SysLogMethod("编辑")
    @ApiOperation("更新方法")
    @PostMapping({"/update"})
    public Result update(@RequestBody @Validated({BaseEntity.Modify.class}) LedgerIndexItem entity) {
        return this.baseService.updateItemById(entity) ? OK : new Result(ResultCode.DATA_EXPIRED);
    }

}
