package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Collections;

/**
 * 推送志愿者信息请求
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SgbSyncVolunteerRequest {
    
    /**
     * 证件类型，默认为身份证类型（必填）
     * 0-内地身份证
     * 3-港澳台居民身份证
     * 4-护照
     */
    private String type;
    
    /**
     * 身份证编号（必填）
     */
    private String idCode;
    
    /**
     * 真实姓名（必填）
     */
    private String name;
    
    /**
     * 手机号码（必填）
     */
    private String phone;

    /**
     * 身份属性 字典类型vol_identity,最多5个值，多个则以英文逗号分割如：1,2
     */
    private String identity;

    /**
     * 专业特长 字典类型vol_skill,最多5个值，多个则以英文逗号分割如：1,2
     */
    private String skill;

    /**
     * 服务领域 接口2.2,最多5个值，多个则以英文逗号分割如：1,2
     */
    private String serviceType;

    /**
     * 服务区域 接口2.1,最多5个值，多个则以英文逗号分割如：1,2
     */
    private String region;

    /**
     * 民族 字典类型nation
     */
    private String nation;

    /**
     * 政治面貌 字典类型politicsstatus
     */
    private String politics;

    /**
     * 文化程度 字典类型education
     */
    private String education;

    /**
     * 接口调用类型 1新增，2修改
     */
    private String saveType;
    
    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncUserInfo info = new SyncUserInfo();
        info.setInfo(Collections.singletonList(this));
        return JSON.toJSONString(info);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncUserInfo {
        private java.util.List<SgbSyncVolunteerRequest> info;
    }
}