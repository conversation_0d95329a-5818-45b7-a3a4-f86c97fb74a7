package com.fykj.scaffold.sync.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDepTypeDict;
import com.fykj.scaffold.sync.mapper.ZyzSyncDepTypeDictMapper;
import com.fykj.scaffold.sync.service.IZyzSyncDepTypeDictService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.dep_type.DepTypeResult;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 市平台职能部门类型数据字典
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzSyncDepTypeDictServiceImpl extends BaseServiceImpl<ZyzSyncDepTypeDictMapper, ZyzSyncDepTypeDict> implements IZyzSyncDepTypeDictService {

    @Override
    public void syncDepType() {
        List<DepTypeResult> regionResultList = ApiWebService.getDepType();
        List<ZyzSyncDepTypeDict> dicts = regionResultList.stream().map(it -> {
            ZyzSyncDepTypeDict dict = new ZyzSyncDepTypeDict();
            dict.setDepTypeId(it.getDepId());
            dict.setDepTypeName(it.getDepName());
            return dict;
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(dicts)) {
            baseMapper.clearAll();
            saveBatch(dicts);
        }
    }

    @Override
    public ZyzSyncDepTypeDict getByDepTypeId(String typeId) {
        return lambdaQuery().eq(ZyzSyncDepTypeDict::getDepTypeId, typeId).one();
    }
}