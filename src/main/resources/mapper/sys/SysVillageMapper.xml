<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.SysVillageMapper">

    <delete id="deleteAll">
      delete  from sys_village
    </delete>
    <select id="page" resultType="com.fykj.scaffold.zyz.domain.entity.SysVillage">
        select v.*,so.name as orgCommunityName,sp.name as parentOrgName from sys_village v left join sys_org so on so.code=v.org_community_code
        left join sys_org sp on sp.code=v.parent_org_code
        where v.is_deleted=0
        <if test="params.key != null and params.key != ''">
            AND ( v.village_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR so.name LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
                      OR sp.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.villageName != null and params.villageName != ''">
            AND  v.village_name LIKE CONCAT(#{params.villageName}, '%')
        </if>
    </select>

</mapper>