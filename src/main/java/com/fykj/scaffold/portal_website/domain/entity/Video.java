package com.fykj.scaffold.portal_website.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

;

/**
 * 视频资源
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("video_upload")
public class Video extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField("name")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 文件名称
     */
    @TableField("file_name")
    @ApiModelProperty(value = "原文件名")
    private String fileName;

    /**
     * 文件扩展后缀（不带点）
     */
    @TableField("file_ext")
    @ApiModelProperty(value = "文件后缀名（不带点）")
    private String fileExt;

    /**
     * 文件访问路径
     */
    @TableField("path")
    @ApiModelProperty(value = "文件访问路径")
    private String path;

    /**
     * 文件大小（Byte）
     */
    @ApiModelProperty(value = "文件大小（Byte）")
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件大小（Byte）text
     */
    @ApiModelProperty(value = "文件大小（Byte）text")
    @TableField(exist = false)
    private String fileSizeText;

    /**
     * 存储服务器编码
     */
    @ApiModelProperty(value = "存储服务器编码（本地:LocalServer/七牛云:QiniuCloud/阿里云:AliCloud/腾讯云:QCloud）")
    @TableField("server_code")
    @DictTrans(transTo = "serverCodeText")
    private String serverCode;

    /**
     * 存储服务器编码text
     */
    @ApiModelProperty(value = "存储服务器编码text")
    @TableField(exist = false)
    private String serverCodeText;

    /**
     * 上传是否成功
     */
    @ApiModelProperty(value = "上传是否成功")
    @TableField("success")
    private Boolean success;

    /**
     * 上传失败原因
     */
    @TableField("fail_reason")
    @ApiModelProperty(value = "上传失败原因")
    private String failReason;
}
