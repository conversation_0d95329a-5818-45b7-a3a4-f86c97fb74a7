package com.fykj.scaffold.ledger.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

@Data
@ApiModel("指标项模型")
public class LedgerItemDto implements Serializable {

    private static final long serialVersionUID = 178766288085219479L;
    @ApiModelProperty(value = "指标项id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long indexItemId;

    @ApiModelProperty(value = "台账项名称")
    private String indexItemName;

    @ApiModelProperty(value = "相关要求")
    private String relatedRequirements;

    /**
     * 附件地址
     */
    @ApiModelProperty(value = "附件地址")
    private String attachmentUrl;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 上报状态
     */
    @ApiModelProperty(value = "上报状态")
    private Boolean status;

    /**
     * 上报时间
     */
    @ApiModelProperty(value = "上报时间")
    @JsonFormat(pattern = DATETIME_FORMAT)
    @DateTimeFormat(pattern = DATETIME_FORMAT)
    private LocalDateTime reportDate;
}
