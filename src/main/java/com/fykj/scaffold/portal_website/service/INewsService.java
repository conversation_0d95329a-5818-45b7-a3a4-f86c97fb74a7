package com.fykj.scaffold.portal_website.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.portal_website.domain.dto.CheckOpDto;
import com.fykj.scaffold.portal_website.domain.dto.NewsAuditDto;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.domain.params.NewsParams;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import fykj.microservice.core.base.IBaseService;
import org.apache.ibatis.annotations.Param;
import result.Result;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 新闻资讯服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface INewsService extends IBaseService<News> {
    /**
     * 保存新闻
     *
     * @param news
     * @return
     */
    boolean saveNews(News news);

    /**
     * 更新新闻
     *
     * @param news
     * @return
     */
    boolean updateNews(News news);

    /**
     * 置顶操作
     *
     * @param id
     * @return
     */
    void setStick(Long id);

    /**
     * 审核操作
     *
     * @param audit
     * @return
     */
    void audit(NewsAuditDto audit);

    /**
     * 批量审核操作
     *
     * @param ids
     * @param memo
     * @return
     */
    Result auditBatch(String ids, String memo);

    /**
     * 网页分页展示新闻公告
     *
     * @param params
     * @return
     */
    IPage<News> pageForApi(NewsParams params);

    /**
     * 网页分页展示新闻公告
     *
     * @param params
     * @return
     */
    IPage<News> pageForMyNewsApi(NewsParams params);

    /**
     * 修改新闻浏览量
     *
     * @param id
     * @return
     */
    void updateCmsContentById(Serializable id);

    /**
     * 网页获取新闻公告详情
     *
     * @param id
     * @return
     */
    News getForApi(Serializable id);

    CheckOpDto checkOp(Long id, String source);

    /**
     * 新闻资讯上下架
     *
     * @param id
     */
    void ground(String id);

    /**
     * 获取每天定时任务需要同步的数据
     *
     * @return
     */
    List<Long> getNeedSync();

    /**
     * 批量同步
     *
     * @param newsIds
     */
    void syncBatch(List<Long> newsIds);

    /**
     * 根据项目id获取新闻
     * @param projectId
     * @return
     */
    List<News> getNewsByProjectId(Long projectId);

    /**
     * 根据团队获取新闻
     * @return
     */
    List<News> getNewsByTeam(Long projectId);

    /**
     * 获取待审核的新闻数量
     * @return
     */
    Integer getNewsWaitAuditNum();

    /**
     * 获取组织新闻发布数量
     * @param params
     * @return
     */
    List<OrgDataFormDto> getOrgPublishNewsNum(OrgDataReportFormParams params);

    /**
     * 获取其他组织的新闻发布数
     * @param orgCode
     * @param start
     * @param end
     * @return
     */
    Integer getOrgSelfPublishNewsNum(String orgCode, LocalDateTime start, LocalDateTime end);

    /**
     * 获取团队新闻发布数量
     * @param params
     * @return
     */
    List<OrgDataFormDto> getTeamPublishNewsNum(OrgDataReportFormParams params);

    /**
     * 获取其他组织的新闻发布数
     * @param start
     * @param end
     * @return
     */
    Integer getOtherOrgPublishNewsNum(LocalDateTime start, LocalDateTime end);

    /**
     * 获取新闻详情
     * @param id
     * @return
     */
    ZSQNewsInfoDto getNewsDetail(Long id);
}
