package com.fykj.scaffold.config.mybatisplus.Handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * JSON类型处理类，继承于{@link FastjsonTypeHandler}
 *
 * <AUTHOR>
 * @date 2022-11-14
 */

@MappedJdbcTypes(JdbcType.VARBINARY)
@MappedTypes({Object.class})
public class JsonTypeHandler extends FastjsonTypeHandler {

    public JsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected String toJson(Object obj) {
        return JSON.toJSONString(obj);
    }
}
