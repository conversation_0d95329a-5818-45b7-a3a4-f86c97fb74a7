package com.fykj.scaffold.capability.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.capability.domain.dto.*;
import com.fykj.scaffold.capability.domain.entity.Course;
import com.fykj.scaffold.capability.domain.entity.CourseUser;
import com.fykj.scaffold.capability.domain.params.CourseParams;
import com.fykj.scaffold.capability.domain.params.CourseUserParams;
import com.fykj.scaffold.capability.domain.params.CreditsRecordParams;
import org.springframework.scheduling.annotation.Async;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ICourseService extends IService<Course> {
    Course saveCourse(CourseSaveDTO dto);

    @Async
    void generateCheckInQrcode(Long courseId);

    void editCourse(CourseUpdateDTO dto);

    void deleteCourse(Long id);

    CourseDetailDTO detailCourse(Long id);

    CourseDetailDTO detailCourseMine(Long id);

    CourseQrcodeDTO getCourseSignInQrcode(Long id);

    void switchOuterDisplay(Long id);

    void switchTop(Long id);

    IPage<CourseListDTO> pageCourse(CourseParams params);

    List<CourseListDTO> listCourse(CourseParams params);

    void signUp(CourseUserSaveDTO dto);

    void cancelSignUp(Long courseId, Long userId);

    void cancelSignUp(Long courseId);

    void signIn(CourseSignInSaveDTO dto);

    IPage<CourseSignUpDTO> pageSignUp(CourseUserParams params);

    IPage<CourseSignInDTO> pageSignIn(CourseUserParams params);

    List<CourseSignUpDTO> listSignUp(CourseUserParams params);

    List<CourseSignInDTO> listSignIn(CourseUserParams params);

    IPage<CourseListDTO> pageMyCourse(CourseParams params);

    List<CourseListDTO> listMyCourse(CourseParams params);

    void sendCredits(Long courseId);

    UserCreditsDetailDTO getUserCredits(Long userId);

    List<CreditsRecordsDTO> listCreditsRecords(CreditsRecordParams params);

    IPage<CreditsRecordsDTO> pageCreditsRecords(CreditsRecordParams params);

    void creditsChange(@Valid CreditsChangeDTO dto);

    /**
     * 获取需要提醒的课程
     * @param start
     * @param end
     * @return
     */
    List<Course> getNeedRemindCourses(LocalDateTime start, LocalDateTime end);


    /**
     * 获取需要提醒的课程报名用户
     * @param courseIds
     * @return
     */
    List<CourseUser> getNeedRemindUsers(List<Long> courseIds);
}
