package com.fykj.scaffold.mall.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.entity.SysUserAddress;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrderFormDto {
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long goodsId;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String orderNo;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品数量")
    private Integer goodsNum;
    /**
     * 商品品牌
     */
    @ApiModelProperty(value = "商品品牌")
    private String goodsBrand;
    /**
     * 商品Logo
     */
    @ApiModelProperty(value = "商品Logo")
    private String goodsLogo;

    /**
     * 积分
     */
    @ApiModelProperty(value = "积分")
    private BigDecimal requiredScore;

    /**
     * 积分
     */
    @ApiModelProperty(value = "积分")
    private BigDecimal offlineScore;
    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    private SysUserAddress userAddress;
}
