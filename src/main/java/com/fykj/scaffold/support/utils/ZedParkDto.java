package com.fykj.scaffold.support.utils;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ZedParkDto {
    /**
     * 三级网格编码
     */
    private String thre_code;

    /**
     * 三级网格名称
     */
    private String thre_name;
    /**
     * ：四级网格编码
     */
    private String  four_code;
    /**
     * ：四级网格名称
     */
    private String four_name;
    /**
     * ：子网格编码
     */
    private String five_code;
    /**
     * ：子网格名称
     */
    private String five_name;
    /**
     * ：详细地址
     */
    private String  last_name;
    /**
     * ：苏州2000坐标系WKT格式
     */
    private String coordinate2000;
    /**
     * 84坐标系WKT格式
     */
    private String coordinate84;
}
