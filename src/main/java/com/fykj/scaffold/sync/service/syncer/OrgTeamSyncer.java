package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzOrgTeam;
import com.fykj.scaffold.zyz.service.IZyzOrgTeamService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.conns.SyncApiCons;
import com.fykj.volunteer.sync.util.domain.sync_team.SyncTeam;
import com.fykj.volunteer.sync.util.domain.sync_team.SyncTeamResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;

@Component
public class OrgTeamSyncer {

    @Autowired
    private IZyzSyncLogService logService;

    @Autowired
    private IZyzOrgTeamService orgTeamService;

    public ZyzSyncLog sync(long orgTeamId, long syncBillId) {
        ZyzOrgTeam orgTeam = orgTeamService.getById(orgTeamId);
        //处于待同步,且审核同步才需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(orgTeam.getIsSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }
        SyncTeam syncTeam = new SyncTeam();
        syncTeam.setOrgName(orgTeam.getName().trim());
        syncTeam.setIdCode(orgTeam.getAdminCard());
        syncTeam.setDpId(orgTeam.getDepSyncId());
        syncTeam.setRegionCode(orgTeam.getRelatedRegionCode());
        if (StringUtil.isNotEmpty(orgTeam.getIntroduction()) && (orgTeam.getIntroduction().length() > 500)) {
            syncTeam.setDescs(orgTeam.getIntroduction().substring(0, 499));
        } else {
            syncTeam.setDescs(orgTeam.getIntroduction());
        }

        syncTeam.setLinkName(orgTeam.getAdminName());
        syncTeam.setLinkPhone(orgTeam.getAdminContact());
        syncTeam.setUniqueCode(String.valueOf(orgTeamId));
        syncTeam.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(orgTeam.getTeamPhoto())));
        if (StringUtil.isEmpty(orgTeam.getSyncId())) {
            syncTeam.setSaveType(SyncTeam.SaveType.ADD);
        } else {
            syncTeam.setSaveType(SyncTeam.SaveType.MODIFY);
            syncTeam.setOrgId(orgTeam.getSyncId());
        }
        SyncTeamResult result = ApiWebService.syncTeam(syncTeam);
        String resultCode = result.getCode();
        String msg = result.getMsg();

        orgTeam.setSyncTime(LocalDateTime.now());
        orgTeam.setSyncRemark(msg);
        boolean isSuccess = "同步成功".equals(msg) || SyncApiCons.ResultCode.SUCCESS.equals(resultCode);
        boolean isExist = StringUtil.isNotEmpty(msg) && (msg.contains("该组织已上传") || msg.contains("该组织已存在"));
        //成功
        if (isSuccess || isExist) {
            orgTeam.setSyncId(result.getOrgId());
            orgTeam.setIsSync(Cons.PlatformSyncState.SUCCESS);
        }
        //其他一律失败
        else {
            orgTeam.setIsSync(Cons.PlatformSyncState.FAILURE);
        }

        //启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.TEAM_SYNC, resultCode, msg
                , syncBillId, orgTeamId, orgTeam.getName()
                , JSONUtil.toJsonStr(syncTeam), JSONUtil.toJsonStr(result));

        //回写业务
        orgTeamService.updateById(orgTeam);
        return log;
    }
}
