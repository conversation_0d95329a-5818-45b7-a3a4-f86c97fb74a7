package com.fykj.volunteer.sync.util.domain.sync_act_show;


import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMember;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActShow {

    /**
     * 唯一标识
     * 必填
     */
    private String uniCode;
    /**
     * 活动招募id
     * 必填
     */
    private String actId;
    /**
     * 公示内容
     * 必填
     */
    private String content;
    /**
     * 公示图片
     * 必填
     */
    private String picsUrl;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "actshow")
    public static class ActShow{
        private List<SyncActShow> info;
    }
    public String buildXml() {
        SyncActShow.ActShow act = new SyncActShow.ActShow();
        act.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(act);
    }
}
