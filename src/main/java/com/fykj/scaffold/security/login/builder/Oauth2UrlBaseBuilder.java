package com.fykj.scaffold.security.login.builder;

import com.fykj.scaffold.security.login.domain.dto.Oauth2BaseDto;
import lombok.Getter;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;

@Getter
public abstract class Oauth2UrlBaseBuilder {

    private ClientCredentialsResourceDetails clientCredentialsResourceDetails;

    private StringBuilder uri;

    private Oauth2BaseDto oauth2BaseDto;

    public Oauth2UrlBaseBuilder(ClientCredentialsResourceDetails clientCredentialsResourceDetails,Oauth2BaseDto oauth2BaseDto) {
        this.clientCredentialsResourceDetails = clientCredentialsResourceDetails;
        this.uri = new StringBuilder(clientCredentialsResourceDetails.getAccessTokenUri());
        this.oauth2BaseDto = oauth2BaseDto;
    }

    protected static final String OAUTH2_TOKEN_URL_USERNAME="username";

    protected static final String OAUTH2_TOKEN_URL_PASSWORD="password";

    protected static final String OAUTH2_TOKEN_URL_GRANT_TYPE="grant_type";

    protected static final String OAUTH2_TOKEN_URL_SCOPE="scope";

    protected static final String OAUTH2_TOKEN_URL_CLIENT_ID="client_id";

    protected static final String OAUTH2_TOKEN_URL_CLIENT_SECRET="client_secret";

    protected static final String OAUTH2_TOKEN_URL_REFRESH_TOKEN="refresh_token";

    protected static final String OAUTH2_GRANT_TYPE_PASSWORD="password";

    public abstract Oauth2UrlBaseBuilder buildOauth2Url();

}
