package com.fykj.scaffold.sync.service;


import com.fykj.scaffold.sync.domain.dto.SyncSpotExportDto;
import com.fykj.scaffold.sync.domain.dto.SyncSpotImportDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncSpot;
import com.fykj.scaffold.sync.domain.params.ZyzSyncSpotParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 市平台同步实践点
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-06
 */
public interface IZyzSyncSpotService extends IBaseService<ZyzSyncSpot> {
    void saveSpot(ZyzSyncSpot spot);

    /**
     * 修改实践点
     * @param spot 实践点信息
     */
    void updateSpot(ZyzSyncSpot spot);

    /**
     * 实践点上下架
     * @param id
     */
    void changeOnShelve(Long id);

    /**
     * 实践点导入
     * @param excel
     * @return
     */
    List<SyncSpotImportDto> dataImport(MultipartFile excel);

    /**
     * 实践点导出
     * @param params
     * @return
     */
    List<SyncSpotExportDto> export(ZyzSyncSpotParams params);
}

