<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallStockMapper">
    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallStock">
        select v.*,so.name as orgName from mall_stock v left join sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.name LIKE CONCAT('%',#{params.keyWord},'%')or
            v.description LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and v.org_code =#{params.orgCode}
        </if>
        order by v.create_date desc
    </select>
</mapper>
