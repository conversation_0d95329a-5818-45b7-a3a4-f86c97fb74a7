package com.fykj.scaffold.big_screen.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("civilization_enterprise")
public class CivilizationEnterprise extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 单位名称
     */
    @TableField("name")
    @NotBlank(message = "单位名称不能为空")
    @ApiModelProperty(value = "单位名称")
    private String name;

    /**
     * 外链
     */
    @TableField("link")
    @ApiModelProperty(value = "外链")
    private String link;

    /**
     * 图片
     */
    @TableField(value = "pic", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "图片url")
    private String pic;

    /**
     * 介绍
     */
    @TableField(value = "introduction", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "介绍")
    private String introduction;

    /**
     * 生效时间
     */
    @TableField("effective_time")
    @JsonFormat(pattern = DATETIME_FORMAT)  //格式化前台日期参数注解
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("status")
    private Boolean status;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;
}
