package com.fykj.scaffold.capability.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("User Credits Detail DTO")
public class UserCreditsDetailDTO {

    @ApiModelProperty("用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty("用户姓名")
    private String userName;

//    @ApiModelProperty("用户头像")
//    private String userAvatar;

    @ApiModelProperty("学分")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long credits;

}
