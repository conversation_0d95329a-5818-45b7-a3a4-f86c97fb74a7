package com.fykj.scaffold.security.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.security.business.domain.dto.OrgDto;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.zyz.domain.dto.dashboard_sum.SubAssociationDataSumDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门表
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2022-10-12
 */
public interface SysOrgMapper extends BaseMapper<SysOrg> {

    List<OrgDto> findByParent(Long parentId);

    /**
     * 获取分协会累计数据
     * @param subAssociationOrgCodes
     * @return
     */
    List<SubAssociationDataSumDto> getSubAssociationSumData(@Param("orgCodes") List<String> subAssociationOrgCodes);

    /**
     * 通过 id获取团队名称
     * @param codes
     * @return
     */
    List<SysOrg> getNameByCodes(@Param("codes") List<String> codes);
}
