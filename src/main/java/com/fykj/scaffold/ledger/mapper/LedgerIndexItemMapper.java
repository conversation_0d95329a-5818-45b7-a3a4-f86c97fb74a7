package com.fykj.scaffold.ledger.mapper;

import com.fykj.scaffold.ledger.domain.dto.LedgerItemDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndexItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 台账项-Mapper接口
 *
 * @date 2025-05-26
 */
public interface LedgerIndexItemMapper extends BaseMapper<LedgerIndexItem> {


    /**
     * 获取台账项
     *
     * @param indexId
     * @return
     */
    List<LedgerIndexItem> getByIndexId(@Param("indexId") Long indexId);

    /**
     * 获取局办对应的指标id
     *
     * @param departmentId
     * @return
     */
    List<String> getIndexIdByDepartmentId(@Param("departmentId") String departmentId);


    /**
     * 获取用户指标项
     *
     * @param departmentId
     * @param indexId
     * @return
     */
    List<LedgerItemDto> getUserIndexItem(@Param("departmentId") Long departmentId, @Param("indexId") Long indexId);
}
