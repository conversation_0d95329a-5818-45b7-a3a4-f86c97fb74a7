package com.fykj.scaffold.zyz.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectActivityPlan;

import java.util.List;

/**
 * 活动计划安排
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
public interface IZyzProjectActivityPlanService extends IBaseService<ZyzProjectActivityPlan> {


    /**
     * 保存活动计划安排列表
     * @param projectActivityPlanList，projectId
     */
    void saveProjectActivityPlan(List<ZyzProjectActivityPlan> projectActivityPlanList, Long projectId);
}

