package com.fykj.scaffold.ledger.domain.params;

import fykj.microservice.core.base.BaseParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 台账指标-查询参数
 *
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("台账指标-查询参数")
public class LedgerIndexParams extends BaseParams {

    private static final long serialVersionUID = 873820094041180365L;

    @ApiModelProperty("关键字查询，模糊匹配")
    @MatchType(value = QueryType.LIKE)
    private String keyword;
}
