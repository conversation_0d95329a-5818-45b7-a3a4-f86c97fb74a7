package com.fykj.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@Configuration
@Slf4j
public class ThreadPoolConfig {

    @Bean
    public ScheduledExecutorService newSingleThreadScheduledExecutor() {
        return Executors.newSingleThreadScheduledExecutor();
    }

    @Bean
    public ExecutorService newWorkStealingPool() {
        log.info("cpu:{}", Runtime.getRuntime().availableProcessors());
        return Executors.newWorkStealingPool(Runtime.getRuntime().availableProcessors());
    }
}
