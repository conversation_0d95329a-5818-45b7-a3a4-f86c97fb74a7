package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirementTemp;
import fykj.microservice.core.base.IBaseService;

/**
 * 需求临时表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-05-05
 */
public interface IZyzRequirementTempService extends IService<ZyzRequirementTemp>, IBaseService<ZyzRequirementTemp> {

    /**
     * 新增
     * @param entity
     * @return
     */
    boolean saveRequirementTemp(ZyzRequirementTemp entity);
}

