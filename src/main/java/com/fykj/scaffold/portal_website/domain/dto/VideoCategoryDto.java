package com.fykj.scaffold.portal_website.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cms_content")
public class VideoCategoryDto extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;


    /**
     * 图片
     */
    @ApiModelProperty(value = "图片url")
    private String titleImgUrl;

//    /**
//     * 生效时间
//     */
//
//    @JsonFormat(pattern = Cons.DATETIME_FORMAT)  //格式化前台日期参数注解
//    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
//    @ApiModelProperty(value = " 生效时间")
//    private LocalDateTime effectiveDate;
//
//
//
//    /**
//     * 过期日期
//     */
//
//    @JsonFormat(pattern = Cons.DATETIME_FORMAT)  //格式化前台日期参数注解
//    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
//    @ApiModelProperty(value = " 过期日期")
//    private LocalDateTime expirationDate;

//    /**
//     * 是否上架
//     */
//    @ApiModelProperty(value = "是否上架")
//    private Boolean grounding;


    /**
     * 排序
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "排序")
    private Integer sequence;


    /**
     * 视频路径
     */
    @ApiModelProperty(value = "视频路径")
    private String videoShowPath;
}
