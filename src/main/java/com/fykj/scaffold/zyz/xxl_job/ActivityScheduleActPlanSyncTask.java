package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollUtil;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleActPlanService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityScheduleActPlanSyncTask {

    @Autowired
    private IZyzActivityScheduleActPlanService scheduleActPlanService;

    @XxlJob("Activity_Schedule_Act_Plan_Sync_Handler")
    public void scheduleActPlanSyncJobHandler() {
        XxlJobHelper.log("Activity_Schedule_Act_Plan_Sync_Handler-Job, START!");
        // 获取待同步的活动关联数据以及活动关联三天内同步失败的数据
        List<Long> planIds = scheduleActPlanService.getNeedSync();
        if (CollUtil.isNotEmpty(planIds)) {
            try {
                scheduleActPlanService.syncBatch(planIds);
            } catch (Exception e) {
                log.error("活动公示批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Activity_Schedule_Act_Plan_Sync_Handler-Job, END!");
    }
}
