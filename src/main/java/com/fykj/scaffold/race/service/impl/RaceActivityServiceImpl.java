package com.fykj.scaffold.race.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.race.domain.dto.WinPhotoDto;
import com.fykj.scaffold.race.domain.entity.Activity;
import com.fykj.scaffold.race.domain.entity.ActivityPrizeGrade;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.mapper.ActivityMapper;
import com.fykj.scaffold.race.service.IActivityPrizeGradeService;
import com.fykj.scaffold.race.service.IActivityPrizeService;
import com.fykj.scaffold.race.service.IRaceActivityService;
import com.fykj.scaffold.race.service.IRacePrizePhotoJsonService;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import result.ResultCode;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 摄影大赛-活动表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RaceActivityServiceImpl extends BaseServiceImpl<ActivityMapper, Activity> implements IRaceActivityService {

    @Autowired
    private IActivityPrizeGradeService activityPrizeGradeService;
    @Autowired
    private IActivityPrizeService activityPrizeService;

    @Autowired
    private IRacePrizePhotoJsonService racePrizePhotoJsonService;

    @Autowired
    private IRaceActivityService activityService;

    @Override
    public IPage<Activity> page(BaseParams params) {
        BaseParams.Order order = new BaseParams.Order();
        order.setColumn("sequence");
        order.setSort("desc");
        params.setOrders(CollectionUtil.newArrayList(order));
        IPage<Activity> iPage = super.page(params);
        iPage.getRecords().forEach(item -> {
            List<String> prizeName = activityPrizeGradeService.getPrizeName(item.getId());
            if (!ObjectUtils.isEmpty(prizeName)) {
                item.setPrize(String.join("|", prizeName));
            }
        });
        return iPage;
    }

    @Override
    public boolean setEnable(Long id) {
        Activity activity = getById(id);
        if (Boolean.TRUE.equals(activity.getEnable())) {
            throw new BusinessException(ResultCode.DATA_EXPIRED, "禁用失败，需保证开启一个活动");
        }
        if (!lambdaQuery().eq(Activity::getEnable, true).list().isEmpty()) {
            Activity res = lambdaQuery().eq(Activity::getEnable, true).one();
            res.setEnable(false);
            updateById(res);
        }
        activity.setEnable(!activity.getEnable());
        return updateById(activity);
    }

    @Override
    public boolean setShow(Long id) {
        Activity activity = getById(id);
        activity.setIsShow(!activity.getIsShow());
        return updateById(activity);
    }

    @Override
    public boolean setUpload(Long id) {
        Activity activity = getById(id);
        activity.setUpload(!activity.getUpload());
        return updateById(activity);
    }

    @Override
    public Activity getActivityByEnable() {
        return lambdaQuery().eq(Activity::getEnable, true).one();
    }

    @Override
    public List<Activity> getActivityByShow() {
        return lambdaQuery().eq(Activity::getIsShow, true).orderByDesc(Activity::getSequence).list();
    }

    private final static List<Long> OLD_PHOTO_ACTIVITY_IDS = CollectionUtil.newArrayList(Long.valueOf("1"),Long.valueOf("2"),Long.valueOf("3"),Long.valueOf("4"),Long.valueOf("5"),Long.valueOf("6"),Long.valueOf("7"));

    @Override
    public List<WinPhotoDto> getActivityWinPhoto(Long activityId) {
        List<WinPhotoDto> res = new ArrayList<>();
        List<ActivityPrizeGrade> gradeList = activityPrizeGradeService.getPrize(activityId).stream().sorted(Comparator.comparing(ActivityPrizeGrade::getSequence)).collect(Collectors.toList());
        gradeList.forEach(item -> {
            if (OLD_PHOTO_ACTIVITY_IDS.contains(activityId)) {
                List<Photo> photos = racePrizePhotoJsonService.getPrizePhoto(item.getId());
                DesensitiseUtil.desensitise(photos);
                WinPhotoDto winPhotoDto = new WinPhotoDto();
                winPhotoDto.setPhotoList(photos);
                winPhotoDto.setPrizeSequence(item.getSequence());
                winPhotoDto.setPrizeName(item.getName());
                res.add(winPhotoDto);
            } else {
                WinPhotoDto winPhotoDto = new WinPhotoDto();
                List<Photo> photos = activityPrizeService.getPhotosByPrize(item.getId());
                DesensitiseUtil.desensitise(photos);
                winPhotoDto.setPhotoList(photos);
                winPhotoDto.setPrizeSequence(item.getSequence());
                winPhotoDto.setPrizeName(item.getName());
                res.add(winPhotoDto);
            }
        });
        return res;
    }


    @Override
    public boolean save(Activity entity) {
        entity.setEnable(false);
        entity.setIsShow(true);
        return super.save(entity);
    }
}
