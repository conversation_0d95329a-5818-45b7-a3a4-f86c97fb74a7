package com.fykj.scaffold.advanced_unit.service;


import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardUnitApply;
import fykj.microservice.core.base.IBaseService;
/**
 * 文明单位-申请表
 *
 * 服务类
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
public interface IAuStandardUnitApplyService extends IBaseService<AuStandardUnitApply> {

    void createApplyByUnitIdAndYear(Long id, Integer civilizationYear);

}
