package com.fykj.scaffold.capability.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 学分记录管理
 * 表对应：cu_credits_records
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "cu_credits_records", autoResultMap = true)
public class CreditsRecords extends BaseEntity {

    @TableField("course_id")
    @ApiModelProperty(value = "课程ID")
    private Long courseId;

    @TableField("user_id")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @TableField("credits")
    @ApiModelProperty(value = "学分")
    private Integer credits;

    @TableField("generate_at")
    @ApiModelProperty(value = "生成时间")
    private LocalDateTime generateAt;

}
