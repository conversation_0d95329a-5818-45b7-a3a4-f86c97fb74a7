<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzBadgeTransactionMapper">
    <!-- 自定义分页查询勋章交易流水 -->
    <select id="query" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTransaction">
        SELECT
            t.*,
            b.name as badgeName,
            to_volunteer.phone AS toPhone,
            from_volunteer.phone AS fromPhone
        FROM zyz_badge_transaction t
        LEFT JOIN zyz_badge b ON t.badge_id = b.id
        LEFT JOIN zyz_volunteer to_volunteer ON t.to_id = to_volunteer.id
        LEFT JOIN zyz_volunteer from_volunteer ON t.from_id = from_volunteer.id
        <where>
            AND t.is_deleted = 0
            <if test="params.operationType != null and params.operationType != ''">
                AND t.operation_type = #{params.operationType}
            </if>
            <if test="params.fromId != null">
                AND t.from_id = #{params.fromId}
            </if>
            <if test="params.fromName != null and params.fromName != ''">
                AND t.from_name LIKE CONCAT('%', #{params.fromName}, '%')
            </if>
            <if test="params.toName != null and params.toName != ''">
                AND t.to_name LIKE CONCAT('%', #{params.toName}, '%')
            </if>
            <if test="params.startTime != null">
                AND t.create_date &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND t.create_date &lt;= #{params.endTime}
            </if>
            <if test="params.badgeName != null and params.badgeName != ''">
                AND b.name LIKE CONCAT('%', #{params.badgeName}, '%')
            </if>
            <if test="params.badgeId != null">
                AND t.badge_id = #{params.badgeId}
            </if>
            <if test="params.phone != null and params.phone != ''">
                AND to_volunteer.phone LIKE CONCAT('%', #{params.phone}, '%')
            </if>
            <if test="params.relatedVolunteerId != null">
                AND (t.from_id = #{params.relatedVolunteerId} OR t.to_id = #{params.relatedVolunteerId})
            </if>
            <if test="params.relatedMe != null and params.relatedMe == true
                and params.relatedVolunteerId != null">
                AND (
                    t.from_id = #{params.relatedVolunteerId}
                    OR t.to_id = #{params.relatedVolunteerId}
                )
            </if>
        </where>
        ORDER BY t.create_date DESC
    </select>
    
    <!-- 统计交易数量 -->
    <select id="countTransactionByType" resultType="com.fykj.scaffold.zyz.domain.dto.TransactionCountDTO">
        SELECT
            operation_type,
            COUNT(1) as count
        FROM zyz_badge_transaction t
        <where>
            AND t.is_deleted = 0
            <if test="params.operationType != null and params.operationType != ''">
                AND t.operation_type = #{params.operationType}
            </if>
            <if test="params.fromId != null">
                AND t.from_id = #{params.fromId}
            </if>
            <if test="params.fromName != null and params.fromName != ''">
                AND t.from_name LIKE CONCAT('%', #{params.fromName}, '%')
            </if>
            <if test="params.toName != null and params.toName != ''">
                AND t.to_name LIKE CONCAT('%', #{params.toName}, '%')
            </if>
            <if test="params.startTime != null">
                AND t.create_date &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND t.create_date &lt;= #{params.endTime}
            </if>
        </where>
        GROUP BY operation_type
    </select>
    
    <!-- 统计积分兑换档数量 -->
    <select id="countPointsExchangeByRange" resultType="com.fykj.scaffold.zyz.domain.dto.PointsRangeCountDTO">
        SELECT
            CASE
                WHEN points_before_fee BETWEEN 0 AND 100 THEN '0-100'
                WHEN points_before_fee BETWEEN 101 AND 200 THEN '101-200'
                WHEN points_before_fee > 200 THEN '200+'
                ELSE 'other'
            END as `range`,
            COUNT(1) as `count`
        FROM zyz_badge_transaction t
        <where>
            AND t.is_deleted = 0
            <if test="params.operationType != null and params.operationType != ''">
                AND t.operation_type = #{params.operationType}
            </if>
            <if test="params.fromId != null">
                AND t.from_id = #{params.fromId}
            </if>
            <if test="params.fromName != null and params.fromName != ''">
                AND t.from_name LIKE CONCAT('%', #{params.fromName}, '%')
            </if>
            <if test="params.toName != null and params.toName != ''">
                AND t.to_name LIKE CONCAT('%', #{params.toName}, '%')
            </if>
            <if test="params.startTime != null">
                AND t.create_date &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND t.create_date &lt;= #{params.endTime}
            </if>
        </where>
        GROUP BY `range`
    </select>
</mapper>