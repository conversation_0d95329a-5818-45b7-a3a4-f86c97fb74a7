package com.fykj.scaffold.mall.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsParams;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@RestController
@RequestMapping("/admin/mall/score-goods")
@Api(tags = "商品接口")
public class MallScoreGoodsController extends BaseController<IMallScoreGoodsService, MallScoreGoods, MallScoreGoodsParams> {

    @AuditLog("商品分页查询")
    @ApiOperation("商品分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallScoreGoods>> getPages(@RequestBody MallScoreGoodsParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @ResponseBody
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @ApiOperation(value = "修改状态")
    public Result updateStatus(long goodsId, boolean isClearStorge) {
        if (!baseService.updateStatus(goodsId, isClearStorge)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ResponseBody
    @RequestMapping(value = "/goodsStorageIn", method = RequestMethod.POST)
    @ApiOperation(value = "库存数量变动")
    public Result goodsStorageIn(long goodsId, int goodsNum) {
        baseService.editGoodsNum(goodsId, goodsNum);
        return OK;
    }

    @ApiOperation("保存并返回商品对象")
    @PostMapping("/saveMallGoods")
    public Result saveMallGoods(@RequestBody MallScoreGoods goods) {
        baseService.saveMallGoods(goods);
        return OK;
    }

    @ApiOperation("修改并返回商品对象")
    @PostMapping("/updateMallGoods")
    public Result updateMallGoods(@RequestBody MallScoreGoods goods) {
        if (!baseService.updateMallGoods(goods)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("商品分页导出数据到Excel")
    @PostMapping("/exportGoods")
    @ApiOperation("导出数据到Excel")
    public void exportGoods(@RequestBody MallScoreGoodsParams params) {
        List<MallScoreGoodsDto> list = baseService.getGoodsList(params);
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "goods.xlsx", MallScoreGoodsDto.class);
    }

    @ApiOperation(value = "创建PAD兑换商品小程序二维码")
    @GetMapping(value = "/createQrCode")
    public Result createQrCode(@RequestParam long goodsId, @RequestParam String appId, @RequestParam Boolean prod) {
        return new JsonResult<>(baseService.createQrCode(appId, goodsId, prod));
    }

    @ApiOperation("商品小程序分页查询")
    @PostMapping("/getPagesForStar")
    public JsonResult<IPage<MallScoreGoods>> getPagesForStar(@RequestBody MallScoreGoodsParams params) {
        params.setStatus(true);
        params.setStar(true);
        params.setPadExchange(Boolean.FALSE);
        return new JsonResult<>(baseService.pageForStar(params));
    }
}
