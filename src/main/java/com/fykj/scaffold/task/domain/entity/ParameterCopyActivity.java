package com.fykj.scaffold.task.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 活动批量创建参数表
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("parameter_copy_activity")
public class ParameterCopyActivity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 选中的日期开始日期
     */
    @TableField("act_start_day")
    @ApiModelProperty(value = "选中的日期开始日期")
    private LocalDate actStartDay;
    /**
     * 选中的日期开始日期
     */
    @TableField("act_end_day")
    @ApiModelProperty(value = "选中的日期开始日期")
    private LocalDate actEndDay;
    /**
     * 选中的每天的时间段
     */
    @TableField("act_time_section_start_time")
    @ApiModelProperty(value = "选中的每天的时间段开始时间")
    private LocalTime actTimeSectionStartTime;

    /**
     * 选中的每天的时间段
     */
    @TableField("act_time_section_end_time")
    @ApiModelProperty(value = "选中的每天的时间段开始时间")
    private LocalTime actTimeSectionEndTime;
    /**
     * 时间段分组
     */
    @TableField("act_t_group_section")
    @ApiModelProperty(value = "时间段分组")
    private String actTGroupSection;
    /**
     * 需要造的活动数
     */
    @TableField("activity_nums")
    @ApiModelProperty(value = "需要造的活动数")
    private Integer activityNums;
    /**
     * 最大志愿者人数
     */
    @TableField("max_volunteer_nums")
    @ApiModelProperty(value = "最大志愿者人数")
    private Integer maxVolunteerNums;
    /**
     * 最新志愿者人数
     */
    @TableField("min_volunteer_nums")
    @ApiModelProperty(value = "最新志愿者人数")
    private Integer minVolunteerNums;
    /**
     * 活动名称自定义字段
     */
    @TableField("custom_fields")
    @ApiModelProperty(value = "活动名称自定义字段")
    private String customFields;
    /**
     * 任务是否完成
     */
    @TableField("is_complete")
    @ApiModelProperty(value = "任务是否完成")
    private Boolean complete;
    @TableField(exist = false)
    @ApiModelProperty(value = "任务是否完成")
    private List<CopyVolunteer> volunteerList;
    /**
     * 任务名称
     */
    @TableField("name")
    @ApiModelProperty(value = "任务名称")
    private String name;

    /**
     * 预计生成的服务时长
     */
    @TableField("estimate_service_long")
    @ApiModelProperty(value = "预计生成的服务时长")
    private BigDecimal estimateServiceLong;

    /**
     * 实际生成的服务时长
     */
    @TableField("reality_service_long")
    @ApiModelProperty(value = "实际生成的服务时长")
    private BigDecimal realityServiceLong;

    /**
     * 同步任务状态
     */
    @TableField("sync_task_status")
    @ApiModelProperty(value = "同步任务状态")
    private String syncTaskStatus;
}
