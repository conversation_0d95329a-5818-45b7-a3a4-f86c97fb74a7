package com.fykj.scaffold.security.business.mapper;

import com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto;
import com.fykj.scaffold.security.business.domain.entity.SysApi;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 系统资源-API接口
 * <p>
 * Mapper 接口
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface SysApiMapper extends BaseMapper<SysApi> {
    List<ProtectedApiOAuthDto> getRoleApiPathList();
}
