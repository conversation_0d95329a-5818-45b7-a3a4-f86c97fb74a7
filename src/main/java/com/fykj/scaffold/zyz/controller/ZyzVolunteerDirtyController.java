package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerDirty;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerDirtyParams;
import com.fykj.scaffold.zyz.service.IZyzVolunteerDirtyService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 志愿者表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-01
 */
@RestController
@RequestMapping("/admin/zyz/volunteer/dirty")
public class ZyzVolunteerDirtyController extends BaseController<IZyzVolunteerDirtyService, ZyzVolunteerDirty, ZyzVolunteerDirtyParams> {



}
