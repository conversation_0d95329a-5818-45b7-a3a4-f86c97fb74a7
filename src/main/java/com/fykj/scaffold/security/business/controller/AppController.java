package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.App;
import com.fykj.scaffold.security.business.domain.params.AppParams;
import com.fykj.scaffold.security.business.service.IAppService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import utils.UUIDUtils;

/**
 * 应用管理前端控制器
 *
 * <AUTHOR>
 * @date 2021-05-20
 */
@RestController
@RequestMapping("/admin/app")
public class AppController extends BaseController<IAppService, App, AppParams> {


    @ApiOperation("获取随机AppId")
    @GetMapping("/appId")
    public JsonResult<String> appId() {
        return new JsonResult<>(UUIDUtils.generateShortUuid());
    }

}
