package com.fykj.scaffold.civilization_cultivation.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 文明培育数据
 *
 * <AUTHOR> @email ${email}
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("文明培育数据导入模型")
public class CivilizationCultivationImportDto implements Serializable {

    private static final long serialVersionUID = -5484384830463476697L;
    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String phone;

    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private Integer year;

    /**
     * 所属社区
     */
    @ExcelProperty(value = "所属社区")
    private String community;

    /**
     * 事迹
     */
    @ExcelProperty(value = "事迹")
    private String achievements;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 外链
     */
    @ExcelProperty(value = "外链")
    private String outLinkAddress;

    /**
     * 介绍
     */
    @ExcelProperty(value = "介绍")
    private String description;

    /**
     * 导入失败原因
     */
    @ExcelProperty(value = "导入失败原因")
    private String failMsg;
}
