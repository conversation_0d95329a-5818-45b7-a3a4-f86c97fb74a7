package com.fykj.scaffold.mall.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ExchangeDRExportDto {
    private static final long serialVersionUID = 1L;
    @ExcelProperty("所属年度")
    @ColumnWidth(30)
    @ApiModelProperty(value = "所属年度")
    private String year;
    /**
     * 服务时长(小时)
     */
    @ExcelProperty("服务时长(小时)")
    @ColumnWidth(40)
    private String serverTime;

    /**
     * 达标人数
     */
    @ExcelProperty("达标人数")
    @ColumnWidth(30)
    private Integer qualifiedPersonnelNum;

    @ExcelProperty("时长")
    @ColumnWidth(30)
    @ApiModelProperty(value = "时长")
    private BigDecimal totalServerTime;
    /**
     * 产生积分
     */
    @ExcelProperty("产生积分")
    @ColumnWidth(40)
    private BigDecimal totalPoint;
    /**
     * 兑换积分
     */
    @ExcelProperty("兑换积分")
    @ColumnWidth(30)
    private BigDecimal totalExchangePoint;
    /**
     * 兑换人次
     */
    @ExcelProperty("兑换人次")
    @ColumnWidth(30)
    private Integer totalExchangeNum;
}
