package com.fykj.scaffold.zsq_docking.receive.service;


import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.NewsDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import result.Result;

/**
 * @Author：yangxu
 * @Date：2025/4/22 11:19
 * @Description：
 */
public interface INewsReceiveFromZSQService {

    /**
     * 新闻对接
     *
     * @param dockingInfo
     * @param data
     * @param dockingRecord
     */
    Result docking(NewsDockingDto dockingInfo, ZSQNewsInfoDto data, ZSQDockingNewsRecord dockingRecord);
}
