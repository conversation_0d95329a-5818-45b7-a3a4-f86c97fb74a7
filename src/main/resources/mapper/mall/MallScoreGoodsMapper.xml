<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallScoreGoodsMapper">

    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallScoreGoods">
        select v.*,so.name as orgName from mall_score_goods v left join   sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.goods_name LIKE CONCAT('%',#{params.keyWord},'%')or
                 v.link_person_name LIKE CONCAT('%',#{params.keyWord},'%')
                or v.link_phone LIKE CONCAT('%',#{params.keyWord},'%')
                 )
        </if>
        <if test="params.maxRequiredScore != null and params.maxRequiredScore != ''">
            and (v.required_score &lt;= #{params.maxRequiredScore} or  v.offline_score &lt;= #{params.maxRequiredScore})
        </if>
        <if test="params.minRequiredScore != null and params.minRequiredScore != ''">
            and (v.required_score &gt;= #{params.minRequiredScore} or  v.offline_score >= #{params.minRequiredScore})
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and v.org_code =#{params.orgCode}
        </if>
        <if test="params.scoreGoodsType != null and params.scoreGoodsType != ''">
            and v.score_goods_type =#{params.scoreGoodsType}
        </if>
        <if test="params.status != null">
            and v.status= #{params.status}
        </if>
        <if test="params.star != null">
            and v.is_star= #{params.star}
        </if>
       order by v.create_date desc
    </select>
    <select id="getGoodsList" resultType="com.fykj.scaffold.mall.domain.dto.MallScoreGoodsDto">
        select v.goods_name as goodsName,
               v.goods_count as goodsCount,
               v.required_score as requiredScore,
               v.offline_score as offlineScore,
               v.exchange_address as exchangeAddress,
               v.link_person_name as linkPersonName,
               v.link_phone as linkPhone,
        (CASE v.status
        WHEN true THEN  '上架'
        WHEN false THEN  '下架'
        END)  as statusText,so.name as orgName from mall_score_goods v left join   sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.goods_name LIKE CONCAT('%',#{params.keyWord},'%')or
            v.link_person_name LIKE CONCAT('%',#{params.keyWord},'%')
            or v.link_phone LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.maxRequiredScore != null and params.maxRequiredScore != ''">
            and (v.required_score &lt;= #{params.maxRequiredScore} or  v.offline_score &lt;= #{params.maxRequiredScore})
        </if>
        <if test="params.minRequiredScore != null and params.minRequiredScore != ''">
            and (v.required_score &gt;= #{params.minRequiredScore} or  v.offline_score >= #{params.minRequiredScore})
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and v.org_code =#{params.orgCode}
        </if>
        <if test="params.scoreGoodsType != null and params.scoreGoodsType != ''">
            and v.score_goods_type =#{params.scoreGoodsType}
        </if>
        <if test="params.status != null">
            and v.status= #{params.status}
        </if>
        order by v.create_date desc
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.mall.domain.entity.MallScoreGoods">
        select v.*,
               so.name as orgName
        from mall_score_goods v
        left join sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE -- and v.goods_count &gt; 0
        <if test="params.padExchange != null and params.padExchange == true">
            AND v.score_goods_type = 'mall_score_goods_type_pad'
          <if test="params.stationCode != null and params.stationCode != ''">
              AND v.station_code LIKE CONCAT('%', CONCAT(#{params.stationCode}, '%'))
          </if>
        </if>
        <if test="params.padExchange == null or params.padExchange == false">
            AND v.score_goods_type != 'mall_score_goods_type_pad'
        </if>
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.goods_name LIKE CONCAT('%',#{params.keyWord},'%')or
            v.link_person_name LIKE CONCAT('%',#{params.keyWord},'%')
            or v.link_phone LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.maxRequiredScore != null and params.maxRequiredScore != ''">
            and (v.required_score &lt;= #{params.maxRequiredScore} or  v.offline_score &lt;= #{params.maxRequiredScore})
        </if>
        <if test="params.minRequiredScore != null and params.minRequiredScore != ''">
            and (v.required_score &gt;= #{params.minRequiredScore} or  v.offline_score >= #{params.minRequiredScore})
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and v.org_code =#{params.orgCode}
        </if>
        <if test="params.status != null">
            and v.status= #{params.status}
        </if>
        <if test="params.machineNo != null and params.machineNo != ''">
            and v.machine_no =#{params.machineNo}
        </if>
        order by v.create_date desc
    </select>
    <select id="sumGoodsCountBySku" resultType="java.lang.Integer">
        select ifnull(sum(goods_count),0) from mall_score_goods where is_deleted = false and sku=#{sku}
    </select>
    <select id="pageForApiStar" resultType="com.fykj.scaffold.mall.domain.entity.MallScoreGoods">
        select v.*,
        so.name as orgName
        from mall_score_goods v
        left join sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.padExchange != null and params.padExchange == true">
            AND v.score_goods_type = 'mall_score_goods_type_pad'
            <if test="params.stationCode != null and params.stationCode != ''">
                AND v.station_code LIKE CONCAT('%', CONCAT(#{params.stationCode}, '%'))
            </if>
        </if>
        <if test="params.padExchange == null or params.padExchange == false">
            AND v.score_goods_type != 'mall_score_goods_type_pad'
        </if>
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.goods_name LIKE CONCAT('%',#{params.keyWord},'%')
                or v.link_person_name LIKE CONCAT('%',#{params.keyWord},'%')
                or v.link_phone LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.maxRequiredScore != null and params.maxRequiredScore != ''">
            and (v.required_score &lt;= #{params.maxRequiredScore} or  v.offline_score &lt;= #{params.maxRequiredScore})
        </if>
        <if test="params.minRequiredScore != null and params.minRequiredScore != ''">
            and (v.required_score &gt;= #{params.minRequiredScore} or  v.offline_score >= #{params.minRequiredScore})
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and v.org_code =#{params.orgCode}
        </if>
        <if test="params.status != null">
            and v.status= #{params.status}
        </if>
        <if test="params.star != null">
            and v.is_star= #{params.star}
        </if>
        <if test="params.courteousType != null and params.courteousType != ''">
            and v.courteous_type= #{params.courteousType}
        </if>
        order by v.sequence desc, v.create_date desc
    </select>
</mapper>
