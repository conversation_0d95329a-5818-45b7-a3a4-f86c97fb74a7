package com.fykj.scaffold.zsq_docking.push_mq.listener;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingActivityRecord;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingMqFail;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingActivityRecordService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingMqFailService;
import com.fykj.scaffold.zsq_docking.push_mq.event.ActivityDockingEvent;
import com.fykj.scaffold.zsq_docking.push_mq.service.IZSQDataPushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.DOCKING_RECORD_STATUS_FAIL;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.TOPIC_ACTIVITY_DOCKING;

@Slf4j
@Service
@RocketMQMessageListener(consumerGroup = "${spring.application.name}" + TOPIC_ACTIVITY_DOCKING, topic = TOPIC_ACTIVITY_DOCKING)
public class ActivityDockingListener implements RocketMQListener<ActivityDockingEvent>, AutoPushZSHMonitor {


    @Autowired
    private IZSQDockingActivityRecordService activityDockingRecordService;

    @Autowired
    private IZSQDataPushService pushService;

    @Autowired
    private IZSQDockingMqFailService pushFailService;

    @Override
    public void onMessage(ActivityDockingEvent notice) {
        ZSQDockingMqFail fail = new ZSQDockingMqFail();
        Long dockingRecordId = notice.getDockingRecordId();
        fail.setDockingRecordId(dockingRecordId);
        fail.setMessageId(notice.getMsgId());
        fail.setContent(JSONUtil.toJsonStr(notice));
        fail.setMsgTime(notice.getMsgCreateTime());
        Long activityId = notice.getActId();
        if (activityId == null) {
            fail.setExceptionTrace("活动 id 未获取到！");
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingActivityRecord record = activityDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【活动 id 未获取到】！");
                activityDockingRecordService.updateById(record);
            }
            return;
        }
        try {
            pushService.activityDocking(notice);
        } catch (Exception e) {
            fail.setExceptionTrace(ExceptionUtil.stacktraceToString(e));
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingActivityRecord record = activityDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【" + ExceptionUtil.stacktraceToString(e) + "】！");
                activityDockingRecordService.updateById(record);
            }
        }
    }
}
