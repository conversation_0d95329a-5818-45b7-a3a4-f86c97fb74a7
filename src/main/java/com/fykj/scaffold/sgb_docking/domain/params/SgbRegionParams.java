package com.fykj.scaffold.sgb_docking.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 苏州志愿者平台区划信息查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("苏州志愿者平台区划信息查询参数")
public class SgbRegionParams extends BaseParams {

    private static final long serialVersionUID = 1L;

    /**
     * 区划编号
     */
    @ApiModelProperty("区划编号")
    @MatchType(QueryType.LIKE)
    private String regionCode;

    /**
     * 区划名称
     */
    @ApiModelProperty("区划名称")
    @MatchType(QueryType.LIKE)
    private String regionName;

    /**
     * 父级区划编号
     */
    @ApiModelProperty("父级区划编号")
    @MatchType(QueryType.LIKE)
    private String parentCode;
} 