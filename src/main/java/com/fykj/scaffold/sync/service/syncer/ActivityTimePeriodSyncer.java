package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActp;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActpResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.fykj.scaffold.support.conns.Cons.*;

/**
 * 活动同步
 */
@Component
public class ActivityTimePeriodSyncer {

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog sync(long actTimePeriodId, long syncBillId) {
        ZyzActivityTimePeriod actTimePeriod = actTimePeriodService.getById(actTimePeriodId);
        Long activityId = actTimePeriod.getActivityId();
        ZyzActivity activity = activityService.getById(actTimePeriod.getActivityId());
        //活动审核通过，当前时间大于活动结束时间，且处于待同步状态需要同步
        if (PlatformSyncState.SUCCESS.equals(actTimePeriod.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }

        if (!ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(activity.getAuditStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动还未审核通过");
        }

        if (!actTimePeriod.getEndTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此时间分段活动尚未结束");
        }
        int timePeriods = actTimePeriodService.lambdaQuery().eq(ZyzActivityTimePeriod::getActivityId, activityId).count().intValue();
        String actName = activity.getName();
        String startTime = actTimePeriod.getStartTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        String endTime = actTimePeriod.getEndTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        if (timePeriods > 1) {
            actName += ("【" + startTime + "--" + endTime + "】");
        }
        SyncActp syncAct = new SyncActp();
        syncAct.setUniCode(String.valueOf(actTimePeriod.getId()));
        syncAct.setOrgId(SyncConstant.AS_ORG_ID);
        syncAct.setName(actName);
        syncAct.setServiceField(activity.getBelongFieldEnd());
        syncAct.setRegionCode(SyncConstant.AS_REGION_CODE);
        syncAct.setDescs(actName);
        syncAct.setAddress(activity.getAddress());
        syncAct.setStartDate(startTime.substring(0, 10));
        syncAct.setEndDate(endTime.substring(0, 10));
        syncAct.setStartTime(startTime.substring(11, 16));
        syncAct.setEndTime(endTime.substring(11, 16));
        syncAct.setLinkName(activity.getContactPerson());
        syncAct.setLinkPhone(activity.getContactPhone());
        syncAct.setRecordWay(SyncActp.recordWay.TIME_SLOT);
        syncAct.setServiceTime(null);
        syncAct.setTxMapLan(activity.getLongitude() != null ? activity.getLongitude().toString() : SyncConstant.DEFAULT_LONGITUDE);
        syncAct.setTxMapLat(activity.getLatitude() != null ? activity.getLatitude().toString() : SyncConstant.DEFAULT_LATITUDE);
        syncAct.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(activity.getPicture())));
        syncAct.setServiceTarget("99");

        SyncActpResult syncActpResult = ApiWebService.syncActivity(syncAct);

        actTimePeriod.setSyncTime(LocalDateTime.now());
        actTimePeriod.setSyncRemark(syncActpResult.getMsg());
        //成功
        if ("同步成功".equals(syncActpResult.getMsg())) {
            actTimePeriod.setSyncId(syncActpResult.getApId());
            actTimePeriod.setSync(PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (syncActpResult.getMsg().contains("唯一号已存在")) {
            if (StringUtil.isNotEmpty(syncActpResult.getApId())) {
                actTimePeriod.setSyncId(syncActpResult.getApId());
            }
            actTimePeriod.setSync(PlatformSyncState.SUCCESS);
            actTimePeriod.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            actTimePeriod.setSync(PlatformSyncState.FAILURE);
        }

        // 启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(PlatformSyncBiz.ACTIVITY_SYNC, syncActpResult.getCode(), syncActpResult.getMsg()
                , syncBillId, actTimePeriod.getId(), actName
                , JSONUtil.toJsonStr(syncAct), JSONUtil.toJsonStr(syncActpResult));

        // 回写业务
        actTimePeriodService.updateById(actTimePeriod);

        return log;
    }
}
