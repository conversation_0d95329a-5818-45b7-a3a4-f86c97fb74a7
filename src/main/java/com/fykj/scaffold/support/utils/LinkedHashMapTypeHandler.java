package com.fykj.scaffold.support.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import utils.StringUtil;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedHashMap;

/**
 * 2025/5/20
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(LinkedHashMap.class)
public abstract class LinkedHashMapTypeHandler<T, K> extends BaseTypeHandler<LinkedHashMap<T, K>> {

    /** 序列化时：只关闭循环引用检测，不触发任何 key 排序 */
    private static final SerializerFeature[] WRITE_FEATURES = {
            SerializerFeature.DisableCircularReferenceDetect
    };

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    LinkedHashMap<T, K> parameter,
                                    JdbcType jdbcType) throws SQLException {
        String content = CollUtil.isEmpty(parameter)
                ? "{}"
                : JSON.toJSONString(parameter, WRITE_FEATURES);
        ps.setString(i, content);
    }

    @Override
    public LinkedHashMap<T, K> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getString(columnName));
    }

    @Override
    public LinkedHashMap<T, K> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getString(columnIndex));
    }

    @Override
    public LinkedHashMap<T, K> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getString(columnIndex));
    }

    private LinkedHashMap<T, K> parseJson(String content) {
        if (StringUtil.isEmpty(content)) {
            return new LinkedHashMap<>();
        }
        // 使用子类提供的 TypeReference，且加上 Feature.OrderedField 保留 JSON 文本的键顺序
        return JSON.parseObject(content, specificType(), Feature.OrderedField);
    }

    /**
     * 子类必须实现，返回具体的 TypeReference，
     * 比如 new TypeReference<LinkedHashMap<String, String>>() {}
     */
    protected abstract TypeReference<LinkedHashMap<T, K>> specificType();
}

