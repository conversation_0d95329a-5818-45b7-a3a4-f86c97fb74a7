package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.mapper.DictMapper;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.conns.Cons;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.client.DictCacheClient;
import fykj.microservice.cache.config.RedisService;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.base.BaseTreeEntity;
import fykj.microservice.core.beans.vo.IdTextVo;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@Service
@Slf4j
public class DictServiceImpl extends BaseServiceImpl<DictMapper, Dict> implements IDictService {
    @Autowired
    private DictCacheClient dictCacheClient;

    @Autowired
    private RedisService redisService;


    @Override
    public Dict getByCode(String code) {
        Dict dict = lambdaQuery().eq(Dict::getCode, code).eq(Dict::getStatus, 1).one();
        if (dict == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "未找到指定code的数据字典:" + code);
        }
        return dict;
    }

    @Override
    public Dict getByCode(String code, Boolean throwEx) {
        try {
            return getByCode(code);
        } catch (Exception e) {
            if (throwEx == null || throwEx) {
                throw e;
            } else {
                return null;
            }
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Dict> findByParentCode(String code) {
        Dict parent = getByCode(code);
        return lambdaQuery().eq(Dict::getParentId, parent.getId()).eq(Dict::getStatus, true)
                .orderByAsc(Dict::getSequence).list();
    }

    @Override
    public List<IdTextVo> idTextVoList(String type) {
        return findByParentCode(type).stream()
                .filter(Dict::getStatus)
                .map(it -> new IdTextVo(it.getCode(), it.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<IdTextVo> findTopDict() {
        return lambdaQuery().isNull(Dict::getParentId).list().stream()
                .map(it -> new IdTextVo(it.getId().toString(), it.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<IdTextVo> findTopDictCode() {
        return lambdaQuery().isNull(Dict::getParentId).list().stream()
                .map(it -> new IdTextVo(it.getCode(), it.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean checkCodeExists(Serializable id, String code) {
        return lambdaQuery().eq(Dict::getCode, code)
                .ne(id != null, Dict::getId, id).count() > 0;
    }

    @Override
    public void batchAdd(long parentId, String text) {
        List<String> textLines = Arrays.asList(text.split("\n"));
        Dict parent = get(parentId);
        for (int idx = 0; idx < textLines.size(); idx++) {
            String line = textLines.get(idx);
            String[] lineContents = line.split(Mark.SPACE);
            String name = lineContents[0];
            String code = lineContents[1];
            Dict dict = new Dict();
            dict.setParentId(parentId);
            dict.setName(name);
            dict.setCode(code);
            // dict.setValue(code);
            dict.setSequence(idx + 1);
            dict.setStatus(true);
            dict.setValue(parent.getValue() + Mark.COMMA + code);
            save(dict);
        }
    }

    @Override
    public Dict get(Long id) {
        Dict dict = super.getById(id);
        Long pid = dict.getParentId();
        if (pid != null) {
            Dict parent = super.getById(pid);
            dict.setParentName(parent.getName());
        }
        return dict;
    }

    @Override
    public List<Dict> tree() {
        return buildTree(list());
    }

    @Override
    public List<Dict> tree(String parentCode, boolean includeRoot) {
        List<Dict> rootDict;
        if (includeRoot) {
            rootDict = CollectionUtil.newArrayList(getByCode(parentCode));
        } else {
            rootDict = findByParentCode(parentCode);
        }
        Map<Long, List<Dict>> allDicts = list()
                .stream()
                .filter(it -> Objects.nonNull(it.getParentId()))
                .collect(Collectors.groupingBy(BaseTreeEntity::getParentId));
        rootDict.forEach(dict -> buildChildrenNode(dict, allDicts));
        return rootDict;
    }

    private void buildChildrenNode(Dict parentNode, Map<Long, List<Dict>> dictMap) {
        Long parentId = parentNode.getId();
        List<Dict> children = dictMap.get(parentId);
        parentNode.setChildren(children);
        if (CollectionUtil.isNotEmpty(children)) {
            children.forEach(child -> buildChildrenNode(child, dictMap));
        }
    }

    @Override
    public List<Dict> list() {
        return list(new QueryWrapper<Dict>().orderByAsc("sequence"));
    }

    @Override
    public boolean save(Dict entity) {
        if (checkCodeExists(null, entity.getCode())) {
            throw new BusinessException(ResultCode.FAIL, "数据编码已存在");
        }
        dictCacheClient.addDict(entity.getCode(), entity.getName());
//        entity.setCodePrefix(buildCodePrefix(entity));
        return super.save(entity);
    }

    @Override
    public boolean updateById(Dict entity) {
        String code = entity.getCode();
        if (checkCodeExists(entity.getId(), code)) {
            throw new BusinessException(ResultCode.FAIL, "数据编码已存在");
        }
        dictCacheClient.deleteDict(code);
        dictCacheClient.addDict(code, entity.getName());
//        entity.setCodePrefix(buildCodePrefix(entity));
        return super.updateById(entity);
    }

//    private String buildCodePrefix(Dict dict) {
//        if (dict.getParentId() == null) {
//            return dict.getCode();
//        } else {
//            List<String> pList = CollectionUtil.newArrayList(dict.getCode());
//            Long pId = dict.getParentId();
//            while (pId != null) {
//                Dict pDict = getById(pId);
//                pList.add(pDict.getCode());
//                pId = pDict.getParentId();
//            }
//            return StringUtil.join(CollectionUtil.reverse(pList));
//        }
//    }

    @Override
    public void refreshDictCache() {
        dictCacheClient.deleteAll();
        Map<String, String> dictMap = list().stream().collect(Collectors.toMap(Dict::getCode, Dict::getName));
        redisService.hmSetBatch(DictCacheClient.DICT_KEY, dictMap, -1);
    }

    @Override
    public String getNameByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return "";
        }

        try {
            Dict dict = getByCode(code);
            return dict.getName();

        } catch (Exception e) {
            log.error("not found of dict code:{}。", code, e);
        }
        return code;
    }

    @Override
    public String findFileTypeByValue(String value) {
        try {
            String parentId = getByCode(Cons.FILE_TYPE).getId().toString();
            Dict dict = lambdaQuery().eq(Dict::getParentId, parentId)
                    .like(Dict::getValue, ',' + value + ',').one();
            return dict != null ? dict.getCode() : Cons.OTHER_TYPES;
        } catch (MyBatisSystemException e) {
            log.error("查询文件类型{}失败", value, e);
            throw new BusinessException(ResultCode.ERROR, "查询文件类型{+" + value + "+}失败");
        }
    }

    @Override
    public List<String> getOssExtList() {
        String parentId = getByCode(Cons.FILE_TYPE).getId().toString();
        return lambdaQuery().eq(Dict::getParentId, parentId)
                .eq(Dict::getStatus, true).list()
                .stream()
                .map(Dict::getValue)
                .map(it -> it.substring(1, it.length() - 1))
                .flatMap(it -> Arrays.stream(it.split(Mark.COMMA)))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, List<Dict>> getDictMap(String[] dictArray) {
        Map<String, List<Dict>> result = new HashMap<>();
        for (String dict : dictArray) {
            List<Dict> dictList = findByParentCode(dict);
            result.put(dict, dictList);
        }
        return result;
    }

    @Override
    public int getIntValueByCode(String code) {
        Dict dict = getByCode(code);
        return Integer.parseInt(dict.getValue());
    }

    @Override
    public String getStringValueByCode(String code) {
        Dict dict = getByCode(code);
        return dict.getValue();
    }
}
