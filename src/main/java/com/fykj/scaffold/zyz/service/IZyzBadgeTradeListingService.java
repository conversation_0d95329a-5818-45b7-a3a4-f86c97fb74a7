package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgeTradeDetailDTO;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadge;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTradeListing;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeTradeListingParams;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeTradeHallVO;
import fykj.microservice.core.base.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 勋章交易发布-服务类
 *
 * @date 2025-05-09
 */
public interface IZyzBadgeTradeListingService extends IBaseService<ZyzBadgeTradeListing> {

    /**
     * 分页查询
     * @param params 查询参数
     * @return 分页结果
     */
    Page<ZyzBadgeTradeListing> pageQuery(ZyzBadgeTradeListingParams params);
    
    /**
     * 根据勋章ID查询平台发布的交易（状态为发布中）
     * @param badgeId 勋章ID
     * @return 交易列表
     */
    List<ZyzBadgeTradeListing> findPlatformListingByBadgeId(Long badgeId);
    
    /**
     * 取消交易发布
     * @param id 交易发布ID
     */
    void cancelListing(Long id);
    
    /**
     * 批量取消交易发布
     * @param ids 交易发布ID列表
     */
    void batchCancelListing(List<Long> ids);
    
    /**
     * 创建勋章平台交易发布记录
     * @param badge 勋章信息
     * @return 创建的交易发布记录
     */
    ZyzBadgeTradeListing createPlatformListing(ZyzBadge badge);

    /**
     * 计算费率后积分
     * @param pointsBeforeFee 费率前积分
     * @param feeRate 费率百分比
     * @return 费率后积分
     */
    int calculatePointsAfterFee(Integer pointsBeforeFee, int feeRate);
    
    /**
     * 查询交易大厅列表
     * @param params 查询参数
     * @return 交易大厅分页数据
     */
    IPage<ZyzBadgeTradeHallVO> queryTradingListings(ZyzBadgeTradeListingParams params);


    /**
     * 获取勋章交易详情
     * @param listingId 交易上架ID
     * @return 勋章交易详情
     */
    ZyzBadgeTradeDetailDTO getTradeDetail(Long listingId);

    Map.Entry<Integer, Integer> getListingPointRange(Long badgeId);
}

