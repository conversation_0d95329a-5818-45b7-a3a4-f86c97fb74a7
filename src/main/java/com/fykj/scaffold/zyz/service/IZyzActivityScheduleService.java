package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.zyz.domain.dto.ActivityScheduleReportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivitySchedule;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 阵地计划
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
public interface IZyzActivityScheduleService extends IBaseService<ZyzActivitySchedule> {

    /**
     * 阵地计划--分页查询
     *
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<ZyzActivitySchedule> pagesForSchedule(ZyzActivityScheduleParams params);

    /**
     * 阵地计划-分页查询
     *
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<ZyzActivitySchedule> pagesForAudit(ZyzActivityScheduleParams params);


    /**
     * 获取阵地计划详情
     *
     * @param scheduleId 计划id
     * @return 计划详情
     */
    ZyzActivitySchedule getDetail(Long scheduleId);

    /**
     * 保存/修改-保存为草稿
     *
     * @param entity 阵地计划
     */
    void saveOrUpdateToDraft(ZyzActivitySchedule entity);

    /**
     * 保存阵地计划并提交
     *
     * @param entity 活动公示
     * @return 是否成功
     */
    boolean render(ZyzActivitySchedule entity);

    /**
     * 提交（by id)
     *
     * @param id 公示id
     * @return 是否成功
     */
    boolean renderById(Long id);

    /**
     * 撤回
     *
     * @param id 计划id
     * @return 是否成功
     */
    boolean recall(long id);

    /**
     * 阵地计划审核
     *
     * @param idList 计划id列表
     * @param status 状态
     * @param remark 备注
     */
    void audit(List<Long> idList, Boolean status, String remark);

    /**
     * 上下架
     *
     * @param id 计划id
     * @return 是否成功
     */
    boolean updateAutoStatus(Long id);

    /**
     * 获取需要同步的数据
     *
     * @return id列表
     */
    List<Long> getNeedSync();

    /**
     * 批量同步
     *
     * @param scheduleIds 计划ids
     */
    void syncBatch(List<Long> scheduleIds);

    /**
     * 单条同步
     *
     * @param scheduleId 计划id
     * @return ZyzSyncLog 同步log
     */
    ZyzSyncLog syncOne(Long scheduleId);

    /**
     * 阵地计划报表--分页查询
     *
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<ActivityScheduleReportDto> pagesForReport(ZyzActivityScheduleParams params);

    /**
     *  根据计划类型, 计划数值, 发布组织查找详情
     * @param scheduleType 计划类型
     * @param scheduleTypeData 计划数值
     * @param publishOrgCode 发布组织code
     * @return 计划详情
     */
    ZyzActivitySchedule getDetailByScheduleTypeAndScheduleTypeDataAndPublishOrgCode(
            String scheduleType, String scheduleTypeData, String publishOrgCode);

    /**
     *  根据计划类型, 计划数值, 发布组织查找计划详情的列表
     * @param scheduleType 计划类型
     * @param scheduleTypeData 计划数值
     * @param publishOrgCode 发布组织code
     * @return 计划详情的列表
     */
    List<ZyzActivityScheduleDetail> getDetailListByScheduleTypeAndScheduleTypeDataAndPublishOrgCode(
            String scheduleType, String scheduleTypeData, String publishOrgCode);
            
    /**
     * 获取阵地计划三级级联数据
     * 第一级：scheduleType字典
     * 第二级：ZyzActivitySchedule实体
     * 第三级：ZyzActivityScheduleDetail实体
     *
     * @return 级联数据列表
     */
    List<CascaderExtraDto> getScheduleCascaderData();
}
