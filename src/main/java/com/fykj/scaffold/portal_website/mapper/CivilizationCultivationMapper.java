package com.fykj.scaffold.portal_website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto;
import com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation;
import com.fykj.scaffold.portal_website.domain.params.CivilizationCultivationParams;
import com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationCultivateDto;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  文明培育Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface CivilizationCultivationMapper extends BaseMapper<CivilizationCultivation> {

    /**
     * 分页查询文明培育
     * @param page
     * @param params
     * @return
     */
    IPage<CivilizationCultivation> pageForCivilizationCultivation(Page<CivilizationCultivation> page, @Param("params") CivilizationCultivationParams params);

    /**
     * 分页查询文明培育(网页展示)
     * @param page
     * @param params
     * @return
     */
    IPage<CivilizationCultivation> pageForCivilizationCultivationApi(Page<CivilizationCultivation> page, @Param("params") CivilizationCultivationParams params);

    /**
     * 获取年份
     * @param categoryId
     * @return
     */
    List<String> getYears(@Param("categoryId") String categoryId);

    /**
     * 获取大屏数据列表
     * @param params
     * @return
     */
    List<CivilizationCultivateDto> getScreenDataList(@Param("params") DataScreenParams params);


    /**
     * 获取最大年份
     * @param categoryId
     * @return
     */
    String getMaxYears(@Param("categoryId") String categoryId);



    /**
     * 获取驾驶舱大屏数据列表
     * @param categoryId
     * @param year
     * @return
     */
    List<CivilizationCultivationDashboardDto> mienCockpitData(@Param("categoryId") String categoryId, @Param("year") String year);
}
