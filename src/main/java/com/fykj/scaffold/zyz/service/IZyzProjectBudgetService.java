package com.fykj.scaffold.zyz.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectBudget;

import java.util.List;

/**
 * 项目预算明细
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
public interface IZyzProjectBudgetService extends IBaseService<ZyzProjectBudget> {


    /**
     * 保存项目预算明细列表
     * @param list，projectId
     */
    void saveProjectBudget(List<ZyzProjectBudget> list,Long projectId);
}

