package com.fykj.scaffold.zyz.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectTypeProject;

import java.util.List;

/**
 * 公益伙伴计划-项目类别中间表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
public interface IZyzProjectTypeProjectService extends IBaseService<ZyzProjectTypeProject> {


    /**
     * 保存项目类别
     * @param type
     * @param projectId
     */
    void saveProjectType(List<String> type, Long projectId);

    /**
     * 根据项目id获取项目类别
     * @param projectId
     * @return
     */
    List<String> getTypeList(Long projectId);
}

