package com.fykj.scaffold.onenet;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class OneNetParams implements Serializable {
    private static final long serialVersionUID = -7185989971411436841L;

    private String appmark;

    private String time;

    private String sign;

    private String servicename;

    private String params;

}
