package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.conns.SyncApiCons;
import com.fykj.volunteer.sync.util.domain.sync_user.SyncUser;
import com.fykj.volunteer.sync.util.domain.sync_user.SyncUserResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 志愿者同步
 */
@Component
public class VolunteerSyncer {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IZyzSyncLogService logService;

    //证件类型
    private final static Map<String, String> TYPE_MAP = new HashMap<>();

    static {
        TYPE_MAP.put("certificate_type_04", SyncUser.CardType.SYNC_USER_TYPE_GA);
        TYPE_MAP.put("certificate_type_03", SyncUser.CardType.SYNC_USER_TYPE_DL);
        TYPE_MAP.put("certificate_type_02", SyncUser.CardType.SYNC_USER_TYPE_GA);
        TYPE_MAP.put("certificate_type_01", SyncUser.CardType.SYNC_USER_TYPE_HZ);
    }

    public ZyzSyncLog sync(long volunteerId, long syncBillId) {
        ZyzVolunteer volunteer = volunteerService.getById(volunteerId);
        if (Cons.PlatformSyncState.SUCCESS.equals(volunteer.getIsSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }
        SyncUser oneUser = new SyncUser();
        oneUser.setIdCode(volunteer.getCertificateId());
        oneUser.setType(TYPE_MAP.get(volunteer.getCertificateType()));
        oneUser.setName(volunteer.getName());
        oneUser.setPhone(volunteer.getPhone());
        SyncUserResult result = ApiWebService.syncUser(oneUser);
        String resultCode = result.getCode();
        String msg = result.getMsg();

        volunteer.setSyncTime(LocalDateTime.now());
        volunteer.setSyncRemark(msg);
        //成功
        if (("操作成功".equals(msg) || "同步成功".equals(msg)) && SyncApiCons.ResultCode.SUCCESS.equals(resultCode)) {
            //同步成功，修改实名状态为已实名
            volunteer.setCertification(true);
            volunteer.setIsSync(Cons.PlatformSyncState.SUCCESS);
        }
        //同步成功：实名认证超时（请联系苏州志愿者平台管理员进行身份证手动认证，未实名认证的志愿者将会限制上传业务数据）
        else if (msg.contains("实名认证超时")) {
            volunteer.setIsSync(Cons.PlatformSyncState.FAILURE);
        }
        //该志愿者已存在，针对这种情况我们认为同步成功了，因为该志愿者可能从其他平台注册了志愿者且已同步
        else if (msg.contains("该志愿者已存在")) {
            volunteer.setCertification(true);
            volunteer.setSyncRemark("市区提示已存在，系统自动处理为同步成功");
            volunteer.setIsSync(Cons.PlatformSyncState.SUCCESS);
        }
        //其他一律失败
        else {
            volunteer.setIsSync(Cons.PlatformSyncState.FAILURE);
        }
        //回写业务
        volunteerService.updateById(volunteer);

        //记录日志
        return logService.buildSyncLog(Cons.PlatformSyncBiz.VOLUNTEER_SYNC, resultCode, msg
                , syncBillId, volunteerId, volunteer.getName()
                , JSONUtil.toJsonStr(oneUser), JSONUtil.toJsonStr(result));
    }
}
