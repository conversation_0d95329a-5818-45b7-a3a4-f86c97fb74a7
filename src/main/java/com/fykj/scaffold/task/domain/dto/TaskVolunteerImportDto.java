package com.fykj.scaffold.task.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ExcelIgnoreUnannotated
public class TaskVolunteerImportDto implements Serializable {
    private static final long serialVersionUID = 4504576285413277066L;

    /**
     * 志愿者名称
     */
    @ExcelProperty(index = 0)
    private String name;

    /**
     * 志愿者身份证号码
     */
    @ExcelProperty(index = 1)
    private String certificateId;

    /**
     * 志愿者手机号
     */
    @ExcelProperty(index = 2)
    @ApiModelProperty(value = "志愿者手机号")
    private String phone;

}
