package com.fykj.scaffold.security.login.builder;

import com.fykj.scaffold.security.login.domain.dto.OAuth2ClientDto;
import constants.Mark;
import lombok.Getter;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;

/**
 * <AUTHOR>
 * 凭证模式url
 */
@Getter
public class Oauth2UrlClientCredentialsBuilder extends Oauth2UrlBaseBuilder {

    public Oauth2UrlClientCredentialsBuilder(ClientCredentialsResourceDetails clientCredentialsResourceDetails, OAuth2ClientDto dto) {
        super(clientCredentialsResourceDetails,dto);
    }

    @Override
    public Oauth2UrlClientCredentialsBuilder buildOauth2Url(){
        OAuth2ClientDto oauth2BaseDto = (OAuth2ClientDto) getOauth2BaseDto();
        getUri().append(Mark.QUESTION)
                .append("&").append(OAUTH2_TOKEN_URL_GRANT_TYPE).append(Mark.EQUAL).append(oauth2BaseDto.getLoginType())
                .append("&").append(OAUTH2_TOKEN_URL_SCOPE).append(Mark.EQUAL).append(String.join("", getClientCredentialsResourceDetails().getScope()))
                .append("&").append(OAUTH2_TOKEN_URL_CLIENT_ID).append(Mark.EQUAL).append(oauth2BaseDto.getAppId())
                .append("&").append(OAUTH2_TOKEN_URL_CLIENT_SECRET).append(Mark.EQUAL).append(oauth2BaseDto.getClientSecret());
        return this;
    }

}
