package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fykj.scaffold.support.utils.ListTypeHandler;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "sys_app_menu_config")
public class SysAppMenuConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单名称
     */
    @TableField("name")
    @ApiModelProperty(value = "菜单名称")
    private String name;

    /**
     * 菜单code
     */
    @TableField("code")
    @ApiModelProperty(value = "菜单code")
    private String code;

    /**
     * 栏位code
     */
    @DictTrans(transTo = "categoryName")
    @TableField("category_code")
    @ApiModelProperty(value = "栏位code")
    private String categoryCode;

    /**
     * 栏位name
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "栏位name")
    private String categoryName;

    /**
     * 菜单描述
     */
    @TableField("description")
    @ApiModelProperty(value = "菜单描述")
    private String description;

    /**
     * 图片地址
     */
    @TableField("picture")
    @ApiModelProperty(value = "图片地址")
    private String picture;

    /**
     * 是否需要登录
     */
    @TableField("need_login")
    @ApiModelProperty(value = "是否需要登录")
    private Boolean needLogin;

    /**
     * 是否启用
     */
    @TableField("status")
    @ApiModelProperty(value = "是否启用")
    private Boolean status;

    /**
     * 是否外链
     */
    @TableField("out_link")
    @ApiModelProperty(value = "是否外链")
    private Boolean outLink;

    /**
     * 跳转目录
     */
    @TableField("jump_url")
    @ApiModelProperty(value = "跳转目录")
    private String jumpUrl;

    /**
     * 限制身份
     */
    @TableField(value = "limit_capacity", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "限制身份")
    private String limitCapacity;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;


}
