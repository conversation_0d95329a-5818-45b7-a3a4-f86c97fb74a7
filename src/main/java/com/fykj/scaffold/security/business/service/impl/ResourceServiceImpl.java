package com.fykj.scaffold.security.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto;
import com.fykj.scaffold.security.business.domain.entity.Resource;
import com.fykj.scaffold.security.business.domain.entity.SysAction;
import com.fykj.scaffold.security.business.mapper.ResourceMapper;
import com.fykj.scaffold.security.business.service.IResourceService;
import com.fykj.scaffold.security.business.service.ISysActionService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@Service
public class ResourceServiceImpl extends BaseServiceImpl<ResourceMapper, Resource> implements IResourceService {

    @Autowired
    private ISysActionService actionService;

    @Override
    public List<Resource> menuListForRolePage() {
        List<Resource> list = lambdaQuery().orderByAsc(Resource::getType, Resource::getSequence).list();
        List<SysAction> actionList = actionService.lambdaQuery().list();
        list.forEach(menu -> menu.setActionList(actionList.stream().filter(a -> a.getMenuId().equals(menu.getId())).collect(Collectors.toList())));
        return list;
    }

    @Override
    public List<Resource> list() {
        return list(new QueryWrapper<Resource>().orderByAsc("sequence"));
    }

    @Override
    public List<Resource> findResourceByUserId(long userId) {
        return baseMapper.findResourceByUserId(userId);
    }

    @Override
    public List<Resource> findResourceByUserIdAndRoleIds(long userId, String type, List<Long> roleIds) {
        return baseMapper.findResourceByUserIdAndRoleIds(userId, type, roleIds);
    }
}
