package com.fykj.scaffold.mall.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("商品查询参数")
public class MallScoreGoodsParams extends BaseParams {
    @ApiModelProperty("关键字")
    private String keyWord;
    @ApiModelProperty("最大积分")
    private BigDecimal maxRequiredScore;
    @ApiModelProperty("最小积分")
    private BigDecimal minRequiredScore;
    @ApiModelProperty("组织机构code")
    private String orgCode;
    @ApiModelProperty("状态")
    private Boolean status;

    @ApiModelProperty("商品类型")
    private String scoreGoodsType;

    @ApiModelProperty("是否pad兑换商品")
    private Boolean padExchange;

    @ApiModelProperty("站点编码")
    private String stationCode;

    @ApiModelProperty("是否是星光闪耀商品")
    private Boolean star;

    @ApiModelProperty(value = "星光闪耀礼遇类别")
    private String courteousType;

    @ApiModelProperty(value = "兑换机编码")
    private String machineNo;

}
