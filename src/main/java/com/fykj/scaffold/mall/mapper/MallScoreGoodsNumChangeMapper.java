package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsNumChange;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsNumChangeParams;
import org.apache.ibatis.annotations.Param;

/**
 *
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-22
 */
public interface MallScoreGoodsNumChangeMapper extends BaseMapper<MallScoreGoodsNumChange> {
    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    IPage<MallScoreGoodsNumChange> getPage(IPage<MallScoreGoodsNumChange> page, @Param("params") MallScoreGoodsNumChangeParams params);
}
