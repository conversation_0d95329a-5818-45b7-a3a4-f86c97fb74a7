package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncDep;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncDepService;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzResourceService;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.res.SyncRes;
import com.fykj.volunteer.sync.util.domain.res.SyncResResult;
import exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 需求同步
 */
@Component
public class ResourceSyncer {

    @Autowired
    private IZyzSyncLogService logService;

    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzSyncDepService depService;

    //需求类型
    private final static Map<String, String> RES_TYPE_MAP = new HashMap<>();

    static {
        RES_TYPE_MAP.put("RT_project", SyncRes.ResType.PROJECT_SERVICE);
        RES_TYPE_MAP.put("RT_specialist", SyncRes.ResType.TEAM_PRO);
        RES_TYPE_MAP.put("RT_team", SyncRes.ResType.TEAM_PRO);
        RES_TYPE_MAP.put("RT_pavilion", SyncRes.ResType.AREA);
    }

    public ZyzSyncLog sync(long resourceId, long syncBillId, String saveType) {
        ZyzResource resource = resourceService.getById(resourceId);
        //处于待同步,且审核同步才需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(resource.getSync()) && (StringUtils.isEmpty(saveType) || !SyncRes.SaveType.MODIFY.equals(saveType))) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }
        if (!ZyzCons.RESOURCE_STATUS_AUDIT_SUCCESS.equals(resource.getStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此资源审核未通过，无法同步");
        }
        SysOrg org = orgService.getByCode(resource.getPublishOrgCode());
        SyncRes syncRes = new SyncRes();
        syncRes.setUniCode(String.valueOf(resource.getId()));
        boolean teamPublish = resource.getTeamPublish();
        syncRes.setPubType(teamPublish ? SyncRes.PubType.TEAM : SyncRes.PubType.BASE);
        String pubId;
        if (teamPublish) {
            ZyzTeam team = teamService.getById(resource.getTeamId());
            if (!Cons.PlatformSyncState.SUCCESS.equals(team.getIsSync()) || StringUtils.isEmpty(team.getSyncId())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "由于该资源发布团队未同步成功，所以无法同步该需求");
            }
            pubId = team.getSyncId();
        } else {
            pubId = org.getSyncId();
            if (StringUtils.isEmpty(pubId)) {
                pubId = org.getDepId();
                if (StringUtils.isEmpty(org.getDepId())) {
                    throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到该资源发布组织的deptId，无法同步该资源");
                }
                ZyzSyncDep dep = depService.getByDepId(org.getDepId());
                if (ObjectUtil.isEmpty(dep)) {
                    throw new BusinessException(ResultCode.BAD_REQUEST, "未获取到该资源发布组织的dept，无法同步该资源");
                }
                syncRes.setPubType(SyncRes.PubType.DEP);
            }
        }
        syncRes.setPubId(pubId);
        syncRes.setResType(RES_TYPE_MAP.get(resource.getType()));
        syncRes.setResName(resource.getName());
        syncRes.setContent(resource.getDetail());
        syncRes.setResDate(resource.getStartTime().toString() + "至" + resource.getEndTime().toString());
        syncRes.setContact(resource.getContactPerson());
        syncRes.setContactNo(resource.getContactPhone());
        syncRes.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(resource.getPicture())));
        syncRes.setAddress(resource.getAddress());
        syncRes.setTxMapLan(resource.getLongitude() != null ? resource.getLongitude().toString() : "");
        syncRes.setTxMapLat(resource.getLatitude() != null ? resource.getLatitude().toString() : "");
        syncRes.setSaveType(StringUtils.isEmpty(saveType) ? SyncRes.SaveType.ADD : saveType);
        SyncResResult result = ApiWebService.syncRes(syncRes);
        String resultCode = result.getCode();
        String msg = result.getMsg();

        resource.setSyncTime(LocalDateTime.now());
        resource.setSyncRemark(msg);
        //成功
        if ("同步成功".equals(msg)) {
            resource.setSyncId(result.getResId());
            resource.setSync(Cons.PlatformSyncState.SUCCESS);
        }
        else if (msg.contains("唯一号的资源已存在") || msg.contains("已存在")) {
            if (StringUtils.isNotEmpty(result.getResId())) {
                resource.setSyncId(result.getResId());
            }
            resource.setSync(Cons.PlatformSyncState.SUCCESS);
            resource.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            resource.setSync(Cons.PlatformSyncState.FAILURE);
        }

        //启用单独事务处理日志
        ZyzSyncLog log = logService
                .buildSyncLog(Cons.PlatformSyncBiz.RESOURCE_SYNC, resultCode, msg
                        , syncBillId, resourceId, resource.getName()
                        , JSONUtil.toJsonStr(syncRes), JSONUtil.toJsonStr(result));

        //回写业务
        resourceService.updateById(resource);
        return log;
    }
}
