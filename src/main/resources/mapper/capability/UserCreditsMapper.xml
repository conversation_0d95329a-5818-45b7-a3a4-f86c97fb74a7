<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fykj.scaffold.capability.mapper.UserCreditsMapper">
    <select id="getUserCredits" resultType="com.fykj.scaffold.capability.domain.dto.UserCreditsDetailDTO">
        SELECT
            cuc.user_id AS userId,
            cuc.credits AS credits,
            su.`name` AS userName
        FROM cu_user_credits cuc
        LEFT JOIN sys_user su ON cuc.user_id = su.id
        WHERE cuc.user_id = #{userId}
    </select>
</mapper>
