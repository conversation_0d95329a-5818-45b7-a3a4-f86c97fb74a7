package com.fykj.scaffold.mall.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/15
 */
@Data
@ApiModel("机器积分兑换信息")
@NoArgsConstructor
@AllArgsConstructor
public class MachineResultDto {
    private boolean success = true;
    private boolean result;
    private Map<String, String> data = new HashMap<>();

    public MachineResultDto(boolean result, String msg) {
        this.result = result;
        data.put("message", msg);
    }
}
