package com.fykj.scaffold.security.login.controller;

import com.fykj.scaffold.security.login.util.ZWTDockingUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author：yangxu
 * @Date：2024/12/10 17:57
 * @Description：
 */
@RestController
@RequestMapping("/zwt-docking")
@Api(tags = "对接-政务通")
public class ZWTDockingController {

    @Autowired
    private ZWTDockingUtils zwtDockingUtils;

    @ApiOperation("重定向政务通oauth2登录")
    @GetMapping(value = "/redirectZWTGovLogin")
    public void redirectZWTGovLogin(HttpServletResponse response) throws Exception {
        String oauth2AuthorizeUrl = zwtDockingUtils.getRedirectLoginUrl();
        response.sendRedirect(oauth2AuthorizeUrl);
    }

    @ApiOperation("登录获取token")
    @GetMapping(value = "/govLogin")
    public Result govLogin(@RequestParam String code, @RequestParam String verifyCode) {
        return new JsonResult<>(zwtDockingUtils.govLogin(code, verifyCode));
    }
}
