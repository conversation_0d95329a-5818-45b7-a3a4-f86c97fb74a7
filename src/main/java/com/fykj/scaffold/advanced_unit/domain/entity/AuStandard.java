package com.fykj.scaffold.advanced_unit.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文明单位-指标标准-测评版本
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("au_standard")
public class AuStandard extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 所属年份
     */
    @TableField("year")
    @ApiModelProperty(value = "所属年份")
    private Integer year;

    /**
     * 展示状态
     */
    @TableField("status")
    @ApiModelProperty(value = "展示状态")
    private Boolean status;

    /**
     * 测评版本名称
     */
    @TableField("name")
    @ApiModelProperty(value = "测评版本名称")
    private String name;

    /**
     * 测评说明
     */
    @TableField("description")
    @ApiModelProperty(value = "测评说明")
    private String description;

    /**
     * 爬取ID
     */
    @TableField("spider_id")
    @ApiModelProperty(value = "原始ID")
    private String spiderId;

}
