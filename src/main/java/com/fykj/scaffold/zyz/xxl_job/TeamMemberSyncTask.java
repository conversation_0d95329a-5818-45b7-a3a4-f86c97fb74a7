package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 团队加入同步补偿
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TeamMemberSyncTask {

    @Autowired
    private IZyzVolunteerTeamService vtService;

    @XxlJob("Team_Member_Sync_Handler")
    public void teamMemberSyncJobHandler() {
        XxlJobHelper.log("Team_Member_Sync_Handler-Job, START!");

        // 原有同步逻辑
        List<ZyzVolunteerTeam> vtList = vtService.getNeedSync();
        if (CollectionUtil.isNotEmpty(vtList)) {
            try {
                vtService.syncBatch(vtList);
            } catch (Exception e) {
                log.error("志愿者加入团队批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }

        XxlJobHelper.log("Team_Member_Handler-Job, END!");
    }
}
