package com.fykj.scaffold.civilization_cultivation.front;

import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationShowParams;
import com.fykj.scaffold.civilization_cultivation.service.ICivilizationCultivationShowService;
import com.fykj.scaffold.security.business.service.IDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import static com.fykj.scaffold.civilization_cultivation.cons.CivilizationCultivationCons.CC_TYPE_DICT;

@RestController
@RequestMapping("/api/manage/civilization_cultivation")
@Api(tags = "文明培育展示--无需登录")
public class CivilizationCultivationManageApi {

    @Autowired
    private IDictService dictService;

    @Autowired
    private ICivilizationCultivationShowService ccShowService;

    @ApiOperation("获取文明培育各类型")
    @GetMapping({"/getTypes"})
    public Result getTypes() {
        return new JsonResult<>(dictService.findByParentCode(CC_TYPE_DICT));
    }

    @ApiOperation("获取文明培育各类型的可筛查年份")
    @GetMapping({"/getYears"})
    public Result getYears(@RequestParam String type) {
        return new JsonResult<>(ccShowService.getYears(type));
    }

    @ApiOperation("文明培育分页展示")
    @PostMapping({"/pageShow"})
    public Result pageShow(@RequestBody CivilizationCultivationShowParams params) {
        return new JsonResult<>(ccShowService.pageShow(params));
    }

    @ApiOperation("文明培育分页展示(正式环境)")
    @PostMapping({"/pageShowProd"})
    public Result pageShowProd(@RequestBody CivilizationCultivationShowParams params) {
        return new JsonResult<>(ccShowService.pageShowProd(params));
    }

    @ApiOperation("文明培育详情")
    @GetMapping({"/getDetail"})
    public Result getDetail(@RequestParam Long id) {
        return new JsonResult<>(ccShowService.getDetail(id));
    }

    @ApiOperation("文明培育列表-首页轮播展示")
    @PostMapping({"/homeBannerShow"})
    public Result homeBannerShow(@RequestBody CivilizationCultivationShowParams params) {
        return new JsonResult<>(ccShowService.homeBannerShow(params));
    }

    @ApiOperation("文明培育列表-首页轮播展示(正式环境)")
    @PostMapping({"/homeBannerShowProd"})
    public Result homeBannerShowProd() {
        return new JsonResult<>(ccShowService.homeBannerShowProd());
    }
}
