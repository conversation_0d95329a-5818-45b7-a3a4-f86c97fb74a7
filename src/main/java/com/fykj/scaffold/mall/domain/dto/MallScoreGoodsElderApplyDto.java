package com.fykj.scaffold.mall.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@ApiModel("适老化报名兑换信息")
@NoArgsConstructor
@AllArgsConstructor
public class MallScoreGoodsElderApplyDto {

    @ApiModelProperty(value = "报名id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;


    @ApiModelProperty(value = "志愿者id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;


    @ApiModelProperty(value = "志愿者姓名")
    private String volunteerName;


    @ApiModelProperty(value = "志愿者户籍所在地")
    private String volunteerAddress;

    @ApiModelProperty(value = "志愿者身份证号")
    private String volunteerCertificateId;


    @ApiModelProperty(value = "志愿者证件类型（身份证还是护照等等)")
    private String volunteerCertificateType;

    @ApiModelProperty(value = "志愿者联系方式")
    private String volunteerPhone;

    @ApiModelProperty(value = "老年人姓名")
    private String elderName;


    @ApiModelProperty(value = "老年人联系方式")
    private String elderPhone;


    @ApiModelProperty(value = "老年人户籍所在地")
    private String elderAddress;


    @ApiModelProperty(value = "老年人证件号")
    private String elderCertificateId;


    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "兑换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exchangeTime;



    @ApiModelProperty(value = "兑换积分（锁定积分）")
    private BigDecimal exchangePoint;


    @ApiModelProperty(value = "服务时长")
    private BigDecimal serviceLong;


    @ApiModelProperty(value = "兑换之前的积分")
    private BigDecimal exchangeBeforeScore;


    @ApiModelProperty(value = "兑换之后的积分")
    private BigDecimal exchangeAfterScore;

}
