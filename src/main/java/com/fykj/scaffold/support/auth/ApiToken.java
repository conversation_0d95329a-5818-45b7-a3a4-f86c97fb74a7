package com.fykj.scaffold.support.auth;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * API Token验证注解
 * 用于标记需要进行token验证的接口
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiToken {
    /**
     * 是否启用token验证
     */
    boolean required() default true;
} 