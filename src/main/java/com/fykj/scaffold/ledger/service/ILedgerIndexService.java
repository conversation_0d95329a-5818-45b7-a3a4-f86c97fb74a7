package com.fykj.scaffold.ledger.service;

import com.fykj.scaffold.ledger.domain.entity.LedgerIndex;
import fykj.microservice.core.base.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 台账指标-服务类
 *
 * @date 2025-05-26
 */
public interface ILedgerIndexService extends IBaseService<LedgerIndex> {

    /**
     * 获取树
     *
     * @return 树
     */
    List<LedgerIndex> tree();


    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    LedgerIndex get(Long id);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    boolean deleteById(Long id);

    /**
     * 获取用户指标树
     *
     * @return
     */
    List<LedgerIndex> getUserIndexTree();

    /**
     * 名称对应idMap
     *
     * @return
     */
    Map<String, Long> getIndexNameMap();
}

