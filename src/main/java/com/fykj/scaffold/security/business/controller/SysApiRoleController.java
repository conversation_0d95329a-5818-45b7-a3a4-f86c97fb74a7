package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.dto.ActionOrRoleApiPathDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiRole;
import com.fykj.scaffold.security.business.domain.params.SysApiRoleParams;
import com.fykj.scaffold.security.business.service.ISysApiRoleService;
import com.fykj.scaffold.security.oauth2.filter.Oauth2FilterInvocationSecurityMetadataSource;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 接口-角色映射表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@RestController
@RequestMapping("/admin/sys_api_role")
public class SysApiRoleController extends BaseController<ISysApiRoleService, SysApiRole, SysApiRoleParams> {

    @GetMapping("/bindApi")
    @ApiOperation("获取接口绑定列表")
    public JsonResult<List<String>> roleApi(@RequestParam Long bindId) {
        List<String> apiList = baseService.roleApi(bindId);
        return new JsonResult<>(apiList);
    }

    @Autowired
    Oauth2FilterInvocationSecurityMetadataSource oauth2FilterInvocationSecurityMetadataSource;

    @ApiOperation("保存方法")
    @PostMapping({"/savePath"})
    public Result savePath(@RequestBody ActionOrRoleApiPathDto dto) {
        boolean result = this.baseService.savePath(dto);
        if (result) {
            oauth2FilterInvocationSecurityMetadataSource.init();
            return new Result();
        }
        return new Result(ResultCode.FAIL);
    }
}
