package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShowLog;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityTimePeriodShowLogParams;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodShowLogService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 活动公示操作记录表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-19
 */
@RestController
@RequestMapping("/admin/zyz/activity/time/period/show/log")
@Api(tags = "活动公示操作记录表接口")
public class ZyzActivityTimePeriodShowLogController extends BaseController<IZyzActivityTimePeriodShowLogService, ZyzActivityTimePeriodShowLog, ZyzActivityTimePeriodShowLogParams> {

    @Autowired
    private IDictService dictService;

    @ApiOperation("查询活动公示日志")
    @GetMapping({"/getLogsByShowId"})
    public JsonResult<List<ZyzActivityTimePeriodShowLog>> getLogsByShowId(Long showId) {
        List<ZyzActivityTimePeriodShowLog> result = this.baseService.getLogsByShowId(showId);
        result.forEach(x -> x.setOperateTypeText(dictService.getNameByCode(x.getOperateType())));
        return new JsonResult<>(result);
    }
}
