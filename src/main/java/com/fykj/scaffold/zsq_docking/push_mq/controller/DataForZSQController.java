package com.fykj.scaffold.zsq_docking.push_mq.controller;

import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.zsq_docking.push_mq.event.ActivityDockingEvent;
import com.fykj.scaffold.zsq_docking.push_mq.event.NewsDockingEvent;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.TOPIC_ACTIVITY_DOCKING;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.TOPIC_NEWS_DOCKING;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/api/zsq/data")
@Api(tags = "知社区新闻接口接收")
@Slf4j
public class DataForZSQController {

    @Autowired
    private INewsService newsService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Autowired
    private ISysOrgService sysOrgService;

    @ApiOperation("获取新闻详情")
    @GetMapping({"/getNewsDetail"})
    public Result getNewsDetail(@RequestParam Long id) {
        return new JsonResult<>(newsService.getNewsDetail(id));
    }

    @ApiOperation("新闻操作通知知社区同步")
    @GetMapping({"/noticeZSQSync"})
    public Result noticeZSQSync(@RequestParam(required = false) Long id, @RequestParam(required = false) String operation, @RequestParam(required = false) String zwtUserId, @RequestParam(required = false) Boolean onShelf) {
        mqTemplate.send(TOPIC_NEWS_DOCKING, MessageBuilder.withPayload(NewsDockingEvent.builder().newsId(id).operation(operation).zwtUserId(zwtUserId).onShelf(onShelf).build()).build());
        return Result.get(true);
    }


    @ApiOperation("获取活动详情")
    @GetMapping({"/getActivityDetail"})
    public Result getActivityDetail(@RequestParam Long id) {
        log.info("获取活动详情id:{}",id);
        log.error("获取活动详情id:{}",id);
        return new JsonResult<>(activityService.getActivityDetail(id));
    }

    @ApiOperation("活动操作通知知社区同步")
    @GetMapping({"/noticeZSQActivitySync"})
    public Result noticeZSQActivitySync(@RequestParam(required = false) Long id, @RequestParam(required = false) String operation, @RequestParam(required = false) String zwtUserId) {
        mqTemplate.send(TOPIC_ACTIVITY_DOCKING, MessageBuilder.withPayload(ActivityDockingEvent.builder().actId(id).operation(operation).zwtUserId(zwtUserId).build()).build());
        return Result.get(true);
    }

    @ApiOperation("获取组织列表")
    @GetMapping({"/getSysOrgList"})
    public Result getSysOrgList() {
        return new JsonResult<>(sysOrgService.list());
    }

}
