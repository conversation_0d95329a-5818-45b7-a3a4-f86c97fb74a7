package com.fykj.scaffold.zyz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.domain.entity.OssConfig;
import com.fykj.scaffold.security.business.domain.entity.SysOss;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.IOssConfigService;
import com.fykj.scaffold.security.business.service.ISysOssService;
import com.fykj.scaffold.support.utils.PdfUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.AwardImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzCertificate;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerCertificate;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerCertificateParams;
import com.fykj.scaffold.zyz.mapper.ZyzVolunteerCertificateMapper;
import com.fykj.scaffold.zyz.service.IZyzCertificateService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerCertificateService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.LocalDateTimeUtil;
import utils.StringUtil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.core.toolkit.StringPool.NEWLINE;

/**
 * 志愿者证书关联表
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ZyzVolunteerCertificateServiceImpl extends BaseServiceImpl<ZyzVolunteerCertificateMapper, ZyzVolunteerCertificate> implements IZyzVolunteerCertificateService {

    private static final String MSG = "证书生成异常";
    private static final Integer SERVICE_LONG_1500 = 1500;
    private static final Integer SERVICE_LONG_1000 = 1000;
    private static final Integer SERVICE_LONG_600 = 600;
    private static final Integer SERVICE_LONG_300 = 300;
    private static final Integer SERVICE_LONG_100 = 100;
    @Autowired
    private IZyzVolunteerService volunteerService;
    @Autowired
    private IZyzCertificateService zyzCertificateService;
    @Autowired
    private IDictService dictService;
    @Autowired
    private ISysOssService sysOssService;
    @Autowired
    private IOssConfigService ossConfigService;

    @Override
    public IPage<ZyzVolunteerCertificate> getPages(ZyzVolunteerCertificateParams params) {
        return baseMapper.getPages(params.getPage(), params);
    }

    @Override
    public List<ZyzVolunteerCertificate> getAwardedCertificateList(Long volunteerId) {
        ZyzVolunteer volunteer = volunteerService.getById(volunteerId);
        if(Objects.isNull(volunteer)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "该志愿者在本系统中不存在");
        }
        return getAwardedCertificateList(volunteer.getPhone(), volunteer);
    }

    @Override
    public List<ZyzVolunteerCertificate> getAwardedCertificateList(String phone) {
       return getAwardedCertificateList(phone, null);
    }

    public List<ZyzVolunteerCertificate> getAwardedCertificateList(String phone, ZyzVolunteer volunteer) {
        //获取颁发的证书
        List<ZyzVolunteerCertificate> list = baseMapper.getAwardList(phone);
        if(Objects.isNull(volunteer)) {
            volunteer = volunteerService.lambdaQuery()
                    .eq(ZyzVolunteer::getPhone, phone)
                    .eq(ZyzVolunteer::getWriteOff, false)
                    .one();
        }
        //获取默认的证书（每个人都会有的）
        List<ZyzCertificate> defaultCerList = zyzCertificateService.lambdaQuery()
                .eq(ZyzCertificate::getDefaultCer, true)
                .orderByAsc(ZyzCertificate::getSequence)
                .list();
        if (volunteer != null) {
            for (ZyzCertificate zyzCertificate : defaultCerList) {
                ZyzVolunteerCertificate zyzCer = lambdaQuery()
                        .eq(ZyzVolunteerCertificate::getVolunteerId, volunteer.getId())
                        .eq(ZyzVolunteerCertificate::getAwardCertificateId, zyzCertificate.getId())
                        .one();
                // 如果默认证书还没有记录的话，就生成一条，并且记录下图片和pdf
                if (zyzCer == null) {
                    // 添加记录
                    zyzCer = newZyzVolunteerCertificate(zyzCertificate, volunteer);
                    zyzCer.setSequence(zyzCertificate.getSequence());
                    zyzCer.setDefaultCer(zyzCertificate.getDefaultCer());
                    zyzCer.setRealTimeCer(zyzCertificate.getRealTimeCer());
                    super.save(zyzCer);
                    list.add(zyzCer);
                }
            }
        }
        list.sort(Comparator.comparing(ZyzVolunteerCertificate::getSequence));
        // 根据前端的展示要求，一把全部生成出来
        for (ZyzVolunteerCertificate volunteerCertificate : list) {
            boolean needGenerate = volunteerCertificate.getRealTimeCer() || StringUtil.isEmpty(volunteerCertificate.getImgUrl()) || StringUtil.isEmpty(volunteerCertificate.getPdfUrl());
            //实时证书的话需要实时数据，所以重新生成一下
            if (needGenerate) {
                ZyzVolunteerCertificate newItem = this.generateCertificatePdfById(volunteerCertificate.getId());
                volunteerCertificate.setImgUrl(newItem.getImgUrl());
                volunteerCertificate.setPdfUrl(newItem.getPdfUrl());
            }
        }
        return list;
    }

    private ZyzVolunteerCertificate newZyzVolunteerCertificate(ZyzCertificate zyzCertificate, ZyzVolunteer volunteer) {
        ZyzVolunteerCertificate zyzCer;
        zyzCer = new ZyzVolunteerCertificate();
        zyzCer.setVolunteerId(volunteer.getId());
        zyzCer.setVolunteerName(volunteer.getName());
        zyzCer.setCertificateType(volunteer.getCertificateType());
        zyzCer.setCertificateId(volunteer.getCertificateId());
        zyzCer.setPhone(volunteer.getPhone());
        zyzCer.setAwardCertificateId(zyzCertificate.getId());
        zyzCer.setAwardCertificateName(zyzCertificate.getName());
        zyzCer.setReceiveDate(LocalDate.now());
        return zyzCer;
    }

    @Override
    public boolean checkCertificateUsed(long id) {
        return lambdaQuery().eq(ZyzVolunteerCertificate::getAwardCertificateId, id).count() > 0;
    }

    @Override
    public List<AwardImportDto> dataImport(MultipartFile excel, Long awardCertificateId) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请上传文件！");
        }
        ZyzCertificate cer = zyzCertificateService.getById(awardCertificateId);
        if (cer == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "证书不存在！");
        }
        // 已下架的证书不能颁发
        if (Boolean.FALSE.equals(cer.getStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "证书已禁用！");
        }
        List<AwardImportDto> dataList = ExcelUtil.readExcel(excel, AwardImportDto.class, 0, 1);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        if ("示例数据".equals(dataList.get(0).getName())) {
            dataList.remove(0);
        }
        if (dataList.size() > 10000) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请控制每批导入数据量在10000条内！");
        }

        List<AwardImportDto> cidNotEmpty = dataList.stream().filter(it -> StringUtil.isNotEmpty(it.getCertificateId())).collect(Collectors.toList());
        Set<String> cidSet = cidNotEmpty.stream().map(AwardImportDto::getCertificateId).collect(Collectors.toSet());
        List<AwardImportDto> phoneNotEmpty = dataList.stream().filter(it -> StringUtil.isNotEmpty(it.getPhone())).collect(Collectors.toList());
        Set<String> phoneSet = phoneNotEmpty.stream().map(AwardImportDto::getPhone).collect(Collectors.toSet());
        if (cidNotEmpty.size() > cidSet.size() || phoneNotEmpty.size() > phoneSet.size()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "Excel中存在志愿者手机号或身份证号重复的数据，请进行去重处理后导入！");
        }

        return dataImportHandle(dataList, cer);
    }

    private List<AwardImportDto> dataImportHandle(List<AwardImportDto> dataList, ZyzCertificate cer) {
        List<AwardImportDto> importFailRecords = new ArrayList<>();
        List<ZyzVolunteerCertificate> awardList = new ArrayList<>();
        List<ZyzVolunteer> volunteersExist = volunteerService.getVolunteersExist();
        List<String> dbAwardlist = lambdaQuery().eq(ZyzVolunteerCertificate::getAwardCertificateId, cer.getId()).list().stream().map(ZyzVolunteerCertificate::getPhone).collect(Collectors.toList());
        Map<String, ZyzVolunteer> volunteerMap = volunteersExist.stream().collect(Collectors.toMap(ZyzVolunteer::getPhone, Function.identity(), (k1, k2) -> k2));
        Map<String, Dict> dictMap = dictService.findByParentCode(ZyzCons.CERTIFICATE_TYPE_DC).stream().collect(Collectors.toMap(Dict::getName, Function.identity(), (key1, key2) -> key2));
        dataList.forEach(it -> {
            List<String> errorReasons = new ArrayList<>();
            // 字段必填校验
            validateNecessary(it, errorReasons);
            // 手机号证件号存在校验
            validateNotExist(it, volunteersExist, volunteerMap, errorReasons);
            // 存在校验
            validateExist(it, dbAwardlist, errorReasons);
            // 字典值存在校验
            validateDictExist(it, dictMap, errorReasons);
            if (CollUtil.isEmpty(errorReasons)) {
                ZyzVolunteerCertificate award;
                try {
                    award = generateCertificate(it, volunteersExist, cer);
                    if (award != null) {
                        awardList.add(award);
                    }
                } catch (IOException e) {
                    errorReasons.add(MSG);
                }
            }
            if (CollUtil.isNotEmpty(errorReasons)) {
                it.setFailReason(String.join(NEWLINE, errorReasons));
                importFailRecords.add(it);
            }
        });
        if (CollUtil.isNotEmpty(awardList)) {
            super.saveBatch(awardList);
        }
        return importFailRecords;
    }

    private void validateNecessary(AwardImportDto dto, List<String> errorReasons) {
        if (StringUtil.isEmpty(dto.getName())) {
            errorReasons.add("志愿者姓名须必填！");
        }
        if (StringUtil.isEmpty(dto.getPhone()) && StringUtil.isEmpty(dto.getCertificateId())) {
            errorReasons.add("志愿者手机号码或者证件号须必填！");
        }


    }

    private void validateNotExist(AwardImportDto dto, List<ZyzVolunteer> volunteersExist, Map<String, ZyzVolunteer> volunteerMap, List<String> errorReasons) {
        String phone = dto.getPhone();
        ZyzVolunteer vo = volunteerMap.get(phone);
        if (vo == null) {
            vo = StringUtil.isEmpty(dto.getCertificateId()) ? null : volunteersExist.stream().filter(it -> dto.getCertificateId().equals(it.getCertificateId())).findAny().orElse(null);
            if (vo == null) {
                errorReasons.add("志愿者手机号码/身份证系统不存在！");
                return;
            }
        }
        if (!vo.getName().equals(dto.getName())) {
            errorReasons.add("志愿者手机号码或者身份证对应的姓名与系统志愿者不匹配！");
        }

    }

    private void validateExist(AwardImportDto dto, List<String> dbAwardlist, List<String> errorReasons) {
        if (dbAwardlist.contains(dto.getPhone())) {
            errorReasons.add("志愿者证书已颁发！");
        }

    }

    private void validateDictExist(AwardImportDto dto, Map<String, Dict> dictMap, List<String> errorReasons) {
        if (!validateDictValue(dto.getCertificateType(), dictMap)) {
            errorReasons.add("志愿者证件类型不在系统字典值中！");
        }
    }

    private boolean validateDictValue(String excelVal, Map<String, Dict> dictMap) {
        if (StringUtil.isEmpty(excelVal)) {
            return true;
        }
        Dict dict = dictMap.get(excelVal);
        return !ObjectUtils.isEmpty(dict);
    }


    @Override
    public boolean generateCertificatePdfByIds(List<Long> ids) {
        ids.forEach(this::generateCertificatePdfById);
        return true;
    }

    @Override
    public ZyzVolunteerCertificate generateCertificatePdfById(Long id) {
        ZyzVolunteerCertificate zyzCer = getById(id);
        ZyzVolunteer volunteer = volunteerService.getVolunteerById(zyzCer.getVolunteerId());
        ZyzCertificate cer = zyzCertificateService.getById(zyzCer.getAwardCertificateId());
        if (CollUtil.isEmpty(cer.getAttachmentList()) || StringUtil.isEmpty(cer.getAttachmentList().get(0).getPath())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "证书pdf没有配置！");
        }
        try {
            String star = getStar(volunteer.getServiceLong());
            SysOss file = generatePdf(volunteer.getName(), volunteer.getCertificateId(), volunteer.getServiceLong().toString(), star, (cer.getAttachmentList().get(0).getPath()));
            if (file != null) {
                zyzCer.setPdfUrl(file.getPath());
                //把pdf转成图片格式
                String imgUrl = convertPdf2Img(file.getPath(),Float.valueOf(cer.getDpi()));
                zyzCer.setImgUrl(imgUrl);
                super.updateById(zyzCer);
            } else {
                throw new BusinessException(ResultCode.BAD_REQUEST, MSG);
            }
            return zyzCer;
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new BusinessException(ResultCode.BAD_REQUEST, MSG, e);
        }
    }

    private String getStar(BigDecimal serviceLong) {
        if (serviceLong.compareTo(BigDecimal.valueOf(SERVICE_LONG_100)) < 0) {
            return "";
        }
        //一星级 100
        if (serviceLong.compareTo(BigDecimal.valueOf(SERVICE_LONG_300)) < 0) {
            return "一星级志愿者";
        }
        //二星级 300
        if (serviceLong.compareTo(BigDecimal.valueOf(SERVICE_LONG_600)) < 0) {
            return "二星级志愿者";
        }
        //三星级 600
        if (serviceLong.compareTo(BigDecimal.valueOf(SERVICE_LONG_1000)) < 0) {
            return "三星级志愿者";
        }
        //四星级 1000
        if (serviceLong.compareTo(BigDecimal.valueOf(SERVICE_LONG_1500)) < 0) {
            return "四星级志愿者";
        }
        //五星级 1500
        return "五星级志愿者";
    }

    private ZyzVolunteerCertificate generateCertificate(AwardImportDto dto, List<ZyzVolunteer> volunteersExist, ZyzCertificate cer) throws IOException {
        //获取志愿者信息
        ZyzVolunteer volunteer = StringUtil.isEmpty(dto.getPhone()) ? null : volunteersExist.stream().filter(it -> dto.getPhone().equals(it.getPhone())).findAny().orElse(null);
        if (volunteer == null) {
            volunteer = StringUtil.isEmpty(dto.getCertificateId()) ? null : volunteersExist.stream().filter(it -> dto.getCertificateId().equals(it.getCertificateId())).findAny().orElse(null);
        }
        if (volunteer != null) {
            ZyzVolunteerCertificate zyzCer = lambdaQuery().eq(ZyzVolunteerCertificate::getVolunteerId, volunteer.getId()).eq(ZyzVolunteerCertificate::getAwardCertificateId, cer.getId()).one();
            if (zyzCer == null) {
                // 添加记录
                zyzCer = newZyzVolunteerCertificate(cer, volunteer);
            }
            return zyzCer;
        }
        return null;
    }

    private SysOss generatePdf(String volunteerName, String idCard, String serviceLong, String star, String pdfUrl) throws IOException {
        Map<String, Object> pdfMap = new HashMap<>();
        pdfMap.put("volunteerName", volunteerName);
        pdfMap.put("idCard", idCard);
        pdfMap.put("serviceLong", serviceLong);
        pdfMap.put("star", star);
        pdfMap.put("sysDate", LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyy年MM月dd日"));
        OssConfig ossConfig = ossConfigService.getConfig();
        String url = pdfUrl.replace(ossConfig.getUrl(), ossConfig.getStorageLocation());
        try (InputStream inputStream = PdfUtil.generatePdf(pdfMap, url)) {
            if (inputStream != null) {
                MultipartFile multipartFile = convert(inputStream);
                return sysOssService.upload(multipartFile, "LocalServer", false, null, null);
            }
        }
        return null;
    }

    private MultipartFile convert(InputStream inputStream) throws IOException {
        return new MockMultipartFile("certificate.pdf", "certificate.pdf", "application/pdf", inputStream);
    }

    private String convertPdf2Img(String pdfUrl, Float dpi) {
        try {
            OssConfig ossConfig = ossConfigService.getConfig();
            // 加载PDF文档
            String url = pdfUrl.replace(ossConfig.getUrl(), ossConfig.getStorageLocation());
            File pdfFile = new File(url);
            String folder = url.substring(0, url.lastIndexOf('/') + 1);
            String imgPath;
            try (PDDocument document = PDDocument.load(pdfFile, MemoryUsageSetting.setupTempFileOnly())) {

                // 创建PDF渲染器
                PDFRenderer pdfRenderer = new PDFRenderer(document);
                // 渲染第一页为BufferedImage
                BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(0, (dpi==null? 36:dpi), ImageType.RGB);

                // 将BufferedImage保存为图片文件
                imgPath = folder + IdUtil.getSnowflake().nextId() + ".png";
                File outputFile = new File(imgPath);
                ImageIO.write(bufferedImage, "PNG", outputFile);

                // 关闭PDF文档
                document.close();
            }
            return imgPath.replace(ossConfig.getStorageLocation(), ossConfig.getUrl());
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return "";
    }

    @Override
    public Map<String, String> imagePreview(long id) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        Map<String, String> returnMap = new HashMap<>();
        ZyzCertificate cer = zyzCertificateService.getById(id);
        if (CollUtil.isEmpty(cer.getAttachmentList()) || StringUtil.isEmpty(cer.getAttachmentList().get(0).getPath())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "证书pdf没有配置！");
        }
        try {
            String star = "一星级志愿者";
            SysOss file = generatePdf("韩梅梅", "320123456789106666", "100", star, cer.getAttachmentList().get(0).getPath());
            if (file != null) {
                // 把pdf转成图片格式
                returnMap.put("imagePath", convertPdf2Img(file.getPath(),Float.valueOf(cer.getDpi())));
                long endTime = System.currentTimeMillis(); // 记录结束时间
                returnMap.put("executionTime", String.valueOf(endTime - startTime)); // 返回执行时间，移除末尾空格
                return returnMap; // 将return语句移动到if语句内
            } else {
                throw new BusinessException(ResultCode.BAD_REQUEST, MSG);
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new BusinessException(ResultCode.BAD_REQUEST, MSG, e);
        }
    }

    @Override
    public ZyzVolunteerCertificate awardCertificateToVolunteer(Long volunteerId, Long awardCertificateId) {
        // 校验志愿者是否存在
        ZyzVolunteer volunteer = volunteerService.getVolunteerById(volunteerId);
        if (volunteer == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者不存在！");
        }

        // 校验证书是否存在
        ZyzCertificate certificate = zyzCertificateService.getById(awardCertificateId);
        if (certificate == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "证书不存在！");
        }

        // 检查是否已经颁发过该证书
        ZyzVolunteerCertificate existingCertificate = lambdaQuery()
                .eq(ZyzVolunteerCertificate::getVolunteerId, volunteerId)
                .eq(ZyzVolunteerCertificate::getAwardCertificateId, awardCertificateId)
                .one();

        if (existingCertificate != null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "该志愿者已经获得此证书！");
        }

        // 创建证书记录
        ZyzVolunteerCertificate volunteerCertificate = newZyzVolunteerCertificate(certificate, volunteer);
        volunteerCertificate.setSequence(certificate.getSequence());
        volunteerCertificate.setDefaultCer(certificate.getDefaultCer());
        volunteerCertificate.setRealTimeCer(certificate.getRealTimeCer());

        // 保存证书记录
        super.save(volunteerCertificate);

        return volunteerCertificate;
    }


}
