package com.fykj.scaffold.security.business.mapper;

import com.fykj.scaffold.security.business.domain.entity.SysAction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统资源-功能操作
 * <p>
 * Mapper 接口
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
public interface SysActionMapper extends BaseMapper<SysAction> {

    /**
     * 查询用户拥有的按钮
     *
     * @param userId
     * @return
     */
    List<SysAction> findActionByUserIdAndRoleIds(@Param("userId") long userId, @Param("roleIds") List<Long> roleIds);
}
