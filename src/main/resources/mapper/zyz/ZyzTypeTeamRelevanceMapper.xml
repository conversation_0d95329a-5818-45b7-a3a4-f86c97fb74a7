<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzTypeTeamRelevanceMapper">

    <select id="getPagesByType" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeam">
        SELECT
            t.*,
            aa.livelyCount as livelyCountThisYear,
            aa.serviceLongThisYear as serviceLongThisYear,
            so.name as orgName
        FROM zyz_type_team_relevance r
                 INNER JOIN  zyz_team t on r.team_id = t.id and t.is_deleted = FALSE
                 LEFT JOIN sys_org so on t.org_code = so.code and so.is_deleted = FALSE
                 LEFT JOIN (
            SELECT
                vt.team_id,
                sum(IF( v.is_lively = TRUE, 1, 0 )) AS livelyCount,
                SUM(service_long_this_year) as serviceLongThisYear
            FROM
                zyz_volunteer_team vt
                    LEFT JOIN zyz_volunteer v ON v.id = vt.volunteer_id
            GROUP BY vt.team_id
        ) aa ON aa.team_id = t.id
        WHERE
            r.is_deleted = FALSE
        <if test="params.typeId != null and params.typeId != ''">
            and r.type_id = #{params.typeId}
        </if>
    </select>
    <select id="getListAll" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTypeTeamRelevance">
        select r.*,t.name as typeName  from zyz_type_team_relevance r left join zyz_team_type t on t.id=r.type_id
        where r.is_deleted = FALSE and t.is_deleted = FALSE <if test="teamId!=null">and r.team_id = #{teamId}</if>
    </select>
</mapper>