package com.fykj.scaffold.zyz.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.MiniActivityTimePeriodDto;
import com.fykj.scaffold.zyz.domain.dto.MyActivityApiDto;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityTimePeriodParams;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/mini/activity-time-period")
@Api(tags = "微信小程序活动时间段接口--需要登录")
public class MiniActivityTimePeriodController {
    @Autowired
    private IZyzActivityTimePeriodService timePeriodService;

    @ApiOperation("时间段分页")
    @PostMapping({"/timePeriodPages"})
    public JsonResult<IPage<MiniActivityTimePeriodDto>> timePeriodPages(@RequestBody ZyzActivityTimePeriodParams params) {
        params.setLogin(true);
        IPage<MiniActivityTimePeriodDto> result = timePeriodService.miniTimePeriodPage(params);
        return new JsonResult<>(result);
    }
}
