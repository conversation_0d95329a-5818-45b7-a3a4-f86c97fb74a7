package com.fykj.scaffold.mall.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("自助服务机器查询参数")
public class MallSelfMachineParams extends BaseParams {
    @MatchType(value = QueryType.LIKE, fieldName = "machine_no")
    private String machineNo;

    @MatchType(value = QueryType.LIKE, fieldName = "link_phone")
    private String linkPhone;

    @MatchType(value = QueryType.EQ, fieldName = "status")
    private String status;

    @MatchType(value = QueryType.EQ, fieldName = "org_code")
    private String orgCode;
    @ApiModelProperty(value = "经度/必传")
    private BigDecimal longitude;
    @ApiModelProperty(value = "纬度/必传")
    private BigDecimal latitude;
}
