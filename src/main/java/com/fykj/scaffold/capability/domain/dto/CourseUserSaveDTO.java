package com.fykj.scaffold.capability.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel("报名dto")
public class CourseUserSaveDTO {

    @ApiModelProperty("课程ID")
    @NotNull(message = "课程ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long courseId;

    @ApiModelProperty("团队/企业名称")
    private String teamOrEntName;

    @ApiModelProperty("职务")
    private String position;
}
