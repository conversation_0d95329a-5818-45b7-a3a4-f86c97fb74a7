package com.fykj.scaffold.zyz.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.MyTeamDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/mini/volunteer/team")
@Api(tags = "微信小程序志愿者团队接口")
public class MiniVolunteerTeamController {
    @Autowired
    private IZyzTeamService zyzTeamService;
    @Autowired
    private IZyzVolunteerTeamService iZyzVolunteerTeamService;
    @Autowired
    private IZyzVolunteerService volunteerService;

    @ApiOperation("新增团队")
    @PostMapping("/saveTeam")
    public Result saveTeam(@RequestBody ZyzTeam entity) {
        //插入联系人身份证号码
        entity.setAdminCard(volunteerService.getByPhone(entity.getAdminContact()).getCertificateId());
        zyzTeamService.saveTeam(entity);
        return new Result();
    }

    @ApiOperation("再次申请团队")
    @GetMapping("/updateTeamStatus")
    public Result updateTeamStatus(Long teamId) {
        if (!zyzTeamService.updateTeamStatus(teamId)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return new Result();
    }

    @ApiOperation("志愿者退出团队")
    @GetMapping("/withdrawFromTeam")
    public Result withdrawFromTeam(Long teamId) {
        if (!iZyzVolunteerTeamService.withdrawFromTeam(teamId)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return new Result();
    }

    @ApiOperation("志愿者取消申请加入团队")
    @GetMapping("/cancelJoinTeam")
    public Result cancelJoinTeam(Long teamId) {
        if (!iZyzVolunteerTeamService.cancelJoinTeam(teamId)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return new Result();
    }

    @ApiOperation("志愿者删除团队申请记录")
    @GetMapping("/removeTeamApply")
    public Result removeTeamApply(Long auditId) {
        if (!iZyzVolunteerTeamService.removeTeamApply(auditId)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return new Result();
    }

    @ApiOperation("志愿者申请加入团队")
    @GetMapping("/joinTeam")
    public Result joinTeam(Long teamId) {
        if (!iZyzVolunteerTeamService.joinTeam(teamId)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return new Result();
    }

    @ApiOperation("查看申请状态/申请团队状态/1表示可申请/2表示已申请/3表示已通过/4表示未成为志愿者")
    @GetMapping("/checkStatus")
    public Result checkStatus(Long teamId) {
        return new JsonResult<>(zyzTeamService.checkStatus(teamId));
    }


    @ApiOperation("分页查询我的申请团队团队")
    @PostMapping({"/pages"})
    public JsonResult<IPage<MyTeamDto>> pagesForMyApplyTeam(@RequestBody ZyzTeamParams params) {
        IPage<MyTeamDto> result = zyzTeamService.getMyPagesForApi(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("分页查询我的团队")
    @PostMapping({"/pagesForMyTeam"})
    public JsonResult<IPage<MyTeamDto>> pagesForMyTeam(@RequestBody ZyzTeamParams params) {
        IPage<MyTeamDto> result = zyzTeamService.getMyTeamPagesForApi(params);
        return new JsonResult<>(result);
    }
}
