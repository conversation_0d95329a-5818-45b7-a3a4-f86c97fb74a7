package com.fykj.scaffold.zsq_docking.log.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 知社区对接新闻对接记录
 *
 * <AUTHOR> @email ${email}
 * @date 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zsq_docking_news_record")
public class ZSQDockingNewsRecord extends BaseEntity {

    private static final long serialVersionUID = 9212634362781250021L;

    /**
     * 新闻 id
     */
    @TableField("news_id")
    @ApiModelProperty(value = "新闻 id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long newsId;

    /**
     * 对接类型
     */
    @TableField("docking_type")
    @ApiModelProperty(value = "对接失败原因")
    private String dockingType;

    /**
     * 对接操作类型
     */
    @TableField("docking_operation")
    @ApiModelProperty(value = "对接操作类型")
    private String dockingOperation;

    /**
     * 对接状态
     */
    @TableField("docking_status")
    @ApiModelProperty(value = "对接状态")
    private Integer dockingStatus;

    /**
     * 对接时间
     */
    @TableField("docking_time")
    @ApiModelProperty(value = "对接时间")
    @JsonFormat(pattern = DATETIME_FORMAT)
    private LocalDateTime dockingTime;

    /**
     * 对接详细信息
     */
    @TableField("docking_msg")
    @ApiModelProperty(value = "对接详细信息")
    private String dockingMsg;

    /**
     * 对接数据（push:发送给mq的数据，receive:接口收到的数据）
     */
    @TableField("docking_data")
    @ApiModelProperty(value = "对接数据（push:发送给mq的数据，receive:接口收到的数据）")
    private String dockingData;

    /**
     * 是否为重试记录
     */
    @TableField("retry")
    @ApiModelProperty(value = "是否为重试记录")
    private Boolean retry;
}
