package com.fykj.scaffold.config.mybatisplus;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class MetaObjectHandlerConfig {

//    @Bean
//    MetaObjectHandler metaObjectHandler(){
//        return new MyMetaObjectHandler();
//    }
}
