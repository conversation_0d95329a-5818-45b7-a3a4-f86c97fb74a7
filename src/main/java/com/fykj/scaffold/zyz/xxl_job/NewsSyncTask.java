package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 新闻同步补偿
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class NewsSyncTask {

    @Autowired
    private INewsService newsService;

    @XxlJob("News_Sync_Handler")
    public void newsSyncJobHandler() {
        XxlJobHelper.log("News_Sync_Handler-Job, START!");
        // 获取待同步的新闻数据以及新闻审核通过时间三天内新闻同步失败的数据
        List<Long> newsIds = newsService.getNeedSync();
        if (CollectionUtil.isNotEmpty(newsIds)) {
            try {
                newsService.syncBatch(newsIds);
            } catch (Exception e) {
                log.error("新闻批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("News_Sync_Handler-Job, END!");
    }
}
