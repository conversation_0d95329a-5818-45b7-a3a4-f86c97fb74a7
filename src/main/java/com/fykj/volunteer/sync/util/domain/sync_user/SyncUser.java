package com.fykj.volunteer.sync.util.domain.sync_user;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncUser {
    /**
     * 证件类型，默认为身份证类型（必填）
     * 0-内地身份证
     * 3-港澳台居民身份证
     * 4-护照
     */
    private String type;
    /**
     * 身份证编号（必填）
     */
    private String idCode;
    /**
     * 真实姓名（必填）
     */
    private String name;
    /**
     * 手机号码（必填）
     */
    private String phone;
    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 志愿者证件类型
     */
    public interface CardType {
        /**
         * 内地身份证
         */
      String SYNC_USER_TYPE_DL="0";
        /**
         * 港澳身份证
         */
      String SYNC_USER_TYPE_GA="3";
        /**
         * 护照
         */
      String SYNC_USER_TYPE_HZ="4";

    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "vol")
    public static class Vol {
        private List<SyncUser> info;
    }

    public String buildXml() {
        Vol vol = new Vol();
        vol.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(vol);
    }
}
