package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto;
import com.fykj.scaffold.security.business.domain.entity.Resource;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * <p>
 * 资源服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public interface IResourceService extends IBaseService<Resource> {


    /**
     * 角色页面获取资源列表
     *
     * @return 资源树
     */
    List<Resource> menuListForRolePage();

    /**
     * 查询用户拥有的所有菜单
     *
     * @param userId
     * @return
     */
    List<Resource> findResourceByUserId(long userId);

    /**
     * 查询用户拥有的所有菜单
     *
     * @param userId
     * @return
     */
    List<Resource> findResourceByUserIdAndRoleIds(long userId, String type, List<Long> roleIds);
}
