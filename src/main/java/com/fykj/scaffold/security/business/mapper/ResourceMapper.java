package com.fykj.scaffold.security.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto;
import com.fykj.scaffold.security.business.domain.entity.Resource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public interface ResourceMapper extends BaseMapper<Resource> {

    /**
     * 根据角色主键、资源类型和父资源查询资源列表
     *
     * @param roleId   角色主键
     * @param type     资源类型
     * @param parentId 父资源主键
     * @return 资源列表
     */
    List<Resource> findByRoleIdAndTypeAndParent(@Param("roleId") Long roleId, @Param("type") Integer type, @Param("parentId") Long parentId);

    /**
     * 根据角色主键、资源类型查询资源列表
     *
     * @param roleId 角色主键
     * @return 资源列表
     */
    List<Resource> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 更具用户id查询菜单资源
     *
     * @param userId
     * @return
     */
    List<Resource> findResourceByUserId(long userId);

    /**
     * 更具用户id查询菜单资源
     *
     * @param userId
     * @return
     */
    List<Resource> findResourceByUserIdAndRoleIds(@Param("userId") long userId, @Param("type") String type ,@Param("roleIds") List<Long>roleIds);
}
