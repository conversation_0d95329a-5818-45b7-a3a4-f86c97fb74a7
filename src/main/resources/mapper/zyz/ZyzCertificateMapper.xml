<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzCertificateMapper">
    <select id="query" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzCertificate">
        SELECT
            zc.*,
            zcc.`name` AS categoryName
        FROM 
            zyz_certificate zc
        LEFT JOIN zyz_certificate_category zcc ON zc.category = zcc.id
        WHERE
            zc.is_deleted = 0
        <if test="params.key != null and params.key != ''">
            AND (
                zc.name LIKE CONCAT('%', #{params.name}, '%')
                OR
                zc.ode LIKE CONCAT('%', #{params.name}, '%')
                OR
                zc.content LIKE CONCAT('%', #{params.name}, '%')
                OR
                zc.remarks LIKE CONCAT('%', #{params.name}, '%')
            )
        </if>
        <if test="params.category != null">
            AND zc.category = #{params.category}
        </if>
        ORDER BY
            zc.create_date DESC
    </select>
</mapper>
