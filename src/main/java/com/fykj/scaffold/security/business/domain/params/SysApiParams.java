package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 系统资源-API接口
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SysApiParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"api_code", "api_name", "api_path"})
    @ApiModelProperty("api编码/名称/路径模糊查询")
    private String keyword;

    @MatchType(value = QueryType.EQ, fieldName = "status")
    @ApiModelProperty("状态")
    private Boolean status;

    @MatchType(value = QueryType.EQ, fieldName = "is_persist")
    @ApiModelProperty("是否保留")
    private Boolean persist;

    @MatchType(value = QueryType.LIKE, fieldName = {"api_category"})
    @ApiModelProperty("类目")
    private String categoryId;
}
