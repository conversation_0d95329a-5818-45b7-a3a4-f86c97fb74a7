package com.fykj.scaffold.security.oauth2.error;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 1. 访问接口时，校验token是否过期或不正确
 * <AUTHOR>
 */
public class MyOAuth2AuthenticationEntryPoint extends OAuth2AuthenticationEntryPoint {

    @Autowired
    private ErrorAnalyzer throwableAnalyzer;
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        try {
            throwableAnalyzer.translate(request,response,authException);
        } catch (Exception e) {
            throw new ServletException();
        }
    }
}
