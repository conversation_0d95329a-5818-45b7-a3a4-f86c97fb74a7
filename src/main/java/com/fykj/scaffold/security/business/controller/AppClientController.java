package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.AppClient;
import com.fykj.scaffold.security.business.service.IAppClientService;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.UUIDUtils;

/**
 * 应用管理前端控制器
 *
 * <AUTHOR>
 * @date 2021-05-20
 */
@RestController
@RequestMapping("/admin/app/client")
public class AppClientController {
    @Autowired
    private IAppClientService baseService;

    @ApiOperation("保存方法")
    @PostMapping("/save")
    public Result save(@RequestBody @Validated({BaseEntity.Add.class}) AppClient entity) {
        boolean result = this.baseService.save(entity);
        return result ? new Result() : new Result(ResultCode.FAIL);
    }

    @ApiOperation("密钥重置")
    @GetMapping("/secret/reset")
    public JsonResult<String> secretRest(@RequestParam String appId) {
        String secret = this.baseService.secretRest(appId);
        return new JsonResult<>(secret);
    }

}

