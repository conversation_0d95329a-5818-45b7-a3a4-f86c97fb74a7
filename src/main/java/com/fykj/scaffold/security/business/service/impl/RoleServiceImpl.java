package com.fykj.scaffold.security.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.dto.RoleResourceDto;
import com.fykj.scaffold.security.business.domain.dto.RoleTreeDataDto;
import com.fykj.scaffold.security.business.domain.entity.Resource;
import com.fykj.scaffold.security.business.domain.entity.Role;
import com.fykj.scaffold.security.business.domain.entity.SysRoleAction;
import com.fykj.scaffold.security.business.domain.params.RoleParams;
import com.fykj.scaffold.security.business.mapper.RoleMapper;
import com.fykj.scaffold.security.business.service.IResourceService;
import com.fykj.scaffold.security.business.service.IRoleResourceService;
import com.fykj.scaffold.security.business.service.IRoleService;
import com.fykj.scaffold.security.business.service.ISysRoleActionService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.support.wrapper.QueryWrapperBuilder;
import constants.Mark;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.util.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import utils.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.advanced_unit.cons.AdvancedUnitCons.ADVANCE_UNIT_ADMIN_ROLE_CODE;
import static com.fykj.scaffold.advanced_unit.cons.AdvancedUnitCons.ADVANCE_UNIT_DEFAULT_ROLE_CODE;
import static com.fykj.scaffold.support.conns.Cons.RoleCode.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-16
 */
@Service
public class RoleServiceImpl extends BaseServiceImpl<RoleMapper, Role> implements IRoleService {
    @Autowired
    private IRoleResourceService roleResourceService;

    @Autowired
    private ISysRoleActionService roleActionService;

    @Autowired
    private IResourceService resourceService;

//    @Override
//    public List<Role> export(RoleParams params) {
//        return baseMapper.export(params);
//    }

    @Override
    public IPage<Role> page(RoleParams params) {
        if (params == null) {
            params = new RoleParams();
        }
        return super.page(params);
    }

    @Override
    public boolean save(RoleResourceDto dto) {
        Role role = new Role();
        BeanUtil.copyProperties(dto, role);
        //保存角色
        boolean flag = saveOrUpdate(role);
        List<Long> actionIds = dto.getActionIdList().stream().map(Long::parseLong).collect(Collectors.toList());
        List<Long> menuIds = dto.getMenuIdList().stream().map(Long::parseLong).collect(Collectors.toList());
        menuIds.forEach(m -> {

        });

        //保存角色与菜单关系
        roleActionService.saveRoleActionRelationship(role.getId(), actionIds);
        roleResourceService.saveRoleResourceRelationship(role.getId(), menuIds);
        return flag;
    }

    @Override
    public RoleResourceDto findOneById(Long id) {
        Role role = getById(id);
        RoleResourceDto dto = new RoleResourceDto();
        BeanUtil.copyProperties(role, dto);
        List<String> resourceIds = roleResourceService.findResourceIdList(id);
        List<String> actionIds = new ArrayList<>();
        for (SysRoleAction it : roleActionService.lambdaQuery().eq(SysRoleAction::getRoleId, id).list()) {
            String s = String.valueOf(it.getActionId());
            actionIds.add(s);
        }
        dto.setActionIdList(actionIds);
        dto.setMenuIdList(resourceIds);
        return dto;
    }

    @Override
    public boolean checkCode(Long id, String code) {
        return lambdaQuery().eq(Role::getCode, code).ne(StringUtil.isNotEmpty(id), Role::getId, id).count() > 0;
    }

    @Override
    public Role getRoleByCode(String code) {
        return lambdaQuery().eq(Role::getCode, code).one();
    }

    @Override
    public List<Role> list() {
        RoleParams params = new RoleParams();
        return list(QueryWrapperBuilder.build(params));
    }

    @Override
    public List<RoleTreeDataDto> roleTree() {
        List<RoleTreeDataDto> result = new ArrayList<>();
        List<Role> allRoles = super.list();
        allRoles.stream().filter(it -> ROLE_CODE_VOLUNTEER.equals(it.getCode())).findAny().ifPresent(volunteer -> result.add(new RoleTreeDataDto(volunteer.getName(), Collections.singletonList(volunteer.getCode()), null)));
        List<String> adminRoleCodes = Arrays.asList(ROLE_CODE_ASSOCIATION_ADMIN, ROLE_CODE_SUB_ASSOCIATION_ADMIN, ROLE_CODE_COMMUNITY_ADMIN, ROLE_CODE_TEAM_ADMIN);
        List<Role> adminRoles = allRoles.stream().filter(it -> adminRoleCodes.contains(it.getCode())).collect(Collectors.toList());
        RoleTreeDataDto adminRoleTree = new RoleTreeDataDto("管理角色", adminRoleCodes, new ArrayList<>());
        adminRoleCodes.forEach(it -> {
            adminRoles.stream().filter(r -> r.getCode().equals(it)).findAny().ifPresent(role -> adminRoleTree.getChildren().add(new RoleTreeDataDto(role.getName(), Collections.singletonList(it), null)));
        });
        result.add(adminRoleTree);
        List<String> advanceUnitRoleCodes = Arrays.asList(ADVANCE_UNIT_ADMIN_ROLE_CODE, ADVANCE_UNIT_DEFAULT_ROLE_CODE);
        List<Role> advanceUnitRoles = allRoles.stream().filter(it -> advanceUnitRoleCodes.contains(it.getCode())).collect(Collectors.toList());
        RoleTreeDataDto advanceUnitRoleTree = new RoleTreeDataDto("文明单位角色", advanceUnitRoleCodes, new ArrayList<>());
        advanceUnitRoleCodes.forEach(it -> {
            advanceUnitRoles.stream().filter(r -> r.getCode().equals(it)).findAny().ifPresent(role -> advanceUnitRoleTree.getChildren().add(new RoleTreeDataDto(role.getName(), Collections.singletonList(it), null)));
        });
        result.add(advanceUnitRoleTree);
        // TODO 部委局办角色处理
        List<String> departmentRoleCodes = Collections.singletonList("DEPART_BUREAU_ADMIN");
        List<Role> departmentRoles = allRoles.stream().filter(it -> departmentRoleCodes.contains(it.getCode())).collect(Collectors.toList());
        RoleTreeDataDto departmentRoleTree = new RoleTreeDataDto("部委局办角色", departmentRoleCodes, new ArrayList<>());
        departmentRoleCodes.forEach(it -> {
            departmentRoles.stream().filter(r -> r.getCode().equals(it)).findAny().ifPresent(role -> departmentRoleTree.getChildren().add(new RoleTreeDataDto(role.getName(), Collections.singletonList(it), null)));
        });
        result.add(departmentRoleTree);
        List<String> otherRoleCodes = Arrays.asList("SYS_ADMIN", "OM_ADMIN", "ACT_BULU_MANAGER", "GENERAL_USER", "VISITOR", "SIP_ZSQ_USER");
        List<Role> otherRoles = allRoles.stream().filter(it -> otherRoleCodes.contains(it.getCode())).collect(Collectors.toList());
        RoleTreeDataDto otherRoleTree = new RoleTreeDataDto("其他角色", otherRoleCodes, new ArrayList<>());
        otherRoleCodes.forEach(it -> {
            otherRoles.stream().filter(r -> r.getCode().equals(it)).findAny().ifPresent(role -> otherRoleTree.getChildren().add(new RoleTreeDataDto(role.getName(), Collections.singletonList(it), null)));
        });
        result.add(otherRoleTree);
        return result;
    }
}
