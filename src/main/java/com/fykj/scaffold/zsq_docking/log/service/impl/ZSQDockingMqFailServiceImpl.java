package com.fykj.scaffold.zsq_docking.log.service.impl;

import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingMqFail;
import com.fykj.scaffold.zsq_docking.log.mapper.ZSQDockingMqFailMapper;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingMqFailService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 知社区对接推送mq消费失败记录表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-13
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZSQDockingMqFailServiceImpl extends BaseServiceImpl<ZSQDockingMqFailMapper, ZSQDockingMqFail> implements IZSQDockingMqFailService {


}
