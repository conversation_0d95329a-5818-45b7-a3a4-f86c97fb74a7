package com.fykj.scaffold.portal_website.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.cms.domain.entity.CmsCategory;
import com.fykj.scaffold.cms.domain.entity.CmsCategoryContent;
import com.fykj.scaffold.cms.domain.entity.CmsUserOpLog;
import com.fykj.scaffold.cms.service.ICmsCategoryContentService;
import com.fykj.scaffold.cms.service.ICmsCategoryService;
import com.fykj.scaffold.cms.service.ICmsUserOpLogService;
import com.fykj.scaffold.mq.cons.TopicCons;
import com.fykj.scaffold.mq.event.CmsSyncEvent;
import com.fykj.scaffold.portal_website.conns.PortalWebsiteCons;
import com.fykj.scaffold.portal_website.domain.dto.CheckOpDto;
import com.fykj.scaffold.portal_website.domain.dto.NewsAuditDto;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.domain.entity.NewsAudit;
import com.fykj.scaffold.portal_website.domain.params.NewsParams;
import com.fykj.scaffold.portal_website.mapper.NewsMapper;
import com.fykj.scaffold.portal_website.service.INewsAuditService;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.audit_flow.AuditFlowInput;
import com.fykj.scaffold.support.audit_flow.AuditHandleResult;
import com.fykj.scaffold.support.audit_flow.AuditHandleUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.support.utils.ImageUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.sync.service.IPlatformSyncService;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingNewsRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.event.NewsDockingEvent;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzProject;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import com.fykj.scaffold.zyz.service.IZyzProjectService;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.fykj.scaffold.portal_website.conns.PortalWebsiteCons.*;
import static com.fykj.scaffold.support.conns.Cons.TOP_DEPT_CODE;
import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_NEWS_AUDIT_NOTICE;
import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_NEWS_AUDIT_RESULT;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.NewsDataDockingType.*;
import static constants.Mark.COMMA;

/**
 * <p>
 * 新闻资讯服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
@Slf4j
public class NewsServiceImpl extends BaseServiceImpl<NewsMapper, News> implements INewsService {

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private ICmsCategoryService categoryService;

    @Autowired
    private ICmsCategoryContentService categoryContentService;

    @Autowired
    private INewsAuditService auditService;

    @Autowired
    private IZyzTeamService zyzTeamService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Autowired
    private AuditHandleUtil auditUtil;

    @Autowired
    private ICmsUserOpLogService cmsUserOpLogService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private IPlatformSyncService syncService;

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private IZyzProjectService projectService;

    @Autowired
    private IZSQDockingNewsRecordService newsDockingRecordService;

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;


    @Override
    public boolean saveNews(News entity) {
        String capacity = Oauth2Util.getManagerCapacity();
        if (!Cons.RoleCode.ROLE_CODE_ASSOCIATION_ADMIN.equals(capacity)
                && !Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)
                && !Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN.equals(capacity)
                && !Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(capacity)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "您当前身份没有保存新闻资源的权限！");
        }
        basicCheck(entity);
        String orgCode = Oauth2Util.getOrgCode();
        Long teamId = Oauth2Util.getTeamId();
        if (StringUtil.isEmpty(orgCode) && ObjectUtil.isEmpty(teamId)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "未获取到用户的身份信息！");
        }
        entity.setTeamPublish(Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(capacity));
        if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(capacity)) {
            entity.setTeamId(teamId);
            ZyzTeam team = teamService.getById(teamId);
            entity.setOrgCode(team.getOrgCode());
            entity.setOrgName(team.getName());
        } else {
            entity.setOrgCode(orgCode);
            entity.setOrgName(orgService.getByCode(orgCode).getName());
        }
        AuditHandleResult auditResult = auditUtil.newsFlow(
                AuditFlowInput.builder()
                        .teamPublish(entity.getTeamPublish())
                        .publishOrgCode(entity.getOrgCode())
                        .publishTeamId(entity.getTeamId())
                        .build());

        entity.setAuditStatus(auditResult.getStatus());
        entity.setAuditOrgCode(auditResult.getOrgCode());
        entity.setType(NEWS_CMS_CODE);
        entity.setSync(Cons.PlatformSyncState.WAIT_SYNC);
        super.save(entity);
        saveCategoryContent(entity);
        if (!auditResult.isFinish()) {
            //发送信息
            List<String> orgCodes = new ArrayList<>();
            orgCodes.add(auditResult.getOrgCode());
            sendTmpMsg.sendToAuditOrg(SMT_NEWS_AUDIT_NOTICE, orgCodes, entity.getTitle(), entity.getOrgName());
        } else {
            List<Long> newsIds = new ArrayList<>();
            newsIds.add(entity.getId());
            NewsAudit record = new NewsAudit();
            record.setContentId(entity.getId());
            record.setPass(true);
            record.setMemo(null);
            auditService.save(record);
            sendTmpMsg.sendToNewsPublisher(SMT_NEWS_AUDIT_RESULT, newsIds, NAS_PASS.equals(auditResult.getStatus()) ? "通过" : "不通过", entity.getTitle());
        }
        //审核通过直接发mq
//        if (NAS_PASS.equals(auditResult.getStatus())) {
//            mqTemplate.send(TopicCons.CMS_SYNC, MessageBuilder.withPayload(CmsSyncEvent.builder().contentId(entity.getId()).build()).build());
//        }
        // 对接知社区
        dockingZSQ(entity, DT_CREATE, null);
        return true;
    }

    @Override
    public boolean updateNews(News entity) {
        basicCheck(entity);
        categoryContentService.removeByContentId(entity.getId(), false);
        saveCategoryContent(entity);
        News exists = super.getById(entity.getId());
        setOrgName(exists);
        AuditHandleResult auditResult = auditUtil.newsFlow(
                AuditFlowInput.builder()
                        .teamPublish(exists.getTeamPublish())
                        .publishOrgCode(exists.getOrgCode())
                        .publishTeamId(exists.getTeamId())
                        .build());
        entity.setAuditStatus(auditResult.getStatus());
        entity.setAuditOrgCode(auditResult.getOrgCode());
        entity.setSync(null);
        super.updateById(entity);
        if (!auditResult.isFinish()) {
            //发送信息
            List<String> orgCodes = new ArrayList<>();
            orgCodes.add(auditResult.getOrgCode());
            sendTmpMsg.sendToAuditOrg(SMT_NEWS_AUDIT_NOTICE, orgCodes, entity.getTitle(), exists.getOrgName());
        } else {
            List<Long> newsIds = new ArrayList<>();
            newsIds.add(entity.getId());
            NewsAudit record = new NewsAudit();
            record.setContentId(entity.getId());
            record.setPass(true);
            record.setMemo(null);
            auditService.save(record);
            sendTmpMsg.sendToNewsPublisher(SMT_NEWS_AUDIT_RESULT, newsIds, NAS_PASS.equals(auditResult.getStatus()) ? "通过" : "不通过", entity.getTitle());
        }
        // 对接知社区
        dockingZSQ(entity, DT_UPDATE, null);
        return true;
    }

    private void saveCategoryContent(News entity) {
        CmsCategory category = categoryService.getByCode(NEWS_CMS_CODE);
        List<String> categoryIdList = entity.getCategoryIds();
        int size = categoryIdList.size();
        Long categoryId = Long.valueOf(categoryIdList.get(size - 1));
        CmsCategoryContent categoryContent = new CmsCategoryContent();
        categoryContent.setCategoryId(categoryId);
        categoryContent.setCategoryCode(categoryService.getById(categoryContent.getCategoryId()).getCode());
        categoryContent.setParentId(size == 1 ? category.getId() : Long.valueOf(categoryIdList.get(size - 2)));
        categoryContent.setCategoryList(String.valueOf(category.getId()).concat(COMMA).concat(categoryIdList.stream().map(String::valueOf).collect(Collectors.joining(COMMA))));
        categoryContent.setStick(Boolean.FALSE);
        categoryContent.setContentId(entity.getId());
        categoryContentService.save(categoryContent);
    }

    private void setOrgName(News entity) {
        if (entity.getTeamPublish()) {
            ZyzTeam team = teamService.getById(entity.getTeamId());
            entity.setOrgName(team.getName());
        } else {
            SysOrg org = orgService.getByCode(entity.getOrgCode());
            entity.setOrgName(org.getName());
        }
    }

    @Override
    public News getById(Serializable id) {
        News news = super.getById(id);
        List<CmsCategoryContent> categoryContents = categoryContentService.getByContentId(id, false);
        if (CollectionUtil.isEmpty(categoryContents)) {
            throw new BusinessException(ResultCode.FAIL, "数据类型获取失败！");
        }
        CmsCategoryContent categoryContent = categoryContents.get(0);
        List<String> categoryList = Arrays.asList(categoryContent.getCategoryList().split(COMMA));
        news.setCategoryIds(categoryList.subList(1, categoryList.size()));
        return news;
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        list.forEach(it -> {
            Long newsId = Long.valueOf(String.valueOf(it));
            News news = super.getById(newsId);
            super.removeById(newsId);
            categoryContentService.removeByContentId(newsId, false);
            // 对接知社区
            dockingZSQ(news, DT_DELETE, null);
        });
        return true;
    }

    @Override
    public IPage<News> page(BaseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new NewsParams();
        }
        String currentUserOrgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isNotEmpty(currentUserOrgCode)) {
            ((NewsParams) params).setCurrentOrgCode(currentUserOrgCode);
        }
        //查询列表数据
        Page<News> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForNews(page, (NewsParams) params).convert(this::convert);
    }

    private News convert(News record) {
        record = DictTransUtil.trans(record);
        List<String> categoryIds = Arrays.asList(record.getCategory().split(COMMA));
        List<String> categoryName = new ArrayList<>();
        categoryIds.subList(1, categoryIds.size()).forEach(it -> {
            categoryName.add(categoryService.getById(it).getName());
        });
        record.setCategory(String.join(Mark.LINE, categoryName));
        return record;
    }

    @Override
    public Integer getNewsWaitAuditNum() {
        String orgCode = Oauth2Util.getOrgCode();
        return baseMapper.getNewsWaitAuditNum(orgCode, NAS_PASS, NAS_REFUSE);
    }

    @Override
    public void setStick(Long id) {
        List<CmsCategoryContent> categoryContents = categoryContentService.getByContentId(id, false);
        if (CollectionUtil.isEmpty(categoryContents)) {
            throw new BusinessException(ResultCode.FAIL, "未找到该记录的类型信息！");
        }
        CmsCategoryContent categoryContent = categoryContents.get(0);
        categoryContent.setStick(!categoryContent.getStick());
        categoryContentService.updateById(categoryContent);
//        // 置顶某条数据后其他数据取消置顶
//        if (categoryContent.getStick()) {
//            categoryContentService.lambdaUpdate().ne(CmsCategoryContent::getId, categoryContent.getId()).like(CmsCategoryContent::getCategoryList, String.valueOf(categoryContent.getCategoryId())).set(CmsCategoryContent::getStick, false).update();
//        }
    }

    @Override
    public void audit(NewsAuditDto audit) {
        News exists = super.getById(audit.getId());
        setOrgName(exists);
        AuditFlowInput input = AuditFlowInput
                .builder()
                .teamPublish(exists.getTeamPublish())
                .publishOrgCode(exists.getOrgCode())
                .publishTeamId(exists.getTeamId())
                .currentOrgCode(Oauth2Util.getOrgCode())
                .pass(audit.getPass())
                .build();
        AuditHandleResult auditResult = auditUtil.newsFlow(input);
        if (!auditResult.isFinish()) {
            //发送信息
            List<String> orgCodes = new ArrayList<>();
            orgCodes.add(auditResult.getOrgCode());
            sendTmpMsg.sendToAuditOrg(SMT_NEWS_AUDIT_NOTICE, orgCodes, exists.getTitle(), exists.getOrgName());
        } else {
            List<Long> newsIds = new ArrayList<>();
            newsIds.add(exists.getId());
            sendTmpMsg.sendToNewsPublisher(SMT_NEWS_AUDIT_RESULT, newsIds, NAS_PASS.equals(auditResult.getStatus()) ? "通过" : "不通过", exists.getTitle());
        }
        exists.setAuditStatus(auditResult.getStatus());
        exists.setAuditOrgCode(auditResult.getOrgCode());
        exists.setAuditMemo(audit.getMemo());
        if (!audit.getPass()) {
            exists.setGrounding(Boolean.FALSE);
        }
        super.updateById(exists);
        NewsAudit record = new NewsAudit();
        record.setContentId(audit.getId());
        record.setPass(audit.getPass());
        record.setMemo(audit.getMemo());
        auditService.save(record);

        //通过的话同步给市平台
        if (PortalWebsiteCons.NAS_PASS.equals(auditResult.getStatus())) {
            mqTemplate.send(TopicCons.CMS_SYNC, MessageBuilder.withPayload(CmsSyncEvent.builder().contentId(audit.getId()).build()).build());
        }
        //对接知社区
        if (auditResult.isFinish()) {
            dockingZSQ(exists, DT_AUDIT, null);
        }
    }

    @Override
    public Result auditBatch(String ids, String memo) {
        if (StringUtil.isEmpty(ids)) {
            throw new BusinessException(ResultCode.FAIL, "请勾选需要批量审核的记录！");
        }
        List<Long> idList = Arrays.stream(ids.split(COMMA)).map(Long::valueOf).collect(Collectors.toList());
        List<String> news = new ArrayList<>();
        for (Long it : idList) {
            transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            transactionTemplate.executeWithoutResult((status) -> {
                try {
                    NewsAuditDto dto = new NewsAuditDto();
                    dto.setId(it);
                    dto.setPass(Boolean.TRUE);
                    dto.setMemo(memo);
                    audit(dto);
                } catch (Exception e) {
                    News exists = super.getById(it);
                    news.add(exists.getTitle());
                    status.setRollbackOnly();
                }
            });
        }
        if (CollectionUtil.isEmpty(news)) {
            return new JsonResult<>(ResultCode.OK);
        } else {
            return new JsonResult<>(ResultCode.FAIL.code(), "新闻【" + String.join("】,【", news) + "】审核出现异常！");
        }
    }

    @Override
    public IPage<News> pageForApi(NewsParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new NewsParams();
        }
        Page<News> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        String categoryCode = params.getCategoryCode();
        if (StringUtil.isEmpty(categoryCode)) {
            return baseMapper.pageForNewsApi(page, params).convert(this::convert);
        }
        String[] categoryCodeList = categoryCode.split(COMMA);
        List<String> categoryIds = new ArrayList<>();
        for (String it : categoryCodeList) {
            CmsCategory category = categoryService.getByCode(it);
            if (category != null) {
                categoryIds.add(String.valueOf(category.getId()));
            }
        }
        if (categoryIds.isEmpty()) {
            return baseMapper.pageForNewsApi(page, params).convert(this::convert);
        }
        if (categoryIds.size() == 1) {
            params.setCategoryId(categoryIds.get(0));
        } else {
            params.setCategoryIds(categoryIds);
        }
        return baseMapper.pageForNewsApi(page, params).convert(this::convert);
    }

    @Override
    public List<News> getNewsByTeam(Long projectId) {
//        Long teamId = Oauth2Util.getTeamId();
//        if (teamId == null) {
//            throw new BusinessException(ResultCode.FAIL, "当前登录用户不属于任何团队！");
//        }
        ZyzProject project = projectService.getProjectById(projectId);
        return baseMapper.getNewsByTeam(project.getTeamId(),projectId);
    }

    @Override
    public IPage<News> pageForMyNewsApi(NewsParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new NewsParams();
        }
        if (Oauth2Util.getUserId() == null) {
            throw new BusinessException(ResultCode.TOKEN_ERROR_CODE, "登录失效请重新登录！");
        }
        params.setUserId((Long) Oauth2Util.getUserId());
        Page<News> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForMyNewsApi(page, params);
    }

    @Override
    public void updateCmsContentById(Serializable id) {
        News news = super.getById(id);
        news.setActualReading(news.getActualReading() == null ? 1 : news.getActualReading() + 1);
        super.updateById(news);
    }

    @Override
    public News getForApi(Serializable id) {
        News news = super.getById(id);
        if (news.getTeamPublish()) {
            ZyzTeam team = zyzTeamService.getById(news.getTeamId());
            news.setPublisher(ObjectUtil.isEmpty(team) ? "未知团队" : team.getName());
        } else {
            news.setPublisher(orgService.getByCode(news.getOrgCode()).getName());
        }
        return news;
    }

    @Override
    public CheckOpDto checkOp(Long id, String source) {
        CheckOpDto dto = new CheckOpDto();
        Long userId = (Long) Oauth2Util.getUserId();
        if (StringUtil.isEmpty(userId)) {
            dto.setThumbsUp(false);
            dto.setCollect(false);
        } else {
            CmsUserOpLog cmsUserOpLogThumbsUp = cmsUserOpLogService.lambdaQuery()
                    .eq(CmsUserOpLog::getCmsCateContId, id)
                    .eq(CmsUserOpLog::getType, PortalWebsiteCons.CMS_USER_OP_TYPE_THUMBS_UP)
                    .eq(CmsUserOpLog::getSource, source)
                    .eq(CmsUserOpLog::getUserId, userId).one();
            CmsUserOpLog cmsUserOpLogCollect = cmsUserOpLogService.lambdaQuery()
                    .eq(CmsUserOpLog::getCmsCateContId, id)
                    .eq(CmsUserOpLog::getSource, source)
                    .eq(CmsUserOpLog::getType, PortalWebsiteCons.CMS_USER_OP_TYPE_COLLECT)
                    .eq(CmsUserOpLog::getUserId, userId).one();
            dto.setThumbsUp(cmsUserOpLogThumbsUp != null);
            dto.setCollect(cmsUserOpLogCollect != null);
        }
        return dto;
    }

    @Override
    public void ground(String id) {
        News news = getById(id);
        news.setGrounding(!news.getGrounding());
        super.updateById(news);
        dockingZSQ(news, DT_ON_OFF, news.getGrounding());
    }

    @Override
    public List<Long> getNeedSync() {
        return baseMapper.getNeedSync(LocalDateTime.now(), LocalDate.now().minusDays(3).atStartOfDay());
    }

    @Override
    public void syncBatch(List<Long> newsIds) {
        if (CollectionUtil.isEmpty(newsIds)) {
            return;
        }
        newsIds.forEach(it -> {
            try {
                syncOne(it);
            } catch (Exception e) {
                log.error("id为[{}]的新闻同步异常！异常原因：{}", it, e.getMessage(), e);
            }
        });
    }

    @Override
    public List<News> getNewsByProjectId(Long projectId) {
        return lambdaQuery().eq(News::getProjectId, projectId).orderByDesc(News::getEffectiveDate).list();
    }



    private void syncOne(Long newsId) {
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@Nonnull TransactionStatus transactionStatus) {
                try {
                    syncService.syncCms(newsId, IdUtil.getSnowflake(1, 1).nextId());
                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();
                    throw new BusinessException(ResultCode.FAIL, "id为【" + newsId + "】的新闻同步异常！异常原因：" + e.getMessage(), e);
                }
            }
        });
    }

    @Override
    public List<OrgDataFormDto> getOrgPublishNewsNum(OrgDataReportFormParams params) {
        return baseMapper.getOrgPublishNewsNum(params);
    }

    @Override
    public Integer getOrgSelfPublishNewsNum(String orgCode, LocalDateTime start, LocalDateTime end) {
        return baseMapper.getOrgSelfPublishNewsNum(orgCode, start, end);
    }

    @Override
    public List<OrgDataFormDto> getTeamPublishNewsNum(OrgDataReportFormParams params) {
        return baseMapper.getTeamPublishNewsNum(params);
    }

    @Override
    public Integer getOtherOrgPublishNewsNum(LocalDateTime start, LocalDateTime end) {
        return baseMapper.getOtherOrgPublishNewsNum(start, end);
    }

    private void basicCheck(News entity) {
        //验证图片，有些文件后缀名是图片，但是其实并不是真的图片
        ImageUtil.checkPic(entity.getTitleImgUrl());
        //验证联系电话
        if (StringUtil.isNotEmpty(entity.getTitle()) && entity.getTitle().length() > 1000) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "标题长度不能大于100");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ZSQNewsInfoDto getNewsDetail(Long id) {
        News news = lambdaQuery().eq(News::getId, id).one();
        if (news == null) {
            return null;
        }
        List<CmsCategoryContent> categoryContents = categoryContentService.getByContentId(id, false);
        if (CollectionUtil.isNotEmpty(categoryContents)) {
            CmsCategoryContent categoryContent = categoryContents.get(0);
            List<String> categoryList = Arrays.asList(categoryContent.getCategoryList().split(COMMA));
            news.setCategoryIds(categoryList.subList(1, categoryList.size()));
        }
        ZSQNewsInfoDto dto = new ZSQNewsInfoDto();
        dto.setId(id);
        dto.setOrgCode(news.getOrgCode());
        dto.setTitle(news.getTitle());
        String categoryId = news.getCategoryIds().get(0);
        CmsCategory category = categoryService.getById(categoryId);
        dto.setNewsType(category.getCode());
        dto.setOutsideLink(news.getUseCustomLinks());
        dto.setExternalLink(news.getCustomLinks());
        dto.setPublishTime(news.getEffectiveDate());
        dto.setBrief(news.getBriefIntroduction());
        dto.setContentDesc(news.getDescription());
        dto.setPicture(news.getTitleImgUrl());
        dto.setReviewStatus(news.getAuditStatus());
        dto.setReviewReason(news.getAuditMemo());
        dto.setOnShelf(news.getGrounding());
        return dto;
    }


    public void dockingZSQ(News news, String operation, Boolean onShelf) {
        try {
            String publishOrgCode = news.getOrgCode();
            SysOrg publishOrg = orgService.getByCode(publishOrgCode);
            String publishOrgCodeLink = publishOrg.getCodePrefix();
            List<String> publishOrgCodeLinkList = Arrays.asList(publishOrgCodeLink.split(COMMA));
            if ((publishOrgCodeLinkList.size() != 2 && publishOrgCodeLinkList.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgCodeLinkList.get(0))) {
                return;
            }
        } catch (Exception e) {
            log.warn("新闻发布组织编码信息获取异常，异常原因：【{}】！", e.getMessage());
            return;
        }
        ZSQDockingNewsRecord dockingRecord = new ZSQDockingNewsRecord();
        Long newsId = news.getId();
        dockingRecord.setNewsId(newsId);
        dockingRecord.setDockingType(DOCKING_TYPE_PUSH);
        dockingRecord.setDockingOperation(operation);
        dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_PENDING);
        dockingRecord.setDockingTime(LocalDateTime.now());
        dockingRecord.setRetry(Boolean.FALSE);
        Serializable userId = Oauth2Util.getUserId();
        String zwtUserId = userId == null ? null : ssoUserMappingService.getSSOUserIdByProviderAndZYZUserId(SsoCons.SsoProvider.ZWT.name(), (Long)userId);
        if (StringUtil.isEmpty(zwtUserId)) {
            dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
            dockingRecord.setDockingMsg("当前操作人" + (userId == null ? Strings.EMPTY : "{" + userId + "}") + "未获取政务通 userId ！");
            newsDockingRecordService.save(dockingRecord);
            NewsDockingEvent event = NewsDockingEvent.builder().dockingRecordId(dockingRecord.getId()).newsId(newsId).operation(operation).zyzUserId(Oauth2Util.getUserId()).onShelf(onShelf).build();
            dockingRecord.setDockingData(JSONObject.toJSONString(event));
            newsDockingRecordService.updateById(dockingRecord);
            return;
        }
        newsDockingRecordService.save(dockingRecord);
        NewsDockingEvent event = NewsDockingEvent.builder().dockingRecordId(dockingRecord.getId()).newsId(newsId).operation(operation).zwtUserId(zwtUserId).onShelf(onShelf).build();
        dockingRecord.setDockingData(JSONObject.toJSONString(event));
        newsDockingRecordService.updateById(dockingRecord);
        mqTemplate.send(TOPIC_NEWS_DOCKING, MessageBuilder.withPayload(event).build());
    }
}
