package com.fykj.scaffold.advanced_unit.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 文明单位-指标标准-单位打分
 * 查询参数
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明单位-指标项关联单位-筛选参数")
public class AuStandardIndexUnitParams extends BaseParams {

    @ApiModelProperty(value = "测评版本ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "测评版本ID不能为空")
    @MatchType(QueryType.EQ)
    private Long standardId;

    @ApiModelProperty(value = "测评项ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    private Long itemId;

    @ApiModelProperty(value = "指标ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    private Long indexId;

    @ApiModelProperty(value = "申请ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    private Long applyId;

    @ApiModelProperty(value = "单位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long belongUserId;
}
