package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto;
import com.fykj.scaffold.zyz.domain.dto.data_screen.ReqResActTeamDataDto;
import com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import fykj.microservice.core.base.IBaseService;
import fykj.microservice.core.beans.vo.IdTextVo;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

/**
 * 志愿者团队表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-30
 */
public interface IZyzTeamService extends IBaseService<ZyzTeam> {

    /**
     * 团队数据导入
     *
     * @param excel Excel文件
     * @return 失败记录列表
     */
    List<TeamImportDto> dataImport(MultipartFile excel);

    /**
     * 导入团队管理员相关逻辑处理
     *
     * @param teamIds 团队ID列表
     */
    void dealTeamAdminBatch(List<Long> teamIds);

    /**
     * 获取我的团队 分页查询
     * @param params
     * @return
     */
    IPage<ZyzTeam> getMyPages(ZyzTeamParams params);

    /**
     * 分页查询我的申请团队 --小程序
     * @param params
     * @return
     */
    IPage<MyTeamDto> getMyPagesForApi(ZyzTeamParams params);

    /**
     * 获取我的团队 分页查询 --小程序
     * @param params
     * @return
     */
    IPage<MyTeamDto> getMyTeamPagesForApi(ZyzTeamParams params);

    /**
     * 获取团队列表 --小程序
     * @param params
     * @return
     */
    IPage<TeamApiDto> pageForApi(ZyzTeamParams params);

    /**
     * 获取团队导出列表 --小程序
     * @param params
     * @return
     */
    List<TeamExportDto> getList(ZyzTeamParams params);


    /**
     * 修改重点
     * @return
     */
    boolean updateEmphasis(Long teamId,String emphasisType);

    /**
     * 保存团队，并在审核记录表添加记录
     * @param entity
     * @return
     */
    void saveTeam(ZyzTeam entity);

    /**
     * 再次审核
     * @param teamId
     * @return
     */
    boolean updateTeamStatus(Long teamId);

    /**
     * 查看申请状态
     * @param teamId
     * @return
     */
    String checkStatus(Long teamId);

    /**
     * 获取审核通过的团队
     * @param params
     * @return
     */
    IPage<ZyzTeam> getPassTeamPages(ZyzTeamParams params);

    /**
     * 获取团队列表
     * @param params
     * @return
     */
    List<ZyzTeam> getTeamList(ZyzTeamParams params);

    /**
     * 获取全部团队 -- 分页查询
     * @param params
     * @return
     */
    IPage<ZyzTeam> getPages(ZyzTeamParams params);

    /**
     * 停用团队，修改状态为false
     * @param id
     * @return
     */
    boolean stopUse(Long id);

    /**
     * 获取团队详情，根据团队id获取团队类型
     * @param id
     * @return
     */
    ZyzTeam getTeamById(Long id, Boolean infoChangeAudit);

    /**
     * 获取团队详情，根据团队id获取团队类型
     * @param id
     * @param num
     * @return
     */
    ZyzTeam getTeamByIdForApi(Long id,int num);

    /**
     * 修改团队，删除类型中间表，并新增团队类型
     * @param entity
     * @return
     */
    void updateTeam(ZyzTeam entity);

    /**
     * 团队信息变更
     * @param entity
     * @return
     */
    void updateTeamNeedAudit(ZyzTeam entity);

    /**
     * 团队信息变更撤回
     * @param id
     * @return
     */
    void callbackInfoChange(Long id);

    /**
     *删除团队，并去除团队管理员权限
     * @param idList
     * @return
     */
    boolean removeTeam(List<Long> idList);

    /**
     * 获取组织下面的团队
     * @return
     */
    List<ZyzTeam> getOrgTeamList();

    /**
     * 获取每天定时任务需要同步的数据
     * @return
     */
    List<Long> getNeedSync();

    /**
     * 获取每天定时任务需要SGB同步的数据
     * @return
     */
    List<Long> getSgbNeedSync();

    /**
     * 批量同步
     * @param teamIds
     */
    void syncBatch(List<Long> teamIds);

    /**
     * 获取团队注册待审核数量
     * @return
     */
    Integer getTeamWaitAuditNum();

    /**
     * 获取团队注册待审核数量
     * @return
     */
    Integer getTeamNumByOrgCode(String orgCode);

    /**
     * 更新团队成员数量
     * @param teamId
     */
    void updateTeamMemberNum(Long teamId);

    /**
     * 按组织分组统计数量
     * @param params
     * @return
     */
    List<OrgSumDto> sumNumByOrg(DataScreenParams params);

    /**
     * 获取大屏数据列表
     * @param params
     * @return
     */
    List<ReqResActTeamDataDto> getScreenDataList(DataScreenParams params);

    /**
     * 通过名称获取团队列表
     * @param name
     * @return
     */
    List<ZyzTeam> getTeamListByName(String name);

    /**
     * 获取组织团队数据统计
     * @param params
     * @return
     */
    List<OrgDataFormDto> getOrgTeamInfoSum(OrgDataReportFormParams params);

    /**
     * 获取组织本级团队统计
     * @param orgCode
     * @param start
     * @param end
     * @return
     */
    Integer getOrgSelfTeamSum(String orgCode, LocalDate start, LocalDate end);

    /**
     * 获取其他组织团队统计
     * @param start
     * @param end
     * @return
     */
    Integer getOtherOrgTeamSum(LocalDate start, LocalDate end);

    /**
     * 团队修改上级组织
     * @param id 团队id
     * @param orgCode 上级组织编码
     * @return
     */
    void updateOrgCode(Long id,String orgCode);

    /**
     * 获取团队 前端展示 -- 分页查询
     * @param params 参数
     * @return 团队分页列表
     */
    IPage<TeamApiDto> pageForFrontend(ZyzTeamParams params);

    /**
     * 获取团队统计信息
     * @param teamId 团队ID
     * @return 团队统计信息
     */
    TeamStatDto getTeamStat(Long teamId);

    /**
     * 获取团队id和名称
     * @return 团队列表
     */
    List<IdTextVo> listIdTextVo();

    /**
     * 根据团队名称获取团队列表（大型赛事授权）
     * @param teamName
     * @return
     */
    List<ZyzTeam> getTeamListByNameForBAGrant(String teamName);

    /**
     * 根据团队ID获取TeamApiDto
     *
     * @param teamId 团队ID
     * @return TeamApiDto
     */
    TeamApiDto getTeamApiDtoById(Long teamId);
}
