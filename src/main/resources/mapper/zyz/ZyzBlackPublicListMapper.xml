<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzBlackPublicListMapper">
    <select id="getBlackByMobile" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzBlackPublicList">
        select *
        from zyz_black_public_list
        where cancel_time
            > now()
          AND is_deleted = 0
          and mobile = #{mobile}
        ORDER BY cancel_time desc limit 1
    </select>
</mapper>
