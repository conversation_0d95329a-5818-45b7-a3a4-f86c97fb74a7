package com.fykj.scaffold.civilization_cultivation.controller.api;

import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow;
import com.fykj.scaffold.civilization_cultivation.service.ICivilizationCultivationShowService;
import com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation;
import com.fykj.scaffold.portal_website.service.ICivilizationCultivationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

@RestController
@RequestMapping("/api/civilization_cultivation/cockpit")
@Api(tags = "文明培育展示-驾驶舱接口")
public class CivilizationCultivationCockpitController {

    @Autowired
    private ICivilizationCultivationShowService civilizationCultivationShowService;

    @Autowired
    private ICivilizationCultivationService civilizationCultivationService;

    @ApiOperation("道德模范、时代新人、先进典型、园区好人驾驶舱数据")
    @GetMapping({"/cockpitData"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型（道德模范（CCT_DDMF）、时代新人（CCT_SDXR）、先进典型（CCT_XJDX）、园区好人（CCT_YQHR））", paramType = "query", dataType = "string")
    })
    public JsonResult<List<CivilizationCultivationDashboardDto>> cockpitData(@RequestParam String type) {
        return new JsonResult<>(civilizationCultivationShowService.cockpitData(type));
    }

    @ApiOperation("道德模范、时代新人、先进典型、园区好人驾驶舱数据(正式环境)")
    @GetMapping({"/cockpitDataProd"})
    public JsonResult<List<CivilizationCultivationDashboardDto>> cockpitDataProd() {
        return new JsonResult<>(civilizationCultivationShowService.cockpitDataProd());
    }

    @ApiOperation("道德模范、时代新人、先进典型、园区好人驾驶舱数据详情")
    @GetMapping({"/cockpitData/detail"})
    public JsonResult<CivilizationCultivationShow> cockpitDataDetail(@RequestParam Long id) {
        return new JsonResult<>(civilizationCultivationShowService.getDetail(id));
    }

    @ApiOperation("团队风采、志愿风采驾驶舱数据")
    @GetMapping({"/mienCockpitData"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型（志愿风采（PERSONAL_MIEN）、团队风采（TEAM_MIEN））", paramType = "query", dataType = "string")
    })
    public JsonResult<List<CivilizationCultivationDashboardDto>> mienCockpitData(@RequestParam String type) {
        return new JsonResult<>(civilizationCultivationService.mienCockpitData(type));
    }

    @ApiOperation("团队风采、志愿风采驾驶舱数据详情")
    @GetMapping({"/mienCockpitData/detail"})
    public JsonResult<CivilizationCultivation> mienCockpitDataDetail(@RequestParam Long id) {
        return new JsonResult<>(civilizationCultivationService.getById(id));
    }
}
