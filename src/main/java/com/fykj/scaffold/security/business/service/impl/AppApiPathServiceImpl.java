package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.domain.dto.ApiPathDto;
import com.fykj.scaffold.security.business.domain.entity.AppApiPath;
import com.fykj.scaffold.security.business.domain.entity.SysApi;
import com.fykj.scaffold.security.business.mapper.AppApiPathMapper;
import com.fykj.scaffold.security.business.service.IAppApiPathService;
import com.fykj.scaffold.security.business.service.ISysApiService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 应用接口权限
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2021-05-28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AppApiPathServiceImpl extends BaseServiceImpl<AppApiPathMapper, AppApiPath> implements IAppApiPathService {

//    @Autowired
//    private AppApiServiceImpl appApiService;

    @Autowired
    private ISysApiService iSysApiService;

    @Override
    public boolean savePath(ApiPathDto dto) {
        String appId = dto.getAppId();
        baseMapper.deleteAppApiPathByAppId(appId);
        if (CollectionUtils.isEmpty(dto.getPathIdList())) {
            return true;
        }
        Map<Serializable, SysApi> apiMap = iSysApiService.list().stream()
                .collect(Collectors.toMap(SysApi::getId, appApi -> appApi));
        List<AppApiPath> list = dto.getPathIdList().stream().map(it -> {
            AppApiPath path = new AppApiPath();
            path.setAppId(dto.getAppId());
            path.setApiId(Long.valueOf(it));
            SysApi api = apiMap.get(path.getApiId());
            path.setName(api.getApiName());
            path.setPath(api.getApiPath());
            return path;
        }).collect(Collectors.toList());
        return saveBatch(list);
    }

    @Override
    public List<String> getApiIdListByAppId(String appId) {
        List<AppApiPath> paths = getByAppId(appId);
        return paths.stream().map(AppApiPath::getApiId).map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public List<AppApiPath> getByAppId(String appId) {
        return lambdaQuery().eq(AppApiPath::getAppId,appId).list();
    }
}
