package com.fykj.scaffold.sync.service;

import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto;

import java.util.List;

/**
 * 市区同步数据业务
 */
public interface IPlatformSyncService {
    /**
     * 同步志愿者数据
     */
    ZyzSyncLog syncVolunteer(long volunteerId, long syncBillId);

    /**
     * 同步团队数据
     */
    ZyzSyncLog syncTeam(long teamId, long syncBillId);

    /**
     * 同步团队成员数据
     */
    ZyzSyncLog syncTeamMember(long teamId, long volunteerId, long syncBillId);

    /**
     * 同步cms
     */
    ZyzSyncLog syncCms(long contentId, long syncBillId);

    /**
     * 同步需求
     */
    ZyzSyncLog syncRequirement(long requirementId, long syncBillId, String saveType);

    /**
     * 同步资源
     */
    ZyzSyncLog syncResource(long resourceId, long syncBillId, String saveType);

    /**
     * 同步职能部门
     */
    ZyzSyncLog syncDep(long depId, long syncBillId);

    /**
     * 同步实践阵地
     */
    ZyzSyncLog syncBase(long baseId, long syncBillId);

    /**
     * 同步实践点
     */
    ZyzSyncLog syncSpot(long spotId, long syncBillId);

    /**
     * 同步活动
     *
     * @param activityId
     * @param syncBillId
     * @return
     */
    ZyzSyncLog syncActivity(long activityId, long syncBillId);

    /**
     * 同步活动(按时间段分多个活动同步)
     *
     * @param actTimePeriodId
     * @param syncBillId
     * @return
     */
    ZyzSyncLog syncActTimePeriod(long actTimePeriodId, long syncBillId);

    /**
     * 同步活动发布招募
     *
     * @param activityId
     * @param syncBillId
     * @return
     */
    ZyzSyncLog syncActivityRecruit(long activityId, long syncBillId);

    /**
     * 同步活动加入招募
     *
     * @param apply
     * @return
     */
    ZyzSyncLog syncActivityMember(ActSyncRecordDto apply);

    /**
     * 同步活动加入招募
     *
     * @param applies
     * @return
     */
    void syncActivityMember(List<ActSyncRecordDto> applies);


    /**
     * 同步服务时长
     *
     * @param apply
     * @return
     */
    ZyzSyncLog syncServiceTime(ActSyncRecordDto apply);

    /**
     * 同步服务时长
     *
     * @param applies
     * @return
     */
    void syncServiceTime(List<ActSyncRecordDto> applies);

    /**
     * 消息发送
     * @param recordId
     */
    void msgSend(Long recordId);

    /**
     * 同步实践单
     *
     * @param orderId
     * @param syncBillId
     * @param type
     * @return
     */
    ZyzSyncLog syncOrder(long orderId, long syncBillId, String type);

    /**
     * 社区同名团队同步
     * @param orgTeamId 团队id
     * @param syncBillId 交易id
     * @return 同步log
     */
    ZyzSyncLog syncOrgTeam(long orgTeamId, long syncBillId);

    /**
     * 活动公示同步
     *
     * @param actShowId  活动公示id
     * @param syncBillId 订单id
     * @return 同步记录
     */
    ZyzSyncLog syncActShow(long actShowId, long syncBillId);

    /**
     * 活动公示同步
     *
     * @param scheduleId 计划id
     * @param syncBillId 订单id
     * @return 同步记录
     */
    ZyzSyncLog syncActSchedule(long scheduleId, long syncBillId);


    /**
     * 活动计划同步
     *
     * @param scheduleActPlanId 阵地计划活动关联id
     * @param syncBillId        订单id
     * @return 同步记录
     */
    ZyzSyncLog syncScheduleActPlan(long scheduleActPlanId, long syncBillId);
}

