package com.fykj.scaffold.civilization_cultivation.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文明培育征集-管理
 * 查询参数
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明培育征集-管理查询参数")
public class CivilizationCultivationCollectParams extends BaseParams {

    private static final long serialVersionUID = 1629004693183176906L;

    @ApiModelProperty("文明培育类型")
    @MatchType(value = QueryType.EQ, fieldName = "type")
    private String type;

    @ApiModelProperty("联系电话")
    @MatchType(value = QueryType.LIKE, fieldName = "phone")
    private String phone;

    @ApiModelProperty("所属街道社区")
    @MatchType(value = QueryType.EQ, fieldName = "belong_community_code")
    private String orgCode;

    @ApiModelProperty("年份")
    @MatchType(value = QueryType.EQ, fieldName = "year")
    private Integer year;

    @ApiModelProperty("状态")
    @MatchType(value = QueryType.EQ, fieldName = "status")
    private String status;

    @ApiModelProperty("推荐者")
    private Long creator;

    @ApiModelProperty("核实街道code")
    @MatchType(value = QueryType.EQ, fieldName = "belong_street_code")
    private String checkStreetOrg;

    @ApiModelProperty("可查看数据状态")
    @MatchType(value = QueryType.IN, fieldName = "status")
    private List<String> viewableStatus;
}
