<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzTeamGrantBigActivityMapper">

    <select id="pageForGrantRecords" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeamGrantBigActivity">
         SELECT
            ztgba.id AS id,
            zt.id AS teamId,
            zt.name AS teamName,
            IF(zv.name IS NULL, zt.admin_name, zv.name) AS teamAdminName,
            IF(zv.phone IS NULL, zt.admin_contact, zv.phone) AS teamAdminContact,
            ztgba.grant_time AS grantTime,
            ztgba.status
         FROM zyz_team_grant_big_activity ztgba
         LEFT JOIN zyz_team zt ON ztgba.team_id = zt.id AND zt.is_deleted = 0
         LEFT JOIN zyz_volunteer_team zvt ON zvt.team_id = zt.id AND zvt.duty_type = 'duty_type_admin' AND zvt.is_deleted = 0
         LEFT JOIN zyz_volunteer zv ON zvt.volunteer_id = zv.id AND zv.is_deleted = 0
         WHERE ztgba.is_deleted = 0 AND zt.id IS NOT NULL
         <if test="params.keyword != null and params.keyword != ''">
             AND zt.name LIKE CONCAT('%', #{params.keyword}, '%')
         </if>
         ORDER BY ztgba.grant_time DESC
    </select>

    <select id="getGrantedTeam" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeamGrantBigActivity">
        SELECT
            zt.id AS teamId,
            zt.name AS teamName
        FROM zyz_team_grant_big_activity ztgba
        LEFT JOIN zyz_team zt ON ztgba.team_id = zt.id AND zt.is_deleted = 0
        WHERE ztgba.is_deleted = 0 AND zt.id IS NOT NULL AND ztgba.status = 1
        <if test="teamName != null and teamName != ''">
            AND zt.name LIKE CONCAT('%', #{teamName}, '%')
        </if>
        ORDER BY ztgba.grant_time DESC
    </select>
</mapper>
