package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2021-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class AppParams extends BaseParams {

    @MatchType(value = QueryType.LIKE)
    @ApiModelProperty("应用名称模糊查询")
    private String name;

    @MatchType(value = QueryType.EQ)
    @ApiModelProperty("应用类型")
    private String type;

}
