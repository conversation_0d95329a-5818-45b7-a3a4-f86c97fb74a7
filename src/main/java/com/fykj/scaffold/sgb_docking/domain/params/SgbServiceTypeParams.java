package com.fykj.scaffold.sgb_docking.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 苏州志愿者平台服务领域查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("苏州志愿者平台服务领域查询参数")
public class SgbServiceTypeParams extends BaseParams {

    private static final long serialVersionUID = 1L;

    /**
     * 类型编号
     */
    @ApiModelProperty("类型编号")
    @MatchType(QueryType.LIKE)
    private String typeId;

    /**
     * 类型名称
     */
    @ApiModelProperty("类型名称")
    @MatchType(QueryType.LIKE)
    private String typeName;

    /**
     * 父级类型编号
     */
    @ApiModelProperty("父级类型编号")
    @MatchType(QueryType.EQ)
    private String fatherId;
    
    /**
     * 节点路径
     */
    @ApiModelProperty("节点路径")
    @MatchType(QueryType.LIKE)
    private String nodePath;
} 