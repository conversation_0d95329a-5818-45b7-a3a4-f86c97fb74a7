package com.fykj.scaffold.security.oauth2.config;

import com.fykj.scaffold.security.oauth2.access.MyUserDetailsServiceImpl;
import com.fykj.scaffold.security.oauth2.onecode.OneCodeAuthenticationProvider;
import com.fykj.scaffold.security.oauth2.onecode.OneCodeTokenGranter;
import com.fykj.scaffold.security.oauth2.sms.SmsCodeAuthenticationProvider;
import com.fykj.scaffold.security.oauth2.sms.SmsCodeTokenGranter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.CompositeTokenGranter;
import org.springframework.security.oauth2.provider.TokenGranter;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 *
 * 授权码模式（authorization code）
 * 最常用，可用于第三方应用授权、SSO登录实现等。
 * 简化模式（implicit）
 * 一般不用
 * 密码模式（resource owner password credentials）
 * 客户端模式（client credentials）
 * 一般用于后台服务接口间的认证

 */
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfiguration extends AuthorizationServerConfigurerAdapter {
    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    RedisConnectionFactory redisConnectionFactory;
    @Autowired
    private MyUserDetailsServiceImpl userDetailsService;
    @Autowired
    private SmsCodeAuthenticationProvider smsCodeAuthenticationProvider;
    @Autowired
    private OneCodeAuthenticationProvider oneCodeAuthenticationProvider;

    @Bean
    TokenStore tokenStore(){
        return new RedisTokenStore(redisConnectionFactory);
    }

    @Autowired
    private DataSource dataSource;

    @Bean
    public ClientDetailsService clientDetails() {
        return new JdbcClientDetailsService(dataSource);
    }

    /**
     * 配置授权资源
     * @param clients
     * @throws Exception
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        clients.withClientDetails(clientDetails());
    }

    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {

        endpoints.allowedTokenEndpointRequestMethods(HttpMethod.GET, HttpMethod.POST, HttpMethod.OPTIONS);
        endpoints
                .tokenStore(tokenStore())
                .authenticationManager(authenticationManager)
                //必须手动设置userdetailsservice,否则refresh_token无法使用
                .userDetailsService(userDetailsService)
                //该字段设置设置refresh token是否重复使用,true:reuse;false:no reuse.
                //默认设置refresh token过期时间比token长一分钟
                .reuseRefreshTokens(false)
                .tokenGranter(tokenGranter(endpoints));
    }

    @Override
    public void configure(AuthorizationServerSecurityConfigurer oauthServer) throws Exception {
        //允许表单认证
        oauthServer.allowFormAuthenticationForClients();
        //tokenKeyAccess 启用jwt时启动
        oauthServer.tokenKeyAccess("permitAll()")
                .checkTokenAccess("permitAll()");
    }

    private TokenGranter tokenGranter(final AuthorizationServerEndpointsConfigurer endpoints) {
        List<TokenGranter> granters = new ArrayList<TokenGranter>(Collections.singletonList(endpoints.getTokenGranter()));
        SmsCodeTokenGranter smsCodeTokenGranter = new SmsCodeTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), authenticationManager);
        granters.add(smsCodeTokenGranter);
        smsCodeAuthenticationProvider.setUserServiceDetail(userDetailsService);

        OneCodeTokenGranter oneCodeTokenGranter = new OneCodeTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), authenticationManager);
        granters.add(oneCodeTokenGranter);
        oneCodeAuthenticationProvider.setUserServiceDetail(userDetailsService);

        return new CompositeTokenGranter(granters);
    }
}
