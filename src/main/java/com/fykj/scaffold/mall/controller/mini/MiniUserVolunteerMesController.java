package com.fykj.scaffold.mall.controller.mini;

import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/mini/goods/mes")
@Api(tags = "微信小程序志愿者信息接口")
public class MiniUserVolunteerMesController {

    @Autowired
    private IMallScoreGoodsService mallScoreGoodsService;

    @Autowired
    private ISysOrgService sysOrgService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @ApiOperation("兑换页面详情")
    @GetMapping("/getExchangeOrder")
    public Result getExchangeOrder(@RequestParam Long id) {
        return new JsonResult<>(mallScoreGoodsService.getOrderFormDto(id));
    }

    @ApiOperation("获取登录账号所属的分协会组织机构--小程序端")
    @GetMapping("/getVolunteerPerOrgForMini")
    public JsonResult<SysOrg> getVolunteerPerOrgForMini() {
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        SysOrg org = sysOrgService.lambdaQuery().eq(SysOrg::getCode, volunteer.getOrgCode()).one();
        if (ObjectUtil.isEmpty(org)) {
            return new JsonResult<>(null);
        }
        if (org.getParentId() == null) {
            return new JsonResult<>(null);
        }
        SysOrg parentOrg = sysOrgService.getOrgById(org.getParentId());
        if (ObjectUtil.isEmpty(parentOrg)) {
            return new JsonResult<>(null);
        }
        return new JsonResult<>(parentOrg);
    }

}
