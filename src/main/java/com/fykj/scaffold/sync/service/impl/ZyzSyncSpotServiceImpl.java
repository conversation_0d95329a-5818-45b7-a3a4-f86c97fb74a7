package com.fykj.scaffold.sync.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.ImageUtil;
import com.fykj.scaffold.sync.domain.dto.SyncSpotExportDto;
import com.fykj.scaffold.sync.domain.dto.SyncSpotImportDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDict;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncSpot;
import com.fykj.scaffold.sync.domain.params.ZyzSyncSpotParams;
import com.fykj.scaffold.sync.mapper.ZyzSyncSpotMapper;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictService;
import com.fykj.scaffold.sync.service.IZyzSyncSpotService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.util.SystemUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;


/**
 * 市平台同步实践点
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-06
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzSyncSpotServiceImpl extends BaseServiceImpl<ZyzSyncSpotMapper, ZyzSyncSpot> implements IZyzSyncSpotService {

    @Autowired
    private IZyzSyncRegionDictService regionDictService;

    @Override
    public IPage<ZyzSyncSpot> page(BaseParams params) {
        return super.page(params).convert(it -> {
            ZyzSyncRegionDict regionDict = regionDictService.getByRegionCode(it.getRegionCode());
            it.setRegionName(regionDict != null ? regionDict.getRegionName() : "");
            DictTransUtil.trans(it);
            return it;
        });
    }

    @Override
    public void saveSpot(ZyzSyncSpot spot) {
        //图片格式验证
        ImageUtil.checkPic(spot.getPicLogoUrl());
        spot.setRegionCode("320508");
        spot.setIsSync(Cons.PlatformSyncState.WAIT_SYNC);
        save(spot);
    }

    @Override
    public void updateSpot(ZyzSyncSpot spot) {
        //图片格式验证
        ImageUtil.checkPic(spot.getPicLogoUrl());
        //修改后同步状态修改为待同步
        spot.setIsSync(Cons.PlatformSyncState.WAIT_SYNC);
        super.updateById(spot);
    }

    @Override
    public void changeOnShelve(Long id) {
        ZyzSyncSpot spot = lambdaQuery().eq(ZyzSyncSpot::getId, id).one();
        if (spot == null) {
            throw new BusinessException(ResultCode.FAIL, "实践点不存在！");
        }
        spot.setOnShelve(!spot.getOnShelve());
        super.updateById(spot);
    }

    @Override
    public List<SyncSpotImportDto> dataImport(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请上传文件！");
        }
        List<SyncSpotImportDto> dataList = ExcelUtil.readExcel(excel, SyncSpotImportDto.class, 0, 1);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        List<SyncSpotImportDto> failList = new ArrayList<>();
        List<ZyzSyncSpot> needSave = new ArrayList<>();
        dataList.forEach(it -> {
            String mobile = it.getContactNo();
            if (StringUtils.isNotEmpty(mobile) && !isMobile(mobile) && !is8lPhone(mobile)) {
                it.setFailReason("负责人电话格式不正确，须为手机号或带区号的座机号！");
                failList.add(it);
                return;
            }
            try {
                ZyzSyncSpot spot = new ZyzSyncSpot();
                BeanUtils.copyProperties(it, spot);
                spot.setRegionCode("320508");
                spot.setIsSync(Cons.PlatformSyncState.WAIT_SYNC);
                needSave.add(spot);
            } catch (Exception e) {
                it.setFailReason(e.getMessage());
                failList.add(it);
            }
        });
        if (!needSave.isEmpty()) {
            super.saveBatch(needSave);
        }
        return failList;
    }

    /**
     * 中国手机号（宽松匹配）
     * @param phone 手机号
     * @return 是否有效
     */
    private boolean isMobile(String phone) {
        return Pattern.matches("^1[0-9]{10}$", phone);
    }

    /**
     * 国内座机号（带区号，严格匹配）
     * @param phone 座机号
     * @return 是否有效
     */
    private boolean is8lPhone(String phone) {
        return Pattern.matches("^(0\\d{2,3})-?(\\d{7,8})$", phone);
    }

    @Override
    public List<SyncSpotExportDto> export(ZyzSyncSpotParams params) {
        List<ZyzSyncSpot> records = super.list(params);
        if (records == null || records.isEmpty()) {
            return null;
        }
        List<SyncSpotExportDto> result = new ArrayList<>();
        records.forEach(it -> {
            ZyzSyncRegionDict regionDict = regionDictService.getByRegionCode(it.getRegionCode());
            it.setRegionName(regionDict != null ? regionDict.getRegionName() : "");
            SyncSpotExportDto dto = new SyncSpotExportDto();
            BeanUtils.copyProperties(it, dto);
            String picture = dto.getPicLogoUrl();
            if (StringUtils.isNotEmpty(picture)) {
                dto.setPicLogoUrl(SystemUtil.getProperty("httpAttachmentUrl") + picture);
            }
            result.add(dto);
        });
        return result;
    }
}
