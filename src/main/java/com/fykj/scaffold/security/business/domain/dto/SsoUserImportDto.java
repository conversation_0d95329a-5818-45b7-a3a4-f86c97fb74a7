package com.fykj.scaffold.security.business.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 2025/5/8
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class SsoUserImportDto {

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "政务通id")
    private String userId;

    @ExcelProperty(value = "手机号")
    private String mobile;

    @ExcelProperty(value = "街道")
    private String street;

    @ExcelProperty(value = "社区/工作站/民联所")
    private String community;

    @ExcelProperty(value = "失败原因")
    private String reason;

    @ExcelIgnore
    private String streetOrgCode;

    @ExcelIgnore
    private String communityOrgCode;

    @ExcelIgnore
    private Long platformUserId;
}
