package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.SysVillage;
import com.fykj.scaffold.zyz.domain.params.SysVillageParams;
import com.fykj.scaffold.zyz.service.ISysVillageService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 小区表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/admin/sys/village")
public class SysVillageController extends BaseController<ISysVillageService, SysVillage, SysVillageParams> {
    @ApiOperation(value = "小区信息导入")
    @PostMapping(value = "/uploadExcel")
    public Result uploadExcel(@RequestParam("excel") MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            return new Result(ResultCode.BAD_REQUEST.code(), "请选择要上传的文件");
        }
        return new JsonResult<>(baseService.uploadExcel(excel));
    }
    @ApiOperation("小区分页查询")
    @PostMapping({"/getPages"})
    public JsonResult<IPage<SysVillage>> pagesForAudit(@RequestBody(required = false) SysVillageParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new SysVillageParams();
        }
        IPage<SysVillage> result = this.baseService.getPages(params);
        return new JsonResult<>(result);
    }
}
