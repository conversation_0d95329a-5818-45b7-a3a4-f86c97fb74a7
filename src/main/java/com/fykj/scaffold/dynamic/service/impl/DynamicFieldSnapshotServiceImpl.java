package com.fykj.scaffold.dynamic.service.impl;

import com.fykj.scaffold.dynamic.domain.entity.DynamicField;
import com.fykj.scaffold.dynamic.domain.entity.DynamicFieldSnapshot;
import com.fykj.scaffold.dynamic.mapper.DynamicFieldSnapshotMapper;
import com.fykj.scaffold.dynamic.service.IDynamicFieldService;
import com.fykj.scaffold.dynamic.service.IDynamicFieldSnapshotService;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 字段信息快照-服务实现类
 *
 * @date 2025-05-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DynamicFieldSnapshotServiceImpl
        extends BaseServiceImpl<DynamicFieldSnapshotMapper, DynamicFieldSnapshot>
        implements IDynamicFieldSnapshotService {

    @Autowired
    private IDynamicFieldService dynamicFieldService;

    /**
     * 根据snapshotId和modelId查找字段list
     *
     * @param snapshotId 快照id
     * @param moduleCode 模块code
     * @return 字段list
     */
    @Override
    public List<DynamicFieldSnapshot> getBySnapshotIdAndModuleCode(Long snapshotId, String moduleCode) {
        if (ObjectUtils.isEmpty(snapshotId)) {
            // 降级处理：返回 commonFlag=true 的字段
            return getCommonFlagTrueFields(moduleCode);
        }
        
        return lambdaQuery()
                .eq(DynamicFieldSnapshot::getSnapshotId, snapshotId)
                .eq(DynamicFieldSnapshot::getModuleCode, moduleCode)
                .orderByAsc(DynamicFieldSnapshot::getFieldOrder)
                .list();
    }

    /**
     * 根据snapshotId和modelId查找字段list(过滤系统原有字段
     *
     * @param snapshotId 快照id
     * @param moduleCode 模块code
     * @return 字段list
     */
    @Override
    public List<DynamicFieldSnapshot> getBySnapshotIdAndModuleCodeForApi(Long snapshotId, String moduleCode) {
        return getBySnapshotIdAndModuleCode(snapshotId, moduleCode).stream()
                .filter(x -> Boolean.FALSE.equals(x.getSystemFlag()))
                .collect(Collectors.toList());
    }

    /**
     * 获取 commonFlag=true 的字段，并转换为 DynamicFieldSnapshot
     * 
     * @param moduleCode 模块编码
     * @return 字段快照列表
     */
    private List<DynamicFieldSnapshot> getCommonFlagTrueFields(String moduleCode) {
        // 查询 commonFlag=true 的字段
        List<DynamicField> commonFields = dynamicFieldService.lambdaQuery()
                .eq(DynamicField::getModuleCode, moduleCode)
                .eq(DynamicField::getCommonFlag, true)
                .list();
                
        if (commonFields.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 设置排序序号
        AtomicInteger order = new AtomicInteger(1);
        
        // 转换为 DynamicFieldSnapshot
        return commonFields.stream().map(field -> {
            DynamicFieldSnapshot snapshot = new DynamicFieldSnapshot();
            BeanUtils.copyProperties(field, snapshot);
            snapshot.setSnapshotId(null);
            snapshot.setFieldOrder(order.getAndIncrement());
            snapshot.setRequireFlag(true);
            return snapshot;
        }).collect(Collectors.toList());
    }

}
