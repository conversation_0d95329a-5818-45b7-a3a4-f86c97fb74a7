package com.fykj.volunteer.sync.util.domain.sync_dep;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <dp>
 *   <info>
 *     <code>SUCCESS</code>
 *     <msg>同步成功</msg>
 *     <uniCode>1001</uniCode>
 *     <dpId>13977</dpId>
 *   </info>
 * </dp>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncDepResult {
    /**
     * 职能部门编号
     */
    private String dpId;
    /**
     * 唯一编号
     */
    private String uniCode;
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "dp")
    public static class Dp {
        private List<SyncDepResult> info;
    }
}
