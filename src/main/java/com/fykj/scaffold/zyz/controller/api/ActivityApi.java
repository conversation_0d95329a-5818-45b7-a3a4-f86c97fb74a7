package com.fykj.scaffold.zyz.controller.api;

import com.fykj.scaffold.zyz.domain.dto.ActivityTimePeriodShowDto;
import com.fykj.scaffold.zyz.domain.dto.ActivityTimePeriodStatDto;
import com.fykj.scaffold.zyz.domain.dto.VolunteerServiceLongDetailDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.params.ActivityTimePeriodShowApiParams;
import com.fykj.scaffold.zyz.domain.params.ActivityTimePeriodStatParams;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodShowService;
import exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import javax.validation.Valid;
import java.util.List;

/**
 * 团队微信前端接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/api/zyz/activity")
@Api(tags = "用户端--活动(无需登录前端接口")
public class ActivityApi {

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzActivityTimePeriodShowService activityTimePeriodShowService;

    @Autowired
    private IZyzActivityApplyService activityApplyService;

    @ApiOperation(value = "获取活动二维码路径")
    @GetMapping(value = "/getActivityQrCode")
    public Result getActivityQrCode(String appid, long activityId) {
        ZyzActivity activity = activityService.getById(activityId);
        if (activity != null && StringUtil.isNotEmpty(activity.getQrCodeUrl())) {
            return new JsonResult<>(activity.getQrCodeUrl());
        }
        return new JsonResult<>(activityService.createQrCode(appid, activityId));
    }

    @ApiOperation(value = "获取活动公示信息列表")
    @GetMapping(value = "/getActivityShowList")
    public JsonResult<List<ActivityTimePeriodShowDto>> getActivityShowList(
            @Valid ActivityTimePeriodShowApiParams params) {
        return new JsonResult<>(activityTimePeriodShowService.pageForApi(params));
    }

    @ApiOperation(value = "获取活动时间段统计信息")
    @GetMapping(value = "/getTimePeriodStats")
    public JsonResult<List<ActivityTimePeriodStatDto>> getTimePeriodStats(
            @Valid ActivityTimePeriodStatParams params) {
        return new JsonResult<>(activityApplyService.getTimePeriodStats(params));
    }

    @ApiOperation(value = "获取志愿者服务时长详情")
    @GetMapping(value = "/getVolunteerServiceLongDetails")
    public JsonResult<List<VolunteerServiceLongDetailDto>> getVolunteerServiceLongDetails(
            @Valid ActivityTimePeriodStatParams params) {
        if(params.getTimePeriodId() == null) {
            throw new BusinessException(ResultCode.NOT_VALID, "活动时间段必传");
        }
        return new JsonResult<>(activityApplyService.getVolunteerServiceLongDetails(params));
    }

}
