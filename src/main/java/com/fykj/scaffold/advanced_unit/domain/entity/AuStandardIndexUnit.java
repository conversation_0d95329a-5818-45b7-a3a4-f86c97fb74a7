package com.fykj.scaffold.advanced_unit.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.dto.AttachmentDto;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文明单位-指标标准-单位打分
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "au_standard_index_unit", autoResultMap = true)
public class AuStandardIndexUnit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 测评版本ID
     */
    @TableField("standard_id")
    @ApiModelProperty(value = "测评版本ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "测评版本ID不能为空")
    private Long standardId;

    /**
     * 测评项ID
     */
    @TableField("item_id")
    @ApiModelProperty(value = "测评项ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "测评项ID不能为空")
    private Long itemId;

    /**
     * 指标ID
     */
    @TableField("index_id")
    @ApiModelProperty(value = "指标ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "指标ID不能为空")
    private Long indexId;

    /**
     * 申请ID
     */
    @TableField("apply_id")
    @ApiModelProperty(value = "申请ID")
    private Long applyId;

    @TableField("org_id")
    @ApiModelProperty(value = "单位ID")
    @NotNull(message = "单位ID不能为空")
    private Long orgId;


    /**
     * 自评总分
     */
    @TableField("self_score")
    @ApiModelProperty(value = "自评总分")
    private Integer selfScore = 0;

    /**
     * 复评打分
     */
    @TableField("final_score")
    @ApiModelProperty(value = "复评打分")
    private Integer finalScore = 0;

    /**
     * 自评次数
     */
    @TableField("self_count")
    @ApiModelProperty(value = "自评次数")
    private Integer selfCount;

    /**
     * 复评次数
     */
    @TableField("final_count")
    @ApiModelProperty(value = "复评次数")
    private Integer finalCount;

    /**
     * 附件
     */
    @TableField(value = "attachment", typeHandler = AttachmentDto.AttachmentListTypeHandler.class)
    @ApiModelProperty(value = "附件")
    private List<AttachmentDto> attachment;

    /**
     * 爬取ID
     */
    @TableField("spider_id")
    @ApiModelProperty(value = "原始ID")
    private String spiderId;

    @TableField(exist = false)
    @ApiModelProperty(value = "指标名称")
    private String name;

    @TableField(exist = false)
    @ApiModelProperty(value = "指标分值")
    private Integer score;

    @TableField(exist = false)
    @ApiModelProperty(value = "测评方法及内容")
    private String content;

    @TableField(exist = false)
    @ApiModelProperty(value = "顺序")
    private Integer orderNum;

}
