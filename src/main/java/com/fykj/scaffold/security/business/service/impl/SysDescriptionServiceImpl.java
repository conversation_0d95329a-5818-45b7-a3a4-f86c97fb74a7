package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.domain.entity.SysDescription;
import com.fykj.scaffold.security.business.mapper.SysDescriptionMapper;
import com.fykj.scaffold.security.business.service.ISysDescriptionService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;


/**
 * 系统规则配置
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysDescriptionServiceImpl extends BaseServiceImpl<SysDescriptionMapper, SysDescription> implements ISysDescriptionService {

    @Override
    public void saveOrUpdateDescription(SysDescription description) {
        boolean isExist = lambdaQuery().eq(SysDescription::getCode, description.getCode()).ne(description.getId() != null, BaseEntity::getId, description.getId()).exists();
        if (isExist) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "code已存在，请重试");
        }
        saveOrUpdate(description);
    }
}