package com.fykj.scaffold.portal_website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.portal_website.domain.entity.Video;
import com.fykj.scaffold.portal_website.domain.params.VideoParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  视频资源Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface VideoMapper extends BaseMapper<Video> {

    /**
     * 通过id获取资源（加上行锁）
     * @param ids
     * @return
     */
    List<Video> getForUpdate(@Param("ids") List<String> ids);

    /**
     * 分页查询视频资源
     * @param page
     * @param params
     * @return
     */
    IPage<Video> pageForVideo(Page<Video> page, @Param("params")VideoParams params);
}
