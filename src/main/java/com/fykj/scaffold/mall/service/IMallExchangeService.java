package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.*;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.params.MallExchangeParams;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import fykj.microservice.core.base.IBaseService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
public interface IMallExchangeService extends IBaseService<MallExchange> {
    /**
     * 分页查询--后端页面
     */
    IPage<MallExchange> getPage(MallExchangeParams params);

    List<MallExchangeDto> getExchangeList(MallExchangeParams params);

    boolean exchange(Long id, Long addressId, String orderNo, Boolean isEms);

    /**
     * pad兑换
     *
     * @param id
     * @return
     */
    boolean padGoodsExchange(Long id, String orderNo, String stationCode);

    boolean cancelExchange(Long id, boolean isApp);

    boolean writeOffVerificationCode(Long exchangeId, String verificationCode);

    IPage<MallExchange> pageForApi(MallExchangeParams params);

    MallExchange getExchangeForApi(Long id);

    /**
     * 积分兑换实物
     *
     * @param dto 参数
     */
    void exchangeByMachine(PointExchangeDto dto);


    /**
     * 是否同意兑换
     *
     * @param orderNum 订单号
     * @return 是否
     */
    MachineResultDto isAcceptOrder(String orderNum);

    /**
     * 机器兑换接口回调
     *
     * @param orderNum 订单
     * @param result   结果
     * @return 接口结果
     */
    MachineResultDto outStockResult(String orderNum, int result);

    /**
     * 自动取消订单
     */
    void autoCancelExpireOrder();

    /**
     * 积分退还提示,临时提示
     */
    Map<String, Object> pointCardReturnTip();


    /**
     * 星光闪耀商品兑换
     *
     * @param id 商品id
     * @return 兑换记录
     */
    MallStarExchangeDto exchangeForStar(Long id, String orderNo);

    /**
     * 兑换验证
     *
     * @param goods     商品信息
     * @param volunteer 志愿者信息
     */
    void validateStartGoodsExchanged(MallScoreGoods goods, ZyzVolunteer volunteer);

    /**
     * 兑换星光闪耀商品
     *
     * @param starGoodsExchangeDto 兑换信息
     * @return 兑换记录
     */
    MallExchange starGoodsExchange(StarGoodsExchangeDto starGoodsExchangeDto);

}

