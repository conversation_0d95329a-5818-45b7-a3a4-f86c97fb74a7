package com.fykj.scaffold.zsq_docking.log.controller;

import com.fykj.scaffold.zsq_docking.log.domain.dto.RegionAllDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingRegionAllRecord;
import com.fykj.scaffold.zsq_docking.log.domain.params.ZSQDockingRegionAllRecordParams;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingRegionAllRecordService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

@RestController
@RequestMapping("/admin/zsq_docking_region_all_record")
@Api(tags = "知社区活动所属区域对接记录")
public class ZSQDockingRegionAllRecordController extends BaseController<IZSQDockingRegionAllRecordService, ZSQDockingRegionAllRecord, ZSQDockingRegionAllRecordParams> {

    @ApiOperation("获取全部方法")
    @GetMapping({"/getRegionAllDockingRecords"})
    public JsonResult<List<ZSQDockingRegionAllRecord>> getRegionAllDockingRecords() {
        return new JsonResult<>(this.baseService.getRegionAllDockingRecords());
    }

    @ApiOperation("失败重试")
    @GetMapping({"/retry"})
    public Result retry(@RequestParam Long recordId) {
        baseService.retry(recordId);
        return OK;
    }

    @ApiOperation("获取对接记录详情")
    @GetMapping({"/getDockingRecordDetail"})
    public JsonResult<RegionAllDockingRecordDetailDto> getDockingRecordDetail(@RequestParam Long recordId) {
        return new JsonResult<>(baseService.getDockingRecordDetail(recordId));
    }
}
