package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 消息模版
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("message_template")
public class MsgTemplate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模块code
     */
    @TableField("module")
    @ApiModelProperty(value = "模块code")
    @DictTrans
    private String module;

    /**
     * 模版code
     */
    @TableField("code")
    @ApiModelProperty(value = "模版code")
    private String code;

    /**
     * 模版标题
     */
    @TableField("title")
    @ApiModelProperty(value = "模版标题")
    private String title;

    /**
     * 模版内容
     */
    @TableField("template")
    @ApiModelProperty(value = "模版内容")
    private String template;

    /**
     * 模版内容参数个数
     */
    @TableField("template_params_num")
    @ApiModelProperty(value = "模版内容参数个数")
    private Integer templateParamsNum;

    /**
     * 消息提示（当且仅当需要微信推送的时候要填）
     */
    @TableField(value = "msg_prompt", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "消息提示（当且仅当需要微信推送的时候要填）")
    private String msgPrompt;

    /**
     * 消息提示参数个数
     */
    @TableField("prompt_params_num")
    @ApiModelProperty(value = "消息提示参数个数")
    private Integer promptParamsNum;

    /**
     * 模版说明
     */
    @TableField("description")
    @ApiModelProperty(value = "模版说明")
    private String description;

    /**
     * 推送方式
     */
    @TableField("push_way")
    @ApiModelProperty(value = "推送方式")
    private String pushWay;

    /**
     * 状态
     */
    @TableField("status")
    @ApiModelProperty(value = "状态")
    private Boolean status;

    /**
     * 跳转链接
     */
    @TableField("jump_link")
    @ApiModelProperty(value = "跳转链接")
    private String jumpLink;

    /**
     * 跳转链接参数个数
     */
    @TableField("link_params_num")
    @ApiModelProperty(value = "跳转链接参数个数")
    private Integer linkParamsNum;
}
