package com.fykj.scaffold.sync.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDict;
import com.fykj.scaffold.sync.mapper.ZyzSyncRegionDictMapper;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.region.RegionResult;
import constants.Mark;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import utils.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 市平台地域数据字典
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzSyncRegionDictServiceImpl extends BaseServiceImpl<ZyzSyncRegionDictMapper, ZyzSyncRegionDict> implements IZyzSyncRegionDictService {

    @Override
    public ZyzSyncRegionDict getByRegionCode(String regionCode) {
        return lambdaQuery().eq(ZyzSyncRegionDict::getRegionCode, regionCode).one();
    }

    @Override
    public void syncRegionDict() {
        List<RegionResult> regionResultList = ApiWebService.getRegion("320508");
        List<ZyzSyncRegionDict> dicts = regionResultList.stream().map(it -> {
            ZyzSyncRegionDict dict = new ZyzSyncRegionDict();
            BeanUtil.copyProperties(it, dict);
            return dict;
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(dicts)) {
            return;
        }
        Map<String, ZyzSyncRegionDict> parentMap = dicts.stream().collect(Collectors.toMap(ZyzSyncRegionDict::getRegionCode, a -> a, (k1, k2) -> k1));
        dicts.forEach(dict -> {
            List<String> codePrefix = new ArrayList<>();
            //先把自己添加进去
            codePrefix.add(dict.getRegionCode());
            ZyzSyncRegionDict previousOrg = parentMap.get(dict.getParentCode());
            while (previousOrg != null) {
                codePrefix.add(previousOrg.getRegionCode());
                previousOrg = parentMap.get(previousOrg.getParentCode());
            }
            //倒叙变为字符串
            dict.setCodeLink(StringUtil.join(CollectionUtil.reverse(codePrefix), Mark.COMMA));
        });
        baseMapper.clearAll();
        saveBatch(dicts);
    }
}