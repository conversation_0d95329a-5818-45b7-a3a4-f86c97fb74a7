<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzTeamRetrieveMapper">

    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieve">
        SELECT
            tr.*,
            so.name as orgName,
            so_audit.name as auditOrgName
        FROM zyz_team_retrieve tr
        LEFT JOIN sys_org so on tr.org_code = so.code
        LEFT JOIN sys_org so_audit on tr.audit_org_code = so_audit.code
        WHERE tr.is_deleted = 0
        <if test="params.needAudit != null and params.needAudit == true ">
            AND tr.audit_org_code = #{params.myOrgCode} AND tr.team_status = 'team_audit_waiting'
        </if>
        <if test="params.admin != null and params.admin != ''">
            and (tr.admin_name LIKE CONCAT('%',#{params.admin},'%') OR tr.admin_contact LIKE CONCAT('%',#{params.admin},'%'))
        </if>
        <if test="params.curator != null and params.curator != ''">
            and (tr.curator_name LIKE CONCAT('%',#{params.curator},'%') OR tr.curator_contact LIKE CONCAT('%',#{params.curator},'%'))
        </if>
        <if test="params.teamStatus != null and params.teamStatus != ''">
            and tr.team_status = #{params.teamStatus}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and tr.org_code LIKE CONCAT(#{params.orgCode},'%')
        </if>
        <if test="params.name != null and params.name != ''">
            and tr.name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.hurry != null">
            and tr.hurry = #{params.hurry}
        </if>
        ORDER BY tr.create_date  desc
    </select>

    <select id="getList" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieve">
        SELECT
            tr.*,
            so.name as orgName,
            so_audit.name as auditOrgName
        FROM zyz_team_retrieve tr
        LEFT JOIN sys_org so on tr.org_code = so.code
        LEFT JOIN sys_org so_audit on tr.audit_org_code = so_audit.code
        WHERE tr.is_deleted = 0
        <if test="params.retrieveIds != null and params.retrieveIds.size > 0">
            AND tr.id IN
            <foreach item="item" collection="params.retrieveIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY tr.create_date  desc
    </select>
</mapper>
