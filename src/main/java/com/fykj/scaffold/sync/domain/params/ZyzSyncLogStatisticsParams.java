package com.fykj.scaffold.sync.domain.params;

import fykj.microservice.core.base.BaseParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 市平台信息同步日志
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ZyzSyncLogStatisticsParams extends BaseParams {
    private String bizType;

    private String yearMon;
    private String resultCode;
    private String message;
    private String start;

    private String end;
}
