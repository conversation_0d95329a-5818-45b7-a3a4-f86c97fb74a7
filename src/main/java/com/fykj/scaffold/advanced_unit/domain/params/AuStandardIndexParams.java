package com.fykj.scaffold.advanced_unit.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 文明单位-指标标准-测评指标
 * 查询参数
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明单位-指标项-筛选参数")
public class AuStandardIndexParams extends BaseParams {

    @ApiModelProperty(value = "测评版本ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    private Long standardId;

    @ApiModelProperty(value = "测评项ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    private Long itemId;

    @ApiModelProperty(value = "测评项名称")
    @MatchType(QueryType.LIKE)
    private String name;

}
