<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.civilization_cultivation.mapper.CivilizationCultivationCollectMapper">

    <select id="myCollectSubmit" resultType="com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect">
        SELECT
            ccc.id AS id,
            IF(ccc.picture IS NULL OR ccc.picture = '', NULL, SUBSTRING_INDEX(ccc.picture, ',', 1)) AS picture,
            ccc.name AS name,
            ccc.create_date AS createDate,
            ccc.status AS status,
            sd.name AS statusText,
            IF(ccc.status = 'CC_WAIT_DEAL', TRUE, FALSE) AS enableCallback,
            IF(ccc.status = 'CC_WAIT_DEAL' OR ccc.status = 'CC_CALLBACK' OR ccc.status = 'CC_CHECK_REFUSE', TRUE, FALSE) AS enableRemove
        FROM civilization_cultivation_collect ccc
        LEFT JOIN sys_dict sd ON ccc.status = sd.code
        WHERE ccc.is_deleted = 0 AND ccc.type = #{params.type} AND ccc.creator = #{params.creator}
        ORDER BY ccc.create_date DESC
    </select>

    <select id="selectMaxCode" resultType="java.lang.String">
        SELECT MAX(SUBSTR(code FROM 5 FOR 3))
        FROM `yq_good_person`
        WHERE `year`=#{year}
    </select>

    <select id="pagesForGoodPerson" resultType="com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect">
        SELECT *
        FROM yq_good_person t
        LEFT JOIN sys_org o on t.publish_org_code = o.`code`
        WHERE t.is_deleted = FALSE
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
                      OR t.code LIKE CONCAT('%',CONCAT(#{params.key}, '%'))
                      OR t.link_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
                      OR t.achievement LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
                      OR t.submit_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and t.publish_org_code  LIKE CONCAT(#{params.publishOrgCode},'%')
        </if>
        <if test="params.myOrgCode!='top_dept' and params.myOrgCode != null and params.myOrgCode != ''">
            AND t.publish_org_code LIKE CONCAT(#{params.myOrgCode},'%')
            AND t.status!='good_person_status_processing'
        </if>
        <if test="params.status != null and params.status != ''">
            AND t.status = #{params.status}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND  (t.create_date &lt; #{params.endTime} AND t.create_date &gt; #{params.startTime} )
        </if>
        <if test="params.year != null and params.year != ''">
            AND t.year = #{params.year}
        </if>
        <if test="params.submitPhone != null and params.submitPhone != ''">
            AND t.submit_phone = #{params.submitPhone}
        </if>
        ORDER BY t.create_date DESC
    </select>

    <select id="pagesForPC" resultType="com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect">
        SELECT *
        FROM yq_good_person t
        LEFT JOIN sys_org o on t.publish_org_code = o.`code`
        WHERE t.is_deleted = FALSE AND is_display=true
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR t.code LIKE CONCAT('%',CONCAT(#{params.key}, '%'))
            OR t.link_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR t.achievement LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR t.submit_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and t.publish_org_code  LIKE CONCAT(#{params.publishOrgCode},'%')
        </if>
        <if test="params.status != null and params.status != ''">
            AND t.status = #{params.status}
        </if>
        <if test="params.year != null and params.year != ''">
            AND t.year = #{params.year}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND  (t.create_date &lt; #{params.endTime} AND t.create_date &gt; #{params.startTime} )
        </if>
        ORDER BY t.sequence DESC
    </select>

</mapper>
