package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.entity.ZyzResourceTemp;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceTempParams;
import com.fykj.scaffold.zyz.service.IZyzResourceTempService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 资源临时表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-05
 */
@RestController
@RequestMapping("/admin/zyz/resource_temp")
public class ZyzResourceTempController extends BaseController<IZyzResourceTempService, ZyzResourceTemp, ZyzResourceTempParams> {

    @SysLogMethod("资源临时数据新增")
    @AuditLog("资源临时数据新增")
    @ApiOperation("保存方法(返回id)")
    @PostMapping({"/saveForId"})
    public Result saveForId(@RequestBody @Validated({BaseEntity.Add.class}) ZyzResourceTemp entity) {
        boolean result = this.baseService.saveResourceTemp(entity);
        if (result) {
            return new JsonResult<>(String.valueOf(entity.getId()));
        }
        return new Result(ResultCode.FAIL);
    }
}
