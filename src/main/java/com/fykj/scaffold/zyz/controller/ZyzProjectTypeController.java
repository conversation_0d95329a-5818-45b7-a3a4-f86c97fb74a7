package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectDocking;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectType;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectTypeParams;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceParams;
import com.fykj.scaffold.zyz.service.IZyzProjectTypeService;
import com.fykj.volunteer.sync.util.domain.res.SyncRes;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;
import io.swagger.annotations.Api;
/**
 * 公益伙伴计划-项目类别表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-24
 */
@RestController
@RequestMapping("/admin/project/type")
@Api(tags = "公益伙伴计划-项目类别接口")
public class ZyzProjectTypeController extends BaseController<IZyzProjectTypeService, ZyzProjectType, ZyzProjectTypeParams> {

    @ApiOperation("资源审核分页查询")
    @PostMapping({"/saveOrUpdateType"})
    public Result saveOrUpdateType(@RequestBody ZyzProjectType type) {
        baseService.saveOrUpdateType(type);
        return OK;
    }



    @ApiOperation("获取项目类型列表")
    @GetMapping("/getTypeList")
    public JsonResult<List<ZyzProjectType>> getTypeList(Long projectId) {
        return new JsonResult<>(baseService.getTypeList(projectId));
    }
}
