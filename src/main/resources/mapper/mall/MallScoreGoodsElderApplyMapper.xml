<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallScoreGoodsElderApplyMapper">
    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.dto.MallScoreGoodsElderApplyDto">
        SELECT
        app.*,
        v.exchange_time,
        v.exchange_point,
        vo.service_long,
        v.exchange_before_score,
        v.exchange_after_score
        FROM  mall_score_goods_elder_apply app
        LEFT JOIN  mall_exchange v ON app.exchange_id=v.id  AND v.is_deleted=0
        JOIN zyz_volunteer vo ON vo.id=app.volunteer_id AND vo.is_deleted=0 AND vo.write_off = 0
        where app.is_deleted=FALSE
        <if test="params.keyWord != null and params.keyWord != ''">
            AND (app.goods_name LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.volunteer_name LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.volunteer_phone LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.volunteer_certificate_id LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.volunteer_address LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.elder_name LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.elder_phone LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.elder_certificate_id LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.remarks LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.startTime != null  ">
            AND v.exchange_time <![CDATA[ >= ]]> #{params.startTime}
        </if>
        <if test="params.endTime != null  ">
            AND v.exchange_time  <![CDATA[ <= ]]> #{params.endTime}
        </if>
        ORDER BY v.exchange_time DESC
    </select>
</mapper>
