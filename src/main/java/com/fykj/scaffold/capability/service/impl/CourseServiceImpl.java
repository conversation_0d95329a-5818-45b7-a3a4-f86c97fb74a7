package com.fykj.scaffold.capability.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaQrcodeService;
import cn.binarywang.wx.miniapp.api.impl.WxMaQrcodeServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fykj.scaffold.capability.cons.CourseCons;
import com.fykj.scaffold.capability.cons.CourseType;
import com.fykj.scaffold.capability.domain.dto.*;
import com.fykj.scaffold.capability.domain.entity.Course;
import com.fykj.scaffold.capability.domain.entity.CourseUser;
import com.fykj.scaffold.capability.domain.entity.CreditsRecords;
import com.fykj.scaffold.capability.domain.entity.UserCredits;
import com.fykj.scaffold.capability.domain.params.CourseParams;
import com.fykj.scaffold.capability.domain.params.CourseUserParams;
import com.fykj.scaffold.capability.domain.params.CreditsRecordParams;
import com.fykj.scaffold.capability.mapper.CourseMapper;
import com.fykj.scaffold.capability.mapper.CourseUserMapper;
import com.fykj.scaffold.capability.mapper.CreditsRecordsMapper;
import com.fykj.scaffold.capability.mapper.UserCreditsMapper;
import com.fykj.scaffold.capability.service.ICourseService;
import com.fykj.scaffold.support.conns.WxaEnvEnum;
import com.fykj.scaffold.support.oss.FilePathDto;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import com.fykj.scaffold.zyz.util.LngLonUtil;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import groovy.lang.Tuple2;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.oss.OssCons.OSS_LOCAL;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course>
        implements ICourseService {

    @Value("${spring.profiles.active:default}")
    private String profile;

    @Resource
    private CourseUserMapper courseUserMapper;

    @Resource
    private CreditsRecordsMapper creditsRecordsMapper;

    @Resource
    private UserCreditsMapper userCreditsMapper;

    @Override
    public Course saveCourse(CourseSaveDTO dto) {
        Course entity = new Course();
        BeanUtil.copyProperties(dto, entity, CopyOptions.create());
        initCourse(entity);
        save(entity);
        return entity;
    }

    private void initCourse(Course course) {
        course.setOuterDisplay(false);
        course.setPublishOrg(Oauth2Util.getOrgCode());
        if(CourseType.OFFLINE.name().equals(course.getCourseType())) {
            course.setCheckInDistance(CourseCons.DEFAULT_SIGN_IN_DISTANCE);
        }
    }

    @Async
    @Override
    public void generateCheckInQrcode(Long courseId) {
        Course course = getById(courseId);
        if(CourseType.ONLINE.name().equals(course.getCourseType())) {
            return;
        }
        course.setCheckInQrcode(null);
        if (StringUtil.isNotEmpty(course.getCheckInQrcode())) {
            return;
        }
        WxMaQrcodeService wxMaQrcodeService = new WxMaQrcodeServiceImpl(
                WxMaConfiguration.getMaService("wx29145e35261335f9"));
        course.setCheckInQrcode(getWxaCodeOssPath(wxMaQrcodeService, course.getId()));
        updateById(course);
    }

    /**
     * 获取微信小程序码OssPath
     * @param wxMaQrcodeService 微信小程序码服务
     * @param id id参数
     * @return 微信小程序码OssPath
     */
    private String getWxaCodeOssPath(WxMaQrcodeService wxMaQrcodeService, Long id) {
        try(InputStream registrationWxaCodeIn = createWxaCodeInStream(wxMaQrcodeService, id)) {
            FilePathDto dto = OssSaveUtil.save(registrationWxaCodeIn, OSS_LOCAL,
                    (UUID.fastUUID() + ".jpg"));
            return dto.getUrl();
        } catch (IOException | WxErrorException e) {
            log.error(String.format("生成微信小程序码失败: %d", id), e);
        }
        return "";
    }

    /**
     * 创建微信小程序码InputStream
     * @param id id
     * @return 微信小程序码InputStream
     * @throws WxErrorException 异常
     */
    private InputStream createWxaCodeInStream(WxMaQrcodeService wxMaQrcodeService, Long id)
            throws WxErrorException, IOException {
        // 判断小程序版本
        String env = "prod".equals(profile)
                ? WxaEnvEnum.RELEASE.getCode()
                : WxaEnvEnum.TRIAL.getCode();
        // 获取小程序码bytes
        byte[] wxaCodeUnLimitBytes = wxMaQrcodeService.createWxaCodeBytes(
                // 跳转path
                "/pages_lesson/pages/lesson/sign?from=qrCode&id="+id,
                // 环境
                env,
                // 宽度
                430,
                // 自动颜色
                false,
                // rgb 000指定为黑色
                CourseCons.WXA_CODE_LINE_COLOR,
                // 是否透明底色
                false);
        return new ByteArrayInputStream(wxaCodeUnLimitBytes);
    }

    @Override
    public void editCourse(CourseUpdateDTO dto) {
        Course entity = getCourseFromDatabase(dto.getId());
        // TODO 校验
        validateCourseCanEdit(entity, dto);
        BeanUtil.copyProperties(dto, entity, CopyOptions.create());
        updateById(entity);
    }

    private void validateCourseCanEdit(Course course, CourseUpdateDTO dto) {
        if(Boolean.TRUE.equals(course.getOuterDisplay())) {
            throw new BusinessException(ResultCode.FORBIDDEN, "请先下架再修改");
        }
        if(!Objects.equals(course.getCourseType(), dto.getCourseType())) {
            throw new BusinessException(ResultCode.FORBIDDEN, "课程类型不允许修改");
        }
        LocalDateTime now = LocalDateTime.now();
        if(CourseType.OFFLINE.name().equals(course.getCourseType())) {
            LocalDate courseDate = course.getCourseDate();
            if (Objects.isNull(courseDate)) {
                throw new BusinessException(ResultCode.FORBIDDEN, "课程日期不能为空");
            }
            LocalDateTime courseStartAt = courseDate.atTime(course.getCourseStartTime());
            if(now.isAfter(courseStartAt)) {
                if(!Objects.equals(course.getCourseCredits(), dto.getCourseCredits())) {
                    throw new BusinessException(ResultCode.FORBIDDEN, "课程已开始，不允许修改学分");
                }
                if(!Objects.equals(courseDate, dto.getCourseDate())) {
                    throw new BusinessException(ResultCode.FORBIDDEN, "课程已开始，不允许修改日期");
                }
                if(!Objects.equals(course.getCourseStartTime(), dto.getCourseStartTime())) {
                    throw new BusinessException(ResultCode.FORBIDDEN, "课程已开始，不允许修改开始时间");
                }
            }
        }
    }

    @Override
    public void deleteCourse(Long id) {
        Course course = getCourseFromDatabase(id);
        if(Boolean.TRUE.equals(course.getOuterDisplay())) {
            throw new BusinessException(ResultCode.FORBIDDEN, "请先下架再删除");
        }
        removeById(id);
    }

    @Override
    public CourseDetailDTO detailCourse(Long id) {
        Course course = getCourseFromDatabase(id);
        CourseDetailDTO dto = new CourseDetailDTO();
        BeanUtil.copyProperties(course, dto, CopyOptions.create());
        DictTransUtil.trans(dto);
        return dto;
    }

    @Override
    public CourseDetailDTO detailCourseMine(Long id) {
        Course course = getCourseFromDatabase(id);
        CourseDetailDTO dto = new CourseDetailDTO();
        BeanUtil.copyProperties(course, dto, CopyOptions.create());
        DictTransUtil.trans(dto);
        Long userId = (Long) Oauth2Util.getUserId();
        if(Objects.nonNull(userId)) {
            CourseUser courseUser = getCourseUserFromDatabase(id, userId);
            if(Objects.nonNull(courseUser)) {
                dto.setSignUpAt(courseUser.getSignUpAt());
                dto.setSignInAt(courseUser.getSignInAt());
            }
        }
        return dto;
    }

    @Override
    public CourseQrcodeDTO getCourseSignInQrcode(Long id) {
        Course course = getCourseFromDatabase(id);
        if(CourseType.ONLINE.name().equals(course.getCourseType())) {
            throw new BusinessException(ResultCode.FORBIDDEN, "在线课程无需签到");
        }
        if(StrUtil.isEmpty(course.getCheckInQrcode())) {
            generateCheckInQrcode(id);
        }
        CourseQrcodeDTO dto = new CourseQrcodeDTO();
        Tuple2<LocalDateTime, LocalDateTime> signInTimeRange = getSignInTimeRange(course);
        BeanUtil.copyProperties(course, dto, CopyOptions.create());
        dto.setSignInStartAt(signInTimeRange.getV1());
        dto.setSignInEndAt(signInTimeRange.getV2());
        DictTransUtil.trans(dto);
        return dto;
    }

    private Tuple2<LocalDateTime, LocalDateTime> getSignInTimeRange(Course course) {
        LocalDateTime startAt = course.getCourseDate().atTime(course.getCourseStartTime());
        LocalDateTime endAt = course.getCourseDate().atTime(course.getCourseEndTime());
        // 根据课程时间，提前半个小时和课程结束前可以签到，其余时间签到无效
        return new Tuple2<>(startAt.minusMinutes(30), endAt);
    }

    @Override
    public void switchOuterDisplay(Long id) {
        Course course = getCourseFromDatabase(id);
        course.setOuterDisplay(Boolean.FALSE.equals(course.getOuterDisplay()));
        updateById(course);
    }

    @Override
    public void switchTop(Long id) {
        Course course = getCourseFromDatabase(id);
        course.setTop(Boolean.FALSE.equals(course.getTop()));
        updateById(course);
    }

    @Override
    public IPage<CourseListDTO> pageCourse(CourseParams params) {
        IPage<CourseListDTO> page = params.getPage();
        List<CourseListDTO> list = baseMapper.query(page, params);
        DictTransUtil.trans(list);
        page.setRecords(list);
        return page;
    }

    @Override
    public List<CourseListDTO> listCourse(CourseParams params) {
        List<CourseListDTO> list = baseMapper.query(params);
        DictTransUtil.trans(list);
        return list;
    }

    private Course getCourseFromDatabase(Long id) {
        return Optional.ofNullable(getById(id))
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "该资源已下架或者删除"));
    }

    @Override
    public void signUp(CourseUserSaveDTO dto) {
        Course course = getCourseFromDatabase(dto.getCourseId());
        Long userId = (Long) Oauth2Util.getUserId();
        if(Objects.nonNull(getCourseUserFromDatabase(course.getId(), userId))) {
            throw new BusinessException(ResultCode.FORBIDDEN, "已报名该课程");
        }
        // 校验学分范围
        UserCredits userCredits = getUserCreditsByUserId(userId);
        validateUserCredits(course, userCredits);

        CourseUser courseUser = new CourseUser();
        initCourseUser(course, courseUser, userId, dto);
        courseUserMapper.insert(courseUser);
    }

    private void validateUserCredits(Course course,UserCredits userCredits) {
        // 学分适用范围
        String applicableCreditRange = course.getApplicableCreditRange();
        String[] split = applicableCreditRange.split(StrPool.UNDERLINE);
        long start = Long.parseLong(split[1]);
        long end = split.length >=3 && StrUtil.isNotBlank(split[2])
                ? Long.parseLong(split[2]) : Long.MAX_VALUE;
        // 用户学分
        Long credits = userCredits.getCredits();
        if(start > credits || end < credits) {
            throw new BusinessException(ResultCode.FORBIDDEN, "您的学分不在适用的范围内");
        }
    }



    private void initCourseUser(Course course, CourseUser courseUser, Long userId, CourseUserSaveDTO dto) {
        courseUser.setCourseId(course.getId());
        courseUser.setUserId(userId);
        courseUser.setShouldCredits(course.getCourseCredits());
        courseUser.setCompleteStatus(false);
        courseUser.setCreditsStatus(false);
        courseUser.setSignUpAt(LocalDateTime.now());
        courseUser.setTeamOrEntName(dto.getTeamOrEntName());
        courseUser.setPosition(dto.getPosition());
    }

    @Override
    public void cancelSignUp(Long courseId, Long userId) {
        CourseUser courseUser = getCourseUserFromDatabase(courseId, userId);
        if(courseUser.getCompleteStatus()) {
            throw new BusinessException(ResultCode.FORBIDDEN, "该课程已完成，不允许取消报名");
        }
        courseUserMapper.deleteById(courseUser);
    }

    @Override
    public void cancelSignUp(Long courseId) {
        CourseUser courseUser = getCourseUserFromDatabase(courseId, (Long) Oauth2Util.getUserId());
        if(Objects.isNull(courseUser)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "未报名该课程");
        }
        if(courseUser.getCompleteStatus()) {
            throw new BusinessException(ResultCode.FORBIDDEN, "该课程已完成，不允许取消报名");
        }
        courseUserMapper.deleteById(courseUser);
    }

    @Override
    public void signIn(CourseSignInSaveDTO dto) {
        Course course = getCourseFromDatabase(dto.getCourseId());
        if(CourseType.ONLINE.name().equals(course.getCourseType())) {
            throw new BusinessException(ResultCode.FORBIDDEN, "在线课程不允许签到");
        }
        Tuple2<LocalDateTime, LocalDateTime> signInTimeRange = getSignInTimeRange(course);
        LocalDateTime now = LocalDateTime.now();
        if(now.isBefore(signInTimeRange.getV1())
                || now.isAfter(signInTimeRange.getV2())) {
            throw new BusinessException(ResultCode.FORBIDDEN, "不在签到时间内");
        }
        CourseUser courseUser = getCourseUserFromDatabase(course.getId(), (Long) Oauth2Util.getUserId());
        if(Objects.isNull(courseUser)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "未报名该课程");
        }
        if(Objects.nonNull(courseUser.getSignInAt())) {
            throw new BusinessException(ResultCode.FORBIDDEN, "该课程已签到");
        }
        // 签到距离校验
        validateSignDistance(course, dto);
        courseUser.setSignInAt(now);
        courseUser.setSignInAddress(dto.getSignInAddress());
        courseUser.setSignInLongitude(dto.getSignInLongitude());
        courseUser.setSignInLatitude(dto.getSignInLatitude());

        // 没有签退功能, 活动结束时间时间即为完成时间
        courseUser.setCompleteStatus(true);
        courseUser.setCompletedAt(course.getCourseDate().atTime(course.getCourseEndTime()));

        courseUserMapper.updateById(courseUser);
        // 发放学分
        sendCredits(course.getId(), courseUser.getUserId(), course.getCourseCredits());
    }

    private void validateSignDistance(Course course, CourseSignInSaveDTO dto) {
        long distance = LngLonUtil.measureDistance(
                course.getLongitude(), course.getLatitude(),
                dto.getSignInLongitude(), dto.getSignInLatitude());
        BigDecimal checkInDistance = course.getCheckInDistance();
        if(distance > checkInDistance.longValue()) {
            throw new BusinessException(ResultCode.FORBIDDEN, "签到距离太远");
        }
    }

    private CourseUser getCourseUserFromDatabase(Long courseId, Long userId) {
        return courseUserMapper.selectOne(Wrappers.<CourseUser>lambdaQuery()
                .eq(CourseUser::getCourseId, courseId).eq(CourseUser::getUserId, userId));
    }

    @Override
    public IPage<CourseSignUpDTO> pageSignUp(CourseUserParams params) {
        IPage<CourseSignUpDTO> page = params.getPage();
        List<CourseSignUpDTO> list = courseUserMapper.querySignUp(page, params);
        DictTransUtil.trans(list);
        DesensitiseUtil.desensitise(list);
        page.setRecords(list);
        return page;
    }

    @Override
    public IPage<CourseSignInDTO> pageSignIn(CourseUserParams params) {
        IPage<CourseSignInDTO> page = params.getPage();
        List<CourseSignInDTO> list = courseUserMapper.querySignIn(page, params);
        DictTransUtil.trans(list);
        DesensitiseUtil.desensitise(list);
        page.setRecords(list);
        return page;
    }

    @Override
    public List<CourseSignUpDTO> listSignUp(CourseUserParams params) {
        List<CourseSignUpDTO> list = courseUserMapper.querySignUp(params);
        DictTransUtil.trans(list);
        DesensitiseUtil.desensitise(list);
        return list;
    }

    @Override
    public List<CourseSignInDTO> listSignIn(CourseUserParams params) {
        List<CourseSignInDTO> list = courseUserMapper.querySignIn(params);
        DictTransUtil.trans(list);
        DesensitiseUtil.desensitise(list);
        return list;
    }

    @Override
    public IPage<CourseListDTO> pageMyCourse(CourseParams params) {
        IPage<CourseListDTO> page = params.getPage();
        List<CourseListDTO> list = baseMapper.queryMine(page, params);
        DictTransUtil.trans(list);
        page.setRecords(list);
        return page;
    }

    @Override
    public List<CourseListDTO> listMyCourse(CourseParams params) {
        List<CourseListDTO> list = baseMapper.queryMine(params);
        DictTransUtil.trans(list);
        return list;
    }

    public void sendCredits() {
        // xxl-job 定时任务
        LocalDate yesterday = LocalDate.now().minusDays(1);
        List<Course> courseList = lambdaQuery().eq(Course::getCourseDate, yesterday)
                .eq(Course::getCourseType, CourseType.OFFLINE.name())
                .list();
        courseList.forEach(this::sendCredits);
    }

    @Override
    public void sendCredits(Long courseId) {
        // 管理员手动发放积分, 测试环境没有xxl
        sendCredits(getCourseFromDatabase(courseId));
    }

    public void sendCredits(Course course) {
        List<CourseUser> courseUser = getCourseUserFromDatabase(course.getId());
        List<CourseUser> needUpdateList = courseUser.stream()
                .filter(x -> !x.getCreditsStatus())
                .filter(x -> Objects.nonNull(x.getCompletedAt()))
                .peek(x -> {
                    x.setActualCredits(course.getCourseCredits());
                    x.setCreditsStatus(true);
                }).collect(Collectors.toList());
        // 更新签到状态
        needUpdateList.forEach(courseUserMapper::updateById);
        // 生成积分记录
        needUpdateList.forEach(x -> sendCredits(x.getCourseId(), x.getUserId(), course.getCourseCredits()));
    }

    public void sendCredits(Long courseId, Long userId, Integer credits) {
        // 插入学分记录
        CreditsRecords creditsRecord = new CreditsRecords();
        creditsRecord.setCourseId(courseId);
        creditsRecord.setUserId(userId);
        creditsRecord.setCredits(credits);
        creditsRecord.setGenerateAt(LocalDateTime.now());
        creditsRecordsMapper.insert(creditsRecord);
        // 更新学分总数
        UserCredits userCredits = getUserCreditsByUserId(userId);
        userCredits.setCredits(userCredits.getCredits() + credits);
        userCreditsMapper.updateById(userCredits);
    }

    private UserCredits getUserCreditsByUserId(Long userId) {
        UserCredits userCredits = userCreditsMapper.selectOne(Wrappers.<UserCredits>lambdaQuery()
                .eq(UserCredits::getUserId, userId));
        if(Objects.isNull(userCredits)) {
            userCredits = createUserCredits(userId);
        }
        return userCredits;
    }

    @Override
    public UserCreditsDetailDTO getUserCredits(Long userId) {
        UserCreditsDetailDTO userCredits = userCreditsMapper.getUserCredits(userId);
        if(Objects.isNull(userCredits)) {
            createUserCredits(userId);
            userCredits = userCreditsMapper.getUserCredits(userId);
        }
        return userCredits;
    }

    @Override
    public List<CreditsRecordsDTO> listCreditsRecords(CreditsRecordParams params) {
        List<CreditsRecordsDTO> list = creditsRecordsMapper.getUserCredits(params);
        return list;
    }

    @Override
    public IPage<CreditsRecordsDTO> pageCreditsRecords(CreditsRecordParams params) {
        IPage<CreditsRecordsDTO> page = params.getPage();
        List<CreditsRecordsDTO> list = creditsRecordsMapper.getUserCredits(page, params);
        page.setRecords(list);
        return page;
    }

    @Override
    public void creditsChange(CreditsChangeDTO dto) {
        CourseUser courseUser = getCourseUserFromDatabase(dto.getCourseId(), dto.getUserId());
        CreditsRecords creditsRecord = getCourseRecordsFromDatabase(dto.getCourseId(), dto.getUserId());
        UserCredits userCredits = getUserCreditsByUserId(dto.getUserId());
        courseUser.setActualCredits(dto.getCredits());
        courseUserMapper.updateById(courseUser);
        // 更新学分记录
        creditsRecord.setCredits(dto.getCredits());
        creditsRecord.setGenerateAt(LocalDateTime.now());
        creditsRecordsMapper.updateById(creditsRecord);
        // 计算新学分与旧学分差值
        // 更新学分总数
        int addCredits = dto.getCredits() - creditsRecord.getCredits();
        userCredits.setCredits(userCredits.getCredits() + addCredits);
        userCreditsMapper.updateById(userCredits);
    }

    private CreditsRecords getCourseRecordsFromDatabase(Long courseId, Long userId) {
        return creditsRecordsMapper.selectOne(Wrappers.<CreditsRecords>lambdaQuery()
                .eq(CreditsRecords::getCourseId, courseId)
                .eq(CreditsRecords::getUserId, userId));
    }

    private UserCredits createUserCredits(Long userId) {
        UserCredits userCredits = new UserCredits();
        userCredits.setUserId(userId);
        userCredits.setCredits(0L);
        userCreditsMapper.insert(userCredits);
        return userCredits;
    }

    private List<CourseUser> getCourseUserFromDatabase(Long courseId) {
        return courseUserMapper.selectList(Wrappers.<CourseUser>lambdaQuery()
                .eq(CourseUser::getCourseId, courseId));
    }

    @Override
    public List<Course> getNeedRemindCourses(LocalDateTime start, LocalDateTime end) {
        return baseMapper.getNeedRemindCourses(start, end);
    }

    @Override
    public List<CourseUser> getNeedRemindUsers(List<Long> courseIds) {
        return courseUserMapper.selectList(Wrappers.<CourseUser>lambdaQuery().in(CourseUser::getCourseId, courseIds));
    }
}
