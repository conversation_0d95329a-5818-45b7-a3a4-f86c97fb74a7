package com.fykj.scaffold.mall.controller;


import com.fykj.scaffold.mall.domain.entity.MallStockReplenishPlanDetail;
import com.fykj.scaffold.mall.domain.params.MallStockReplenishPlanDetailParams;
import com.fykj.scaffold.mall.service.IMallStockReplenishPlanDetailService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 物料进补计划明细管理前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@RestController
@RequestMapping("/admin/mall/stock_replenish_plan_detail")
@Api(tags = "物料进补计划明细管理接口")
public class MallStockReplenishPlanDetailController extends BaseController<IMallStockReplenishPlanDetailService, MallStockReplenishPlanDetail, MallStockReplenishPlanDetailParams> {
}
