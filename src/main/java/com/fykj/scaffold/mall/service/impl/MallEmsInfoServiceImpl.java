package com.fykj.scaffold.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdcardUtil;
import com.fykj.scaffold.mall.domain.entity.MallEmsInfo;
import com.fykj.scaffold.mall.mapper.MallEmsInfoMapper;
import com.fykj.scaffold.mall.service.IMallEmsInfoService;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.service.IMsgRecordService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.util.List;


/**
 * 积分商城- 年度区级优秀志愿者地址收集表
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-02-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallEmsInfoServiceImpl extends BaseServiceImpl<MallEmsInfoMapper, MallEmsInfo> implements IMallEmsInfoService {


    @Autowired
    private IMsgRecordService msgRecordService;

    @Autowired
    private IZyzVolunteerService zyzVolunteerService;

    @Override
    public Boolean submit(MallEmsInfo entity) {
        ZyzVolunteer volunteer = validInfo(entity);
        //新增或者修改已有的地址信息
        MallEmsInfo info = lambdaQuery().eq(MallEmsInfo::getVolunteerId, volunteer.getId()).one();
        if (info == null) {
            info = new MallEmsInfo();
        }
        BeanUtil.copyProperties(entity, info, "id", "version");
        info.setVolunteerId(volunteer.getId());
        info.setRemark(info.getTitle());
        return super.saveOrUpdate(info);
    }

    private ZyzVolunteer validInfo(MallEmsInfo entity) {
        //验证身份证号码
        if (entity.getVolunteerCertificateType().equals(ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD) && !IdcardUtil.isValidCard(entity.getVolunteerCertificateId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者身份证校验异常！");
        }
        if (StringUtil.isNotEmpty(entity.getRecipientCertificateId()) && entity.getRecipientCertificateType().equals(ZyzCons.CERTIFICATE_TYPE_CERTIFICATE_CARD) && !IdcardUtil.isValidCard(entity.getRecipientCertificateId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "收件人身份证校验异常！");
        }
        //验证根据手机号码和证件号码能否获取到对应的志愿者
        ZyzVolunteer volunteer = zyzVolunteerService.getByPhone(entity.getVolunteerPhone());
        if (volunteer == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "根据手机号码查找志愿者失败！");
        }
        if (!volunteer.getCertificateId().equalsIgnoreCase(entity.getVolunteerCertificateId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "填写的志愿者证件号码与注册的证件号码不一致！");
        }
        //获取该批次志愿者列表
        if (StringUtil.isEmpty(entity.getTitle())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "填报链接不完整，请拷贝完整链接！");
        }
        List<MsgRecord> recordList = msgRecordService.getListByTitle(entity.getTitle());
        if (recordList.stream().noneMatch(x -> x.getReceiverMobile().equals(entity.getVolunteerPhone()))) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "填写的志愿者不在名单，请联系管理员！");
        }
        return volunteer;
    }
}
