package com.fykj.scaffold.support.utils;

import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.support.conns.Cons;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import fykj.microservice.core.support.util.SystemUtil;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Token工具类，专注于从请求头中解析OAuth2 token获取用户信息
 * 可以解析以"/api"开头的接口中的OAuth2 token
 */
@UtilityClass
public class TokenUtil {
    
    private static final Logger log = LoggerFactory.getLogger(TokenUtil.class);
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String BEARER_PREFIX_LOWERCASE = "bearer ";
    
    /**
     * 获取当前用户，优先从OAuth2上下文获取，失败则尝试从请求头解析OAuth2 token
     * @return 用户详情对象，如果无法获取则返回null
     */
    public static BackendUserDetail getUser() {
        // 先尝试从OAuth2上下文获取
        BackendUserDetail user = Oauth2Util.getUser();
        if (user != null) {
            return user;
        }
        
        // 尝试从请求头获取并解析OAuth2 token
        try {
            HttpServletRequest request = getRequest();
            if (request == null) {
                return null;
            }
            
            // 尝试获取并解析OAuth2 token
            String authHeader = request.getHeader(AUTHORIZATION_HEADER);
            if (authHeader != null) {
                String token = null;
                if (authHeader.startsWith(BEARER_PREFIX)) {
                    token = authHeader.substring(BEARER_PREFIX.length());
                } else if (authHeader.startsWith(BEARER_PREFIX_LOWERCASE)) {
                    token = authHeader.substring(BEARER_PREFIX_LOWERCASE.length());
                }
                
                if (token != null) {
                    return getUserFromOAuth2Token(token);
                }
            }
        } catch (Exception e) {
            log.warn("解析OAuth2 token获取用户信息失败", e);
        }
        
        return null;
    }
    
    /**
     * 获取当前用户ID，优先从OAuth2上下文获取，失败则尝试从请求头解析OAuth2 token
     * @return 用户ID，如果无法获取则返回null
     */
    public static Serializable getUserId() {
        // 先尝试从OAuth2上下文获取
        Serializable userId = Oauth2Util.getUserId();
        if (userId != null) {
            return userId;
        }
        
        // 尝试通过getUser方法获取用户，然后提取ID
        BackendUserDetail user = getUser();
        return Optional.ofNullable(user)
                .map(BackendUserDetail::getId)
                .orElse(null);
    }
    
    /**
     * 从OAuth2 token解析用户信息
     * @param token OAuth2令牌字符串
     * @return 用户详情对象，如果解析失败则返回null
     */
    private static BackendUserDetail getUserFromOAuth2Token(String token) {
        try {
            // 获取TokenStore bean
            TokenStore tokenStore = SystemUtil.getBean(TokenStore.class);
            if (tokenStore == null) {
                log.warn("无法获取TokenStore bean");
                return null;
            }
            
            // 读取token信息
            OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
            if (accessToken == null || accessToken.isExpired()) {
                log.warn("Token无效或已过期");
                return null;
            }
            
            // 读取认证信息
            OAuth2Authentication authentication = tokenStore.readAuthentication(token);
            if (authentication == null) {
                return null;
            }
            
            Object principal = authentication.getPrincipal();
            if (principal instanceof BackendUserDetail) {
                return (BackendUserDetail) principal;
            }
            
        } catch (Exception e) {
            log.warn("解析OAuth2 token失败", e);
        }
        
        return null;
    }
    
    /**
     * 获取当前请求对象
     * @return HttpServletRequest对象，如果不在请求上下文中则返回null
     */
    private static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
    
    /**
     * 检查当前用户是否为管理员
     * @return 是否为管理员
     */
    public static boolean isAdmin() {
        return Optional.ofNullable(getUser())
                .map(it -> it.isHasRole(Cons.RoleCode.ROLE_CODE_ADMIN))
                .orElse(false);
    }
    
    /**
     * 检查当前用户是否具有指定角色
     * @param roleCode 角色代码
     * @return 是否具有指定角色
     */
    public static boolean hasRole(String roleCode) {
        return Optional.ofNullable(getUser())
                .map(it -> it.isHasRole(roleCode))
                .orElse(false);
    }
    
    /**
     * 获取当前用户的角色ID列表
     * @return 角色ID列表
     */
    public static List<Long> getRoleIds() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getRoleIds)
                .orElse(new ArrayList<>());
    }
    
    /**
     * 获取当前用户的真实姓名
     * @return 用户真实姓名
     */
    public static String getName() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getNickName)
                .orElse(null);
    }
    
    /**
     * 获取当前用户的手机号
     * @return 用户手机号
     */
    public static String getMobile() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getMobile)
                .orElse(null);
    }
    
    /**
     * 获取当前用户的组织代码
     * @return 组织代码
     */
    public static String getOrgCode() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getOrgCode)
                .orElse(null);
    }
    
    /**
     * 获取当前用户的团队ID
     * @return 团队ID
     */
    public static Long getTeamId() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getTeamId)
                .orElse(null);
    }
} 