package com.fykj.scaffold.portal_website.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.cons.Constants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

;import java.time.LocalDateTime;

/**
 * 新闻资讯审核记录
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cms_audit_record")
public class NewsAudit extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 内容id
     */
    @TableField("content_id")
    @ApiModelProperty(value = " 内容id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contentId;

    /**
     * 是否通过
     */
    @TableField("pass")
    @ApiModelProperty(value = " 简介")
    private Boolean pass;

    /**
     * 备注
     */
    @TableField("memo")
    @ApiModelProperty(value = " 备注")
    private String memo;

    /**
     * 审核人
     */
    @TableField(exist = false)
    @ApiModelProperty(value = " 审核人")
    private String auditor;

    /**
     * 审核时间
     */
    @TableField(exist = false)
    @ApiModelProperty(value = " 审核时间")
    @JsonFormat(pattern = Constants.DATETIME_FORMAT)
    private LocalDateTime auditTime;
}
