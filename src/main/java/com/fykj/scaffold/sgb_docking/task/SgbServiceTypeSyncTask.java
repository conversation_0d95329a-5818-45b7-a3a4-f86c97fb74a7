package com.fykj.scaffold.sgb_docking.task;

import com.fykj.scaffold.sgb_docking.service.ISgbServiceTypeService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 苏州志愿者平台服务领域同步任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbServiceTypeSyncTask {

    @Autowired
    private ISgbServiceTypeService sgbServiceTypeService;

    /**
     * 服务领域同步任务
     */
    @XxlJob("SGB_ServiceType_Sync_Handler")
    public void serviceTypeSyncJobHandler() {
        XxlJobHelper.log("SGB_ServiceType_Sync_Handler-Job, START!");
        try {
            sgbServiceTypeService.syncServiceType();
        } catch (Exception e) {
            log.error("苏州志愿者平台服务领域同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("SGB_ServiceType_Sync_Handler-Job, END!");
    }
}