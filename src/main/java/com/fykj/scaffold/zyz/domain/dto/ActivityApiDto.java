package com.fykj.scaffold.zyz.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.utils.Desensitise;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 活动信息（面向前端）
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-31
 */
@Data
@NoArgsConstructor
public class ActivityApiDto {

    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actId;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private String picture;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 招募对象
     */
    @DictTrans(transTo = "recruitTargetsText")
    @ApiModelProperty(value = "招募对象")
    private String recruitTargets;

    /**
     * 招募对象
     */
    @ApiModelProperty(value = "招募对象")
    private String recruitTargetsText;


    /**
     * 活动地址
     */
    @ApiModelProperty(value = "活动地址")
    private String address;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime endTime;

    /**
     * 活动时间段
     */
    @ApiModelProperty(value = "活动时间段")
    private String dateTimeRange;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @Desensitise
    private String contactPhone;

    /**
     * 活动类型code
     */
    @DictTrans(transTo = "actTypeText")
    @ApiModelProperty(value = "活动类型code")
    private String actType;

    /**
     * 活动类型
     */
    @ApiModelProperty(value = "活动类型")
    private String actTypeText;

    /**
     * 所属领域一级
     */
    @ApiModelProperty(value = "所属领域一级")
    private String belongFieldTop;

    /**
     * 所属领域名称一级
     */
    @ApiModelProperty(value = "所属领域名称一级")
    private String belongFieldNameTop;

    /**
     * 所属领域二级
     */
    @ApiModelProperty(value = "所属领域二级")
    private String belongFieldEnd;

    /**
     * 所属领域二级名称
     */
    @ApiModelProperty(value = "所属领域二级名称")
    private String belongFieldNameEnd;

    /**
     * 活动内容
     */
    @ApiModelProperty(value = "活动内容")
    private String actContents;

    /**
     * 最晚报名结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最晚报名结束时间")
    private LocalDateTime applyEndTime;

    /**
     * 活动主办方
     */
    @ApiModelProperty(value = "活动主办方")
    private String publisherName;

    /**
     * 活动简介
     */
    @ApiModelProperty(value = "活动简介")
    private String actSynopsis;

    /**
     * 活动状态code
     */
    @DictTrans(transTo = "activityStatusText")
    @ApiModelProperty(value = "活动状态code")
    private String activityStatus;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private String activityStatusText;

    @ApiModelProperty(value = "是否定向")
    private Boolean open;

    @ApiModelProperty(value = "报名条件")
    private String applicationRequirements;

    @ApiModelProperty(value = "发布组织code")
    private String publishOrgCode;

    @ApiModelProperty(value = "发布组织")
    private String publishOrgName;

    @ApiModelProperty(value = "是否大型赛事")
    private Boolean bigActivity;

    @ApiModelProperty(value = "公开招募表单快照ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyPublicFormSnapshotId;

    @ApiModelProperty(value = "是否团队发布")
    private Boolean teamPublish;

    @ApiModelProperty(value = "团队ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long teamId;

    @ApiModelProperty(value = "团队信息")
    private TeamApiDto teamInfo;
}
