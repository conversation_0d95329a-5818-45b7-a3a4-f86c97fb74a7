package com.fykj.scaffold.security.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.App;
import com.fykj.scaffold.security.business.domain.entity.AppClient;
import com.fykj.scaffold.security.business.mapper.AppMapper;
import com.fykj.scaffold.security.business.service.IAppApiPathService;
import com.fykj.scaffold.security.business.service.IAppClientService;
import com.fykj.scaffold.security.business.service.IAppService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;


/**
 * 应用管理服务实现类
 *
 * <AUTHOR>
 * @date 2021-05-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AppServiceImpl extends BaseServiceImpl<AppMapper, App> implements IAppService {

    @Autowired
    private IAppClientService appClientService;
    @Autowired
    private IAppApiPathService appApiPathService;

    @Override
    public App getById(Serializable id) {
        App app = super.getById(id);
        AppClient client = appClientService.getByAppId(app.getAppId());
        app.setClientInfo(client);
        app.setApiIdList(appApiPathService.getApiIdListByAppId(app.getAppId()));
        return app;
    }

    @Override
    public IPage<App> page(BaseParams params) {
        return super.page(params).convert(DictTransUtil::trans);
    }


}
