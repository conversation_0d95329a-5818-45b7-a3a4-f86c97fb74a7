package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncPushService;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * SGB团队同步定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbTeamSyncTask {

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private ISgbSyncPushService sgbSyncPushService;

    @XxlJob("SGB_Team_Sync_Handler")
    public void sgbTeamSyncJobHandler() {
        XxlJobHelper.log("SGB_Team_Sync_Handler-Job, START!");

        // SGB团队同步逻辑
        List<Long> sgbTeamIds = teamService.getSgbNeedSync();
        if (CollectionUtil.isNotEmpty(sgbTeamIds)) {
            try {
                sgbSyncPushService.syncTeamBatch(sgbTeamIds);
            } catch (Exception e) {
                log.error("SGB团队批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }

        XxlJobHelper.log("SGB_Team_Sync_Handler-Job, END!");
    }
}
