package com.fykj.scaffold.race.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 摄影大赛-活动表
 *
 * <AUTHOR> @email ${email}y
 * @date 2023-01-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("race_activity")
public class Activity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 活动名称
     */
    @TableField("name")
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 状态(1:启用，0：禁用)
     */
    @TableField("is_enable")
    @ApiModelProperty(value = "状态(1:启用，0：禁用)")
    private Boolean enable;


    /**
     * 是否展示
     */
    @TableField("is_show")
    @ApiModelProperty(value = "是否展示")
    private Boolean isShow;

    /**
     * 是否可以上传
     */
    @TableField("is_upload")
    @ApiModelProperty(value = "是否可以上传")
    private Boolean upload;

    /**
     * 背景图
     */
    @TableField("picture")
    @ApiModelProperty(value = "背景图")
    private String picture;

    /**
     * 大赛简介
     */
    @TableField("remind")
    @ApiModelProperty(value = "大赛简介")
    private String remind;


	/**
	 * 奖项
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "奖项")
	private String prize;


    /**
     * 排序
     */

    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;


}
