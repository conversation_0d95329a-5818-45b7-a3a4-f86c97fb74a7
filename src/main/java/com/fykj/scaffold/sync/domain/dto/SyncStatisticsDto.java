package com.fykj.scaffold.sync.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SyncStatisticsDto implements Serializable {

    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @ApiModelProperty(value = "年月")
    private String yearMon;

    @ApiModelProperty(value = "新增数量")
    private int addNum = 0;

    @ApiModelProperty(value = "成功数量")
    private int successNum = 0;

    @ApiModelProperty(value = "失败数量")
    private int failNum = 0;
}
