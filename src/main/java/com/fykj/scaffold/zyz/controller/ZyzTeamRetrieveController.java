package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieve;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamRetrieveParams;
import com.fykj.scaffold.zyz.service.IZyzTeamRetrieveService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static constants.Mark.COMMA;

/**
 * 志愿者团队找回表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-08-08
 */
@Slf4j
@RestController
@RequestMapping("/admin/zyz/team/retrieve")
@Api(tags = "志愿者团队找回接口")
public class ZyzTeamRetrieveController extends BaseController<IZyzTeamRetrieveService, ZyzTeamRetrieve, ZyzTeamRetrieveParams> {

    @ApiOperation("找回团队分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzTeamRetrieve>> getPages(@RequestBody ZyzTeamRetrieveParams params) {
        IPage<ZyzTeamRetrieve> iPage = baseService.getPages(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        iPage.getRecords().forEach(it -> {
            if (ZyzCons.TEAM_AUDIT_STATUS_WAIT_CHECK.equals(it.getTeamStatus())) {
                it.setTeamStatusText("待核实");
            }
        });
        return new JsonResult<>(iPage);
    }

    @AuditLog("找回团队审核--批量")
    @ApiOperation("批量审核")
    @GetMapping("/audit")
    public Result audit(String teamIds, String remark, Boolean pass) {
        List<Long> idList = Arrays.stream(teamIds.split(COMMA)).map(Long::valueOf).collect(Collectors.toList());
        baseService.audit(idList, remark, pass);
        return OK;
    }

    @ApiOperation(value = "获取团队找回信息")
    @GetMapping(value = "/getTeamById")
    public Result getById(@RequestParam Long id) {
        return new JsonResult<>(baseService.getTeamById(id));
    }
}
