package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

;

/**
 * 积分商城-积分历史记录
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_point_record")
public class ZyzPointRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 志愿者用户id
     */
    @TableField("volunteer_id")
    @ApiModelProperty(value = "志愿者用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;

    /**
     * 志愿者用户名称
     */
    @TableField("volunteer_name")
    @ApiModelProperty(value = "志愿者用户名称")
    private String volunteerName;

    /**
     * 兑换之前的积分
     */
    @TableField("exchange_before_point")
    @ApiModelProperty(value = "兑换之前的积分")
    private BigDecimal exchangeBeforePoint;

    /**
     * 兑换之后的积分
     */
    @TableField("exchange_after_point")
    @ApiModelProperty(value = "兑换之后的积分")
    private BigDecimal exchangeAfterPoint;

    /**
     * 类型
     */
    @TableField("type")
    @ApiModelProperty(value = "类型")
    @DictTrans(transTo = "typeText")
    private String type;

    @TableField(exist = false)
    @ApiModelProperty(value = "状态类型Text")
    private String typeText;
    /**
     * 变动积分
     */
    @TableField("exchange_point")
    @ApiModelProperty(value = "变动积分")
    private BigDecimal exchangePoint;

    /**
     * 变动时间
     */
    @TableField("exchange_time")
    @ApiModelProperty(value = "变动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exchangeTime;

    /**
     * 交易说明
     */
    @TableField("remark")
    @ApiModelProperty(value = "交易说明")
    private String remark;

    /**
     * 业务类型
     */
    @TableField("biz_type")
    @ApiModelProperty(value = "业务类型")
    @DictTrans(transTo = "bizTypeText")
    private String bizType;

    @TableField(exist = false)
    @ApiModelProperty(value = "业务类型Text")
    private String bizTypeText;

    /**
     * 业务id
     */
    @TableField("biz_id")
    @ApiModelProperty(value = "业务id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bizId;

    /**
     * 流水号
     */
    @TableField("trx_id")
    @ApiModelProperty(value = "流水号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long trxId;
    /**
     * 证件编号
     */
    @TableField("certificate_id")
    @ApiModelProperty(value = "证件编号")
    private String certificateId;

    /**
     * 联系方式
     */
    @TableField("phone")
    @ApiModelProperty(value = "联系方式")
    private String phone;

    /**
     *
     */
    @TableField("org_code")
    @ApiModelProperty(value = "")
    private String orgCode;
}
