package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户弱口令列表
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SysWeakPwParams extends BaseParams {

    @ApiModelProperty("关键字查询，模糊匹配用户名、手机号、邮箱")
    @MatchType(value = QueryType.LIKE, fieldName = {"password"})
    private String key;
}
