package com.fykj.scaffold.security.business.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author：yangxu
 * @Date：2025/5/26 10:53
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleTreeDataDto implements Serializable {

    private static final long serialVersionUID = 5341518390137706920L;

    private String name;

    private List<String> codes;

    private List<RoleTreeDataDto> children;
}
