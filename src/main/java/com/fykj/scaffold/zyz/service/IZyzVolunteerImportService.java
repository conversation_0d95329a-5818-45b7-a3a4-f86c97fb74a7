package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.dto.VolunteerImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 志愿者表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-01
 */
public interface IZyzVolunteerImportService extends IService<ZyzVolunteer>, IBaseService<ZyzVolunteer> {

    /**
     * 初始化
     */
    void init();
//
//    void test();

    /**
     * 志愿者数据导入
     *
     * @param dataList
     * @return
     */
    List<VolunteerImportDto> dataImportHandle(List<VolunteerImportDto> dataList);
}

