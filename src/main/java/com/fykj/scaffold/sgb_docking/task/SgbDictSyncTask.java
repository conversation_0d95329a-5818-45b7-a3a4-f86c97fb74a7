package com.fykj.scaffold.sgb_docking.task;

import com.fykj.scaffold.sgb_docking.service.ISgbDictService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 苏州志愿者平台字典数据同步任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbDictSyncTask {

    @Autowired
    private ISgbDictService sgbDictService;

    /**
     * 字典数据同步任务
     */
    @XxlJob("SGB_Dict_Sync_Handler")
    public void dictSyncJobHandler() {
        XxlJobHelper.log("SGB_Dict_Sync_Handler-Job, START!");
        try {
            // 同步所有类型的字典数据
            sgbDictService.syncDict();
        } catch (Exception e) {
            log.error("苏州志愿者平台字典数据同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("SGB_Dict_Sync_Handler-Job, END!");
    }
} 