package com.fykj.scaffold.advanced_unit.service.impl;

import com.fykj.scaffold.advanced_unit.domain.entity.AuStandard;
import com.fykj.scaffold.advanced_unit.mapper.AuStandardMapper;
import com.fykj.scaffold.advanced_unit.service.IAuStandardService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 文明单位-指标标准-测评版本
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AuStandardServiceImpl extends BaseServiceImpl<AuStandardMapper, AuStandard>
        implements IAuStandardService {

    /**
     * 重写save, 检查年份重复
     */
    @Override
    public boolean save(AuStandard entity) {
        // 检查年份是否存在
        // 使用count方法检查年份是否存在
        if (lambdaQuery().eq(AuStandard::getYear, entity.getYear()).count() > 0) {
            throw new BusinessException(ResultCode.NOT_VALID, "年份已存在");
        }
        return super.save(entity);
    }


    @Override
    public List<Integer> getYearsHaveStandard() {
        return list().stream()
                .map(AuStandard::getYear)
                .distinct()
                .collect(Collectors.toList());
    }


    @Override
    public void updateShowStatus(String id) {
        AuStandard standard = getById(id);
        standard.setStatus(Boolean.FALSE.equals(standard.getStatus()));
        updateById(standard);
    }

    /**
     * 根据年份获取标准
     *
     * @param year 年份
     * @return 标准
     */
    @Override
    public AuStandard getByYear(Integer year) {
        return Optional.ofNullable(lambdaQuery().eq(AuStandard::getYear, year).one())
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "所选年份没有配置指标标准"));
    }

}
