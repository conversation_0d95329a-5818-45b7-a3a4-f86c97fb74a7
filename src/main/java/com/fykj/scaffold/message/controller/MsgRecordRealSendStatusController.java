package com.fykj.scaffold.message.controller;


import com.fykj.scaffold.message.domain.entity.MsgRecordRealSendStatus;
import com.fykj.scaffold.message.domain.param.MsgRecordRealSendStatusParams;
import com.fykj.scaffold.message.service.impl.MsgRecordRealSendStatusServiceImpl;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.Result;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@RestController
@RequestMapping("/admin/msg_record_real_send_status")
@Api(tags = "消息真实发送状态")
public class MsgRecordRealSendStatusController extends BaseController<MsgRecordRealSendStatusServiceImpl, MsgRecordRealSendStatus, MsgRecordRealSendStatusParams> {

    @ApiOperation(value = "同步消息的真实发送状态")
    @GetMapping(value = "/syncRealSendStatus")
    public Result syncRealSendStatus() {
        baseService.syncRealSendStatus();
        return OK;
    }
}
