//package com.fykj.scaffold.task.service.impl;
//
//import cn.hutool.core.thread.ThreadUtil;
//import cn.hutool.core.util.IdUtil;
//import com.fykj.scaffold.support.conns.Cons;
//import com.fykj.scaffold.support.utils.Oauth2Util;
//import com.fykj.scaffold.task.domain.dto.TaskMessingDto;
//import com.fykj.scaffold.task.domain.entity.CopyActivity;
//import com.fykj.scaffold.task.domain.entity.CopyActivityApply;
//import com.fykj.scaffold.task.domain.entity.CopyVolunteer;
//import com.fykj.scaffold.task.domain.entity.ParameterCopyActivity;
//import com.fykj.scaffold.task.domain.params.AutoActivityApplyParam;
//import com.fykj.scaffold.task.domain.params.ParameterCopyActivityParams;
//import com.fykj.scaffold.task.repository.CopyActivityApplyRepository;
//import com.fykj.scaffold.task.repository.CopyActivityRepository;
//import com.fykj.scaffold.task.repository.CopyVolunteerRepository;
//import com.fykj.scaffold.task.mapper.ParameterCopyActivityMapper;
//import com.fykj.scaffold.task.service.CopyActivityService;
//import com.fykj.scaffold.task.service.IParameterCopyActivityService;
//import fykj.microservice.core.base.BaseServiceImpl;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//import utils.LocalDateTimeUtil;
//import utils.StringUtil;
//
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.util.List;
//import java.util.stream.Collectors;
//
//
///**
// * 活动批量创建参数表
// *
// * 服务实现类
// * <AUTHOR>
// * @date 2023-09-25
// */
//@Service
//@Transactional(rollbackFor = Exception.class)
//public class ParameterCopyActivityServiceImpl extends BaseServiceImpl<ParameterCopyActivityMapper, ParameterCopyActivity> implements IParameterCopyActivityService {
//    @Autowired
//    private CopyVolunteerRepository volunteerRepository;
//
//    @Autowired
//    private CopyActivityService copyActivityService;
//
//    @Autowired
//    private CopyActivityRepository activityRepository;
//
//    @Autowired
//    private CopyActivityApplyRepository applyRepository;
//
//
//    @Override
//    public boolean save(ParameterCopyActivity entity) {
//        entity.setSyncTaskStatus("task_wait_begin");
//        entity.setName(LocalDateTimeUtil.formatDate(LocalDate.now(),"yyyyMMdd")+ Oauth2Util.getName()+entity.getActStartDay()+"~~"+entity.getActEndDay());
//        super.save(entity);
//        for(CopyVolunteer volunteer:entity.getVolunteerList()){
//            volunteer.setVolunteerId(IdUtil.getSnowflake().nextId());
//            volunteer.setParameterId(entity.getId());
//        }
//        volunteerRepository.saveAll(entity.getVolunteerList());
//        ThreadUtil.execAsync(() -> copyActivityService.createActivityPlan(entity));
//        return true;
//    }
//
//    @Override
//    public Page<CopyActivity> getPage(ParameterCopyActivityParams params) {
//        PageRequest pageRequest = PageRequest.of(params.getCurrentPage()-1, params.getPageSize());
//        Page<CopyActivity> products;
//        if(StringUtil.isEmpty(params.getSync())){
//            products = activityRepository.findByParameterIdOrderByCreateDateDesc(params.getParameterId(),pageRequest);
//        }else{
//            products = activityRepository.findByParameterIdAndSyncOrderByCreateDateDesc(params.getParameterId(),params.getSync(),pageRequest);
//        }
//        return products;
//    }
//
//    @Override
//    public Page<CopyActivityApply> getApplyPage(AutoActivityApplyParam params) {
//        PageRequest pageRequest = PageRequest.of(params.getCurrentPage()-1, params.getPageSize());
//        Page<CopyActivityApply> products;
//        if(StringUtil.isEmpty(params.getSync())){
//            products = applyRepository.findByActivityIdOrderByCreateTimeDesc(params.getActivityId(),pageRequest);
//        }else{
//            products = applyRepository.findByActivityIdAndApplyIsSyncAndServiceLongIsSyncOrderByCreateTimeDesc(params.getActivityId(),params.getSync(),params.getSync(),pageRequest);
//        }
//        return products;
//    }
//
//    @Override
//    public TaskMessingDto queryByParameterId(Long parameterId) {
//        //获取所有的这条任务的创建的活动
//        List<CopyActivity>  activities=activityRepository.findAllByParameterId(parameterId);
//        List<CopyVolunteer> volunteers=volunteerRepository.findAllByParameterId(parameterId);
//        //过滤出成功的
//        List<CopyActivity>  successActivities=activities.stream().filter(it-> Cons.PlatformSyncState.SUCCESS.equals(it.getSync())).collect(Collectors.toList());
//        //获取所有的这条任务的创建的活动招募
//        List<CopyActivityApply> activityApplies=applyRepository.findByParameterId(parameterId);
//        //过滤出成功的时长
//        List<CopyActivityApply> successServiceLongApplies=activityApplies.stream().filter(it-> Cons.PlatformSyncState.SUCCESS.equals(it.getServiceLongIsSync())).collect(Collectors.toList());
//        //过滤出成功的报名
//        List<CopyActivityApply> successApplyApplies=activityApplies.stream().filter(it-> Cons.PlatformSyncState.SUCCESS.equals(it.getApplyIsSync())).collect(Collectors.toList());
//        TaskMessingDto taskMessingDto=new TaskMessingDto();
//        taskMessingDto.setSuccessActivitiesNums(successActivities.size());
//        //找出同步过的活动条数
//        taskMessingDto.setSyncActivitiesNums((int) activities.stream().filter(it ->
//                (Cons.PlatformSyncState.SUCCESS.equals(it.getSync())
//                        || Cons.PlatformSyncState.FAILURE.equals(it.getSync()))).count());
//        taskMessingDto.setRealServiceLong(BigDecimal.ZERO);
//        taskMessingDto.setSuccessVolunteerNums(0);
//        taskMessingDto.setImportVolunteerNums(volunteers.size());
//        if(!CollectionUtils.isEmpty(successServiceLongApplies)){
//            successServiceLongApplies.forEach(it-> taskMessingDto.setRealServiceLong(taskMessingDto.getRealServiceLong()
//                    .add(it.getServiceLong())));
//        }
//        if(!CollectionUtils.isEmpty(successApplyApplies)){
//            taskMessingDto.setSuccessVolunteerNums(Math.toIntExact(successApplyApplies.stream().map(CopyActivityApply::getCertificateId).distinct().count()));
//        }
//        taskMessingDto.setTaskAllActivitiesNums(activities.size());
//        //找出同步过的活动招募条数
//        taskMessingDto.setSyncAppliesNums((int) activityApplies.stream().filter(it ->
//                (Cons.PlatformSyncState.SUCCESS.equals(it.getApplyIsSync())
//                        || Cons.PlatformSyncState.FAILURE.equals(it.getApplyIsSync()))).count());
//        taskMessingDto.setTaskAllAppliesNums(activityApplies.size());
//        return taskMessingDto;
//    }
//}
