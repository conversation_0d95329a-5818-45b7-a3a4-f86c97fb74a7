package com.fykj.scaffold.advanced_unit.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitImportDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.UnitAuditDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.UnitSaveDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuUnit;
import com.fykj.scaffold.advanced_unit.domain.params.AuUnitParams;
import com.fykj.scaffold.advanced_unit.domain.params.UnitActivityParams;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.zyz.domain.dto.ActivityApiDto;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 文明单位-申请表
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
public interface IAuUnitService extends IBaseService<AuUnit> {

    /**
     * 保存单位信息
     *
     * @param dto 包含待保存单位信息的DTO对象
     */
    void submitUnit(UnitSaveDTO dto, boolean manager);

    /**
     * 编辑单位信息
     *
     * @param unit 单位信息
     */
    void editUnit(AuUnit unit);

    /**
     * 审核单位信息
     *
     * @param dto 包含待审核单位信息的DTO对象
     */
    void audit(UnitAuditDTO dto);

    /**
     * 根据查询参数查询AuUnit分页数据
     *
     * @param params 查询参数
     * @return 返回查询到的AuUnit分页数据
     */
    IPage<AuUnit> queryPage(AuUnitParams params);

    /**
     * 根据查询参数查询AuUnit列表数据
     *
     * @param params 查询参数
     * @return 返回查询到的AuUnit列表数据
     */
    List<AuUnit> queryList(AuUnitParams params);

    /**
     * 根据ID查询AuUnit的详细信息
     *
     * @param id AuUnit的唯一标识ID
     * @return 返回查询到的AuUnit对象
     */
    AuUnit queryDetail(Long id);

    /**
     * 更新单位的显示状态
     *
     * @param ids 需要更新的单位ID列表
     */
    void updateShowStatus(List<Long> ids);

    /**
     * 更新单位的显示状态
     *
     * @param id 需要更新的单位I
     */
    void updateShowStatus(Long id);

    /**
     * 根据信用代码查询已通过的单位
     * @param creditCode
     * @return
     */
    AuUnit getPassedByCreditCode(String creditCode);

    /**
     * 数据导入
     * @param excel excel
     * @return 错误列表
     */
    List<AuUnitImportDTO> dataImport(MultipartFile excel);

    /**
     * 分页查询关联活动列表
     * @param params 参数
     * @return 活动分页列表
     */
    IPage<ActivityApiDto> queryActivityPage(UnitActivityParams params);

    /**
     * 分页查询关联消息列表
     * @param params 参数
     * @return 消息分页列表
     */
    IPage<News> queryNewsPage(@Valid UnitActivityParams params);

    /**
     * 首页轮播展示
     * @param params
     * @return
     */
    List<AuUnit> homeBannerShow(AuUnitParams params);

    /**
     * 获取文明单位注册待审核数量
     * @return 待审核数量
     */
    Integer getAdvancedUnitWaitAuditNum();
}
