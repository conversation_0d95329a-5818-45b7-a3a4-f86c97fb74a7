package com.fykj.scaffold.security.login.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：yangxu
 * @Date：2025/1/9 16:55
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PasswordForgetUpdateDto implements Serializable {

    private static final long serialVersionUID = 3146219370168119605L;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("数据（加密）")
    private String data;
}
