package com.fykj.scaffold.race.domain.dto;

import com.fykj.scaffold.race.domain.entity.Photo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 照片路径情况详情
 *
 * <AUTHOR> @email ${email}
 * @date 2022-10-08
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("照片路径")
public class WinPhotoDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;


    @ApiModelProperty(value = "奖项")
    private String prizeName;

    @ApiModelProperty(value = "获奖作品列表")
    private List<Photo> photoList;

    @ApiModelProperty(value = "奖项排序")
    private Integer prizeSequence;
}
