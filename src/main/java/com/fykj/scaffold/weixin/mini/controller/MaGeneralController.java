package com.fykj.scaffold.weixin.mini.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 微信小程序用户接口
 */
@Slf4j
@RestController
@RequestMapping("/api/ma/general")
@Api(tags = "微信小程序用户接口")
public class MaGeneralController {

    @ApiOperation(value = "生成小程序链接")
    @GetMapping(value = "/getUnLimitMiniAppLink")
    public Result getUnLimitMiniAppLink(String appId, String path, String query) {
        try {
            final WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            String url = wxMaService.getLinkService()
                    .generateUrlLink(GenerateUrlLinkRequest.builder().path(path).query(query).build());
            return new JsonResult<>(url);
        } catch (WxErrorException e) {
            log.error("链接生成失败，请联系管理员", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "链接生成失败，请联系管理员!");
        }
    }

}

