package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class ActBaseInfoDockingDto implements Serializable {

    private static final long serialVersionUID = -2986308051446794325L;

    @ApiModelProperty(value = "活动ID", example = "123456789")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "数据版本号（用于乐观锁）", example = "1")
    private Integer version;

    @ApiModelProperty(value = "活动名称", required = true, example = "社区环保志愿活动")
    private String name;

    @ApiModelProperty(value = "关联领域ID集合", notes = "活动所属的多个领域分类")
    private List<String> fieldIds = new ArrayList<>();

    @ApiModelProperty(value = "顶级领域编码", example = "ENVIRONMENT")
    private String belongFieldTop;

    @ApiModelProperty(value = "顶级领域名称", example = "环境保护")
    private String belongFieldNameTop;

    @ApiModelProperty(value = "末端领域编码", example = "GARBAGE_CLASSIFICATION")
    private String belongFieldEnd;

    @ApiModelProperty(value = "末端领域名称", example = "垃圾分类")
    private String belongFieldNameEnd;

    @ApiModelProperty(value = "是否组织内部活动", notes = "true-仅组织成员可见，false-公开活动")
    private Boolean orgSelf;

    @ApiModelProperty(value = "所属团队ID", example = "987654321")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    @ApiModelProperty(value = "招募对象描述", example = "18岁以上志愿者")
    private String recruitTargets;

    @ApiModelProperty(value = "招募对象数组", notes = "用于前端多选展示")
    private List<String> recruitTargetArr;

    @ApiModelProperty(value = "是否区分招募人数", notes = "true-不同对象有独立人数限制")
    private Boolean recruitNumDistinguish;

    @ApiModelProperty(value = "是否需要审核申请", notes = "true-需人工审核参与者")
    private Boolean applyCheck;

    @ApiModelProperty(value = "活动是否开放", notes = "false-草稿状态")
    private Boolean open;

    @ApiModelProperty(value = "受益人数预估", example = "100")
    private Integer benefitPeopleNum;

    @ApiModelProperty(value = "是否显示审核状态", notes = "true-前端展示审核流程")
    private Boolean auditShow;

    @ApiModelProperty(value = "是否置顶", notes = "true-在列表优先展示")
    private Boolean top;

    @ApiModelProperty(value = "是否校验地理位置", notes = "true-需在指定距离内签到")
    private Boolean checkLocation;

    @ApiModelProperty(value = "签到允许距离（米）", example = "500")
    private Integer locationDistance;

    @ApiModelProperty(value = "活动详细地址", example = "XX市XX区XX路123号")
    private String address;

    @ApiModelProperty(value = "经度", example = "116.404")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度", example = "39.915")
    private BigDecimal latitude;

    @ApiModelProperty(value = "联系人姓名", required = true, example = "张先生")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话", required = true, example = "13800138000")
    private String contactPhone;

    @ApiModelProperty(value = "活动简介", required = true, example = "本次环保活动主要清理社区垃圾...")
    private String actSynopsis;

    @ApiModelProperty(value = "报名要求", example = "需自备手套和口罩")
    private String applicationRequirements;

    @ApiModelProperty(value = "封面图片URL", example = "https://example.com/pic.jpg")
    private String picture;

    @ApiModelProperty(value = "活动地点区域编码", notes = "行政区划代码", example = "110105")
    private String activityPlaceRegionCode;

    @ApiModelProperty(value = "发布组织编码", notes = "关联组织体系", example = "ORG_2023")
    private String publishOrgCode;

    @ApiModelProperty(value = "街道主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long streetId;

    @ApiModelProperty(value = "社区主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long communityId;


    @ApiModelProperty(value = "审核原因")
    private String reviewReason;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    private boolean  webShow;
}
