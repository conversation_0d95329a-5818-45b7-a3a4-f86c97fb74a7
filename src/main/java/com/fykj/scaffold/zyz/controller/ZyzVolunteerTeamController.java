package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamApplyDto;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerTeamParams;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 志愿者与团队关系表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-13
 */
@RestController
@RequestMapping("/admin/zyz/volunteer/team")
@Api(tags = "志愿者与团队关系接口")
public class ZyzVolunteerTeamController extends BaseController<IZyzVolunteerTeamService, ZyzVolunteerTeam, ZyzVolunteerTeamParams> {


    @ApiOperation("获取团队志愿者--分页查询")
    @PostMapping("/getMyVolunteerPages")
    public JsonResult<IPage<VolunteerTeamDto>> getMyVolunteerPages(@RequestBody ZyzVolunteerTeamParams params) {
        IPage<VolunteerTeamDto> iPage = baseService.getMyVolunteerPages(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @ApiOperation("获取团队志愿者--列表")
    @GetMapping("/getTeamMembers")
    public JsonResult<List<VolunteerTeamDto>> getTeamMembers(@RequestParam Long teamId) {
        List<VolunteerTeamDto> result = baseService.getTeamMembers(teamId);
        return new JsonResult<>(result);
    }

    @AuditLog("移除团队志愿者")
    @ApiOperation("移除")
    @GetMapping({"/removeVolunteer"})
    public Result removeVolunteer(Long teamId, Long volunteerId) {
        boolean result = baseService.removeVolunteer(teamId, volunteerId);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @AuditLog("导出团队志愿者")
    @PostMapping("/exportVolunteer")
    @ApiOperation("导出数据到Excel")
    public void exportTeam(@RequestBody ZyzVolunteerTeamParams params) {
        List<VolunteerTeamDto> list = baseService.getTeamVolunteerList(params);
        DictTransUtil.trans(list);
//        List<VolunteerTeamDto> dtoList = list.stream().map(it -> {
//            ZyzTeamExportDto dto = new ZyzTeamExportDto();
//            BeanUtil.copyProperties(it, dto);
//            return dto;
//        }).collect(Collectors.toList());
        ExcelUtil.fillExcel(list, "team-volunteer.xlsx", VolunteerTeamDto.class);
    }

    @ApiOperation("获取团队志愿者活动报名情况--分页查询")
    @PostMapping("/getTeamVolunteerApplyList")
    public JsonResult<IPage<VolunteerTeamApplyDto>> getTeamVolunteerApplyList(@RequestBody ZyzVolunteerTeamParams params) {
        IPage<VolunteerTeamApplyDto> iPage = baseService.getTeamVolunteerApplyList(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @AuditLog("导出团队报名")
    @PostMapping("/exportTeamApply")
    @ApiOperation("导出团队报名")
    public void exportTeamApply(@RequestBody ZyzVolunteerTeamParams params) {
        params.setPageSize(-1);
        IPage<VolunteerTeamApplyDto> iPage = baseService.getTeamVolunteerApplyList(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        ExcelUtil.fillExcel(iPage.getRecords(), "team-apply.xlsx", VolunteerTeamApplyDto.class);
    }
}
