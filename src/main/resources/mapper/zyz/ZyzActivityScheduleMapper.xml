<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityScheduleMapper">
    <select id="pagesForSchedule" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivitySchedule">
        SELECT
        s.*,
        u.name as creatorName,
        count(d.id) as detailCount,
        count(p.id) as joinActCount
        FROM zyz_activity_schedule s
        LEFT JOIN sys_org o on s.publish_org_code = o.`code`
        LEFT JOIN sys_user u on u.id=s.creator
        LEFT JOIN zyz_activity_schedule_detail d on d.schedule_id=s.id  AND d.is_deleted=0
        LEFT JOIN zyz_activity_schedule_act_plan p on p.schedule_detail_id=d.id AND p.is_deleted=0
        WHERE s.is_deleted = FALSE
        <if test="params.key != null and params.key != ''">
            AND ( s.title LIKE CONCAT('%', CONCAT(#{params.key}, '%'))  )
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and s.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.myOrgCode!='top_dept' and params.myOrgCode != null and params.myOrgCode != ''">
            AND s.publish_org_code LIKE CONCAT(#{params.myOrgCode},'%')
        </if>
        <if test="params.myOrgCode != null and params.myOrgCode != '' ">
            AND ( (s.publish_org_code !=#{params.myOrgCode} AND s.audit_status = 'act_schedule_audit_success') or(s.publish_org_code =#{params.myOrgCode}))
        </if>
        <if test="params.scheduleType != null and params.scheduleType != ''">
            AND s.schedule_type = #{params.scheduleType}
        </if>
        <if test="params.scheduleTypeData != null and params.scheduleTypeData != '' ">
            AND s.schedule_type_data = #{params.scheduleTypeData}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != '' ">
            AND s.audit_status = #{params.auditStatus}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND  (s.create_date &lt; #{params.endTime} AND  s.create_date &gt; #{params.startTime} )
        </if>
        GROUP BY s.id
        ORDER BY s.create_date DESC
    </select>

    <select id="pagesForAudit" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivitySchedule">
        SELECT
        s.*,
        u.name as creatorName
        FROM zyz_activity_schedule s
        LEFT JOIN sys_org o on s.publish_org_code = o.`code`
        LEFT JOIN sys_user u on u.id=s.creator
        WHERE s.is_deleted = FALSE
        <if test="params.key != null and params.key != ''">
            AND ( s.title LIKE CONCAT('%', CONCAT(#{params.key}, '%'))  )
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and s.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND s.audit_status = #{params.auditStatus}
        </if>
        <if test="params.scheduleType != null and params.scheduleType != '' ">
            AND s.schedule_type = #{params.scheduleType}
        </if>
        <if test="params.scheduleTypeData != null and params.scheduleTypeData != '' ">
            AND s.schedule_type_data = #{params.scheduleTypeData}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND  (s.create_date &lt; #{params.endTime} AND  s.create_date &gt; #{params.startTime} )
        </if>
        <if test="params.needAudit != null and params.needAudit == true ">
            AND s.audit_org_code = #{params.myOrgCode}
            AND s.audit_status = 'act_schedule_wait_audit'
        </if>
        ORDER BY s.create_date DESC
    </select>
    <select id="getNeedSyncRecords" resultType="java.lang.Long">
        SELECT s.id
        FROM zyz_activity_schedule s
        WHERE s.is_deleted = 0  AND s.audit_status = 'act_schedule_audit_success'
          AND (s.sync = 'sync_wait' OR (  s.sync = 'sync_failure' AND s.sync_time &gt;= date_sub(now(),interval 3 day)))
    </select>

    <select id="pagesForReport" resultType="com.fykj.scaffold.zyz.domain.dto.ActivityScheduleReportDto">
    SELECT
        o.name AS orgName,
        #{params.scheduleTypeData} AS scheduleTypeData,
        SUM(IF( s.audit_status='act_schedule_audit_success' ,1,0)) AS actSchedulePassNum,
        SUM(IF( s.audit_status='act_schedule_wait_audit' ,1,0)) AS actScheduleProcessingNum,
        SUM(IF( s.audit_status='act_schedule_reject' ,1,0)) AS actScheduleRejectNum
    FROM  sys_org o LEFT JOIN  zyz_activity_schedule s ON o.code=s.publish_org_code AND schedule_type_data=#{params.scheduleTypeData}
    WHERE o.is_deleted=0
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND o.code LIKE CONCAT(#{params.publishOrgCode}, '%')
        </if>
    GROUP BY o.id
    </select>
</mapper>
