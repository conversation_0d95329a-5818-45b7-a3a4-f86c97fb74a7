package com.fykj.scaffold.portal_website.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 新闻资讯审核查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("新闻资讯审核查询参数")
public class NewsAuditParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    @MatchType(value = QueryType.EQ, fieldName = "content_id")
    private Long contentId;
}
