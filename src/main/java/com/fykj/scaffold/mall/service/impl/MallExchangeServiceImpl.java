package com.fykj.scaffold.mall.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.dto.*;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.params.MallExchangeParams;
import com.fykj.scaffold.mall.mapper.MallExchangeMapper;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.fykj.scaffold.mall.service.IMallPadExchangeStationService;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.mall.service.IMallStockService;
import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.entity.SysUserAddress;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.ISysUserAddressService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.fykj.scaffold.mall.cons.MallCons.MALL_EXCHANGE_STATUS_SUCCESS;
import static com.fykj.scaffold.mall.cons.MallCons.MALL_SCORE_GOODS_TYPE_PAD;
import static com.fykj.scaffold.support.conns.Cons.FIVE_AREA_SUB_ASSOCIATION;


/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MallExchangeServiceImpl extends BaseServiceImpl<MallExchangeMapper, MallExchange> implements IMallExchangeService {
    /**
     * md5秘钥
     */
    private final static String MD5_SALT = "Fyxmt@1";

    @Autowired
    private IMallScoreGoodsService mallScoreGoodsService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ISysUserAddressService addressService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IMallStockService stockService;

    @Autowired
    private IMallPadExchangeStationService stationService;

    /**
     * 分页查询--后端页面
     *
     * @param params
     * @return
     */
    @Override
    public IPage<MallExchange> getPage(MallExchangeParams params) {
        if (params == null) {
            params = new MallExchangeParams();
        }
        IPage<MallExchange> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public List<MallExchangeDto> getExchangeList(MallExchangeParams params) {
        return DictTransUtil.trans(baseMapper.getExchangeList(params));
    }

    @Override
    public boolean exchange(Long id, Long addressId, String orderNo, Boolean isEms) {
        MallScoreGoods goods = mallScoreGoodsService.getById(id);
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        // 每个商品每个账号每月限兑换1次校验
        //validateThisMonthExchanged(id, volunteer.getId());
        validateFrequency(goods, volunteer);
        //根据兑换类型不同，取不同的所需分值
        final BigDecimal needScore = isEms ? goods.getRequiredScore() : goods.getOfflineScore();
        if (volunteer.getTotalPoint().compareTo(needScore) < 0) {
            throw new BusinessException(ResultCode.FAIL, "您的积分可用余额不足！");
        }
        final BigDecimal orgPoint = new BigDecimal(100);
        if (volunteer.getTotalPoint().compareTo(orgPoint) < 0 && Cons.TOP_DEPT_CODE.equals(goods.getOrgCode())) {
            throw new BusinessException(ResultCode.FAIL, "用户积分不足100,不能兑换协会商品，请前往分协会商城兑换");
        }
        MallExchange exchange = new MallExchange();
        exchange.setExchangeGoodsId(goods.getId());
        exchange.setExchangeChannel("积分商城兑换");
        exchange.setExchangeNum(1);
        exchange.setExchangeVolunteerId(volunteer.getId());
        exchange.setExchangeUserLinkPhone(volunteer.getPhone());
        exchange.setExchangeUserName(volunteer.getName());
        exchange.setExchangeTime(LocalDateTime.now());
        exchange.setGoodsName(goods.getGoodsName());
        exchange.setOrderNum(orderNo);
        exchange.setGoodsLogo(goods.getGoodsLogo());
        exchange.setOrgCode(goods.getOrgCode());
        exchange.setExchangePoint(needScore);
        exchange.setExchangeBeforeScore(volunteer.getTotalPoint());
        exchange.setExchangeAfterScore(volunteer.getTotalPoint().subtract(needScore));
        if (isEms) {
            SysUserAddress address = addressService.getById(addressId);
            exchange.setExchangeType(MallCons.MALL_EXCHANGE_TYPE_ONLINE);
            exchange.setStatus(MALL_EXCHANGE_STATUS_SUCCESS);
            exchange.setUserAddressId(address.getId());
            exchange.setAddress(address.getAreaAddress());
            exchange.setRecipient(address.getUserName());
            exchange.setRecipientPhone(address.getLinkPhone());
            exchange.setCounty(address.getAreaName());
            exchange.setCity(address.getCityName());
            exchange.setProv(address.getProvinceName());
            exchange.setEmsStatus(MallCons.MALL_EXCHANGE_EMS_STATUS_DELIVERY);
        } else {
            exchange.setStatus(MallCons.MALL_EXCHANGE_STATUS_WRITTEN_EXCHANGE);
            exchange.setExchangeType(MallCons.MALL_EXCHANGE_TYPE_OFFLINE);
            exchange.setVerificationCode(RandomUtil.randomNumbers(6));
            int expireAfterDays = Integer.parseInt(dictService.getByCode(MallCons.SYSTEM_CONFIG_OFFLINE_TIME).getValue());
            exchange.setExpireTime(LocalDateTime.now().plusDays(expireAfterDays));
        }
        save(exchange);
        //减少积分
        AddPointRecordDto addPointRecordDto = AddPointRecordDto
                .builder()
                .changePoint(needScore)
                .bizId(exchange.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                .remark("志愿者：" + volunteer.getName() + (isEms ? "线上兑换" : "线下兑换") + goods.getGoodsName())
                .volunteerId(volunteer.getId())
                .build();
        volunteerService.reducePoint(addPointRecordDto);
        mallScoreGoodsService.changeGoodsNum(id, -1, "积分兑换");
        stockService.reduceStockTotalQuantity(goods.getSku(), 1);
        return true;
    }

    @Override
    public boolean padGoodsExchange(Long id, String orderNo, String stationCode) {
        MallScoreGoods goods = mallScoreGoodsService.getById(id);
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        // 每个商品每个账号每月限兑换1次校验
//        validateThisMonthExchanged(id, volunteer.getId());
        BigDecimal needScore = goods.getOfflineScore();
        if (volunteer.getTotalPoint().compareTo(needScore) < 0) {
            throw new BusinessException(ResultCode.FAIL, "您的积分可用余额不足！");
        }
        if (!MALL_SCORE_GOODS_TYPE_PAD.equals(goods.getScoreGoodsType())) {
            throw new BusinessException(ResultCode.FAIL, "您兑换的商品不是pad兑换商城中的商品！");
        }
        MallPadExchangeStation station = stationService.lambdaQuery().eq(MallPadExchangeStation::getCode, stationCode).eq(MallPadExchangeStation::getStatus, 1).one();
        if (ObjectUtil.isEmpty(station)) {
            throw new BusinessException(ResultCode.FAIL, "code为【" + stationCode + "】的PAD兑换站点不存在或未启用！");
        }
        MallExchange exchange = new MallExchange();
        exchange.setOrderNum(orderNo);
        exchange.setExchangeType(MallCons.MALL_EXCHANGE_TYPE_PAD);
        exchange.setExchangeVolunteerId(volunteer.getId());
        exchange.setExchangeUserName(volunteer.getName());
        exchange.setExchangeUserLinkPhone(volunteer.getPhone());
        exchange.setExchangeGoodsId(goods.getId());
        exchange.setExchangePoint(needScore);
        exchange.setExchangeNum(1);
        exchange.setGoodsName(goods.getGoodsName());
        exchange.setStationCode(stationCode);
        exchange.setExchangeTime(LocalDateTime.now());
        exchange.setOrgCode(goods.getOrgCode());
        exchange.setExchangeBeforeScore(volunteer.getTotalPoint());
        exchange.setExchangeAfterScore(volunteer.getTotalPoint().subtract(needScore));
        exchange.setStatus(MallCons.MALL_EXCHANGE_STATUS_SUCCESS);
        exchange.setExchangeChannel("PAD兑换站点兑换");
        exchange.setGoodsLogo(goods.getGoodsLogo());
        save(exchange);
        //减少积分
        AddPointRecordDto addPointRecordDto = AddPointRecordDto
                .builder()
                .changePoint(needScore)
                .bizId(exchange.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                .remark("志愿者：" + volunteer.getName() + "在PAD兑换站点" + station.getName() + "线下兑换" + goods.getGoodsName())
                .volunteerId(volunteer.getId())
                .build();
        volunteerService.reducePoint(addPointRecordDto);
        mallScoreGoodsService.changeGoodsNum(id, -1, "积分兑换");
        stockService.reduceStockTotalQuantity(goods.getSku(), 1);
        return true;
    }

    @Override
    public boolean cancelExchange(Long id, boolean isApp) {
        MallExchange exchange = getById(id);
        MallScoreGoods goods = mallScoreGoodsService.getById(exchange.getExchangeGoodsId());
        if (MallCons.MALL_EXCHANGE_TYPE_ONLINE.equals(exchange.getExchangeType())) {
            if (!MallCons.MALL_EXCHANGE_EMS_STATUS_DELIVERY.equals(exchange.getEmsStatus())) {
                throw new BusinessException(ResultCode.FAIL, "仅待发货状态,可取消兑换");
            }
        } else {
            if (!MallCons.MALL_EXCHANGE_STATUS_WRITTEN_EXCHANGE.equals(exchange.getStatus()) && !MALL_SCORE_GOODS_TYPE_PAD.equals(goods.getScoreGoodsType())) {
                throw new BusinessException(ResultCode.FAIL, "仅待核销状态,可取消兑换");
            }
        }
        mallScoreGoodsService.changeGoodsNum(exchange.getExchangeGoodsId(), 1, "取消积分兑换");
        stockService.addStockTotalQuantity(goods.getSku(), 1);
        if (isApp) {
            String phone = Oauth2Util.getMobile();
            if (StringUtil.isEmpty(phone)) {
                throw new BusinessException(ResultCode.FAIL, "请检查，志愿者手机号未找到！");
            }
            if (!volunteerService.getCurrentUserVolunteerInfo().getId().equals(exchange.getExchangeVolunteerId())) {
                throw new BusinessException(ResultCode.FAIL, "请检查，兑换人并非该用户未找到！");
            }
        }
        ZyzVolunteer volunteer = volunteerService.getById(exchange.getExchangeVolunteerId());
        if (ObjectUtil.isEmpty(volunteer)) {
            throw new BusinessException(ResultCode.FAIL, "请检查，志愿者未找到！");
        }
        String remark = "志愿者：" + volunteer.getName() + "取消兑换" + exchange.getGoodsName();
        //增加积分
        AddPointRecordDto addPointRecordDto = AddPointRecordDto
                .builder()
                .changePoint(exchange.getExchangePoint())
                .bizId(exchange.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                .remark(remark)
                .volunteerId(volunteer.getId())
                .build();
        volunteerService.addPoint(addPointRecordDto);
        if (!exchange.getExpireTime().toLocalDate().isBefore(LocalDate.now().minusYears(1).with(TemporalAdjusters.lastDayOfYear())) &&
                !exchange.getExchangeTime().isAfter(LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfYear()), LocalTime.of(0, 10, 0)))) {
            dealPossibleClearExpiredPointExchangeCancel(exchange);
        }
        exchange.setStatus(MallCons.MALL_EXCHANGE_STATUS_CANCEL_EXCHANGE);
        if (!MALL_SCORE_GOODS_TYPE_PAD.equals(goods.getScoreGoodsType())) {
            exchange.setEmsStatus(MallCons.MALL_EXCHANGE_EMS_STATUS_ESS_CANCEL);
            exchange.setExpireTime(LocalDateTime.now());
        }
        return updateById(exchange);
    }

    private void dealPossibleClearExpiredPointExchangeCancel(MallExchange exchange) {
        Integer year = LocalDate.now().getYear();
        Long volunteerId = exchange.getExchangeVolunteerId();
        BigDecimal cancelNeedClearPoint = baseMapper.getVolunteerCancelExchangeNeedClearPointRemain(year, volunteerId);
        if (ObjectUtil.isEmpty(cancelNeedClearPoint) || cancelNeedClearPoint.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        BigDecimal needClear, exchangePoint = exchange.getExchangePoint();
        if (exchangePoint.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        if (exchangePoint.compareTo(cancelNeedClearPoint) >= 0) {
            needClear = cancelNeedClearPoint;
            cancelNeedClearPoint = BigDecimal.ZERO;
        } else {
            needClear = exchangePoint;
            cancelNeedClearPoint = cancelNeedClearPoint.subtract(needClear);
        }
        AddPointRecordDto clearPointRecordDto = AddPointRecordDto
                .builder()
                .changePoint(needClear)
                .bizId(exchange.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                .remark("取消兑换积分退回，积分过期自动清除")
                .volunteerId(volunteerId)
                .build();
        volunteerService.reducePoint(clearPointRecordDto, LocalDateTime.now().plusSeconds(3));
        baseMapper.updateVolunteerNeedClearPoint(year, volunteerId, cancelNeedClearPoint);
    }

    @Override
    public boolean writeOffVerificationCode(Long exchangeId, String verificationCode) {
        MallExchange exchange = getById(exchangeId);
        if (!MallCons.MALL_EXCHANGE_STATUS_WRITTEN_EXCHANGE.equals(exchange.getStatus())) {
            throw new BusinessException(ResultCode.FAIL, "您已经核销过了请勿重复点击，请重新输入");
        }
        if (!verificationCode.equals(exchange.getVerificationCode())) {
            throw new BusinessException(ResultCode.FAIL, "核销码有误，请重新输入");
        }
        if (LocalDateTime.now().isAfter(exchange.getExpireTime())) {
            throw new BusinessException(ResultCode.FAIL, "核销码已过期，请取消后重新兑换");
        }
        BackendUserDetail currentUser = Oauth2Util.getUser();
        if (currentUser == null) {
            throw new BusinessException(ResultCode.ERROR, "系统错误无法检测到当前登录人");
        }
        exchange.setStatus(MALL_EXCHANGE_STATUS_SUCCESS);
        exchange.setVerifyTime(LocalDateTime.now());
        exchange.setVerifyUserId((Long) Oauth2Util.getUserId());
        exchange.setVerifyUserName(currentUser.getNickName());
        return updateById(exchange);
    }

    @Override
    public IPage<MallExchange> pageForApi(MallExchangeParams params) {
        if (params == null) {
            params = new MallExchangeParams();
        }
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        if (ObjectUtil.isEmpty(volunteer)) {
            throw new BusinessException(ResultCode.FAIL, "用户未处于登录状态，请重新试一下");
        }
        params.setUserId(volunteer.getId());
        IPage<MallExchange> iPage = baseMapper.pageForApi(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public MallExchange getExchangeForApi(Long id) {
        MallExchange exchange = getById(id);
        DictTransUtil.trans(exchange);
        return exchange;
    }

    @Override
    public void exchangeByMachine(PointExchangeDto dto) {
        //订单编号+消费积分+兑换商品名称+机器码+签名
        String sign = MD5.create().digestHex(dto.getOrderNum() + dto.getPoint() + dto.getGoodsName() + dto.getMachineNo() + MD5_SALT);
        if (!sign.equals(dto.getSign())) {
            throw new BusinessException(ResultCode.NOT_VALID, "签名校验不通过");
        }

        if (findByOrderNum(dto.getOrderNum()) != null) {
            throw new BusinessException(ResultCode.NOT_VALID, "请勿重复兑换");
        }

        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        if (volunteer.getTotalPoint().compareTo(dto.getPoint()) < 0) {
            throw new BusinessException(ResultCode.NOT_VALID, "积分不足");
        }
        String goodsName = dto.getGoodsName();
        if (!goodsName.contains(")")) {
            throw new BusinessException(ResultCode.NOT_VALID, "兑换机有误,未按格式给与商品名称");
        }
        String goodsCode = goodsName.substring(goodsName.indexOf("(") + 1, goodsName.indexOf(")"));
        log.error("商品信息：" + dto.getMachineNo() + dto.getGoodsName());
        log.error("商品信息：" + goodsCode);
        MallScoreGoods goods = mallScoreGoodsService.lambdaQuery().eq(MallScoreGoods::getMachineNo, dto.getMachineNo()).eq(MallScoreGoods::getMachineGoodsCode, goodsCode).one();
        if (ObjectUtil.isEmpty(goods)) {
            throw new BusinessException(ResultCode.NOT_VALID, "商品编码有误,请联系管理员修改");
        }

        //如果是星光闪耀商品的话，需要额外的一些验证，星级要求，积分要求，当年服务时长要求，兑换频次要求等等
        if (goods.getStar() != null && goods.getStar()) {
            validateStartGoodsExchanged(goods, volunteer);
        } else {
            //兑换频次验证（2024-11-06） 非星官闪耀商品兑换频次也要根据配置来
            validateFrequency(goods, volunteer);
            if (goods.getGoodsCount() - 1 < 0) {
                throw new BusinessException(ResultCode.NOT_VALID, "商品库存不足,请联系管理员修改");
            }
            if (Cons.TOP_DEPT_CODE.equals(goods.getOrgCode())) {
                BigDecimal orgPoint = new BigDecimal(100);
                if (volunteer.getTotalPoint().compareTo(orgPoint) < 0  ) {
                    throw new BusinessException(ResultCode.FAIL, "用户积分不足100,不能兑换协会商品，请前往分协会商城兑换");
                }
            } else {
                validate25100Point(goods, volunteer);
            }
        }
        MallExchange exchange = new MallExchange();
        exchange.setExchangeTime(LocalDateTime.now());
        exchange.setStatus(MallCons.MALL_EXCHANGE_STATUS_SHIPPING);
        exchange.setExchangeVolunteerId(volunteer.getId());
        exchange.setExchangeBeforeScore(volunteer.getTotalPoint());
        exchange.setExchangePoint(dto.getPoint());
        exchange.setExchangeAfterScore(volunteer.getTotalPoint().subtract(dto.getPoint()));
        exchange.setExchangeType(MallCons.MALL_EXCHANGE_TYPE_MACHINE);
        exchange.setExchangeVolunteerId(volunteer.getId());
        exchange.setExchangeUserLinkPhone(volunteer.getPhone());
        exchange.setExchangeUserName(volunteer.getName());
        BeanUtil.copyProperties(dto, exchange);
        exchange.setExchangeNum(1);
        exchange.setGoodsName(goods.getGoodsName());
        exchange.setGoodsLogo(goods.getGoodsLogo());
        exchange.setOrgCode(goods.getOrgCode());
        exchange.setGoodsPoint(dto.getPoint());
        exchange.setExchangeGoodsId(goods.getId());
        exchange.setExchangeChannel("兑换机兑换");
        save(exchange);
        mallScoreGoodsService.changeGoodsNum(goods.getId(), -1, "兑换机兑换");
        stockService.reduceStockTotalQuantity(goods.getSku(), 1);
        String remark = "兑换机兑换" + dto.getGoodsName();
        //减少积分
        AddPointRecordDto addPointRecordDto = AddPointRecordDto
                .builder()
                .changePoint(dto.getPoint())
                .bizId(exchange.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                .remark(remark)
                .volunteerId(volunteer.getId())
                .build();
        volunteerService.reducePoint(addPointRecordDto);
    }

    private void validateThisMonthExchanged(Long goodsId, Long volunteerId) {
        LocalDateTime thisMonthFirstDay = LocalDateTime.of(LocalDate.now().withDayOfMonth(1), LocalTime.MIDNIGHT);
        if (lambdaQuery().eq(MallExchange::getExchangeVolunteerId, volunteerId)
                .eq(MallExchange::getExchangeGoodsId, goodsId)
                .in(MallExchange::getStatus, new String[]{MallCons.MALL_EXCHANGE_STATUS_WRITTEN_EXCHANGE, MallCons.MALL_EXCHANGE_STATUS_SHIPPING, MallCons.MALL_EXCHANGE_STATUS_SHIPPED, MallCons.MALL_EXCHANGE_STATUS_SUCCESS})
                .ge(MallExchange::getExchangeTime, thisMonthFirstDay).exists()) {
            throw new BusinessException(ResultCode.FAIL, "您本月已成功兑换过该商品，请重新选择商品兑换！");
        }
    }

    @Override
    public MachineResultDto isAcceptOrder(String orderNum) {
        if (findByOrderNum(orderNum) == null) {
            return new MachineResultDto(false, "未支付！");
        } else {
            return new MachineResultDto(true, "已支付！");
        }
    }

    @Override
    public MachineResultDto outStockResult(String orderNum, int result) {
        MallExchange exchange = findByOrderNum(orderNum);
        if (exchange == null) {
            return new MachineResultDto(false, "找不到订单");
        }
        if (!MallCons.MALL_EXCHANGE_STATUS_SHIPPING.equals(exchange.getStatus())) {
            return new MachineResultDto(false, "订单已完成，无法再次出库，请检查");
        }
        ZyzVolunteer volunteer = volunteerService.getById(exchange.getExchangeVolunteerId());
        //失败
        if (result == 0) {
            //改状态
            exchange.setStatus(MallCons.MALL_EXCHANGE_STATUS_SHIPMENT_FAIL);
            updateById(exchange);
            log.warn("兑换机主动反馈订单{}兑换失败，系统已做退分处理", orderNum);
            mallScoreGoodsService.changeGoodsNum(exchange.getExchangeGoodsId(), 1, "兑换机兑换失败，退还库存");
            MallScoreGoods goods = mallScoreGoodsService.getById(exchange.getExchangeGoodsId());
            stockService.addStockTotalQuantity(goods.getSku(), 1);
            String remark = "兑换机兑换" + exchange.getGoodsName() + "出货失败，退还积分";
            //增加积分
            AddPointRecordDto addPointRecordDto = AddPointRecordDto
                    .builder()
                    .changePoint(exchange.getExchangePoint())
                    .bizId(exchange.getId())
                    .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                    .remark(remark)
                    .volunteerId(volunteer.getId())
                    .build();
            volunteerService.addPoint(addPointRecordDto);
        } else {
            //改状态
            exchange.setStatus(MallCons.MALL_EXCHANGE_STATUS_SHIPPED);
            updateById(exchange);
        }
        return new MachineResultDto(true, "处理成功！");
    }

    private MallExchange findByOrderNum(String orderNum) {
        return lambdaQuery().eq(MallExchange::getOrderNum, orderNum).eq(MallExchange::getExchangeType, MallCons.MALL_EXCHANGE_TYPE_MACHINE).one();
    }

    @Override
    public void autoCancelExpireOrder() {
        //查找5分钟前的出货中的订单
        List<MallExchange> expireOrders = lambdaQuery()
                .lt(MallExchange::getExchangeTime, LocalDateTime.now().minus(5, ChronoUnit.MINUTES))
                .eq(MallExchange::getStatus, MallCons.MALL_EXCHANGE_STATUS_SHIPPING)
                .list();
        expireOrders.forEach(it -> {
            //退分
            ZyzVolunteer volunteer = volunteerService.getById(it.getExchangeVolunteerId());
            String remark = "兑换机兑换" + it.getGoodsName() + "，兑换机未反馈，系统自动退分";
            AddPointRecordDto addPointRecordDto = AddPointRecordDto
                    .builder()
                    .changePoint(it.getExchangePoint())
                    .bizId(it.getId())
                    .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                    .remark(remark)
                    .volunteerId(it.getExchangeVolunteerId())
                    .build();
            volunteerService.addPoint(addPointRecordDto);
            mallScoreGoodsService.changeGoodsNum(it.getExchangeGoodsId(), 1, "兑换机兑换失败，退还库存");
            MallScoreGoods goods = mallScoreGoodsService.getById(it.getExchangeGoodsId());
            stockService.addStockTotalQuantity(goods.getSku(), 1);
            //改状态
            it.setStatus(MallCons.MALL_EXCHANGE_STATUS_SHIPMENT_FAIL);
            updateById(it);
            log.warn("订单：{}兑换失败，系统检测到5分钟未反馈是否成功，系统已做退分处理", it.getOrderNum());
        });
    }

    @Override
    public Map<String, Object> pointCardReturnTip() {
        String mobile = Oauth2Util.getMobile();
        Map<String, Object> map = baseMapper.pointCardReturnTip(mobile);
        baseMapper.updatePointCardReturnTip(mobile);
        return map;
    }

    @Override
    public MallStarExchangeDto exchangeForStar(Long id, String orderNo) {
        MallScoreGoods goods = mallScoreGoodsService.getById(id);
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        // 星官闪耀商品兑换验证
        validateStartGoodsExchanged(goods, volunteer);
        // 兑换/扣积分/减库存
        StarGoodsExchangeDto starGoodsExchangeDto = StarGoodsExchangeDto
                .builder()
                .goods(goods)
                .volunteer(volunteer)
                .channel("星光闪耀积分商城")
                .exchangeType(MallCons.MALL_EXCHANGE_TYPE_ONLINE)
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                .orderNo(orderNo)
                .needScore(goods.getOfflineScore())
                .remarks("志愿者：" + volunteer.getName() + "线上兑换" + goods.getGoodsName())
                .build();
        MallExchange exchange = starGoodsExchange(starGoodsExchangeDto);
        //返回成功信息
        MallStarExchangeDto returnDto = new MallStarExchangeDto();
        BeanUtil.copyProperties(exchange, returnDto);
        returnDto.setSuccessMsg(goods.getSuccessMsg());
        return returnDto;
    }

    private MallExchange generateExchangeRecord(StarGoodsExchangeDto starGoodsExchangeDto
                                                //MallScoreGoods goods, ZyzVolunteer volunteer, String channel, String exchangeType, String orderNo, BigDecimal needScore
    ) {
        MallExchange exchange = new MallExchange();
        // 商品信息
        exchange.setExchangeGoodsId(starGoodsExchangeDto.getGoods().getId());
        exchange.setGoodsName(starGoodsExchangeDto.getGoods().getGoodsName());
        exchange.setGoodsLogo(starGoodsExchangeDto.getGoods().getGoodsLogo());
        exchange.setOrgCode(starGoodsExchangeDto.getGoods().getOrgCode());
        // 志愿者信息
        exchange.setExchangeVolunteerId(starGoodsExchangeDto.getVolunteer().getId());
        exchange.setExchangeUserLinkPhone(starGoodsExchangeDto.getVolunteer().getPhone());
        exchange.setExchangeUserName(starGoodsExchangeDto.getVolunteer().getName());
        exchange.setExchangeBeforeScore(starGoodsExchangeDto.getVolunteer().getTotalPoint());
        exchange.setExchangeAfterScore(starGoodsExchangeDto.getVolunteer().getTotalPoint().subtract(starGoodsExchangeDto.getNeedScore()));
        //兑换信息
        exchange.setExchangeChannel(starGoodsExchangeDto.getChannel());
        exchange.setExchangeTime(LocalDateTime.now());
        exchange.setOrderNum(starGoodsExchangeDto.getOrderNo());
        exchange.setExchangeNum(1);
        exchange.setExchangePoint(starGoodsExchangeDto.getNeedScore());
        exchange.setGoodsPoint(starGoodsExchangeDto.getNeedScore());
        exchange.setExchangeType(starGoodsExchangeDto.getExchangeType());
        exchange.setStatus(MALL_EXCHANGE_STATUS_SUCCESS);
        save(exchange);
        return exchange;
    }

    @Override
    public void validateStartGoodsExchanged(MallScoreGoods goods, ZyzVolunteer volunteer) {
        //敬请期待的商品无法兑换
        validateDisplayStatus(goods);
        //库存验证
        validateCount(goods);
        //志愿者积分验证
        validatePoint(goods, volunteer);
        //星级要求的验证
        validateStar(goods, volunteer);
        //当年服务时长要求的验证
        validateCurrentYearServiceLong(goods, volunteer);
        //兑换频次验证
        validateFrequency(goods, volunteer);

    }

    private void validateDisplayStatus(MallScoreGoods goods) {
        //敬请期待的商品无法兑换
        if (StringUtil.isNotEmpty(goods.getDisplayStatus()) && goods.getDisplayStatus().equals(MallCons.GOODS_DISPLAY_STATUS_PLAN)) {
            throw new BusinessException(ResultCode.FAIL, "商品上架中，敬请期待");
        }
    }

    private void validateCount(MallScoreGoods goods) {
        if (goods.getGoodsCount() - 1 < 0) {
            throw new BusinessException(ResultCode.NOT_VALID, "商品库存不足,请联系管理员修改");
        }
    }

    private void validatePoint(MallScoreGoods goods, ZyzVolunteer volunteer) {
        if (goods.getOfflineScore() != null && volunteer.getTotalPoint().compareTo(goods.getOfflineScore()) < 0) {
            throw new BusinessException(ResultCode.FAIL, "您的积分可用余额不足！");
        }
        //现有积分要求的验证
        if (goods.getTotalPoint() != null && !goods.getTotalPoint().equals(BigDecimal.ZERO) && (volunteer.getTotalPoint().compareTo(goods.getTotalPoint()) < 0)) {
            throw new BusinessException(ResultCode.FAIL, "现有积分不满足兑换要求");

        }
    }

    private void validate25100Point(MallScoreGoods goods, ZyzVolunteer volunteer) {
        //分协会商品只给本协会下属社区志愿者兑换，且积分需要大于25
        final BigDecimal point = new BigDecimal(25);
        if (FIVE_AREA_SUB_ASSOCIATION.contains(goods.getOrgCode())) {
            if (volunteer.getTotalPoint().compareTo(point) < 0) {
                throw new BusinessException(ResultCode.FAIL, "用户积分不足25,不能兑换商品");
            }
            if (!volunteer.getOrgCode().contains(goods.getOrgCode())) {
                throw new BusinessException(ResultCode.FAIL, "请前往志愿者所在社区的分协会兑换机兑换");
            }
        }
    }

    private void validateStar(MallScoreGoods goods, ZyzVolunteer volunteer) {
        //星级要求的验证
        if (goods.getServiceLong() != null && !goods.getServiceLong().equals(BigDecimal.ZERO) && (goods.getServiceLong().compareTo(volunteer.getServiceLong()) > 0)) {
            throw new BusinessException(ResultCode.FAIL, "用户星级不满足兑换要求");

        }
    }

    private void validateCurrentYearServiceLong(MallScoreGoods goods, ZyzVolunteer volunteer) {
        //当年服务时长要求的验证
        if (goods.getServiceLongThisYear() != null && !goods.getServiceLongThisYear().equals(BigDecimal.ZERO) && (goods.getServiceLongThisYear().compareTo(volunteer.getServiceLongThisYear()) > 0)) {
            throw new BusinessException(ResultCode.FAIL, "当年服务时长不满足兑换要求");

        }
    }

    private void validateFrequency(MallScoreGoods goods, ZyzVolunteer volunteer) {
        LocalDateTime checkDate = LocalDateTime.of(LocalDate.now().withDayOfMonth(1), LocalTime.MIDNIGHT);
        //如果没有设置，或者设置了不设限，就不需要验证
        if (StringUtil.isNotEmpty(goods.getFrequency()) && !goods.getFrequency().equals(MallCons.GOOD_EXCHANGE_FREQUENCY_NONE)) {
            if (goods.getFrequency().equals(MallCons.GOOD_EXCHANGE_FREQUENCY_MONTH_1)) {
                checkDate = LocalDateTime.of(LocalDate.now().withDayOfMonth(1), LocalTime.MIDNIGHT);

            } else if (goods.getFrequency().equals(MallCons.GOOD_EXCHANGE_FREQUENCY_YEAR_1)) {
                checkDate = LocalDateTime.of(LocalDate.now().withDayOfYear(1), LocalTime.MIDNIGHT);
            }
            List<String> statusList = Arrays.asList(MallCons.MALL_EXCHANGE_STATUS_WRITTEN_EXCHANGE, MallCons.MALL_EXCHANGE_STATUS_SHIPPING, MallCons.MALL_EXCHANGE_STATUS_SHIPPED, MallCons.MALL_EXCHANGE_STATUS_SUCCESS);
            if (lambdaQuery().eq(MallExchange::getExchangeVolunteerId, volunteer.getId())
                    .eq(MallExchange::getExchangeGoodsId, goods.getId())
                    .in(MallExchange::getStatus, statusList)
                    .ge(MallExchange::getExchangeTime, checkDate).exists()) {
                throw new BusinessException(ResultCode.FAIL, "您本年或者本月已成功兑换过该商品，请重新选择商品兑换！");
            }
        }
    }

    @Override
    public MallExchange starGoodsExchange(StarGoodsExchangeDto starGoodsExchangeDto
                                          //MallScoreGoods goods, ZyzVolunteer volunteer, String channel, String exchangeType, String bizType, String orderNo, BigDecimal needScore, String remarks
    ) {
        //生成兑换记录
        MallExchange exchange = generateExchangeRecord(starGoodsExchangeDto);
        //减少积分
        AddPointRecordDto addPointRecordDto = AddPointRecordDto
                .builder()
                .changePoint(starGoodsExchangeDto.getNeedScore())
                .bizId(exchange.getId())
                .bizType(starGoodsExchangeDto.getBizType())
                .remark(starGoodsExchangeDto.getRemarks())
                .volunteerId(starGoodsExchangeDto.getVolunteer().getId())
                .build();
        volunteerService.reducePoint(addPointRecordDto);
        //减库存
        mallScoreGoodsService.changeGoodsNum(starGoodsExchangeDto.getGoods().getId(), -1, "积分兑换");
        //减物料
        stockService.reduceStockTotalQuantity(starGoodsExchangeDto.getGoods().getSku(), 1);
        //如果星光闪耀商品兑换完，显示敬请期待
        MallScoreGoods latestGoods = mallScoreGoodsService.getById(starGoodsExchangeDto.getGoods().getId());
        if (latestGoods.getGoodsCount() <= 0 && latestGoods.getStar() != null && latestGoods.getStar()) {
            latestGoods.setDisplayStatus(MallCons.GOODS_DISPLAY_STATUS_PLAN);
            mallScoreGoodsService.updateById(latestGoods);
        }
        return exchange;
    }
}
