package com.fykj.scaffold.mall.service.impl;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.dto.AddPointRecordDto;
import com.fykj.scaffold.mall.domain.dto.StarGoodsExchangeDto;
import com.fykj.scaffold.mall.domain.dto.ThirdPlatformPointChangeDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.entity.ZyzPointRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointRecordParams;
import com.fykj.scaffold.mall.mapper.ZyzPointRecordMapper;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.mall.service.IZyzPointRecordService;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * 积分商城-积分历史记录
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzPointRecordServiceImpl extends BaseServiceImpl<ZyzPointRecordMapper, ZyzPointRecord> implements IZyzPointRecordService {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IMallScoreGoodsService mallScoreGoodsService;

    @Autowired
    private IMallExchangeService mallExchangeService;

    @Override
    public IPage<ZyzPointRecord> getPage(ZyzPointRecordParams params) {
        if (params == null) {
            params = new ZyzPointRecordParams();
        }
        IPage<ZyzPointRecord> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public IPage<ZyzPointRecord> pageForApi(ZyzPointRecordParams params) {
        if (params == null) {
            params = new ZyzPointRecordParams();
        }
        String phone = Oauth2Util.getMobile();
        if (StringUtil.isEmpty(phone)) {
            throw new BusinessException(ResultCode.TOKEN_ERROR_CODE, "未找到手机号码");
        }
        ZyzVolunteer volunteer = volunteerService.getByPhone(phone);
        params.setVolunteerId(volunteer.getId());
        IPage<ZyzPointRecord> iPage = baseMapper.pageForApi(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public void thirdPlatformChangePoint(ThirdPlatformPointChangeDto dto) {
        //保留一位小数
        dto.setChangePoint(dto.getChangePoint().setScale(1, RoundingMode.HALF_UP));
        if (dto.getChangePoint().multiply(BigDecimal.TEN).intValue() % 5 != 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "积分需为0.5的倍数");
        }
        //验证签名
        String sign = MD5.create().digestHex(dto.getBizId() + dto.getIdCard() + dto.getThirdPlatformCode() + dto.getType() + dto.getChangePoint());
        if (!sign.equals(dto.getSign())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "签名校验不通过，请检查");
        }
        ZyzVolunteer volunteer = volunteerService.lambdaQuery().eq(ZyzVolunteer::getCertificateId, dto.getIdCard()).eq(ZyzVolunteer::getWriteOff, false).one();
        if (volunteer == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者不存在，请核实");
        }
        //判断是否重复
        if (lambdaQuery().eq(ZyzPointRecord::getBizType, dto.getThirdPlatformCode()).eq(ZyzPointRecord::getBizId, dto.getBizId()).exists()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "业务流水重复，请勿重复提交");
        }
        //兑换后商品无法退回，所以无法添加积分
        if (MallCons.ZYZ_POINT_RECORD_TYPE_ADD.equals(dto.getType())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "商品兑换后，无法取消");
        }
        String thirdPlatformName = dictService.getByCode(dto.getThirdPlatformCode()).getName();
        //如果填了商品id .且是扣积分的认为是商品兑换,就会根据商品的配置信息，对兑换进行积分验证
        if (StringUtil.isNotEmpty(dto.getGoodsId())) {
            MallScoreGoods goods = mallScoreGoodsService.getGoodsByThirdPartGoodsCode(dto.getThirdPlatformCode(), dto.getGoodsId());
            if (goods == null) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者平台对应的商品不存在，请联系平台管理员");
            }
            if (dto.getChangePoint().compareTo(goods.getOfflineScore()) != 0) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "扣减的积分与平台维护的积分不一致");
            }
            //验证
            mallExchangeService.validateStartGoodsExchanged(goods, volunteer);
            // 生成兑换记录，扣减积分
            StarGoodsExchangeDto starGoodsExchangeDto = StarGoodsExchangeDto
                    .builder()
                    .goods(goods)
                    .volunteer(volunteer)
                    .channel("第三方平台" + thirdPlatformName)
                    .exchangeType(MallCons.MALL_EXCHANGE_TYPE_THIRD_PART)
                    .bizType(dto.getThirdPlatformCode())
                    .orderNo(String.valueOf(dto.getBizId()))
                    .needScore(dto.getChangePoint())
                    .remarks("第三方平台业务积分变动(" + thirdPlatformName + ")：" + dto.getRemark())
                    .build();
            mallExchangeService.starGoodsExchange(starGoodsExchangeDto);
        } else {
            //加减积分
            AddPointRecordDto addPointRecordDto = AddPointRecordDto
                    .builder()
                    .changePoint(dto.getChangePoint())
                    .bizId(dto.getBizId())
                    .bizType(dto.getThirdPlatformCode())
                    .remark("第三方平台业务积分变动(" + thirdPlatformName + ")：" + dto.getRemark())
                    .volunteerId(volunteer.getId())
                    .build();
            volunteerService.reducePoint(addPointRecordDto);
        }

    }
}
