package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamAuditDto;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerTeamAuditParams;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeamAudit;

/**
 * 志愿者与团队关系审核表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-02-14
 */
public interface IZyzVolunteerTeamAuditService extends IService<ZyzVolunteerTeamAudit>, IBaseService<ZyzVolunteerTeamAudit> {

    /**
     * 分页查询
     * @param params
     * @return
     */
    IPage<VolunteerTeamAuditDto> getPages(ZyzVolunteerTeamAuditParams params);

    /**
     * 审核
     * @param ids
     * @param remake
     * @param auditStatus
     * @return
     */
    boolean volunteerAudit(String ids, String remake, String auditStatus);

    /**
     * 获取加入团队待审核数量
     * @return
     */
    Integer getJoinTeamWaitAuditNum();
}

