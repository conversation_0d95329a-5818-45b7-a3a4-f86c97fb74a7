package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.domain.dto.ActionOrRoleApiPathDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiAction;
import com.fykj.scaffold.security.business.mapper.SysApiActionMapper;
import com.fykj.scaffold.security.business.service.ISysApiActionService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 接口-资源映射表
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysApiActionServiceImpl extends BaseServiceImpl<SysApiActionMapper, SysApiAction> implements ISysApiActionService {

    @Override
    public List<String> actionApi(Long actionId) {
        return lambdaQuery().eq(SysApiAction::getActionId, actionId).list().stream().map(s->String.valueOf(s.getApiId())).collect(Collectors.toList());
    }

    @Override
    public boolean savePath(ActionOrRoleApiPathDto dto) {
        Long actionId = Long.valueOf(dto.getBindId());
        baseMapper.deleteByActionId(actionId);
        if (CollectionUtils.isEmpty(dto.getApiIdList())) {
            return true;
        }
        List<SysApiAction> list = dto.getApiIdList().stream().map(it -> {
            SysApiAction actionApi = new SysApiAction();
            actionApi.setActionId(actionId);
            actionApi.setApiId(Long.valueOf(it));
            return actionApi;
        }).collect(Collectors.toList());
        return saveBatch(list);
    }
}