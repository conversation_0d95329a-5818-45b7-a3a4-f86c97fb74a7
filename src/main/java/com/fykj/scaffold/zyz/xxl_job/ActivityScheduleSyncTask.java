package com.fykj.scaffold.zyz.xxl_job;


import cn.hutool.core.collection.CollUtil;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityScheduleSyncTask {

    @Autowired
    private IZyzActivityScheduleService scheduleService;

    @XxlJob("Activity_Schedule_Sync_Handler")
    public void scheduleSyncJobHandler() {
        XxlJobHelper.log("Activity_Schedule_Sync_Handler-Job, START!");
        // 获取待同步的阵地计划数据以及阵地计划审核通过时间三天内同步失败的数据
        List<Long> scheduleIds = scheduleService.getNeedSync();
        if (CollUtil.isNotEmpty(scheduleIds)) {
            try {
                scheduleService.syncBatch(scheduleIds);
            } catch (Exception e) {
                log.error("阵地计划批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Activity_Schedule_Sync_Handler-Job, END!");
    }
}
