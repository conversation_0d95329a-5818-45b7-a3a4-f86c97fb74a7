package com.fykj.scaffold.sgb_docking.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 苏州志愿者平台服务领域
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("苏州志愿者平台服务领域")
@TableName("sgb_service_type")
public class SgbServiceType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 类型编号
     */
    @TableField("type_id")
    @ApiModelProperty("类型编号")
    private String typeId;

    /**
     * 类型名称
     */
    @TableField("type_name")
    @ApiModelProperty("类型名称")
    private String typeName;

    /**
     * 父级类型编号
     */
    @TableField("father_id")
    @ApiModelProperty("父级类型编号")
    private String fatherId;
    
    /**
     * 节点路径
     */
    @TableField("node_path")
    @ApiModelProperty("节点路径")
    private String nodePath;

    /**
     * 子节点列表
     */
    @TableField(exist = false)
    @ApiModelProperty("子节点列表")
    private List<SgbServiceType> children;
} 