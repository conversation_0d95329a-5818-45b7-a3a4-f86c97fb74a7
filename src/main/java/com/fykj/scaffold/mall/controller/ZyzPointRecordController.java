package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.ZyzPointRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointRecordParams;
import com.fykj.scaffold.mall.service.IZyzPointRecordService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 积分商城-积分历史记录
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@RestController
@RequestMapping("/admin/point/record")
@Api(tags = "积分商城-积分历史接口")
public class ZyzPointRecordController extends BaseController<IZyzPointRecordService, ZyzPointRecord, ZyzPointRecordParams> {

    @AuditLog("积分历史记录分页查询")
    @ApiOperation("积分历史记录分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzPointRecord>> getPages(@RequestBody ZyzPointRecordParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @AuditLog("积分流水记录导出数据到Excel")
    @PostMapping("/exportExchange")
    @ApiOperation("导出数据到Excel")
    public void exportExchange(@RequestBody ZyzPointRecordParams params) {
        params.setPageSize(-1);
        List<ZyzPointRecord> list = baseService.getPage(params).getRecords();
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "point-record-export.xlsx", ZyzPointRecord.class);
    }
}
