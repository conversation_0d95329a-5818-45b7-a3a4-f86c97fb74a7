spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-circular-references: true
  devtools:
    livereload:
      enabled: true
    restart:
      enabled: true
    main:
      allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
  elasticsearch:
    connection-timeout: 1
    socket-timeout: 3000
    uris:
      - http://***********:9200
    username: elastic
    password: FengYunXmt@2023
  redis:
    host: ***********
    password: FengYunXmt@2025
    port: 6379
    jedis:
      pool:
        max-idle: 8
        min-idle: 0
        max-maxTotal: 8
        maxWaitMillis: -1
    database: 12
  datasource: #db Configuration
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *****************************************************************************************************************************************************************************************
      username: root
      password: FengYunXmt@2023
      initialSize: 5 # 初始化大小，最小，最大
      minIdle: 300
      maxActive: 500
      maxWait: 60000 # 配置获取连接等待超时的时间
      timeBetweenEvictionRunsMillis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      minEvictableIdleTimeMillis: 300000 # 配置一个连接在池中最小生存的时间，单位是毫秒
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true # 打开PSCache，并且指定每个连接上PSCache的大小
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,wall # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filter:
        stat:
          slow-sql-millis: 5000
          log-slow-sql: true
          merge-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: true
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: /druid/*,/webjars/*
      stat-view-servlet:
        url-pattern: /druid/*
        enabled: true
        allow: 172.21.7.%
        login-username: root
        login-password: FengYunXmt@2023

server:
  tomcat:
    uri-encoding: UTF-8
  port: 9090
  servlet:
    encoding:
      charset: utf-8
    context-path: /volunteer_api
mybatis-plus:
  type-enums-package: com.fykj.scaffold.project.domain.enums
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
#  mapper-locations: classpath*:/mapper/**/*Mapper.xml
#  #  type-aliases-package: com.logs.entity
#  global-config:
#    db-config:
#      logic-delete-value: 1 #配置逻辑删除字段为1是删除
#      logic-not-delete-value: 0 #配置逻辑删除字段为0是未删除
#      db-type: mysql

#Swagger Configure Properties
fykj:
  swagger:
    enable: false
    packageScan: com.fykj
    title: fykj
    description: API Doc
    version: 2.0
    contact:
      name: FengyunXmt Java Group
      email: <EMAIL>
      url:


virtual:
  path:
    temp: /wordToHtml/

logback:
  file: /home/<USER>/logs

referer:
  refererList:
    - http://localhost:9090/
    - http://localhost:8080/
    - http://yqzyz.sipac.gov.cn/
    - https://yqzyz.sipac.gov.cn/
    - https://zyz.sipac.gov.cn/
    - https://zyz.sipac.gov.cn/
    - https://servicewechat.com/

system:
  domain: http://1711yz7467.imwork.net

qrcode:
  errorPage: http://1711yz7467.imwork.net/#/404

security:
  oauth2:
    client:
      user-authorization-uri: http://127.0.0.1:9090/volunteer_api/oauth/authorize
      access-token-uri: http://127.0.0.1:9090/volunteer_api/oauth/token
      client-secret: FengYunXmt@2023
      client-id: client_2
      grant-type: password
      scope: select
html:
  location: /wordToHtml/

xxl:
  enable: true
  job:
    accessToken:
    executor:
      appname: xxl-job-executor
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: -1
      port: 19999
    admin:
      addresses: http://***********:8081/xxl-job-admin

rocketmq:
  nameServer: ************:9876
  producer:
    group: zyzProducer #自定义的组名称
    send-message-timeout: 3000 #消息发送超时时长
  consumer:
    group: zyzConsumer

wx:
  miniapp:
    configs:
      - appid: wx29145e35261335f9
        secret: 849040eef0bbd4f56f81f38bb0dfe00f
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
  mp:
    configs:
      - appId: wxc4d301cafb380fac # 第一个公众号的appid
        secret: c61de5a5baee53c628b197ee453457ba # 公众号的appsecret
        token: test # 接口配置里的Token值

httpAttachmentUrl: https://zyz.sipac.gov.cn

defaultServerCode: LocalServer

#是否自动同步到市平台
auto-sync-platform: true

#是否发送信息
send-message: true

#活动同步是否按时间分段分开同步
act-sync-by-time-period: false

wmyq:
  sso-auth:
    domain: http://wmyq.sipac.gov.cn/wmyq-prod

auto-push-zsh: true

zwt:
  corpId: ww4aea4448795f0776
  corpSecret: 6Mx01eF2L0W4t8xnuRXdHpH6lDdy1T9jAv0BnhngPAk
  agentId: 1000415
  accessTokenGetUrl: http://sipzwt.sipac.gov.cn/cgi-bin/gettoken?corpid=%s&corpsecret=%s
  userIdGetUrl: http://sipzwt.sipac.gov.cn/cgi-bin/user/getuserinfo?access_token=%s&code=%s
  userInfoGetUrl: http://sipzwt.sipac.gov.cn/cgi-bin/user/get?access_token=%s&userid=%s
  zwtOauth2AuthorizeUrl: http://sipzwt.sipac.gov.cn/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&agentid=%s#wechat_redirect
  oauth2AuthorizeRedirectUri: https://zyz.sipac.gov.cn/volunteer_backend/#/common/blank
  validateSecretKey: e6d7a45af67b586e

csr:
  oneCodeFetchUrl: https://csr.sipac.gov.cn/csr_api/fykj/volunteerSysUserMobileLogin
  loginUrl: https://csr.sipac.gov.cn/csr_api/fykj/login

zsq:
  baseUrl: https://zwyyone.sipac.gov.cn/ebus/fengyuntech
  authTokenUrl: /zsq/third/user/token
  userListUrl: /zsq/third/user/list
  codeToTokenUrl: /zsq/third/code/token
  tokenToUserUrl: /zsq/third/user/userInfo
  appId: xsdzyz
  secret: Fengyuntec@acD132

# 苏州志愿者平台API配置
sgb:
  api:
    url: http://zyfw.swsgb.suzhou.com.cn/openCloudApi/v2?
    app-key: 202503041735010
    secret-key: D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9
    algorithm: HmacSHA256
