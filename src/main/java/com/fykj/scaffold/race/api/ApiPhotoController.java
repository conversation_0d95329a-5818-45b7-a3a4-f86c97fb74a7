package com.fykj.scaffold.race.api;


import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.race.domain.dto.PhotoDto;
import com.fykj.scaffold.race.domain.entity.Activity;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.domain.params.PhotoParams;
import com.fykj.scaffold.race.service.IPhotoService;
import com.fykj.scaffold.race.service.IRaceActivityService;
import exception.BusinessException;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.ResultCode;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 摄影大赛-作品表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@RestController
@RequestMapping("/api/race/photo")
public class ApiPhotoController {

    @Autowired
    private IPhotoService photoService;

    @Autowired
    private IRaceActivityService activityService;
    @ApiOperation("参赛作品分页查询--网页端")
    @PostMapping("/getPagesForWeb")
    public JsonResult<IPage<Photo>> getPagesForWeb(@RequestBody PhotoParams params) {
        return new JsonResult<>(photoService.getPagesForWeb(params));
    }

    private static final String folderPath2017 = "D:\\photo0705\\2022-4";

    @GetMapping("/compressImages")
    public void compressImages() {
        // 加载要压缩的图片文件夹
        String[] imageNames = getImageNames(folderPath2017);
        if (imageNames.length == 0) {
            throw new BusinessException(ResultCode.FAIL, "该文件夹下没有图片文件");
        }
        // 遍历文件夹中所有的图片文件并进行压缩和替换操作

        for (String fileName : imageNames) {
            File file = FileUtil.file(folderPath2017 + File.separator + fileName);
            if (file.length() > 200 * 1024) {
                compressAndReplaceImage(file);
            }

        }
//        //获取子文件夹名称
//        List<String> folderNames = getFolderNames(folderPath2017);
//        folderNames.forEach(it ->{
//            String[] imageNames = getImageNames(folderPath2017 + File.separator + it);
//            for (String fileName : imageNames) {
//                File file = FileUtil.file(folderPath2017 + File.separator + it + File.separator + fileName);
//                //如果文件大于200kb则进行压缩
//                if (file.length() > 200 * 1024) {
//                    compressAndReplaceImage(file);
//                }
//            }
//        });

    }


    // 压缩并替换图片文件
    private void compressAndReplaceImage(File file) {
        System.out.println("Compressing image: " + file.getAbsolutePath());
        BufferedImage img = ImgUtil.read(file);
        ImgUtil.scale(img, file, 0.5f);
        System.out.println("Replace image: " + file.getAbsolutePath());
    }


    //将文件名导出
    public static String[] getImageNames(String folderPath) {
        ArrayList<String> imageNames = new ArrayList<>();
        File folder = new File(folderPath);
        if (folder.isDirectory()) {
            File[] files = folder.listFiles();
            for (File file : files) {
                String name = file.getName();
//               if (name.contains("s")){
                imageNames.add(name);
//

            }
        }
        return imageNames.toArray(new String[0]);
    }

//    public static List<String> getFolderNames(String folderPath) {
//        List<String> folderNames = new ArrayList<>();
//        File folder = new File(folderPath);
//        if (folder.isDirectory()) {
//            File[] files = folder.listFiles();
//            for (File file : files) {
//                if (file.isDirectory()) {
//                    folderNames.add(file.getName());
//                    List<String> subFolderNames = getFolderNames(file.getAbsolutePath());
//                    for (String name : subFolderNames) {
//                        folderNames.add(name);
//                    }
//                }
//            }
//        }
//        return folderNames;
//    }

    @ApiOperation(value = "获取活动列表")
    @GetMapping(value = "/getActivityList")
    public JsonResult<List<Activity>> getActivityList() {
        List<Activity> res = activityService.list().stream().sorted(Comparator.comparing(Activity::getCreateDate).reversed()).collect(Collectors.toList());
        return new JsonResult<>(res);
    }

    @ApiOperation("参赛作品列表")
    @GetMapping("/getPhotoList")
    public JsonResult<List<PhotoDto>> getPhotoList(@RequestParam Long activityId) {
        return new JsonResult<>(photoService.getPhotoList(activityId));
    }

}
