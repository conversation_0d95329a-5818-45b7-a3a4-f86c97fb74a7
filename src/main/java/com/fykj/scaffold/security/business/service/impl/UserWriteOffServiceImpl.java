package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.conns.MsgCons;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.entity.MsgTmp;
import com.fykj.scaffold.message.service.IMsgSendService;
import com.fykj.scaffold.message.service.IMsgTmpService;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.domain.entity.UserWriteOff;
import com.fykj.scaffold.security.business.domain.params.UserWriteOffParams;
import com.fykj.scaffold.security.business.mapper.UserWriteOffMapper;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.security.business.service.IUserWriteOffService;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.Cons.DUTY_TYPE_ADMIN;


/**
 * 系统用户注销管理-服务实现类
 *
 * @date 2025-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class UserWriteOffServiceImpl extends BaseServiceImpl<UserWriteOffMapper, UserWriteOff> implements IUserWriteOffService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IZyzVolunteerTeamService volunteerTeamService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IMsgTmpService tmpService;

    @Autowired
    private IMsgSendService msgSendService;

    @Override
    public IPage<UserWriteOff> pageQuery(UserWriteOffParams params) {
        IPage<UserWriteOff> result = baseMapper.pageQuery(params.getPage(), params);
        result.getRecords().forEach(it -> {
            Boolean userExists = it.getUserExists();
            if (userExists) {
                it.setUserRole(baseMapper.getUserRoleDescription(it.getUserId()));
            }
        });
        return result;
    }

    @Override
    public Map<String, Object> getUserVolunteerAndTeamAdminInfo(Long userId) {
        Map<String, Object> result = new HashMap<>();
        if (userId == null) {
            userId = (Long)Oauth2Util.getUserId();
        }
        if (userId == null) {
            throw new BusinessException(ResultCode.FAIL, "当前登录用户未知！");
        }
        User user = userService.getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.FAIL, "当前登录用户未知！");
        }
        ZyzVolunteer volunteer = volunteerService.getByPhone(user.getMobile());
        if (volunteer == null) {
            return result;
        }
        DesensitiseUtil.desensitise(volunteer);
        result.put("volunteerInfo", volunteer);
        Long volunteerId = volunteer.getId();
        List<ZyzTeam> teams = getUserManageTeams(volunteerId);
        if (teams == null || teams.isEmpty()) {
            return result;
        }
        result.put("manageTeams", teams);
        return result;
    }

    private List<ZyzTeam> getUserManageTeams(Long volunteerId) {
        List<ZyzVolunteerTeam> volunteerTeams = volunteerTeamService.lambdaQuery()
                .eq(ZyzVolunteerTeam::getVolunteerId, volunteerId)
                .eq(ZyzVolunteerTeam::getDutyType, DUTY_TYPE_ADMIN)
                .list();
        if (volunteerTeams.isEmpty()) {
            return null;
        }
        List<Long> teamIds = volunteerTeams.stream().map(ZyzVolunteerTeam::getTeamId).collect(Collectors.toList());
        return teamService.lambdaQuery().in(ZyzTeam::getId, teamIds).list();
    }

    @Override
    public void writeOff(Long userId) {
        if (userId == null) {
            userId = (Long)Oauth2Util.getUserId();
        }
        if (userId == null) {
            throw new BusinessException(ResultCode.FAIL, "注销用户未知！");
        }
        User user = userService.getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.FAIL, "注销用户未知！");
        }
        UserWriteOff writeOff = lambdaQuery().eq(UserWriteOff::getUserId, userId).one();
        if (writeOff != null && !writeOff.getCancelApply()) {
            throw new BusinessException(ResultCode.FAIL, "当前用户已申请注销，请勿重复申请！");
        }
        ZyzVolunteer volunteer = volunteerService.getByPhone(user.getMobile());
        if (volunteer != null) {
            List<ZyzTeam> manageTeams = getUserManageTeams(volunteer.getId());
            if (manageTeams != null && !manageTeams.isEmpty()) {
                throw new BusinessException(ResultCode.FAIL, "当前用户存在管理团队，无法注销，请先取消管理团队！");
            }
        }
        try{
            userService.lambdaUpdate().eq(User::getId, userId).set(User::getStatus, false).update();
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "注销用户失败！");
        }
        if (writeOff == null) {
            writeOff = new UserWriteOff();
        }
        writeOff.setUserId(userId);
        if (volunteer != null) {
            writeOff.setVolunteerId(volunteer.getId());
        }
        writeOff.setUsername(user.getUsername());
        writeOff.setMobile(user.getMobile());
        writeOff.setName(user.getName());
        writeOff.setUserRole(baseMapper.getUserRoleDescription(userId));
        LocalDate today = LocalDate.now();
        writeOff.setApplyDate(today);
        writeOff.setCancelApply(false);
        Dict distanceFromDisabledDuration = dictService.lambdaQuery().eq(Dict::getCode, "DISTANCE_FROM_REMOVE_DURATION").eq(Dict::getStatus, 1).one();
        int duration = distanceFromDisabledDuration == null ? 30 : Integer.parseInt(distanceFromDisabledDuration.getValue());
        writeOff.setPlanOrRealAccountLinkInfoRemoveDate(today.plusDays(duration));
        super.saveOrUpdate(writeOff);
        userService.forceOfflineUserByUserId(userId);
        sendWriteOffRemindSms(duration, user.getMobile());
    }

    private void sendWriteOffRemindSms(int duration, String mobile) {
        if (StringUtil.isEmpty(mobile)) {
            log.info("未获取到需要注销提醒的手机号");
            return;
        }
        MsgTmp tmp = tmpService.lambdaQuery().eq(MsgTmp::getCode, "user_write_off_remind").one();
        if (StringUtil.isEmpty(tmp.getTmp())) {
            log.info("未配置短信模板");
            return;
        }
        // 封装短信内容
        String msgContent = String.format(tmp.getTmp(), duration, duration);
        // 构建短信实体
        MsgSend msg = buildMsgEntity(tmp, mobile, msgContent);
        // 保存短信记录
        msgSendService.save(msg);
        // 发送短信
        msgSendService.sendMessage(msg.getId());
    }

    private MsgSend buildMsgEntity(MsgTmp tmp, String mobiles, String msgContent) {
        // 构建短信实体
        MsgSend entity = new MsgSend();
        entity.setTitle(tmp.getTitle());
        entity.setSelfDefine(false);
        entity.setTmpId(tmp.getId());
        entity.setSendType(MsgCons.MSG_PUSH_TYPE_SMS);
        entity.setSelfDefineMsg(msgContent);
        entity.setMsgContent(msgContent);

        // 封装接收人信息
        List<MsgRecord> receivers = Arrays.stream(mobiles.split(","))
                .map(mobile -> {
                    MsgRecord record = new MsgRecord();
                    record.setReceiverMobile(mobile);
                    return record;
                })
                .collect(Collectors.toList());

        entity.setReceivers(receivers);
        return entity;
    }

    @Override
    public void writeOffCancel(Long id) {
        Long userId;
        if (id != null) {
            UserWriteOff writeOff = super.getById(id);
            userId = writeOff.getUserId();
        } else {
            userId = (Long)Oauth2Util.getUserId();
        }
        if (userId == null) {
            throw new BusinessException(ResultCode.FAIL, "用户id未知！");
        }
        writeOffCancelByUserId(userId);
    }

    private void writeOffCancelByUserId(Long userId) {
        User user = userService.lambdaQuery().eq(User::getId, userId).one();
        if (user == null) {
            throw new BusinessException(ResultCode.FAIL, "用户【" + userId + "】不存在！");
        }
        UserWriteOff writeOff = lambdaQuery().eq(UserWriteOff::getUserId, userId).one();
        if (writeOff == null) {
            throw new BusinessException(ResultCode.FAIL, "当前用户未申请注销，无需取消！");
        }
        if (writeOff.getRemoved()) {
            throw new BusinessException(ResultCode.FAIL, "当前用户已注销完成，无法取消注销！");
        }
        if (!LocalDate.now().isBefore(writeOff.getPlanOrRealAccountLinkInfoRemoveDate())) {
            throw new BusinessException(ResultCode.FAIL, "当前用户已到达销户时间，无法取消注销！");
        }
        writeOff.setCancelApply(true);
        super.updateById(writeOff);
        try{
            userService.lambdaUpdate().eq(User::getId, userId).set(User::getStatus, true).update();
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "用户取消注销失败！");
        }
    }

    @Override
    public void writeOffCancelByMobile(String mobile) {
        log.info("取消注销用户手机号：" + mobile);
        User user = userService.lambdaQuery().eq(User::getMobile, mobile).one();
        if (user == null) {
            throw new BusinessException(ResultCode.FAIL, "用户不存在！");
        }
        writeOffCancelByUserId(user.getId());
    }

    @Override
    public void finalWriteOffDeal(Long id) {
        UserWriteOff writeOff = super.getById(id);
        writeOffFinal(Collections.singletonList(writeOff));
    }

    @Override
    public void writeOffFinal(List<UserWriteOff> writeOffList) {
        List<Long> needRemoveUser = new ArrayList<>();
        List<Long> needWriteOffVolunteer = new ArrayList<>();
        writeOffList.forEach(it -> {
            Long userId = it.getUserId();
            if (userId == null) {
                it.setRemoveFailReason("用户id未知");
                return;
            }
            User user = userService.getById(userId);
            if (user == null) {
                throw new BusinessException(ResultCode.FAIL, "用户【" + userId + "】不存在！");
            }
            ZyzVolunteer volunteer = volunteerService.getByPhone(user.getMobile());
            if (volunteer != null) {
                Long volunteerId = volunteer.getId();
                List<ZyzTeam> teams = getUserManageTeams(volunteerId);
                if (teams != null && !teams.isEmpty()) {
                    it.setRemoveFailReason("当前用户存在管理团队，无法注销，请先取消管理团队");
                    return;
                }
                needWriteOffVolunteer.add(volunteerId);
            }
            needRemoveUser.add(userId);
            it.setRemoved(true);
            it.setRemoveFailReason(null);
        });
        super.updateBatchById(writeOffList);
        if (!needRemoveUser.isEmpty()) {
            userService.userWriteOffRemove(needRemoveUser);
        }
        if (!needWriteOffVolunteer.isEmpty()) {
            volunteerService.lambdaUpdate()
                    .set(ZyzVolunteer::getWriteOff, true)
                    .in(ZyzVolunteer::getId, needWriteOffVolunteer)
                    .update();
        }
    }
}
