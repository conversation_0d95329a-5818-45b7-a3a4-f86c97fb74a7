package com.fykj.scaffold.security.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手机端注册成为志愿者dto
 */
@Data
public class VolunteerMobileRegisterDto {
    /**
     * 志愿者姓名
     */
    @ApiModelProperty(value = "志愿者姓名")
    private String name;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 证件类型（身份证还是护照等等)
     */
    @ApiModelProperty(value = "证件类型（身份证还是护照等等)")
    private Integer certificateType;

    /**
     * 身份证号
     */
    @TableField("certificate_id")
    @ApiModelProperty(value = "身份证号")
    private String certificateId;

    /**
     * 组织架构CODE（社区层级)
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织架构CODE（社区层级)")
    private String orgCode;

    /**
     * 监护人身份证
     */
    @ApiModelProperty(value = "监护人身份证")
    private String guardianIdCard;

    /**
     * 国家
     */
    @TableField("country")
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 城市
     */
    @TableField("city")
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 地址
     */
    @TableField("address")
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 性别
     */
    @TableField("sex")
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 名族
     */
    @TableField("nation")
    @ApiModelProperty(value = "名族")
    private String nation;

    /**
     * 学校
     */
    @TableField("school")
    @ApiModelProperty(value = "学校")
    private String school;

    /**
     * 教育程度
     */
    @TableField("education")
    @ApiModelProperty(value = "教育程度")
    private String education;

    /**
     * 行业类别
     */
    @TableField("industry_cate")
    @ApiModelProperty(value = "行业类别")
    private String industryCate;

    /**
     * 专业技能
     */
    @TableField("skill")
    @ApiModelProperty(value = "专业技能")
    private String skill;

    /**
     * 兴趣爱好
     */
    @TableField("hobby")
    @ApiModelProperty(value = "兴趣爱好")
    private String hobby;


    /**
     * 政治面貌
     */
    @TableField("political_scape")
    @ApiModelProperty(value = "政治面貌")
    private String politicalScape;

}
