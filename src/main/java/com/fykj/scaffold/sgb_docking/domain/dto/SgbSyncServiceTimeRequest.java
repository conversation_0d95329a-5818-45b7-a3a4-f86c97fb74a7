package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;

/**
 * 苏州社工部服务时长上传同步请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbSyncServiceTimeRequest {

    /**
     * 身份证编号
     */
    private String idCode;

    /**
     * 第三方系统服务时长唯一标识键
     */
    private String uniCode;

    /**
     * 活动招募编号
     */
    private String actId;

    /**
     * 服务时长记录开始时间 - 格式'HH:mm'，actId关联的活动登记中的recordWay=2时必填
     */
    private String startTime;

    /**
     * 服务时长记录结束时间 - 格式'HH:mm'，actId关联的活动登记中的recordWay=2时必填
     */
    private String endTime;

    /**
     * 服务时长 - 单位：时，精度：0.5的正整数的倍数，与startTime、endTime二选一
     */
    private Double serverTime;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncServiceTimeInfo infos = new SyncServiceTimeInfo();
        infos.setInfos(Collections.singletonList(this));
        return JSON.toJSONString(infos);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncServiceTimeInfo {
        private java.util.List<SgbSyncServiceTimeRequest> infos;
    }
}
