package com.fykj.scaffold.mall.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.PointContributePersonDto;
import com.fykj.scaffold.mall.domain.entity.ZyzPointRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointRecordParams;
import com.fykj.scaffold.mall.service.IZyzPointContributeRecordService;
import com.fykj.scaffold.mall.service.IZyzPointRecordService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/mini/point/record")
@Api(tags = "微信小程序兑换信息接口")
public class MinPointRecordController {

    @Autowired
    private IZyzPointRecordService pointRecordService;

    @Autowired
    private IZyzPointContributeRecordService pointContributeRecordService;

    @ApiOperation("积分流水分页查询")
    @PostMapping("/pageForApi")
    public JsonResult<IPage<ZyzPointRecord>> pageForApi(@RequestBody ZyzPointRecordParams params) {
        return new JsonResult<>(pointRecordService.pageForApi(params));
    }

    @ApiOperation("积分捐赠")
    @GetMapping("/savePointContribute")
    public Result savePointContribute(BigDecimal contributePoint, String orgCode, String orgName) {
        if (contributePoint.compareTo(BigDecimal.ZERO) < 1) {
            throw new BusinessException(ResultCode.FAIL, "积分捐赠失败,捐赠积分不能小于等于0");
        }
        if (!pointContributeRecordService.savePointContribute(contributePoint, orgCode, orgName)) {
            throw new BusinessException(ResultCode.FAIL, "当前网络拥挤，积分捐赠失败请稍后再试");
        }
        return new Result();
    }

    @ApiOperation("积分捐赠名单分页查询")
    @PostMapping("/pageForPointContributePerson")
    public JsonResult<IPage<PointContributePersonDto>> pageForPointContributePerson(@RequestBody BaseParams params) {
        return new JsonResult<>(pointContributeRecordService.pageForPointContributePerson(params));
    }

    @ApiOperation("获取捐赠相关累计")
    @PostMapping("/getContributeSum")
    public JsonResult<List<Object>> getContributeSum() {
        return new JsonResult<>(pointContributeRecordService.getContributeSum());
    }
}
