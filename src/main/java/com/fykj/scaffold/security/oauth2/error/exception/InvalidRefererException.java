package com.fykj.scaffold.security.oauth2.error.exception;

import org.springframework.security.access.AccessDeniedException;

/**
 * 自定义无效的referer异常
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
public class InvalidRefererException extends AccessDeniedException {

    public InvalidRefererException(String msg) {
        super(msg);
    }

    public InvalidRefererException(String msg, Throwable t) {
        super(msg, t);
    }

}
