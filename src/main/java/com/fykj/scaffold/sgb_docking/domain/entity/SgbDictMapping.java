package com.fykj.scaffold.sgb_docking.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 系统字典值与三方字典值映射
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("系统字典值与三方字典值映射")
@TableName("sgb_dict_mapping")
public class SgbDictMapping extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 本系统字典类型
     */
    @TableField("sys_type")
    @ApiModelProperty("本系统字典类型")
    @DictTrans(transTo = "sysTypeText")
    private String sysType;

    /**
     * 本系统字典类型名称
     */
    @TableField(exist = false)
    @ApiModelProperty("本系统字典类型名称")
    private String sysTypeText;

    /**
     * 本系统字典值
     */
    @TableField("sys_value")
    @ApiModelProperty("本系统字典值")
    @DictTrans(transTo = "sysText")
    private String sysValue;

    /**
     * 本系统字典名称
     */
    @TableField(exist = false)
    @ApiModelProperty("本系统字典值")
    private String sysText;

    /**
     * 第三方字典类型
     */
    @TableField("third_type")
    @ApiModelProperty("第三方字典类型")
    private String thirdType;

    /**
     * 第三方字典值
     */
    @TableField("third_value")
    @ApiModelProperty("第三方字典值")
    private String thirdValue;
} 