package com.fykj.scaffold.race.api;

import com.fykj.scaffold.race.domain.dto.WinPhotoDto;
import com.fykj.scaffold.race.domain.entity.Activity;
import com.fykj.scaffold.race.service.IRaceActivityService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 摄影大赛-活动表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@RestController
@RequestMapping("/api/race/activity")
public class ApiActivityController {
@Autowired
private IRaceActivityService activityService;



    @ApiOperation(value = "获取启用活动--网页端")
    @GetMapping(value = "/getActivityByEnable")
    public JsonResult<Activity> getActivityByEnable() {
        Activity res = activityService.getActivityByEnable();
        return new JsonResult<>(res);
    }


    @ApiOperation(value = "获取活动获奖作品--网页端")
    @GetMapping(value = "/getActivityWinPhoto")
    public JsonResult<List<WinPhotoDto>> getActivityWinPhoto(@RequestParam Long activityId) {
        List<WinPhotoDto> res = activityService.getActivityWinPhoto(activityId);
        return new JsonResult<>(res);
    }

    @ApiOperation(value = "获取可展示活动列表--网页端")
    @GetMapping(value = "/getActivityByShow")
    public JsonResult<List<Activity>> getActivityByShow() {
        List<Activity> res = activityService.getActivityByShow();
        return new JsonResult<>(res);
    }
}
