package com.fykj.scaffold.message.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.param.MsgSendParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;
import result.Result;

/**
 * 消息发送服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface IMsgSendService extends IBaseService<MsgSend> {

    /**
     * 消息发送
     * @param sendId
     * @return
     */
    Boolean sendMessage(Long sendId);

    /**
     * 消息发送分页查询
     * @return
     */
    IPage<MsgSend> pageForMsgSend(MsgSendParams params);

    /**
     * 导入接收者
     * @param excel
     * @param siteUse
     * @param sendId
     * @return
     */
    Result excelImport(MultipartFile excel, Boolean siteUse, Long sendId);

    /**
     * 接收者维护页提交
     * @param filePath
     * @param sendId
     */
    void receiverMaintainSubmit(String filePath, Long sendId);
}
