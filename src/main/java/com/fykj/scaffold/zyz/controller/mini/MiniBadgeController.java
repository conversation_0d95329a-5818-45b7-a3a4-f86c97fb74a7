package com.fykj.scaffold.zyz.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTradeListing;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTransaction;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeInstanceParams;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeTradeListingParams;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeTransactionParams;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceDetailVO;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceVO;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeTradeHallVO;
import com.fykj.scaffold.zyz.service.IZyzBadgeInstanceService;
import com.fykj.scaffold.zyz.service.IZyzBadgeService;
import com.fykj.scaffold.zyz.service.IZyzBadgeTradeListingService;
import com.fykj.scaffold.zyz.service.IZyzBadgeTransactionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * 用户端-勋章-接口控制器
 *
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/admin/mini/badge")
@Api(tags = "用户端-勋章")
@Slf4j
public class MiniBadgeController {

    @Autowired
    private IZyzBadgeInstanceService badgeInstanceService;
    
    @Autowired
    private IZyzBadgeService badgeService;
    
    @Autowired
    private IZyzBadgeTransactionService badgeTransactionService;
    
    @Autowired
    private IZyzBadgeTradeListingService badgeTradeListingService;

    @GetMapping("/myBadges")
    @ApiOperation("查询我的勋章")
    public JsonResult<List<ZyzBadgeInstanceVO>> getMyBadges() {
        List<ZyzBadgeInstanceVO> result = badgeInstanceService.listMyBadges();
        return new JsonResult<>(result);
    }

    @PostMapping("/pageMyBadges")
    @ApiOperation("分页查询我的勋章")
    public JsonResult<IPage<ZyzBadgeInstanceVO>> pageMyBadges(
            @RequestBody ZyzBadgeInstanceParams params) {
        // 未兑换给系统的
        params.setRecycle(false);
        IPage<ZyzBadgeInstanceVO> result = badgeInstanceService.pageMyBadges(params);
        return new JsonResult<>(result);
    }
    
    @ApiOperation("我的勋章交易记录")
    @PostMapping("/myTransactionRecords")
    public JsonResult<IPage<ZyzBadgeTransaction>> getMyTransactionRecords(
            @RequestBody ZyzBadgeTransactionParams params) {
        params.setRelatedMe(true);
        // 查询交易记录
        IPage<ZyzBadgeTransaction> result = badgeTransactionService.queryPage(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("获取我的勋章详情(返回了是否已上架")
    @GetMapping("/myBadgeDetail")
    public JsonResult<ZyzBadgeInstanceDetailVO> getMyBadgeInstanceDetail(
            @ApiParam(value = "勋章ID", required = true) 
            @RequestParam @JsonSerialize(using = ToStringSerializer.class) Long badgeId) {
        ZyzBadgeInstanceDetailVO detail = badgeInstanceService.getMyBadgeInstanceDetail(badgeId);
        return new JsonResult<>(detail);
    }

    @ApiOperation("获取勋章交易发布信息(发布交易页的信息获取")
    @GetMapping("/tradePublishInfo")
    public JsonResult<ZyzBadgeTradePublishInfoDto> getTradePublishInfo(
            @ApiParam(value = "勋章ID", required = true)
            @RequestParam @JsonSerialize    (using = ToStringSerializer.class) Long badgeId,
            @ApiParam(value = "费率前积分，可选，如不传则使用勋章默认所需积分")
            @RequestParam(required = false) Integer pointsBeforeFee) {
        ZyzBadgeTradePublishInfoDto dto = badgeInstanceService.getTradePublishInfo(badgeId, pointsBeforeFee);
        return new JsonResult<>(dto);
    }

    @ApiOperation("发布勋章交易")
    @PostMapping("/publishTrade")
    public Result publishBadgeTrade(@RequestBody @Validated ZyzBadgeTradePublishDto dto) {
        ZyzBadgeTradeListing result = badgeInstanceService.publishBadgeTrade(dto);
        return new Result();
    }

    @ApiOperation("编辑勋章交易")
    @PostMapping("/editTrade")
    public Result editBadgeTrade(@RequestBody @Validated ZyzBadgeTradePublishDto dto) {
        ZyzBadgeTradeListing result = badgeInstanceService.editBadgeTrade(dto);
        return new Result();
    }
    
    @ApiOperation("取消勋章交易")
    @GetMapping("/cancelTrade")
    public Result cancelBadgeTrade(
            @ApiParam(value = "交易上架ID", required = true)
            @RequestParam @JsonSerialize(using = ToStringSerializer.class) Long listingId) {
        badgeInstanceService.cancelBadgeTrade(listingId);
        return OK;
    }
    
    @ApiOperation("兑换勋章获取积分")
    @GetMapping("/exchangeForPoints")
    public Result exchangeForPoints(
            @ApiParam(value = "勋章ID", required = true)
            @RequestParam @JsonSerialize(using = ToStringSerializer.class) Long badgeId) {
        // 调用服务层方法处理兑换勋章获取积分的业务逻辑
        badgeInstanceService.exchangeForPoints(badgeId);
        return OK;
    }

    @ApiOperation("赠送勋章给其他志愿者")
    @PostMapping("/giftBadge")
    public Result giftBadge(@RequestBody @Validated ZyzBadgeGiftDto giftDto) {
        badgeInstanceService.giftBadge(giftDto);
        return OK;
    }
    
    @ApiOperation("购买勋章")
    @PostMapping("/purchaseBadge")
    public Result purchaseBadge(@RequestBody @Validated ZyzBadgePurchaseDto purchaseDto) {
        badgeInstanceService.purchaseBadge(purchaseDto);
        return OK;
    }
    
    @ApiOperation("勋章交易大厅分页列表")
    @PostMapping("/pageTrade")
    public JsonResult<IPage<ZyzBadgeTradeHallVO>> getTradeHallList(
            @RequestBody ZyzBadgeTradeListingParams params) {
        // 查询勋章交易大厅列表
        IPage<ZyzBadgeTradeHallVO> result = badgeTradeListingService.queryTradingListings(params);
        return new JsonResult<>(result);
    }
    
    @ApiOperation("勋章交易详情")
    @GetMapping("/tradeDetail")
    public JsonResult<ZyzBadgeTradeDetailDTO> getTradeDetail(
            @ApiParam(value = "交易上架ID", required = true)
            @RequestParam @JsonSerialize(using = ToStringSerializer.class) Long listingId) {
        ZyzBadgeTradeDetailDTO detail = badgeTradeListingService.getTradeDetail(listingId);
        return new JsonResult<>(detail);
    }
}