package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.ApplyAppraiseDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityAppraiseParams;
import com.fykj.scaffold.zyz.domain.params.ZyzApplyParams;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityAppraise;

import java.util.List;

/**
 * 活动-评价内容
 *
 * 服务类
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-07
 */
public interface IZyzActivityAppraiseService extends IBaseService<ZyzActivityAppraise> {


    /**
     * 报名评价（我的活动-报名评价）
     *
     * @param appraise
     */
    void applyAppraise(ApplyAppraiseDto appraise);


    /**
     * 修改前端是否展示
     *
     * @param idList
     * @param status
     */
    void appraiseStatus(List<Long> idList, Boolean status);


    /**
     * 分页查询活动评价
     *
     * @param params
     * @return
     */
    IPage<ZyzActivityAppraise> pageForAppraise(ZyzActivityAppraiseParams params);

    /**
     * 获取活动评价
     *
     * @param activityId
     * @return
     */
    List<ApplyAppraiseDto> getActivityEvaluate(long activityId);




    /**
     * 获取活动评价人次
     * @return
     */
    Integer getAppraiseNum(DataScreenParams params);
}

