package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.TeamExportDto;
import com.fykj.scaffold.zyz.domain.dto.TeamImportDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzTeamExportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.util.DownloadFileUtil;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.beans.vo.IdTextVo;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 志愿者团队表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-30
 */
@Slf4j
@RestController
@RequestMapping({"/admin/zyz/team", "/api/admin/zyz/team"})
@Api(tags = "志愿者团队接口")
public class ZyzTeamController extends BaseController<IZyzTeamService, ZyzTeam, ZyzTeamParams> {

    @AuditLog("通过Excel批量导入团队信息")
    @ApiOperation("导入")
    @PostMapping(value = "/dataImport")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<TeamImportDto> failureList = baseService.dataImport(excel);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.writeExcel(failureList, "team_fill_template.xlsx", TeamImportDto.class);
        }
    }

    @AuditLog("导入团队管理员相关逻辑处理")
    @ApiOperation("导入团队管理员相关逻辑处理")
    @PostMapping(value = "/dealTeamAdminBatch")
    public Result dealTeamAdminBatch(@RequestBody List<Long> teamIds) {
        baseService.dealTeamAdminBatch(teamIds);
        return new JsonResult<>();
    }

    @ApiOperation(value = "获取团队列表")
    @GetMapping(value = "/teamIdTextList")
    public Result teamList() {
        return new JsonResult<>(baseService.lambdaQuery().eq(ZyzTeam::getTeamStatus, ZyzCons.TEAM_AUDIT_STATUS_PASS).orderByDesc(ZyzTeam::getFounded).list().stream().map(it -> new IdTextVo(it.getId(), it.getName())).collect(Collectors.toList()));
    }

    @ApiOperation("获取所在团队--分页查询")
    @PostMapping("/getMyPages")
    public JsonResult<IPage<ZyzTeam>> getMyPages(@RequestBody ZyzTeamParams params) {
        IPage<ZyzTeam> iPage = baseService.getMyPages(params);
        DictTransUtil.trans(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @AuditLog("导出报名人员明细")
    @PostMapping("/export")
    @ApiOperation("导出数据到Excel")
    public void export(@RequestBody ZyzTeamParams params) {
        List<TeamExportDto> res = baseService.getList(params);
        ExcelUtil.writeExcel(res, "报名人员明细", TeamExportDto.class);
    }


    @AuditLog("更改重点团队")
    @ApiOperation("更改重点团队")
    @GetMapping("/updateEmphasis")
    public Result updateEmphasis(Long teamId, String emphasisType) {
        if (!baseService.updateEmphasis(teamId, emphasisType)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("新增团队")
    @ApiOperation("新增团队")
    @PostMapping("/saveTeam")
    public Result saveTeam(@RequestBody ZyzTeam entity) {
        baseService.saveTeam(entity);
        return OK;
    }

    @AuditLog("修改团队")
    @ApiOperation("修改团队")
    @PostMapping("/updateTeam")
    public Result updateTeam(@RequestBody ZyzTeam entity) {
        baseService.updateTeam(entity);
        return OK;
    }

    @AuditLog("团队信息变更")
    @ApiOperation("团队信息变更")
    @PostMapping("/updateTeamNeedAudit")
    public Result updateTeamNeedAudit(@RequestBody ZyzTeam entity) {
        baseService.updateTeamNeedAudit(entity);
        return OK;
    }

    @AuditLog("团队信息变更撤回")
    @ApiOperation("团队信息变更撤回")
    @GetMapping("/callbackInfoChange")
    public Result callbackInfoChange(@RequestParam Long id) {
        baseService.callbackInfoChange(id);
        return OK;
    }

    @AuditLog("再次申请")
    @ApiOperation("再次申请")
    @GetMapping("/updateTeamStatus")
    public Result updateTeamStatus(Long teamId) {
        if (!baseService.updateTeamStatus(teamId)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }


    @ApiOperation("获取审核通过团队--分页查询")
    @PostMapping("/getPassTeamPages")
    public JsonResult<IPage<ZyzTeam>> getPassTeamPages(@RequestBody ZyzTeamParams params) {
        IPage<ZyzTeam> res = baseService.getPassTeamPages(params);
        DictTransUtil.trans(res.getRecords());
        DesensitiseUtil.desensitise(res.getRecords());
        return new JsonResult<>(res);
    }


    @ApiOperation("获取团队--分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzTeam>> getPages(@RequestBody ZyzTeamParams params) {
        IPage<ZyzTeam> iPage = baseService.getPages(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    @AuditLog("导出团队")
    @PostMapping("/exportTeam")
    @ApiOperation("导出数据到Excel")
    public void exportTeam(@RequestBody ZyzTeamParams params) {
        List<ZyzTeam> list = baseService.getTeamList(params);
        DictTransUtil.trans(list);
        List<ZyzTeamExportDto> dtoList = list.stream().map(it -> {
            ZyzTeamExportDto dto = new ZyzTeamExportDto();
            BeanUtil.copyProperties(it, dto);
            return dto;
        }).collect(Collectors.toList());
        ExcelUtil.fillExcel(dtoList, "team.xlsx", ZyzTeamExportDto.class);
    }

    @AuditLog("停用团队账号")
    @ApiOperation("停用团队账号")
    @GetMapping("/stopUse")
    public Result stopUse(Long id) {
        boolean result = this.baseService.stopUse(id);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @ApiOperation(value = "获取团队通过id")
    @GetMapping(value = "/getTeamById")
    public Result getTeamById(Long id, @RequestParam(required = false) Boolean infoChangeAudit) {
        ZyzTeam team = baseService.getTeamById(id, infoChangeAudit);
        DictTransUtil.trans(team);
        return new JsonResult<>(team);
    }

    @AuditLog("删除团队")
    @ApiOperation("停用团队账号")
    @GetMapping("/removeTeam")
    public Result removeTeam(String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        boolean result = this.baseService.removeTeam(idList);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @ApiOperation(value = "获取当前组织的团队列表")
    @GetMapping(value = "/getOrgTeamList")
    public Result getOrgTeamList() {
        List<ZyzTeam> res = baseService.getOrgTeamList();
        return new JsonResult<>(res);
    }

    /**
     * 团队信息登记表下载
     */
    @ApiOperation("团队信息登记表下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        DownloadFileUtil.downloadFile(response, "data/docxTemplate/志愿服务团队信息登记表.docx");
    }

    @ApiOperation("通过团队名称获取组织本级及下级团队列表")
    @GetMapping("/getTeamListByName")
    public JsonResult<List<ZyzTeam>> getTeamListByName(@RequestParam String name) {
        List<ZyzTeam> res = baseService.getTeamListByName(name);
        return new JsonResult<>(res);
    }

    @AuditLog("团队修改上级组织")
    @ApiOperation("团队修改上级组织")
    @GetMapping("/updateOrgCode")
    public Result updateOrgCode( Long id,  String orgCode) {
        this.baseService.updateOrgCode(id, orgCode);
        return OK;
    }

    @ApiOperation("获取团队列表")
    @GetMapping("/getTeamListByNameForBAGrant")
    public Result getTeamListByNameForBAGrant(@RequestParam(required = false) String teamName) {
        return new JsonResult<>(baseService.getTeamListByNameForBAGrant(teamName));
    }
}
