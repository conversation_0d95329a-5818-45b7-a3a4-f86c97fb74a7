package com.fykj.scaffold.sgb_docking.task;

import com.fykj.scaffold.sgb_docking.service.ISgbDeptService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 苏州志愿者平台部门单位同步任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbDeptSyncTask {

    @Autowired
    private ISgbDeptService sgbDeptService;

    /**
     * 部门单位同步任务
     */
    @XxlJob("SGB_Dept_Sync_Handler")
    public void deptSyncJobHandler() {
        XxlJobHelper.log("SGB_Dept_Sync_Handler-Job, START!");
        try {
            sgbDeptService.syncDept();
        } catch (Exception e) {
            log.error("苏州志愿者平台部门单位同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("SGB_Dept_Sync_Handler-Job, END!");
    }
}