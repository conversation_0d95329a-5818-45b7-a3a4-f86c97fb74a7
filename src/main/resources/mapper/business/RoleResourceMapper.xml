<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.security.business.mapper.RoleResourceMapper">

    <select id="findResourceIdList" resultType="java.lang.String">
        select resource_id
        from sys_role_resource
        where role_id = #{roleId}
	      and is_deleted = false
        order by create_date asc
    </select>

    <delete id="deleteRoleResourceByRoleId" parameterType="java.lang.Long">
        delete
        from sys_role_resource
        where role_id = #{roleId}
    </delete>
</mapper>
