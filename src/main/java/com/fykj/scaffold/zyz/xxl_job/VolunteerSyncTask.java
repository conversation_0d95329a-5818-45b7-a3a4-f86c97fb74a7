package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 志愿者同步补偿
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VolunteerSyncTask {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @XxlJob("Volunteer_Sync_Handler")
    public void volunteerSyncJobHandler() {
        XxlJobHelper.log("Volunteer_Sync_Handler-Job, START!");

        // 原有同步逻辑
        List<Long> volunteerIds = volunteerService.getNeedSync();
        if (CollectionUtil.isNotEmpty(volunteerIds)) {
            try {
                volunteerService.syncBatch(volunteerIds);
            } catch (Exception e) {
                log.error("志愿者批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Volunteer_Sync_Handler-Job, END!");
    }
}
