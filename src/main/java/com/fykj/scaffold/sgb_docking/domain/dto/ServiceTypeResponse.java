package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 服务领域查询响应实体
 */
@Data
public class ServiceTypeResponse {
    
    /**
     * 响应信息列表
     */
    private List<ServiceTypeInfo> info;
    
    /**
     * 服务领域信息实体
     */
    @Data
    public static class ServiceTypeInfo {
        /**
         * 响应状态码
         * SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 类型编号
         */
        private String typeId;
        
        /**
         * 类型名称
         */
        private String typeName;
        
        /**
         * 父级类型编号
         */
        private String fatherId;
        
        /**
         * 层级
         */
        private String nodePath;
    }
} 