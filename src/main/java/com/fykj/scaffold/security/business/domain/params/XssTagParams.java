package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * XSS标签
 *
 * <AUTHOR>
 * @date 2019/10/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("XSS标签查询参数")
public class XssTagParams extends BaseParams {
    private static final long serialVersionUID = 5051351643065736048L;

    @ApiModelProperty("标签，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "tag")
    private String tag;

    @ApiModelProperty("属性，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "attribute")
    private String attribute;

    @ApiModelProperty("属性值，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "enforced_value")
    private String enforcedValue;

    @ApiModelProperty("协议，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "protocol")
    private String protocol;
}
