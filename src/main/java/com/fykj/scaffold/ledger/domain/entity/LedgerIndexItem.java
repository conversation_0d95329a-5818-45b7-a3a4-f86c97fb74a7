package com.fykj.scaffold.ledger.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 台账项-实体类
 *
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("ledger_index_item")
public class LedgerIndexItem extends BaseEntity  {

	private static final long serialVersionUID = 7221600703698322128L;

	/**
	 * 台账项名称
	 */
	@TableField("name")
    @ApiModelProperty(value = "台账项名称")
	private String name;


	/**
	 * 指标id
	 */
	@TableField("index_id")
	@ApiModelProperty(value = "指标id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long indexId;

	@TableField(exist = false)
	@ApiModelProperty(value = "指标项名称")
	private String indexName;


	/**
	 * 局办id
	 */
	@TableField("department_id")
	@ApiModelProperty(value = "局办id")
	private String departmentId;

	/**
	 * 局办idList
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "局办idList")
	private List<String> departmentIdList;


	@TableField(exist = false)
	@ApiModelProperty(value = "局办名称")
	private String departmentName;

	/**
	 * 相关要求
	 */
	@TableField("related_requirements")
    @ApiModelProperty(value = "相关要求")
	private String relatedRequirements;

}
