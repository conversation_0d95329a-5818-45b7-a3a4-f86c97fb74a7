package com.fykj.scaffold.race.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.fykj.scaffold.race.domain.dto.PhotoImportDto;
import com.fykj.scaffold.race.domain.dto.PictureDto;
import com.fykj.scaffold.race.domain.dto.WinPhotoExcelDto;
import com.fykj.scaffold.race.domain.entity.ActivityPrize;
import com.fykj.scaffold.race.domain.entity.ActivityPrizeGrade;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.mapper.ActivityPrizeMapper;
import com.fykj.scaffold.race.service.IActivityPrizeService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 摄影大赛-活动奖项作品关联表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ActivityPrizeServiceImpl extends BaseServiceImpl<ActivityPrizeMapper, ActivityPrize> implements IActivityPrizeService {
    @Autowired
    private PhotoServiceImpl photoService;

    @Autowired
    private ActivityPrizeGradeServiceImpl activityPrizeGradeService;

    @Override
    public List<Photo> getPhotosByPrize(Long gradeId) {
        List<Photo> res = new ArrayList<>();
        List<ActivityPrize> list = lambdaQuery().eq(ActivityPrize::getGradeId, gradeId).list();
//        Map<Long, String> userNameMap = userService.list().stream().collect(Collectors.toMap(BaseEntity::getId, User::getName));
        list.forEach(item -> {
            Photo photo = photoService.getById(item.getPhotoId());
            if(StringUtil.isNotEmpty(item.getPhotoName())){
                photo.setName(item.getPhotoName());
            }
            List<PictureDto> filterList = new ArrayList<>();
            //导入的时候设置了某些照片才获奖，所以这边再遍历过滤一下
            if (StringUtil.isNotEmpty(item.getPicNums())) {
                List<String> numList = Arrays.stream(item.getPicNums().split(Mark.COMMA)).sorted().collect(Collectors.toList());
                List<PictureDto> picList = photo.getPictureList();
                if (CollUtil.isNotEmpty(picList)) {
                    for (int i = 0; i < picList.size(); i++) {
                        if (numList.contains(String.valueOf(i + 1))) {
                            filterList.add(picList.get(i));
                        }
                    }
                }
                if (CollUtil.isEmpty(filterList)) {
                    // 稍微容一下错，万一真的找不到图片，就显示所有的
                    photo.setPictureList(picList);
                } else {
                    photo.setPictureList(filterList);
                }
            }
            //   photo.setAuthor(userNameMap.get(photo.getUserId()));
            res.add(photo);


        });
        return res;
    }

    @Override
    public List<PhotoImportDto> uploadExcel(MultipartFile excel, Long activityId) {
        List<PhotoImportDto> failureList = new ArrayList<>();
        List<ActivityPrize> res = new ArrayList<>();
        List<WinPhotoExcelDto> excelList;
        try {
            excelList = EasyExcel.read(new BufferedInputStream(excel.getInputStream())).head(WinPhotoExcelDto.class).headRowNumber(2).sheet().doReadSync();
        } catch (IOException e) {
            throw new BusinessException(ResultCode.ERROR, e.getMessage());
        }
        if (CollUtil.isEmpty(excelList)) {
            throw new BusinessException(ResultCode.FAIL, "excel数据为空");
        }
        excelList.forEach(item -> {
            ActivityPrize activityPrize = new ActivityPrize();
            Photo photo = photoService.lambdaQuery().eq(Photo::getNumber, item.getNumber()).eq(Photo::getActivityId, activityId).one();
            if (photo == null) {
                failureList.add(checkData(item, "作品不存在,请检查作品编号是否一致"));
                return;
            }
            if (StringUtil.isNotEmpty(item.getPicNums())) {
                //稍微验一下照片序号有没有越界
                List<String> numList = Arrays.stream(item.getPicNums().split(Mark.COMMA)).sorted().collect(Collectors.toList());
                if (CollUtil.isNotEmpty(numList)) {
                    if (Integer.parseInt(numList.get(0)) < 1) {
                        failureList.add(checkData(item, "照片编号不能小于1"));
                        return;
                    }
                    if (Integer.parseInt(numList.get(numList.size()-1)) > photo.getPictureList().size()) {
                        failureList.add(checkData(item, "照片编号不能大于上传照片数"));
                        return;
                    }
                }
            }

            activityPrize.setPhotoId(photo.getId());
            activityPrize.setPicNums(item.getPicNums());
            activityPrize.setPhotoName(item.getPhotoName());
            ActivityPrizeGrade prizeGrade = activityPrizeGradeService.lambdaQuery().eq(ActivityPrizeGrade::getActivityId, photo.getActivityId()).eq(ActivityPrizeGrade::getName, item.getPrizeName()).one();
            if (ObjectUtils.isEmpty(prizeGrade)) {
                failureList.add(checkData(item, "奖项不存在,请检查奖项名称是否一致"));
            } else {
                activityPrize.setGradeId(prizeGrade.getId());
                res.add(activityPrize);

            }
        });
        saveBatch(res);
        return failureList;
    }

    //校验数据
    private PhotoImportDto checkData(WinPhotoExcelDto dto, String failReason) {
        PhotoImportDto photoImportDto = new PhotoImportDto();
        photoImportDto.setNumber(dto.getNumber());
        photoImportDto.setPrizeName(dto.getPrizeName());
        photoImportDto.setPhotoName(dto.getPhotoName());
        photoImportDto.setAuthor(dto.getAuthor());
        photoImportDto.setAuthorPhone(dto.getAuthorPhone());
        photoImportDto.setPicNums(dto.getPicNums());
        photoImportDto.setFailReason(failReason);
        return photoImportDto;

    }
}
