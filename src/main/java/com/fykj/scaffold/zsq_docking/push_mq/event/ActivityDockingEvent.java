package com.fykj.scaffold.zsq_docking.push_mq.event;


import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDockingEvent  extends BaseDockingMqEvent {

    /**
     * 对接记录 id
     */
    private Long dockingRecordId;
    /**
     * 活动 id
     */
    private Long actId;

    /**
     * 消息 id
     */
    private String operation;

    /**
     * 消息 id
     */
    private String zwtUserId;
    /**
     * 上/下架
     */
    private Boolean onShelf;

    /**
     * 志愿者平台用户 id
     */
    private Serializable zyzUserId;
}
