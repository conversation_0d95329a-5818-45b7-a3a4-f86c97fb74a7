package com.fykj.scaffold.sgb_docking.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fykj.scaffold.sgb_docking.domain.dto.DictResponse;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbDict;
import com.fykj.scaffold.sgb_docking.domain.enums.SgbDictTypeEnum;
import com.fykj.scaffold.sgb_docking.mapper.SgbDictMapper;
import com.fykj.scaffold.sgb_docking.service.ISgbDictService;
import com.fykj.scaffold.sgb_docking.util.SgbApiUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.beans.vo.IdTextVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 苏州志愿者平台字典数据服务实现类
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SgbDictServiceImpl extends BaseServiceImpl<SgbDictMapper, SgbDict> implements ISgbDictService {

    /**
     * 同步所有字典数据
     */
    @Override
    public void syncDict() {
        try {
            log.info("开始同步所有字典数据");
            // 清空所有字典数据
            baseMapper.clearAll();
            
            // 统计同步成功的字典类型数量
            int successCount = 0;
            
            // 遍历字典类型枚举，逐个同步
            for (SgbDictTypeEnum dictType : SgbDictTypeEnum.values()) {
                try {
                    syncDictByType(dictType.getCode());
                    successCount++;
                } catch (Exception e) {
                    log.error("同步字典类型[{}]失败: {}", dictType.getCode(), e.getMessage(), e);
                }
            }
            
            log.info("字典数据同步完成，成功同步{}个字典类型", successCount);
        } catch (Exception e) {
            log.error("同步字典数据失败", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "同步字典数据失败");
        }
    }

    /**
     * 同步指定类型的字典数据
     * @param dictType 字典类型
     */
    @Override
    public void syncDict(String dictType) {
        try {
            // 清空指定类型的字典数据
            baseMapper.clearByType(dictType);
            
            // 调用同步方法
            syncDictByType(dictType);
        } catch (Exception e) {
            log.error("同步字典数据[{}]失败", dictType, e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "同步字典数据失败");
        }
    }
    
    /**
     * 同步指定类型的字典数据（内部方法）
     * @param dictType 字典类型
     */
    private void syncDictByType(String dictType) {
        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("type", dictType);
        String jsonParams = JSON.toJSONString(params);
        
        // 调用接口获取字典数据
        String response = SgbApiUtil.sendRequest("getSgbDictList", jsonParams);
        log.info("获取字典数据[{}]响应: {}", dictType, response);
        
        // 解析响应结果
        DictResponse dictResponse = JSON.parseObject(response, DictResponse.class);
        if (dictResponse == null || dictResponse.getInfo() == null || dictResponse.getInfo().isEmpty()) {
            log.warn("没有获取到字典类型[{}]的数据", dictType);
            return;
        }
        
        // 转换为实体并保存
        List<SgbDict> dictList = dictResponse.getInfo().stream()
            .filter(item -> "SUCCESS".equals(item.getCode()))
            .map(item -> {
                SgbDict dict = new SgbDict();
                dict.setValue(item.getValue());
                dict.setLabel(item.getLabel());
                dict.setType(dictType);
                dict.setDescribe(Optional.ofNullable(item.getDescribe()).orElse(""));
                return dict;
            }).collect(Collectors.toList());
        
        if (!dictList.isEmpty()) {
            log.info("同步字典类型[{}]数据，共{}条", dictType, dictList.size());
            saveBatch(dictList);
        }
    }
    
    /**
     * 根据字典编号获取字典数据
     * @param dictCode 字典编号
     * @return 字典数据
     */
    @Override
    public SgbDict getByDictCode(String dictCode) {
        return lambdaQuery().eq(SgbDict::getValue, dictCode).one();
    }
    
    /**
     * 根据字典类型获取字典数据列表
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    @Override
    public List<SgbDict> listByDictType(String dictType) {
        LambdaQueryWrapper<SgbDict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SgbDict::getType, dictType);
        return this.list(queryWrapper);
    }
    
    /**
     * 获取所有字典类型
     *
     * @return 字典类型列表，使用IdTextVo格式（id=type, text=type）
     */
    @Override
    public List<IdTextVo> getAllDictTypes() {
        // 使用distinct查询获取所有不重复的字典类型
        LambdaQueryWrapper<SgbDict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SgbDict::getType, SgbDict::getDescribe)
                .groupBy(Arrays.asList(SgbDict::getType, SgbDict::getDescribe));
        // 将结果转换为IdTextVo列表
        return this.list(queryWrapper).stream()
                .map(dict -> new IdTextVo(dict.getType(), dict.getDescribe()))
                .collect(Collectors.toList());
    }

    /**
     * 根据字典类型编码获取字典列表
     *
     * @param type 字典类型编码
     * @return 字典列表，使用IdTextVo格式（id=value, text=label）
     */
    @Override
    public List<IdTextVo> getIdTextByType(String type) {
        return lambdaQuery().eq(SgbDict::getType, type)
                .orderByAsc(SgbDict::getValue)
                .list().stream()
                .map(x -> new IdTextVo(x.getValue(), x.getLabel()))
                .collect(Collectors.toList());
    }

} 