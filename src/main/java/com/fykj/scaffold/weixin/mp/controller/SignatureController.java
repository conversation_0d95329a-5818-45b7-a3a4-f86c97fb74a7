package com.fykj.scaffold.weixin.mp.controller;

import exception.BusinessException;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 */
@Slf4j
@ApiIgnore
@RestController
@RequestMapping("/api/wechat/signature")
public class SignatureController {

    @Autowired
    private WxMpService wxService;

    @ApiOperation(value = "创建调用jsapi时所需要的签名")
    @PostMapping("/createJsapi")
    public Result createJsapiSignature(String appId, String url) {
        try {
            WxJsapiSignature jsapiSignature = wxService.switchoverTo(appId).createJsapiSignature(url);
            return new JsonResult<>(jsapiSignature);
        } catch (WxErrorException e) {
            log.error("创建微信签名失败", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "创建微信签名失败，请联系管理员");
        }
    }
}
