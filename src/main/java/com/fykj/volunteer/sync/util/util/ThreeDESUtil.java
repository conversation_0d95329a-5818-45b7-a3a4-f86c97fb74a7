package com.fykj.volunteer.sync.util.util;


import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;

/**
 * 
 * <AUTHOR>
 *
 */
public class ThreeDESUtil {
    // 算法名称 
    public static final String KEY_ALGORITHM = "desede";
    // 算法名称/加密模式/填充方式 
    public static final String CIPHER_ALGORITHM = "desede/CBC/PKCS5Padding";
    //KEYIV
    public static final byte[] KEYIV = { 1, 2, 3, 4, 5, 6, 7, 8 };
    
    public static final String CODE = "UTF-8";
    /** 
     * CBC加密 
     * @param key 密钥 
     * @param keyiv IV 
     * @param data 明文 
     * @return Base64编码的密文 
     * @throws Exception 
     */
    public static byte[] des3EncodeCBC(byte[] key, byte[] keyiv, byte[] data) throws Exception {
        Key deskey = keyGenerator(new String(key));
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        IvParameterSpec ips = new IvParameterSpec(keyiv);
        cipher.init(Cipher.ENCRYPT_MODE, deskey, ips);
        return cipher.doFinal(data);
    }

    /**
     * CBC加密 
     * @param key 接口账号+秘钥
     * @param data 明文
     * @param code 明文编码
     * @return 加密密文
     */
    public static String des3EncodeCBC(String key, String data, String... code) {
		if(key!=null && data != null && !"".equals(data)){
			if(key.length()>48){
				key = key.substring(0, 48).toUpperCase();
			}else{
				key = String.format("%-48s", key).replace(" ", "a").toUpperCase();
			}
			try {
				byte[] d =null;
				if(code.length>0){
						d = data.getBytes(code[0]);
				}else{
					d = data.getBytes(CODE);
				}
				return (Base64.getEncoder().encodeToString(des3EncodeCBC(key.getBytes(), KEYIV, d)).replaceAll("\r\n", ""));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return "";
	}
    
    
    /** 
     *   
     * 生成密钥key对象 
     * @param keyStr 密钥字符串
     * @return 密钥对象 
     * @throws InvalidKeyException   
     * @throws NoSuchAlgorithmException   
     * @throws InvalidKeySpecException   
     * @throws Exception 
     */
    private static Key keyGenerator(String keyStr) throws Exception {
        byte input[] = HexString2Bytes(keyStr);
        DESedeKeySpec KeySpec = new DESedeKeySpec(input);
        SecretKeyFactory KeyFactory = SecretKeyFactory.getInstance(KEY_ALGORITHM);
        return ((Key) (KeyFactory.generateSecret(((java.security.spec.KeySpec) (KeySpec)))));
    }

    private static int parse(char c) {
        if (c >= 'a') return (c - 'a' + 10) & 0x0f;
        if (c >= 'A') return (c - 'A' + 10) & 0x0f;
        return (c - '0') & 0x0f;
    }
 
    // 从十六进制字符串到字节数组转换 
    public static byte[] HexString2Bytes(String hexstr) {
        byte[] b = new byte[hexstr.length() / 2];
        int j = 0;
        for (int i = 0; i < b.length; i++) {
            char c0 = hexstr.charAt(j++);
            char c1 = hexstr.charAt(j++);
            b[i] = (byte) ((parse(c0) << 4) | parse(c1));
        }
        return b;
    }

    /** 
     * CBC解密 
     * @param key 密钥 
     * @param keyiv IV 
     * @param data Base64编码的密文 
     * @return 明文 
     * @throws Exception 
     */
    public static byte[] des3DecodeCBC(byte[] key, byte[] keyiv, byte[] data) throws Exception {
        Key deskey = keyGenerator(new String(key));
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        IvParameterSpec ips = new IvParameterSpec(keyiv);
        cipher.init(Cipher.DECRYPT_MODE, deskey, ips);
        byte[] bOut = cipher.doFinal(data);
        return bOut;
    }

    /**
     * CBC解密 
     * @param key 密钥,长度48位，不足补末尾A，多余截断
     * @param data 密文
     * @param code 编码
     * @return 返回明文
     */
    public static String des3DecodeCBC(String key, String data, String... code) {
		if(key!=null && data != null && !"".equals(data)){
			if(key.length()>48){
				key = key.substring(0, 48).toUpperCase();
			}else{
				key = String.format("%-48s", key).replace(" ", "a").toUpperCase();
			}
			try {
				byte[] d =des3DecodeCBC(key.getBytes(), KEYIV, Base64.getDecoder().decode(data));
				if(code.length>0){
					return new String(d,code[0]);
				}else{
					return new String(d,CODE);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return "";
	}
    
    
    public static void main(String[] args) throws Exception {
        String test = "放得开了房间来得及分类的减肥了到时框架房间抗衰老的房间";
        String tmp = des3EncodeCBC("2016009071025120000033vu2ia1kuc90gxmj1vb09pa3opl99yor", test);
        System.out.println(tmp);
        System.out.println(des3DecodeCBC("2016009071025120000033vu2ia1kuc90gxmj1vb09pa3opl99yor", tmp).equals(test));
    }
}
