package com.fykj.volunteer.sync.util.domain.sync_actp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <actPlan>
 * <info>
 * <info>
 * <code>FAIL</code>
 * <msg>同步失败：活动所属区划不存在</msg>
 * <uniCode>102034</uniCode>
 * </info>
 * </info>
 * </actPlan>
 *
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActpResult {
    /**
     * 活动注册id
     */
    private String apId;
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "actPlan")
    public static class ActPlan {
        private Info info;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "info")
    public static class Info {
        private SyncActpResult info;
    }
}
