package com.fykj.scaffold.zyz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.dto.BadgeImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadge;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeInstance;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTradeListing;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.mapper.ZyzBadgeMapper;
import com.fykj.scaffold.zyz.service.*;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.core.toolkit.StringPool.COMMA;


/**
 * 勋章信息-服务实现类
 *
 * @date 2025-05-09
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ZyzBadgeServiceImpl extends BaseServiceImpl<ZyzBadgeMapper, ZyzBadge> implements IZyzBadgeService {

    @Autowired
    private IZyzBadgeTradeListingService zyzBadgeTradeListingService;
    
    @Autowired
    private IZyzBadgeTransactionService zyzBadgeTransactionService;
    
    @Autowired
    private IZyzBadgeInstanceService zyzBadgeInstanceService;
    
    @Autowired
    private IZyzVolunteerService zyzVolunteerService;

    /**
     * 根据id切换勋章的置顶状态
     * @param id 勋章ID
     */
    public void switchTopStatus(Long id) {
        ZyzBadge badge = getById(id);
        badge.setTop(Boolean.FALSE.equals(badge.getTop()));
        updateById(badge);
    }

    @Override
    public void switchStatus(Long id) {
        ZyzBadge badge = getById(id);
        boolean oldStatus = badge.getStatus();
        badge.setStatus(Boolean.FALSE.equals(badge.getStatus()));
        badge.setListingAt(LocalDateTime.now());
        updateById(badge);
        
        // 处理交易发布
        processListingOnStatusChange(badge, oldStatus);
    }
    
    /**
     * 保存勋章并创建交易发布
     * @param badge 勋章信息
     * @return 是否保存成功
     */
    @Override
    public boolean saveAndCreateListing(ZyzBadge badge) {
        // 保存勋章信息
        boolean result = super.save(badge);
        
        // 如果保存成功并且状态为上架，则创建交易发布记录
        if (result && Boolean.TRUE.equals(badge.getStatus())) {
            zyzBadgeTradeListingService.createPlatformListing(badge);
        }
        
        return result;
    }
    
    /**
     * 更新勋章并处理交易发布
     * @param badge 勋章信息
     * @return 是否更新成功
     */
    @Override
    public boolean updateAndProcessListing(ZyzBadge badge) {
        // 获取当前数据库中的勋章信息
        ZyzBadge oldBadge = getById(badge.getId());
        boolean oldStatus = oldBadge.getStatus();
        
        // 更新勋章信息
        boolean result = super.updateById(badge);
        
        if (result) {
            // 处理交易发布
            processListingOnStatusChange(badge, oldStatus);
        }
        
        return result;
    }
    
    /**
     * 处理勋章状态更改时的交易发布
     * @param badge 勋章信息
     * @param oldStatus 原状态
     */
    private void processListingOnStatusChange(ZyzBadge badge, boolean oldStatus) {
        // 获取该勋章的平台发布交易
        List<ZyzBadgeTradeListing> existingListings = zyzBadgeTradeListingService.findPlatformListingByBadgeId(badge.getId());
        if (oldStatus && !badge.getStatus()) {
            // 状态从上架变为下架时，取消所有发布中的交易
            if (!existingListings.isEmpty()) {
                List<Long> listingIds = existingListings.stream()
                        .map(ZyzBadgeTradeListing::getId)
                        .collect(Collectors.toList());
                zyzBadgeTradeListingService.batchCancelListing(listingIds);
                log.debug("勋章下架，取消相关交易发布，勋章ID：{}", badge.getId());
            }
        } else if (!oldStatus && badge.getStatus()) {
            // 状态从下架变为上架时
            // 如果已有发布中的交易，先取消
            if (!existingListings.isEmpty()) {
                List<Long> listingIds = existingListings.stream()
                        .map(ZyzBadgeTradeListing::getId)
                        .collect(Collectors.toList());
                zyzBadgeTradeListingService.batchCancelListing(listingIds);
                log.debug("勋章上架前，取消已有交易发布，勋章ID：{}", badge.getId());
            }
            
            // 创建新的交易发布
            zyzBadgeTradeListingService.createPlatformListing(badge);
        } else if (oldStatus && badge.getStatus()) {
            // 状态保持上架，但信息可能变更，取消原交易，创建新交易
            if (!existingListings.isEmpty()) {
                List<Long> listingIds = existingListings.stream()
                        .map(ZyzBadgeTradeListing::getId)
                        .collect(Collectors.toList());
                zyzBadgeTradeListingService.batchCancelListing(listingIds);
                log.debug("勋章更新（保持上架），取消已有交易发布，勋章ID：{}", badge.getId());
            }
            
            zyzBadgeTradeListingService.createPlatformListing(badge);
        }
    }
    
    /**
     * 批量授予勋章
     * @param excel Excel文件
     * @param badgeId 勋章ID
     * @return 导入失败的记录列表
     */
    @Override
    public List<BadgeImportDto> batchAwardBadge(MultipartFile excel, Long badgeId) {
        // 验证Excel文件
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请上传文件！");
        }
        
        // 验证勋章ID存在
        ZyzBadge badge = getById(badgeId);
        
        // 读取Excel数据
        List<BadgeImportDto> dataList = ExcelUtil.readExcel(excel, BadgeImportDto.class, 0, 2);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        
        // 移除示例数据
        if ("李嘉泽".equals(dataList.get(0).getName())) {
            dataList.remove(0);
        }
        
        // 校验数据量
        if (dataList.size() > 10000) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请控制每批导入数据量在10000条内！");
        }
        
        // 校验Excel中数据是否有重复
        validateDuplicateInExcel(dataList);
        
        // 处理导入数据
        return processImportData(dataList, badgeId);
    }
    
    /**
     * 校验Excel中是否有重复数据
     * @param dataList 数据列表
     */
    private void validateDuplicateInExcel(List<BadgeImportDto> dataList) {
        // 检查手机号是否重复
        List<BadgeImportDto> phoneNotEmpty = dataList.stream()
                .filter(it -> StringUtil.isNotEmpty(it.getPhone()))
                .collect(Collectors.toList());
        Set<String> phoneSet = phoneNotEmpty.stream()
                .map(BadgeImportDto::getPhone)
                .collect(Collectors.toSet());
        
        // 如果有重复，抛出异常
        if (phoneNotEmpty.size() > phoneSet.size()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "Excel中存在志愿者手机号重复的数据，请进行去重处理后导入！");
        }
    }
    
    /**
     * 处理导入数据
     * @param dataList 数据列表
     * @param badgeId 勋章ID
     * @return 导入失败的记录列表
     */
    private List<BadgeImportDto> processImportData(List<BadgeImportDto> dataList, Long badgeId) {
        List<BadgeImportDto> failureList = new ArrayList<>();
        List<ZyzBadgeInstance> badgeInstances = new ArrayList<>();
        
        // 获取所有已存在志愿者信息
        List<ZyzVolunteer> volunteers = zyzVolunteerService.getVolunteersExist();
        Map<String, Long> phoneMap = volunteers.stream()
                .filter(v -> StringUtil.isNotEmpty(v.getPhone()))
                .collect(Collectors.toMap(
                        ZyzVolunteer::getPhone,
                        ZyzVolunteer::getId,
                        // 如果有重复手机号，保留第一个
                        (v1, v2) -> v1));
        
        // 获取已有该勋章的志愿者ID列表
        List<ZyzBadgeInstance> existingInstances = zyzBadgeInstanceService.lambdaQuery()
                .eq(ZyzBadgeInstance::getBadgeId, badgeId)
                .eq(ZyzBadgeInstance::getRecycle, false)
                .list();
        Set<Long> existingOwnerIds = existingInstances.stream()
                .map(ZyzBadgeInstance::getOwnerId)
                .collect(Collectors.toSet());
        
        // 处理每条数据
        for (BadgeImportDto dto : dataList) {
            List<String> errorReasons = new ArrayList<>();
            Long volunteerId = null;

            if(StringUtil.isEmpty(dto.getName())) {
                errorReasons.add("志愿者姓名必填");
            }

            if(StringUtil.isEmpty(dto.getCertificateId())) {
                errorReasons.add("身份证号码必填");
            }

            if(StringUtil.isEmpty(dto.getPhone())) {
                errorReasons.add("手机号码必填");
            } else {
                volunteerId = phoneMap.get(dto.getPhone());
                if (volunteerId == null) {
                    errorReasons.add("找不到对应的志愿者信息");
                } else if (existingOwnerIds.contains(volunteerId)) {
                    errorReasons.add("该志愿者已拥有此勋章");
                }
            }

            if(!errorReasons.isEmpty()) {
                dto.setFailReason(String.join(COMMA, errorReasons));
                failureList.add(dto);
            }else {
                // 创建勋章实例
                ZyzBadgeInstance instance = new ZyzBadgeInstance();
                instance.setBadgeId(badgeId);
                instance.setOwnerId(volunteerId);
                instance.setRecycle(false);
                badgeInstances.add(instance);
            }
        }
        
        // 批量保存勋章实例
        if (failureList.isEmpty() && CollectionUtil.isNotEmpty(badgeInstances)) {
            zyzBadgeInstanceService.saveBatch(badgeInstances);
            
            // 创建勋章授权流水记录
            Long operatorId = (Long) Oauth2Util.getUserId();
            String operatorName = Oauth2Util.getName();
            zyzBadgeTransactionService.batchCreateAwardTransactions(badgeInstances, badgeId, operatorId, operatorName);
        }
        
        return failureList;
    }

    /**
     * 批量授予勋章（通过志愿者ID列表）
     * @param badgeId 勋章ID
     * @param volunteerIds 志愿者ID列表
     * @return 授予结果信息
     */
    @Override
    public String batchAwardBadgeByIds(Long badgeId, List<Long> volunteerIds) {
        // 验证勋章ID存在
        ZyzBadge badge = getById(badgeId);
        // 验证志愿者ID列表不为空
        if (CollectionUtils.isEmpty(volunteerIds)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者ID列表不能为空");
        }

        // 校验数据量
        if (volunteerIds.size() > 10000) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请控制每批导入数据量在10000条内！");
        }

        // 获取所有已存在志愿者信息
        List<ZyzVolunteer> volunteers = zyzVolunteerService.lambdaQuery()
                .in(ZyzVolunteer::getId, volunteerIds)
                .eq(ZyzVolunteer::getWriteOff, false)
                .list();

        Map<Long, ZyzVolunteer> volunteerMap = volunteers.stream()
                .collect(Collectors.toMap(ZyzVolunteer::getId, v -> v));

        // 获取已有该勋章的志愿者ID列表
        List<ZyzBadgeInstance> existingInstances = zyzBadgeInstanceService.lambdaQuery()
                .eq(ZyzBadgeInstance::getBadgeId, badgeId)
                .eq(ZyzBadgeInstance::getRecycle, false)
                .in(ZyzBadgeInstance::getOwnerId, volunteerIds)
                .list();
        Set<Long> existingOwnerIds = existingInstances.stream()
                .map(ZyzBadgeInstance::getOwnerId)
                .collect(Collectors.toSet());

        // 验证所有志愿者ID的有效性
        for (Long volunteerId : volunteerIds) {
            // 验证志愿者存在
            if (!volunteerMap.containsKey(volunteerId)) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者ID " + volunteerId + " 不存在或已注销");
            }

            // 验证是否已拥有该勋章
            if (existingOwnerIds.contains(volunteerId)) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者ID " + volunteerId + " 已拥有此勋章");
            }
        }

        // 创建勋章实例
        List<ZyzBadgeInstance> badgeInstances = new ArrayList<>();
        for (Long volunteerId : volunteerIds) {
            ZyzBadgeInstance instance = new ZyzBadgeInstance();
            instance.setBadgeId(badgeId);
            instance.setOwnerId(volunteerId);
            instance.setRecycle(false);
            badgeInstances.add(instance);
        }

        // 批量保存勋章实例
        zyzBadgeInstanceService.saveBatch(badgeInstances);

        // 创建勋章授权流水记录
        Long operatorId = (Long) Oauth2Util.getUserId();
        String operatorName = Oauth2Util.getName();
        zyzBadgeTransactionService.batchCreateAwardTransactions(badgeInstances, badgeId, operatorId, operatorName);

        // 构建返回结果
        String result = "批量授予勋章完成，成功授予：" + volunteerIds.size() + "人";

        log.debug("批量授予勋章完成，勋章ID：{}，成功：{}人", badgeId, volunteerIds.size());

        return result;
    }
}
