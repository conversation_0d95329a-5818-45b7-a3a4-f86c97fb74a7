package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 苏州社工部服务时长上传同步响应
 */
@Data
public class SgbSyncServiceTimeResponse {
    
    /**
     * 响应信息列表
     */
    private List<ServiceTimeResult> infos;
    
    /**
     * 服务时长上传推送结果
     */
    @Data
    public static class ServiceTimeResult {

        /**
         * 响应状态码 - SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 第三方系统服务时长唯一标识键
         */
        private String uniCode;
    }
}
