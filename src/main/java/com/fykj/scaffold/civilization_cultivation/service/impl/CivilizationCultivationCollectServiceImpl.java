package com.fykj.scaffold.civilization_cultivation.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CheckAttachmentDto;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CheckInfoUploadDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationCollectParams;
import com.fykj.scaffold.civilization_cultivation.mapper.CivilizationCultivationCollectMapper;
import com.fykj.scaffold.civilization_cultivation.service.ICivilizationCultivationCollectService;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.domain.entity.SysOss;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.security.business.service.ISysOssService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.fykj.scaffold.civilization_cultivation.cons.CivilizationCultivationCons.*;
import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;
import static constants.Mark.COMMA;


/**
 * 文明培育征集-管理
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CivilizationCultivationCollectServiceImpl extends BaseServiceImpl<CivilizationCultivationCollectMapper, CivilizationCultivationCollect> implements ICivilizationCultivationCollectService {

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private ISysOssService ossService;

    @Override
    public IPage<CivilizationCultivationCollect> page(BaseParams params) {
        ((CivilizationCultivationCollectParams)params).setViewableStatus(Arrays.asList(STATUS_WAIT_DEAL, STATUS_WAIT_CHECK, STATUS_CHECK_PASS, STATUS_CHECK_REFUSE));
        IPage<CivilizationCultivationCollect> result = super.page(params);
        DictTransUtil.trans(result.getRecords());
        return result.convert(it -> {
            String pictures = it.getPicture();
            if (StringUtil.isNotEmpty(pictures)) {
                it.setPicture(pictures.split(COMMA)[0]);
            }
            return it;
        });
    }

    @Override
    public void collectSubmit(CivilizationCultivationCollect collectInfo) {
        collectInfo.setYear(LocalDate.now().getYear());
        setBelongOrgInfo(collectInfo);
        List<String> pictureList = collectInfo.getPictureList();
        if (pictureList != null && !pictureList.isEmpty()) {
            collectInfo.setPicture(String.join(COMMA, pictureList));
        }
        collectInfo.setStatus(STATUS_WAIT_DEAL);
        super.saveOrUpdate(collectInfo);
    }

    private void setBelongOrgInfo(CivilizationCultivationCollect entity) {
        String belongCommunityCode = entity.getBelongCommunityCode();
        if (StringUtil.isEmpty(belongCommunityCode)) {
            throw new BusinessException(ResultCode.FAIL, "请选择所属社区！");
        }
        SysOrg community = orgService.getByCode(belongCommunityCode);
        if (community == null) {
            throw new BusinessException(ResultCode.FAIL, "所属社区不存在！");
        }
        entity.setBelongCommunityName(community.getName());
        Long streetId = community.getParentId();
        if (streetId == null) {
            throw new BusinessException(ResultCode.FAIL, "所属街道不存在！");
        }
        SysOrg street = orgService.lambdaQuery().eq(SysOrg::getId, streetId).one();
        if (street == null) {
            throw new BusinessException(ResultCode.FAIL, "所属街道不存在！");
        }
        entity.setBelongStreetCode(street.getCode());
        entity.setBelongStreetName(street.getName());
    }

    @Override
    public IPage<CivilizationCultivationCollect> myCollectSubmit(CivilizationCultivationCollectParams params) {
        Serializable userId = Oauth2Util.getUserId();
        if (userId == null) {
            throw new BusinessException(ResultCode.FAIL, "当前用户获取异常！");
        }
        params.setCreator((Long)userId);
        return baseMapper.myCollectSubmit(params.getPage(), params);
    }

    @Override
    public CivilizationCultivationCollect getDetail(Long id) {
        CivilizationCultivationCollect result = super.getById(id);
        DictTransUtil.trans(result);
        String status = result.getStatus();
        result.setEnableCallback(STATUS_WAIT_DEAL.equals(status));
        result.setEnableEdit(STATUS_CALLBACK.equals(status) || STATUS_CHECK_REFUSE.equals(status));
        result.setEnableRemove(STATUS_WAIT_DEAL.equals(status) || STATUS_CALLBACK.equals(status) || STATUS_CHECK_REFUSE.equals(status));
        return result;
    }

    @Override
    public void collectCallback(Long id) {
        CivilizationCultivationCollect collectInfo = super.getById(id);
        String status = collectInfo.getStatus();
        if (!STATUS_WAIT_DEAL.equals(status)) {
            throw new BusinessException(ResultCode.FAIL, "该条征集数据当前不支持撤回！");
        }
        collectInfo.setStatus(STATUS_CALLBACK);
        super.updateById(collectInfo);
    }

    @Override
    public void collectRemove(Long id) {
        CivilizationCultivationCollect collectInfo = super.getById(id);
        String status = collectInfo.getStatus();
        if (STATUS_WAIT_CHECK.equals(status) || STATUS_CHECK_PASS.equals(status)) {
            throw new BusinessException(ResultCode.FAIL, "该条征集数据当前不支持删除！");
        }
        super.removeById(collectInfo);
    }

    @Override
    public void collectSaveOrUpdate(CivilizationCultivationCollect collectInfo) {
        setBelongOrgInfo(collectInfo);
        List<String> pictureList = collectInfo.getPictureList();
        if (pictureList != null && !pictureList.isEmpty()) {
            collectInfo.setPicture(String.join(COMMA, pictureList));
        }
        collectInfo.setStatus(STATUS_WAIT_DEAL);
        super.saveOrUpdate(collectInfo);
    }

    @Override
    public void transferToStreetCheck(Long id) {
        CivilizationCultivationCollect collectInfo = super.getById(id);
        if (collectInfo == null) {
            throw new BusinessException(ResultCode.FAIL, "当前文明培育征集不存在！");
        }
        collectInfo.setStatus(STATUS_WAIT_CHECK);
        super.updateById(collectInfo);
    }

    @Override
    public IPage<CivilizationCultivationCollect> pageForStreetCheck(CivilizationCultivationCollectParams params) {
        String capacity = Oauth2Util.getManagerCapacity();
        if (!Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            throw new BusinessException(ResultCode.FAIL, "当前用户无街道审核权限！");
        }
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "无法获取您当前的街道组织！");
        }
        params.setCheckStreetOrg(orgCode);
        params.setViewableStatus(Arrays.asList(STATUS_WAIT_CHECK, STATUS_CHECK_PASS, STATUS_CHECK_REFUSE));
        IPage<CivilizationCultivationCollect> result = super.page(params);
        DictTransUtil.trans(result.getRecords());
        return result.convert(it -> {
            String pictures = it.getPicture();
            if (StringUtil.isNotEmpty(pictures)) {
                it.setPicture(pictures.split(COMMA)[0]);
            }
            return it;
        });
    }

    @Override
    public void checkRefuse(Long id) {
        CivilizationCultivationCollect collectInfo = super.getById(id);
        if (collectInfo == null) {
            throw new BusinessException(ResultCode.FAIL, "当前文明培育征集不存在！");
        }
        String capacity = Oauth2Util.getManagerCapacity();
        if (!Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            throw new BusinessException(ResultCode.FAIL, "当前用户无街道审核权限！");
        }
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "无法获取您当前的街道组织！");
        }
        String belongStreetCode = collectInfo.getBelongStreetCode();
        if (!orgCode.equals(belongStreetCode)) {
            throw new BusinessException(ResultCode.FAIL, "您无权限退回该文明培育征集数据！");
        }
        collectInfo.setStatus(STATUS_CHECK_REFUSE);
        super.updateById(collectInfo);
    }

    @Override
    public void checkInfoRemove(Long collectId, String checkInfoId) {
        CivilizationCultivationCollect collectInfo = super.getById(collectId);
        if (collectInfo == null) {
            throw new BusinessException(ResultCode.FAIL, "当前文明培育征集不存在！");
        }
        List<CheckAttachmentDto> checkAttachments = collectInfo.getCheckAttachments();
        if (checkAttachments == null || checkAttachments.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "当前文明培育征集不存在核实信息！");
        }
        boolean removeFlag = false;
        for (CheckAttachmentDto attachment : checkAttachments) {
            if (checkInfoId.equals(attachment.getId())) {
                checkAttachments.remove(attachment);
                removeFlag = true;
                break;
            }
        }
        if (!removeFlag) {
            throw new BusinessException(ResultCode.FAIL, "当前核实信息不存在！");
        }
        if (checkAttachments.isEmpty()) {
            collectInfo.setCheckAttachments(null);
            collectInfo.setStatus(STATUS_WAIT_CHECK);
        } else {
            collectInfo.setCheckAttachments(checkAttachments);
        }
        super.updateById(collectInfo);
    }

    @Override
    public void checkInfoUpload(CheckInfoUploadDto uploadDto) {
        Long collectId = uploadDto.getId();
        CivilizationCultivationCollect collectInfo = super.getById(collectId);
        if (collectInfo == null) {
            throw new BusinessException(ResultCode.FAIL, "当前文明培育征集不存在！");
        }
        List<MultipartFile> files = uploadDto.getFiles();
        if (files == null || files.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "核实信息未上传图片！");
        }
        List<CheckAttachmentDto> checkAttachments = collectInfo.getCheckAttachments();
        if (checkAttachments == null || checkAttachments.isEmpty()) {
            checkAttachments = new ArrayList<>();
        }
        for (MultipartFile it : files) {
            SysOss ossInfo = ossService.upload(it, OssCons.OSS_LOCAL, false, null, null);
            CheckAttachmentDto attachment = new CheckAttachmentDto();
            attachment.setId(String.valueOf(ossInfo.getId()));
            attachment.setName(ossInfo.getFileName());
            attachment.setPath(ossInfo.getPath());
            attachment.setUploadTime(ossInfo.getCreateDate().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT)));
            checkAttachments.add(attachment);
        }
        collectInfo.setStatus(STATUS_CHECK_PASS);
        collectInfo.setCheckAttachments(checkAttachments);
        super.updateById(collectInfo);
    }

    @Override
    public List<CheckAttachmentDto> getCheckInfo(Long id) {
        CivilizationCultivationCollect collectInfo = super.getById(id);
        if (collectInfo == null) {
            throw new BusinessException(ResultCode.FAIL, "当前文明培育征集不存在！");
        }
        return collectInfo.getCheckAttachments().stream().sorted(Comparator.comparing(CheckAttachmentDto::getUploadTime).reversed()).collect(Collectors.toList());
    }
}
