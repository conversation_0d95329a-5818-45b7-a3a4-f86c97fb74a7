package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.SysAction;
import com.fykj.scaffold.security.business.domain.params.SysActionParams;
import com.fykj.scaffold.security.business.service.ISysActionService;
import com.fykj.scaffold.security.oauth2.filter.Oauth2FilterInvocationSecurityMetadataSource;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 系统资源-功能操作
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
@RestController
@RequestMapping("/admin/sys/action")
public class SysActionController extends BaseController<ISysActionService, SysAction, SysActionParams> {

    @Autowired
    private Oauth2FilterInvocationSecurityMetadataSource oauth2FilterInvocationSecurityMetadataSource;

    @ApiOperation("获取菜单下的操作")
    @GetMapping(value = "/findByMenuId")
    public JsonResult<List<SysAction>> findByMenuId(String menuId) {
        return new JsonResult<>(baseService.lambdaQuery().eq(SysAction::getMenuId, Long.parseLong(menuId)).orderByAsc(SysAction::getPriority).list());
    }

    @Override
    public Result save(@RequestBody SysAction entity) {
        if (baseService.isExist(entity.getActionCode(), entity.getId())) {
            throw new BusinessException(ResultCode.ERROR, "编码已存在");
        }
        Result ret = super.save(entity);
        oauth2FilterInvocationSecurityMetadataSource.init();
        return ret;
    }

    @Override
    public Result update(@RequestBody SysAction entity) {
        if (baseService.isExist(entity.getActionCode(), entity.getId())) {
            throw new BusinessException(ResultCode.ERROR, "编码已存在");
        }
        Result ret = super.update(entity);
        oauth2FilterInvocationSecurityMetadataSource.init();
        return ret;
    }

    @Override
    public Result removeByIds(String ids) {
        Result ret = super.removeByIds(ids);
        oauth2FilterInvocationSecurityMetadataSource.init();
        return ret;
    }
}
