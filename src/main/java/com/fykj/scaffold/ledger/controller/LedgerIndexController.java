package com.fykj.scaffold.ledger.controller;

import com.fykj.scaffold.ledger.domain.entity.LedgerIndex;
import com.fykj.scaffold.ledger.domain.params.LedgerIndexParams;
import com.fykj.scaffold.ledger.service.ILedgerIndexService;
import constants.Mark;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.Arrays;
import java.util.List;

/**
 * 台账指标-接口控制器
 *
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/admin/ledger/index")
@Api(tags = "台账指标-管理接口")
public class LedgerIndexController extends BaseController<ILedgerIndexService, LedgerIndex, LedgerIndexParams> {

    @ApiOperation("获取树")
    @GetMapping(value = "tree")
    public JsonResult<List<LedgerIndex>> tree() {
        return new JsonResult<>(baseService.tree());
    }

    @ApiOperation("根据id获取")
    @GetMapping
    @Override
    public Result get(@RequestParam Long id) {
        return new JsonResult<>(this.baseService.get(id));
    }

    @ApiOperation("删除方法")
    @GetMapping(value = "/deleteById")
    public Result deleteById(@RequestParam Long id) {
        boolean result = baseService.deleteById(id);
        if (result) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }
}
