package com.fykj.scaffold.support.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;
import utils.LocalDateTimeUtil;
import utils.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知社区库工具类
 */
@Slf4j
public class SIPCommunityUtil {

    public static SIPCommunityDto getCommunityPersonInfo(String idCard) {
        Map<String, Object> paramMap = new HashMap<>();
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("_expr[1]/zjhm", idCard);
        paramMap.put("criteria", criteria);
        paramMap.put("pageIndex", 0);
        paramMap.put("pageSize", 10);
        Map<String, List<String>> headers = new HashMap<>();
        headers.put("appid", CollectionUtil.newArrayList("buQDw12O"));
        headers.put("Content-Type", CollectionUtil.newArrayList("application/json;charset=UTF-8"));
        HttpResponse httpResponse = HttpUtil.createPost("http://drs.sipac.gov.cn/api/biz/rest/drc/b232eba42052a412e8ca54330d7a11431/query")
                .header(headers)
                .body(JSONUtil.toJsonStr(paramMap)).execute();
        if (httpResponse.getStatus() != HttpStatus.HTTP_OK || !JSONUtil.isJson(httpResponse.body())) {
            log.error("调用知社区库出错,返回内容" + httpResponse.body());
            throw new BusinessException(ResultCode.BAD_REQUEST, "调用知社区库出错");
        }
        JSONObject back = JSONObject.parseObject(httpResponse.body());
        JSONObject exception = back.getJSONObject("exception");
        if (exception != null) {
            String code = exception.getString("code");
            String message = exception.getString("message");
            throw new BusinessException(ResultCode.BAD_REQUEST, "调用知社区库出错(" + code + ")：" + message);
        }
        List<SIPCommunityUtil.Back> backs= JSON.parseArray(back.getString("datas"), SIPCommunityUtil.Back.class);
        if(CollectionUtil.isEmpty(backs)){
            return new SIPCommunityDto();
        }
        SIPCommunityDto dto= new SIPCommunityDto();
        SIPCommunityUtil.Back result=backs.get(0);
        dto.setName(result.getXm());
        dto.setAddress(result.getJzdz());
        dto.setCommunityName(result.getSq());
        dto.setIdCard(result.getZjhm());
        dto.setStreetName(result.getJd());
        dto.setVillageName(result.getXq());
        if(StringUtil.isNotEmpty(result.getTbsh())){
            dto.setSyncTime(LocalDateTimeUtil.parseStringToDateTime(result.getTbsh() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }
        return dto;
    }
    @Data
    public static class Back {
        private String xm;
        private String zjhm;
        private String jzdz;
        private String jd;
        private String sq;
        private String xq;
        private String tbsh;
    }
}

