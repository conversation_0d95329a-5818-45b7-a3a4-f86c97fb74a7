package com.fykj.scaffold.task.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class TaskMessingDto {
    /**
     * 同步成功的总时长
     */
    private BigDecimal realServiceLong;
    /**
     * 同步成功的活动数
     */
    private Integer successActivitiesNums;
    /**
     * 同步成功的志愿者数
     */
    private Integer successVolunteerNums;
    /**
     * 任务导入的所有的志愿者数
     */
    private Integer importVolunteerNums;
    /**
     * 同步过的活动数
     */
    private Integer syncActivitiesNums;
    /**
     * 所有的活动数
     */
    private Integer taskAllActivitiesNums;
    /**
     * 同步过的活动招募数
     */
    private Integer syncAppliesNums;
    /**
     * 所有的动招募数
     */
    private Integer taskAllAppliesNums;
}
