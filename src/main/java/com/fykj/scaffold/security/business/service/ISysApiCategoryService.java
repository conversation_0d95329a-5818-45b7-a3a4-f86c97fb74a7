package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.dto.CascaderDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiCategory;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 接口目录表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface ISysApiCategoryService extends IBaseService<SysApiCategory> {

    /**
     * 类目树组装
     *
     * @return 分类树
     */
    List<CascaderDto> cascader();

    /**
     * 获取级联树
     * @return
     */
    List<SysApiCategory> tree();

    /**
     * 设置启用/禁用
     *
     * @param id 主键
     * @return 更新结果
     */
    boolean setEnable(Long id);

    /**
     * 获取所有启用状态的类目
     * @return
     */
    List<SysApiCategory> listOfEnable();
}

