package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.mapper.SysOrgMapper;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.ImageUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDict;
import com.fykj.scaffold.sync.service.IZyzSyncDepService;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictService;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingSysOrgRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingSysOrgRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.event.SysOrgDockingEvent;
import com.fykj.scaffold.zyz.domain.dto.dashboard_sum.SubAssociationDataSumDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.util.LngLonUtil;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import joptsimple.internal.Strings;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.Cons.FIVE_AREA_SUB_ASSOCIATION;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.ActivityDataDockingType.DT_UPDATE;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;
import com.fykj.scaffold.security.business.cons.SsoCons;

/**
 * 部门表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-10-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysOrgServiceImpl extends BaseServiceImpl<SysOrgMapper, SysOrg> implements ISysOrgService {

    @Autowired
    private IZyzSyncRegionDictService regionDictService;

    @Autowired
    private IZyzSyncDepService depService;
    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IZSQDockingSysOrgRecordService sysOrgDockingRecordService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;

    @Override
    public SysOrg getOrgById(long orgId) {
        SysOrg org = getById(orgId);
        ZyzSyncRegionDict regionDict = regionDictService.getByRegionCode(org.getRegionCode());
        if (regionDict != null) {
            org.setRegionCodeLink(regionDict.getCodeLink());
        }
        return org;
    }

    @Override
    public SysOrg saveOrg(SysOrg org) {
        if (isExist(org.getCode(), null)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "code已存在");
        }
        //基础数据验证
        basicCheck(org);
        org.setCodePrefix(buildCodeLink(org));
        org.setRegionName(regionDictService.getByRegionCode(org.getRegionCode()).getRegionName());
        org.setDepName(depService.getByDepId(org.getDepId()).getDepName());
        BigDecimal longitude = org.getLongitude(), latitude = org.getLatitude();
        if (ObjectUtil.isEmpty(longitude) || ObjectUtil.isEmpty(latitude)) {
            save(org);
            return org;
        }
        double[] gps84 = LngLonUtil.gcj02ToGps84(latitude.doubleValue(), longitude.doubleValue());
        org.setWsg84Latitude(BigDecimal.valueOf(gps84[0]));
        org.setWsg84Longitude(BigDecimal.valueOf(gps84[1]));
        save(org);
        dockingZSQ(org, DT_UPDATE);
        return org;
    }

    private void basicCheck(SysOrg org) {
        //验证图片
        ImageUtil.checkPic(org.getLogoUrl());
        //验证身份证
        checkIdCard(org.getLinkIdCard());
    }

    private void checkIdCard(String idCard) {
        if (StringUtil.isEmpty(idCard)) {
            return;
        }
        //验证身份证
        if (!IdcardUtil.isValidCard(idCard)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "当前联系人身份证号码校验异常！");
        }
        ZyzVolunteer volunteer = volunteerService.lambdaQuery().eq(ZyzVolunteer::getCertificateId, idCard).eq(ZyzVolunteer::getWriteOff, false).one();
        if (volunteer == null) {
            throw new BusinessException(ResultCode.FAIL, "当前联系人身份证号码尚未注册成为志愿者！");
        }


    }

    @Override
    public SysOrg updateOrg(SysOrg org) {
        if (isExist(org.getCode(), org.getId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "code已存在");
        }
        //基础数据验证
        basicCheck(org);
        org.setCodePrefix(buildCodeLink(org));
        org.setRegionName(regionDictService.getByRegionCode(org.getRegionCode()).getRegionName());
        org.setDepName(depService.getByDepId(org.getDepId()).getDepName());
        BigDecimal longitude = org.getLongitude(), latitude = org.getLatitude();
        if (ObjectUtil.isEmpty(longitude) || ObjectUtil.isEmpty(latitude)) {
            updateById(org);
            return org;
        }
        double[] gps84 = LngLonUtil.gcj02ToGps84(latitude.doubleValue(), longitude.doubleValue());
        org.setWsg84Latitude(BigDecimal.valueOf(gps84[0]));
        org.setWsg84Longitude(BigDecimal.valueOf(gps84[1]));
        //修改后同步状态更改为待同步
        org.setIsSync(Cons.PlatformSyncState.WAIT_SYNC);
        updateById(org);
        dockingZSQ(org, DT_UPDATE);
        return org;
    }

    @Override
    public boolean isExist(String code, Long id) {
        return lambdaQuery().eq(SysOrg::getCode, code).ne(id != null, BaseEntity::getId, id).count() > 0;
    }

    /**
     * 构建codeLink
     *
     * @param org
     * @return
     */
    private String buildCodeLink(SysOrg org) {
        //最顶层
        if (org.getParentId() == null) {
            return org.getCode();
        }
        Map<Long, SysOrg> orgMap = list().stream().collect(Collectors.toMap(SysOrg::getId, Function.identity(), (key1, key2) -> key2));
        List<String> codePrefix = new ArrayList<>();
        //先把自己添加进去
        codePrefix.add(org.getCode());
        SysOrg previousOrg = orgMap.get(org.getParentId());
        while (previousOrg != null) {
            codePrefix.add(previousOrg.getCode());
            previousOrg = orgMap.get(previousOrg.getParentId());
        }
        //倒叙变为字符串
        return StringUtil.join(CollectionUtil.reverse(codePrefix), Mark.COMMA);
    }

    @Override
    public SysOrg getByCode(String code) {
        return lambdaQuery().eq(SysOrg::getCode, code).one();
    }

    @Override
    public String getOrgName(String code) {
        SysOrg org = getByCode(code);
        return org != null ? org.getName() : "";
    }

    @Override
    public List<SysOrg> getSubOrg(String topCode, boolean isContainsSelf) {
        SysOrg topOrg = getByCode(topCode);
        String topLink = topOrg.getCodePrefix();
        return lambdaQuery().likeRight(SysOrg::getCodePrefix, topLink).ne(!isContainsSelf, SysOrg::getCode, topCode).orderByAsc(SysOrg::getSequence).list();
    }

    @Override
    public List<SysOrg> getSubOrgOnlyContainFiveSubAssociation(String topCode, boolean isContainsSelf) {
        SysOrg topOrg = getByCode(topCode);
        String topLink = topOrg.getCodePrefix();
        List<SysOrg> tmp = lambdaQuery()
                .likeRight(SysOrg::getCodePrefix, topLink)
                .ne(!isContainsSelf, SysOrg::getCode, topCode)
                .orderByAsc(SysOrg::getSequence).list();
        List<SysOrg> result = new ArrayList<>();
        tmp.forEach(it -> {
            boolean level2Flag = it.getLevel() == 2 && !FIVE_AREA_SUB_ASSOCIATION.contains(it.getCode());
            if (level2Flag) {
                return;
            }
            boolean level3FlagOne = it.getLevel() == 3;
            if (level3FlagOne) {
                SysOrg pOrg = tmp.stream().filter(im -> im.getId().equals(it.getParentId())).findAny().orElse(null);
                if (ObjectUtil.isNotEmpty(pOrg) && !FIVE_AREA_SUB_ASSOCIATION.contains(pOrg.getCode())) {
                    return;
                }
            }
            result.add(it);
        });
        return result;
    }

    @Override
    public List<SysOrg> getOrgTree(String topDeptCode, boolean isContainSelf) {
        SysOrg topOrg = getByCode(topDeptCode);
        List<SysOrg> myDeps = getSubOrg(topDeptCode, false);
        Map<Long, List<SysOrg>> childrenDeptMap = myDeps.stream().collect(Collectors.groupingBy(SysOrg::getParentId));
        List<SysOrg> resList = new ArrayList<>();
        List<SysOrg> children = buildTree(topOrg, childrenDeptMap);
        if (isContainSelf) {
            topOrg.setChildren(children);
            resList.add(topOrg);
        } else {
            resList.addAll(children);
        }
        return resList;
    }

    @Override
    public List<SysOrg> getOrgTreeOnlyContainFiveSubAssociation(String topDeptCode, boolean isContainSelf) {
        SysOrg topOrg = getByCode(topDeptCode);
        List<SysOrg> myDeps = getSubOrgOnlyContainFiveSubAssociation(topDeptCode, false);
        Map<Long, List<SysOrg>> childrenDeptMap = myDeps.stream().collect(Collectors.groupingBy(SysOrg::getParentId));
        List<SysOrg> resList = new ArrayList<>();
        List<SysOrg> children = buildTree(topOrg, childrenDeptMap);
        if (isContainSelf) {
            topOrg.setChildren(children);
            resList.add(topOrg);
        } else {
            resList.addAll(children);
        }
        return resList;
    }

    /**
     * 构建分类树-递归查询所有子分类
     *
     * @param
     */
    private List<SysOrg> buildTree(SysOrg org, Map<Long, List<SysOrg>> map) {
        List<SysOrg> childrenList = map.get(org.getId());
        org.setChildren(childrenList);
        if (!CollectionUtils.isEmpty(childrenList)) {
            childrenList.forEach(item -> this.buildTree(item, map));
        }
        return childrenList;
    }

    @Override
    public SysOrg getParentByCode(String code) {
        SysOrg org = getByCode(code);
        if (ObjectUtil.isEmpty(org) || ObjectUtil.isEmpty(org.getParentId())) {
            return null;
        }
        return getById(org.getParentId());
    }

    @Override
    public Integer getOrgLevel(String orgCode) {
        SysOrg org = getByCode(orgCode);
        return getOrgLevel(org);
    }

    @Override
    public Integer getOrgLevel(SysOrg org) {
        if (ObjectUtil.isEmpty(org)) {
            throw new BusinessException(ResultCode.FAIL, "组织编码有误或发布者所在组织不存在！");
        }
        int i = 1;
        Long parentId = org.getParentId();
        while (ObjectUtil.isNotEmpty(parentId)) {
            org = getById(parentId);
            parentId = org.getParentId();
            i++;
        }
        return i;
    }

    @Override
    public List<SysOrg> getAreaSubAssociation() {
        return getAreaSubAssociation(false, true);
    }

    @Override
    public List<SysOrg> getAreaSubAssociation(boolean withTop, boolean all) {
        List<SysOrg> result = new ArrayList<>();
        if (withTop) {
            SysOrg topOrg = lambdaQuery().isNull(SysOrg::getParentId).one();
            result.add(topOrg);
        }
        if (all) {
            result.addAll(lambdaQuery().eq(SysOrg::getLevel, 2)
                    .orderByAsc(SysOrg::getSequence).list());
        } else {
            result.addAll(lambdaQuery().eq(SysOrg::getLevel, 2).in(SysOrg::getCode, Cons.FIVE_AREA_SUB_ASSOCIATION)
                    .orderByAsc(SysOrg::getSequence).list());
        }
        return result;
    }

    @Override
    public List<SubAssociationDataSumDto> getSubAssociationSumData() {
        return baseMapper.getSubAssociationSumData(Cons.FIVE_AREA_SUB_ASSOCIATION);
    }

    @Override
    public List<String> getSubOrgCodeList(String topCode, boolean isContainsSelf) {
        SysOrg topOrg = getByCode(topCode);
        if (ObjectUtil.isEmpty(topOrg)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "上级机构不存在");
        }
        String topLink = topOrg.getCodePrefix();
        return lambdaQuery().select(SysOrg::getId, SysOrg::getCode)
                .likeRight(SysOrg::getCodePrefix, topLink)
                .ne(!isContainsSelf, SysOrg::getCode, topCode)
                .list().stream()
                .map(SysOrg::getCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysOrg> listNearbyOrg(String orgCode, BigDecimal distance) {
        // 获取所有下级组织
        List<SysOrg> categories = getSubOrg(orgCode, true);

        // 获取中心点组织
        SysOrg centerOrg = categories.stream()
                .filter(org -> orgCode.equals(org.getCode()))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "部门不存在"));

        // 筛选出距离范围内的组织
        return categories.stream()
                .filter(org -> {
                    // 排除中心点自身
                    if (orgCode.equals(org.getCode())) {
                        return false;
                    }
                    // 检查是否有经纬度信息
                    if (org.getLongitude() == null || org.getLatitude() == null) {
                        return false;
                    }
                    // 计算距离
                    return calculatedDistance(org, centerOrg) <= distance.doubleValue();
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算两个组织机构之间的距离(单位:公里)
     * @param org 目标组织
     * @param centerOrg 中心点组织
     * @return 两点之间的距离(公里)
     */
    private static double calculatedDistance(SysOrg org, SysOrg centerOrg) {
        double lat1 = centerOrg.getLatitude().doubleValue();
        double lng1 = centerOrg.getLongitude().doubleValue();
        double lat2 = org.getLatitude().doubleValue();
        double lng2 = org.getLongitude().doubleValue();

        double earthRadius = 6371; // 地球半径，单位千米
        double dLat = Math.toRadians(lat2 - lat1);
        double dLng = Math.toRadians(lng2 - lng1);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                Math.sin(dLng / 2) * Math.sin(dLng / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return earthRadius * c;
    }

    public void dockingZSQ(SysOrg sysOrg, String operation) {

        ZSQDockingSysOrgRecord dockingRecord = new ZSQDockingSysOrgRecord();
        Long sysOrgId = sysOrg.getId();
        dockingRecord.setOrgId(sysOrgId);
        dockingRecord.setDockingType(DOCKING_TYPE_PUSH);
        dockingRecord.setDockingOperation(operation);
        dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_PENDING);
        dockingRecord.setDockingTime(LocalDateTime.now());
        dockingRecord.setRetry(Boolean.FALSE);
        Serializable userId = Oauth2Util.getUserId();
        String zwtUserId = userId == null ? null : ssoUserMappingService.getSSOUserIdByProviderAndZYZUserId(SsoCons.SsoProvider.ZWT.name(), (Long)userId);
        if (StringUtil.isEmpty(zwtUserId)) {
            dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
            dockingRecord.setDockingMsg("当前操作人" + (userId == null ? Strings.EMPTY : "{" + userId + "}") + "未获取政务通 userId ！");
            sysOrgDockingRecordService.save(dockingRecord);
            SysOrgDockingEvent event = SysOrgDockingEvent.builder().dockingRecordId(dockingRecord.getId()).orgId(sysOrgId).operation(operation).zyzUserId(Oauth2Util.getUserId()).build();
            dockingRecord.setDockingData(JSONObject.toJSONString(event));
            sysOrgDockingRecordService.updateById(dockingRecord);
            return;
        }
        sysOrgDockingRecordService.save(dockingRecord);
        SysOrgDockingEvent event = SysOrgDockingEvent.builder().dockingRecordId(dockingRecord.getId()).orgId(sysOrgId).operation(operation).zwtUserId(zwtUserId).build();
        dockingRecord.setDockingData(JSONObject.toJSONString(event));
        sysOrgDockingRecordService.updateById(dockingRecord);
        mqTemplate.send(TOPIC_SYS_ORG_DOCKING, MessageBuilder.withPayload(event).build());
    }

}
