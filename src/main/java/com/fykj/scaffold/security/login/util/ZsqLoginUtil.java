package com.fykj.scaffold.security.login.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.security.login.cons.ZsqConfig;
import com.fykj.scaffold.zsq_docking.utils.SHA256Util;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static cn.hutool.core.text.StrPool.C_SPACE;

@Component
@Slf4j
public class ZsqLoginUtil {

    @Autowired
    private ZsqConfig zsqConfig;

    @Autowired
    private IUserService userService;

    private static final String REQUEST_HEADER_AUTHORIZATION = "Authorization";

    private static final String PARAMS_APPID = "appId";

    private static final String PARAMS_SECRET = "secret";

    private static final String PARAMS_TIME = "time";

    private static final String PARAMS_SIGN = "sign";

    private static final String PARAMS_CODE = "code";

    private static final String PARAMS_USER_TOKEN = "userToken";

    private static final String RESPONSE_CODE = "code";

    private static final String RESPONSE_OBJ = "obj";

    private static final String AUTH_TOKEN_RESPONSE_TOKEN_TYPE = "token_type";

    private static final String AUTH_TOKEN_RESPONSE_ACCESS_TOKEN = "access_token";

    private static final String USER_TOKEN_RESPONSE_USER_TOKEN = "userToken";

    private static final String PRIVATE_KEY = "372f0bd7c8f84dac9963ede85e7b1e0b";

    private static final String LY_SECRET = "8BdaUjqRRdzhuwbjNE5EwW2W4LQJFMyA";

    private static final String LY_APPID = "fengyuntech";

    /**
     * 解密
     *
     * @param ciphertext 密文
     * @return 明文
     */
    public static String decrypt(String ciphertext) {
        SymmetricCrypto sm4 = SmUtil.sm4(HexUtil.decodeHex(PRIVATE_KEY));
        return sm4.decryptStr(ciphertext);
    }

    /**
     * 根据知社区平台 authorization
     *
     * @return authToken
     */
    public String getAuthToken() {
        JSONObject params = new JSONObject();
        params.set(PARAMS_APPID, zsqConfig.getAppId());
        params.set(PARAMS_SECRET, zsqConfig.getSecret());
        String url = zsqConfig.getBaseUrl() + zsqConfig.getAuthTokenUrl();
        JSONObject resultObj = doPost(url, params, null);
        String tokenType = resultObj.getStr(AUTH_TOKEN_RESPONSE_TOKEN_TYPE);
        String token = resultObj.getStr(AUTH_TOKEN_RESPONSE_ACCESS_TOKEN);
        if (StringUtils.isEmpty(tokenType) || StringUtils.isEmpty(token)) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台接口：{" + url + "}请求失败，接口obj返回： " + JSON.toJSONString(resultObj));
        }
        return tokenType + C_SPACE + token;
    }

    /**
     * 根据code获取userToken
     *
     * @param authToken 请求 header authorization
     * @param code   临时code
     * @return userToken
     */
    public String getTokenByCode(String authToken, String code) {
        long time = System.currentTimeMillis();
        String sign = DigestUtils.md5Hex(zsqConfig.getAppId().concat(zsqConfig.getSecret()).concat(String.valueOf(time)));
        JSONObject params = new JSONObject();
        params.set(PARAMS_APPID, zsqConfig.getAppId());
        params.set(PARAMS_TIME, String.valueOf(time));
        params.set(PARAMS_SIGN, sign);
        params.set(PARAMS_CODE, code);
        String url = zsqConfig.getBaseUrl() + zsqConfig.getCodeToTokenUrl();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(REQUEST_HEADER_AUTHORIZATION, authToken);
        JSONObject resultObj = doPost(url, params, headerMap);
        return resultObj.getStr(USER_TOKEN_RESPONSE_USER_TOKEN);
    }

    /**
     * 根据userToken获取用户信息
     *
     * @param authToken 请求 header authorization
     * @param userToken 用户token
     * @return 用户信息
     */
    public JSONObject getUserInfoByToken(String authToken, String userToken) {
        long time = System.currentTimeMillis();
        String sign = DigestUtils.md5Hex(zsqConfig.getAppId().concat(zsqConfig.getSecret()).concat(String.valueOf(time)));
        JSONObject params = new JSONObject();
        params.set(PARAMS_APPID, zsqConfig.getAppId());
        params.set(PARAMS_TIME, String.valueOf(time));
        params.set(PARAMS_SIGN, sign);
        params.set(PARAMS_USER_TOKEN, userToken);
        String url = zsqConfig.getBaseUrl() + zsqConfig.getTokenToUserUrl();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(REQUEST_HEADER_AUTHORIZATION, authToken);
        JSONObject resultObj = doPost(url, params, headerMap);
        String userName = resultObj.getStr("userName");
        if (StringUtils.isNotEmpty(userName)) {
            try {
                resultObj.set("userName", decrypt(userName));
            } catch (Exception e) {
                log.error("知社区用户信息userName{{}}解密失败: {}", userName, e.getMessage());
            }
        }
        String mobile = resultObj.getStr("mobile");
        if (StringUtils.isNotEmpty(mobile)) {
            try {
                resultObj.set("mobile", decrypt(mobile));
            } catch (Exception e) {
                log.error("知社区用户信息mobile{{}}解密失败: {}", mobile, e.getMessage());
            }
        }
        String realName = resultObj.getStr("realName");
        if (StringUtils.isNotEmpty(realName)) {
            try {
                resultObj.set("realName", decrypt(realName));
            } catch (Exception e) {
                log.error("知社区用户信息realName{{}}解密失败: {}", realName, e.getMessage());
            }
        }
        return resultObj;
    }

    private JSONObject doPost(String url, JSONObject params, Map<String, String> headerMap) {
        if (headerMap == null || headerMap.isEmpty()) {
            headerMap = lyAuthHeader();
        } else {
            headerMap.putAll(lyAuthHeader());
        }
        try (HttpResponse response = HttpRequest.post(url)
                .body(params.toString())
                .headerMap(headerMap, true)
                .execute()) {
            if (!response.isOk()) {
                throw new BusinessException(ResultCode.FAIL, "知社区平台接口：{" + url + "}请求失败，状态码: " + response.getStatus());
            }
            JSONObject result = JSONUtil.parseObj(response.body());
            int code = result.getInt(RESPONSE_CODE);
            if (code != 0) {
                throw new BusinessException(ResultCode.FAIL, "知社区平台接口：{" + url + "}请求失败，异常 code：{" + code + "}，接口返回：" + JSON.toJSONString(result));
            }
            JSONObject obj = result.getJSONObject(RESPONSE_OBJ);
            if (obj == null) {
                throw new BusinessException(ResultCode.FAIL, "知社区平台接口：{" + url + "}请求失败，接口返回： " + JSON.toJSONString(result));
            }
            return obj;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台接口：{" + url + "}请求异常，异常原因: " + e.getMessage());
        }
    }

    /**
     * 鉴权请求头构建
     *
     * @return 组装好的请求头
     */
    public Map<String, String> lyAuthHeader() {
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String signature = SHA256Util.getSHA256(timestamp + LY_SECRET + nonce + timestamp);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-tif-paasid", LY_APPID);
        headers.put("x-tif-timestamp", timestamp);
        headers.put("x-tif-nonce", nonce);
        headers.put("x-tif-signature", signature);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    /**
     * 通过手机号获取oneCode
     *
     * @param phone 手机号
     * @return 包含username和oneCode的Map
     */
    public Map<String, String> phoneToOneCodeMap(String phone) {
        User user = userService.lambdaQuery().eq(User::getMobile, phone).one();
        if (user == null) {
            throw new BusinessException(ResultCode.FAIL, "用户不存在，请联系管理员添加用户");
        }

        Map<String, String> retMap = new HashMap<>();
        retMap.put("username", user.getUsername());
        retMap.put("oneCode", userService.genOneCode(user.getUsername()));
        return retMap;
    }
}
