package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.dto.OrgShowSumReportExportDto;
import com.fykj.scaffold.zyz.domain.dto.OrgTeamActivityShowSumReportDto;
import com.fykj.scaffold.zyz.domain.dto.TeamShowSumReportExportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShow;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityTimePeriodShowParams;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodShowService;
import constants.Mark;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动-时段表-公示信息
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@RestController
@RequestMapping("/admin/zyz/activity/time/period/show")
@Api(tags = "活动-时段表-公示信息接口")
public class ZyzActivityTimePeriodShowController extends BaseController<IZyzActivityTimePeriodShowService, ZyzActivityTimePeriodShow, ZyzActivityTimePeriodShowParams> {

    @ApiOperation("活动公示分页查询")
    @PostMapping({"/pagesForShow"})
    public JsonResult<IPage<ZyzActivityTimePeriodShow>> pagesForShow(@RequestBody ZyzActivityTimePeriodShowParams params) {
        IPage<ZyzActivityTimePeriodShow> result = this.baseService.pagesForShow(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("活动公示审核分页查询")
    @PostMapping({"/pagesForAudit"})
    public JsonResult<IPage<ZyzActivityTimePeriodShow>> pagesForAudit(@RequestBody ZyzActivityTimePeriodShowParams params) {
        IPage<ZyzActivityTimePeriodShow> result = this.baseService.pagesForAudit(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("活动公示详情")
    @GetMapping("/getDetail")
    public Result getDetail(Long periodId, Long id) {
        ZyzActivityTimePeriodShow result = baseService.getDetail(periodId, id);
        return new JsonResult<>(result);
    }

    @AuditLog("新增/修改保存为草稿")
    @ApiOperation("新增保存为草稿")
    @PostMapping("/saveOrUpdateToDraft")
    public Result saveOrUpdateToDraft(@RequestBody ZyzActivityTimePeriodShow entity) {
        baseService.saveOrUpdateToDraft(entity);
        return OK;
    }

    @AuditLog("公示提交")
    @ApiOperation("公示提交")
    @PostMapping("/render")
    public Result render(@RequestBody ZyzActivityTimePeriodShow entity) {
        baseService.render(entity);
        return OK;
    }

    @AuditLog("公示提交")
    @ApiOperation("公示提交(by id)")
    @GetMapping("/renderById")
    public Result renderById(@RequestParam("id") Long id) {
        baseService.renderById(id);
        return OK;
    }

    @AuditLog("活动公示撤回")
    @GetMapping(value = "/recall")
    @ApiOperation(value = "活动公示撤回")
    public Result recall(long id) {
        if (!baseService.recall(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("活动公示审核")
    @ApiOperation("活动公示审核")
    @GetMapping("/audit")
    public Result audit(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.audit(idList, status, remark);
        return OK;
    }

    @AuditLog("审核通过活动公示上下架")
    @ApiOperation("上下架")
    @GetMapping("/updateAutoStatus")
    public Result updateAutoStatus(@RequestParam String id) {
        Arrays.stream(id.split(Mark.COMMA))
                .map(Long::valueOf)
                .forEach(i -> baseService.updateAutoStatus(i));
        return OK;
    }

    @ApiOperation("团队活动公示数据统计")
    @PostMapping("/getTeamActInfoSum")
    public JsonResult<IPage<OrgTeamActivityShowSumReportDto>> getTeamActInfoSum(@RequestBody OrgDataReportFormParams params) {
        return new JsonResult<>(baseService.getTeamActInfoSum(params));
    }

    @ApiOperation("组织活动公示数据统计")
    @PostMapping("/getOrgActInfoSum")
    public JsonResult<IPage<OrgTeamActivityShowSumReportDto>> getOrgActInfoSum(@RequestBody OrgDataReportFormParams params) {
        return new JsonResult<>(baseService.getOrgActInfoSum(params));
    }

    @ApiOperation("导出组织活动公示数据统计")
    @PostMapping({"/exportOrgDataForm"})
    public void exportOrgDataForm(@RequestBody OrgDataReportFormParams params) {
        params.setPageSize(-1);
        params.setCurrentPage(1);
        List<OrgTeamActivityShowSumReportDto> result = baseService.getOrgActInfoSum(params).getRecords();
        List<OrgShowSumReportExportDto> exportResult = new ArrayList<>();
        result.forEach(it -> {
            OrgShowSumReportExportDto exportDto = new OrgShowSumReportExportDto();
            BeanUtils.copyProperties(it, exportDto);
            exportDto.setActShowUploadAuditProcessingRejectNum(it.getActShowUploadAuditProcessingNum() + "/" + it.getActShowUploadAuditRejectNum());
            exportResult.add(exportDto);
        });
        ExcelUtil.writeExcel(exportResult, "活动公示-按组织统计", OrgShowSumReportExportDto.class);
    }

    @ApiOperation("导出团队活动公示数据统计")
    @PostMapping({"/exportTeamDataForm"})
    public void exportTeamDataForm(@RequestBody OrgDataReportFormParams params) {
        params.setPageSize(-1);
        params.setCurrentPage(1);
        List<OrgTeamActivityShowSumReportDto> result = baseService.getTeamActInfoSum(params).getRecords();
        List<TeamShowSumReportExportDto> exportResult = new ArrayList<>();
        result.forEach(it -> {
            TeamShowSumReportExportDto exportDto = new TeamShowSumReportExportDto();
            BeanUtils.copyProperties(it, exportDto);
            exportDto.setActShowUploadAuditProcessingRejectNum(it.getActShowUploadAuditProcessingNum() + "/" + it.getActShowUploadAuditRejectNum());
            exportResult.add(exportDto);
        });
        ExcelUtil.writeExcel(exportResult, "活动公示-按团队统计", TeamShowSumReportExportDto.class);
    }
}
