package com.fykj.scaffold.zsq_docking.log.controller;


import com.fykj.scaffold.zsq_docking.log.domain.dto.NewsDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.dto.SysOrgDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingSysOrgRecord;
import com.fykj.scaffold.zsq_docking.log.domain.params.ZSQDockingSysOrgRecordParams;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingSysOrgRecordService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

@RestController
@RequestMapping("/admin/zsq_docking_sys_org_record")
@Api(tags = "知社区组织对接记录")
public class ZSQDockingSysOrgRecordController extends BaseController<IZSQDockingSysOrgRecordService, ZSQDockingSysOrgRecord, ZSQDockingSysOrgRecordParams> {

    @ApiOperation("获取全部方法")
    @GetMapping({"/getSysOrgDockingRecords"})
    public JsonResult<List<ZSQDockingSysOrgRecord>> getSysOrgDockingRecords(@RequestParam Long sysOrgId) {
        return new JsonResult<>(this.baseService.getSysOrgDockingRecords(sysOrgId));
    }
    @ApiOperation("失败重试")
    @GetMapping({"/retry"})
    public Result retry(@RequestParam Long sysOrgId, @RequestParam Long recordId) {
        baseService.retry(sysOrgId, recordId);
        return OK;
    }

    @ApiOperation("获取对接记录详情")
    @GetMapping({"/getDockingRecordDetail"})
    public JsonResult<SysOrgDockingRecordDetailDto> getDockingRecordDetail(@RequestParam Long recordId) {
        return new JsonResult<>(baseService.getDockingRecordDetail(recordId));
    }

}
