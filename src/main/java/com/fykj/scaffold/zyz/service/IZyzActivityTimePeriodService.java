package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.zyz.domain.dto.ActivityRecruitNumStatistics;
import com.fykj.scaffold.zyz.domain.dto.MiniActivityTimePeriodDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityTimePeriodParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 活动-时段表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-07-06
 */
public interface IZyzActivityTimePeriodService extends IBaseService<ZyzActivityTimePeriod> {
    /**
     * 新增报名人数,只校验人数，不对报名人员身份验证
     */
    void addApply(long timePeriodId, int num, String type);

    /**
     * 新增志愿招募报名人数,只校验人数，不对报名人员身份验证
     */
    void addVolunteerApply(long timePeriodId, int num);

    /**
     * 新增群众报名人数,只校验人数，不对报名人员身份验证
     */
    void addMassesApply(long timePeriodId, int num);

    /**
     * 保存活动时段
     *
     * @param activityId
     * @param activityTimes
     */
    void saveActivityTimePeriod(Long activityId, List<ZyzActivityTimePeriod> activityTimes);

    /**
     * 根据活动id获取时段列表
     *
     * @param id
     * @return
     */
    List<ZyzActivityTimePeriod> getListByActivityId(Long id);

    /**
     * 核减报名成功人数
     */
    void minusApply(long periodId, int num, String type);

    /**
     * 核减志愿招募报名成功人数
     */
    void minusVolunteerApply(long periodId, int num);

    /**
     * 核减群众报名成功人数
     */
    void minusMassesApply(long periodId, int num);

    /**
     * 获取所有冲突的时段
     *
     * @param myPeriodId 被查时段id
     * @return 冲突时段
     */
    List<ZyzActivityTimePeriod> getConflictTimePeriod(long myPeriodId);

    /**
     * 获取活动时间段分页
     *
     * @param params
     * @return
     */
    IPage<MiniActivityTimePeriodDto> miniTimePeriodPage(ZyzActivityTimePeriodParams params);

    /**
     * 同步
     *
     * @param actTimePeriodId
     */
    void sync(Long actTimePeriodId);


    /**
     * 批量同步
     *
     * @param actTimePeriodIds
     */
    void syncBatch(List<Long> actTimePeriodIds);

    /**
     * 单条同步
     *
     * @param actTimePeriodId
     */
    ZyzSyncLog syncOne(Long actTimePeriodId);

    /**
     * 批量发布招募同步
     *
     * @param actTimePeriodIds
     */
    void syncRecruitBatch(List<Long> actTimePeriodIds);

    /**
     * 单条发布招募同步
     *
     * @param actTimePeriodId
     */
    ZyzSyncLog syncRecruitOne(Long actTimePeriodId);

    /**
     * 获取需要同步的活动时间段数据
     * @return
     */
    List<Long> getNeedSync();

    /**
     * 获取需要SGB同步的活动时间段数据
     * @return
     */
    List<Long> getSgbNeedSync();

    /**
     * 获取需要招募同步的活动时间段数据
     * @return
     */
    List<Long> getNeedRecruitSync();

    /**
     * 获取需要结算的活动时间段数据
     *
     * @return
     */
    List<Long> getNeedSettlementRecords();

    /**
     * 根据活动id获取时段列表
     *
     * @param activityId
     */
    List<ZyzActivityTimePeriod> getTimePeriodByActivityId(Long activityId);

    /**
     * 获取需要同步实践单的活动时段
     * @return
     */
    List<ZyzActivityTimePeriod> getNeedPSSync();

    /**
     * 根据活动ID统计招募人数
     * @param activityId 活动ID
     * @return 招募人数统计结果
     */
    ActivityRecruitNumStatistics getRecruitNumStatistics(Long activityId);
}

