package com.fykj.scaffold.sgb_docking.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.sgb_docking.domain.dto.DeptResponse;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbDept;
import com.fykj.scaffold.sgb_docking.mapper.SgbDeptMapper;
import com.fykj.scaffold.sgb_docking.service.ISgbDeptService;
import com.fykj.scaffold.sgb_docking.util.SgbApiUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 苏州志愿者平台部门单位服务实现
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SgbDeptServiceImpl extends BaseServiceImpl<SgbDeptMapper, SgbDept> implements ISgbDeptService {

    @Override
    public SgbDept getByDeptId(String deptId) {
        return lambdaQuery().eq(SgbDept::getDeptId, deptId).one();
    }

    @Override
    public void syncDept() {
        try {
            // 构造请求参数
            JSONObject params = new JSONObject();
            String jsonParams = params.toJSONString();
            
            // 调用API接口获取部门单位信息
            String response = SgbApiUtil.sendRequest("getSgbPlatformDepts", jsonParams);
            log.info("查询部门单位信息响应: {}", response);
            
            // 解析响应数据
            DeptResponse deptResponse = JSON.parseObject(response, DeptResponse.class);
            if (deptResponse == null || CollectionUtil.isEmpty(deptResponse.getInfo())) {
                log.error("查询部门单位信息响应数据为空");
                throw new BusinessException(ResultCode.SERVICE_FAIL, "查询部门单位信息响应数据为空");
            }
            
            // 转换为实体对象列表
            List<SgbDept> depts = deptResponse.getInfo().stream()
                .filter(it -> "SUCCESS".equals(it.getCode()))
                .map(it -> {
                    SgbDept dept = new SgbDept();
                    dept.setDeptId(it.getDepId());
                    dept.setDeptName(it.getDepName());
                    dept.setRegionCode(it.getRegionCode());
                    return dept;
                })
                .collect(Collectors.toList());
                
            if (CollectionUtil.isEmpty(depts)) {
                log.info("没有部门单位信息需要同步");
                return;
            }
            
            // 先清空数据库中的数据，再保存新数据
            baseMapper.clearAll();
            saveBatch(depts);
            
            log.info("同步部门单位信息成功，共同步{}条数据", depts.size());
        } catch (Exception e) {
            log.error("同步部门单位信息失败", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "同步部门单位信息失败");
        }
    }
} 