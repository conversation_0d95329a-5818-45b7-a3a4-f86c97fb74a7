<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallExchangeMapper">
    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallExchange">
        select v.*,so.name as orgName from mall_exchange v left join   sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.goods_name LIKE CONCAT('%',#{params.keyWord},'%')or
            v.exchange_user_name LIKE CONCAT('%',#{params.keyWord},'%')
            or v.exchange_user_link_phone LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.status != null and params.status != ''">
            and v.status = #{params.status}
        </if>
        <if test="params.emsStatus != null and params.emsStatus != ''">
            and v.ems_status = #{params.emsStatus}
        </if>
        <if test="params.exchangeType != null and params.exchangeType != ''">
            and v.exchange_type = #{params.exchangeType}
        </if>
        <if test="params.orderNum != null and params.orderNum != ''">
            and v.order_num LIKE CONCAT('%',#{params.orderNum},'%')
        </if>
        <if test="params.machineNo != null and params.machineNo != ''">
            and v.machine_no = #{params.machineNo}
        </if>
        <if test="params.stationCode != null and params.stationCode != ''">
            and v.station_code = #{params.stationCode}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and v.org_code = #{params.orgCode}
        </if>
        <if test="params.startTime != null  ">
            AND v.exchange_time <![CDATA[ >= ]]> #{params.startTime}
        </if>
        <if test="params.endTime != null  ">
            AND v.exchange_time  <![CDATA[ <= ]]> #{params.endTime}
        </if>

        order by v.exchange_time desc
    </select>
    <select id="getExchangeList" resultType="com.fykj.scaffold.mall.domain.dto.MallExchangeDto">
        select
               v.exchange_type as exchangeType,
               v.exchange_user_name as exchangeUserName,
               v.exchange_user_link_phone as exchangeUserLinkPhone,
               v.exchange_time as exchangeTime,
               v.goods_point as goodsPoint,
               v.goods_name as goodsName,
               v.exchange_num as exchangeNum,
               v.exchange_after_score as exchangeAfterScore,
               v.status as status,
               v.ems_status as emsStatus,so.name as orgName,
               v.exchange_point
        from mall_exchange v left join sys_org so on v.org_code = so.code
        where v.is_deleted=FALSE
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.goods_name LIKE CONCAT('%',#{params.keyWord},'%')or
            v.exchange_user_name LIKE CONCAT('%',#{params.keyWord},'%')
            or v.exchange_user_link_phone LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.status != null and params.status != ''">
            and v.status = #{params.status}
        </if>
        <if test="params.emsStatus != null and params.emsStatus != ''">
            and v.ems_status = #{params.emsStatus}
        </if>
        <if test="params.exchangeType != null and params.exchangeType != ''">
            and v.exchange_type = #{params.exchangeType}
        </if>
        <if test="params.machineNo != null and params.machineNo != ''">
            and v.machine_no = #{params.machineNo}
        </if>
        <if test="params.stationCode != null and params.stationCode != ''">
            and v.station_code = #{params.stationCode}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            and v.org_code = #{params.orgCode}
        </if>
        <if test="params.orderNum != null and params.orderNum != ''">
            and v.order_num LIKE CONCAT('%',#{params.orderNum},'%')
        </if>
        order by v.exchange_time desc
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.mall.domain.entity.MallExchange">
        select v.*,
               so.name as orgName,
               g.need_apply,
               g.apply_type,
               IFNULL(app.id, elder.id) AS applyId
        from mall_exchange v
            left join   sys_org so on v.org_code = so.code
        join mall_score_goods g on g.id=v.exchange_goods_id AND g.is_deleted=FALSE
        left join mall_score_goods_apply app on app.exchange_id=v.id AND app.is_deleted=FALSE
        left join mall_score_goods_elder_apply elder on elder.exchange_id=v.id AND elder.is_deleted=FALSE
        where v.is_deleted=FALSE
        and v.exchange_volunteer_id=#{params.userId}
        and v.exchange_type !='mall_exchange_type_machine'
        <if test="params.keyWord != null and params.keyWord != ''">
            and (v.goods_name LIKE CONCAT('%',#{params.keyWord},'%')or
            v.exchange_user_name LIKE CONCAT('%',#{params.keyWord},'%')
            or v.exchange_user_link_phone LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        order by v.exchange_time desc
    </select>

    <select id="pointCardReturnTip" resultType="java.util.Map">
        select * from point_card_return_tip where phone = #{mobile} and is_read = false limit 1
    </select>

    <update id="updatePointCardReturnTip">
        update point_card_return_tip set is_read = true,read_time = now() where phone = #{mobile}
    </update>

    <select id="getVolunteerCancelExchangeNeedClearPointRemain" resultType="java.math.BigDecimal">
        SELECT IFNULL(cancel_need_clear_point, 0)
        FROM exchange_cancel_point_info
        WHERE year = #{year} AND volunteer_id = #{volunteerId}
    </select>

    <update id="updateVolunteerNeedClearPoint">
        UPDATE exchange_cancel_point_info
        SET cancel_need_clear_point = #{cancelNeedClearPointNew}
        WHERE year = #{year} AND volunteer_id = #{volunteerId}
    </update>
</mapper>
