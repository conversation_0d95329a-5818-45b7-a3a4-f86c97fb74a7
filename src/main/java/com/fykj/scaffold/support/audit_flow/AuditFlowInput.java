package com.fykj.scaffold.support.audit_flow;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class AuditFlowInput {

    /**
     * 是否为团队发布
     */
    private Boolean teamPublish;

    /**
     * 发布者组织code
     */
    private String publishOrgCode;

    /**
     * 发布者团队id
     */
    private Long publishTeamId;

    /**
     * 当前审核人组织code
     */
    private String currentOrgCode;

    /**
     * 审核是否通过（实体提交时该参数不用传）
     */
    private Boolean pass;

    /**
     * 审核级数（审核流转类型为指定级数审核时必传）
     */
    private Integer auditLevel;

    /**
     * 直到级（审核流转类型为直到型审核时必传，1为直到top组织审核，2，3相应类推）
     */
    private Integer untilLevel;

    /**
     * 指定级（审核流转类型为指定组织审核时必传，1为top组织审核，2，3相应类推）
     */
    private Integer appointLevel;


}
