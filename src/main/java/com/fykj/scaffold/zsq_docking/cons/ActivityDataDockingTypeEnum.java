package com.fykj.scaffold.zsq_docking.cons;

import lombok.Getter;
import org.springframework.util.StringUtils;

@Getter
public enum ActivityDataDockingTypeEnum {

    CREATE("CREATE", "活动新增"),
    UPDATE("UPDATE", "活动修改"),
    DELETE("DELETE", "活动删除"),
    PUBLISH("PUBLISH", "活动发布"),
    CANCEL("CANCEL", "活动取消"),
    UNKNOWN("UNKNOWN", "未知操作");

    private final String code;
    private final String text;

    ActivityDataDockingTypeEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    /**
     * 根据 code 获取枚举实例
     */
    public static ActivityDataDockingTypeEnum fromCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return UNKNOWN;
        }
        for (ActivityDataDockingTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return UNKNOWN;
    }
}

