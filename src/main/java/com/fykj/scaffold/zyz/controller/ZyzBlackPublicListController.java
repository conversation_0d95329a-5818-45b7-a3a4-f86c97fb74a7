package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.BannedVolunteerSubmitDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzBlackPublicList;
import com.fykj.scaffold.zyz.domain.params.ZyzBlackPublicListParams;
import com.fykj.scaffold.zyz.service.IZyzBlackPublicListService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 群众黑名单
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
@RestController
@RequestMapping("/admin/zyz/black/public/list")
@Api(tags = " 群众黑名单接口")
public class ZyzBlackPublicListController extends BaseController<IZyzBlackPublicListService, ZyzBlackPublicList, ZyzBlackPublicListParams> {

    @ApiOperation("后台分页")
    @PostMapping("/pageForList")
    public JsonResult<IPage<ZyzBlackPublicList>> pageForList(@RequestBody ZyzBlackPublicListParams params) {
        return new JsonResult<>(baseService.pageForList(params));
    }

    @GetMapping(value = "/recall")
    @ApiOperation(value = "解禁")
    public Result recall(long id) {
        if (!baseService.recall(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ApiOperation("拉黑")
    @PostMapping("/black")
    public Result render(@RequestBody BannedVolunteerSubmitDto bannedVolunteerSubmitDto) {
        if (!baseService.black(bannedVolunteerSubmitDto)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ApiOperation("保存黑名单对象")
    @PostMapping("/saveOrUpdateBlackList")
    public Result saveOrUpdateBlackList(@RequestBody ZyzBlackPublicList blackList) {
        if (!baseService.saveBlackList(blackList)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }
}
