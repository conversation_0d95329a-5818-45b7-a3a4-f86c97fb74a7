package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityAppraise;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityAppraiseParams;
import com.fykj.scaffold.zyz.domain.params.ZyzApplyParams;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityApplySettleService;
import com.fykj.scaffold.zyz.service.IZyzActivityAppraiseService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 活动报名表-评价内容
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-09
 */
@RestController
@RequestMapping("/admin/zyz/activity/apply")
@Api(tags = "活动--志愿者报名接口")
public class ZyzActivityApplyController extends BaseController<IZyzActivityApplyService, ZyzActivityApply, ZyzApplyParams> {


    @Autowired
    private IZyzActivityApplySettleService applySettleService;

    @Autowired
    private IZyzActivityAppraiseService appraiseService;

    @ApiOperation("报名审核分页查询--只能看自己组织的数据")
    @PostMapping({"/pagesForAudit"})
    public JsonResult<IPage<ZyzActivityApply>> pagesForAudit(@RequestBody(required = false) ZyzApplyParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzApplyParams();
        }
        if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(Oauth2Util.getManagerCapacity())){
            params.setMyTeamId(Oauth2Util.getTeamId());
        }else {
            params.setMyOrgCode(Oauth2Util.getOrgCode());
        }
        IPage<ZyzActivityApply> result = this.baseService.pagesForAudit(params);
        result.getRecords().forEach(it -> it.setEnableSync(!it.getTimePeriodEndTime().isAfter(LocalDateTime.now())
                && ZyzCons.ACTIVITY_APPLY_STATUS_AUDIT_SUCCESS.equals(it.getAuditStatus())
                && (!Cons.PlatformSyncState.SUCCESS.equals(it.getApplyIsSync()) || !Cons.PlatformSyncState.SUCCESS.equals(it.getServiceLongIsSync()))));
        DesensitiseUtil.desensitise(result.getRecords());
        DictTransUtil.trans(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("报名审核分页查询--全部")
    @PostMapping({"/pagesForAuditAll"})
    public JsonResult<IPage<ZyzActivityApply>> pagesForAuditAll(@RequestBody(required = false) ZyzApplyParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzApplyParams();
        }
        IPage<ZyzActivityApply> result = this.baseService.pagesForAudit(params);
        result.getRecords().forEach(it -> it.setEnableSync(!it.getTimePeriodEndTime().isAfter(LocalDateTime.now())
                && ZyzCons.ACTIVITY_APPLY_STATUS_AUDIT_SUCCESS.equals(it.getAuditStatus())
                && (!Cons.PlatformSyncState.SUCCESS.equals(it.getApplyIsSync()) || !Cons.PlatformSyncState.SUCCESS.equals(it.getServiceLongIsSync()))));
        DesensitiseUtil.desensitise(result.getRecords());
        DictTransUtil.trans(result.getRecords());
        return new JsonResult<>(result);
    }

    @AuditLog("报名审核")
    @ApiOperation("报名审核")
    @GetMapping("/audit")
    public Result audit(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.audit(idList, status, remark);
        return OK;
    }

    @ApiOperation("评价分页查询")
    @PostMapping({"/pageForAppraise"})
    public JsonResult<IPage<ZyzActivityAppraise>> pageForAppraise(@RequestBody(required = false) ZyzActivityAppraiseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzActivityAppraiseParams();
        }
        IPage<ZyzActivityAppraise> result = appraiseService.pageForAppraise(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }


    @AuditLog("活动报名-删除")
    @Override
    public Result removeByIds(String ids) {
        return super.removeByIds(ids);
    }

    @ApiOperation("服务时长分页查询")
    @PostMapping({"/pageForServiceLong"})
    public JsonResult<IPage<ZyzActivityApply>> pageForServiceLong(@RequestBody(required = false) ZyzApplyParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzApplyParams();
        }
        IPage<ZyzActivityApply> result = this.baseService.pageForServiceLong(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @AuditLog("修改前端是否展示")
    @ApiOperation("修改前端是否展示")
    @GetMapping("/appraiseStatus")
    public Result appraiseStatus(@RequestParam String ids, @RequestParam Boolean status) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        appraiseService.appraiseStatus(idList, status);
        return OK;
    }

    @ApiOperation("活动时长导出导出")
    @PostMapping({"/exportForServiceLong"})
    public void exportForServiceLong(@RequestBody(required = false) ZyzApplyParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzApplyParams();
        }
        List<ZyzActivityApplyLongExport> res = baseService.listForServiceLong(params);
        ExcelUtil.writeExcel(res, "活动参与时长记录列表", ZyzActivityApplyLongExport.class);
    }

    @ApiOperation("报名审核分页查询导出")
    @PostMapping({"/exportForAudit"})
    public void exportForAudit(@RequestBody(required = false) ZyzApplyParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzApplyParams();
        }
        List<ZyzActivityApplyAuditExport> res = baseService.listForAudit(params);
        ExcelUtil.writeExcel(res, "报名审核记录列表", ZyzActivityApplyAuditExport.class);
    }


    @ApiOperation("查询活动报名日志")
    @GetMapping({"/getApplyByActivityId"})
    public JsonResult<List<ZyzActivityApplyLongDto>> getApplyByActivityId(Long activityId) {
        List<ZyzActivityApplyLongDto> result = this.baseService.getApplyByActivityId(activityId);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }

    @AuditLog("活动报名结算（单条）")
    @ApiOperation("活动报名结算（单条）")
    @GetMapping("/settleOne")
    public Result settleOne(@RequestParam String id) {
        if (StringUtil.isEmpty(id)) {
            throw new BusinessException(ResultCode.FAIL, "请传入需结算的报名id");
        }
        ZyzActivityApply apply = applySettleService.getAndValidateById(Long.valueOf(id));
        applySettleService.settleOne(apply);
        return new Result(ResultCode.OK);

    }

    @AuditLog("活动报名结算（批量）")
    @ApiOperation("活动报名结算（批量）")
    @GetMapping("/settleBatch")
    public Result settleBatch(@RequestParam String ids) {
        if (StringUtil.isEmpty(ids)) {
            throw new BusinessException(ResultCode.FAIL, "请传入需结算的报名id");
        }
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        List<ZyzActivityApply> applies = new ArrayList<>();
        idList.forEach(id -> applies.add(applySettleService.getAndValidateById(id)));
        boolean settleBatchResult = applySettleService.settleBatch(applies);
        if (settleBatchResult) {
            return new Result(ResultCode.OK);
        }
        return new Result(ResultCode.ERROR.code(), "部分数据结算失败！");
    }

    @ApiOperation("志愿者服务时长分页查询")
    @PostMapping({"/pageForVolunteerServiceLong"})
    public JsonResult<IPage<ZyzActivityApply>> pageForVolunteerServiceLong(@RequestBody(required = false) ZyzApplyParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzApplyParams();
        }
        IPage<ZyzActivityApply> result = this.baseService.pageForVolunteerServiceLong(params);
        return new JsonResult<>(result);
    }


    @ApiOperation("资源列表导出")
    @PostMapping({"/exportServiceLong"})
    public void exportServiceLong(@RequestBody(required = false) ZyzApplyParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzApplyParams();
        }
        List<VolunteerServiceLongExportDto> res = baseService.listForVolunteerServiceLong(params);
        ExcelUtil.writeExcel(res, "志愿者服务时长列表", VolunteerServiceLongExportDto.class);
    }

    @ApiOperation("活动申请加入招募同步（单条）")
    @GetMapping("/syncMemberOne")
    public Result syncMemberOne(@RequestParam Long id) {
        if (StringUtil.isEmpty(id)) {
            throw new BusinessException(ResultCode.FAIL, "请传入id");
        }
        ActSyncRecordDto applyRecord = baseService.getApplyRecordById(id);
        if (ObjectUtil.isEmpty(applyRecord)) {
            throw new BusinessException(ResultCode.FAIL, "报名不存在/报名活动不存在/报名志愿者不存在！");
        }
//        baseService.syncMemberOne(applyRecord);
        return new JsonResult<>(baseService.syncMemberOne(applyRecord));
    }

    @ApiOperation("志愿者服务时长同步（单条）")
    @GetMapping("/syncServiceTimeOne")
    public Result syncServiceTimeOne(@RequestParam Long id) {
        if (StringUtil.isEmpty(id)) {
            throw new BusinessException(ResultCode.FAIL, "请传入id");
        }
        ActSyncRecordDto applyRecord = baseService.getApplyRecordById(id);
        if (ObjectUtil.isEmpty(applyRecord)) {
            throw new BusinessException(ResultCode.FAIL, "报名不存在/报名活动不存在/报名志愿者不存在！");
        }
        return new JsonResult<>(baseService.syncServiceTimeOne(applyRecord));
    }
}
