package com.fykj.scaffold.advanced_unit.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文明单位-申请表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("au_standard_unit_apply")
public class AuStandardUnitApply extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @TableField("org_id")
    @ApiModelProperty(value = "单位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;

    /**
     * 测评版本ID
     */
    @TableField("standard_id")
    @ApiModelProperty(value = "测评版本ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardId;

    /**
     * 原始ID
     */
    @TableField("spider_id")
    @ApiModelProperty(value = "原始ID")
    private String spiderId;

}
