package com.fykj.scaffold.sync.controller;

import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;
import com.fykj.scaffold.sync.domain.params.ZyzSyncBelongFieldDictParams;
import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 市平台信息同步日志
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@RestController
@RequestMapping("/admin/zyz/platform/belongFieldDict")
public class ZyzSyncBelongFieldDictController extends BaseController<IZyzSyncBelongFieldDictService, ZyzSyncBelongFieldDict, ZyzSyncBelongFieldDictParams> {

    @ApiOperation("获取所有服务领域")
    @GetMapping("/allBelongField")
    public Result allBelongField() {
        return new JsonResult<>(baseService.list());
    }

    @ApiOperation("根据fatherId获取服务领域")
    @GetMapping("/getByFatherId")
    public Result getByFatherId(String fatherId) {
        return new JsonResult<>(baseService.getByFatherId(fatherId));
    }

    @ApiOperation("根据typeId获取服务领域")
    @GetMapping("/getByTypeId")
    public Result getByTypeId(String typeId) {
        return new JsonResult<>(baseService.getByTypeId(typeId));
    }

    @GetMapping("/cascade")
    @ApiOperation("获取级联树")
    public JsonResult<List<ZyzSyncBelongFieldDict>> cascade() {
        List<ZyzSyncBelongFieldDict> categories = baseService.cascade();
        return new JsonResult<>(categories);
    }

    @ApiOperation("修改时长限制")
    @GetMapping("/updateLimitTime")
    public Result updateLimitTime(Long id ,Integer minTimeLimit,Integer maxTimeLimit){
        boolean result =  baseService.updateLimitTime(id,minTimeLimit,maxTimeLimit);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @GetMapping("/getTopBelongField")
    @ApiOperation("获取一级领域")
    public JsonResult<List<ZyzSyncBelongFieldDict>> getTopBelongField() {
        List<ZyzSyncBelongFieldDict> fields = baseService.lambdaQuery().eq(ZyzSyncBelongFieldDict::getNodePath, "1").orderByAsc(ZyzSyncBelongFieldDict::getTypeId).list();
        return new JsonResult<>(fields);
    }
}
