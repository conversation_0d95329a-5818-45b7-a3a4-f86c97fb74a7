package com.fykj.scaffold.zyz.xxl_job;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fykj.scaffold.capability.domain.entity.Course;
import com.fykj.scaffold.capability.domain.entity.CourseUser;
import com.fykj.scaffold.capability.service.ICourseService;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.SysMsgTmpCons.*;


/**
 * 课程报名提醒
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CourseSignUpRemindTask {

    @Autowired
    private ICourseService courseService;

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @XxlJob("Course_Sign_Up_Remind")
    public void activitySignNoticeSend() {
        XxlJobHelper.log("Course_Sign_Up_Remind-Job, START!");
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime later23hours50Minutes = now.plusHours(23).plusMinutes(50);
        LocalDateTime later24hours = now.plusHours(24);
        LocalDateTime later1hours50Minutes = now.plusHours(1).plusMinutes(50);
        LocalDateTime later2hours = now.plusHours(2);
        List<Course> need24hoursRemindCourses = courseService.getNeedRemindCourses(later23hours50Minutes, later24hours);
        List<Course> need2hoursRemindCourses = courseService.getNeedRemindCourses(later1hours50Minutes, later2hours);
        if (need24hoursRemindCourses.isEmpty()) {
            log.info("没有需要提前1天提醒的活动!");
        } else {
            List<Long> courseIds = need24hoursRemindCourses.stream().map(Course::getId).collect(Collectors.toList());
            Map<Long, String> courseIdNameMap = need24hoursRemindCourses.stream().collect(Collectors.toMap(Course::getId, Course::getCourseName));
            Map<Long, LocalDate> courseIdDateMap = need24hoursRemindCourses.stream().collect(Collectors.toMap(Course::getId, Course::getCourseDate));
            Map<Long, LocalTime> courseIdStartTImeMap = need24hoursRemindCourses.stream().collect(Collectors.toMap(Course::getId, Course::getCourseStartTime));
            List<CourseUser> courseUserList = courseService.getNeedRemindUsers(courseIds);
            if (CollectionUtils.isEmpty(courseUserList)) {
                log.info("没有需要提醒的报名用户!");
            } else {
                courseIdNameMap.forEach((key, value) -> {
                    Set<Long> userIds = courseUserList.stream().filter(it -> key.equals(it.getCourseId())).map(CourseUser::getUserId).collect(Collectors.toSet());
                    sendTmpMsg.sendToPointUser(COURSE_SIGN_UP_REMIND_EARLY_ONE_DAY, new ArrayList<>(userIds), value, courseIdDateMap.get(key), courseIdStartTImeMap.get(key));
                });
            }
        }
        if (need2hoursRemindCourses.isEmpty()) {
            log.info("没有需要提前2小时提醒的活动!");
        } else {
            List<Long> courseIds = need2hoursRemindCourses.stream().map(Course::getId).collect(Collectors.toList());
            Map<Long, String> courseIdNameMap = need2hoursRemindCourses.stream().collect(Collectors.toMap(Course::getId, Course::getCourseName));
            List<CourseUser> courseUserList = courseService.getNeedRemindUsers(courseIds);
            if (CollectionUtils.isEmpty(courseUserList)) {
                log.info("没有需要提醒的报名用户!");
            } else {
                courseIdNameMap.forEach((key, value) -> {
                    Set<Long> userIds = courseUserList.stream().filter(it -> key.equals(it.getCourseId())).map(CourseUser::getUserId).collect(Collectors.toSet());
                    sendTmpMsg.sendToPointUser(COURSE_SIGN_UP_REMIND_EARLY_TWO_HOURS, new ArrayList<>(userIds), value);
                });
            }
        }
        XxlJobHelper.log("Course_Sign_Up_Remind-Job, END!");
    }
}
