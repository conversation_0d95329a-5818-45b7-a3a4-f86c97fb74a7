package com.fykj.scaffold.config.mybatisplus;

import exception.BusinessException;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import result.ResultCode;

/**
 * 全局乐观锁切面
 */
@Aspect
@Component
public class OptimisticLockerAop {

    @AfterReturning(value = "execution(* com.baomidou.mybatisplus.core.mapper.BaseMapper.updateById(..))", returning = "obj")
    public Object afterUpdateById(JoinPoint joinPoint, Object obj) {
        Integer ret = (Integer) obj;
        if (ret == 0) {
            throw new BusinessException(ResultCode.DATA_EXPIRED, "网络拥挤，请稍后再试");
        }
        return ret;
    }
}
