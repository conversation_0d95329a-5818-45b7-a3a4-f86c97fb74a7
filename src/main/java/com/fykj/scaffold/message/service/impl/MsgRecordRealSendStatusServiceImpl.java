package com.fykj.scaffold.message.service.impl;

import com.fykj.scaffold.message.domain.entity.MsgRecordRealSendStatus;
import com.fykj.scaffold.message.mapper.MsgRecordRealSendStatusMapper;
import com.fykj.scaffold.message.service.IMsgRecordRealSendStatusService;
import com.fykj.scaffold.support.utils.MessagesApiUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import result.ResultCode;

import java.util.List;

/**
 * 消息真实发送状态实现类
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@Service
@Slf4j
public class MsgRecordRealSendStatusServiceImpl extends BaseServiceImpl<MsgRecordRealSendStatusMapper, MsgRecordRealSendStatus> implements IMsgRecordRealSendStatusService {

    @Override
    public void syncRealSendStatus() {
        MessagesApiUtil.QueryStatusResult queryStatus = MessagesApiUtil.queryStatus();
        if (MessagesApiUtil.ReturnStatus.FAIL.equals(queryStatus.getReturnStatus())) {
            throw new BusinessException(ResultCode.FAIL, "同步消息发送真实状态失败！失败原因：" + queryStatus.getMessage());
        }
        List<MsgRecordRealSendStatus> statusList = queryStatus.getTask();
        super.saveBatch(statusList);
    }
}

