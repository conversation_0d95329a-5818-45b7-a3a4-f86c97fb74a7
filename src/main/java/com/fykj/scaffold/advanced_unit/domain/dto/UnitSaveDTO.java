package com.fykj.scaffold.advanced_unit.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitHonorLevel;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitNature;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;


@Data
public class UnitSaveDTO {

    @ApiModelProperty(value = "单位名称")
    @NotBlank(message = "单位名称不能为空")
    @ExcelProperty("单位名称")
    private String unitName;

    @ApiModelProperty(value = "统一社会信用代码（18位）")
    @Size(max = 18, message = "统一社会信用代码长度不能超过18位")
    @NotBlank(message = "统一社会信用代码不能为空")
    @ExcelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "联系人")
    @ExcelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系方式")
    @ExcelProperty("联系方式")
    private String contactPhone;

    @ApiModelProperty(value = "详细地址")
    @ExcelProperty("详细地址")
    private String address;

    @ApiModelProperty(value = "经纬度（经度,纬度）")
    @ExcelProperty("经纬度")
    private String coordinates;

    @ApiModelProperty(value = "荣誉等级 字典CODE:" + AdvancedUnitHonorLevel.DICT_CODE)

    private String honorLevel;

    @ApiModelProperty(value = "单位性质（党政机关/事业单位等）字典CODE:" + AdvancedUnitNature.DICT_CODE)
    private String unitNature;

    @ApiModelProperty(value = "单位类型 字典CODE:" + AdvancedUnitType.DICT_CODE)
    private String unitType;

    @ApiModelProperty(value = "主管部门code")
    private String governingDepartment;

    @ApiModelProperty(value = "主管部门名称")
    private String governingDepartmentName;

    @ApiModelProperty(value = "单位简介")
    private String introduction;

    @ApiModelProperty(value = "关联团队")
    private String relatedTeams;

    @ApiModelProperty(value = "单位logo")
    private String unitLogo;

}
