package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.conns.SyncApiCons;
import com.fykj.volunteer.sync.util.domain.sync_base.SyncBase;
import com.fykj.volunteer.sync.util.domain.sync_base.SyncBaseResult;
import com.fykj.volunteer.sync.util.domain.sync_dep.SyncDep;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;

/**
 * 实践阵地同步
 */
@Component
public class BaseSyncer {

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog sync(long orgId, long syncBillId) {
        SysOrg org = orgService.getById(orgId);
        //新闻审核通过，且处于待同步状态需要同步
        if (Cons.PlatformSyncState.SUCCESS.equals(org.getIsSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }

        SyncBase syncBase = new SyncBase();
        syncBase.setName(org.getName());
        syncBase.setUniCode(String.valueOf(org.getId()));
        syncBase.setRegionCode(org.getRegionCode());
        syncBase.setType(String.valueOf(org.getLevel()));
        syncBase.setContact(org.getLinkMan());
        syncBase.setContactNo(org.getLinkMobile());
        if (StringUtil.isNotEmpty(org.getDescription()) && (org.getDescription().length() > 500)) {
            syncBase.setIntro(org.getDescription().substring(0, 499));
        } else {
            syncBase.setIntro(org.getDescription());
        }
        syncBase.setMapAddress(org.getAddress());
        syncBase.setTxMapLat(org.getLatitude().toString());
        syncBase.setTxMapLan(org.getLongitude().toString());
        syncBase.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(org.getLogoUrl())));
        if (StringUtil.isNotEmpty(org.getSyncId())) {
            syncBase.setSaveType(SyncDep.SaveType.MODIFY);
            syncBase.setPbId(org.getSyncId());
        } else {
            syncBase.setSaveType(SyncDep.SaveType.ADD);
        }
        SyncBaseResult result = ApiWebService.syncBase(syncBase);

        org.setSyncTime(LocalDateTime.now());
        org.setSyncRemark(result.getMsg());
        //成功
        if ("同步成功".equals(result.getMsg()) || SyncApiCons.ResultCode.SUCCESS.equals(result.getCode())) {
            org.setIsSync(Cons.PlatformSyncState.SUCCESS);
            org.setSyncId(result.getPbId());
        }
        //其他一律失败
        else {
            org.setIsSync(Cons.PlatformSyncState.FAILURE);
        }
        orgService.updateById(org);

        //记录日志
        return logService.buildSyncLog(Cons.PlatformSyncBiz.BASE_SYNC, result.getCode(), result.getMsg()
                , syncBillId, org.getId(), org.getName()
                , JSONUtil.toJsonStr(syncBase), JSONUtil.toJsonStr(result));
    }
}
