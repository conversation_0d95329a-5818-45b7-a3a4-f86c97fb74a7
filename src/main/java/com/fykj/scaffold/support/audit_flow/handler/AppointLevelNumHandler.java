package com.fykj.scaffold.support.audit_flow.handler;

import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.audit_flow.AuditFlowHandleInstance;
import com.fykj.scaffold.support.audit_flow.AuditFlowInput;
import com.fykj.scaffold.support.audit_flow.AuditFlowOutput;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

/**
 * 指定审核级数审核处理引擎
 */
@Component
public class AppointLevelNumHandler extends AuditFlowHandleInstance {

    @Autowired
    private ISysOrgService orgService;

    @Override
    public AuditFlowOutput handle(AuditFlowInput input) {
        // 提交实体的时候返回审核流转情况
        if (ObjectUtil.isEmpty(input.getPass())) {
            return submitHandle(input);
        }
        // 审核操作的时候返回审核流转情况
        return auditHandle(input);
    }

    private AuditFlowOutput submitHandle(AuditFlowInput input) {
        if (ObjectUtil.isEmpty(input.getTeamPublish())) {
            throw new BusinessException(ResultCode.FAIL, "是否是团队发布未知！");
        }
        AuditFlowOutput output = new AuditFlowOutput();
        // 获取发布者的挂靠或上级组织
        SysOrg parentOrg = getParentOrg(input.getTeamPublish(), input.getPublishTeamId(), input.getPublishOrgCode());
        // 上级组织不存在，则直接审核通过
        if (parentOrg == null) {
            output.setStatus(1);
            return output;
        }
        // 上级或挂靠组织存在时，置为待审核态，并输出首先审核组织code
        output.setStatus(0);
        output.setOrgCode(parentOrg.getCode());
        return output;
    }

    private AuditFlowOutput auditHandle(AuditFlowInput input) {
        Integer auditLevel = input.getAuditLevel();
        String currentAuditOrg = input.getCurrentOrgCode();
        if (ObjectUtil.isEmpty(auditLevel)) {
            throw new BusinessException(ResultCode.FAIL, "审核级数未知！");
        }
        if (auditLevel > 3) {
            throw new BusinessException(ResultCode.FAIL, "审核级数最高为3级！");
        }
        if (StringUtil.isEmpty(currentAuditOrg)) {
            throw new BusinessException(ResultCode.FAIL, "当前审核组织未知！");
        }
        // 审核不通过直接修改状态为不通过
        if (!input.getPass()) {
            AuditFlowOutput output = new AuditFlowOutput();
            output.setStatus(2);
            return output;
        }
        int i = 0;
        boolean isTeam = input.getTeamPublish();
        Long teamId = input.getPublishTeamId();
        String orgCode = input.getPublishOrgCode();
        while (i++ < auditLevel) {
            SysOrg parentOrg = getParentOrg(isTeam, teamId, orgCode);
            // 循环直到父级组织不存在后直接抛出不在审核链上异常
            if (parentOrg == null) {
                break;
            }
            // 当前审核组织不等于循环得到的父级组织，继续循环
            if (!currentAuditOrg.equals(parentOrg.getCode())) {
                isTeam = false;
                orgCode = parentOrg.getCode();
                continue;
            }
            return outputResult(parentOrg, i, auditLevel);
        }
        throw new BusinessException(ResultCode.FAIL, "当前审核组织不在该条记录的审核链上！");
    }

    private AuditFlowOutput outputResult(SysOrg parentOrg, int currentLevel, int auditLevel) {
        AuditFlowOutput output = new AuditFlowOutput();
        // 当前审核组织等于循环得到的父级组织，返回输出
        Long ppId = parentOrg.getParentId();
        // 当前审核组织为顶级组织时直接审核通过
        if (ObjectUtil.isEmpty(ppId)) {
            output.setStatus(1);
            return output;
        }
        // 当前审核组织距离发布人组织的层级刚好等于审核级数直接审核通过
        if (currentLevel == auditLevel) {
            output.setStatus(1);
            return output;
        }
        // 当前审核组织距离发布人组织的层级小于审核级数，输出下一级审核组织code
        SysOrg nextAuditOrg = orgService.getById(ppId);
        if (nextAuditOrg == null) {
            throw new BusinessException(ResultCode.FAIL, "下一级审核组织id有误或下一级审核组织不存在！");
        }
        output.setStatus(0);
        output.setOrgCode(nextAuditOrg.getCode());
        return output;
    }
}
