package com.fykj.scaffold.security.business.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import fykj.microservice.core.support.excel.annotation.BoolConvert;
import fykj.microservice.core.support.excel.annotation.LocalDateTimeConvert;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分协会/社区管理员账号权限
 */
@Data
@NoArgsConstructor
public class SaCaAccountPrivilegesDto {

    /**
     * 账号
     */
    @ExcelProperty("账号")
    @ColumnWidth(30)
    private String username;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    @ColumnWidth(30)
    private String name;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    @ColumnWidth(30)
    private String mobile;

    /**
     * 账号类型
     */
    @ExcelProperty("账号类型")
    @ColumnWidth(30)
    private String role;

    /**
     * 管理组织
     */
    @ExcelProperty("管理组织")
    @ColumnWidth(40)
    private String org;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    @ColumnWidth(15)
    @BoolConvert(trueText = "正常", falseText = "禁用")
    private Boolean status;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @LocalDateTimeConvert
    @ColumnWidth(40)
    private LocalDateTime createDate;
}
