package com.fykj.scaffold.portal_website.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation;
import com.fykj.scaffold.portal_website.domain.params.CivilizationCultivationParams;
import com.fykj.scaffold.portal_website.service.ICivilizationCultivationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;

/**
 * 文明培育接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/portal_website/civilization_cultivation")
@Api(tags = "pc文明培育接口--无需登录")
public class CivilizationCultivationApi {

    @Autowired
    private ICivilizationCultivationService civilizationCultivationService;

    @ApiOperation("分页查询文明培育")
    @PostMapping({"/pages"})
    public JsonResult<IPage<CivilizationCultivation>> pageForNews(@RequestBody CivilizationCultivationParams params) {
        IPage<CivilizationCultivation> result = civilizationCultivationService.pageForApi(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("文明培育详情")
    @GetMapping({"/get"})
    public JsonResult<CivilizationCultivation> get(@RequestParam Long id) {
        CivilizationCultivation result = civilizationCultivationService.getById(id);
        return new JsonResult<>(result);
    }

    @ApiOperation("文明培育获取年份")
    @GetMapping({"/getYears"})
    public JsonResult<List<String>> getYears(@RequestParam String categoryCode) {
        List<String> result = civilizationCultivationService.getYears(categoryCode);
        return new JsonResult<>(result);
    }
}
