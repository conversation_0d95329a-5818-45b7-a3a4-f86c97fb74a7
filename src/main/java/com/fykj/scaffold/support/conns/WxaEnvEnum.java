package com.fykj.scaffold.support.conns;

import lombok.Getter;

/**
 * 2024/5/15
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public enum WxaEnvEnum {
    // 正式版为 "release"，体验版为 "trial"，开发版为 "develop"
    RELEASE("release", "正式版"),
    TRIAL("trial", "体验版"),
    DEVELOP("develop", "开发版");

    private final String code;
    private final String desc;

    WxaEnvEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
