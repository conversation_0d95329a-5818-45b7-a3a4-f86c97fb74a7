package com.fykj.scaffold.advanced_unit.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitCons;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitEvaluateListDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitIndexEvaluateCountDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardIndexUnit;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardIndexUnitParams;
import com.fykj.scaffold.advanced_unit.service.IAuStandardIndexUnitService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import javax.validation.Valid;
import java.util.List;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * 文明单位-申请表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Validated
@RestController
@Api(tags = "管理端-文明单位-评价管理")
@RequestMapping("/admin/manager/advanced-unit/unit/evaluate")
public class AuUnitEvaluateController {

    @Autowired
    private IAuStandardIndexUnitService indexUnitService;

    @ApiOperation("单位评测管理-分页列表")
    @PostMapping({"/page"})
    public JsonResult<IPage<AuUnitEvaluateListDTO>> queryEvaluatePage(
            @RequestBody(required = false) AuStandardIndexUnitParams params) {
        // 判断用户权限
        if(!Oauth2Util.getRoleCodes().contains(AdvancedUnitCons.ADVANCE_UNIT_ADMIN_ROLE_CODE)) {
            params.setBelongUserId((Long) Oauth2Util.getUserId());
        }
        return new JsonResult<>(indexUnitService.queryEvaluatePage(params));
    }

    @ApiOperation("单位评测管理-统计")
    @PostMapping({"/count"})
    public JsonResult<AuUnitIndexEvaluateCountDTO> countIndexEvaluate(
            @RequestBody(required = false) @Valid AuStandardIndexUnitParams params) {
        return new JsonResult<>(indexUnitService.countIndexEvaluate(params));
    }

    @ApiOperation("单位评测管理-指标-分页列表")
    @PostMapping({"/index/page"})
    public JsonResult<IPage<AuStandardIndexUnit>> queryIndexEvaluatePage(
            @RequestBody(required = false) AuStandardIndexUnitParams params) {
        return new JsonResult<>(indexUnitService.queryIndexEvaluatePage(params));
    }

    @ApiOperation("单位评测管理-评测保存")
    @PostMapping({"/save"})
    public Result evaluate(@RequestBody @Valid AuStandardIndexUnit unit) {
        indexUnitService.evaluate(unit);
        return OK;
    }

    @ApiOperation("单位评测管理-评测保存")
    @PostMapping({"/save-batch"})
    public Result evaluateBatch(@RequestBody @Valid List<AuStandardIndexUnit> unit) {
        indexUnitService.evaluate(unit);
        return OK;
    }

}
