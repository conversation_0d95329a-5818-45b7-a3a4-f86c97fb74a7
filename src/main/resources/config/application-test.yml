spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-circular-references: true
  devtools:
    livereload:
      enabled: true
    restart:
      enabled: true
    main:
      allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
#  elasticsearch:
#    connection-timeout: 1
#    socket-timeout: 3000
#    pathPrefix: es
#    uris:
#      - http://zhcs.cnsaas.com
#    username: elastic
#    password: FengYunXmt@2023
  redis:
    host: ************
    password:
    port: 6379
    jedis:
      pool:
        max-idle: 8
        min-idle: 0
        max-maxTotal: 8
        maxWaitMillis: -1
    database: 3
  datasource: #db Configuration
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************
      username: volunteer
      password: <PERSON>YunXmt@2023
      initialSize: 5 # 初始化大小，最小，最大
      minIdle: 5
      maxActive: 50
      maxWait: 10000 # 配置获取连接等待超时的时间
      timeBetweenEvictionRunsMillis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      minEvictableIdleTimeMillis: 300000 # 配置一个连接在池中最小生存的时间，单位是毫秒
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true # 打开PSCache，并且指定每个连接上PSCache的大小
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,wall # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filter:
        stat:
          slow-sql-millis: 5000
          log-slow-sql: true
          merge-sql: true
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: /druid/*,/webjars/*
      stat-view-servlet:
        url-pattern: /druid/*
        enabled: true
        allow: 127.0.0.1
        login-username: root
        login-password: FengYunXmt@2023
#logging:
#  level:
#    org.springframework: DEBUG

server:
  tomcat:
    uri-encoding: UTF-8
  port: 9090
  servlet:
    encoding:
      charset: utf-8
    context-path: /volunteer_api
mybatis-plus:
  type-enums-package: com.fykj.scaffold.project.domain.enums
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
#  mapper-locations: classpath*:/mapper/**/*Mapper.xml
#  #  type-aliases-package: com.logs.entity
#  global-config:
#    db-config:
#      logic-delete-value: 1 #配置逻辑删除字段为1是删除
#      logic-not-delete-value: 0 #配置逻辑删除字段为0是未删除
#      db-type: mysql

#Swagger Configure Properties
fykj:
  swagger:
    enable: true
    packageScan: com.fykj
    title: fykj
    description: API Doc
    version: 2.0
    contact:
      name: FengyunXmt Java Group
      email: <EMAIL>
      url:


virtual:
  path:
    temp: /wordToHtml/

logback:
  file: /logs

referer:
  refererList:
    - http://localhost:9090/
    - http://localhost:8080/
    - http://localhost:8081/
    - http://sungent.fyxmt.com/
    - https://sungent.fyxmt.com/
    - https://servicewechat.com/
    - https://weixin.2500sz.net/
    - http://weixin.2500sz.net/
    - http://demo47.fyxmt.com/
    - http://demo45.fyxmt.com/

system:
  domain: http://1711yz7467.imwork.net

qrcode:
  errorPage: http://1711yz7467.imwork.net/#/404

security:
  oauth2:
    client:
      user-authorization-uri: http://127.0.0.1:9090/volunteer_api/oauth/authorize
      access-token-uri: http://127.0.0.1:9090/volunteer_api/oauth/token
      client-secret: 123456
      client-id: client_2
      grant-type: password
      scope: select
html:
  location: /wordToHtml/

sms:
  configs:
    - code: yqtjpt
      template: 【风云疫情统计平台】您的验证码是%s。如非本人操作，请忽略本短信

xxl:
  enable: false
  job:
    accessToken:
    executor:
      appname: demo
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: -1
      port: 19999
    admin:
      addresses: http://localhost:18080/xxl-job-admin

rocketmq:
  nameServer: ************:9876
  producer:
    group: zyzProducer #自定义的组名称
    send-message-timeout: 3000 #消息发送超时时长
  consumer:
    group: zyzConsumer

wx:
  miniapp:
    configs:
      - appid: wx29145e35261335f9
        secret: 849040eef0bbd4f56f81f38bb0dfe00f
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
  mp:
    configs:
      - appId: wxc4d301cafb380fac # 第一个公众号的appid
        secret: c61de5a5baee53c628b197ee453457ba # 公众号的appsecret
        token: test # 接口配置里的Token值

httpAttachmentUrl: https://sungent.fyxmt.com/volunteer_api

defaultServerCode: LocalServer

#是否自动同步到市平台
auto-sync-platform: false

#是否发送信息
send-message: true

#活动同步是否按时间分段分开同步
act-sync-by-time-period: false

wmyq:
  sso-auth:
    domain: https://wmyq.sipac.gov.cn/wmyq-test

auto-push-zsh: true

zwt:
  corpId: wwec70a5fa19fd89d6
  corpSecret: IWA4clQk6cT-gIwot7OzMB8K3g3kmVskijlRLzh7hOw
  agentId: 1000133
  accessTokenGetUrl: http://************/cgi-bin/gettoken?corpid=%s&corpsecret=%s
  userIdGetUrl: http://************/cgi-bin/user/getuserinfo?access_token=%s&code=%s
  userInfoGetUrl: http://************/cgi-bin/user/get?access_token=%s&userid=%s
  zwtOauth2AuthorizeUrl: http://************/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&agentid=%s#wechat_redirect
  oauth2AuthorizeRedirectUri: http://demo47.fyxmt.com/volunteer_backend/#/common/blank
  validateSecretKey: e6d7a45af67b586e

csr:
  oneCodeFetchUrl: http://demo47.fyxmt.com/csr_api/fykj/volunteerSysUserMobileLogin
  loginUrl: http://demo47.fyxmt.com/csr_api/fykj/login


zsq:
  baseUrl: http://demo45.fyxmt.com/community_api
  authTokenUrl: /fykj/third/token
  userListUrl: /admin/user/getUserListBySocial
  codeToTokenUrl: /admin/sync/getTokenByCode
  tokenToUserUrl: /admin/sync/getUserInfo
  appId: xsdzyz
  secret: Fengyuntec@acD132

# 苏州志愿者平台API配置
sgb:
  api:
    url: http://test.xinsanqiang.net/sgbOpenApitest/v2?
    app-key: 20220531001
    secret-key: jwr56df345d6753226d33445223g8899
    algorithm: HmacSHA256
