package com.fykj.scaffold.advanced_unit.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitAuditStatus;
import com.fykj.scaffold.advanced_unit.domain.entity.AuUnit;
import com.fykj.scaffold.advanced_unit.domain.entity.AuUnitHonor;
import com.fykj.scaffold.advanced_unit.domain.params.AuUnitHonorParams;
import com.fykj.scaffold.advanced_unit.domain.params.AuUnitParams;
import com.fykj.scaffold.advanced_unit.domain.params.UnitActivityParams;
import com.fykj.scaffold.advanced_unit.service.IAuUnitHonorService;
import com.fykj.scaffold.advanced_unit.service.IAuUnitService;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.zyz.domain.dto.ActivityApiDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import javax.validation.Valid;
import java.util.List;

/**
 * 文明单位-申请表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Validated
@RestController
@Api(tags = "用户端-文明单位-单位信息")
@RequestMapping("/api/advanced-unit/unit")
public class UnitApi {

    @Autowired
    private IAuUnitService baseService;

    @Autowired
    private IAuUnitHonorService honorService;

    @ApiOperation("分页查询")
    @PostMapping({"/page"})
    public JsonResult<IPage<AuUnit>> list(@RequestBody AuUnitParams params) {
        params.setAuditStatus(AdvancedUnitAuditStatus.PASS.getCode());
        params.setShowStatus(true);
        IPage<AuUnit> result = baseService.queryPage(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("获取全部方法")
    @GetMapping({"/list"})
    public JsonResult<List<AuUnit>> all(@RequestBody AuUnitParams params) {
        params.setAuditStatus(AdvancedUnitAuditStatus.PASS.getCode());
        params.setShowStatus(true);
        return new JsonResult<>(baseService.queryList(params));
    }

    @ApiOperation("根据id获取")
    @GetMapping("/detail")
    public JsonResult<AuUnit> get(@RequestParam Long id) {
        return new JsonResult<>(baseService.queryDetail(id));
    }

    @ApiOperation("荣誉-分页查询")
    @PostMapping("/honor/page")
    public JsonResult<IPage<AuUnitHonor>> honorPage(
            @RequestBody @Valid AuUnitHonorParams params) {
        return new JsonResult<>(honorService.page(params));
    }

    @ApiOperation("展开活动(文明实践(举办活动))-分页查询")
    @PostMapping("/activity/page")
    public JsonResult<IPage<ActivityApiDto>> activityPage(
            @RequestBody @Valid UnitActivityParams params) {
        return new JsonResult<>(baseService.queryActivityPage(params));
    }

    @ApiOperation("单位风采(团队新闻)-分页查询")
    @PostMapping("/news/page")
    public JsonResult<IPage<News>> newsPage(
            @RequestBody @Valid UnitActivityParams params) {
        return new JsonResult<>(baseService.queryNewsPage(params));
    }

    @ApiOperation("文明单位列表-首页轮播展示")
    @PostMapping({"/homeBannerShow"})
    public Result homeBannerShow(@RequestBody AuUnitParams params) {
        params.setAuditStatus(AdvancedUnitAuditStatus.PASS.getCode());
        params.setShowStatus(true);
        return new JsonResult<>(baseService.homeBannerShow(params));
    }
}
