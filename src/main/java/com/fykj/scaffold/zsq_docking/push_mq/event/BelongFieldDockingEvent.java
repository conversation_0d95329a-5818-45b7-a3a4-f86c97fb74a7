package com.fykj.scaffold.zsq_docking.push_mq.event;


import com.fykj.scaffold.zsq_docking.push_mq.dto.ActZyzSyncBelongFieldDto;
import com.fykj.scaffold.zsq_docking.push_mq.dto.RequirementSyncForZyzDto;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BelongFieldDockingEvent  extends BaseDockingMqEvent {

    /**
     * 对接记录 id
     */
    private Long dockingRecordId;


    /**
     * 消息 id
     */
    private String operation;

    /**
     * 消息 id
     */
    private String zwtUserId;


    /**
     * 所属领域列表信息
     */
    private List<ActZyzSyncBelongFieldDto> belongFieldList;

    /**
     * 志愿者平台用户 id
     */
    private Serializable zyzUserId;

}
