package com.fykj.scaffold.sync.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto;
import com.fykj.scaffold.sync.domain.entity.PracticeSheet;
import com.fykj.scaffold.sync.domain.params.PracticeSheetParams;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogStatisticsParams;
import com.fykj.scaffold.sync.mapper.PracticeSheetMapper;
import com.fykj.scaffold.sync.service.IPracticeSheetService;
import com.fykj.scaffold.sync.service.IPlatformSyncService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import result.ResultCode;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 实践单服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Service
@Slf4j
public class PracticeSheetServiceImpl extends BaseServiceImpl<PracticeSheetMapper, PracticeSheet> implements IPracticeSheetService {

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private IPlatformSyncService syncService;

    @Override
    public IPage<PracticeSheet> page(BaseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new PracticeSheetParams();
        }
        return super.page(params).convert(DictTransUtil::trans);
    }

    @Override
    public void syncBatch(List<Long> orderIds, String type) {
        if (CollectionUtil.isEmpty(orderIds)) {
            return;
        }
        orderIds.forEach(it -> {
            try {
                syncOne(it, type);
            }catch (Exception e) {
                log.error("id为[{}],类型为{}的实践单同步异常！异常原因：{}", it, type, e.getMessage(), e);
            }
        });
    }

    @Override
    public void syncOne(Long orderId, String type) {
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NonNull TransactionStatus transactionStatus) {
                try {
                    syncService.syncOrder(orderId, IdUtil.getSnowflake(1, 1).nextId(), type);
                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();
//                    log.error("id为[{}],类型为{}的实践单同步异常！异常原因：{}", orderId, type, e.getMessage(), e);
                    throw new BusinessException(ResultCode.FAIL, "id为【" + orderId + "】，类型为" + type + "的实践单同步异常！异常原因：" + e.getMessage(), e);
                }
            }
        });
    }

    @Override
    public IPage<SyncStatisticsDto> statisticsPage(ZyzSyncLogStatisticsParams params) {
        return baseMapper.statisticsPage(params.getPage(), params);
    }

    @Override
    public SyncStatisticsDto getByYearMon(String yearMon) {
        return baseMapper.getByYearMon(yearMon);
    }

    @Override
    public List<SyncFailStatisticsDto> statisticsFailList(ZyzSyncLogStatisticsParams params) {
        return baseMapper.statisticsFailList(params);
    }

    @Override
    public List<ZyzSyncLogDto> exportStatistics(ZyzSyncLogStatisticsParams params) {
        return baseMapper.exportStatistics(params);
    }
}
