package com.fykj.scaffold.security.login.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：yangxu
 * @Date：2025/1/9 16:55
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PasswordForgetValidateDto implements Serializable {

    private static final long serialVersionUID = -8344496492976657105L;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("验证码")
    private String smsVerificationCode;
}
