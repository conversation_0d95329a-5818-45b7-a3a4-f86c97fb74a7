package com.fykj.scaffold.sgb_docking.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.sgb_docking.domain.dto.RegionResponse;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbRegion;
import com.fykj.scaffold.sgb_docking.mapper.SgbRegionMapper;
import com.fykj.scaffold.sgb_docking.service.ISgbRegionService;
import com.fykj.scaffold.sgb_docking.util.SgbApiUtil;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 苏州志愿者平台区划信息服务实现
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SgbRegionServiceImpl extends BaseServiceImpl<SgbRegionMapper, SgbRegion> implements ISgbRegionService {

    @Override
    public SgbRegion getByRegionCode(String regionCode) {
        return lambdaQuery().eq(SgbRegion::getRegionCode, regionCode).one();
    }

    @Override
    public void syncRegion() {
        try {
            // 构造请求参数
            JSONObject root = new JSONObject();
            JSONArray infoArray = new JSONArray();
            JSONObject infoItem = new JSONObject();
            // 只要工业园区数据
            infoItem.put("parentCode", "320508");
            infoArray.add(infoItem);
            root.put("info", infoArray);
            String params = root.toJSONString();
            
            // 调用API接口获取区划信息
            String response = SgbApiUtil.sendRequest("getSgbRegion", params);
            log.info("查询区划信息响应: {}", response);
            
            // 解析响应数据
            RegionResponse regionResponse = JSON.parseObject(response, RegionResponse.class);
            if (regionResponse == null || CollectionUtil.isEmpty(regionResponse.getInfo())) {
                log.error("查询区划信息响应数据为空");
                throw new BusinessException(ResultCode.SERVICE_FAIL, "查询区划信息响应数据为空");
            }
            
            // 转换为实体对象列表
            List<SgbRegion> regions = regionResponse.getInfo().stream()
                .filter(it -> "SUCCESS".equals(it.getCode()))
                .map(it -> {
                    SgbRegion region = new SgbRegion();
                    region.setRegionCode(it.getRegionCode());
                    region.setRegionName(it.getRegionName());
                    region.setParentCode(it.getParentCode());
                    return region;
                })
                .collect(Collectors.toList());
                
            if (CollectionUtil.isEmpty(regions)) {
                log.info("没有区划信息需要同步");
                return;
            }
            
            // 先清空数据库中的数据，再保存新数据
            baseMapper.clearAll();
            saveBatch(regions);
            
            log.info("同步区划信息成功，共同步{}条数据", regions.size());
        } catch (Exception e) {
            log.error("同步区划信息失败", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "同步区划信息失败");
        }
    }

    @Override
    public List<SgbRegion> getRegionTree() {
        // 获取所有区划数据
        List<SgbRegion> allRegions = list();
        if (CollectionUtil.isEmpty(allRegions)) {
            return new ArrayList<>();
        }

        // 找出根节点（parentCode为null或空字符串的节点）
        List<SgbRegion> rootNodes = allRegions.stream()
                // 只要工业园区的数据
                .filter(region -> "320508".equals(region.getRegionCode()))
                .collect(Collectors.toList());

        // 构建父子关系映射
        Map<String, List<SgbRegion>> parentChildrenMap = allRegions.stream()
                .filter(region -> StringUtil.isNotEmpty(region.getParentCode()))
                .collect(Collectors.groupingBy(SgbRegion::getParentCode));

        // 为每个根节点构建子树
        rootNodes.forEach(rootNode -> buildChildrenNode(rootNode, parentChildrenMap));

        return rootNodes;
    }

    @Override
    public List<CascaderExtraDto> getRegionCascaderTree() {
        // 复用现有的树结构构建逻辑
        List<SgbRegion> regionTree = getRegionTree();

        // 转换为 CascaderExtraDto 格式
        return regionTree.stream()
                .map(this::convertToCascaderDto)
                .collect(Collectors.toList());
    }

    /**
     * 将 SgbRegion 转换为 CascaderExtraDto
     * @param region 区划实体
     * @return 级联选择DTO
     */
    private CascaderExtraDto convertToCascaderDto(SgbRegion region) {
        CascaderExtraDto cascaderDto = new CascaderExtraDto();

        // 设置基本属性
        cascaderDto.setValue(region.getId());
        cascaderDto.setLabel(region.getRegionName());
        cascaderDto.setDisabled(false);

        // 递归转换子节点
        if (CollectionUtil.isNotEmpty(region.getChildren())) {
            List<CascaderExtraDto> childrenDto = region.getChildren().stream()
                    .map(this::convertToCascaderDto)
                    .collect(Collectors.toList());
            cascaderDto.setChildren(childrenDto);
        }

        return cascaderDto;
    }

    /**
     * 递归构建子节点
     * @param parentNode 父节点
     * @param parentChildrenMap 父子关系映射
     */
    private void buildChildrenNode(SgbRegion parentNode, Map<String, List<SgbRegion>> parentChildrenMap) {
        String parentRegionCode = parentNode.getRegionCode();
        List<SgbRegion> children = parentChildrenMap.get(parentRegionCode);
        parentNode.setChildren(children);
        if (CollectionUtil.isNotEmpty(children)) {
            children.forEach(child -> buildChildrenNode(child, parentChildrenMap));
        }
    }
} 