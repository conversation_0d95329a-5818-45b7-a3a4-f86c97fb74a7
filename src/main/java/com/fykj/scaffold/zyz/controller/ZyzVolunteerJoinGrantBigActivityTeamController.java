package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.dto.ZyzVolunteerJoinGrantBigActivityTeamDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerJoinGrantBigActivityTeam;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerJoinGrantBigActivityTeamParams;
import com.fykj.scaffold.zyz.service.IZyzVolunteerJoinGrantBigActivityTeamService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 志愿者加入授权大型活动发布的团队记录-接口控制器
 *
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/admin/zyz/volunteer/joinGrantBigActivityTeam")
@Api(tags = "志愿者加入授权大型活动发布的团队记录-管理接口")
public class ZyzVolunteerJoinGrantBigActivityTeamController extends BaseController<IZyzVolunteerJoinGrantBigActivityTeamService, ZyzVolunteerJoinGrantBigActivityTeam, ZyzVolunteerJoinGrantBigActivityTeamParams> {

    @ApiOperation("导出")
    @PostMapping({"/export"})
    public void export(@RequestBody(required = false) ZyzVolunteerJoinGrantBigActivityTeamParams params) {
        List<ZyzVolunteerJoinGrantBigActivityTeam> res = baseService.list(params);
        List<ZyzVolunteerJoinGrantBigActivityTeamDto> exportList = res.stream()
                .map(it -> new ZyzVolunteerJoinGrantBigActivityTeamDto(it.getVolunteerName(), it.getVolunteerPhone(), it.getTeamName(), it.getJoinTime())) // 假设 Obj2 有 Obj2(Obj1 obj1) 构造方法
                .collect(Collectors.toList());
        ExcelUtil.writeExcel(exportList, "志愿者加入大型赛事授权团队记录", ZyzVolunteerJoinGrantBigActivityTeamDto.class);
    }
}
