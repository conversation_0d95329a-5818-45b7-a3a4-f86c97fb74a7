package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.dto.ActivityScheduleReportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivitySchedule;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleParams;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleService;
import constants.Mark;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阵地计划
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@RestController
@RequestMapping("/admin/zyz/activity/schedule")
@Api(tags = "阵地计划接口")
public class ZyzActivityScheduleController extends BaseController<IZyzActivityScheduleService, ZyzActivitySchedule, ZyzActivityScheduleParams> {
    @ApiOperation("阵地计划分页查询")
    @PostMapping({"/pagesForSchedule"})
    public JsonResult<IPage<ZyzActivitySchedule>> pagesForSchedule(@RequestBody ZyzActivityScheduleParams params) {
        IPage<ZyzActivitySchedule> result = this.baseService.pagesForSchedule(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("阵地计划审核分页查询")
    @PostMapping({"/pagesForAudit"})
    public JsonResult<IPage<ZyzActivitySchedule>> pagesForAudit(@RequestBody ZyzActivityScheduleParams params) {
        IPage<ZyzActivitySchedule> result = this.baseService.pagesForAudit(params);
        return new JsonResult<>(result);
    }


    @AuditLog("新增/修改保存为草稿")
    @ApiOperation("新增保存为草稿")
    @PostMapping("/saveOrUpdateToDraft")
    public Result saveOrUpdateToDraft(@RequestBody ZyzActivitySchedule entity) {
        baseService.saveOrUpdateToDraft(entity);
        return OK;
    }

    @ApiOperation("阵地计划详情")
    @GetMapping("/getDetail")
    public Result getDetail(Long scheduleId) {
        ZyzActivitySchedule result = baseService.getDetail(scheduleId);
        return new JsonResult<>(result);
    }


    @AuditLog("计划提交")
    @ApiOperation("计划提交(by id)")
    @GetMapping("/renderById")
    public Result renderById(@RequestParam("id") Long id) {
        baseService.renderById(id);
        return OK;
    }

    @AuditLog("计划提交")
    @ApiOperation("计划提交")
    @PostMapping("/render")
    public Result render(@RequestBody ZyzActivitySchedule entity) {
        baseService.render(entity);
        return OK;
    }

    @AuditLog("阵地计划撤回")
    @GetMapping(value = "/recall")
    @ApiOperation(value = "阵地计划撤回")
    public Result recall(long id) {
        if (!baseService.recall(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("审核通过阵地计划上下架")
    @ApiOperation("上下架")
    @GetMapping("/updateAutoStatus")
    public Result updateAutoStatus(Long id) {
        if (!baseService.updateAutoStatus(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("阵地计划审核")
    @ApiOperation("阵地计划审核")
    @GetMapping("/audit")
    public Result audit(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.audit(idList, status, remark);
        return OK;
    }

    @ApiOperation("阵地计划报表分页查询")
    @PostMapping({"/pagesForReport"})
    public JsonResult<IPage<ActivityScheduleReportDto>> pagesForReport(@RequestBody ZyzActivityScheduleParams params) {
        if (StringUtil.isEmpty(params.getScheduleTypeData())) {
            params.setScheduleTypeData(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM"));
        }
        IPage<ActivityScheduleReportDto> result = this.baseService.pagesForReport(params);
        return new JsonResult<>(result);
    }

    @ApiOperation("阵地计划导出")
    @PostMapping({"/exportScheduleReport"})
    public void exportScheduleReport(@RequestBody(required = false) ZyzActivityScheduleParams params) {
        if (StringUtil.isEmpty(params.getScheduleTypeData())) {
            params.setScheduleTypeData(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM"));
        }
        params.setPageSize(-1);
        params.setCurrentPage(1);
        List<ActivityScheduleReportDto> result = baseService.pagesForReport(params).getRecords();
        DictTransUtil.trans(result);
        ExcelUtil.writeExcel(result, "计划报表", ActivityScheduleReportDto.class);
    }
    
    @ApiOperation("获取阵地计划三级级联数据")
    @GetMapping("/getCascaderData")
    public JsonResult<List<CascaderExtraDto>> getCascaderData() {
        List<CascaderExtraDto> result = baseService.getScheduleCascaderData();
        return new JsonResult<>(result);
    }
}
