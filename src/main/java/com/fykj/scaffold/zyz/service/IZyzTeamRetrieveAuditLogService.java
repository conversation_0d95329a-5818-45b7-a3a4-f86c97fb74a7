package com.fykj.scaffold.zyz.service;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieveAuditLog;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 团队找回审核操作记录表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-08-108
 */
public interface IZyzTeamRetrieveAuditLogService extends IBaseService<ZyzTeamRetrieveAuditLog> {

    /**
     * 根据团队id获取日志
     * @return
     */
    List<ZyzTeamRetrieveAuditLog> getLogsByTeamId(Long teamId);
}

