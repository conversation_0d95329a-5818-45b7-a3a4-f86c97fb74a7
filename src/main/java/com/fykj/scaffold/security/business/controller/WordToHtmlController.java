package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.service.impl.WordToHtmlServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 富文本框Word转Html
 *
 * <AUTHOR>
 */
@ApiIgnore
@RestController
@RequestMapping("/admin/editor")
@Api(tags = "CMS-富文本框Word转Html")
public class WordToHtmlController {

    @Autowired
    private WordToHtmlServiceImpl service;

    @ApiOperation("Word转Html")
    @PostMapping(value = "/wordToHtml")
    public JsonResult<String> wordToHtml(MultipartFile file) {
        String filePath = service.writeFileToTargetPath(file);
        return new JsonResult<>(service.getWordToTransfer(filePath));
    }
}
