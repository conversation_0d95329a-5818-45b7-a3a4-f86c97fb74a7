package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceDockingDto extends BaseDockingDto {

    private static final long serialVersionUID = -8019862830334976337L;

    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long resourceId;

    /**
     * 上下架状态
     */
    @ApiModelProperty(value = "上下架状态")
    private Boolean onShelf;
}
