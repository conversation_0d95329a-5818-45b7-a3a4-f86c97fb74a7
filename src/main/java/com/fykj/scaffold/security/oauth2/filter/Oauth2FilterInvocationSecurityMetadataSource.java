package com.fykj.scaffold.security.oauth2.filter;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto;
import com.fykj.scaffold.security.business.domain.entity.SysApi;
import com.fykj.scaffold.security.business.service.ISysApiService;
import constants.Mark;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class Oauth2FilterInvocationSecurityMetadataSource implements FilterInvocationSecurityMetadataSource, InitializingBean {

    @Autowired
    private ISysApiService apiService;

    private final static Map<String, Collection<ConfigAttribute>> RESOURCEMAP = new HashMap<>();

    @PostConstruct
    public void init() {
        RESOURCEMAP.clear();
        List<SysApi> protectedApiList = apiService.lambdaQuery().eq(SysApi::getStatus, true).list();
        List<ProtectedApiOAuthDto> roleApiPathList = apiService.getRoleApiPathList();
        Map<String, List<ProtectedApiOAuthDto>> roleApiMap = roleApiPathList.stream().collect(Collectors.groupingBy(ProtectedApiOAuthDto::getPath));
        protectedApiList.forEach(api -> {
            List<ProtectedApiOAuthDto> roleList = roleApiMap.get(api.getApiPath());
            if (CollectionUtil.isEmpty(roleList)) {
                //如果这个接口需要保护，但是没有任何角色拥有此权限，那也必须要放一个，不然springsecurity直接不管这个接口
                RESOURCEMAP.put(api.getApiPath(), CollectionUtil.newArrayList(new SecurityConfig("nobodyHasRole")));
            } else {
                Set<SecurityConfig> roleIds = roleList.stream().map(it -> new SecurityConfig(String.valueOf(it.getRoleId()))).collect(Collectors.toSet());
                RESOURCEMAP.put(api.getApiPath(), new ArrayList<>(roleIds));
            }
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    public Collection<ConfigAttribute> getAttributes(Object object) throws IllegalArgumentException {
        String requestUrl = ((FilterInvocation) object).getRequestUrl();
        if (requestUrl.contains(Mark.QUESTION)) {
            requestUrl = requestUrl.substring(0, requestUrl.indexOf(Mark.QUESTION));
        }
        return RESOURCEMAP.get(requestUrl);
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return true;
    }
}
