package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.entity.MallSelfMachine;
import com.fykj.scaffold.mall.domain.params.MallSelfMachineParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
public interface MallSelfMachineMapper extends BaseMapper<MallSelfMachine> {
    /**
     * 分页查询--后端页面，有权限限制
     * @return
     */
    @DataFilter
    IPage<MallSelfMachine> getPage(IPage<MallSelfMachine> page, @Param("params") MallSelfMachineParams params);

    IPage<MallSelfMachine> pageForApi(IPage<MallSelfMachine> page, @Param("params") MallSelfMachineParams params);

    /**
     * @return
     */
    @DataFilter
    List<MallSelfMachine> getMachinesByCurrOrg();
}

