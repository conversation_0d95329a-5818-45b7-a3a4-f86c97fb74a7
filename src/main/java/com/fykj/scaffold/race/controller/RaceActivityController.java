package com.fykj.scaffold.race.controller;

import com.fykj.scaffold.race.domain.dto.WinPhotoDto;
import com.fykj.scaffold.race.domain.entity.Activity;
import com.fykj.scaffold.race.domain.params.ActivityParams;
import com.fykj.scaffold.race.service.IRaceActivityService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 摄影大赛-活动表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@RestController
@RequestMapping("/admin/race/activity")
public class RaceActivityController extends BaseController<IRaceActivityService, Activity, ActivityParams> {

    /**
     * 设置启用/禁用
     *
     * @param id id
     */
    @ApiOperation(value = "设置启用/禁用")
    @GetMapping(value = "/setEnable")
    public Result setEnable(@RequestParam Long id) {
        if (baseService.setEnable(id)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }

    /**
     * 设置启用/禁用
     *
     * @param id id
     */
    @ApiOperation(value = "设置启用/禁用")
    @GetMapping(value = "/setShow")
    public Result setShow(@RequestParam Long id) {
        if (baseService.setShow(id)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }

    /**
     * 设置启用/禁用
     *
     * @param id id
     */
    @ApiOperation(value = "设置启用/禁用")
    @GetMapping(value = "/setUpload")
    public Result setUpload(@RequestParam Long id) {
        if (baseService.setUpload(id)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }


    @ApiOperation(value = "获取启用活动--网页端")
    @GetMapping(value = "/getActivityByEnable")
    public JsonResult<Activity> getActivityByEnable() {
        Activity res = baseService.getActivityByEnable();
        return new JsonResult<>(res);
    }

    @ApiOperation(value = "获取可展示活动列表--网页端")
    @GetMapping(value = "/getActivityByShow")
    public JsonResult<List<Activity>> getActivityByShow() {
        List<Activity> res = baseService.getActivityByShow();
        return new JsonResult<>(res);
    }

    @ApiOperation(value = "获取活动获奖作品--网页端")
    @GetMapping(value = "/getActivityWinPhoto")
    public JsonResult<List<WinPhotoDto>> getActivityWinPhoto(@RequestParam Long activityId) {
        List<WinPhotoDto> res = baseService.getActivityWinPhoto(activityId);
        return new JsonResult<>(res);
    }
}
