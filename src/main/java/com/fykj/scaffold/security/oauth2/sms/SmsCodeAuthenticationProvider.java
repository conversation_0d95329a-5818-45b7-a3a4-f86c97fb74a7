package com.fykj.scaffold.security.oauth2.sms;

import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.security.oauth2.access.MyUserDetailsServiceImpl;
import com.fykj.scaffold.support.conns.Cons;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

@Component
public class SmsCodeAuthenticationProvider implements AuthenticationProvider {

    private MyUserDetailsServiceImpl myUserDetailsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    @Lazy
    private IUserService userService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        SmsCodeAuthenticationToken authenticationToken = (SmsCodeAuthenticationToken) authentication;
        String mobile = (String) authenticationToken.getPrincipal();
        User user = userService.lambdaQuery().eq(User::getMobile, mobile).one();
        UserDetails userDetails = myUserDetailsService.loadUserByUsername(user.getUsername());
        final String redisLoginSmsCodeKey = mobile + Cons.SmsValidCodeTemplate.LOGIN.name();
        String realCode = redisService.get(redisLoginSmsCodeKey);
        if (StringUtil.isEmpty(realCode) || !realCode.equals(authenticationToken.getCode())) {
            throw new InvalidGrantException("验证码错误,请重新输入");
        } else {
            redisService.remove(redisLoginSmsCodeKey);
        }
        SmsCodeAuthenticationToken authenticationResult = new SmsCodeAuthenticationToken(userDetails, userDetails.getAuthorities());
        authenticationResult.setDetails(authenticationToken.getDetails());
        return authenticationResult;
    }

    public void setUserServiceDetail(MyUserDetailsServiceImpl userServiceDetail) {
        this.myUserDetailsService = userServiceDetail;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return SmsCodeAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
