package com.fykj.scaffold.portal_website.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto;
import com.fykj.scaffold.cms.domain.entity.CmsCategory;
import com.fykj.scaffold.cms.domain.entity.CmsCategoryContent;
import com.fykj.scaffold.cms.service.ICmsCategoryContentService;
import com.fykj.scaffold.cms.service.ICmsCategoryService;
import com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation;
import com.fykj.scaffold.portal_website.domain.params.CivilizationCultivationParams;
import com.fykj.scaffold.portal_website.mapper.CivilizationCultivationMapper;
import com.fykj.scaffold.portal_website.service.ICivilizationCultivationService;
import com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationCultivateDto;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.fykj.scaffold.portal_website.conns.PortalWebsiteCons.CIVILIZATION_CULTIVATION_CMS_CODE;

/**
 * <p>
 * 风采服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class CivilizationCultivationServiceImpl extends BaseServiceImpl<CivilizationCultivationMapper, CivilizationCultivation> implements ICivilizationCultivationService {

    @Autowired
    private ICmsCategoryService categoryService;

    @Autowired
    private ICmsCategoryContentService categoryContentService;

    @Override
    public boolean save(CivilizationCultivation entity) {
        entity.setType("CIVILIZATION_CULTIVATION");
        entity.setEffectiveDate(LocalDateTime.of(LocalDate.parse(entity.getPublishDate()), LocalTime.MIN));
        super.save(entity);
        saveCategoryContent(entity);
        return true;
    }

    @Override
    public boolean updateById(CivilizationCultivation entity) {
        categoryContentService.removeByContentId(entity.getId(), false);
        saveCategoryContent(entity);
        entity.setEffectiveDate(LocalDateTime.of(LocalDate.parse(entity.getPublishDate()), LocalTime.MIN));
        super.updateById(entity);
        return true;
    }

    private void saveCategoryContent(CivilizationCultivation entity) {
        CmsCategory category = categoryService.getByCode(CIVILIZATION_CULTIVATION_CMS_CODE);
        String categoryId = entity.getCategoryId();
        CmsCategoryContent categoryContent = new CmsCategoryContent();
        categoryContent.setCategoryId(Long.valueOf(categoryId));
        categoryContent.setCategoryCode(categoryService.getById(categoryContent.getCategoryId()).getCode());
        categoryContent.setParentId(category.getId());
        categoryContent.setCategoryList(String.valueOf(category.getId()).concat(Mark.COMMA).concat(categoryId));
        categoryContent.setContentId(entity.getId());
        categoryContent.setSequence(ObjectUtil.isEmpty(entity.getSequence()) ? 999 : entity.getSequence());
        categoryContentService.save(categoryContent);
    }

    @Override
    public CivilizationCultivation getById(Serializable id) {
        CivilizationCultivation civilizationCultivation = super.getById(id);
        List<CmsCategoryContent> categoryContents = categoryContentService.getByContentId(id, false);
        if (CollectionUtil.isEmpty(categoryContents)) {
            throw new BusinessException(ResultCode.FAIL, "数据类型获取失败！");
        }
        CmsCategoryContent categoryContent = categoryContents.get(0);
        String categoryId = String.valueOf(categoryContent.getCategoryId());
        civilizationCultivation.setCategoryId(categoryId);
        civilizationCultivation.setSequence(categoryContent.getSequence());
        civilizationCultivation.setPublishDate(civilizationCultivation.getEffectiveDate().format(DateTimeFormatter.ISO_DATE));
        return civilizationCultivation;
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        super.removeByIds(list);
        list.forEach(it -> {
            categoryContentService.removeByContentId(Long.valueOf(String.valueOf(it)), false);
        });
        return true;
    }

    @Override
    public IPage<CivilizationCultivation> page(BaseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new CivilizationCultivationParams();
        }
        Page<CivilizationCultivation> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForCivilizationCultivation(page, (CivilizationCultivationParams) params).convert(this::convert);
    }

    private CivilizationCultivation convert(CivilizationCultivation record) {
        List<String> categoryIds = Arrays.asList(record.getCategory().split(Mark.COMMA));
        List<String> categoryName = new ArrayList<>();
        categoryIds.subList(1, categoryIds.size()).forEach(it -> {
            categoryName.add(categoryService.getById(it).getName());
        });
        record.setCategory(String.join(Mark.LINE, categoryName));
        return record;
    }

    @Override
    public IPage<CivilizationCultivation> pageForApi(CivilizationCultivationParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new CivilizationCultivationParams();
        }
        Page<CivilizationCultivation> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        if (StringUtil.isEmpty(params.getCategoryCode())) {
            return baseMapper.pageForCivilizationCultivationApi(page, params);
        }
        CmsCategory category = categoryService.getByCode(params.getCategoryCode());
        if(category!=null) {
            params.setCategoryId(String.valueOf(category.getId()));
        }
        return baseMapper.pageForCivilizationCultivationApi(page, params);
    }

    @Override
    public List<String> getYears(String categoryCode) {
        CmsCategory category = categoryService.getByCode(categoryCode);
        String categoryId = String.valueOf(category.getId());
        return baseMapper.getYears(categoryId);
    }

    @Override
    public CivilizationCultivation getForApi(Serializable id) {
        CivilizationCultivation civilizationCultivation = super.getById(id);
        return civilizationCultivation;
    }

    @Override
    public void ground(String id) {
        CivilizationCultivation civilizationCultivation = getById(id);
        civilizationCultivation.setGrounding(!civilizationCultivation.getGrounding());
        super.updateById(civilizationCultivation);
    }

    @Override
    public List<CivilizationCultivateDto> getScreenDataList(DataScreenParams params) {
        return baseMapper.getScreenDataList(params);
    }

    @Override
    public List<CivilizationCultivationDashboardDto> mienCockpitData(String categoryCode) {
        CmsCategory category = categoryService.getByCode(categoryCode);
        if(category==null) {
            return Collections.emptyList();
        }
        String categoryId = String.valueOf(category.getId());
        String year = baseMapper.getMaxYears(categoryId);
        if(StringUtil.isEmpty(year)) {
            return Collections.emptyList();
        }
        return baseMapper.mienCockpitData(categoryId, year);
    }
}
