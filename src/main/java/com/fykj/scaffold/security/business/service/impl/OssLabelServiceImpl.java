package com.fykj.scaffold.security.business.service.impl;

import fykj.microservice.core.base.BaseServiceImpl;
import com.fykj.scaffold.security.business.domain.entity.OssLabel;
import com.fykj.scaffold.security.business.mapper.OssLabelMapper;
import com.fykj.scaffold.security.business.service.IOssLabelService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Service
public class OssLabelServiceImpl extends BaseServiceImpl<OssLabelMapper, OssLabel> implements IOssLabelService {

    @Override
    public List<Long> getByOssId(Long ossId) {
        List<OssLabel> ossLabels = lambdaQuery().eq(OssLabel::getOssId,ossId).list();
        return ossLabels.stream().map(OssLabel::getLabelId).collect(Collectors.toList());
    }
}
