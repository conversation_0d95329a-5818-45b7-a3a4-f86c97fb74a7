package com.fykj.scaffold.mall.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author：yangxu
 * @Date：2025/4/3 17:02
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("积分捐赠名单信息")
public class PointContributePersonDto implements Serializable {

    private static final long serialVersionUID = 328994731613233650L;

    @ApiModelProperty("捐赠者姓名")
    private String personName;

    @ApiModelProperty("捐赠积分")
    private BigDecimal contributePoint;

    @ApiModelProperty("捐赠积分排名")
    private Integer contributePointRank;
}
