package com.fykj.scaffold.security.business.service;


import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.zyz.domain.dto.dashboard_sum.SubAssociationDataSumDto;
import fykj.microservice.core.base.IBaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 组织架构service
 */
public interface ISysOrgService extends IBaseService<SysOrg> {
    /**
     * 获取组织
     *
     * @param orgId 组织id
     * @return 组织信息
     */
    SysOrg getOrgById(long orgId);

    /**
     * 保存组织
     *
     * @param org 组织对象
     * @return 新增后的组织
     */
    SysOrg saveOrg(SysOrg org);

    /**
     * 更新组织
     *
     * @param org 组织对象
     * @return 更新后的组织对象
     */
    SysOrg updateOrg(SysOrg org);

    /**
     * code是否存在
     *
     * @param code 组织的code
     * @param id   如果是更新，请传入id，
     * @return boolean
     */
    boolean isExist(String code, Long id);


    /**
     * 根据code获取部门
     *
     * @param code 组织code
     * @return 组织对象
     */
    SysOrg getByCode(String code);

    /**
     * 根据code获取组织名称，如果没有则返回空
     *
     * @param code 组织code
     * @return 组织名称
     */
    String getOrgName(String code);

    /**
     * 获取下属部门
     *
     * @param topCode
     * @return
     */
    List<SysOrg> getSubOrg(String topCode, boolean isContainsSelf);

    /**
     * 获取下属部门(只包含五个实践所)
     *
     * @param topCode
     * @return
     */
    List<SysOrg> getSubOrgOnlyContainFiveSubAssociation(String topCode, boolean isContainsSelf);

    /**
     * 构建一颗组织架构树
     *
     * @param topDeptCode   最顶层的部门的code
     * @param isContainSelf 是否要包含本身节点
     * @return 树形组织架构
     */
    List<SysOrg> getOrgTree(String topDeptCode, boolean isContainSelf);

    /**
     * 构建一颗组织架构树
     *
     * @param topDeptCode   最顶层的部门的code
     * @param isContainSelf 是否要包含本身节点
     * @return 树形组织架构
     */
    List<SysOrg> getOrgTreeOnlyContainFiveSubAssociation(String topDeptCode, boolean isContainSelf);

    /**
     * 通过编码查询父级组织
     *
     * @param code 组织code
     * @return 这个code组织的直接上级
     */
    SysOrg getParentByCode(String code);

    /**
     * 通过组织code获取组织层级
     *
     * @param orgCode 组织code
     * @return 组织的level
     */
    Integer getOrgLevel(String orgCode);

    /**
     * 通过组织获取组织层级
     *
     * @param org 组织
     * @return 组织的level
     */
    Integer getOrgLevel(SysOrg org);

    /**
     * 获取5大街道分协会列表
     *
     * @return 5大分协会，不带top，也不带children
     */
    List<SysOrg> getAreaSubAssociation();

    /**
     * @param withTop 是否带上top
     * @return level = 2，视情withTop的值带top，也不带children
     */
    List<SysOrg> getAreaSubAssociation(boolean withTop, boolean all);

    /**
     * 获取分协会累计数据
     * @return
     */
    List<SubAssociationDataSumDto> getSubAssociationSumData();

    /**
     * 获取下级部门code列表
     * @param topCode 上级部门code
     * @param isContainsSelf 是否包含本身
     * @return List<String>
     */
    List<String> getSubOrgCodeList(String topCode, boolean isContainsSelf);

    /**
     * 获取指定距离范围内的组织机构列表
     * @param orgCode 中心点组织code
     * @param distance 距离范围(单位:公里)
     * @return 符合条件的组织机构列表
     */
    List<SysOrg> listNearbyOrg(String orgCode, BigDecimal distance);
}
