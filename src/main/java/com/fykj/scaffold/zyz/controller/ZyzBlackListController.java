package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.BannedVolunteerSubmitDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzBlackList;
import com.fykj.scaffold.zyz.domain.params.ZyzBlackListParams;
import com.fykj.scaffold.zyz.service.IZyzBlackListService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * 黑名单
 *
 * 前端控制器
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-23
 */
@RestController
@RequestMapping("/admin/zyz/black/list")
@Api(tags = "黑名单接口")
public class ZyzBlackListController extends BaseController<IZyzBlackListService, ZyzBlackList,ZyzBlackListParams> {
    @GetMapping(value = "/recall")
    @ApiOperation(value = "解禁")
    public Result recall(long id) {
        if (!baseService.recall(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

/*    @GetMapping(value = "/test")
    @ApiOperation(value = "解禁")
    public Result test() {
        baseService.getMeetsRequirementsBlackList();
        return OK;
    }*/
    @ApiOperation("后台分页")
    @PostMapping("/pageForList")
    public JsonResult<IPage<ZyzBlackList>> pageForList(@RequestBody ZyzBlackListParams params) {
        return new JsonResult<>(baseService.pageForList(params));
    }

    @ApiOperation("拉黑")
    @PostMapping("/black")
    public Result render(@RequestBody BannedVolunteerSubmitDto bannedVolunteerSubmitDto) {
        baseService.black(bannedVolunteerSubmitDto);
        return OK;
    }
    @ApiOperation("保存黑名单对象")
    @PostMapping("/saveOrUpdateBlackList")
    public Result saveOrUpdateBlackList(@RequestBody ZyzBlackList blackList) {
        baseService.saveBlackList(blackList);
        return OK;
    }


}
