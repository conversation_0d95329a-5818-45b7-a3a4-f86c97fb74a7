package com.fykj.scaffold.portal_website.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 视频资源查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("视频资源查询参数")
public class VideoParams extends BaseParams {

    private static final long serialVersionUID = 5051351643065736048L;

    private String name;

    private String serverCode;

    private Integer importStatus;
}
