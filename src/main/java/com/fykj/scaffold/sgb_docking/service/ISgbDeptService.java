package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbDept;
import fykj.microservice.core.base.IBaseService;

/**
 * 苏州志愿者平台部门单位服务接口
 */
public interface ISgbDeptService extends IBaseService<SgbDept> {
    
    /**
     * 同步部门单位信息
     */
    void syncDept();
    
    /**
     * 根据部门单位编号获取部门单位信息
     * @param deptId 部门单位编号
     * @return 部门单位信息
     */
    SgbDept getByDeptId(String deptId);
}