package com.fykj.scaffold.sync.service.impl;

import com.fykj.scaffold.message.service.IMsgRecordService;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.ICityDataSyncConfigService;
import com.fykj.scaffold.sync.service.IPlatformSyncService;
import com.fykj.scaffold.sync.service.syncer.*;
import com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional
public class PlatformSyncServiceImpl implements IPlatformSyncService {

    @Autowired
    private VolunteerSyncer volunteerSyncer;

    @Autowired
    private TeamSyncer teamSyncer;

    @Autowired
    private TeamJoinSyncer teamJoinSyncer;

    @Autowired
    private NewsSyncer newsSyncer;

    @Autowired
    private RequirementSyncer requirementSyncer;

    @Autowired
    private ResourceSyncer resourceSyncer;

    @Autowired
    private DepSyncer depSyncer;

    @Autowired
    private BaseSyncer baseSyncer;

    @Autowired
    private SpotSyncer spotSyncer;

    @Autowired
    private ActivitySyncer activitySyncer;

    @Autowired
    private ActivityTimePeriodSyncer actTimePeriodSyncer;

    @Autowired
    private ActivityRecruitSyncer activityRecruitSyncer;

    @Autowired
    private ActivityMemberSyncer activityMemberSyncer;

    @Autowired
    private ServiceTimeSyncer serviceTimeSyncer;

    @Autowired
    private OrderSyncer orderSyncer;

    @Autowired
    private IMsgRecordService msgRecordService;

    @Autowired
    private ICityDataSyncConfigService cityDataSyncConfigService;

    @Autowired
    private OrgTeamSyncer orgTeamSyncer;

    @Autowired
    private ActShowSyncer actShowSyncer;

    @Autowired
    private ActScheduleSyncer actScheduleSyncer;

    @Autowired
    private ScheduleActPlanSyncer scheduleActPlanSyncer;

    @Override
    public ZyzSyncLog syncVolunteer(long volunteerId, long syncBillId) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return volunteerSyncer.sync(volunteerId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncTeam(long teamId, long syncBillId) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return teamSyncer.sync(teamId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncTeamMember(long teamId, long volunteerId, long syncBillId) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return teamJoinSyncer.sync(teamId, volunteerId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncCms(long contentId, long syncBillId) {
        return newsSyncer.sync(contentId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncRequirement(long requirementId, long syncBillId, String saveType) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return requirementSyncer.sync(requirementId, syncBillId, saveType);
    }

    @Override
    public ZyzSyncLog syncResource(long resourceId, long syncBillId, String saveType) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return resourceSyncer.sync(resourceId, syncBillId, saveType);
    }

    @Override
    public ZyzSyncLog syncDep(long depId, long syncBillId) {
        return depSyncer.sync(depId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncBase(long baseId, long syncBillId) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return baseSyncer.sync(baseId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncSpot(long spotId, long syncBillId) {
        return spotSyncer.sync(spotId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncActivity(long activityId, long syncBillId) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return activitySyncer.sync(activityId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncActTimePeriod(long actTimePeriodId, long syncBillId) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return actTimePeriodSyncer.sync(actTimePeriodId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncActivityRecruit(long activityId, long syncBillId) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return activityRecruitSyncer.sync(activityId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncActivityMember(ActSyncRecordDto apply) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return activityMemberSyncer.syncOne(apply);
    }

    @Override
    public void syncActivityMember(List<ActSyncRecordDto> applies) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        activityMemberSyncer.syncBatch(applies);
    }

    @Override
    public ZyzSyncLog syncServiceTime(ActSyncRecordDto apply) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        return serviceTimeSyncer.syncOne(apply);
    }

    @Override
    public void syncServiceTime(List<ActSyncRecordDto> applies) {
        cityDataSyncConfigService.checkPlatformSyncSwitch();
        serviceTimeSyncer.syncBatch(applies);
    }

    @Override
    public void msgSend(Long recordId) {
        msgRecordService.sendMsgRecord(recordId);
    }

    @Override
    public ZyzSyncLog syncOrder(long orderId, long syncBillId, String type) {
        return orderSyncer.sync(orderId, syncBillId, type);
    }

    @Override
    public ZyzSyncLog syncOrgTeam(long orgTeamId, long syncBillId) {
        return orgTeamSyncer.sync(orgTeamId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncActShow(long actShowId, long syncBillId) {
        return actShowSyncer.sync(actShowId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncActSchedule(long scheduleId, long syncBillId) {
        return actScheduleSyncer.sync(scheduleId, syncBillId);
    }

    @Override
    public ZyzSyncLog syncScheduleActPlan(long scheduleActPlanId, long syncBillId) {
        return scheduleActPlanSyncer.sync(scheduleActPlanId, syncBillId);
    }



}
