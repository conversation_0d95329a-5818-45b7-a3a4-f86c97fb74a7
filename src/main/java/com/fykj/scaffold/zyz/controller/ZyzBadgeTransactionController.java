package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTransaction;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeTransactionParams;
import com.fykj.scaffold.zyz.domain.vo.PointsExchangeStatisticsVO;
import com.fykj.scaffold.zyz.domain.vo.TransactionStatisticsVO;
import com.fykj.scaffold.zyz.service.IZyzBadgeTransactionService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 勋章交易流水-接口控制器
 *
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/admin/zyz/badge/transaction")
@Api(tags = "勋章交易流水-管理接口")
public class ZyzBadgeTransactionController
        extends BaseController<IZyzBadgeTransactionService, ZyzBadgeTransaction, ZyzBadgeTransactionParams> {

    @ApiOperation(value = "分页查询勋章交易流水")
    @PostMapping("/queryPage")
    public JsonResult<IPage<ZyzBadgeTransaction>> customPage(@RequestBody ZyzBadgeTransactionParams params) {
        IPage<ZyzBadgeTransaction> page = baseService.queryPage(params);
        return new JsonResult<>(page);
    }

    @ApiOperation(value = "统计交易数量")
    @PostMapping("/statisticsTransaction")
    public JsonResult<TransactionStatisticsVO> statisticsTransaction(@RequestBody ZyzBadgeTransactionParams params) {
        TransactionStatisticsVO result = baseService.statisticsTransaction(params);
        return new JsonResult<>(result);
    }

    @ApiOperation(value = "统计积分兑换档数量")
    @PostMapping("/statisticsPointsExchange")
    public JsonResult<PointsExchangeStatisticsVO> statisticsPointsExchange(@RequestBody ZyzBadgeTransactionParams params) {
        PointsExchangeStatisticsVO result = baseService.statisticsPointsExchange(params);
        return new JsonResult<>(result);
    }
}
