package com.fykj.scaffold.dynamic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.dynamic.domain.entity.DynamicFormFieldRelation;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;

/**
 * 模板-字段关联-Mapper接口
 *
 * @date 2025-05-19
 */
public interface DynamicFormFieldRelationMapper extends BaseMapper<DynamicFormFieldRelation> {

    boolean removeByIds(@Param("idList") Collection<?> idList);

    boolean removeById(@Param("id") Serializable id);

    boolean removeByFormId(@Param("formId") Serializable formId);
}
