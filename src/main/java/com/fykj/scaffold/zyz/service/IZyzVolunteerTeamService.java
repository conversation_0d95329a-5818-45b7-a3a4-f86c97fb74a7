package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamApplyDto;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamDto;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamInfoDto;
import com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerTeamByPhoneParams;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerTeamParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 志愿者与团队关系表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-13
 */
public interface IZyzVolunteerTeamService extends IService<ZyzVolunteerTeam>, IBaseService<ZyzVolunteerTeam> {
    /**
     * 根据teamId和volunteerId获取
     *
     * @param teamId
     * @param volunteerId
     * @return
     */
    ZyzVolunteerTeam getByTeamIdAndVolunteerId(long teamId, long volunteerId);

    /**
     * 移除团队志愿者
     *
     * @return
     */
    boolean removeVolunteer(long teamId, long volunteerId);

    /**
     * 移除团队志愿者
     *
     * @param id
     * @return
     */
    boolean withdrawFromTeam(Long id);

    /**
     * 取消申请加入团队
     *
     * @param id
     * @return
     */
    boolean cancelJoinTeam(Long id);

    /**
     * 志愿者删除团队申请记录
     *
     * @param auditId
     * @return
     */
    boolean removeTeamApply(Long auditId);

    /**
     * 加入团队志愿者
     *
     * @param id
     * @return
     */
    boolean joinTeam(Long id);


    /**
     * 根据团队id和志愿者id获取数据
     *
     * @param volunteerId
     * @param teamId
     * @return
     */
    ZyzVolunteerTeam getByVolunteerIdAndTeamId(long volunteerId, long teamId);

    /**
     * 获取团队下的志愿者 分页查询
     * @return
     */
    IPage<VolunteerTeamDto> getMyVolunteerPages(ZyzVolunteerTeamParams params);

    /**
     * 获取团队成员
     * @param teamId
     * @return
     */
    List<VolunteerTeamDto> getTeamMembers(Long teamId);

    /**
     * 获取团队下的志愿者
     * @return
     */
    List<VolunteerTeamDto> getTeamVolunteerList(ZyzVolunteerTeamParams params);

    /**
     * 处理团队管理员通用方法
     * @param teamId
     */
    void dealTeamAdmin(Long teamId);

    /**
     * 刷新团队管理员数据
     * @param teamId
     * @param userId
     * @param volunteerId
     */
    void teamAdminRefresh(Long teamId, Long userId, Long volunteerId);

    /**
     * 团队新增，保存团队管理员
     *
     * @param teamId
     * @param volunteerId
     */
    void saveTeamAdmin(Long teamId, Long volunteerId);

    /**
     * 获取团队下的志愿者人数
     * @param teamId
     * @return
     */
    Integer getTeamVolunteerNum(Long teamId);

    /**
     * 获取每天定时任务需要同步的数据
     * @return
     */
    List<ZyzVolunteerTeam> getNeedSync();

    /**
     * 获取每天定时任务需要SGB同步的数据
     * @return
     */
    List<Long> getSgbNeedSync();

    /**
     * 批量同步
     * @param vtList
     */
    void syncBatch(List<ZyzVolunteerTeam> vtList);

    /**
     * 获取团队注册人数
     * @param params
     * @return
     */
    List<OrgDataFormDto> getTeamVolunteerSum(OrgDataReportFormParams params);

    /**
     * 获取团队成员报名列表
     *
     * @param params 查询参数
     * @return 分页数据
     */
    IPage<VolunteerTeamApplyDto> getTeamVolunteerApplyList(ZyzVolunteerTeamParams params);

    /**
     * 根据志愿者手机号查询所属团队
     *
     * @param params 查询参数，包含手机号
     * @return 团队信息分页数据
     */
    IPage<VolunteerTeamInfoDto> getTeamsByVolunteerPhone(ZyzVolunteerTeamByPhoneParams params);
}

