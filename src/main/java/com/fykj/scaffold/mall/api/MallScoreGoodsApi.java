package com.fykj.scaffold.mall.api;

/**
 * <AUTHOR>
 */

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsParams;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

/**
 * 商品微信前端接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/mall/score/goods")
@Api(tags = "pc --商品无需登录前端接口")
public class MallScoreGoodsApi {
    @Autowired
    private IMallScoreGoodsService goodsService;

    @ApiOperation("商品小程序分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallScoreGoods>> getPages(@RequestBody MallScoreGoodsParams params) {
        params.setStatus(true);
        params.setPadExchange(Boolean.FALSE);
        return new JsonResult<>(goodsService.pageForApi(params));
    }

    @ApiOperation("PAD兑换商品商城分页查询")
    @PostMapping("/getPadExchangeGoodsPages")
    public JsonResult<IPage<MallScoreGoods>> getPadExchangeGoodsPages(@RequestBody MallScoreGoodsParams params) {
        params.setStatus(true);
        params.setPadExchange(Boolean.TRUE);
        return new JsonResult<>(goodsService.pageForApi(params));
    }

    @ApiOperation("商品查询详情")
    @GetMapping({"/get"})
    public JsonResult<MallScoreGoods> get(@RequestParam String id) {
        MallScoreGoods result = goodsService.getById(id);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }

    @ApiOperation(value = "创建PAD兑换商品小程序二维码")
    @GetMapping(value = "/createQrCode")
    public Result createQrCode(@RequestParam long goodsId, @RequestParam String appId, @RequestParam Boolean prod) {
        return new JsonResult<>(goodsService.createQrCode(appId, goodsId, prod));
    }

    @ApiOperation("商品小程序分页查询")
    @PostMapping("/getPagesForStar")
    public JsonResult<IPage<MallScoreGoods>> getPagesForStar(@RequestBody MallScoreGoodsParams params) {
        params.setStatus(true);
        params.setStar(true);
        params.setPadExchange(Boolean.FALSE);
        return new JsonResult<>(goodsService.pageForApiStar(params));
    }

    @ApiOperation("商品查询详情")
    @GetMapping({"/getForStar"})
    public JsonResult<MallScoreGoods> getForStar(@RequestParam String id) {
        MallScoreGoods result = goodsService.getById(id);
        result.setOrderNo(IdUtil.getSnowflake(1L, 1L).nextIdStr());
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }
}
