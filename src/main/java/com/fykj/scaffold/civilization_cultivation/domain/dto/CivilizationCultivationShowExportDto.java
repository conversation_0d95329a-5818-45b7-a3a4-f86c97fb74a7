package com.fykj.scaffold.civilization_cultivation.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import fykj.microservice.core.support.excel.annotation.BoolConvert;
import fykj.microservice.core.support.excel.annotation.LocalDateTimeConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文明培育展示数据导出
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CivilizationCultivationShowExportDto implements Serializable {

    private static final long serialVersionUID = 4847427999889878396L;

    /**
     * 类型
     */
    @ExcelProperty("类型")
    @ApiModelProperty(value = "类型")
    private String typeText;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 联系电话
     */
    @ExcelProperty("联系电话")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 年份
     */
    @ExcelProperty("年份")
    @ApiModelProperty(value = "年份")
    private Integer year;

    /**
     * 所属街道社区
     */
    @ExcelProperty("所属街道社区")
    @ApiModelProperty(value = "所属街道社区")
    private String belongStreetCommunity;

    /**
     * 地址
     */
    @ExcelProperty("地址")
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 事迹/成就
     */
    @ExcelProperty("事迹/成就")
    @ApiModelProperty(value = "事迹/成就")
    private String achievements;

    /**
     * 封面图
     */
    @ExcelProperty("封面图")
    @ApiModelProperty(value = "封面图")
    private String picture;

    /**
     * 外链地址
     */
    @ExcelProperty("外链地址")
    @ApiModelProperty(value = "外链地址")
    private String outLinkAddress;

    /**
     * 介绍
     */
    @ExcelProperty("介绍")
    @ApiModelProperty(value = "介绍")
    private String description;

    /**
     * 上/下架
     */
    @ExcelProperty("上/下架")
    @ApiModelProperty(value = "是否上下架")
    @BoolConvert
    private Boolean onShelve;

    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    @ApiModelProperty(value = "更新时间")
    @LocalDateTimeConvert
    private LocalDateTime updateDate;
}
