package com.fykj.scaffold.ledger.service.impl;

import com.fykj.scaffold.ledger.domain.dto.LedgerReportDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerDepartment;
import com.fykj.scaffold.ledger.domain.entity.LedgerReport;
import com.fykj.scaffold.ledger.mapper.LedgerReportMapper;
import com.fykj.scaffold.ledger.service.ILedgerDepartmentService;
import com.fykj.scaffold.ledger.service.ILedgerReportService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.Result;
import result.ResultCode;

import java.time.LocalDateTime;


/**
 * 台账上报-服务实现类
 *
 * @date 2025-05-26
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class LedgerReportServiceImpl extends BaseServiceImpl<LedgerReportMapper, LedgerReport> implements ILedgerReportService {

    @Autowired
    private ILedgerDepartmentService departmentService;

    @Override
    public Result report(LedgerReportDto reportDto) {
        Long userId = (Long) Oauth2Util.getUserId();
        LedgerDepartment department = departmentService.getByUserId(userId);
        if (department == null) {
            return new Result(ResultCode.FAIL.code(),"该账号无需测评台账报送");
        }
        reportDto.setDepartmentId(department.getId());
        reportDto.setStatus(true);
        return commonReport(reportDto);

    }

    @Override
    public Result reportAdmin(LedgerReportDto reportDto) {
        LedgerDepartment department = departmentService.getById(reportDto.getDepartmentId());
        if (department == null) {
            return new Result(ResultCode.FAIL.code(),"该局办无需测评台账报送");
        }
        return commonReport(reportDto);
    }

    private Result commonReport(LedgerReportDto reportDto) {
        LedgerReport report = lambdaQuery().eq(LedgerReport::getDepartmentId, reportDto.getDepartmentId())
                .eq(LedgerReport::getIndexItemId, reportDto.getIndexItemId()).one();
        if (report == null) {
            report = new LedgerReport();
            report.setIndexItemId(reportDto.getIndexItemId());
            report.setDepartmentId(reportDto.getDepartmentId());
        }
        report.setReportDate(LocalDateTime.now());
        report.setAttachmentUrl(reportDto.getAttachmentUrl());
        report.setAttachmentName(reportDto.getAttachmentName());
        report.setStatus(reportDto.getStatus());
        saveOrUpdate(report);
        return new Result();
    }

}
