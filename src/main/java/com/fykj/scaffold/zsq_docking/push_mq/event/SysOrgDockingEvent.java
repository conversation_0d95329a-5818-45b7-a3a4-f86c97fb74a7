package com.fykj.scaffold.zsq_docking.push_mq.event;


import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysOrgDockingEvent extends BaseDockingMqEvent {

    /**
     * 对接记录 id
     */
    private Long dockingRecordId;

    /**
     * 组织 id
     */
    private Long orgId;

    /**
     * 消息 id
     */
    private String operation;
    /**
     * 消息 id
     */
    private String zwtUserId;
    /**
     * 需求信息
     */
    private SysOrg sysOrg;

    /**
     * 志愿者平台用户 id
     */
    private Serializable zyzUserId;

}
