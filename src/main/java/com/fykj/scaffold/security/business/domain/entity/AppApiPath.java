package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 应用接口权限
 *
 * <AUTHOR> @email ${email}
 * @date 2021-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_app_api_path")
public class AppApiPath extends BaseEntity  {

	private static final long serialVersionUID = 1L;

	/**
	 * 接口名称
	 */
	@TableField("name")
	@ApiModelProperty(value = "接口名称")
	private String name;

	/**
	 * 接口路径id
	 */
	@TableField("api_id")
	@ApiModelProperty(value = "接口路径id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long apiId;

	/**
	 * 接口路径
	 */
	@TableField("path")
	@ApiModelProperty(value = "接口路径")
	private String path;

	/**
	 * AppId
	 */
	@TableField("app_id")
	@ApiModelProperty(value = "AppId")
	private String appId;


}
