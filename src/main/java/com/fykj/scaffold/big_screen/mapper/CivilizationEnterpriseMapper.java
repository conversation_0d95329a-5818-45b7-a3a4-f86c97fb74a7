package com.fykj.scaffold.big_screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.big_screen.domain.entity.CivilizationEnterprise;
import com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationEnterpriseDto;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  文明单位Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
public interface CivilizationEnterpriseMapper extends BaseMapper<CivilizationEnterprise> {

    /**
     * 获取大屏数据列表
     * @param params
     * @return
     */
    List<CivilizationEnterpriseDto> getScreenDataList(@Param("params") DataScreenParams params);
}
