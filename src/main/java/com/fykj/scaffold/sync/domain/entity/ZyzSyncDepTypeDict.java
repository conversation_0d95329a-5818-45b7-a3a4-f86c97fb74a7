package com.fykj.scaffold.sync.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 市平台职能部门类型数据字典
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_sync_dep_type_dict")
public class ZyzSyncDepTypeDict extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门类型id
     */
    @TableField("dep_type_id")
    @ApiModelProperty(value = "部门类型id")
    private String depTypeId;

    /**
     * 部门类型名称
     */
    @TableField("dep_type_name")
    @ApiModelProperty(value = "部门类型名称")
    private String depTypeName;


}
