package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.zyz.service.IAnnualAccountSumService;
import com.fykj.scaffold.zyz.service.IAnnualAccountVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 年度账单数据生成任务
 */
@Component
@Slf4j
public class AnnualAccountTask {

    @Autowired
    private IAnnualAccountSumService sumService;

    @Autowired
    private IAnnualAccountVolunteerService volunteerService;

    @XxlJob("Annual_Account_Handler")
    public void annualAccountJobHandler() {
        XxlJobHelper.log("Annual_Account_Handler-Job, START!");
        try {
            sumService.getDataSum(Boolean.TRUE);
            volunteerService.volunteerSum();
        } catch (Exception e) {
            log.error("年度账单数据生成任务失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Annual_Account_Handler-Job, END!");
    }
}
