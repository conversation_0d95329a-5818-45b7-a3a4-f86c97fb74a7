package com.fykj.scaffold.civilization_cultivation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CheckAttachmentDto;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CheckInfoUploadDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationCollectParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;


/**
 * 文明培育征集-管理
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface ICivilizationCultivationCollectService extends IBaseService<CivilizationCultivationCollect> {

    /**
     * 征集提交
     * @param collectInfo
     */
    void collectSubmit(CivilizationCultivationCollect collectInfo);

    /**
     * 我的征集提交
     * @param params
     * @return
     */
    IPage<CivilizationCultivationCollect> myCollectSubmit(CivilizationCultivationCollectParams params);

    /**
     * 获取征集详情
     * @param id
     * @return
     */
    CivilizationCultivationCollect getDetail(Long id);

    /**
     * 征集撤回
     * @param id
     */
    void collectCallback(Long id);

    /**
     * 征集删除
     * @param id
     */
    void collectRemove(Long id);

    /**
     * 征集新增/修改
     * @param collectInfo
     */
    void collectSaveOrUpdate(CivilizationCultivationCollect collectInfo);

    /**
     * 转发街道核实
     * @param id
     */
    void transferToStreetCheck(Long id);

    /**
     * 分页查询街道核实数据
     * @param params
     * @return
     */
    IPage<CivilizationCultivationCollect> pageForStreetCheck(CivilizationCultivationCollectParams params);

    /**
     * 核实退回
     * @param id
     */
    void checkRefuse(Long id);

    /**
     * 核实信息删除
     * @param collectId
     * @param checkInfoId
     */
    void checkInfoRemove(Long collectId, String checkInfoId);

    /**
     * 核实信息上传
     * @param uploadDto
     */
    void checkInfoUpload(CheckInfoUploadDto uploadDto);

    /**
     * 获取核实信息
     * @param id
     * @return
     */
    List<CheckAttachmentDto> getCheckInfo(Long id);
}

