package com.fykj.scaffold.security.business.service.impl;


import com.fykj.scaffold.security.business.domain.entity.SysWeakPw;
import com.fykj.scaffold.security.business.mapper.SysWeakPwMapper;
import com.fykj.scaffold.security.business.service.ISysWeakPwService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 用户弱口令列表
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2025-06-25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysWeakPwServiceImpl extends BaseServiceImpl<SysWeakPwMapper, SysWeakPw> implements ISysWeakPwService {

    @Override
    public Boolean isWeakPassword(String pw) {
        return lambdaQuery()
                .eq(SysWeakPw::getPassword, pw)
                .eq(SysWeakPw::getStatus, true)
                .exists();
    }
}