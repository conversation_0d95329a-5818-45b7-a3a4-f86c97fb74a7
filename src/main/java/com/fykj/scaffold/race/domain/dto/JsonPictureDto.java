package com.fykj.scaffold.race.domain.dto;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fykj.scaffold.support.utils.ListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 照片路径情况详情
 *
 * <AUTHOR> @email ${email}
 * @date 2022-10-08
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("照片路径")
public class JsonPictureDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;



    @ApiModelProperty(value = "编号")
    private String workNum;

    @ApiModelProperty(value = "作者")
    private String author;

    @ApiModelProperty(value = "栏目")
    private String category;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "奖项")
    private String prize;

    @ApiModelProperty(value = "状态")
    private String state;

    @ApiModelProperty(value = "名称")
    private String workName;

    @ApiModelProperty(value = "地址")
    private String photoAddress;


    @ApiModelProperty(value = "图片")
    @TableField(typeHandler = PictureDto.pictureListTypeHandler.class)
    private List<PictureDto> photos;


    /**
     * json处理器
     */
    public static class JsonPictureTypeHandler extends ListTypeHandler<JsonPictureDto> {

        @Override
        protected TypeReference<List<JsonPictureDto>> specificType() {
            return new TypeReference<List<JsonPictureDto>>() {
            };
        }

    }


}
