package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.report_form.*;
import com.fykj.scaffold.zyz.domain.params.AgeRangeReportFormParams;
import com.fykj.scaffold.zyz.domain.params.BelongFieldReportFormParams;
import com.fykj.scaffold.zyz.domain.params.OrgDataReportFormParams;
import com.fykj.scaffold.zyz.domain.params.ReportFormParams;
import com.fykj.scaffold.zyz.service.IReportFormService;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * 后台管理报表接口
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-08-15
 */
@RestController
@RequestMapping("/admin/report_form")
@Slf4j
@Api(tags = "志愿报表接口")
public class ReportFormController {

    @Autowired
    private IReportFormService reportFormService;

    @ApiOperation("获取活跃度报表")
    @PostMapping("/getActivationForm")
    public JsonResult<List<ActivationFormDto>> getActivationForm(@RequestBody ReportFormParams params) {
        return new JsonResult<>(reportFormService.getActivationForm(params));
    }

    @ApiOperation("活跃度报表导出")
    @PostMapping({"/exportActivationForm"})
    public void exportActivationForm(@RequestBody ReportFormParams params) {
        List<ActivationFormDto> res = reportFormService.getActivationForm(params);
        if (!params.getContainSub()) {
            ExcelUtil.writeExcel(res, "活跃度报表", ActivationFormDto.class);
            return;
        }
        List<ActivationFormDto> finalRes = new ArrayList<>(res);
        res.forEach(it -> {
            List<ActivationFormDto> children = it.getChildren();
            if (CollectionUtil.isEmpty(children)) {
                return;
            }
            finalRes.addAll(children);
            children.forEach(im -> {
                if (CollectionUtil.isEmpty(im.getChildren())) {
                    return;
                }
                finalRes.addAll(im.getChildren());
            });
        });
        ExcelUtil.writeExcel(finalRes, "活跃度报表", ActivationFormDto.class);
    }

    @ApiOperation("初始化志愿活动报表宽表")
    @GetMapping("/initActFormReport")
    public Result initActFormReport() {
        reportFormService.initActFormReport();
        return OK;
    }

    @ApiOperation("获取活动报表")
    @PostMapping("/getActivityForm")
    public JsonResult<List<ActivityFormDto>> getActivityForm(@RequestBody ReportFormParams params) {
        return new JsonResult<>(reportFormService.getActivityForm(params));
    }

    @ApiOperation("获取活动报表-活动数据统计分页")
    @PostMapping("/pageForActSum")
    public JsonResult<IPage<ActSumFormDto>> pageForActSum(@RequestBody ReportFormParams params) {
        return new JsonResult<>(reportFormService.pageForActSum(params));
    }

    @ApiOperation("获取活动报表-团队数据统计分页")
    @PostMapping("/pageForTeamSum")
    public JsonResult<IPage<TeamSumFormDto>> pageForTeamSum(@RequestBody ReportFormParams params) {
        return new JsonResult<>(reportFormService.pageForTeamSum(params));
    }

    @ApiOperation("活动报表导出")
    @PostMapping({"/exportActivityForm"})
    public void exportActivityForm(@RequestBody ReportFormParams params) {
        if (params.getLevel() == 1) {
            ExcelUtil.writeExcel(reportFormService.getActivityForm(params), "领域活动报表", ActivityFormDto.class);
            return;
        }
        if ("act".equals(params.getSubTable())) {
            ExcelUtil.writeExcel(reportFormService.listForActSum(params), "领域下活动信息列表", ActSumFormDto.class);
            return;
        }
        if ("team".equals(params.getSubTable())) {
            ExcelUtil.writeExcel(reportFormService.listForTeamSum(params), "领域下活动参与团队信息列表", TeamSumFormDto.class);
        }
    }

    @ApiOperation("获取组织架构数据报表-组织数据")
    @PostMapping("/getOrgDataList")
    public JsonResult<List<OrgDataFormDto>> getOrgDataList(@RequestBody OrgDataReportFormParams params) {
        return new JsonResult<>(reportFormService.getOrgDataList(params));
    }

    @ApiOperation("导出组织架构数据报表-组织数据")
    @PostMapping({"/exportOrgDataForm"})
    public void exportOrgDataForm(@RequestBody OrgDataReportFormParams params) {
        List<OrgDataFormDto> result = reportFormService.getOrgDataList(params);
        List<OrgDataFormExportDto> exportResult = new ArrayList<>();
        result.forEach(it -> {
            OrgDataFormExportDto exportDto = new OrgDataFormExportDto();
            BeanUtils.copyProperties(it, exportDto);
            exportResult.add(exportDto);
        });
        ExcelUtil.writeExcel(exportResult, "组织架构数据报表-按组织统计", OrgDataFormExportDto.class);
    }

    @ApiOperation("获取组织架构数据报表-团队数据")
    @PostMapping("/getTeamDataList")
    public JsonResult<IPage<OrgDataFormDto>> getTeamDataList(@RequestBody OrgDataReportFormParams params) {
        return new JsonResult<>(reportFormService.getTeamDataList(params));
    }

    @ApiOperation("导出组织架构数据报表-团队数据")
    @PostMapping({"/exportTeamDataForm"})
    public void exportTeamDataForm(@RequestBody OrgDataReportFormParams params) {
        params.setPageSize(9999);
        List<OrgDataFormDto> result = reportFormService.getTeamDataList(params).getRecords();
        List<TeamDataFormExportDto> exportResult = new ArrayList<>();
        result.forEach(it -> {
            TeamDataFormExportDto exportDto = new TeamDataFormExportDto();
            BeanUtils.copyProperties(it, exportDto);
            exportResult.add(exportDto);
        });
        ExcelUtil.writeExcel(exportResult, "组织架构数据报表-按团队统计", TeamDataFormExportDto.class);
    }

    @ApiOperation("获取服务领域数据报表")
    @PostMapping("/getBelongFieldReportFormData")
    public JsonResult<List<BelongFieldFormDto>> getBelongFieldReportFormData(@RequestBody BelongFieldReportFormParams params) {
        return new JsonResult<>(reportFormService.getBelongFieldSumData(params));
    }

    @ApiOperation("导出服务领域数据报表")
    @PostMapping({"/exportBelongFieldFormData"})
    public void exportBelongFieldFormData(@RequestBody BelongFieldReportFormParams params) {
        List<BelongFieldFormDto> result = reportFormService.getBelongFieldSumData(params);
        if ("team".equals(params.getQueryType())) {
            if (CollectionUtil.isEmpty(result)) {
                ExcelUtil.writeExcel(new ArrayList<>(), "服务领域数据报表", BelongFieldTeamQueryFormDto.class);
                return;
            }
            ExcelUtil.writeExcel(result.stream().map(it -> {
                BelongFieldTeamQueryFormDto dto = new BelongFieldTeamQueryFormDto();
                BeanUtils.copyProperties(it, dto);
                return dto;
            }).collect(Collectors.toList()), "服务领域数据报表", BelongFieldTeamQueryFormDto.class);
            return;
        }
        ExcelUtil.writeExcel(result, "服务领域数据报表", BelongFieldFormDto.class);
    }

    @ApiOperation("获取年龄段数据报表")
    @PostMapping("/getAgeRangeReportFormData")
    public JsonResult<List<AgeRangeFormDto>> getAgeRangeReportFormData(@RequestBody AgeRangeReportFormParams params) {
        return new JsonResult<>(reportFormService.getAgeRangeSumData(params));
    }

    @ApiOperation("导出年龄段数据报表")
    @PostMapping({"/exportAgeRangeFormData"})
    public void exportAgeRangeFormData(@RequestBody AgeRangeReportFormParams params) {
        List<AgeRangeFormDto> result = reportFormService.getAgeRangeSumData(params);
        ExcelUtil.writeExcel(result, "年龄段数据报表", AgeRangeFormDto.class);
    }
}
