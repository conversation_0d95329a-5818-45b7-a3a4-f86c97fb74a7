package com.fykj.scaffold.retrieve.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.retrieve.domain.dto.AccountTeamInfoResultDto;
import com.fykj.scaffold.retrieve.domain.dto.RetrieveTeamDto;
import com.fykj.scaffold.retrieve.domain.dto.RetrieveTeamResultDto;
import com.fykj.scaffold.retrieve.service.IRetrieveTeamService;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.oss.IOssSaver;
import com.fykj.scaffold.support.oss.impl.LocalOssSaver;
import com.fykj.scaffold.support.utils.AesUtil;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamRetrieveParams;
import com.fykj.scaffold.zyz.service.*;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.support.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;

import javax.imageio.ImageIO;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.Cons.DEFAULT_IGNORE_COPY_FILED;
import static com.fykj.scaffold.zyz.conns.ZyzCons.TEAM_AUDIT_STATUS_PASS;

/**
 * <AUTHOR>
 * @since 2023-05-19
 */
@Slf4j
@Service
public class RetrieveTeamServiceImpl implements IRetrieveTeamService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzTeamRetrieveService teamRetrieveService;



    @Override
    public RetrieveTeamResultDto retrieveTeam(RetrieveTeamDto dto) {
        // 由于很多人不知道密码，只能写死了2023-11-09
        dto.setPassword("qvLw#8iOv^");
        RetrieveTeamResultDto result = new RetrieveTeamResultDto();
        result.setRetrieveResultCode(800);
        List<Long> oldSysTeamIds = fetchTeamIdsByApi(dto.getUsernameOrEmailAddress(), dto.getPassword());
        // 获取需要找回的团队信息
        getNeedRetrieveTeamIds(result, oldSysTeamIds);
        return result;
    }

    private List<Long> fetchTeamIdsByApi(String usernameOrEmailAddress, String password) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("UsernameOrEmailAddress", usernameOrEmailAddress);
        paramMap.put("Password", password);
        Map<String, List<String>> headers = new HashMap<>();
        headers.put("Content-Type", CollectionUtil.newArrayList("application/json;charset=UTF-8"));
        HttpResponse httpResponse;
        try {
            // http://***********/api/Team/getAccountTeamInfo
            httpResponse = HttpUtil.createPost("https://***********/api/Team/getAccountTeamInfo")
                    .header(headers)
                    .timeout(30000)
                    .body(JSONUtil.toJsonStr(paramMap)).execute();
        } catch (Exception e) {
            log.error("请求地址：{}, headers：{}, params：{}, exception：{}",
                    "http://***********/api/Team/getAccountTeamInfo", headers, paramMap, e.toString());
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求获取旧平台账号信息失败，请联系管理员！");
        }
        if (httpResponse.getStatus() != HttpStatus.HTTP_OK || !JSONUtil.isTypeJSON(httpResponse.body())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "网络中断，未获取到旧平台账号信息，请联系管理员！");
        }
        JSONObject back = JSONObject.parseObject(httpResponse.body());
        if (back.getInteger("code") != 1) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "旧平台账号密码错误，请重试！");
        }
        List<AccountTeamInfoResultDto> backs = JSON.parseArray(back.getString("data"), AccountTeamInfoResultDto.class);
        if (backs == null || backs.size() == 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "该账号在旧平台没有管理的团队，请勿重复找回！");
        }
        return backs.stream().map(it -> Long.parseLong(it.getId())).collect(Collectors.toList());
    }

    private void getNeedRetrieveTeamIds(RetrieveTeamResultDto result, List<Long> oldSysTeamIds) {
        // 志愿者加入团队审核通过的数据
//        List<ZyzVolunteerTeamAudit> vtAudit = vtAuditService.lambdaQuery()
//                .eq(ZyzVolunteerTeamAudit::getVolunteerId, volunteerId)
//                .eq(ZyzVolunteerTeamAudit::getStatus, VOLUNTEER_AUDIT_STATUS_PASS)
//                .list();
//        List<Long> volunteerJoinTeamAudited = CollectionUtil.isEmpty(vtAudit) ? null : vtAudit.stream().map(ZyzVolunteerTeamAudit::getTeamId).collect(Collectors.toList());
        // 志愿者加入团队并成为管理员，但排除那些已经审核通过加入数据的
//        List<ZyzVolunteerTeam> volunteerTeamList = volunteerTeamService.lambdaQuery()
//                .eq(ZyzVolunteerTeam::getVolunteerId, volunteerId)
//                .eq(ZyzVolunteerTeam::getDutyType, DUTY_TYPE_ADMIN)
//                .notIn(CollectionUtil.isNotEmpty(volunteerJoinTeamAudited), ZyzVolunteerTeam::getTeamId, volunteerJoinTeamAudited)
//                .list();
        // 老系统管理团队数据 + 当前系统需要核实的数据（合并去重）
        Set<Long> needRetrieveTeamIdSet = new HashSet<>(oldSysTeamIds);
//        if (CollectionUtil.isNotEmpty(volunteerTeamList)) {
//            needRetrieveTeamIdSet.addAll(volunteerTeamList.stream().map(ZyzVolunteerTeam::getTeamId).collect(Collectors.toSet()));
//        }
        // 团队找回审核通过的数据
        List<ZyzTeamRetrieve> teamRetrieveAuditPass = teamRetrieveService.lambdaQuery()
                .in(ZyzTeamRetrieve::getId, needRetrieveTeamIdSet)
//                .eq(ZyzTeamRetrieve::getVolunteerId, volunteerId)
                .eq(ZyzTeamRetrieve::getTeamStatus, TEAM_AUDIT_STATUS_PASS)
                .list();
        List<Long> teamRetrieveAuditPassed = CollectionUtil.isEmpty(teamRetrieveAuditPass) ? null : teamRetrieveAuditPass.stream().map(ZyzTeamRetrieve::getId).collect(Collectors.toList());
        List<Long> needRetrieveTeamIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(teamRetrieveAuditPassed)) {
            needRetrieveTeamIdSet.forEach(it -> {
                if (!teamRetrieveAuditPassed.contains(it)) {
                    needRetrieveTeamIds.add(it);
                }
            });
        } else {
            needRetrieveTeamIds.addAll(needRetrieveTeamIdSet);
        }
        if (CollectionUtil.isEmpty(needRetrieveTeamIds)) {
            result.setTeams(null);
            return;
        }
        List<ZyzTeam> needRetrieveTeams = teamService.lambdaQuery().in(ZyzTeam::getId, needRetrieveTeamIds).list();
        List<ZyzTeamRetrieve> newRetrieve = new ArrayList<>();
        needRetrieveTeams.forEach(it -> {
            if (teamRetrieveService.lambdaQuery().eq(ZyzTeamRetrieve::getId, it.getId()).exists()) {
                return;
            }
            ZyzTeamRetrieve teamRetrieve = new ZyzTeamRetrieve();
            BeanUtils.copyProperties(it, teamRetrieve, "version", "deleted", "creator", "updater", "createDate", "updateDate");
//            teamRetrieve.setVolunteerId(volunteerId);
            teamRetrieve.setTeamStatus(ZyzCons.TEAM_AUDIT_STATUS_WAIT_CHECK);
            teamRetrieve.setAuditOrgCode(null);
            teamRetrieve.setHurry(Boolean.FALSE);
            newRetrieve.add(teamRetrieve);
        });
        if (CollectionUtil.isNotEmpty(newRetrieve)) {
            teamRetrieveService.saveBatch(newRetrieve);
        }
        result.setTeams(getRetrieveTeams(needRetrieveTeamIds));
    }

    private List<ZyzTeamRetrieve> getRetrieveTeams(List<Long> teamIds) {
        ZyzTeamRetrieveParams params = new ZyzTeamRetrieveParams();
        params.setRetrieveIds(teamIds);
        List<ZyzTeamRetrieve> teams = teamRetrieveService.getList(params);
        DictTransUtil.trans(teams);
        DesensitiseUtil.desensitise(teams);
        teams.forEach(it -> {
            if (ZyzCons.TEAM_AUDIT_STATUS_WAIT_CHECK.equals(it.getTeamStatus())) {
                it.setTeamStatusText("待核实");
            }
        });
        return teams;
    }

    @Override
    public String createQrCode(String appId) {
        try {
            final WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            File qrCodeFile = wxMaService.getQrcodeService().createWxaCode("pages/user/index");
            ImageIO.write(ImageIO.read(qrCodeFile), "png", qrCodeFile);
            IOssSaver ossSaver = SpringContextUtil.getBean(LocalOssSaver.class);
            String path = ossSaver.save(FileUtil.getInputStream(qrCodeFile), "二维码.png").getUrl();
            return path;
        } catch (WxErrorException | IOException e) {
            log.error("小程序二维码创建失败，请联系管理员", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "生成失败，请联系管理员!");
        }
    }
}
