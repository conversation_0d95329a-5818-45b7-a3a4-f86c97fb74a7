package com.fykj.scaffold.race.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.race.domain.dto.PhotoDto;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.domain.params.PhotoParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;


/**
 * 摄影大赛-作品表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
public interface IPhotoService extends IBaseService<Photo> {

    boolean updateStatus(String ids, Boolean status,String auditRemark);

    /**
     *根据活动id获取作品
     * @param activityId
     * @return
     */
    List<Photo> getPhoto(Long activityId);

    /**
     * 获取我的参赛作品列表
     * @return
     */
    List<Photo> getMyPhoto();


    /**
     * 网页端 --分页查询
     * @param params
     * @return
     */
    IPage<Photo> getPagesForWeb(PhotoParams params);

    /**
     * 分页查询
     * @param params
     * @return
     */
    IPage<Photo> getPages(PhotoParams params);

//    byte[] getZipByte( Long activityId);

    List<PhotoDto> getPhotoList(Long activityId);
}

