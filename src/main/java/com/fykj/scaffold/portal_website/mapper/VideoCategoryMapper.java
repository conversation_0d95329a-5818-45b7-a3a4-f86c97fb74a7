package com.fykj.scaffold.portal_website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.portal_website.domain.dto.VideoCategoryDto;
import com.fykj.scaffold.portal_website.domain.entity.VideoCategory;
import com.fykj.scaffold.portal_website.domain.params.VideoCategoryParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  视频栏目Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface VideoCategoryMapper extends BaseMapper<VideoCategory> {

    /**
     * 分页查询视频
     * @param page
     * @param params
     * @return
     */
    IPage<VideoCategory> pageForVideoContent(Page<VideoCategory> page, @Param("params") VideoCategoryParams params);
    /**
     * api分页查询视频
     * @param page
     * @param params
     * @return
     */
    IPage<VideoCategory> pageForApi(Page<VideoCategory> page, @Param("params") VideoCategoryParams params);

    List<VideoCategoryDto> getTrainVideo();
}
