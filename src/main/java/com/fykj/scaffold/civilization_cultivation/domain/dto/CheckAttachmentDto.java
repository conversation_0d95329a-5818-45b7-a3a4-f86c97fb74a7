package com.fykj.scaffold.civilization_cultivation.domain.dto;

import com.alibaba.fastjson.TypeReference;
import com.fykj.scaffold.support.utils.ListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 文明培育核实附件模型
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("文明培育核实附件模型")
public class CheckAttachmentDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;

    @ApiModelProperty(value = "附件id")
    private String id;

    @ApiModelProperty(value = "附件名称")
    private String name;

    @ApiModelProperty(value = "附件地址")
    private String path;

    @ApiModelProperty(value = "上传时间")
    private String uploadTime;

    /**
     * json处理器
     */
    public static class AttachmentListTypeHandler extends ListTypeHandler<CheckAttachmentDto> {

        @Override
        protected TypeReference<List<CheckAttachmentDto>> specificType() {
            return new TypeReference<List<CheckAttachmentDto>>() {
            };
        }

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CheckAttachmentDto that = (CheckAttachmentDto) o;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(path, that.path) && Objects.equals(uploadTime, that.uploadTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, path, uploadTime);
    }
}
