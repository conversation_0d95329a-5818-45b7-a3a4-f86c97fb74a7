package com.fykj.scaffold.advanced_unit.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 文明单位-导入DTO
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@ApiModel("文明单位数据导入模型")
@EqualsAndHashCode(callSuper = false)
public class AuUnitImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "单位名称")
    private String unitName;

    @ExcelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ExcelProperty(value = "联系人")
    private String contactPerson;

    @ExcelProperty(value = "联系方式")
    private String contactPhone;

    @ExcelProperty(value = "详细地址")
    private String address;

    @ExcelIgnore
    private String honorLevel;

    @ExcelProperty(value = "荣誉等级")
    private String honorLevelName;

    @ExcelIgnore
    private String unitNature;

    @ExcelProperty(value = "单位性质")
    private String unitNatureName;

    @ExcelIgnore
    private String unitType;

    @ExcelProperty(value = "单位类型")
    private String unitTypeName;

    @ExcelProperty(value = "单位简介")
    private String introduction;

    @ExcelProperty(value = "导入失败原因")
    private String reason;

}
