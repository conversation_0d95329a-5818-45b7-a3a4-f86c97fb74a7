package com.fykj.scaffold.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.mall.domain.dto.StockReplenishPlanDto;
import com.fykj.scaffold.mall.domain.entity.MallStock;
import com.fykj.scaffold.mall.domain.entity.MallStockReplenishPlan;
import com.fykj.scaffold.mall.domain.entity.MallStockReplenishPlanDetail;
import com.fykj.scaffold.mall.domain.params.MallStockReplenishPlanParams;
import com.fykj.scaffold.mall.mapper.MallStockReplenishPlanMapper;
import com.fykj.scaffold.mall.service.IMallStockReplenishPlanDetailService;
import com.fykj.scaffold.mall.service.IMallStockReplenishPlanService;
import com.fykj.scaffold.mall.service.IMallStockService;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.fykj.scaffold.mall.cons.MallCons.STOCK_REPLENISH_PLAN_TYPE_COMMON;
import static com.fykj.scaffold.mall.cons.MallCons.STOCK_REPLENISH_PLAN_TYPE_MACHINE;
import static com.fykj.scaffold.support.conns.Cons.RoleCode.ROLE_CODE_ASSOCIATION_ADMIN;
import static com.fykj.scaffold.support.conns.Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN;
import static constants.Mark.COMMA;

/**
 * <p>
 * 物料进补计划管理服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Service
public class MallStockReplenishPlanServiceImpl extends BaseServiceImpl<MallStockReplenishPlanMapper, MallStockReplenishPlan> implements IMallStockReplenishPlanService {

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IMallStockReplenishPlanDetailService detailService;

    @Autowired
    private IMallStockService stockService;

    @Override
    public List<StockReplenishPlanDto> getPlanList(MallStockReplenishPlanParams params) {
        String managerCapacity = Oauth2Util.getManagerCapacity();
        if (ROLE_CODE_ASSOCIATION_ADMIN.equals(managerCapacity)) {
            return dealAssociationAdminData(params.getOrgCode(), params.getYear(), params.getType(), params.getStatus());
        }
        if (ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(managerCapacity)) {
            return dealSubAssociationAdminData(params.getYear(), params.getType(), params.getStatus());
        }
        throw new BusinessException(ResultCode.FAIL, "您没有权限查看年度补货计划！");
    }

    private List<StockReplenishPlanDto> dealAssociationAdminData(String paramsOrgCode, String paramsYear, String type, Integer status) {
        List<StockReplenishPlanDto> result = new ArrayList<>();
        List<SysOrg> orgList = orgService.getAreaSubAssociation(true, false);
        if (StringUtil.isNotEmpty(paramsOrgCode)) {
            orgList = orgList.stream().filter(it -> it.getCode().equals(paramsOrgCode)).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(orgList)) {
            return null;
        }
        orgList.forEach(it -> result.add(generateReplenishPlanDto(paramsYear, type, status, it)));
        return result;
    }

    private List<StockReplenishPlanDto> dealSubAssociationAdminData(String paramsYear, String type, Integer status) {
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            return null;
        }
        SysOrg org = orgService.getByCode(orgCode);
        if (ObjectUtil.isEmpty(org)) {
            return null;
        }
        List<StockReplenishPlanDto> result = new ArrayList<>();
        result.add(generateReplenishPlanDto(paramsYear, type, status, org));
        return result;
    }

    private StockReplenishPlanDto generateReplenishPlanDto(String paramsYear, String type, Integer status, SysOrg org) {
        StockReplenishPlanDto dto = new StockReplenishPlanDto();
        dto.setOrgCode(org.getCode());
        dto.setOrgName(org.getName());
        List<MallStockReplenishPlan> planList = lambdaQuery()
                .eq(MallStockReplenishPlan::getPublishOrgCode, dto.getOrgCode())
                .eq(StringUtil.isNotEmpty(paramsYear), MallStockReplenishPlan::getPlanYear, paramsYear)
                .eq(StringUtil.isNotEmpty(type), MallStockReplenishPlan::getType, type)
                .eq(ObjectUtil.isNotEmpty(status), MallStockReplenishPlan::getSubmit, ObjectUtil.isNotEmpty(status) ? (status == 0 ? Boolean.FALSE : Boolean.TRUE) : null)
                .orderByDesc(Arrays.asList(MallStockReplenishPlan::getPlanYear, MallStockReplenishPlan::getSubmit, MallStockReplenishPlan::getCreateDate))
                .list();
        if (CollectionUtil.isNotEmpty(planList)) {
            dto.getPlanList().addAll(planList);
        }
        return dto;
    }

    @Override
    public MallStockReplenishPlan getReplenishPlanById(Long id) {
        MallStockReplenishPlan plan = super.getById(id);
        List<MallStockReplenishPlanDetail> detailList = detailService.lambdaQuery().eq(MallStockReplenishPlanDetail::getPlanId, id).orderByAsc(MallStockReplenishPlanDetail::getSequence).list();
        if (CollectionUtil.isEmpty(detailList)) {
            return plan;
        }
        List<MallStock> stocks = stockService.getStockByOrg();
        detailList.forEach(it -> {
            MallStock stock = stocks.stream().filter(im -> im.getId().equals(it.getStockId())).findAny().orElse(null);
            if (ObjectUtil.isEmpty(stock)) {
                it.setStockDeleted(Boolean.TRUE);
            } else {
                it.setStockDeleted(Boolean.FALSE);
            }
        });
        plan.getDetailList().addAll(detailList);
        return plan;
    }

    @Override
    public boolean saveOrUpdateDraft(MallStockReplenishPlan entity) {
        dealPublishOrg(entity);
        entity.setSubmit(Boolean.FALSE);
        saveOrUpdateHandle(entity);
        return true;
    }

    @Override
    public boolean submit(MallStockReplenishPlan entity) {
        dealPublishOrg(entity);
        if (existSubmittedRecord(entity.getId(), entity.getType(), entity.getPlanYear(), entity.getPublishOrgCode(), entity.getMachineId())) {
            throw new BusinessException(ResultCode.FAIL, "您所在组织当前年度已提交相应的" + (STOCK_REPLENISH_PLAN_TYPE_COMMON.equals(entity.getType()) ? "年度补货计划" : "兑换机补货计划") + "，请勿重复提交！");
        }
        entity.setSubmit(Boolean.TRUE);
        saveOrUpdateHandle(entity);
        return true;
    }

    @Override
    public boolean submitById(Long id) {
        MallStockReplenishPlan exist = super.getById(id);
        if (existSubmittedRecord(exist.getId(), exist.getType(), exist.getPlanYear(), exist.getPublishOrgCode(), exist.getMachineId())) {
            throw new BusinessException(ResultCode.FAIL, "您所在组织当前年度已提交相应的" + (STOCK_REPLENISH_PLAN_TYPE_COMMON.equals(exist.getType()) ? "年度补货计划" : "兑换机补货计划") + "，请勿重复提交！");
        }
        exist.setSubmit(Boolean.TRUE);
        return super.updateById(exist);
    }

    private boolean existSubmittedRecord(Long id, String type, String planYear, String orgCode, Long machineId) {
        return lambdaQuery().ne(ObjectUtil.isNotEmpty(id), MallStockReplenishPlan::getId, id)
                .eq(MallStockReplenishPlan::getSubmit, Boolean.TRUE)
                .eq(MallStockReplenishPlan::getType, type)
                .eq(MallStockReplenishPlan::getPlanYear, planYear)
                .eq(MallStockReplenishPlan::getPublishOrgCode, orgCode)
                .eq(ObjectUtil.isNotEmpty(machineId), MallStockReplenishPlan::getMachineId, machineId)
                .exists();
    }

    private void dealPublishOrg(MallStockReplenishPlan entity) {
        String managerCapacity = Oauth2Util.getManagerCapacity();
        if (!ROLE_CODE_ASSOCIATION_ADMIN.equals(managerCapacity) && !ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(managerCapacity)) {
            throw new BusinessException(ResultCode.FAIL, "您没有管理年度补货计划数据的权限！");
        }
        String orgCode = Oauth2Util.getOrgCode();
        SysOrg org = orgService.getByCode(orgCode);
        entity.setPublishOrgCode(orgCode);
        entity.setPublishOrgName(org.getName());
    }

    private void saveOrUpdateHandle(MallStockReplenishPlan entity) {
        super.saveOrUpdate(entity);
        Long planId = entity.getId();
        List<MallStockReplenishPlanDetail> detailList = CollectionUtil.isEmpty(entity.getDetailList()) ? null : entity.getDetailList().stream().filter(it -> ObjectUtil.isNotEmpty(it.getStockId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(detailList)) {
            return;
        }
        int sequence = 0;
        for (MallStockReplenishPlanDetail it : detailList) {
            it.setPlanId(planId);
            it.setSequence(sequence++);
        }
        detailService.saveOrUpdateBatch(detailList);
        List<MallStockReplenishPlanDetail> exists = detailService.lambdaQuery().eq(MallStockReplenishPlanDetail::getPlanId, planId).list();
        if (CollectionUtil.isEmpty(exists)) {
            return;
        }
        List<Long> needRemoveIds = exists.stream()
                .map(MallStockReplenishPlanDetail::getId)
                .filter(id -> !detailList.stream()
                        .map(MallStockReplenishPlanDetail::getId)
                        .collect(Collectors.toList())
                        .contains(id))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(needRemoveIds)) {
            return;
        }
        detailService.removeBatchByIds(needRemoveIds);
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        super.removeByIds(list);
        detailService.lambdaUpdate().in(MallStockReplenishPlanDetail::getPlanId, list).remove();
        return true;
    }

    @Override
    public MallStockReplenishPlan generatePlan(String type, BigDecimal budgetAmount, String year, Long machineId) {
        // 校验权限，参数是否符合生成规则
        validateEnableGenerate(type, budgetAmount, machineId);
        // 按预算金额比例反映到去年的每个物料数量上，生成新的补货计划
        return generatePlanByProportionalMethod(type, budgetAmount, year, machineId);
    }

    private void validateEnableGenerate(String type, BigDecimal budgetAmount, Long machineId) {
        String managerCapacity = Oauth2Util.getManagerCapacity();
        if (!ROLE_CODE_ASSOCIATION_ADMIN.equals(managerCapacity) && !ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(managerCapacity)) {
            throw new BusinessException(ResultCode.FAIL, "您没有生成补货计划的权限！");
        }
        if (!STOCK_REPLENISH_PLAN_TYPE_COMMON.equals(type) && !STOCK_REPLENISH_PLAN_TYPE_MACHINE.equals(type)) {
            throw new BusinessException(ResultCode.FAIL, "请传入正确的补货计划类型！");
        }
        if (ObjectUtil.isEmpty(budgetAmount) || BigDecimal.ZERO.equals(budgetAmount)) {
            throw new BusinessException(ResultCode.FAIL, "请传入预算金额，方能生成补货计划！");
        }
        if (STOCK_REPLENISH_PLAN_TYPE_MACHINE.equals(type) && ObjectUtil.isEmpty(machineId)) {
            throw new BusinessException(ResultCode.FAIL, "兑换机补货计划生成需传入兑换机id！");
        }
    }

    private MallStockReplenishPlan generatePlanByProportionalMethod(String type, BigDecimal budgetAmount, String year, Long machineId) {
        // 获取去年计划作为参照
        MallStockReplenishPlan reference = getLastYearPlan(type, budgetAmount, year, machineId);
        try {
            BigDecimal refActualAmount = reference.getActualAmount();
            List<MallStock> stocks = stockService.list();
            if (BigDecimal.ZERO.equals(refActualAmount)) {
                return reference;
            }
            BigDecimal ratio = budgetAmount.divide(refActualAmount, 4, RoundingMode.UP);
            reference.getDetailList().forEach(it -> dealDetailData(stocks, it, ratio, type));
            return reference;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "年度补货计划生成失败！");
        }
    }

    private void dealDetailData(List<MallStock> stocks, MallStockReplenishPlanDetail detail, BigDecimal ratio, String type) {
        MallStock stock = stocks.stream().filter(im -> im.getId().equals(detail.getStockId())).findAny().orElse(null);
        if (STOCK_REPLENISH_PLAN_TYPE_COMMON.equals(type)) {
            dealCommonPlan(detail, ratio, stock);
        } else {
            dealMachinePlan(detail, ratio, stock);
        }
    }

    private void dealCommonPlan(MallStockReplenishPlanDetail detail, BigDecimal ratio, MallStock stock) {
        int oriRepQua = detail.getReplenishQuantity();
        BigDecimal oriUnitPrice = detail.getUnitPrice();
        BigDecimal curUnitPrice = stock.getUnitPrice();
        if (ObjectUtil.isEmpty(stock)) {
            detail.setStockDeleted(Boolean.TRUE);
            detail.setInventoryQuantity(0);
            detail.setInventoryTotalQuantity(0);
            detail.setReplenishQuantity(BigDecimal.valueOf(oriRepQua).multiply(ratio).setScale(0, RoundingMode.HALF_UP).intValue());
            return;
        }
        detail.setStockName(stock.getName());
        detail.setStockSku(stock.getSku());
        detail.setUnitPrice(stock.getUnitPrice());
        detail.setInventoryQuantity(stock.getInventoryQuantity());
        detail.setInventoryTotalQuantity(stock.getInventoryTotalQuantity());
        detail.setReplenishQuantity(BigDecimal.ZERO.equals(curUnitPrice) ? oriRepQua : BigDecimal.valueOf(oriRepQua).multiply(oriUnitPrice.multiply(ratio)).divide(curUnitPrice, 0, RoundingMode.HALF_UP).intValue());
    }

    private void dealMachinePlan(MallStockReplenishPlanDetail detail, BigDecimal ratio, MallStock stock) {
        int oriRepQua = detail.getReplenishQuantity();
        int oriActualUnitQua = detail.getActualUnitQuantity();
        String monthNum = detail.getMonthNumSet();
        List<Integer> oriMonthQuaList = StringUtil.isEmpty(monthNum) ? null :
                Arrays.stream(monthNum.split(COMMA)).map(it -> {
                    int parse;
                    try {
                        parse = Integer.parseInt(it);
                    } catch (Exception e) {
                        parse = 0;
                    }
                    return parse;
                }).collect(Collectors.toList());
        BigDecimal oriUnitPrice = detail.getUnitPrice();
        BigDecimal curUnitPrice = stock.getUnitPrice();
        if (ObjectUtil.isEmpty(stock)) {
            detail.setStockDeleted(Boolean.TRUE);
            detail.setActualUnitQuantity(BigDecimal.valueOf(oriActualUnitQua).multiply(ratio).setScale(0, RoundingMode.HALF_UP).intValue());
            detail.setReplenishQuantity(BigDecimal.valueOf(oriRepQua).multiply(ratio).setScale(0, RoundingMode.HALF_UP).intValue());
            detail.setMonthNumSet(oriMonthQuaList == null || oriMonthQuaList.size() != 12 ? null :
                    oriMonthQuaList.stream().map(it -> BigDecimal.valueOf(it).multiply(ratio).setScale(0, RoundingMode.HALF_UP).toString()).collect(Collectors.joining(COMMA)));
            return;
        }
        detail.setStockName(stock.getName());
        detail.setStockSku(stock.getSku());
        detail.setUnitPrice(stock.getUnitPrice());
        if (BigDecimal.ZERO.equals(curUnitPrice)) {
            return;
        }
        detail.setActualUnitQuantity(BigDecimal.valueOf(oriActualUnitQua).multiply(oriUnitPrice.multiply(ratio)).divide(curUnitPrice, 0, RoundingMode.HALF_UP).intValue());
        detail.setReplenishQuantity(BigDecimal.valueOf(oriRepQua).multiply(oriUnitPrice.multiply(ratio)).divide(curUnitPrice, 0, RoundingMode.HALF_UP).intValue());
        detail.setMonthNumSet(oriMonthQuaList == null || oriMonthQuaList.size() != 12 ? null :
                oriMonthQuaList.stream().map(it -> BigDecimal.valueOf(it).multiply(oriUnitPrice.multiply(ratio)).divide(curUnitPrice, 0, RoundingMode.HALF_UP).toString()).collect(Collectors.joining(COMMA)));
    }

    private MallStockReplenishPlan getLastYearPlan(String type, BigDecimal budgetAmount, String year, Long machineId) {
        String lastYear;
        if (StringUtil.isEmpty(year)) {
            int currentYear = LocalDate.now().getYear();
            lastYear = String.valueOf(currentYear - 1);
        } else {
            lastYear = String.valueOf(Integer.parseInt(year) - 1);
        }
        String orgCode = Oauth2Util.getOrgCode();
        List<MallStockReplenishPlan> lastYearPlanList = lambdaQuery()
                .eq(MallStockReplenishPlan::getType, type)
                .eq(MallStockReplenishPlan::getPlanYear, lastYear)
                .eq(MallStockReplenishPlan::getPublishOrgCode, orgCode)
                .eq(STOCK_REPLENISH_PLAN_TYPE_MACHINE.equals(type), MallStockReplenishPlan::getMachineId, machineId)
                .orderBy(true, false, Arrays.asList(MallStockReplenishPlan::getSubmit, MallStockReplenishPlan::getCreateDate))
                .list();
        if (CollectionUtil.isEmpty(lastYearPlanList)) {
            throw new BusinessException(ResultCode.FAIL, "未获取到去年的补货计划，无法生成今年的补货计划，请您自行编辑补货计划！");
        }
        MallStockReplenishPlan result = null;
        for (MallStockReplenishPlan it : lastYearPlanList) {
            List<MallStockReplenishPlanDetail> refStocks = detailService.lambdaQuery().eq(MallStockReplenishPlanDetail::getPlanId, it.getId()).orderByAsc(MallStockReplenishPlanDetail::getSequence).list();
            if (CollectionUtil.isEmpty(refStocks)) {
                continue;
            }
            refStocks.forEach(im -> {
                im.setId(null);
                im.setCreateDate(null);
                im.setCreator(null);
                im.setUpdateDate(null);
                im.setUpdater(null);
                im.setVersion(null);
                im.setStockDeleted(Boolean.FALSE);
            });
            result = it;
            result.setDetailList(refStocks);
            break;
        }
        if (result == null) {
            throw new BusinessException(ResultCode.FAIL, "去年的补货计划没有具体的物料补货信息，无法生成今年的补货计划，请您自行编辑补货计划！");
        }
        result.setId(null);
        result.setCreateDate(null);
        result.setCreator(null);
        result.setUpdateDate(null);
        result.setUpdater(null);
        result.setVersion(null);
        result.setSubmit(Boolean.FALSE);
        result.setPlanYear(year);
        result.setBudgetAmount(budgetAmount);
        return result;
    }
}
