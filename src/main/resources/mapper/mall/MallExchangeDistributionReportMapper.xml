<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallExchangeDistributionReportMapper">

    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallExchangeDistributionReport">
        select * from mall_exchange_distribution_report where is_deleted=false
        <if test="params.year != null and params.year != ''">
            and year= #{params.year}
        </if>
    </select>
</mapper>