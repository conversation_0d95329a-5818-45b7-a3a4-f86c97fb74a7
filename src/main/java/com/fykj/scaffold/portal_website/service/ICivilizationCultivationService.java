package com.fykj.scaffold.portal_website.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto;
import com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation;
import com.fykj.scaffold.portal_website.domain.params.CivilizationCultivationParams;
import com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationCultivateDto;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import fykj.microservice.core.base.IBaseService;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 文明培育服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface ICivilizationCultivationService extends IBaseService<CivilizationCultivation> {

    /**
     * 网页展示分页查询文明培育
     * @param params
     * @return
     */
    IPage<CivilizationCultivation> pageForApi(CivilizationCultivationParams params);

    /**
     * 获取文明培育年份
     * @param categoryCode
     * @return
     */
    List<String> getYears(String categoryCode);

    /**
     * 网页展示分页文明培育详情
     * @param id
     * @return
     */
    CivilizationCultivation getForApi(Serializable id);

    /**
     * 文明培育上下架
     * @param id
     */
    void ground(String id);

    /**
     * 获取大屏数据列表
     * @param params
     * @return
     */
    List<CivilizationCultivateDto> getScreenDataList(DataScreenParams params);

    /**
     *
     * 文明培育大屏团队风采、志愿风采数据
     * @param categoryCode
     * @return
     */
    List<CivilizationCultivationDashboardDto> mienCockpitData(String categoryCode);
}
