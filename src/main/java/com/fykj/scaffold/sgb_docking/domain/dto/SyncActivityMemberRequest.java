package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.List;

/**
 * 推送活动参与人员请求
 */
@Data
public class SyncActivityMemberRequest {
    
    /**
     * 市平台活动ID
     */
    private String platformActivityId;
    
    /**
     * 参与人员列表
     */
    private List<ActivityMember> members;
    
    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        return JSON.toJSONString(this);
    }
    
    /**
     * 活动参与人员信息
     */
    @Data
    public static class ActivityMember {
        
        /**
         * 身份证号码
         */
        private String idCode;
        
        /**
         * 参与人姓名
         */
        private String name;
        
        /**
         * 签到状态：1-已签到，0-未签到
         */
        private String status;
        
        /**
         * 服务时长(小时)
         */
        private Double serviceHours;
    }
} 