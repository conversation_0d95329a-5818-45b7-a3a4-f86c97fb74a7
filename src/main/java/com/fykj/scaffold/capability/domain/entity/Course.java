package com.fykj.scaffold.capability.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 课程管理
 * 表对应：course
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "cu_course", autoResultMap = true)
public class Course extends BaseEntity {

    @TableField("course_name")
    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @TableField("course_category")
    @ApiModelProperty(value = "课程分类 (字典配置)")
    private String courseCategory;

    @TableField("course_type")
    @ApiModelProperty(value = "课程类型 (线上/线下) online, offline")
    private String courseType;

    @TableField("course_date")
    @ApiModelProperty(value = "课程日期")
    private LocalDate courseDate;

    @TableField("course_start_time")
    @ApiModelProperty(value = "课程开始时间 (起始时间)")
    private LocalTime courseStartTime;

    @TableField("course_end_time")
    @ApiModelProperty(value = "课程结束时间 (结束时间)")
    private LocalTime courseEndTime;

    @TableField("long_term_valid")
    @ApiModelProperty(value = "长期有效")
    private Boolean longTermValid;

    @TableField("online_course_deadline")
    @ApiModelProperty(value = "线上课程截止时间 (过期后不可学习)")
    private LocalDateTime onlineCourseDeadline;

    @TableField("course_credits")
    @ApiModelProperty(value = "课程学分 (正常签到范围内可获得学分，线上视频学习需要学习完整可获得学分)")
    private Integer courseCredits;

    @TableField("applicable_credit_range")
    @ApiModelProperty(value = "适用学分区间 字典")
    private String applicableCreditRange;

    @TableField("registration_limit")
    @ApiModelProperty(value = "报名人数控制")
    private Integer registrationLimit;

    @TableField("address")
    @ApiModelProperty(value = "地址 (地图地址，方便导航通过地图选择)")
    private String address;

    @TableField("longitude")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @TableField("latitude")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @TableField("check_in_distance")
    @ApiModelProperty(value = "签到距离")
    private BigDecimal checkInDistance;

    @TableField("contact_person")
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @TableField("contact_phone")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @TableField("description")
    @ApiModelProperty(value = "简介")
    private String description;

    @TableField("image")
    @ApiModelProperty(value = "图片")
    private String image;

    @TableField("order_num")
    @ApiModelProperty(value = "顺序")
    private Integer orderNum;

    @TableField("top")
    @ApiModelProperty(value = "置顶")
    private Boolean top;

    @TableField("publish_org")
    @ApiModelProperty(value = "发布组织 (发布人所属组织)")
    private String publishOrg;

    @TableField("outer_display")
    @ApiModelProperty(value = "是否对外展示 (默认不对外展示，需要设置对外展示后才可以)")
    private Boolean outerDisplay;

    @TableField("check_in_qrcode")
    @ApiModelProperty(value = "签到二维码 (课程需要生成二维码，使用二维码签到，微信扫码后跳转到该课程详情；根据课程时间，提前1小时及课程结束前20分钟均可签到)")
    private String checkInQrcode;

}
