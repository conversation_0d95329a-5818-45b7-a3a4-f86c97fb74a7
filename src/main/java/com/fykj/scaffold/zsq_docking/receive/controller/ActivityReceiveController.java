package com.fykj.scaffold.zsq_docking.receive.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingActivityRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingActivityRecordService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingLogService;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ActivityDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQActivityInfoDto;
import com.fykj.scaffold.zsq_docking.receive.service.IActivityReceiveFromZSQService;
import com.fykj.scaffold.zsq_docking.utils.ZSQHttpRequestUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static cn.hutool.core.text.StrPool.LF;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.ActivityDataDockingType.DT_DELETE;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;

@Slf4j
@RestController
@RequestMapping("/api/zsq/receive/activity")
@Api(tags = "知社区活动接口接收")
public class ActivityReceiveController {

    @Autowired
    private IZSQDockingLogService logService;

    @Autowired
    private IZSQDockingActivityRecordService activityDockingRecordService;

    @Autowired
    private IActivityReceiveFromZSQService activityService;

    @Autowired
    private ZSQHttpRequestUtils zsqHttpRequestUtils;

    @ApiOperation("活动对接")
    @PostMapping({"/docking"})
    public Result docking(@RequestBody ActivityDockingDto dockingInfo) {

        Long dockingRecordId = dockingInfo.getDockingRecordId();
        ZSQDockingActivityRecord dockingRecord = null;
        if (dockingRecordId != null) {
            dockingRecord = activityDockingRecordService.lambdaQuery().eq(ZSQDockingActivityRecord::getId, dockingRecordId).one();
            if (dockingRecord == null) {
                return new Result(ResultCode.NOT_FOUND.code(), "对接id{" + dockingRecordId + "}对应的对接记录不存在！");
            }
        }
        if (dockingRecord == null) {
            dockingRecord = new ZSQDockingActivityRecord();
            dockingRecord.setDockingType(DOCKING_TYPE_RECEIVE);
            dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_PENDING);
            dockingRecord.setDockingTime(LocalDateTime.now());
            dockingRecord.setDockingData(JSONObject.toJSONString(dockingInfo));
            dockingRecord.setRetry(Boolean.FALSE);
            activityDockingRecordService.save(dockingRecord);
        }
        Long activityId = dockingInfo.getActId();
        List<String> validFailMsgList = new ArrayList<>();
        if (activityId == null) {
            validFailMsgList.add("活动id未获取到！");
        }
        String operation = dockingInfo.getOperation();
        if (StringUtils.isEmpty(operation)) {
            validFailMsgList.add("活动操作类型未获取到！");
        }
        String zwtUserId = dockingInfo.getZwtUserId();
        if (StringUtils.isEmpty(zwtUserId)) {
            validFailMsgList.add("活动操作人政务通userid未获取到！");
        }
        ZSQActivityInfoDto data = null;
        if (activityId != null) {
            data = validActivityDetailResponse(validFailMsgList, activityId, operation);
        }
        dockingRecord.setActivityId(activityId);
        dockingRecord.setDockingOperation(operation);
        if (!validFailMsgList.isEmpty()) {
            String validFailMsg = String.join(LF, validFailMsgList);
            dockingRecord.setDockingStatus(2);
            dockingRecord.setDockingMsg(validFailMsg);
            activityDockingRecordService.updateById(dockingRecord);
            return Result.get(false, validFailMsg);
        }
        activityDockingRecordService.updateById(dockingRecord);
        return activityService.docking(dockingInfo, data, dockingRecord);
    }

    private ZSQActivityInfoDto validActivityDetailResponse(List<String> validFailMsgList, Long activityId, String operation) {
        JSONObject responseData;
        try {
            responseData = zsqHttpRequestUtils.getActivityDetail(activityId);
        } catch (Exception e) {
            validFailMsgList.add("知社区活动详情接口调用异常，异常原因【" + e.getMessage() + "】！");
            return null;
        }
        if (responseData == null) {
            validFailMsgList.add("知社区活动详情接口响应为空！");
            return null;
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        if (success == null || !success) {
            validFailMsgList.add("知社区活动详情接口响应异常，接口响应【" + JSON.toJSONString(responseData) + "】！");
            return null;
        }
        JSONObject obj = responseData.getJSONObject(ZSQ_API_RES_OBJ);
        log.info("知社区活动详情接口响应：{}", JSON.toJSONString(responseData));
        if (obj == null && !DT_DELETE.equals(operation)) {
            validFailMsgList.add("知社区活动详情接口响应obj为空（非删除操作），接口响应【" + JSON.toJSONString(responseData) + "】！");
            return null;
        }
        try {
            return JSONObject.toJavaObject(responseData.getJSONObject(ZSQ_API_RES_OBJ), ZSQActivityInfoDto.class);
        } catch (Exception e) {
            validFailMsgList.add("知社区活动详情接口响应obj信息转换异常，接口响应【" + JSON.toJSONString(responseData) + "】，" + "异常原因【" + e.getMessage() + "】！");
            return null;
        }
    }
}
