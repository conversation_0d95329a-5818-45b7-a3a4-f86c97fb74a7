package com.fykj.scaffold.security.business.controller;


import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.service.IDictService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.beans.vo.IdTextVo;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import utils.StringUtil;

import java.util.List;

/**
 * <p>
 * 数据字典前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@RestController
@RequestMapping("/admin/dict")
@Api(tags = "数据字典接口")
public class DictController extends BaseController<IDictService, Dict, BaseParams> {

    @GetMapping("/code")
    public JsonResult<Dict> getByCode(@RequestParam String code) {
        return new JsonResult<>(baseService.getByCode(code));
    }

    @GetMapping("/parent")
    public JsonResult<List<Dict>> findByParentCode(@RequestParam String code) {
        List<Dict> dictList = baseService.findByParentCode(code);
        return new JsonResult<>(dictList);
    }

    @GetMapping("/idTextVoList")
    public JsonResult<List<IdTextVo>> idTextVoList(@RequestParam String code) {
        return new JsonResult<>(baseService.idTextVoList(code));
    }

    @GetMapping("/findTopDict")
    public JsonResult<List<IdTextVo>> findTopDict() {
        List<IdTextVo> dictList = baseService.findTopDict();
        return new JsonResult<>(dictList);
    }

    @GetMapping("/findTopDictCode")
    public JsonResult<List<IdTextVo>> findTopDictCode() {
        List<IdTextVo> dictList = baseService.findTopDictCode();
        return new JsonResult<>(dictList);
    }

    @ApiOperation("获取全部字典树")
    @GetMapping(value = "tree")
    public JsonResult<List<Dict>> tree(@RequestParam(required = false) String parentCode, @RequestParam(required = false) Boolean includeRoot) {
        if (includeRoot == null) {
            includeRoot = true;
        }
        List<Dict> dicts;
        if (StringUtil.isEmpty(parentCode)) {
            dicts = baseService.tree();
        } else {
            dicts = baseService.tree(parentCode, includeRoot);
        }
        return new JsonResult<>(dicts);
    }

    @ApiOperation("批量添加数据字典")
    @PostMapping(value = "/batchAdd")
    public Result batchAdd(long parentId, String text) {
        baseService.batchAdd(parentId, text);
        return OK;
    }


    @SysLogMethod("查看详情")
    @ApiOperation("根据id获取")
    @GetMapping
    @Override
    public Result get(@RequestParam Long id) {
        return new JsonResult<>(this.baseService.get(id));
    }


}
