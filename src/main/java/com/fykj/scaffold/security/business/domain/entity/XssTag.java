package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Xss标签白名单
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_xss_tag")
public class XssTag extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标签名
     */
    @TableField("tag")
    private String tag;

    /**
     * 属性名
     */
    @TableField("attribute")
    private String attribute;

    /**
     * 属性值
     */
    @TableField("enforced_value")
    private String enforcedValue;

    /**
     * 协议值
     */
    @TableField("protocol")
    private String protocol;

    /**
     * 状态
     */
    @TableField("status")
    private Boolean status;
}
