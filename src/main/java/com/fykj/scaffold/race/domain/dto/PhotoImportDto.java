package com.fykj.scaffold.race.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 员工
 *
 * <AUTHOR> @email ${email}
 * @date 2022-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("作品导入失败模板")
public class PhotoImportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "奖项",index = 0)
    private String prizeName;

    @ExcelProperty(value = "作品名称",index = 1)
    private String photoName;

    @ExcelProperty(value = "作品编号",index = 2)
    private String number;

    @ExcelProperty(value = "作者姓名",index = 3)
    private String author;

    @ExcelProperty(value = "联系方式",index = 4)
    private String authorPhone;


    @ExcelProperty(value = "照片编号",index = 5)
    private String picNums;
    /**
    /**
     * 导入失败原因
     */
    @ExcelProperty(value = "导入失败原因",index = 6)
    private String failReason;
}
