spring:
  server:
    MaxFileSize: 10240M
    MaxRequestSize: 10240M
  application:
    name: volunteer
  profiles:
    active: dev
  thymeleaf:
    prefix: classpath:/templates/
logging:
  config: classpath:logback-boot.xml

wmyq:
  sso-auth:
    key: 3ce351dbe22f4a56949ed271bfba0e67
    secret: b0e2763f239a4d7fa91b8e5c35703ac0
    domain: http://localhost:8181
    login-api: /auth/auth/sso/login
    resource-api: /admin/sso/user/userMenusAndActionAuthorities

zwt:
  corpId: wwec70a5fa19fd89d6
  corpSecret: IWA4clQk6cT-gIwot7OzMB8K3g3kmVskijlRLzh7hOw
  agentId: 1000133
  accessTokenGetUrl: http://************/cgi-bin/gettoken?corpid=%s&corpsecret=%s
  userIdGetUrl: http://************/cgi-bin/user/getuserinfo?access_token=%s&code=%s
  userInfoGetUrl: http://************/cgi-bin/user/get?access_token=%s&userid=%s
  zwtOauth2AuthorizeUrl: http://************/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&agentid=%s#wechat_redirect
  oauth2AuthorizeRedirectUri: http://demo47.fyxmt.com/volunteer_backend/#/common/blank
  validateSecretKey: e6d7a45af67b586e

api:
  token:
    # 是否启用token验证
    enabled: true
    # token请求头名称
    header-name: X-API-TOKEN
    # token映射，key为token值(uuid)，value为名称
    keys:
      "550e8400-e29b-41d4-a716-************": "简道云"