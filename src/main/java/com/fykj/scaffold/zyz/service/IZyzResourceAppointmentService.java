package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.dto.AppointmentAuditExportDto;
import com.fykj.scaffold.zyz.domain.dto.ResourceAppointmentDto;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceAppointmentParams;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzResourceAppointment;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资源预约表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-03-02
 */
public interface IZyzResourceAppointmentService extends IService<ZyzResourceAppointment>, IBaseService<ZyzResourceAppointment> {

    /**
     * 新增预约
     * @param appointment
     * @return
     */
    boolean saveAppointment(ZyzResourceAppointment appointment);

    /**
     * 校验当前组织或团队是否预约过该资源
     * @param resourceId
     * @return
     */
    boolean validateAppointed(String resourceId);

    IPage<ZyzResourceAppointment> pagesForAudit(ZyzResourceAppointmentParams params);

    /**
     * 获取资源对接预约待审核的数量
     * @return
     */
    Integer getResAppointmentWaitAuditNum();

    /**
     * 资源预约审核
     *
     */
    void audit(ResourceAppointmentDto appointmentDto);

    List<AppointmentAuditExportDto> listForAppointmentAudit(ZyzResourceAppointmentParams params);

    /**
     * 获取预约记录
     * @param resourceId
     * @return
     */
    List<ZyzResourceAppointment> getAppointmentByResourceId(Long resourceId);

    ZyzResourceAppointment getAppointmentById(Long id);

    /**
     * 评价
     * @param id 需求Id
     * @param evaluateRemark 评价内容
     * @param evaluateStar 评价星级
     * @return
     */
    boolean updateRemark(Long id, String evaluateRemark, BigDecimal evaluateStar);

    /**
     * 获取需要进行实践单同步的资源预约id
     * @return
     */
    List<Long> getNeedPracticeSheetSync();

    /**
     * 获取组织/团队资源预约数量
     * @param teamAppointment
     * @param orgCode
     * @param teamId
     * @return
     */
    Integer getAppointmentResNum(Boolean teamAppointment, String orgCode, Long teamId);
}

