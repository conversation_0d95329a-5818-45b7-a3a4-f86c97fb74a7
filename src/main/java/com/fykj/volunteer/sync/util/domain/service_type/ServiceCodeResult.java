package com.fykj.volunteer.sync.util.domain.service_type;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

/**
 *   <info>
 *     <code>SUCCESS</code>
 *     <msg>成功</msg>
 *     <typeId>771</typeId>
 *     <typeName>理论政策</typeName>
 *     <fatherId>0</fatherId>
 *     <nodePath>1</nodePath>
 *   </info>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class ServiceCodeResult {
    //类型id
    private String typeId;
    //类型名称
    private String typeName;
    //父id
    private String fatherId;
    //层级
    private String nodePath;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "ps")
    public static class ServiceCodePs {
        private List<ServiceCodeResult> info = new ArrayList<>();
    }
}
