package com.fykj.scaffold.zyz.controller.mini;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementParams;
import com.fykj.scaffold.zyz.service.IZyzRequirementService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.Arrays;

@Slf4j
@RestController
@RequestMapping("/admin/mini/requirement")
@Api(tags = "微信小程序需求接口--需登录")
public class MiniRequirementController {


    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzVolunteerService volunteerService;


    /**
     * 1.判断是否需要对接  -- 对这个需求对接过的，无法展示 判断是否为管理员账号
     * 2.对接  -- 志愿者和游客无法对接
     * 3. 我的对接
     */

    @ApiOperation("是否展示对接按钮 -- 小程序")
    @GetMapping("/showDocking")
    public Result isShow(Long reqId) {
        ZyzRequirement requirement = requirementService.getById(reqId);
        //若需求对接组织不为空，则表示已对接
        if (ObjectUtil.isNotEmpty(requirement.getDockingOrgCode())) {
            return new JsonResult<>(new Result(-3, "需求已对接"));
        }
        if (Arrays.stream(Cons.RoleCode.MANAGER_CAPACITY_ROLE).noneMatch(it -> it.equals(Oauth2Util.getManagerCapacity()))) {
            return new JsonResult<>(new Result(-4, "个人身份无法对接"));
        }
        ZyzVolunteer volunteer = volunteerService.getByPhone(Oauth2Util.getMobile());
        if (volunteer == null || !volunteer.getPerfect()) {
            return new JsonResult<>(new Result(-1, "信息未完善"));
        }
        if (!volunteer.getCertification()) {
            return new JsonResult<>(new Result(-2, "用户未实名"));
        }
        return new Result();
    }

    @AuditLog("需求对接")
    @ApiOperation("需求对接")
    @GetMapping("/docking")
    public Result docking(@RequestParam String id, @RequestParam String contactPerson, @RequestParam String contactPhone) {
        requirementService.docking(id, contactPerson, contactPhone);
        return new Result();
    }


    @ApiOperation("我的对接分页查询")
    @PostMapping({"/pagesForMyDocking"})
    public JsonResult<IPage<ZyzRequirement>> pagesForMyDocking(@RequestBody(required = false) ZyzRequirementParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzRequirementParams();
        }
        IPage<ZyzRequirement> result = requirementService.pagesForMyDocking(params);
        return new JsonResult<>(result);
    }

}
