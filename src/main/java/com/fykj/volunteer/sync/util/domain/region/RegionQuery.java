package com.fykj.volunteer.sync.util.domain.region;

import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 同步需求
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class RegionQuery {
    /**
     * 父级区划编号，每次只能传一条(不传则查对接本平台所有区划)
     */
    private String parentCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "regGet")
    public static class Req {
        private RegionQuery info;
    }

    public String buildXml() {
        Req req = new Req();
        req.setInfo(this);
        return XmlUtils.toStr(req);
    }
}
