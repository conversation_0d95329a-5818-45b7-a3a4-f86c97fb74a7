package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 推送活动信息请求
 */
@Data
public class SyncActivityRequest {
    
    /**
     * 活动ID
     */
    private String activityId;
    
    /**
     * 活动名称
     */
    private String title;
    
    /**
     * 活动内容介绍
     */
    private String content;
    
    /**
     * 服务类别ID
     */
    private String typeId;
    
    /**
     * 服务开始时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String startTime;
    
    /**
     * 服务结束时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String endTime;
    
    /**
     * 发布组织ID
     */
    private String deptId;
    
    /**
     * 服务地点
     */
    private String address;
    
    /**
     * 区域编码
     */
    private String areaId;
    
    /**
     * 联系人
     */
    private String linkman;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 需要志愿者人数
     */
    private Integer needVolNum;
    
    /**
     * 标签，多个标签用英文逗号隔开
     */
    private String tags;
    
    /**
     * 项目图片URL地址
     */
    private String imgUrl;
    
    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncActivityInfo info = new SyncActivityInfo();
        info.setInfo(Collections.singletonList(this));
        return JSON.toJSONString(info);
    }
    
    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncActivityInfo {
        private List<SyncActivityRequest> info;
    }
} 