<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.security.business.mapper.SysApiCategoryMapper">

    <select id="findByParent" resultType="com.fykj.scaffold.security.business.domain.dto.CascaderDto">
        SELECT
        id as value,
        name as label
        FROM
        sys_api_category
        WHERE
        is_deleted = 0
        AND status = 1
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
        ORDER BY sequence ASC
    </select>
</mapper>