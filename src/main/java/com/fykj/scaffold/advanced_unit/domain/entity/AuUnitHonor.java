package com.fykj.scaffold.advanced_unit.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文明单位-指标标准-测评项
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("au_unit_honor")
public class AuUnitHonor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 文明单位ID
     */
    @TableField("org_id")
    @ApiModelProperty(value = "文明单位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;

    /**
     * 荣誉名称
     */
    @TableField("name")
    @ApiModelProperty(value = "荣誉名称")
    private String name;

    /**
     * 顺序
     */
    @TableField("order_num")
    @ApiModelProperty(value = "顺序")
    private Integer orderNum;

    /**
     * 图片
     */
    @TableField("image")
    @ApiModelProperty(value = "图片")
    private String image;

    @TableField("spider_id")
    @ApiModelProperty(value = "原始ID")
    private String spiderId;

}
