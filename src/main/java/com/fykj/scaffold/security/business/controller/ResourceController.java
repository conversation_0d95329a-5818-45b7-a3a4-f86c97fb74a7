package com.fykj.scaffold.security.business.controller;


import com.fykj.scaffold.security.business.domain.entity.Resource;
import com.fykj.scaffold.security.business.service.IResourceService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * <p>
 * 资源前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@RestController
@RequestMapping("/admin/resource")
@Api(tags = "资源管理接口")
public class ResourceController extends BaseController<IResourceService, Resource, BaseParams> {

    @ApiOperation("获取全部资源树")
    @GetMapping(value = "/resourceListByType")
    public JsonResult<List<Resource>> resourceListByType(String type) {
        return new JsonResult<>(baseService.lambdaQuery().eq(Resource::getType, type).orderByAsc(Resource::getSequence).list());
    }

    @ApiOperation("获取全部资源树")
    @GetMapping(value = "menuListForRolePage")
    public JsonResult<List<Resource>> menuListForRolePage() {
        List<Resource> resources = baseService.menuListForRolePage();
        return new JsonResult<>(resources);
    }

    @Override
    public Result save(@RequestBody Resource entity) {
        super.save(entity);
        return new JsonResult<>(entity);
    }

    @Override
    public Result update(@RequestBody Resource entity) {
        super.update(entity);
        return new JsonResult<>(entity);
    }
}
