package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.List;


/**
 *  活动同步
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActivitySyncTask {

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzActivityApplyService applyService;

    @Value("${act-sync-by-time-period}")
    private Boolean actSyncByTimePeriod = false;

    @XxlJob("Activity_Sync_Handler")
    public void activitySyncJobHandler() {
        XxlJobHelper.log("Activity_Sync_Handler-Job, START!");

        // 原有同步逻辑
        List<Long> ids = actSyncByTimePeriod ? actTimePeriodService.getNeedSync() : activityService.getNeedSync();
        if (CollectionUtil.isNotEmpty(ids)) {
            try {
                if (actSyncByTimePeriod) {
                    actTimePeriodService.syncBatch(ids);
                } else {
                    activityService.syncBatch(ids);
                }
            } catch (Exception e) {
                log.error("活动批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        // 原有发布招募同步逻辑
        List<Long> activityRecruitIds = actTimePeriodService.getNeedRecruitSync();
        if (CollectionUtil.isNotEmpty(activityRecruitIds)) {
            try {
                actTimePeriodService.syncRecruitBatch(activityRecruitIds);
            } catch (Exception e) {
                log.error("活动时间段发布招募批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        // 原有活动申请加入招募同步逻辑
        List<ActSyncRecordDto> needMemberSyncRecords = applyService.getNeedMemberSync(null);
        if (CollectionUtil.isNotEmpty(needMemberSyncRecords)) {
            Instant start = Instant.now();
            try {
                applyService.syncMemberBatch(needMemberSyncRecords);
            } catch (Exception e) {
                log.error("活动申请加入招募批量同步失败，失败原因：{}", e.getMessage(), e);
            }
            Instant end = Instant.now();
            log.info("批量同步加入招募用时：{}", Duration.between(start, end).toMillis());
        }
        // 原有活动志愿者服务时长同步逻辑
        List<ActSyncRecordDto> needSyncServiceTimeApplyIds = applyService.getNeedServiceTimeSync(null);
        if (CollectionUtil.isNotEmpty(needSyncServiceTimeApplyIds)) {
            Instant start = Instant.now();
            try {
                applyService.syncServiceTimeBatch(needSyncServiceTimeApplyIds);
            } catch (Exception e) {
                log.error("活动志愿者服务时长批量同步失败，失败原因：{}", e.getMessage(), e);
            }
            Instant end = Instant.now();
            log.info("批量同步服务时长用时：{}", Duration.between(start, end).toMillis());
        }

        XxlJobHelper.log("Activity_Sync_Handler-Job, END!");
    }
}
