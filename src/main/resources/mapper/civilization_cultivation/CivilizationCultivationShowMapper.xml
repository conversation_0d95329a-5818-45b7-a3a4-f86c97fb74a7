<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.civilization_cultivation.mapper.CivilizationCultivationShowMapper">

    <select id="getYears" resultType="java.lang.Integer">
        SELECT DISTINCT year
        FROM civilization_cultivation_show
        WHERE is_deleted = 0 AND type = #{type}
        ORDER BY year DESC
    </select>

    <select id="pageShow" resultType="com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow">
        SELECT
            id AS id,
            picture AS picture,
            name AS name,
            achievements AS achievements,
            out_link AS out_link,
            out_link_address AS outLinkAddress
        FROM civilization_cultivation_show
        WHERE is_deleted = 0 AND on_shelve = TRUE AND type = #{params.type} AND year = #{params.year}
        ORDER BY sequence, create_date DESC, id
    </select>

    <select id="pageShowProd" resultType="com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow">
        SELECT
            id AS id,
            picture AS picture,
            name AS name,
            achievements AS achievements,
            out_link AS out_link,
            out_link_address AS outLinkAddress
        FROM civilization_cultivation_show
        WHERE is_deleted = 0 AND on_shelve = TRUE
        ORDER BY year DESC, sequence, create_date DESC, id
    </select>

    <select id="homeBannerShow" resultType="com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow">
        SELECT
            id AS id,
            picture AS picture,
            name AS name,
            achievements AS achievements,
            out_link AS out_link,
            out_link_address AS outLinkAddress
        FROM civilization_cultivation_show
        WHERE is_deleted = 0 AND on_shelve = TRUE AND type = #{params.type} AND year = #{params.year}
        ORDER BY sequence, create_date DESC
    </select>

    <select id="homeBannerShowProd" resultType="com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow">
        SELECT
            id AS id,
            picture AS picture,
            name AS name,
            achievements AS achievements,
            out_link AS out_link,
            out_link_address AS outLinkAddress
        FROM civilization_cultivation_show
        WHERE is_deleted = 0 AND on_shelve = TRUE
        ORDER BY year DESC, sequence, create_date DESC
    </select>

    <select id="getMaxYear" resultType="java.lang.Integer">
        SELECT MAX(year)
        FROM civilization_cultivation_show
        WHERE is_deleted = 0 AND type = #{type}
    </select>

    <select id="cockpitData"
            resultType="com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto">
        SELECT
            id AS id,
            picture AS picture,
            NAME AS NAME,
            achievements AS achievements,
            year AS year,
            out_link_address AS link
        FROM
            civilization_cultivation_show
        WHERE
            is_deleted = 0
          AND on_shelve = TRUE
          AND type = #{type}
          AND year = #{year}
        ORDER BY
            sequence
    </select>

    <select id="cockpitDataProd" resultType="com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto">
        SELECT
            id AS id,
            picture AS picture,
            NAME AS NAME,
            achievements AS achievements,
            year AS year,
            out_link_address AS link
        FROM
            civilization_cultivation_show
        WHERE
            is_deleted = 0
          AND on_shelve = TRUE
        ORDER BY year DESC, sequence, create_date DESC
    </select>
</mapper>
