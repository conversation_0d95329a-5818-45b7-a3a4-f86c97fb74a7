package com.fykj.scaffold.ledger.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.ledger.domain.dto.DepartmentImportDto;
import com.fykj.scaffold.ledger.domain.dto.ItemImportDto;
import com.fykj.scaffold.ledger.domain.params.LedgerDepartmentParams;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.ledger.domain.entity.LedgerDepartment;
import fykj.microservice.core.beans.vo.IdTextVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 台账责任局办-服务类
 *
 * @date 2025-05-26
 */
public interface ILedgerDepartmentService extends IBaseService<LedgerDepartment> {

    /**
     * 分页
     *
     * @param params
     * @return
     */
    IPage<LedgerDepartment> pages(LedgerDepartmentParams params);

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    LedgerDepartment get(Long id);

    /**
     * 保存部门
     *
     * @param department
     * @return
     */
    boolean saveDepartment(LedgerDepartment department);

    /**
     * 保存部门
     *
     * @param department
     * @return
     */
    boolean updateDepartmentById(LedgerDepartment department);

    /**
     * 获取map集合
     *
     * @return
     */
    Map<Long, String> getDepartmentMap();

    /**
     * 获取name对应id map集合
     *
     * @return
     */
    Map<String, Long> getDepartmentNameMap();

    /**
     * 校验用户是否是局办管理员
     *
     * @return
     */
    boolean validUser();

    /**
     * 获取用户对应的局办
     *
     * @param userId
     * @return
     */
    LedgerDepartment getByUserId(Long userId);

    /**
     * 上下架
     *
     * @param id
     */
    void setStatus(Long id);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    boolean deleteById(Long id);

    /**
     * 导入
     *
     * @param excel
     * @return
     */
    List<DepartmentImportDto> dataImport(MultipartFile excel);


    /**
     * 下拉列表
     *
     * @return
     */
    List<IdTextVo> selectList();

    /**
     * 获取部门下的负责人手机号
     *
     * @param departmentIdList
     * @return
     */
    List<String> getMobileByDepartmentIdList(List<String> departmentIdList);
}

