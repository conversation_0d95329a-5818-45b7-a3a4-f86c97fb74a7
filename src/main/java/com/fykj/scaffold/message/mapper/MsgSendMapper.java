package com.fykj.scaffold.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.param.MsgSendParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  消息发送mapper
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface MsgSendMapper extends BaseMapper<MsgSend> {

    /**
     * 分页查询消息发送
     * @param page
     * @param params
     * @return
     */
    IPage<MsgSend> pageForMsgSend(IPage<MsgSend> page, @Param("params") MsgSendParams params);

    /**
     * 消息总发送数
     * @param msgSendIds
     * @return
     */
    List<MsgRecord> totalSend(@Param("msgSendIds") List<Long> msgSendIds);
}
