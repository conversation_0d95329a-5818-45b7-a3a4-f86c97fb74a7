package com.fykj.scaffold.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncWaringListDto;
import com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogParams;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogStatisticsParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 市平台信息同步日志
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
public interface ZyzSyncLogMapper extends BaseMapper<ZyzSyncLog> {

    /**
     * 分页查询
     * @param page
     * @param params
     * @return
     */
    IPage<SyncStatisticsDto> statisticsPage(IPage<SyncStatisticsDto> page, @Param("params") ZyzSyncLogStatisticsParams params);

    /**
     * 导出
     *
     * @param params
     * @return
     */
    List<ZyzSyncLogDto> exportStatistics(@Param("params") ZyzSyncLogStatisticsParams params);


    List<SyncFailStatisticsDto> statisticsFailList(@Param("params") ZyzSyncLogStatisticsParams params);

    /**
     * 获取预警类型
     *
     * @return
     */
    List<SyncWaringListDto> getWaringList(@Param("parentId") Long parentId);



    /**
     * 根据年月查询
     *
     * @param yearMon
     * @return
     */
    List<SyncStatisticsDto> getByYearMon(@Param("yearMon") String yearMon);
}
