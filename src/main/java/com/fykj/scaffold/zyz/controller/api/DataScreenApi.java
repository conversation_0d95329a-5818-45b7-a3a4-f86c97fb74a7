package com.fykj.scaffold.zyz.controller.api;

import com.fykj.scaffold.zyz.domain.dto.data_screen.*;
import com.fykj.scaffold.zyz.service.IDataScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;

@RestController
@RequestMapping("/api/data_screen")
@Slf4j
@Api(tags = "数据大屏接口")
public class DataScreenApi {

    @Autowired
    private IDataScreenService dataScreenService;

    @ApiOperation("统计1--组织架构统计")
    @GetMapping("/sum1_org_sum")
    public JsonResult<OrgNumSumDto> orgSum() {
        return new JsonResult<>(dataScreenService.orgSum());
    }

    @ApiOperation("统计2--子组织需求/资源/活动统计")
    @GetMapping("/sum2_rra_sum_by_parent_org")
    public JsonResult<List<OrgRRASumDto>> rraSumByParentOrg(@ApiParam(name = "parentOrgCode", value = "父级组织code") @RequestParam(required = false) String parentOrgCode,
                                                            @ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.rraSumByParentOrg(parentOrgCode, sumType));
    }

    @ApiOperation("统计3--组织概览统计")
    @GetMapping("/sum3_org_overview")
    public JsonResult<MultiDimensionSumDto> orgOverview(@ApiParam(name = "orgCode", value = "组织code") @RequestParam(required = false) String orgCode,
                                                        @ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.orgOverview(orgCode, sumType));
    }

    @ApiOperation("统计4--组织下服务领域活动/资源/需求数量占比统计")
    @GetMapping("/sum4_sum_belong_field_rra_num_percent")
    public JsonResult<List<NumPercentSumDto>> sumBelongFieldRRANumPercent(@ApiParam(name = "orgCode", value = "组织code") @RequestParam(required = false) String orgCode,
                                                                   @ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.sumBelongFieldRRANumPercent(orgCode, sumType));
    }

    @ApiOperation("统计5--组织下资源需求类型占比统计")
    @GetMapping("/sum5_sum_req_res_type_percent")
    public JsonResult<List<NumPercentSumDto>> sumReqResTypePercent(@ApiParam(name = "orgCode", value = "组织code") @RequestParam(required = false) String orgCode,
                                                                   @ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.sumReqResTypePercent(orgCode, sumType));
    }

    @ApiOperation("统计6--子组织需求/资源/活动统计（列表展示）")
    @GetMapping("/sum6_rra_sum_by_parent_org_table_show")
    public JsonResult<List<OrgRRASumTableDataDto>> rraSumByParentOrgTableShow(@ApiParam(name = "parentOrgCode", value = "父级组织code") @RequestParam(required = false) String parentOrgCode,
                                                                              @ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.rraSumByParentOrgTableShow(parentOrgCode, sumType));
    }

    @ApiOperation("统计7--文明实践数据统计")
    @GetMapping("/sum7_civilization_practice_sum")
    public JsonResult<CivilizationPracticeSumDto> civilizationPracticeSum(@ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.civilizationPracticeSum(sumType));
    }

    @ApiOperation("统计8--文明培育数据列表")
    @GetMapping("/sum8_get_civilization_cultivate_list")
    public JsonResult<List<CivilizationCultivateDto>> getCivilizationCultivateList(@ApiParam(name = "dimension", value = "维度:\n时代新人(NEW_GENERATION)\n身边榜样(AROUND_US_MODELS)\n童心筑梦(CHILDREN_BUILD_DREAMS)\n企业责任(ENTERPRISE_RESPONSIBILITY)", required = true) @RequestParam String dimension,
                                                                                   @ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.getCivilizationCultivateList(dimension, sumType));
    }

    @ApiOperation("统计9--文明单位数据列表")
    @GetMapping("/sum9_get_civilization_enterprise_list")
    public JsonResult<List<CivilizationEnterpriseDto>> getCivilizationEnterpriseList(@ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.getCivilizationEnterpriseList(sumType));
    }

    @ApiOperation("统计10--组织下需求/资源/活动/团队列表")
    @GetMapping("/sum10_get_req_res_act_team_list")
    public JsonResult<List<ReqResActTeamDataDto>> getReqResActTeamList(@ApiParam(name = "orgCode", value = "组织code", required = true) @RequestParam String orgCode,
                                                                       @ApiParam(name = "dimension", value = "维度:\n需求(req)/资源(res)/活动(act)/团队(team)", required = true) @RequestParam String dimension,
                                                                       @ApiParam(name = "sumType", value = "统计类型:\n本月(this_month)/本年(this_year)/累计(total)") @RequestParam(required = false) String sumType) {
        return new JsonResult<>(dataScreenService.getReqResActTeamList(orgCode, dimension, sumType));
    }

    @ApiOperation("统计11--获取进行中活动点位数据")
    @GetMapping("/sum11 get_ing_act_position_list")
    public JsonResult<List<ActPositionInfoDto>> getIngActPositionList() {
        return new JsonResult<>(dataScreenService.getIngActPositionList());
    }
}
