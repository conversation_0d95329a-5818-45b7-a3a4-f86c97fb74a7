package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 积分商城- 年度区级优秀志愿者地址收集表
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_ems_info")
public class MallEmsInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 志愿者名称
     */
    @TableField("volunteer_name")
    @ApiModelProperty(value = "志愿者名称")
    private String volunteerName;
    /**
     * 志愿者id
     */
    @TableField("volunteer_id")
    @ApiModelProperty(value = "志愿者id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long volunteerId;

    /**
     * 志愿者手机号码
     */
    @TableField("volunteer_phone")
    @ApiModelProperty(value = "志愿者手机号码")
    private String volunteerPhone;
    /**
     * 志愿者证件类型
     */
    @TableField("volunteer_certificate_type")
    @ApiModelProperty(value = "志愿者证件类型")
    private String volunteerCertificateType;
    /**
     * 志愿者证件号
     */
    @TableField("volunteer_certificate_id")
    @ApiModelProperty(value = "志愿者证件号")
    private String volunteerCertificateId;
    /**
     * 收件人姓名
     */
    @TableField("recipient_name")
    @ApiModelProperty(value = "收件人姓名")
    private String recipientName;
    /**
     * 收件人电话
     */
    @TableField("recipient_phone")
    @ApiModelProperty(value = "收件人电话 ")
    private String recipientPhone;
    /**
     * 收件人证件类型
     */
    @TableField("recipient_certificate_type")
    @ApiModelProperty(value = "收件人证件类型")
    private String recipientCertificateType;
    /**
     * 收件人证件号码
     */
    @TableField("recipient_certificate_id")
    @ApiModelProperty(value = "收件人证件号码")
    private String recipientCertificateId;
    /**
     * 省市区
     */
    @TableField("address")
    @ApiModelProperty(value = "省市区")
    private String address;
    /**
     * 详细地址
     */
    @TableField("address_detail")
    @ApiModelProperty(value = "详细地址")
    private String addressDetail;
    /**
     * 备注
     */
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 短信标题
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "短信标题")
    private String title;

}
