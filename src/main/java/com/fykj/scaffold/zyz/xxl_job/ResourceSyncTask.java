package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.service.IZyzResourceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 资源同步补偿
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ResourceSyncTask {

    @Autowired
    private IZyzResourceService resourceService;

    @XxlJob("Resource_Sync_Handler")
    public void resourceSyncJobHandler() {
        XxlJobHelper.log("Resource_Sync_Handler-Job, START!");
        // 获取待同步的资源数据以及资源审核通过时间三天内资源同步失败的数据
        List<Long> resourceIds = resourceService.getNeedSync();
        if (CollectionUtil.isNotEmpty(resourceIds)) {
            try {
                resourceService.syncBatch(resourceIds);
            } catch (Exception e) {
                log.error("资源批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }
        XxlJobHelper.log("Resource_Sync_Handler-Job, END!");
    }
}
