package com.fykj.scaffold.task.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "活动招募查询参数")
public class AutoActivityApplyParam extends BaseParams {
    @ApiModelProperty(value = "同步状态")
    private String sync;

    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;
}
