package com.fykj.scaffold.sgb_docking.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.sgb_docking.cons.SgbCons;
import com.fykj.scaffold.sgb_docking.domain.bo.SyncInfo;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncStatusRecord;
import com.fykj.scaffold.sgb_docking.domain.params.SgbSyncStatusRecordParams;
import com.fykj.scaffold.sgb_docking.mapper.SgbSyncStatusRecordMapper;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncStatusRecordService;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;


/**
 * 数据同步状态记录-服务实现类
 *
 * @date 2025-06-24
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SgbSyncStatusRecordServiceImpl extends BaseServiceImpl<SgbSyncStatusRecordMapper, SgbSyncStatusRecord>
        implements ISgbSyncStatusRecordService {

    @Override
    public void saveOrUpdateSyncStatus(Boolean syncStatus, String syncRemark,
                                       String bizType, Long bizId, String sgbId) {
        SgbSyncStatusRecord sgbSyncStatusRecord = Optional.ofNullable(getByBizTypeAndId(bizType, bizId)).orElseGet(SgbSyncStatusRecord::new);
        sgbSyncStatusRecord.setSyncTime(LocalDateTime.now());
        sgbSyncStatusRecord.setBusinessType(bizType);
        sgbSyncStatusRecord.setBusinessId(bizId);
        sgbSyncStatusRecord.setSyncStatus(syncStatus);
        sgbSyncStatusRecord.setSyncRemark(syncRemark);
        sgbSyncStatusRecord.setSgbId(sgbId);
        saveOrUpdate(sgbSyncStatusRecord);
    }

    @Override
    public SgbSyncStatusRecord getByBizTypeAndId(String bizType, Long bizId) {
        return lambdaQuery().eq(SgbSyncStatusRecord::getBusinessType, bizType)
                .eq(SgbSyncStatusRecord::getBusinessId, bizId).one();
    }

    @Override
    public boolean isBizDataSyncSuccess(String bizType, Long bizId) {
        return Optional.ofNullable(lambdaQuery().eq(SgbSyncStatusRecord::getBusinessType, bizType)
                .eq(SgbSyncStatusRecord::getBusinessId, bizId).one())
                .map(SgbSyncStatusRecord::getSyncStatus)
                .orElse(false);
    }

    @Override
    public SyncInfo determineSyncInfo(String bizType, Long bizId) {
        return Optional.ofNullable(getByBizTypeAndId(bizType, bizId))
                .filter(record -> !StrUtil.isBlank(record.getSgbId()))
                .map(record -> new SyncInfo(SgbCons.SgbSaveType.UPDATE.getValue(), record.getSgbId()))
                .orElse(new SyncInfo(SgbCons.SgbSaveType.INSERT.getValue(), null));
    }

    @Override
    public String determineSaveType(String bizType, Long bizId) {
        return determineSyncInfo(bizType, bizId).getSaveType();
    }

    @Override
    public IPage<SgbSyncStatusRecord> getPageWithBizName(SgbSyncStatusRecordParams params) {
        Page<SgbSyncStatusRecord> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.selectPageWithBizName(page, params);
    }
}
