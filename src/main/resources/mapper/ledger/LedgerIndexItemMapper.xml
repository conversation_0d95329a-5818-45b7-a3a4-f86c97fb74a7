<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.ledger.mapper.LedgerIndexItemMapper">

    <select id="getByIndexId" resultType="com.fykj.scaffold.ledger.domain.entity.LedgerIndexItem">
        SELECT
            t.*,
            i.`name` AS index_name
        FROM
            ledger_index_item t
                LEFT JOIN ledger_index i ON t.index_id = i.id
        WHERE
            t.is_deleted = 0
          AND t.index_id = #{indexId}
        ORDER BY
            t.create_date DESC
    </select>
    <select id="getIndexIdByDepartmentId" resultType="java.lang.String">
        SELECT DISTINCT
            index_id
        FROM
            ledger_index_item
        WHERE
            is_deleted = 0
          AND department_id LIKE CONCAT('%', #{departmentId}, '%')
    </select>
    <select id="getUserIndexItem" resultType="com.fykj.scaffold.ledger.domain.dto.LedgerItemDto">
        SELECT
            i.id AS index_item_id,
            i.`name` AS index_item_name,
            i.related_requirements AS related_requirements,
            IFNULL( r.`status`, 0 ) AS `status`,
            r.attachment_name,
            r.attachment_url,
            r.report_date
        FROM
            ledger_index_item i
                LEFT JOIN ledger_report r ON r.index_item_id = i.id
                AND r.department_id = #{departmentId}
        WHERE
            i.is_deleted = 0
          AND i.index_id = #{indexId}
          AND i.department_id LIKE CONCAT('%', #{departmentId}, '%')
        ORDER BY
            i.create_date DESC
    </select>
</mapper>