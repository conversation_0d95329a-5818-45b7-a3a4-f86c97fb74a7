package com.fykj.scaffold.sync.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 市平台同步职能部门
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_sync_dep")
public class ZyzSyncDep extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 职能部门id
     */
    @TableField("dep_id")
    @ApiModelProperty(value = "职能部门id")
    private String depId;

    /**
     * 职能部门名称
     */
    @TableField("dep_name")
    @ApiModelProperty(value = "职能部门名称")
    private String depName;

    /**
     * 职能部门类型id
     */
    @TableField("dep_type_id")
    @ApiModelProperty(value = "职能部门id")
    private String depTypeId;

    @TableField(exist = false)
    private String depTypeName;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;

    /**
     * 是否同步
     */
    @DictTrans(transTo = "isSyncText")
    @TableField("is_sync")
    @ApiModelProperty(value = "是否同步")
    private String isSync;

    /**
     * 是否同步
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否同步")
    private String isSyncText;

    /**
     * 同步备注
     */
    @TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 市平台ID
     */
    @TableField("sync_id")
    @ApiModelProperty(value = "市平台ID")
    private String syncId;

    /**
     * 简介
     */
    @TableField("intro")
    @ApiModelProperty(value = "简介")
    private String intro;

    /**
     * 纬度
     */
    @TableField("tx_map_lat")
    @ApiModelProperty(value = "纬度")
    private BigDecimal txMapLat;

    /**
     * 经度
     */
    @TableField("tx_map_lan")
    @ApiModelProperty(value = "经度")
    private BigDecimal txMapLan;

    /**
     * 负责人
     */
    @TableField("contact")
    @ApiModelProperty(value = "负责人")
    private String contact;

    /**
     * 负责人电话
     */
    @TableField("contact_no")
    @ApiModelProperty(value = "负责人电话")
    private String contactNo;

    /**
     * 详细地址
     */
    @TableField("map_address")
    @ApiModelProperty(value = "详细地址")
    private String mapAddress;

    /**
     * 行政区划
     */
    @TableField("region_code")
    @ApiModelProperty(value = "行政区划")
    private String regionCode;

    /**
     * 图片
     */
    @TableField("pic_logo_url")
    @ApiModelProperty(value = "图片")
    private String picLogoUrl;
}
