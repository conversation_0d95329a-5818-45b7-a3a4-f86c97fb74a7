package com.fykj.scaffold.message.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("msg_tmp")
public class MsgTmp extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 编码
     */
    @TableField("code")
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 模板
     */
    @TableField("tmp")
    @ApiModelProperty(value = "模板")
    private String tmp;

    /**
     * 启用状态
     */
    @TableField("status")
    @ApiModelProperty(value = "启用状态")
    private Boolean status;

    /**
     * 跳转url
     */
    @TableField("page_url")
    @ApiModelProperty(value = "跳转url")
    private String pageUrl;
}
