package com.fykj.volunteer.sync.util.domain.sync_spot;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步实践点
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncSpot {
    /**
     * 实践点名称
     */
    private String name;
    /**
     * 唯一号
     */
    private String uniCode;
    /**
     * 区划编号详见区划查询接口，只能传此账号平台及下属区划；
     * type=1时区划编号应为6位；
     * type=2时区划编号应为9位；
     * type=3时区划编号应为12位；
     */
    private String regionCode;
    /**
     * 负责人姓名
     */
    private String contact;
    /**
     * 负责人电话
     */
    private String contactNo;
    /**
     * 介绍
     */
    private String intro;
    /**
     * 地址
     */
    private String mapAddress;
    /**
     * 腾讯坐标-经度
     */
    private String txMapLan;
    /**
     * 腾讯坐标-纬度
     */
    private String txMapLat;
    /**
     * 阵地logo图片图片外网url地址转换为base64编码（必须带http或者https全路径）
     */
    private String picLogoUrl;
    /**
     * 接口调用类型1新增，2修改
     */
    private String saveType;
    /**
     * 阵地编号saveType为2时则此psId为必填
     */
    private String psId;

    public static class SaveType {
        /**
         * 新增
         */
        public final static String ADD = "1";

        /**
         * 修改
         */
        public final static String MODIFY = "2";
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "ps")
    public static class Ps {
        private List<SyncSpot> info;
    }

    public String buildXml() {
        SyncSpot.Ps vol = new SyncSpot.Ps();
        vol.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(vol);
    }
}
