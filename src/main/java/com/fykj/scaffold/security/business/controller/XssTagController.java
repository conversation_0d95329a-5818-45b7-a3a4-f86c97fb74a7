package com.fykj.scaffold.security.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.XssTag;
import com.fykj.scaffold.security.business.domain.params.XssTagParams;
import com.fykj.scaffold.security.business.service.IXssTagService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

/**
 * xss白名单配置
 *
 * <AUTHOR>
 * @date 2019/10/30
 */
@RestController
@RequestMapping("/admin/xss/tags")
@Api(tags = "系统-xss白名单配置")
public class XssTagController extends BaseController<IXssTagService, XssTag, XssTagParams> {

    @ApiOperation("分页查询")
    @PostMapping(value = "/pages")
    @Override
    public JsonResult<IPage<XssTag>> list(@RequestBody XssTagParams params) {
        return new JsonResult<>(baseService.findByPage(params));
    }

    @ApiOperation("新增")
    @PostMapping(value = "/save")
    @Override
    public Result save(@RequestBody XssTag info) {
        baseService.saveInfo(info);
        return new JsonResult<>();
    }

    @ApiOperation("更新方法")
    @PostMapping(value = "/update")
    @Override
    public Result update(@RequestBody XssTag info) {
        baseService.updateInfo(info);
        return new JsonResult<>();
    }
}
