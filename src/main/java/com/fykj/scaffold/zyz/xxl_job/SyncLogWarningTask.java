package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 上报日志统计警告
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SyncLogWarningTask {

    @Autowired
    private IZyzSyncLogService syncLogService;

    @XxlJob("Sync_Log_Waring_Handler")
    public void syncWaringHandler() {
        XxlJobHelper.log("Sync_Log_Waring_Handler-Job, START!");
        syncLogService.sendMsg();
        XxlJobHelper.log("Sync_Log_Waring_Handler-Job, END!");
    }
}
