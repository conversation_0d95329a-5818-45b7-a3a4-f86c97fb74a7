package com.fykj.scaffold.sync.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import fykj.microservice.core.support.excel.annotation.BoolConvert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：yangxu
 * @Date：2025/5/21 15:34
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncSpotExportDto implements Serializable {

    private static final long serialVersionUID = -3598665542330730866L;

    @ExcelProperty("职能部门名称")
    private String name;

    @ExcelProperty("行政区划")
    private String regionName;

    @ExcelProperty("负责人")
    private String contact;

    @ExcelProperty("负责人电话")
    private String contactNo;

    @ExcelProperty("描述")
    private String intro;

    @ExcelProperty("详细地址")
    private String mapAddress;

    @ExcelProperty("图片")
    private String picLogoUrl;

    @ExcelProperty("上下架状态")
    @BoolConvert(trueText = "上架", falseText = "下架")
    private Boolean onShelve;
}
