package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
@ApiModel("接收志愿者活动临时需求模型")
public class RequirementTempSyncForZyzDto implements Serializable {

    @ApiModelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 需求名称
     */
    @ApiModelProperty(value = "需求名称")
    private String name;

    /**
     * 发布组织code
     */
    @ApiModelProperty(value = "发布组织code")
    private String publishOrgCode;

    /**
     * 发布组织
     */
    @ApiModelProperty(value = "发布组织")
    private String publishOrgName;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String type;

    /**
     * 所属领域一级
     */
    @ApiModelProperty(value = "所属领域一级")
    private String belongFieldTop;

    /**
     * 所属领域一级名称
     */
    @ApiModelProperty(value = "所属领域一级名称")
    private String belongFieldNameTop;

    /**
     * 所属领域二级
     */
    @ApiModelProperty(value = "所属领域二级")
    private String belongFieldEnd;

    /**
     * 所属领域二级名称
     */
    @ApiModelProperty(value = "所属领域二级名称")
    private String belongFieldNameEnd;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private String picture;

    /**
     * 需求详情
     */
    @ApiModelProperty(value = "需求详情")
    private String detail;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 是否是团队维护
     */
    @ApiModelProperty(value = "是否是团队维护")
    private Boolean teamPublish;

    /**
     * 团队id
     */
    @ApiModelProperty(value = "团队id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    /**
     * 服务对象
     */
    @ApiModelProperty(value = "服务对象")
    private String serviceObj;

    /**
     * 服务地址
     */
    @ApiModelProperty(value = "服务地址")
    private String serviceAddress;

    /**
     * 是否自动上架
     */
    @ApiModelProperty(value = "是否自动上架")
    private Boolean autoEnable;
}