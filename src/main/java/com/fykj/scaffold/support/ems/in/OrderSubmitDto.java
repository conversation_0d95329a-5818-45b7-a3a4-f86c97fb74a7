package com.fykj.scaffold.support.ems.in;

import lombok.Data;

/**
 * 订单下定接口
 */
@Data
public class OrderSubmitDto {
    //物流订单号
    private String logisticsOrderNo;
    //内件性质，1：文件3、物品
    private int contentsAttribute = 3;
    //预约开始时间
    private String exceptStartTime;
    //预约结束时间
    private String exceptEndTime;
    //（1、实时派揽-根据预约时间上门2、定时派揽-固定频次上门按照机构配置时间分上下午推送，未维护默认上午9:00 和下午16:00 两个频次）
    private String pushMethod="1";
    //获 取 运 单 号
    //标识0否1是
    private String isQuhao="1";
    //业务产品分类 1：特快专递2：快递包裹3：代收/到付
    private int bizProductNo = 2;
    //寄件人
    private Address sender;
    //收件人
    private Address receiver;
    //上 门 取 件 人 信息
    private Delivery delivery;

    //地址信息
    @Data
    public static class Address {
        //姓名
        private String name;
        //手机
        private String mobile;
        //省份
        private String prov;
        //城市
        private String city;
        //区县
        private String county;
        //地址
        private String address;
    }
    //地址信息
    @Data
    public static class Delivery {
        //省份
        private String prov;
        //城市
        private String city;
        //区县
        private String county;
        //地址
        private String address;
    }
}
