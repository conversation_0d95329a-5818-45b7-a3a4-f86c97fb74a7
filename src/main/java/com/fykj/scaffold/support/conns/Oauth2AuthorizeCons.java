package com.fykj.scaffold.support.conns;

import constants.AuthorizeCons;

/**
 * <AUTHOR>
 */
public class Oauth2AuthorizeCons extends AuthorizeCons {

    public static final String OAUTH2_TOKEN_HEADER = "Authorization";

    public static final String GRANT_TYPE = "grant_type";

    public static final String CLIENT_CREDENTIALS = "client_credentials";

    public static final String CLIENT_ID = "client_id";

    public static final String CLIENT_SECRET = "client_secret";

    public static final String USERNAME = "username";

    public static final String PASSWORD = "password";

    public static final String SCOPE = "scope";

    public static final String ONE_CODE = "one_code";

    public static final String REFRESH_TOKEN = "refresh_token";

    public static final String SMS_CODE = "sms_code";

}
