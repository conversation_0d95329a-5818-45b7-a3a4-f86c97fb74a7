//package com.fykj.scaffold.task.service.impl;
//
//
//import cn.hutool.core.codec.Base64;
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.util.IdUtil;
//import com.fykj.scaffold.security.business.domain.entity.SysOrg;
//import com.fykj.scaffold.security.business.service.ISysOrgService;
//import com.fykj.scaffold.support.conns.Cons;
//import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;
//import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
//import com.fykj.scaffold.sync.util.SyncUtil;
//import com.fykj.scaffold.task.domain.dto.CreateActivityTimeDto;
//import com.fykj.scaffold.task.domain.entity.CopyActivity;
//import com.fykj.scaffold.task.domain.entity.CopyActivityApply;
//import com.fykj.scaffold.task.domain.entity.CopyVolunteer;
//import com.fykj.scaffold.task.domain.entity.ParameterCopyActivity;
//import com.fykj.scaffold.task.repository.CopyActivityApplyRepository;
//import com.fykj.scaffold.task.repository.CopyActivityRepository;
//import com.fykj.scaffold.task.service.CopyActivityService;
//import com.fykj.scaffold.task.service.IParameterCopyActivityService;
//import com.fykj.scaffold.zyz.util.ServiceLongTool;
//import com.fykj.volunteer.sync.util.api.ApiWebService;
//import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMember;
//import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMemberResult;
//import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActp;
//import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActpResult;
//import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActs;
//import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActsResult;
//import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTime;
//import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTimeResult;
//import constants.Mark;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import utils.LocalDateTimeUtil;
//import utils.StringUtil;
//
//import java.math.BigDecimal;
//import java.security.NoSuchAlgorithmException;
//import java.security.SecureRandom;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//import java.util.stream.Collectors;
//
//import static com.fykj.scaffold.support.conns.Cons.*;
//
///**
// * 服务实现类
// *
// * <AUTHOR>
// * @date 2023-02-15
// */
//@Service
//@Transactional(rollbackFor = Exception.class)
//@Slf4j
//public class CopyActivityServiceImpl implements CopyActivityService {
//
//    @Autowired
//    private CopyActivityRepository copyActivityRepository;
//
//    @Autowired
//    private ISysOrgService orgService;
//
//    @Autowired
//    private IZyzSyncBelongFieldDictService belongFieldDictService;
//
//    @Autowired
//    private CopyActivityApplyRepository applyRepository;
//
///*    @Autowired
//    private ElasticsearchRestTemplate elasticsearchTemplate;*/
//
//    @Autowired
//    private IParameterCopyActivityService parameterCopyActivityService;
//
//    private static Random rand;
//
//    static {
//        try {
//            rand = SecureRandom.getInstanceStrong();
//        } catch (NoSuchAlgorithmException e) {
//            log.info("Exception:{}", e.getMessage());
//        }
//    }
//
//    @Override
//    public CopyActivity saveOrUpdateCopyActivity(CopyActivity activity) {
//        return copyActivityRepository.save(activity);
//    }
//
//    @Override
//    public List<CopyActivity> saveOrUpdateCopyActivities(List<CopyActivity> list) {
//        return (List<CopyActivity>) copyActivityRepository.saveAll(list);
//    }
//
//    @Override
//    public CopyActivityApply saveOrUpdateCopyActivityApply(CopyActivityApply activity) {
//        return applyRepository.save(activity);
//    }
//
//    @Override
//    public List<CopyActivityApply> saveOrUpdateCopyActivityApplies(List<CopyActivityApply> list) {
//        return (List<CopyActivityApply>) applyRepository.saveAll(list);
//    }
//
//    @Override
//    public void syncAct(Long actId) {
//        Optional<CopyActivity> copyActivity = copyActivityRepository.findById(actId);
//        CopyActivity activity = copyActivity.orElse(new CopyActivity());
//        syncActivity(activity);
//        //查找所有的活动id下的报名信息
//        List<CopyActivityApply> applies = applyRepository.findByActivityId(actId);
//        applies.forEach(activityApply -> {
//            activityApply.setActSyncId(activity.getRecruitSyncId());
//            activityApply.setApplyTime(activity.getStartTime());
//            syncApplies(activityApply);
//        });
//        saveOrUpdateCopyActivity(activity);
//        saveOrUpdateCopyActivityApplies(applies);
//    }
//
//    @Override
//    public void syncActByParameterId(ParameterCopyActivity activity, Long parameterId) {
//        List<CopyActivity> copyActivities =
//                copyActivityRepository.findAllByParameterIdAndSync(parameterId, Cons.PlatformSyncState.WAIT_SYNC);
//        copyActivities.addAll(copyActivityRepository.findAllByParameterIdAndSync(parameterId, Cons.PlatformSyncState.FAILURE));
//        //
//        if (CollectionUtil.isEmpty(copyActivities)) {
//            activity.setSyncTaskStatus("task_ending");
//            parameterCopyActivityService.updateById(activity);
//            return;
//        }
//        copyActivities.forEach(copyActivity -> {
//            syncActivity(copyActivity);
//            List<CopyActivityApply> applies = applyRepository.findByActivityId(copyActivity.getActivityId());
//            applies.forEach(activityApply -> {
//                activityApply.setActSyncId(copyActivity.getRecruitSyncId());
//                activityApply.setApplyTime(copyActivity.getStartTime());
//                syncApplies(activityApply);
//            });
//            saveOrUpdateCopyActivityApplies(applies);
//        });
//        saveOrUpdateCopyActivities(copyActivities);
//        activity.setSyncTaskStatus("task_ending");
//        parameterCopyActivityService.updateById(activity);
//    }
//
//    /**
//     * 同步活动表相关信息
//     * @param activity 活动对象信息
//     */
//    private void syncActivity(CopyActivity activity) {
//        int actCount = 1;
//        boolean actNeedRetry = true;
//        //同步活动到市平台 重试5次
//        while (actCount <= 5 && actNeedRetry) {
//            actNeedRetry = syncAct(activity);
//            actCount++;
//        }
//        int recruitCount = 1;
//        boolean recruitNeedRetry = true;
//        //同步招募到市平台 重试5次
//        while (recruitCount <= 5 && recruitNeedRetry) {
//            recruitNeedRetry = syncRecruit(activity);
//            recruitCount++;
//        }
//    }
//
//
//    /**
//     * 同步报名表相关信息
//     * @param apply 报名对象信息
//     */
//    private void syncApplies(CopyActivityApply apply) {
//        int actMemberCount = 1;
//        boolean actMemberNeedRetry = true;
//        //同步报名到市平台 重试5次
//        while (actMemberCount <= 5 && actMemberNeedRetry) {
//            actMemberNeedRetry = syncActMember(apply);
//            actMemberCount++;
//        }
//        int serviceTimeCount = 1;
//        boolean serviceTimeNeedRetry = true;
//        //同步服务时长到市平台 重试5次
//        while (serviceTimeCount <= 5 && serviceTimeNeedRetry) {
//            serviceTimeNeedRetry = syncServiceTime(apply);
//            serviceTimeCount++;
//        }
//    }
//
//    /*@Override
//    public Double sumServiceTimeByDate(LocalDate date) {
//        QueryBuilder queryBuilder = QueryBuilders.boolQuery();
//*//*
//                .must(QueryBuilders.rangeQuery("createTime").format("yyyy-MM-dd").from(LocalDateTimeUtil.formatDate(date)).to(LocalDateTimeUtil.formatDate(date)));
//*//*
//        // 聚合查询。goodsSales是要统计的字段，sum_sales是自定义的别名
//        SumAggregationBuilder sumBuilder = AggregationBuilders.sum("sumServiceLong").field("serviceLong");
//
//        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
//                .withQuery(queryBuilder)
//                .withAggregations(sumBuilder)
//                .build();
//        SearchHits<CopyActivityApply> searchHits;
//        try {
//            searchHits = elasticsearchTemplate.search(searchQuery, CopyActivityApply.class);
//        } catch (Throwable e) {
//            throw new RuntimeException(e);
//        }
//        // 获取聚合信息
//        Aggregations aggregations = (Aggregations) searchHits.getAggregations().aggregations();
//        ParsedSum categoryNameAgg = aggregations.get("sumServiceLong");
//        return categoryNameAgg.getValue();
//    }*/
//
//    private List<CreateActivityTimeDto> createActivityTimeDtos(ParameterCopyActivity activity, List<Integer> timeNumList) {
//        List<CreateActivityTimeDto> dtoList = new ArrayList<>();
//        final LocalDate startDay = activity.getActStartDay();
//        final LocalDate endDay = activity.getActEndDay();
//        LocalDate currentDay = startDay;
//        //创建选中日期中分配完时间段生成新的日期
//        while (currentDay.isBefore(endDay) || currentDay.isEqual(endDay)) {
//            Collections.shuffle(timeNumList);
//            int createHours = 0;
//            for (int i : timeNumList) {
//                LocalTime startTime = activity.getActTimeSectionStartTime().plusHours(createHours);
//                createHours += i;
//                LocalTime endTime = activity.getActTimeSectionStartTime().plusHours(createHours);
//                CreateActivityTimeDto createActivityTimeDto = new CreateActivityTimeDto();
//                createActivityTimeDto.setStartTime(currentDay.atTime(startTime));
//                createActivityTimeDto.setEndTime(currentDay.atTime(endTime));
//                //获取所有的组合时间段
//                dtoList.add(createActivityTimeDto);
//            }
//            currentDay = currentDay.plusDays(1);
//        }
//        return dtoList;
//    }
//
//    private CopyActivity createActivity(ParameterCopyActivity activity, int signNum, CreateActivityTimeDto createActivityTimeDto, SysOrg org, ZyzSyncBelongFieldDict dict) {
//        //创建活动对象
//        CopyActivity copyActivity = new CopyActivity();
//        copyActivity.setRecruitmentNum(signNum);
//        copyActivity.setApplyNum(signNum);
//        copyActivity.setParameterId(activity.getId());
//        copyActivity.setStartTime(LocalDateTimeUtil.formatDateTime(createActivityTimeDto.getStartTime(), "yyyyMMddHHmmss"));
//        copyActivity.setEndTime(LocalDateTimeUtil.formatDateTime(createActivityTimeDto.getEndTime(), "yyyyMMddHHmmss"));
//        copyActivity.setBelongFieldEnd(dict.getTypeId());
//        //名称生成规则为社区名称+所属领域二级名称+自定义字段+日期yyyyMMdd
//        copyActivity.setName(org.getName() + dict.getTypeName() + (StringUtil.isEmpty(activity.getCustomFields()) ? "" : activity.getCustomFields()) + LocalDateTimeUtil.formatDate(activity.getActStartDay(), "yyyyMMdd"));
//        copyActivity.setAddress(org.getAddress());
//        copyActivity.setContactPerson(org.getLinkMan());
//        copyActivity.setContactPhone(org.getLinkMobile());
//        copyActivity.setLatitude(org.getLatitude());
//        copyActivity.setApplyStartTime(copyActivity.getStartTime());
//        copyActivity.setLongitude(org.getLongitude());
//        copyActivity.setActivityId(IdUtil.getSnowflake().nextId());
//        //这边加入线程池待加入
////            syncAct(copyActivity, 5);
//        copyActivity.setSync(Cons.PlatformSyncState.WAIT_SYNC);
//        copyActivity.setCreateDate(LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
//        return copyActivity;
//    }
//
//    private CopyActivityApply createActivityApply(CopyVolunteer volunteer, CopyActivity copyActivity, Long parameterId) {
//        //创建报名对象及时长信息
//        CopyActivityApply activityApply = new CopyActivityApply();
//        activityApply.setActivityApplyId(IdUtil.getSnowflake().nextId());
//        activityApply.setActivityName(copyActivity.getName());
//        activityApply.setActSyncId(copyActivity.getSyncId());
//        activityApply.setApplyTime(copyActivity.getApplyStartTime());
//        activityApply.setCertificateId(volunteer.getCertificateId());
//        activityApply.setActivityId(copyActivity.getActivityId());
//        activityApply.setVolunteerId(volunteer.getVolunteerId());
//        activityApply.setVolunteerName(volunteer.getName());
//        activityApply.setVolunteerPhone(volunteer.getPhone());
//        activityApply.setParameterId(parameterId);
//        activityApply.setCreateTime(LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
//        activityApply.setApplyIsSync(Cons.PlatformSyncState.WAIT_SYNC);
//        activityApply.setActEndTime(copyActivity.getEndTime());
//        BigDecimal serviceLong = ServiceLongTool.calculateServiceLong(LocalDateTimeUtil.parseStringToDateTime(copyActivity.getStartTime(), "yyyyMMddHHmmss"), LocalDateTimeUtil.parseStringToDateTime(copyActivity.getEndTime(), "yyyyMMddHHmmss"));
//        activityApply.setServiceLong(serviceLong);
//        activityApply.setServiceLongIsSync(Cons.PlatformSyncState.WAIT_SYNC);
//        return activityApply;
//    }
//
//    @Override
//    public void createActivityPlan(ParameterCopyActivity activity) {
//        List<Integer> timeNumList = Arrays.stream(activity.getActTGroupSection().split(Mark.COMMA)).map(Integer::parseInt).collect(Collectors.toList());
//        List<CreateActivityTimeDto> dtoList = createActivityTimeDtos(activity, timeNumList);
//        List<CopyActivity> activityList = new ArrayList<>();
//        Collections.shuffle(dtoList);
//        List<CreateActivityTimeDto> newRealActDateTime = activity.getActivityNums() == dtoList.size() ? dtoList : dtoList.subList(0, activity.getActivityNums());
//        List<SysOrg> orgList = orgService.lambdaQuery().eq(SysOrg::getLevel, 3).list();
//        List<ZyzSyncBelongFieldDict> dictList = belongFieldDictService.lambdaQuery().ne(ZyzSyncBelongFieldDict::getFatherId, "0").list();
//        List<CopyActivityApply> applies = new ArrayList<>();
//        List<CopyVolunteer> volunteerList = activity.getVolunteerList();
//        //造活动
//        for (CreateActivityTimeDto createActivityTimeDto : newRealActDateTime) {
//            int signNum = getRandom(activity.getMinVolunteerNums(), activity.getMaxVolunteerNums());
//            //随机拿社区
//            SysOrg org = orgList.get(getRandom(0, orgList.size() - 1));
//            //随机取所属领域
//            ZyzSyncBelongFieldDict dict = dictList.get(getRandom(0, dictList.size() - 1));
//            CopyActivity copyActivity = createActivity(activity, signNum, createActivityTimeDto, org, dict);
//            activityList.add(copyActivity);
//            //打乱志愿者名单
//            Collections.shuffle(volunteerList);
//            List<CopyVolunteer> volunteers = volunteerList.size() <= signNum ? volunteerList : volunteerList.subList(0, signNum);
//            //造志愿者申请和时长信息
//            for (CopyVolunteer volunteer : volunteers) {
//                applies.add(createActivityApply(volunteer, copyActivity, activity.getId()));
//            }
//        }
//        saveOrUpdateCopyActivityApplies(applies);
//        saveOrUpdateCopyActivities(activityList);
//        activity.setComplete(true);
//        double sumHours = timeNumList.stream().mapToInt(Integer::valueOf).sum();
//        //计算预计造数据的时长 计算公式为时间段分组值总和/时间段分组的list的个数*活动数*(志愿者人数平均数)*倍率0.8
//        activity.setEstimateServiceLong(new BigDecimal(
//                sumHours / timeNumList.size() * activity.getActivityNums() * (activity.getMaxVolunteerNums() + activity.getMinVolunteerNums()) / 2 * 0.8
//        ));
//        parameterCopyActivityService.updateById(activity);
//    }
//
//    public int getRandom(int min, int max) {
//        return rand.nextInt(max) % (max - min + 1) + min;
//    }
//
//    /**
//     * 同步服务时长
//     * @param activityApply 报名对象信息
//     * @return true 表示需要重试 false表示不需要
//     */
//    private boolean syncServiceTime(CopyActivityApply activityApply) {
//        if (StringUtil.isEmpty(activityApply.getActSyncId())) {
//            activityApply.setServiceLongIsSync(Cons.PlatformSyncState.FAILURE);
//            return false;
//        }
//        SyncServiceTime syncServiceTime = new SyncServiceTime();
//        syncServiceTime.setUniCode(String.valueOf(activityApply.getActivityApplyId()));
//        syncServiceTime.setIdCode(activityApply.getCertificateId());
//        syncServiceTime.setActId(activityApply.getActSyncId());
//        LocalDateTime start = LocalDateTimeUtil.parseStringToDateTime(activityApply.getApplyTime(), "yyyyMMddHHmmss");
//        syncServiceTime.setStartTime(start.format(DateTimeFormatter.ofPattern(TIME_FORMAT)));
//        syncServiceTime.setEndTime(LocalDateTimeUtil.parseStringToDateTime(activityApply.getActEndTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(TIME_FORMAT)));
//        syncServiceTime.setServerTime(null);
//        try {
//            SyncServiceTimeResult result = ApiWebService.syncServiceTime(syncServiceTime);
//
//            activityApply.setServiceLongSyncTime(LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
//            activityApply.setServiceLongSyncRemark(result.getMsg());
//            //成功
//            if ("同步成功".equals(result.getMsg())) {
//                activityApply.setServiceLongIsSync(Cons.PlatformSyncState.SUCCESS);
//            }
//            //数据重复
//            else if (result.getMsg().contains("已同步过")) {
//                activityApply.setServiceLongIsSync(Cons.PlatformSyncState.SUCCESS);
//                activityApply.setServiceLongSyncRemark("市平台反馈数据重复，系统自动判定成功");
//            }
//            //其他一律失败
//            else {
//                activityApply.setServiceLongIsSync(Cons.PlatformSyncState.FAILURE);
//            }
//            return false;
//        } catch (Exception e) {
//            activityApply.setServiceLongIsSync(Cons.PlatformSyncState.FAILURE);
//            return true;
//        }
//    }
//
//    /**
//     * 同步活动
//     *
//     * @param activity 活动对象信息
//     * @return true 表示需要重试 false表示不需要
//     */
//    private boolean syncAct(CopyActivity activity) {
//        SyncActp syncAct = new SyncActp();
//        syncAct.setUniCode(String.valueOf(activity.getActivityId()));
//        syncAct.setOrgId(Cons.SyncConstant.AS_ORG_ID);
//        syncAct.setName(activity.getName());
//        syncAct.setServiceField(activity.getBelongFieldEnd());
//        syncAct.setRegionCode(Cons.SyncConstant.AS_REGION_CODE);
//        syncAct.setDescs(activity.getName());
//        syncAct.setAddress(activity.getAddress());
//        syncAct.setStartDate(LocalDateTimeUtil.parseStringToDateTime(activity.getStartTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
//        syncAct.setEndDate(LocalDateTimeUtil.parseStringToDateTime(activity.getEndTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
//        syncAct.setStartTime(LocalDateTimeUtil.parseStringToDateTime(activity.getStartTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(TIME_FORMAT)));
//        syncAct.setEndTime(LocalDateTimeUtil.parseStringToDateTime(activity.getEndTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(TIME_FORMAT)));
//        syncAct.setLinkName(activity.getContactPerson());
//        syncAct.setLinkPhone(activity.getContactPhone());
//        syncAct.setRecordWay(SyncActp.recordWay.TIME_SLOT);
//        syncAct.setServiceTime(null);
//        syncAct.setTxMapLan(activity.getLongitude() != null ? activity.getLongitude().toString() : "");
//        syncAct.setTxMapLat(activity.getLatitude() != null ? activity.getLatitude().toString() : "");
//        syncAct.setPicLogoUrl(Base64.encode(SyncUtil.getPicPath(activity.getPicture())));
//        syncAct.setServiceTarget("99");
//        try {
//            SyncActpResult syncActpResult = ApiWebService.syncActivity(syncAct);
//
//            activity.setSyncTime(LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
//            activity.setSyncRemark(syncActpResult.getMsg());
//            //成功
//            if ("同步成功".equals(syncActpResult.getMsg())) {
//                activity.setSyncId(syncActpResult.getApId());
//                activity.setSync(Cons.PlatformSyncState.SUCCESS);
//            }
//            //数据重复
//            else if (syncActpResult.getMsg().contains("唯一号已存在")) {
//                if (StringUtil.isNotEmpty(syncActpResult.getApId())) {
//                    activity.setSyncId(syncActpResult.getApId());
//                }
//                activity.setSync(Cons.PlatformSyncState.SUCCESS);
//                activity.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
//            }
//            //其他一律失败
//            else {
//                activity.setSync(Cons.PlatformSyncState.FAILURE);
//                activity.setSyncRemark(syncActpResult.getMsg());
//            }
//            return false;
//        } catch (Exception e) {
//            activity.setSync(Cons.PlatformSyncState.FAILURE);
//            return true;
//        }
//    }
//
//    /**
//     * 同步报名信息
//     * @param applyRecord 报名信息
//     * @return true 表示需要重试 false表示不需要
//     */
//    private boolean syncActMember(CopyActivityApply applyRecord) {
//        if (StringUtil.isEmpty(applyRecord.getActSyncId())) {
//            applyRecord.setApplyIsSync(Cons.PlatformSyncState.FAILURE);
//            return false;
//        }
//        SyncActMember syncActMember = new SyncActMember();
//        syncActMember.setUniCode(String.valueOf(applyRecord.getActivityApplyId()));
//        syncActMember.setActId(applyRecord.getActSyncId());
//        syncActMember.setIdCode(applyRecord.getCertificateId());
//        syncActMember.setJoinTime(LocalDateTimeUtil.parseStringToDateTime(applyRecord.getApplyTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
//        try {
//            SyncActMemberResult result = ApiWebService.syncActMember(syncActMember);
//            applyRecord.setApplySyncTime(LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
//            applyRecord.setApplySyncRemark(result.getMsg());
//            //成功
//            if (result.getMsg().contains("招募成功")) {
//                applyRecord.setApplyIsSync(Cons.PlatformSyncState.SUCCESS);
//            }
//            //数据重复
//            else if (result.getMsg().contains("已加入")) {
//                applyRecord.setApplyIsSync(Cons.PlatformSyncState.SUCCESS);
//                applyRecord.setApplySyncRemark("市平台反馈数据重复，系统自动判定成功");
//            }
//            //其他一律失败
//            else {
//                applyRecord.setApplyIsSync(Cons.PlatformSyncState.FAILURE);
//            }
//            return false;
//        } catch (Exception e) {
//            applyRecord.setApplyIsSync(Cons.PlatformSyncState.FAILURE);
//            return true;
//        }
//    }
//
//    /**
//     * 同步招募
//     *
//     * @param activity 活动信息
//     * @return true 表示需要重试 false表示不需要
//     */
//    private boolean syncRecruit(CopyActivity activity) {
//        if (StringUtil.isEmpty(activity.getSyncId())) {
//            activity.setRecruitSync(Cons.PlatformSyncState.FAILURE);
//            return false;
//        }
//        String startTime = LocalDateTimeUtil.parseStringToDateTime(activity.getStartTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
//        String endTime = LocalDateTimeUtil.parseStringToDateTime(activity.getEndTime(), "yyyyMMddHHmmss").format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
//        SyncActs syncActs = new SyncActs();
//        syncActs.setUniCode(IdUtil.getSnowflake().nextIdStr());
//        syncActs.setApId(activity.getSyncId());
//        syncActs.setActDate(startTime.substring(0, 10));
//        syncActs.setStartTime(startTime.substring(11, 16));
//        syncActs.setEndTime(endTime.substring(11, 16));
//        try {
//            SyncActsResult syncActsResult = ApiWebService.syncActivityRecruit(syncActs);
//            activity.setRecruitSyncTime(LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
//            activity.setRecruitSyncRemark(syncActsResult.getMsg());
//            //成功
//            if (syncActsResult.getMsg().contains("同步成功")) {
//                activity.setRecruitSyncId(syncActsResult.getActId());
//                activity.setRecruitSync(Cons.PlatformSyncState.SUCCESS);
//            }
//            //数据重复
//            else if (syncActsResult.getMsg().contains("数据重复")) {
//                if (StringUtil.isNotEmpty(syncActsResult.getActId())) {
//                    activity.setRecruitSyncId(syncActsResult.getActId());
//                }
//                activity.setRecruitSync(Cons.PlatformSyncState.SUCCESS);
//                activity.setRecruitSyncRemark("市平台反馈数据重复，系统自动判定成功");
//            }
//            //其他一律失败
//            else {
//                activity.setRecruitSync(Cons.PlatformSyncState.FAILURE);
//            }
//            return false;
//        } catch (Exception e) {
//            activity.setRecruitSync(Cons.PlatformSyncState.FAILURE);
//            return true;
//        }
//    }
//
//}
