package com.fykj.scaffold.dynamic.controller;

import com.fykj.scaffold.dynamic.domain.entity.DynamicFieldSnapshot;
import com.fykj.scaffold.dynamic.domain.params.DynamicFieldSnapshotParams;
import com.fykj.scaffold.dynamic.service.IDynamicFieldSnapshotService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 字段信息快照-接口控制器
 *
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/admin/dynamic/field/snapshot")
@Api(tags = "字段信息快照-管理接口")
public class DynamicFieldSnapshotController
        extends BaseController<IDynamicFieldSnapshotService, DynamicFieldSnapshot, DynamicFieldSnapshotParams> {

    @ApiOperation("获取全部方法")
    @PostMapping({"/list"})
    public JsonResult<List<DynamicFieldSnapshot>> all(@RequestBody DynamicFieldSnapshotParams params) {
        List<DynamicFieldSnapshot> result = baseService.list(params);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }

}
