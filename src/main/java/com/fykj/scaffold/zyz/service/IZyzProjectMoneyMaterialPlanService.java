package com.fykj.scaffold.zyz.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectMoneyMaterialPlan;

import java.util.List;

/**
 * 资金/物资使用计划表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
public interface IZyzProjectMoneyMaterialPlanService extends IBaseService<ZyzProjectMoneyMaterialPlan> {


    /**
     * 保存资金/物资使用计划列表
     * @param projectMoneyMaterialPlanList，projectId
     */
    void saveProjectMoneyMaterialPlan(List<ZyzProjectMoneyMaterialPlan> projectMoneyMaterialPlanList, Long projectId);
}

