package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamType;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamTypeParams;
import com.fykj.scaffold.zyz.service.IZyzTeamTypeService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.beans.vo.IdTextVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队特色表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-02-21
 */
@RestController
@RequestMapping("/admin/zyz/team/type")
@Api(tags = "团队特色接口")
public class ZyzTeamTypeController extends BaseController<IZyzTeamTypeService, ZyzTeamType,ZyzTeamTypeParams> {


    @ApiOperation(value = "获取团队类型列表")
    @GetMapping(value = "/teamTypeList")
    public Result teamTypeList() {
        List<ZyzTeamType> typeList = baseService.getList();
        return new JsonResult<>(typeList);
    }

    @ApiOperation("获取团队类型--分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzTeamType>> getPages(@RequestBody ZyzTeamTypeParams params) {
        IPage<ZyzTeamType> iPage = baseService.getPages(params);
        return new JsonResult<>(iPage);
    }



}
