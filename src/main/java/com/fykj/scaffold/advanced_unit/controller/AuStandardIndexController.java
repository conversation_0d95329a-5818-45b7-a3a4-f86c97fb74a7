package com.fykj.scaffold.advanced_unit.controller;

import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardIndex;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardIndexParams;
import com.fykj.scaffold.advanced_unit.service.IAuStandardIndexService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文明单位-指标标准-测评指标
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@RestController
@Api(tags = "管理端-文明单位-测评项")
@RequestMapping("/admin/manager/advanced-unit/index")
public class AuStandardIndexController
        extends BaseController<IAuStandardIndexService, AuStandardIndex, AuStandardIndexParams> {

}
