package com.fykj.volunteer.sync.util.domain.sync_acts;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <act>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>活动招募上报失败：唯一标识键不能为空</msg>
 *   </info>
 * </act>
 * <act>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>活动招募上报失败：活动id不存在</msg>
 *     <uniCode>102327</uniCode>
 *   </info>
 * </act>
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActsResult {
    /**
     * 活动注册id
     */
    private String actId;
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "act")
    public static class Act {
        private List<SyncActsResult> info;
    }

}
