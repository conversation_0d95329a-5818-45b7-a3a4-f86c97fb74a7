package com.fykj.scaffold.jjh_app;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class AppUserDto implements Serializable {
    private static final long serialVersionUID = -7185989971411436841L;
    private String certLevel;

    private String certName;

    private String certNo;

    private String mail;

    private String mobile;

    private String uniqueId;

    private String userType;
}
