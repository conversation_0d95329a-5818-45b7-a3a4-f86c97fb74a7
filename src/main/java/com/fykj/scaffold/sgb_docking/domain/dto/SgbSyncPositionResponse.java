package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 苏州社工部志愿阵地同步响应
 */
@Data
public class SgbSyncPositionResponse {
    
    /**
     * 响应信息列表
     */
    private List<PositionResult> info;
    
    /**
     * 志愿阵地推送结果
     */
    @Data
    public static class PositionResult {

        /**
         * 响应状态码 - SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 对接方编号
         */
        private String uniCode;

        /**
         * 阵地编号
         */
        private String bpId;
    }
}
