package com.fykj.scaffold.security.login.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("刷新token参数对象")
public class Oauth2RefreshTokenDto {

    @ApiModelProperty(value = "客户端id")
    private String clientId;

    @NotNull(message = "refreshToken必填")
    @ApiModelProperty(value = "Refresh Token")
    private String refreshToken;
}
