package com.fykj.scaffold.ledger.controller;

import com.fykj.scaffold.ledger.domain.dto.LedgerReportDto;
import com.fykj.scaffold.ledger.service.ILedgerReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;

/**
 * 台账上报-接口控制器
 *
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/admin/ledger/report")
@Api(tags = "台账上报-管理接口")
public class LedgerReportController {

    @Autowired
    private ILedgerReportService reportService;

    @ApiOperation(value = "上报")
    @PostMapping(value = "/save")
    public Result save(@RequestBody @Validated LedgerReportDto report) {
        return reportService.reportAdmin(report);
    }
}
