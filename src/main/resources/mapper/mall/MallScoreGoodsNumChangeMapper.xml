<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallScoreGoodsNumChangeMapper">

    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.entity.MallScoreGoodsNumChange">
        select v.* from mall_score_goods_num_change v
        where v.is_deleted=FALSE and v.goods_id=#{params.goodsId}
        order by v.create_date desc
    </select>
</mapper>