package com.fykj.scaffold.security.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.UserWriteOff;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.security.business.domain.params.UserWriteOffParams;
import org.apache.ibatis.annotations.Param;

/**
 * 系统用户注销管理-Mapper接口
 *
 * @date 2025-05-23
 */
public interface UserWriteOffMapper extends BaseMapper<UserWriteOff> {

    /**
     * 分页查询用户注销记录
     * @param page
     * @param params
     * @return
     */
    IPage<UserWriteOff> pageQuery(IPage<UserWriteOff> page, @Param("params") UserWriteOffParams params);

    /**
     * 获取用户角色身份介绍
     * @param userId
     * @return
     */
    String getUserRoleDescription(@Param("userId") Long userId);
}
