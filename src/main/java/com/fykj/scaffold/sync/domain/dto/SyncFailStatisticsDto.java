package com.fykj.scaffold.sync.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SyncFailStatisticsDto implements Serializable {

    @ApiModelProperty(value = "上报失败原因")
    private String message;
    @ApiModelProperty(value = "上报失败原因")
    private String errorMsg;

    @ApiModelProperty(value = "上报失败数量")
    private int failNum = 0;
    @ApiModelProperty(value = "排名")
    private int rankNum = 0;
}
