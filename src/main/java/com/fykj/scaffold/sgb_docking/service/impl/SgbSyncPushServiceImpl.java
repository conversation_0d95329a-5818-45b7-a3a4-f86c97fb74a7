package com.fykj.scaffold.sgb_docking.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.sgb_docking.cons.SgbCons;
import com.fykj.scaffold.sgb_docking.domain.bo.SyncInfo;
import com.fykj.scaffold.sgb_docking.domain.bo.SyncResult;
import com.fykj.scaffold.sgb_docking.domain.dto.*;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncLog;
import com.fykj.scaffold.sgb_docking.service.ISgbFieldMappingService;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncLogService;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncPushService;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncStatusRecordService;
import com.fykj.scaffold.sgb_docking.util.SgbApiUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.service.*;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import result.ResultCode;

import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;

/**
 * 苏州志愿者平台推送服务实现类
 */
@Slf4j
@Service
public class SgbSyncPushServiceImpl implements ISgbSyncPushService {

    @Autowired
    private ISgbSyncLogService logService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ISgbFieldMappingService sgbFieldMappingService;

    @Autowired
    private ISgbSyncStatusRecordService syncStatusRecordService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzVolunteerTeamService volunteerTeamService;

    @Autowired
    private IZyzActivityApplyService activityApplyService;

    @Autowired
    private IZyzActivityTimePeriodService activityTimePeriodService;

    @Autowired
    private ISysOrgService sysOrgService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private IDictService dictService;

    /**
     * 推送志愿者信息到苏州社工部平台
     *
     * @param bizId  志愿者业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncVolunteer(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取志愿者数据
        ZyzVolunteer volunteer = volunteerService.getById(bizId);

        // 2. 构建同步请求
        SgbSyncVolunteerRequest syncRequest = buildSyncRequest(volunteer, bizId);
        String requestJson = syncRequest.buildJson();
        log.info("推送志愿者信息请求: {}", requestJson);

        // 3. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_USER, requestJson);
        log.info("推送志愿者信息响应: {}", responseStr);

        // 4. 解析响应结果
        SyncResult syncResult = parseApiResponse(responseStr);

        // 5. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.VOLUNTEER_SYNC, bizId);

        // 6. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.VOLUNTEER_SYNC, syncId, bizId, volunteer.getName(), requestJson, responseStr);
    }

    /**
     * 注册组织 - 推送志愿服务组织信息到苏州社工部平台
     *
     * @param bizId  组织业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncOrg(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取组织数据（系统组织）
        SysOrg sysOrg = sysOrgService.getById(bizId);

        // 2. 构建同步请求
        SgbSyncPositionRequest syncRequest = buildPositionSyncRequest(sysOrg, bizId);
        String requestJson = syncRequest.buildJson();
        log.info("推送组织信息请求: {}", requestJson);

        // 3. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_POSITION, requestJson);
        log.info("推送组织信息响应: {}", responseStr);

        // 4. 解析响应结果
        SyncResult syncResult = parsePositionApiResponse(responseStr);

        // 5. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.BASE_SYNC, bizId);

        // 6. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.BASE_SYNC, syncId, bizId, "组织", requestJson, responseStr);
    }

    /**
     * 注册组织 - 推送志愿服务组织信息到苏州社工部平台
     *
     * @param bizId  组织业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncTeam(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取组织数据
        ZyzTeam team = teamService.getById(bizId);
        if (team == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "未找到指定的团队信息");
        }

        // 2. 检查是否已有成功的同步记录
        SyncInfo syncInfo = syncStatusRecordService.determineSyncInfo(SgbCons.PlatformSyncBiz.TEAM_SYNC, bizId);
        boolean hasSuccessfulSync = SgbCons.SgbSaveType.UPDATE.getValue().equals(syncInfo.getSaveType());

        if (hasSuccessfulSync) {
            // 已有成功同步记录，按原来的更新逻辑处理
            log.info("组织[{}]已有成功同步记录，执行更新推送", team.getName());
        } else {
            // 3. 没有成功同步记录，先查询组织是否已存在
            SgbQueryTeamResponse queryResponse = queryTeamByName(team.getName());

            // 4. 如果组织已存在，直接标记为已同步
            if (queryResponse.hasTeam()) {
                log.info("组织[{}]已存在于SGB系统中，teamId: {}, 直接标记为已同步", team.getName(), queryResponse.getTeamId());

                // 创建成功的同步结果
                SyncResult syncResult = SyncResult.success("SUCCESS", "组织已存在，直接同步成功", queryResponse.getTeamId());

                // 更新同步状态
                updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.TEAM_SYNC, bizId);

                // 记录同步日志
                String queryRequestJson = SgbQueryTeamRequest.builder().teamName(team.getName()).build().buildJson();
                String queryResponseJson = JSON.toJSONString(queryResponse);
                return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.TEAM_SYNC, syncId, bizId, team.getName(), queryRequestJson, queryResponseJson);
            }

            // 5. 组织不存在，执行创建推送逻辑
            log.info("组织[{}]不存在于SGB系统中，执行创建推送", team.getName());
        }

        // 6. 构建同步请求（新增或更新）
        SgbSyncTeamRequest syncRequest = buildTeamSyncRequest(team, syncInfo);
        String requestJson = syncRequest.buildJson();
        log.info("推送组织信息请求: {}", requestJson);

        // 7. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_TEAM, requestJson);
        log.info("推送组织信息响应: {}", responseStr);

        // 8. 解析响应结果
        SyncResult syncResult = parseTeamApiResponse(responseStr);

        // 9. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.TEAM_SYNC, bizId);

        // 10. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.TEAM_SYNC, syncId, bizId, team.getName(), requestJson, responseStr);
    }


    /**
     * 加入团队 - 推送志愿者加入团队的关系信息
     *
     * @param bizId  加入团队记录业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncTeamVolunteer(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取加入团队数据
        ZyzVolunteerTeam volunteerTeam = volunteerTeamService.getById(bizId);

        // 2. 构建同步请求
        SgbSyncOrgMemberRequest syncRequest = buildOrgMemberSyncRequest(volunteerTeam, bizId);
        String requestJson = syncRequest.buildJson();
        log.info("推送加入团队信息请求: {}", requestJson);

        // 3. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_ORG_MEMBER, requestJson);
        log.info("推送加入团队信息响应: {}", responseStr);

        // 4. 解析响应结果
        SyncResult syncResult = parseOrgMemberApiResponse(responseStr);

        // 5. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.TEAM_JOIN_SYNC, bizId);

        // 6. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.TEAM_JOIN_SYNC, syncId, bizId, "加入团队", requestJson, responseStr);
    }

    /**
     * 活动登记 - 推送志愿服务活动基本信息
     *
     * @param bizId  活动业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncActivity(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取活动数据
        ZyzActivity activity = activityService.getById(bizId);

        // 2. 构建同步请求
        SgbSyncActivityRequest syncRequest = buildActivitySyncRequest(activity, bizId);
        String requestJson = syncRequest.buildJson();
        log.info("推送活动信息请求: {}", requestJson);

        // 3. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_ACTIVITY, requestJson);
        log.info("推送活动信息响应: {}", responseStr);

        // 4. 解析响应结果
        SyncResult syncResult = parseActivityApiResponse(responseStr);

        // 5. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.ACTIVITY_SYNC, bizId);

        // 6. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.ACTIVITY_SYNC, syncId, bizId, activity.getName(), requestJson, responseStr);
    }


    /**
     * 发布活动招募 - 推送活动招募信息
     *
     * @param bizId  活动招募业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncActivityRecruit(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取活动招募数据（活动时段）
        ZyzActivityTimePeriod activityTimePeriod = activityTimePeriodService.getById(bizId);

        // 2. 构建同步请求
        SgbSyncActivityRecruitRequest syncRequest = buildActivityRecruitSyncRequest(activityTimePeriod, bizId);
        String requestJson = syncRequest.buildJson();
        log.info("推送活动招募信息请求: {}", requestJson);

        // 3. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_ACTIVITY_RECRUIT, requestJson);
        log.info("推送活动招募信息响应: {}", responseStr);

        // 4. 解析响应结果
        SyncResult syncResult = parseActivityRecruitApiResponse(responseStr);

        // 5. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.ACTIVITY_RECRUIT_SYNC, bizId);

        // 6. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.ACTIVITY_RECRUIT_SYNC, syncId, bizId, "活动招募", requestJson, responseStr);
    }

    /**
     * 加入活动招募 - 推送志愿者参与活动招募的信息
     *
     * @param bizId  活动参与记录业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncActivityMember(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取活动参与数据
        ZyzActivityApply activityApply = activityApplyService.getById(bizId);

        // 2. 构建同步请求
        SgbSyncActivityMemberRequest syncRequest = buildActivityMemberSyncRequest(activityApply, bizId);
        String requestJson = syncRequest.buildJson();
        log.info("推送加入活动招募信息请求: {}", requestJson);

        // 3. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_ACTIVITY_MEMBER, requestJson);
        log.info("推送加入活动招募信息响应: {}", responseStr);

        // 4. 解析响应结果
        SyncResult syncResult = parseActivityMemberApiResponse(responseStr);

        // 5. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.ACTIVITY_MEMBER_SYNC, bizId);

        // 6. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.ACTIVITY_MEMBER_SYNC, syncId, bizId, "加入活动招募", requestJson, responseStr);
    }

    /**
     * 服务时长上传 - 推送志愿者服务时长记录
     *
     * @param bizId  服务时长记录业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SgbSyncLog syncServiceTime(long bizId, long syncId) {
        checkSgbSyncSwitch();
        // 1. 获取服务时长数据（通过活动报名记录）
        ZyzActivityApply activityApply = activityApplyService.getById(bizId);

        // 2. 构建同步请求
        SgbSyncServiceTimeRequest syncRequest = buildServiceTimeSyncRequest(activityApply, bizId);
        String requestJson = syncRequest.buildJson();
        log.info("推送服务时长信息请求: {}", requestJson);

        // 3. 调用API接口
        String responseStr = callSgbApi(SgbCons.ApiMethod.SYNC_SERVICE_TIME, requestJson);
        log.info("推送服务时长信息响应: {}", responseStr);

        // 4. 解析响应结果
        SyncResult syncResult = parseServiceTimeApiResponse(responseStr);

        // 5. 更新同步状态
        updateSyncStatus(syncResult, SgbCons.PlatformSyncBiz.SERVICE_TIME_SYNC, bizId);

        // 6. 记录同步日志
        return buildSyncLog(syncResult, SgbCons.PlatformSyncBiz.SERVICE_TIME_SYNC, syncId, bizId, "服务时长", requestJson, responseStr);
    }

    // ==================== 批量同步方法实现 ====================

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncVolunteerBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncVolunteer, "SGB志愿者批量同步失败"));
    }

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncOrgBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncOrg, "SGB志愿阵地批量同步失败"));
    }

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncTeamBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncTeam, "SGB团队批量同步失败"));
    }

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncTeamVolunteerBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncTeamVolunteer, "SGB加入团队批量同步失败"));
    }

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncActivityBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncActivity, "SGB活动批量同步失败"));
    }

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncActivityRecruitBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncActivityRecruit, "SGB活动招募批量同步失败"));
    }

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncActivityMemberBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncActivityMember, "SGB加入活动招募批量同步失败"));
    }

    @Override
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    public void syncServiceTimeBatch(List<Long> bizIds) {
        checkSgbSyncSwitch();
        if (CollectionUtil.isEmpty(bizIds)) {
            return;
        }
        bizIds.forEach(bizId -> executeInIndependentTransaction(bizId, this::syncServiceTime, "SGB服务时长批量同步失败"));
    }

    /**
     * 构建同步请求对象
     */
    private SgbSyncVolunteerRequest buildSyncRequest(ZyzVolunteer volunteer, long bizId) {
        // 构建字段映射请求
        SgbSyncVolunteerRequest syncRequest = sgbFieldMappingService.mappingVolunteer(volunteer);

        // 设置保存类型（新增或更新）
        String saveType = syncStatusRecordService.determineSaveType(SgbCons.PlatformSyncBiz.VOLUNTEER_SYNC, bizId);
        syncRequest.setSaveType(saveType);

        return syncRequest;
    }



    /**
     * 调用苏州社工部API
     */
    private String callSgbApi(String apiMethod, String requestJson) {
        try {
            return SgbApiUtil.sendRequest(apiMethod, requestJson);
        } catch (Exception e) {
            log.error("调用苏州社工部API失败，方法: {}", apiMethod, e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "调用苏州社工部API失败: " + e.getMessage());
        }
    }

    /**
     * 解析API响应结果
     */
    private SyncResult parseApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncVolunteerResponse response = JSON.parseObject(responseStr, SgbSyncVolunteerResponse.class);
        if (response == null || response.getInfo() == null || response.getInfo().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncVolunteerResponse.VolunteerResult result = response.getInfo().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getUId();

        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 判断是否同步成功
     */
    private boolean isSuccessfulSync(String resultMsg, String resultCode) {
        boolean isSuccessMsg = StrUtil.isNotBlank(resultMsg)
                && (resultMsg.contains("成功") || SgbCons.SuccessMessages.SUCCESS_MSG_LIST.contains(resultMsg));
        boolean isSuccessCode = SgbCons.SgbResponseCode.SUCCESS.name().equals(resultCode);
        return isSuccessMsg || isSuccessCode;
    }

    /**
     * 更新同步状态记录
     */
    private void updateSyncStatus(SyncResult syncResult, String bizType, long bizId) {
        syncStatusRecordService.saveOrUpdateSyncStatus(syncResult.isSuccess(), syncResult.getResultMsg(), bizType, bizId, syncResult.getSgbId());
    }

    /**
     * 构建同步日志
     */
    private SgbSyncLog buildSyncLog(
            SyncResult syncResult, String bizType, long syncId, long bizId, String objectName, String requestJson, String responseStr) {
        return logService.buildSyncLog(bizType, syncResult.getResultCode(), syncResult.getResultMsg(),
                syncId, bizId, objectName, requestJson, responseStr);
    }

    /**
     * 构建组织同步请求对象
     */
    private SgbSyncPositionRequest buildPositionSyncRequest(SysOrg sysOrg, long bizId) {
        // 使用字段映射服务构建请求
        SgbSyncPositionRequest syncRequest = sgbFieldMappingService.mappingPosition(sysOrg);

        // 获取同步信息（包含保存类型和sgbId）
        SyncInfo syncInfo = syncStatusRecordService.determineSyncInfo(SgbCons.PlatformSyncBiz.BASE_SYNC, bizId);
        syncRequest.setSaveType(syncInfo.getSaveType());

        // 如果是更新操作，设置bpId
        if (SgbCons.SgbSaveType.UPDATE.getValue().equals(syncInfo.getSaveType())) {
            if (StrUtil.isNotBlank(syncInfo.getSgbId())) {
                syncRequest.setBpId(syncInfo.getSgbId());
                log.debug("设置组织更新操作的bpId: {}", syncInfo.getSgbId());
            } else {
                log.warn("更新组织时未找到bpId，bizId: {}", bizId);
            }
        }

        return syncRequest;
    }

    /**
     * 解析组织API响应结果
     */
    private SyncResult parsePositionApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncPositionResponse response = JSON.parseObject(responseStr, SgbSyncPositionResponse.class);
        if (response == null || response.getInfo() == null || response.getInfo().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncPositionResponse.PositionResult result = response.getInfo().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getBpId();

        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 构建组织同步请求对象
     */
    private SgbSyncTeamRequest buildTeamSyncRequest(ZyzTeam team, SyncInfo syncInfo) {
        // 使用字段映射服务构建请求
        SgbSyncTeamRequest syncRequest = sgbFieldMappingService.mappingTeam(team);

        // 设置保存类型（新增或更新）
        syncRequest.setSaveType(syncInfo.getSaveType());

        // 设置组织ID（用于更新操作）
        if (SgbCons.SgbSaveType.UPDATE.getValue().equals(syncInfo.getSaveType()) && StrUtil.isNotBlank(syncInfo.getSgbId())) {
            syncRequest.setOrgId(syncInfo.getSgbId());
        }

        return syncRequest;
    }

    /**
     * 解析组织API响应结果
     */
    private SyncResult parseTeamApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncTeamResponse response = JSON.parseObject(responseStr, SgbSyncTeamResponse.class);
        if (response == null || response.getInfo() == null || response.getInfo().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncTeamResponse.TeamResult result = response.getInfo().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getOrgId();

        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 构建加入组织同步请求对象
     */
    private SgbSyncOrgMemberRequest buildOrgMemberSyncRequest(ZyzVolunteerTeam volunteerTeam, long bizId) {
        // 使用字段映射服务构建请求
        return sgbFieldMappingService.mappingOrgMember(volunteerTeam);
    }

    /**
     * 解析加入组织API响应结果
     */
    private SyncResult parseOrgMemberApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncOrgMemberResponse response = JSON.parseObject(responseStr, SgbSyncOrgMemberResponse.class);
        if (response == null || response.getInfos() == null || response.getInfos().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncOrgMemberResponse.OrgMemberResult result = response.getInfos().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getSpcode();



        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 构建活动同步请求对象
     */
    private SgbSyncActivityRequest buildActivitySyncRequest(ZyzActivity activity, long bizId) {
        // 使用字段映射服务构建请求
        return sgbFieldMappingService.mappingActivity(activity);
    }

    /**
     * 解析活动API响应结果
     */
    private SyncResult parseActivityApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncActivityResponse response = JSON.parseObject(responseStr, SgbSyncActivityResponse.class);
        if (response == null || response.getOutActList() == null || response.getOutActList().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncActivityResponse.ActivityResult result = response.getOutActList().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getApId();

        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 构建活动招募同步请求对象
     */
    private SgbSyncActivityRecruitRequest buildActivityRecruitSyncRequest(ZyzActivityTimePeriod activityTimePeriod, long bizId) {
        // 使用字段映射服务构建请求
        return sgbFieldMappingService.mappingActivityRecruit(activityTimePeriod);
    }

    /**
     * 解析活动招募API响应结果
     */
    private SyncResult parseActivityRecruitApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncActivityRecruitResponse response = JSON.parseObject(responseStr, SgbSyncActivityRecruitResponse.class);
        if (response == null || response.getOuts() == null || response.getOuts().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncActivityRecruitResponse.ActivityRecruitResult result = response.getOuts().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getActId();

        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 构建加入活动招募同步请求对象
     */
    private SgbSyncActivityMemberRequest buildActivityMemberSyncRequest(ZyzActivityApply activityApply, long bizId) {
        // 使用字段映射服务构建请求
        return sgbFieldMappingService.mappingActivityMember(activityApply);
    }

    /**
     * 解析加入活动招募API响应结果
     */
    private SyncResult parseActivityMemberApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncActivityMemberResponse response = JSON.parseObject(responseStr, SgbSyncActivityMemberResponse.class);
        if (response == null || response.getOut() == null || response.getOut().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncActivityMemberResponse.ActivityMemberResult result = response.getOut().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getUniCode();

        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 构建服务时长同步请求对象
     */
    private SgbSyncServiceTimeRequest buildServiceTimeSyncRequest(ZyzActivityApply activityApply, long bizId) {
        // 使用字段映射服务构建请求
        return sgbFieldMappingService.mappingServiceTime(activityApply);
    }

    /**
     * 解析服务时长API响应结果
     */
    private SyncResult parseServiceTimeApiResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbSyncServiceTimeResponse response = JSON.parseObject(responseStr, SgbSyncServiceTimeResponse.class);
        if (response == null || response.getInfos() == null || response.getInfos().isEmpty()) {
            return SyncResult.failed("响应数据为空", null, null);
        }

        // 提取第一个结果
        SgbSyncServiceTimeResponse.ServiceTimeResult result = response.getInfos().get(0);
        String resultCode = result.getCode();
        String resultMsg = result.getMsg();
        String sgbId = result.getUniCode();

        // 判断同步状态并返回结果
        boolean isSuccess = isSuccessfulSync(resultMsg, resultCode);
        return isSuccess ? SyncResult.success(resultCode, resultMsg, sgbId) : SyncResult.failed(resultMsg, resultCode, sgbId);
    }

    /**
     * 根据组织名称查询组织ID
     *
     * @param teamName 组织名称
     * @return 查询结果，如果找到组织则返回组织ID，否则返回null
     */
    private SgbQueryTeamResponse queryTeamByName(String teamName) {
        try {
            // 构建查询请求
            SgbQueryTeamRequest queryRequest = SgbQueryTeamRequest.builder().teamName(teamName).build();
            String requestJson = queryRequest.buildJson();
            log.info("查询组织信息请求: {}", requestJson);

            // 调用API接口
            String responseStr = callSgbApi(SgbCons.ApiMethod.GET_TEAM_ID_BY_NAME, requestJson);
            log.info("查询组织信息响应: {}", responseStr);

            // 解析响应结果
            return parseQueryTeamResponse(responseStr);
        } catch (Exception e) {
            log.error("查询组织信息失败，组织名称: {}", teamName, e);
            // 查询失败时返回失败结果，不影响后续的创建流程
            SgbQueryTeamResponse failedResponse = new SgbQueryTeamResponse();
            failedResponse.setCode("FAIL");
            failedResponse.setMsg("查询组织信息失败: " + e.getMessage());
            return failedResponse;
        }
    }

    /**
     * 解析查询组织API响应结果
     */
    private SgbQueryTeamResponse parseQueryTeamResponse(String responseStr) {
        // 验证响应格式
        if (!JSONUtil.isTypeJSON(responseStr)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "社工部接口返回非正常数据");
        }

        // 解析响应
        SgbQueryTeamResponse response = JSON.parseObject(responseStr, SgbQueryTeamResponse.class);
        if (response == null) {
            SgbQueryTeamResponse failedResponse = new SgbQueryTeamResponse();
            failedResponse.setCode("FAIL");
            failedResponse.setMsg("响应数据为空");
            return failedResponse;
        }

        return response;
    }

    /**
     * 在独立事务中执行单个ID的同步操作
     * @param bizId 业务ID
     * @param syncFunction 同步方法函数（接受bizId和syncId两个参数）
     * @param errorMessage 错误日志消息前缀
     */
    private void executeInIndependentTransaction(Long bizId, BiConsumer<Long, Long> syncFunction, String errorMessage) {
        // 使用TransactionTemplate确保每个ID都有独立事务
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        transactionTemplate.execute(transactionStatus -> {
            try {
                Long syncId = IdUtil.getSnowflake(1, 1).nextId();
                syncFunction.accept(bizId, syncId);
            } catch (Exception e) {
                transactionStatus.setRollbackOnly();
                log.error("{}，bizId: {}, 异常原因：{}", errorMessage, bizId, e.getMessage(), e);
            }
            return null;
        });
    }

    /**
     * 检查SGB同步功能开关
     */
    private void checkSgbSyncSwitch() {
        Optional.ofNullable(dictService.getByCode(SgbCons.SyncSwitch.SGB_SYNC_SWITCH))
                .map(Dict::getValue)
                .filter(Cons.COMMON_SWITCH.YES::equals)
                .orElseThrow(() -> new BusinessException(ResultCode.FAIL, "文明办同步功能已关闭"));
    }

}