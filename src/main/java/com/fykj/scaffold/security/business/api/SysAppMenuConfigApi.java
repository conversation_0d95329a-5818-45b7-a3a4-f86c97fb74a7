package com.fykj.scaffold.security.business.api;

import cn.hutool.http.HttpUtil;
import com.fykj.scaffold.security.business.domain.entity.SysAppMenuConfig;
import com.fykj.scaffold.security.business.domain.params.SysAppMenuConfigParams;
import com.fykj.scaffold.security.business.service.ISysAppMenuConfigService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/api/sys/app-menu-config")
public class SysAppMenuConfigApi extends BaseController<ISysAppMenuConfigService, SysAppMenuConfig, SysAppMenuConfigParams> {

    @ApiOperation("根据栏位获取可以展示的app列表")
    @GetMapping(value = "/getAllEnableAppByCategoryCode")
    public Result getAllEnableApp(String categoryCode) {
        return new JsonResult<>(baseService.lambdaQuery()
                .eq(SysAppMenuConfig::getCategoryCode, categoryCode)
                .eq(SysAppMenuConfig::getStatus, true)
                .apply("(limit_capacity is null or limit_capacity ='')")
                .orderByAsc(SysAppMenuConfig::getSequence).list());
    }
}
