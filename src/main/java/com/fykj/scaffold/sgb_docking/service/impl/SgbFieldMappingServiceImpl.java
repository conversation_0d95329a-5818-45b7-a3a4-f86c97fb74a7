package com.fykj.scaffold.sgb_docking.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.sgb_docking.cons.SgbCons;
import com.fykj.scaffold.sgb_docking.domain.dto.*;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncStatusRecord;
import com.fykj.scaffold.sgb_docking.service.ISgbDictMappingService;
import com.fykj.scaffold.sgb_docking.service.ISgbFieldMappingService;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncStatusRecordService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.zyz.domain.dto.ActivityRecruitNumStatistics;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.core.support.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static constants.Mark.COMMA;

@Service
@Slf4j
public class SgbFieldMappingServiceImpl implements ISgbFieldMappingService {

    @Autowired
    private ISgbDictMappingService sgbDictMappingService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private ISgbSyncStatusRecordService sgbSyncStatusRecordService;

    @Autowired
    private IZyzActivityTimePeriodService activityTimePeriodService;

    @Override
    public SgbSyncVolunteerRequest mappingVolunteer(ZyzVolunteer entity) {
        Map<String, String> sys2sgb = getVolunteerSys2sgb(entity);
        String type = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getCertificateType()))
                // 其他
                .orElse("0");
        // 身份-在职党员
        String identity = Optional.ofNullable(entity.getIdentity())
                .orElse("1001");
        // 行业类别 -> 服务类型
        String serviceType = Optional.ofNullable(entity.getServiceType())
                .orElse("74805");
        // 政治面貌 -> 政治面貌
        String politics = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getPoliticalScape()))
                // 根据党员判断‘党员’｜‘其他’
                .orElseGet(() -> Boolean.TRUE.equals(entity.getIsParty())? "0":"3");
        // 教育程度 -> 文化程度
        String education = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getEducation()))
                // 其他
                .orElse("9");
        // 民族 -> 民族
        String nation = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getNation()))
                // 其他
                .orElse("56");
        // 专业特长
        String skill = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getSkill()))
                // 社会工作-其他
                .orElse("2099");
        // 区划
        String region = Optional.ofNullable(orgService.getByCode(entity.getOrgCode()))
                .map(SysOrg::getRegionCode)
                .orElse("320508");
        return SgbSyncVolunteerRequest.builder()
                .type(type)
                .idCode(entity.getCertificateId()).name(entity.getName()).phone(entity.getPhone())
                .identity(identity).skill(skill).serviceType(serviceType)
                .region(region).nation(nation)
                .politics(politics).education(education)
                .build();
    }

    @Override
    public SgbSyncPositionRequest mappingPosition(SysOrg entity) {
        // 阵地类型映射（根据组织层级判断）
        String typeId = getPositionTypeId(entity);
        return SgbSyncPositionRequest.builder()
                .saveType(SgbCons.SgbSaveType.INSERT.getValue()) // 默认新增
                .uniCode(String.valueOf(entity.getId()))
                .name(entity.getName())
                .typeId(typeId)
                // 获取区划编号，必填字段不做默认值处理
                .regionCode(entity.getRegionCode())
                .mapAddress(entity.getAddress())
                .txMapLan(Optional.ofNullable(entity.getLongitude())
                        .map(String::valueOf)
                        .orElse(""))
                .txMapLat(Optional.ofNullable(entity.getLatitude())
                        .map(String::valueOf)
                        .orElse(""))
                .contact(entity.getLinkMan())
                .contactNo(entity.getLinkMobile())
                // 写死
                .workTime("09:00-17:30")
                .intro(entity.getDescription())
                // 已确认，重复传一次描述
                .systemDesc(entity.getDescription())
                .picLogoUrl(base64File(entity.getLogoUrl()))
                .build();
    }

    @Override
    public SgbSyncTeamRequest mappingTeam(ZyzTeam entity) {
        Map<String, String> sys2sgb = getTeamSys2sgb(entity);
        SysOrg sysOrg = orgService.getByCode(entity.getOrgCode());
        String regionCode = Optional.ofNullable(sysOrg)
                .map(SysOrg::getRegionCode)
                .orElse("320508");
        // dpId 写死 0022268551-工业园区文明办
        String dpId = "0022268551";
        // 团队性质映射
        String attribute = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getTeamNature()))
                .orElse("4");
        // 服务领域映射
        String serviceTypes = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getBelongFields()))
                .orElseThrow(() -> new BusinessException(ResultCode.FAIL, "字典类型" + entity.getBelongFields() + "没有映射关系"));
        // 服务对象字典映射 默认:99-其他
        String serviceTarget = Optional.ofNullable(entity.getServiceTarget())
                .orElse("99");
        // 团队类型字典映射 默认:306-职工志愿者团队
        String teamClassify = Optional.ofNullable(entity.getTeamClassify())
                .orElse("306");
        // 是否成立党组织
        String sfPartyOrg = Optional.ofNullable(entity.getPartyOrgFlag())
                .filter(Boolean.TRUE::equals)
                .map(x -> "1")
                .orElse("0");
        // 是否社会工作服务机构
        String sfSocialOrg = Optional.ofNullable(entity.getSocialOrgFlag())
                .filter(Boolean.TRUE::equals)
                .map(x -> "1")
                .orElse("0");
        return SgbSyncTeamRequest.builder()
                .dpId(dpId)
                .orgName(entity.getName())
                .idCode(entity.getCuratorCard())
                .regionCode(regionCode)
                .descs(entity.getIntroduction())
                .linkName(entity.getCuratorName())
                .linkPhone(entity.getCuratorContact())
                .uniqueCode(String.valueOf(entity.getId()))
                .attribute(attribute)
                .orgDept(dpId)
                .sfSocailOrg(sfSocialOrg)
                .fwRegion(regionCode)
                .serviceTypes(serviceTypes)
                .serviceTarget(serviceTarget)
                .teamClassify(teamClassify)
                .picLogoUrl(base64File(entity.getTeamPhoto()))
                .sfPartyOrg(sfPartyOrg)
                .build();
    }

    @Override
    public SgbSyncOrgMemberRequest mappingOrgMember(ZyzVolunteerTeam entity) {
        // 从志愿者表里取志愿者身份证
        String idCode = Optional.ofNullable(volunteerService.getById(entity.getVolunteerId()))
                .map(ZyzVolunteer::getCertificateId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "相关志愿者还未同步成功"));
        // 从团队同步状态记录中取社工部团队id
        String orgId = Optional.ofNullable(sgbSyncStatusRecordService.getByBizTypeAndId(SgbCons.PlatformSyncBiz.TEAM_SYNC, entity.getTeamId()))
                .map(SgbSyncStatusRecord::getSgbId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "相关团队还未同步成功"));
        // 是否全职,没有这个字段,默认否
        String fullTime = "否";
        // 加入团队时间
        String joinTime = Optional.ofNullable(entity.getJoinTime())
                .orElse(entity.getCreateDate())
                .format(DatePattern.NORM_DATE_FORMATTER);
        return SgbSyncOrgMemberRequest.builder()
                .idcode(idCode)
                .orgid(orgId)
                .jointime(joinTime)
                .isfulltime(fullTime)
                .description(null)
                .uniquecode(String.valueOf(entity.getId()))
                .build();
    }

    @Override
    public SgbSyncActivityRequest mappingActivity(ZyzActivity entity) {
        Map<String, String> sys2sgb = getActivitySys2sgb(entity);

        // 活动类型: 志愿类
        String type = "1";

        // 组织
        String orgId = getSgbOrgId(entity);
        // 服务领域映射
        String serviceField = Optional.ofNullable(getSgbDictValue(sys2sgb, entity.getActType()))
                .orElse("74805");
        // 服务对象字典映射(字典配的默认值
        String serviceTarget = Optional.ofNullable(getSgbDictValue(sys2sgb, "sgb_activity_service_target"))
                .orElse("99");
        // 服务时长记录方式 - 1.按次 2.按时间段
        String recordWay = "2";

        // 获取活动招募人数统计
        ActivityRecruitNumStatistics recruitStats = activityTimePeriodService.getRecruitNumStatistics(entity.getId());
        String recruitNum = String.valueOf(recruitStats.getVolunteerRecruitNum() != null ? recruitStats.getVolunteerRecruitNum() : 0);
        String audienceNum = String.valueOf(recruitStats.getMassesRecruitNum() != null ? recruitStats.getMassesRecruitNum() : 0);

        return SgbSyncActivityRequest.builder()
                .type(type)
                .uniCode(String.valueOf(entity.getId()))
                .orgId(orgId)
                .name(entity.getName())
                .serviceField(serviceField)
                .regionCode(entity.getActivityPlaceRegionCode())
                .descs(entity.getActSynopsis())
                .address(entity.getAddress())
                .startDate(entity.getStartTime().format(DatePattern.NORM_DATE_FORMATTER))
                .endDate(entity.getEndTime().format(DatePattern.NORM_DATE_FORMATTER))
                .startTime(entity.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm")))
                .endTime(entity.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm")))
                .linkName(entity.getContactPerson())
                .linkPhone(entity.getContactPhone())
                .recordWay(recordWay)
                // 单位：小时当recordWay=1时必填，且是0.5的正整数倍
                .serviceTime(null)
                .picLogoUrl(base64File(entity.getPicture()))
                .txMapLan(Optional.ofNullable(entity.getLongitude()).map(String::valueOf).orElse(Cons.SyncConstant.DEFAULT_LONGITUDE))
                .txMapLat(Optional.ofNullable(entity.getLatitude()).map(String::valueOf).orElse(Cons.SyncConstant.DEFAULT_LATITUDE))
                .serviceTarget(serviceTarget)
                .recruitNum(recruitNum)
                .audienceNum(audienceNum)
                .build();
    }

    /**
     * 获取阵地关联的团队
     * @param entity 活动
     * @return 团队sgb id
     */
    private String getSgbOrgId(ZyzActivity entity) {
        Long teamId = Optional.ofNullable(entity.getTeamId())
                .orElseGet(() -> orgService.getByCode(entity.getPublishOrgCode()).getMainTeamId());
        if(Objects.isNull(teamId)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "找不到活动的发布团队");
        }
        return Optional.ofNullable(sgbSyncStatusRecordService.getByBizTypeAndId(SgbCons.PlatformSyncBiz.TEAM_SYNC, teamId))
                .map(SgbSyncStatusRecord::getSgbId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "相关团队尚未同步成功"));
    }

    @Override
    public SgbSyncActivityRecruitRequest mappingActivityRecruit(ZyzActivityTimePeriod entity) {
        // 获取活动在SGB平台的ID
        String apId = Optional.ofNullable(sgbSyncStatusRecordService.getByBizTypeAndId(
                        SgbCons.PlatformSyncBiz.ACTIVITY_SYNC, entity.getActivityId()))
                .map(SgbSyncStatusRecord::getSgbId)
                .filter(StringUtil::isNotEmpty)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND,
                        "相关活动尚未同步成功，无法获取SGB平台ID"));

        return SgbSyncActivityRecruitRequest.builder()
                .uniCode(String.valueOf(entity.getId()))
                .apId(apId)
                .actDate(entity.getStartTime().format(DatePattern.NORM_DATE_FORMATTER))
                .startTime(entity.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm")))
                .endTime(entity.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm")))
                .build();
    }

    @Override
    public SgbSyncActivityMemberRequest mappingActivityMember(ZyzActivityApply entity) {
        // 获取活动招募在SGB平台的ID
        String actId = getSgbActIdByTimePeriodId(entity);
        String idCode = getIdCodeByVolunteerId(entity);
        return SgbSyncActivityMemberRequest.builder()
                .uniCode(String.valueOf(entity.getId()))
                .actId(actId)
                .idCode(idCode)
                .joinTime(entity.getApplyTime().format(DatePattern.NORM_DATE_FORMATTER))
                .build();
    }

    private String getIdCodeByVolunteerId(ZyzActivityApply entity) {
        return Optional.ofNullable(volunteerService.getById(entity.getVolunteerId()))
                .map(ZyzVolunteer::getCertificateId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND,
                        "无法获取活动参加人的身份证号"));
    }

    private String getSgbActIdByTimePeriodId(ZyzActivityApply entity) {
        return Optional.ofNullable(sgbSyncStatusRecordService.getByBizTypeAndId(
                        SgbCons.PlatformSyncBiz.ACTIVITY_RECRUIT_SYNC, entity.getTimePeriodId()))
                .map(SgbSyncStatusRecord::getSgbId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND,
                        "相关活动招募尚未同步成功，无法获取SGB平台ID"));
    }

    @Override
    public SgbSyncServiceTimeRequest mappingServiceTime(ZyzActivityApply entity) {
        // 服务时长计算逻辑 - 根据签到签退时间或直接从字段获取，格式化为0.5的正整数倍数
        Double serverTime = Optional.ofNullable(entity.getServiceLong())
                .map(BigDecimal::doubleValue)
                .orElse(0.0);

        // 获取活动招募在SGB平台的ID
        String actId = getSgbActIdByTimePeriodId(entity);
        String idCode = getIdCodeByVolunteerId(entity);
        return SgbSyncServiceTimeRequest.builder()
                .idCode(idCode)
                .uniCode(String.valueOf(entity.getId()))
                .actId(actId)
                .serverTime(serverTime)
                // 如果需要按时间段记录，添加startTime和endTime
                .startTime(Optional.ofNullable(entity.getTimePeriodStartTime())
                        .map(DateTimeFormatter.ofPattern("HH:mm")::format)
                        .orElse(null))
                .endTime(Optional.ofNullable(entity.getTimePeriodEndTime())
                        .map(DateTimeFormatter.ofPattern("HH:mm")::format)
                        .orElse(null))
                .build();
    }

    /**
     * 获取志愿阵地类型ID
     *
     * @param entity 组织实体
     * @return 阵地类型ID
     */
    private static String getPositionTypeId(SysOrg entity) {
        return SgbCons.PositionType.getTypeIdByLevel(entity.getLevel());
    }

    /**
     * 将图片相对路径转换为完整URL，然后进行base64编码并URLEncoder转码
     *
     * @param relativePath 图片相对路径（entity中存储的相对路径）
     * @return 完整URL的base64编码后URLEncoder转码字符串
     */
    private String base64File(String relativePath) {
        if (StringUtil.isEmpty(relativePath)) {
            return "";
        }

        try {
            // 获取httpAttachmentUrl配置
            String httpAttachmentUrl = SystemUtil.getProperty("httpAttachmentUrl");
            if (StringUtil.isEmpty(httpAttachmentUrl)) {
                log.error("httpAttachmentUrl配置未找到");
                return "";
            }

            // 拼接完整的图片URL
            String fullImageUrl;
            if (relativePath.startsWith("http://") || relativePath.startsWith("https://")) {
                // 如果已经是完整URL，直接使用
                fullImageUrl = relativePath;
            } else {
                // 拼接httpAttachmentUrl和相对路径
                fullImageUrl = httpAttachmentUrl + (relativePath.startsWith("/") ? "" : "/") + relativePath;
            }

            log.debug("处理图片URL: {} -> {}", relativePath, fullImageUrl);

            // 将完整URL转换为base64编码
            String base64String = Base64.getEncoder().encodeToString(fullImageUrl.getBytes(StandardCharsets.UTF_8));

            // 进行URLEncoder转码
            return URLEncoder.encode(base64String, StandardCharsets.UTF_8.toString());

        } catch (Exception e) {
            log.error("处理图片URL转base64时发生异常: {}, 相对路径: {}", e.getMessage(), relativePath, e);
            return "";
        }
    }

    private Map<String, String> getVolunteerSys2sgb(ZyzVolunteer entity) {
        List<String> needMappingDictFieldValues = new ArrayList<>();
        needMappingDictFieldValues.add(entity.getCertificateType());
        needMappingDictFieldValues.add(entity.getSkill());
        needMappingDictFieldValues.add(entity.getNation());
        needMappingDictFieldValues.add(entity.getEducation());
        needMappingDictFieldValues.add(entity.getPoliticalScape());
        return sgbDictMappingService.multiGetSgbDictStrBySysDictValue(needMappingDictFieldValues);
    }

    private Map<String, String> getTeamSys2sgb(ZyzTeam entity) {
        List<String> needMappingDictFieldValues = new ArrayList<>();
        needMappingDictFieldValues.add(entity.getBelongFields());
        needMappingDictFieldValues.add(entity.getTeamNature());
        return sgbDictMappingService.multiGetSgbDictStrBySysDictValue(needMappingDictFieldValues);
    }

    private Map<String, String> getActivitySys2sgb(ZyzActivity entity) {
        List<String> needMappingDictFieldValues = new ArrayList<>();
        needMappingDictFieldValues.add(entity.getActType());
        needMappingDictFieldValues.add("sgb_activity_service_target");
        return sgbDictMappingService.multiGetSgbDictStrBySysDictValue(needMappingDictFieldValues);
    }

    private String getSgbDictValue(Map<String, String> sys2sgb, String sysDictValue) {
        return Optional.ofNullable(sysDictValue)
                .map(sysValue -> sysValue.split(COMMA))
                .map(Arrays::stream)
                .map(x -> x.limit(5)
                        .map(sys2sgb::get)
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.joining(COMMA)))
                .filter(StrUtil::isNotBlank)
                .orElse(null);
    }

}
