package com.fykj.scaffold.security.business.mapper;

import com.fykj.scaffold.security.business.domain.entity.SysApiRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 接口-角色映射表
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface SysApiRoleMapper extends BaseMapper<SysApiRole> {

    /**
     * 按照角色ID删除绑定接口
     *
     * @param roleId
     */
    void deleteByRoleId(@Param("roleId") Long roleId);
}
