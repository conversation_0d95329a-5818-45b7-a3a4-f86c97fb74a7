package com.fykj.scaffold.zsq_docking.push_mq.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 14:45
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
@ApiModel("接收志愿者活动数据模型")
public class ActZyzSyncBelongFieldDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    private String typeId;

    /**
     * 服务领域名称
     */
    @ApiModelProperty(value = "服务领域名称")
    private String typeName;

    /**
     * 父节点id
     */
    @ApiModelProperty(value = "父节点id")
    private String fatherId;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private String nodePath;

    /**
     * 最小时间限制
     */
    @ApiModelProperty(value = "最小时间限制")
    private Integer minTimeLimit;

    /**
     * 最大时间限制
     */
    @ApiModelProperty(value = "最大时间限制")
    private Integer maxTimeLimit;
}
