package com.fykj.scaffold.zsq_docking.push_mq.listener;


import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingMqFail;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingSysOrgRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingMqFailService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingSysOrgRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.event.SysOrgDockingEvent;
import com.fykj.scaffold.zsq_docking.push_mq.service.IZSQDataPushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.DOCKING_RECORD_STATUS_FAIL;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.TOPIC_SYS_ORG_DOCKING;

@Slf4j
@Service
@RocketMQMessageListener(consumerGroup = "${spring.application.name}" + TOPIC_SYS_ORG_DOCKING, topic = TOPIC_SYS_ORG_DOCKING)
public class SysOrgDockingListener implements RocketMQListener<SysOrgDockingEvent>, AutoPushZSHMonitor {

    @Autowired
    private IZSQDockingSysOrgRecordService sysOrgDockingRecordService;

    @Autowired
    private IZSQDataPushService pushService;

    @Autowired
    private IZSQDockingMqFailService pushFailService;

    @Override
    public void onMessage(SysOrgDockingEvent notice) {
        ZSQDockingMqFail fail = new ZSQDockingMqFail();
        Long dockingRecordId = notice.getDockingRecordId();
        fail.setDockingRecordId(dockingRecordId);
        fail.setMessageId(notice.getMsgId());
        fail.setContent(JSONUtil.toJsonStr(notice));
        fail.setMsgTime(notice.getMsgCreateTime());
        Long orgId = notice.getOrgId();
        if (orgId == null) {
            fail.setExceptionTrace("组织 id 未获取到！");
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingSysOrgRecord record = sysOrgDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【组织 id 未获取到】！");
                sysOrgDockingRecordService.updateById(record);
            }
            return;
        }
        try {
            pushService.sysOrgDocking(notice);
        } catch (Exception e) {
            fail.setExceptionTrace(ExceptionUtil.stacktraceToString(e));
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingSysOrgRecord record = sysOrgDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【" + ExceptionUtil.stacktraceToString(e) + "】！");
                sysOrgDockingRecordService.updateById(record);
            }
        }
    }
}
