package com.fykj.scaffold.mall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsElderApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsApply;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsElderApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsElderApplyParams;
import com.fykj.scaffold.mall.mapper.MallScoreGoodsElderApplyMapper;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.fykj.scaffold.mall.service.IMallScoreGoodsElderApplyService;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;


/**
 * 星光闪耀适老化改造福申请表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2024-10-16
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallScoreGoodsElderApplyServiceImpl extends BaseServiceImpl<MallScoreGoodsElderApplyMapper, MallScoreGoodsElderApply> implements IMallScoreGoodsElderApplyService {
    @Autowired
    private IMallExchangeService mallExchangeService;
    @Autowired
    private IMallScoreGoodsService mallScoreGoodsService;
    @Autowired
    private IZyzVolunteerService zyzVolunteerService;

    @Override
    public IPage<MallScoreGoodsElderApplyDto> getPage(MallScoreGoodsElderApplyParams params) {
        IPage<MallScoreGoodsElderApplyDto> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public Boolean saveOrUpdateApply(MallScoreGoodsElderApply apply) {
        //验证并赋值商品id
        MallExchange exchange = applyCheck(apply);
        apply.setGoodsName(exchange.getGoodsName());
        apply.setVolunteerId(exchange.getExchangeVolunteerId());
        apply.setVolunteerName(exchange.getExchangeUserName());
        apply.setVolunteerPhone(exchange.getExchangeUserLinkPhone());
        ZyzVolunteer volunteer = zyzVolunteerService.getVolunteerById(exchange.getExchangeVolunteerId());
        apply.setVolunteerCertificateType(volunteer.getCertificateType());
        apply.setVolunteerCertificateId(volunteer.getCertificateId());
        return super.saveOrUpdate(apply);
    }

    private MallExchange applyCheck(MallScoreGoodsElderApply apply) {
        if (StringUtil.isEmpty(apply.getExchangeId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请填写兑换id");
        }
        MallExchange exchange = mallExchangeService.getById(apply.getExchangeId());
        apply.setGoodsId(exchange.getExchangeGoodsId());
        MallScoreGoods goods = mallScoreGoodsService.getById(exchange.getExchangeGoodsId());
        if (goods.getNeedApply() == null || !goods.getNeedApply()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "商品无需申请！");
        }
        boolean isExist = lambdaQuery()
                .eq(MallScoreGoodsElderApply::getExchangeId, apply.getExchangeId())
                .ne(apply.getId() != null, BaseEntity::getId, apply.getId()).exists();
        if (isExist) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "已经申请");
        }
        return exchange;
    }

    @Override
    public MallScoreGoodsElderApply getApplyInfo(Long exchangeId) {
        MallScoreGoodsElderApply result = lambdaQuery()
                .eq(MallScoreGoodsElderApply::getExchangeId, exchangeId)
                .one();
        DictTransUtil.trans(result);
        return result;
    }
}
