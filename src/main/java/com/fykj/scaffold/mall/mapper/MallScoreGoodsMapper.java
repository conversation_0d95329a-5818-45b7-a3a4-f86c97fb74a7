package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoods;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
public interface MallScoreGoodsMapper extends BaseMapper<MallScoreGoods> {
    /**
     * 分页查询--后端页面，有权限限制
     *
     * @return
     */
    @DataFilter
    IPage<MallScoreGoods> getPage(IPage<MallScoreGoods> page, @Param("params") MallScoreGoodsParams params);

    IPage<MallScoreGoods> pageForApi(IPage<MallScoreGoods> page, @Param("params") MallScoreGoodsParams params);

    @DataFilter
    List<MallScoreGoodsDto> getGoodsList(@Param("params") MallScoreGoodsParams params);

    int sumGoodsCountBySku(String sku);

    /**
     * 星光闪耀积分商城列表
     *
     * @param page   分页参数
     * @param params 参数
     * @return 分页列表
     */
    IPage<MallScoreGoods> pageForApiStar(IPage<MallScoreGoods> page, @Param("params") MallScoreGoodsParams params);
}
