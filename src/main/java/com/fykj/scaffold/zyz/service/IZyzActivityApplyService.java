package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.dto.block_chain.ActivityApplyLongBlockChainDto;
import com.fykj.scaffold.zyz.domain.dto.report_form.VolunteerOrgSumDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.domain.params.*;
import fykj.microservice.core.base.IBaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动报名表-评价内容
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-09
 */
public interface IZyzActivityApplyService extends IService<ZyzActivityApply>, IBaseService<ZyzActivityApply> {
    /**
     * 分页查询报名审核
     *
     * @param params
     * @return
     */
    IPage<ZyzActivityApply> pagesForAudit(ZyzApplyParams params);

    /**
     * 报名审核
     *
     * @param idList
     * @param status
     * @param remark
     */
    void audit(List<Long> idList, Boolean status, String remark);




    /**
     * 分页查询活动评价
     *
     * @param params
     * @return
     */
    IPage<ZyzActivityApply> pageForServiceLong(ZyzApplyParams params);

    /**
     * 我的预约资源导出列表查询
     *
     * @param params
     * @return
     */
    List<ZyzActivityApplyLongExport> listForServiceLong(ZyzApplyParams params);

    /**
     * 我的预约资源导出列表查询
     *
     * @param params
     * @return
     */
    List<ZyzActivityApplyAuditExport> listForAudit(ZyzApplyParams params);

    /**
     * 根据活动id获取报名日志
     *
     * @param activityId
     * @return
     */
    List<ZyzActivityApplyLongDto> getApplyByActivityId(Long activityId);

    /**
     * 获取需要结算的活动报名记录
     *
     * @param actTimePeriodIds
     * @return
     */
    List<ZyzActivityApply> getNeedSettlementRecords(List<Long> actTimePeriodIds);

    /**
     * 志愿者--服务时长--分页查询
     *
     * @param params
     * @return
     */
    IPage<ZyzActivityApply> pageForVolunteerServiceLong(ZyzApplyParams params);

    /**
     * 志愿者--服务时长--导出查询
     *
     * @param params
     * @return
     */
    List<VolunteerServiceLongExportDto> listForVolunteerServiceLong(ZyzApplyParams params);

    /**
     * 获取待需要加入招募同步的活动数据
     *
     * @param actTimePeriodId
     * @return
     */
    List<ActSyncRecordDto> getNeedMemberSync(Long actTimePeriodId);

    /**
     * 获取待需要SGB加入招募同步的活动数据
     *
     * @param actTimePeriodId
     * @return
     */
    List<Long> getSgbNeedMemberSync(Long actTimePeriodId);

    /**
     * 获取待需要服务时长同步的活动数据
     *
     * @param actTimePeriodId
     * @return
     */
    List<ActSyncRecordDto> getNeedServiceTimeSync(Long actTimePeriodId);

    /**
     * 获取待需要SGB服务时长同步的活动数据
     *
     * @param actTimePeriodId
     * @return
     */
    List<Long> getSgbNeedServiceTimeSync(Long actTimePeriodId);

    /**
     * 通过报名id获取报名记录（封装）
     *
     * @param applyId
     * @return
     */
    ActSyncRecordDto getApplyRecordById(Long applyId);

    /**
     * 批量加入招募同步
     *
     * @param applies
     */
    void syncMemberBatch(List<ActSyncRecordDto> applies);

    /**
     * 单条加入招募同步
     *
     * @param apply
     */
    ZyzSyncLog syncMemberOne(ActSyncRecordDto apply);

    /**
     * 批量服务时长同步
     *
     * @param applies
     */
    void syncServiceTimeBatch(List<ActSyncRecordDto> applies);

    /**
     * 单条服务时长同步
     *
     * @param apply
     */
    ZyzSyncLog syncServiceTimeOne(ActSyncRecordDto apply);

    /**
     * 报名（活动详情-我要报名）
     */
    void apply(long timePeriodId);

    /**
     * 我的活动报名分页（我的活动）
     *
     * @param params
     * @return
     */
    IPage<ApplyPageDto> myActApplyPage(ZyzApplyParams params);

    /**
     * 取消报名（我的活动-取消报名）
     *
     * @param applyId
     * @param cancelRemark
     */
    void applyCancel(Long applyId, String cancelRemark);

    /**
     * 报名签到前置检查（我的活动-报名签到前置检查）
     *
     * @param applyId
     * @param longitude
     * @param latitude
     */
    Integer applySignCheck(Long applyId, BigDecimal longitude, BigDecimal latitude);

    /**
     * 报名签到（我的活动-报名签到）
     *
     * @param applyId
     * @param longitude
     * @param latitude
     */
    void applySign(Long applyId, BigDecimal longitude, BigDecimal latitude);

    /**
     * 报名删除（我的报名-报名删除）
     *
     * @param applyId
     */
    void applyRemove(Long applyId);

    /**
     * 我的报名分页（我的报名）
     *
     * @param params
     * @return
     */
    IPage<ApplyPageDto> myApplyPage(ZyzApplyParams params);

    /**
     * 小程序-我的-服务时长积分数据
     *
     * @param params
     * @return
     */
    IPage<ApplyPageDto> myServiceData(ZyzApplyParams params);

    /**
     * 获取不同维度的活动数量
     *
     * @return
     */
    DifferentDimensionActNumDto actNumByDifferentDimension();

    /**
     * 获取活动报名待审核数量
     *
     * @return
     */
    Integer getActApplyWaitAuditNum();

    /**
     * 获取团队活动下参与者的总服务时长
     *
     * @param teamPublish
     * @param orgCode
     * @param teamId
     * @return
     */
    BigDecimal getActTotalServiceTime(Boolean teamPublish, String orgCode, Long teamId);

    /**
     * 获取团队活动下参与者的今年服务时长
     * @param teamIds
     * @return
     */
    List<TeamActServiceLongDto> getActTotalServiceTimeThisYearForTeam(List<Long> teamIds);

    /**
     * 获取组织志愿者统计
     * @return
     */
    List<VolunteerOrgSumDto> getVolunteerOrgSum(ReportFormParams params);

    List<ActivityApplyLongBlockChainDto> getServiceLongForBlockchain(ServiceLongForBlockChainParamsDto dto);

    /**
     * pc端文明实践排行榜的活动统计
     * @param inOrgCode
     * @param rLikeOrgCode
     * @return
     */
    List<ZyzActivityApply> getActTimePeriodNum(List<String> inOrgCode, String rLikeOrgCode);

    /**
     * 根据时间段分组统计志愿者数量和服务时长
     * @param params 查询参数，包含活动ID
     * @return 统计结果列表
     */
    List<ActivityTimePeriodStatDto> getTimePeriodStats(ActivityTimePeriodStatParams params);

    /**
     * 根据活动ID和时间段ID查询志愿者姓名和服务时长
     * @param activityId 活动ID
     * @param timePeriodId 时间段ID
     * @return 志愿者服务时长详情列表
     */
    List<VolunteerServiceLongDetailDto> getVolunteerServiceLongDetails(ActivityTimePeriodStatParams params);

    /**
     * 根据志愿者手机号查询服务时长记录
     * @param params 查询参数
     * @return 服务时长记录分页数据
     */
    IPage<VolunteerServiceRecordDto> getVolunteerServiceRecords(ZyzVolunteerServiceParams params);

    /**
     * 按活动时间段统计参与人数和服务时长
     * @param params 查询参数
     * @return 统计结果分页数据
     */
    IPage<ActivityPeriodStatDto> getActivityPeriodStats(ZyzActivityPeriodStatParams params);
}
