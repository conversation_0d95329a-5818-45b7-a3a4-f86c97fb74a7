package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.zyz.domain.entity.AnnualAccountVolunteer;
import fykj.microservice.core.base.IBaseService;

public interface IAnnualAccountVolunteerService extends IBaseService<AnnualAccountVolunteer> {

    /**
     * 志愿者数据统计
     */
    void volunteerSum();

    /**
     * 志愿者数据统计
     * @param volunteerId
     */
    AnnualAccountVolunteer volunteerSum(Long volunteerId);
}
