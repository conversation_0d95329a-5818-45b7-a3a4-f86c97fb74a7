FROM 218.4.170.235:8081/library/java:8
ENV TZ=Asia/Shanghai
RUN /bin/cp /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
ADD volunteer_api-0.0.1-SNAPSHOT.jar scaffold.jar
VOLUME /tmp
VOLUME /home/<USER>
VOLUME /file
ENV JAVA_OPTS=""
#ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/scaffold.jar"]
#容器启动后需要执行的命令
ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar scaffold.jar"]
EXPOSE 9090
