package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation;
import com.fykj.scaffold.mall.domain.params.MallPadExchangeStationParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
public interface IMallPadExchangeStationService extends IBaseService<MallPadExchangeStation> {

    /**
     * 分页查询--后端页面
     */
    IPage<MallPadExchangeStation> getPage(MallPadExchangeStationParams params);

    /**
     * 分页查询--小程序页面
     * @param params
     * @return
     */
    IPage<MallPadExchangeStation> pageForApi(MallPadExchangeStationParams params);

    /**
     * 获取当前登录人组织及下级组织下的pad兑换站点
     *
     * @return
     */
    List<MallPadExchangeStation> getStationsByCurrOrg();

    /**
     * 启用/禁用
     * @param id
     */
    void statusChange(String id);
}

