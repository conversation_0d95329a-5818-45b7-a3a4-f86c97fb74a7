<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fykj.scaffold.capability.mapper.CourseUserMapper">

    <select id="querySignUp" resultType="com.fykj.scaffold.capability.domain.dto.CourseSignUpDTO">
        SELECT
            zv.id AS volunteerId,
            cc.id AS courseId,
            cc.course_name AS courseName,
            cc.course_type AS courseType,
            su.id AS userId,
            su.`name` AS `name`,
            su.mobile AS phone,
            zv.certificate_id AS certificateId,
            zv.org_code AS orgCode,
            so.`name` AS orgName,
            zv.address AS address,
            ccu.sign_up_at AS signUpAt,
            ccu.team_or_ent_name AS teamOrEntName,
            ccu.position
        FROM cu_course cc
        LEFT JOIN cu_course_user ccu ON cc.id = ccu.course_id
        LEFT JOIN sys_user su ON su.id = ccu.user_id
        LEFT JOIN zyz_volunteer zv ON su.mobile = zv.phone
        LEFT JOIN sys_org so ON so.`code` = zv.org_code
        <where>
            AND cc.is_deleted = 0
            AND su.is_deleted = 0
            AND ccu.is_deleted = 0
            AND zv.is_deleted = 0
            <if test="params.courseId != null">
                AND cc.id = #{params.courseId}
            </if>
            <if test="params.keyword != null and params.keyword != ''">
                AND (
                    su.`name` LIKE CONCAT('%', #{params.keyword}, '%')
                    OR
                    su.mobile LIKE CONCAT('%', #{params.keyword}, '%')
                    OR
                    zv.certificate_id LIKE CONCAT('%', #{params.keyword}, '%')
                )
            </if>
            <if test="params.courseName != null and params.courseName != ''">
                AND cc.course_name LIKE CONCAT('%', #{params.courseName}, '%')
            </if>
            <if test="params.signUpStartTime != null">
                AND ccu.sign_up_at >= #{params.signUpStartTime}
            </if>
            <if test="params.signUpEndTime != null">
                AND ccu.sign_up_at &lt;= #{params.signUpEndTime}
            </if>
        </where>
        ORDER BY ccu.id DESC
    </select>

    <select id="querySignIn" resultType="com.fykj.scaffold.capability.domain.dto.CourseSignInDTO">
        SELECT
            zv.id AS volunteerId,
            cc.id AS courseId,
            su.id AS userId,
            su.`name` AS `name`,
            su.mobile AS phone,
            zv.certificate_id AS certificateId,
            zv.org_code AS orgCode,
            so.`name` AS orgName,
            zv.address AS address,
            ccu.sign_up_at AS signUpAt,
            ccu.actual_credits AS actualCredits,
            ccu.sign_in_at AS signInAt,
            ccu.sign_in_address AS signInAddress,
            ccu.sign_in_latitude AS signInLatitude,
            ccu.sign_in_longitude AS signInLongitude,
            ccu.complete_status AS completeStatus,
            ccu.completed_at AS completedAt,
            CONCAT(cc.course_date, ' ', cc.course_start_time ) AS courseStartAt
        FROM cu_course cc
        LEFT JOIN cu_course_user ccu ON cc.id = ccu.course_id
        LEFT JOIN sys_user su ON su.id = ccu.user_id
        LEFT JOIN zyz_volunteer zv ON su.mobile = zv.phone
        LEFT JOIN sys_org so ON so.`code` = zv.org_code
        <where>
            AND cc.is_deleted = 0
            AND su.is_deleted = 0
            AND ccu.is_deleted = 0
            AND zv.is_deleted = 0
            <if test="params.courseId != null">
                AND cc.id = #{params.courseId}
            </if>
            <if test="params.keyword != null and params.keyword != ''">
                AND (
                    su.`name` LIKE CONCAT('%', #{params.keyword}, '%')
                    OR
                    su.mobile LIKE CONCAT('%', #{params.keyword}, '%')
                    OR
                    zv.certificate_id LIKE CONCAT('%', #{params.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY ccu.id DESC
    </select>
</mapper>
