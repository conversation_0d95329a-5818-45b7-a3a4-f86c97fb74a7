package com.fykj.scaffold.mall.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 机器积分兑换信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel("机器积分兑换信息")
@NoArgsConstructor
@AllArgsConstructor
public class PointExchangeDto {

    @ApiModelProperty(value = "订单号")
    @NotEmpty(message = "订单号不能为空")
    private String orderNum;

    @ApiModelProperty(value = "积分")
    @NotNull(message = "需要的积分不能为空")
    private BigDecimal point;

    @ApiModelProperty(value = "商品名称")
    @NotEmpty(message = "商品名称不能为空")
    private String goodsName;

    @ApiModelProperty(value = "机器号码")
    @NotEmpty(message = "机器号码为空")
    private String machineNo;

    @ApiModelProperty(value = "签名")
    @NotEmpty(message = "签名不能为空")
    private String sign;
}
