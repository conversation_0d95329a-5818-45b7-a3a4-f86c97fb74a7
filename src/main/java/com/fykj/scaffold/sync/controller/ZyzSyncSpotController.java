package com.fykj.scaffold.sync.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.sync.domain.dto.SyncSpotExportDto;
import com.fykj.scaffold.sync.domain.dto.SyncSpotImportDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncSpot;
import com.fykj.scaffold.sync.domain.params.ZyzSyncSpotParams;
import com.fykj.scaffold.sync.service.IZyzSyncSpotService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.Result;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 市平台同步实践点
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-06
 */
@RestController
@RequestMapping("/admin/platform/sync-spot")
public class ZyzSyncSpotController extends BaseController<IZyzSyncSpotService, ZyzSyncSpot, ZyzSyncSpotParams> {

    @AuditLog("保存实践点")
    @PostMapping("/saveSpot")
    @ApiOperation("保存实践点")
    public Result saveSpot(@RequestBody ZyzSyncSpot spot) {
        baseService.saveSpot(spot);
        return new Result();
    }

    @AuditLog("修改实践点")
    @PostMapping("/updateSpot")
    @ApiOperation("修改实践点")
    public Result updateSpot(@RequestBody ZyzSyncSpot spot) {
        baseService.updateSpot(spot);
        return new Result();
    }

    @GetMapping("/changeOnShelve")
    @ApiOperation("实践点上下架")
    public Result changeOnShelve(@RequestParam Long id) {
        baseService.changeOnShelve(id);
        return new Result();
    }

    @ApiOperation("实践点Excel模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/sync_spot_template.xlsx");
    }

    @ApiOperation("导入")
    @PostMapping(value = "/dataImport")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<SyncSpotImportDto> failureList = baseService.dataImport(excel);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "sync_spot_fill_template.xlsx", SyncSpotImportDto.class);
        }
    }

    @ApiOperation("导出")
    @PostMapping({"/export"})
    public void export(@RequestBody(required = false) ZyzSyncSpotParams params) {
        List<SyncSpotExportDto> res = baseService.export(params);
        ExcelUtil.writeExcel(res, "实践点数据列表", SyncSpotExportDto.class);
    }
}
