<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzVolunteerTeamMapper">
    <delete id="deleteByTeamIdAndVolunteerId">
        DELETE
        FROM zyz_volunteer_team
        WHERE team_id = #{teamId}
          and volunteer_id = #{volunteerId};
    </delete>
    <select id="getTeamVolunteerList" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerTeamDto">
        SELECT
        v.name as volunteerName,
        v.phone as phone,
        v.service_long as serviceLong,
        v.service_long_this_year as serviceLongThisYear,
        v.certificate_type as certificateType,
        v.certificate_id as certificateId,
        t.duty_type as dutyType,
        t.join_time as joinTime,
        t.team_id as teamId,
        v.id as volunteerId,
        v.status as volunteerStatus,
        t.is_sync as isSync,
        t.sync_remark as syncRemark,
        t.sync_time as syncTime
        FROM `zyz_volunteer_team` t
        inner JOIN zyz_volunteer v on v.id = t.volunteer_id and v.is_deleted = FALSE AND v.write_off = 0
        WHERE t.is_deleted = FALSE
        <if test="params.name != null and params.name != ''">
            and v.name LIKE CONCAT('%', CONCAT(#{params.name}, '%'))
        </if>
        <if test="params.teamId != null and params.teamId != ''">
            and t.team_id = #{params.teamId}
        </if>
        <if test="params.phone != null and params.phone != ''">
            and v.phone LIKE CONCAT('%', CONCAT(#{params.phone}, '%'))
        </if>
        <if test="params.certificateId != null and params.certificateId != ''">
            and v.certificate_id LIKE CONCAT('%', CONCAT(#{params.certificateId}, '%'))
        </if>
        <if test="params.joinStartDate != null ">
            and date( t.join_time) >= #{params.joinStartDate}
        </if>
        <if test="params.joinEndDate != null ">
            and #{params.joinEndDate} >= date( t.join_time)
        </if>
        ORDER BY t.join_time  desc
    </select>

    <select id="getTeamMembers" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerTeamDto">
        SELECT
            v.name AS volunteerName,
            v.phone AS phone,
            v.certificate_id AS certificateId,
            v.is_party AS party
        FROM zyz_volunteer_team t
        INNER JOIN zyz_volunteer v ON v.id = t.volunteer_id AND v.is_deleted = 0 AND v.write_off = 0
        WHERE t.is_deleted = 0
        <if test="teamId != null">
            AND t.team_id = #{teamId}
        </if>
        ORDER BY t.join_time DESC
    </select>

    <select id="getTeamVolunteerNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_volunteer_team
        WHERE is_deleted = 0 AND team_id = #{teamId}
    </select>

    <select id="getNeedSync" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam">
        SELECT team_id AS teamId, volunteer_id AS volunteerId
        FROM zyz_volunteer_team
        WHERE is_deleted = 0
          AND join_time &lt;= #{now}
          AND join_time &gt;= date_sub(NOW(), INTERVAL 15 DAY)
          AND (is_sync = 'sync_wait' OR (is_sync = 'sync_failure' AND join_time &gt;= #{threeDaysBeforeNow}))
    </select>

    <select id="getSgbNeedSync" resultType="java.lang.Long">
        SELECT vt.id
        FROM zyz_volunteer_team vt
        LEFT JOIN sgb_sync_status_record ssr
                  ON ssr.business_type = 'sgb_sync_biz_join_team'
                      AND ssr.business_id = vt.id
                      AND ssr.is_deleted = 0
        WHERE vt.is_deleted = 0
          AND vt.join_time &lt;= #{now}
          AND vt.join_time &gt;= date_sub(NOW(), INTERVAL 15 DAY)
          AND (
                ssr.id IS NULL
                OR (ssr.sync_status = 0 AND vt.join_time &gt;= #{threeDaysBeforeNow})
              )
    </select>

    <select id="getTeamVolunteerSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            t.id AS teamId,
            t.name AS teamName,
            IFNULL(nt.count, 0) AS registerNum
        FROM zyz_team t
        LEFT JOIN (
            SELECT
                vt.team_id,
                COUNT(DISTINCT vt.volunteer_id) AS count
            FROM zyz_volunteer_team vt
            WHERE vt.is_deleted = 0
            <if test="params.end != null">
                AND vt.join_time &lt;= #{params.end}
            </if>
            GROUP BY vt.team_id
        ) nt ON t.id = nt.team_id
        WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
            <if test="params.teamIdList != null and params.teamIdList.size > 0">
                AND t.id IN
                <foreach item="item" collection="params.teamIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>
    <select id="getTeamVolunteerApplyList" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerTeamApplyDto">
        SELECT
        v.id AS volunteerId,
        t.team_id AS teamId,
        v.name AS volunteerName,
        v.phone AS phone,
        v.certificate_type AS certificateType,
        p.activity_name AS activityName,
        p.time_period_start_time AS  timePeriodStartTime,
        p.time_period_end_time AS  timePeriodEndTime,
        p.sign_time AS signTime,
        a.address AS address,
        p.service_long AS serviceLong
        FROM `zyz_volunteer_team` t
        INNER JOIN zyz_volunteer v ON v.id = t.volunteer_id AND v.is_deleted = FALSE AND v.write_off = 0
        INNER JOIN zyz_activity_apply p on p.volunteer_id=v.id
        INNER JOIN zyz_activity a ON a.id=p.activity_id AND a.team_publish= TRUE AND a.team_id=t.team_id
        WHERE t.is_deleted = FALSE AND a.audit_status='act_audit_success' AND p.audit_status='act_apply_audit_success'
        <if test="params.name != null and params.name != ''">
            AND v.name LIKE CONCAT('%', CONCAT(#{params.name}, '%'))
        </if>
        <if test="params.volunteerId != null and params.volunteerId != ''">
            AND t.volunteer_id = #{params.volunteerId}
        </if>
        AND t.team_id = #{params.teamId}
        ORDER BY p.create_date  DESC
    </select>

    <select id="getTeamsByVolunteerPhone" resultType="com.fykj.scaffold.zyz.domain.dto.VolunteerTeamInfoDto">
        SELECT 
            t.id,
            t.name,
            t.team_photo as teamPhoto,
            t.team_number as teamNumber,
            t.civil_registration as civilRegistration,
            t.founded,
            t.business_supervisory_unit as businessSupervisoryUnit,
            t.register_place as registerPlace,
            t.team_no as teamNo,
            t.admin_name as adminName,
            t.admin_contact as adminContact,
            t.is_emphasis as isEmphasis,
            t.services,
            t.team_status as teamStatus,
            so.name as orgName,
            aa.livelyCount as livelyCountThisYear,
            aa.serviceLongThisYear as serviceLongThisYear
        FROM zyz_volunteer v
        INNER JOIN zyz_volunteer_team vt ON v.id = vt.volunteer_id AND vt.is_deleted = FALSE
        INNER JOIN zyz_team t ON vt.team_id = t.id AND t.is_deleted = FALSE
        LEFT JOIN sys_org so on t.org_code = so.code and so.is_deleted = FALSE
        LEFT JOIN (
            SELECT
                vt.team_id,
                sum(IF( v.is_lively = TRUE, 1, 0 )) AS livelyCount,
                SUM(service_long_this_year) as serviceLongThisYear
            FROM
                zyz_volunteer_team vt
                LEFT JOIN zyz_volunteer v ON v.id = vt.volunteer_id
            GROUP BY vt.team_id
        ) aa ON aa.team_id = t.id
        WHERE 
            v.is_deleted = FALSE 
            AND v.write_off = FALSE
            AND v.phone = #{phone}
        ORDER BY t.create_date DESC
    </select>
</mapper>
