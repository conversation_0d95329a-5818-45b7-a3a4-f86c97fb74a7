package com.fykj.scaffold.zsq_docking.receive.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.cms.domain.entity.CmsCategory;
import com.fykj.scaffold.cms.domain.entity.CmsCategoryContent;
import com.fykj.scaffold.cms.service.ICmsCategoryContentService;
import com.fykj.scaffold.cms.service.ICmsCategoryService;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.domain.entity.SysUserExpand;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.security.business.service.ISysUserExpandService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.zsq_docking.cons.NewsDataDockingTypeEnum;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import com.fykj.scaffold.zsq_docking.receive.aop.ZSQDataOperationLog;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.NewsDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import com.fykj.scaffold.zsq_docking.receive.service.INewsReceiveFromZSQService;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.Result;
import result.ResultCode;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.text.StrPool.LF;
import static com.fykj.scaffold.portal_website.conns.PortalWebsiteCons.*;
import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;
import static com.fykj.scaffold.support.conns.Cons.TOP_DEPT_CODE;
import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_NEWS_AUDIT_NOTICE;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.NewsDataDockingType.*;
import static constants.Mark.COMMA;

/**
 * 新闻接收知社区数据-服务实现类
 *
 * @Author：yangxu
 * @Date：2025/4/22 11:20
 * @Description：
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class NewsReceiveFromZSQServiceImpl implements INewsReceiveFromZSQService {

    @Autowired
    private INewsService newsService;

    @Autowired
    private ICmsCategoryService categoryService;

    @Autowired
    private ICmsCategoryContentService categoryContentService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;

    @Autowired
    private ISysUserExpandService userExpandService;

    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern(DATETIME_FORMAT);

    @Override
    @ZSQDataOperationLog
    public Result docking(NewsDockingDto dockingInfo, ZSQNewsInfoDto data, ZSQDockingNewsRecord dockingRecord) {
        String operation = dockingInfo.getOperation();
        String zwtUserId = dockingInfo.getZwtUserId();
        switch (operation) {
            case DT_CREATE:
                // 新闻创建
                return newsSave(zwtUserId, data, false);
            case DT_UPDATE:
                // 新闻编辑
                return newsUpdate(zwtUserId, data);
            case DT_DELETE:
                // 新闻删除
                Long newsId = dockingInfo.getNewsId();
                return newsDelete(zwtUserId, data, newsId);
            case DT_ON_OFF:
                // 新闻上/下架
                Boolean onShelf = dockingInfo.getOnShelf();
                return newsOnOff(zwtUserId, data, onShelf);
            default:
                throw new BusinessException(ResultCode.FAIL, "新闻操作类型{" + operation + "}异常！");
        }
    }

    private SysOrg validateNewsPublishOrg(String orgCode) {
        if (StringUtils.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "新闻创建组织未知！");
        }
        SysOrg org = orgService.getByCode(orgCode);
        if (org == null) {
            throw new BusinessException(ResultCode.FAIL, "新闻创建组织{" + orgCode + "}不存在！");
        }
        return org;
    }

    private List<SysUserExpand> validateZwtUser(String zwtUserId) {
        Long userId = ssoUserMappingService.getPlatformUserIdByProviderAndUserId(SsoCons.SsoProvider.ZWT.name(), zwtUserId);
        if (userId == null) {
            throw new BusinessException(ResultCode.FAIL, "政务通用户{" + zwtUserId + "（政务通 userid ）}在志愿者平台不存在！");
        }
        return userExpandService.getByUserId(userId);
    }

    private void newsBaseInfoSet(News news, ZSQNewsInfoDto data) {
        news.setTitle(data.getTitle());
        news.setTitleImgUrl(data.getPicture());
        news.setBriefIntroduction(data.getBrief());
        news.setDescription(data.getContentDesc());
        news.setEffectiveDate(data.getPublishTime());
        news.setType(NEWS_CMS_CODE);
        news.setGrounding(data.getOnShelf());
        news.setUseCustomLinks(data.getOutsideLink());
        news.setCustomLinks(data.getExternalLink());
    }

    private CmsCategoryContent newsCategoryLinkGenerate(Long newsId, ZSQNewsInfoDto data) {
        String newsType = data.getNewsType();
        if (newsType == null) {
            throw new BusinessException(ResultCode.FAIL, "知社区创建的新闻类型未知！");
        }
        CmsCategory pCategory = categoryService.getByCode(NEWS_CMS_CODE);
        CmsCategory category = categoryService.getByCode(newsType);
        if (category == null) {
            throw new BusinessException(ResultCode.FAIL, "新闻同步创建失败，知社区创建的新闻类型{" + newsType + "}志愿者系统不存在！");
        }
        Long pCategoryId = pCategory.getId(), categoryId = category.getId();
        CmsCategoryContent categoryContent = new CmsCategoryContent();
        categoryContent.setCategoryId(categoryId);
        categoryContent.setCategoryCode(newsType);
        categoryContent.setContentId(newsId);
        categoryContent.setParentId(pCategoryId);
        categoryContent.setCategoryList(String.join(COMMA, new String[]{String.valueOf(pCategoryId), String.valueOf(categoryId)}));
        return categoryContent;
    }

    private Result newsSave(String zwtUserId, ZSQNewsInfoDto data, Boolean update) {
        Long newsId = data.getId();
        // 校验新闻创建操作新闻是否已存在
        News news = newsService.lambdaQuery().eq(News::getId, newsId).one();
        if (news != null) {
            throw new BusinessException(ResultCode.FAIL, "新闻已存在，与接收知社区操作{" + NewsDataDockingTypeEnum.CREATE.getText() + "}冲突！");
        }
        // 获取新闻发布组织的信息
        String publishOrgCode = data.getOrgCode();
        SysOrg publishOrg = validateNewsPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
        // 获取当前操作人（政务通用户）的身份信息
        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
        // 校验发布组织是否在当前操作人的身份中
        if (!userOrgCodes.contains(publishOrgCode)) {
            throw new BusinessException(ResultCode.FAIL, "知社区用户{" + zwtUserId + "（政务通 userid ）}在志愿者平台没有该新闻发布组织{" + publishOrg.getName() + "}的管理身份" + (update ? "（更新时新增）!" : "！"));
        }
        // 通过该新闻的创建组织判断是否能通过知社区平台的账号进行创建操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该新闻发布组织为{" + publishOrgName + "}，所以知社区平台用户无法在志愿者平台同步创建该新闻" + (update ? "（更新时新增）!" : "！"));
        }
        // 创建新闻并保存
        news = new News();
        news.setId(newsId);
        newsBaseInfoSet(news, data);
        news.setOrgCode(publishOrgCode);
        news.setTeamPublish(Boolean.FALSE);
        String auditOrgCode = publishOrgLink.get(publishOrgLink.size() - 2);
        news.setAuditOrgCode(auditOrgCode);
        news.setAuditStatus(publishOrgLink.size() == 2 ? NAS_ASSOCIATION_WAIT_CHECK : NAS_SUB_ASSOCIATION_WAIT_CHECK);
        news.setSync(Cons.PlatformSyncState.WAIT_SYNC);
        newsService.save(news);
        // 保存新闻栏目关联关系
        CmsCategoryContent categoryContent = newsCategoryLinkGenerate(newsId, data);
        categoryContentService.save(categoryContent);
        // 发送提醒审核短信给审核组织负责人
        sendTmpMsg.sendToAuditOrg(SMT_NEWS_AUDIT_NOTICE, Collections.singletonList(news.getAuditOrgCode()), news.getTitle(), publishOrgName);
        return Result.get(true, "新闻同步创建成功" + (update ? "（更新时新增）!" : "！"));
    }

    private Result newsUpdate(String zwtUserId, ZSQNewsInfoDto data) {
        Long newsId = data.getId();
        // 校验新闻更新操作新闻是否存在
        News news = newsService.lambdaQuery().eq(News::getId, newsId).one();
        if (news == null) {
            return newsSave(zwtUserId, data, true);
//            throw new BusinessException(ResultCode.FAIL, "新闻不存在，与接收知社区操作【" + NewsDataDockingTypeEnum.UPDATE.getText() + "】冲突！");
        }
        // 获取新闻发布组织的信息
        String newsOrgCode = news.getOrgCode();
        String publishOrgCode = data.getOrgCode();
        if (!newsOrgCode.equals(publishOrgCode)) {
            throw new BusinessException(ResultCode.FAIL, "志愿者端新闻发布组织{" + newsOrgCode +"}和知社区修改的新闻所属组织{" + publishOrgCode + "}冲突！");
        }
        SysOrg publishOrg = validateNewsPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
        // 获取当前操作人（政务通用户）的身份信息
        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
        // 校验发布组织是否在当前操作人的身份中
        if (!userOrgCodes.contains(publishOrgCode)) {
            throw new BusinessException(ResultCode.FAIL, "知社区用户{" + zwtUserId + "（政务通 userid ）}在志愿者平台没有该新闻发布组织{" + publishOrg.getName() + "}的管理身份，无法同步修改该新闻数据！");
        }
        // 通过该新闻的创建组织判断是否能通过知社区平台的账号进行修改操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该新闻发布组织为{" + publishOrgName + "}，所以知社区平台用户无法在志愿者平台同步修改该新闻！");
        }
        // 创建修改并保存
        newsBaseInfoSet(news, data);
        String auditOrgCode = publishOrgLink.get(publishOrgLink.size() - 2);
        news.setAuditOrgCode(auditOrgCode);
        news.setAuditStatus(publishOrgLink.size() == 2 ? NAS_ASSOCIATION_WAIT_CHECK : NAS_SUB_ASSOCIATION_WAIT_CHECK);
        newsService.updateById(news);
        // 保存新闻栏目关联关系
        categoryContentService.removeByContentId(newsId, Boolean.FALSE);
        CmsCategoryContent categoryContent = newsCategoryLinkGenerate(newsId, data);
        categoryContentService.save(categoryContent);
        // 发送提醒审核短信给审核组织负责人
        sendTmpMsg.sendToAuditOrg(SMT_NEWS_AUDIT_NOTICE, Collections.singletonList(news.getAuditOrgCode()), news.getTitle(), publishOrgName);
        return Result.get(true, "新闻同步修改成功");
    }

    private Result newsDelete(String zwtUserId, ZSQNewsInfoDto data, Long newsId) {
        // 校验删除操作是否仍能在知社区平台获取到该新闻数据
        if (data != null) {
            throw new BusinessException(ResultCode.FAIL, "从知社区平台仍能获取新闻信息，与接收知社区操作{" + NewsDataDockingTypeEnum.DELETE.getText() + "}冲突！");
        }
        News news = newsService.lambdaQuery().eq(News::getId, newsId).one();
        // 校验是否志愿者平台已删除
        if (news == null) {
            return new Result(ResultCode.OK.code(), "该数据在知社区平台通知志愿者平台删除前已删除成功！");
        }
        // 获取新闻发布组织的信息
        String publishOrgCode = news.getOrgCode();
        SysOrg publishOrg = validateNewsPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
        // 获取当前操作人（政务通用户）的身份信息
        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
        // 校验当前操作人是否有权限删除该条数据
        boolean enableDelete = false;
        for (String it : publishOrgLink) {
            if (userOrgCodes.contains(it)) {
                enableDelete = true;
                break;
            }
        }
        if (!enableDelete) {
            throw new BusinessException(ResultCode.FAIL, "知社区用户{" + zwtUserId + "（政务通 userid ）}在志愿者平台没有该新闻发布组织{" + publishOrg.getName() + "}及其上级组织的任一管理身份，无法同步删除该新闻数据！");
        }
        // 通过该新闻的创建组织判断是否能通过知社区平台的账号进行删除操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该新闻发布组织为{" + publishOrgName + "}，所以知社区平台用户无法在志愿者平台同步删除该新闻！");
        }
        // 删除该新闻
        newsService.removeById(newsId);
        // 删除新闻栏目关联关系数据
        categoryContentService.removeByContentId(newsId, Boolean.FALSE);
        return Result.get(true, "新闻同步删除成功");
    }

    private Result newsOnOff(String zwtUserId, ZSQNewsInfoDto data, Boolean onShelf) {
        // 校验上下架操作是否通知了具体的上下架状态
        if (onShelf == null) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台通知志愿者平台新闻上/下架，但未获取到具体的上/下架操作参数！");
        }
        // 通知的上下架操作跟知社区平台的数据上下架状态不一致
        Boolean newsOnShelf = data.getOnShelf();
        if (!onShelf.equals(newsOnShelf)) {
            return Result.get(true, "知社区平台获取到的新闻数据上下架状态{" + (newsOnShelf ? "新闻上架" : "新闻下架") + "}和通知志愿者平台的操作{" + (onShelf ? "新闻上架" : "新闻下架") + "}冲突！");
        }
        Long newsId = data.getId();
        // 校验新闻是否存在
        News news = newsService.lambdaQuery().eq(News::getId, newsId).one();
        if (news == null) {
            throw new BusinessException(ResultCode.FAIL, "新闻不存在，与接收知社区操作{" + (onShelf ? "上架" : "下架") + "}冲突！");
        }
        Boolean newsCurrentOnShelf = news.getGrounding();
        if (onShelf.equals(newsCurrentOnShelf)) {
            return Result.get(true, "该数据在知社区平台通知志愿者平台前已进行" + (onShelf ? "上架" : "下架") + "操作！");
        }
        String publishOrgCode = data.getOrgCode();
        SysOrg publishOrg = validateNewsPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
        // 获取当前操作人（政务通用户）的身份信息
        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
        // 通过该新闻的创建组织判断是否能通过知社区平台的账号进行上/下架操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该新闻发布组织为{" + publishOrgName + "}，所以知社区平台用户无法在志愿者平台同步" + (onShelf ? "上架" : "下架") + "该新闻！");
        }
        // 校验当前操作人是否有权限上/下架该条数据
        String publishOrgParentOrgCode = publishOrgLink.get(publishOrgLink.size() - 2);
        SysOrg publishOrgParentOrg = orgService.getByCode(publishOrgParentOrgCode);
        if (publishOrgParentOrg == null) {
            throw new BusinessException(ResultCode.FAIL, "志愿者平台不存在该新闻发布组织的上级组织信息，无法对该新闻进行" + (onShelf ? "上架" : "下架") + "操作！");
        }
        if (!userOrgCodes.contains(publishOrgParentOrgCode)) {
            throw new BusinessException(ResultCode.FAIL, "知社区用户{" + zwtUserId + "（政务通 userid ）}在志愿者平台没有该新闻发布组织{" + publishOrg.getName() + "}的上级组织{" + publishOrgParentOrg.getName() + "}的管理身份，无法同步" +  (onShelf ? "上架" : "下架") + "该新闻数据！");
        }
        // 校验新闻是否审核通过
        String auditStatus = news.getAuditStatus();
        if (!NAS_PASS.equals(auditStatus)) {
            throw new BusinessException(ResultCode.FAIL, "该新闻在志愿者平台尚未审核通过，无法对该新闻进行" + (onShelf ? "上架" : "下架") + "操作！");
        }
        // 下架可直接修改保存
        if (!onShelf) {
            news.setGrounding(Boolean.FALSE);
            newsService.updateById(news);
            return Result.get(true);
        }
        // 上架需再校验两个平台的数据一致性
        List<CmsCategoryContent> categoryContents = categoryContentService.getByContentId(newsId, false);
        String volunteerNewsType;
        if (CollectionUtil.isEmpty(categoryContents)) {
            volunteerNewsType = null;
        } else {
            CmsCategoryContent categoryContent = categoryContents.get(0);
            volunteerNewsType = categoryContent.getCategoryCode();
        }
        List<String> notConsistentInfo = validatePlatformDataConsistent(data, news, volunteerNewsType);
        if (!notConsistentInfo.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台通知上架的新闻数据与志愿者平台数据存在信息不一致，无法同步上架该新闻！不一致信息如下：\n" + String.join(LF, notConsistentInfo));
        }
        // 上架修改保存
        news.setGrounding(Boolean.TRUE);
        newsService.updateById(news);
        return Result.get(true);
    }

    private List<String> validatePlatformDataConsistent(ZSQNewsInfoDto data, News news, String volunteerNewsType) {
        List<String> notConsistentInfo = new ArrayList<>();
        String newsTitle = news.getTitle(), dataTitle = data.getTitle();
        if (!validatePropertySame(newsTitle, dataTitle)) {
            notConsistentInfo.add("标题不一致{志愿者平台：" + dataTitle +"，知社区平台：" + dataTitle + "}");
        }
        String newsImg = news.getTitleImgUrl(), dataImg = data.getPicture();
        if (!validatePropertySame(newsImg, dataImg)) {
            notConsistentInfo.add("图片不一致{志愿者平台：" + newsImg +"，知社区平台：" + dataImg + "}");
        }
        String newsBrief = news.getBriefIntroduction(), dataBrief = data.getBrief();
        if (!validatePropertySame(newsBrief, dataBrief)) {
            notConsistentInfo.add("简介不一致{志愿者平台：" + newsBrief +"，知社区平台：" + dataBrief + "}");
        }
        String newsDesc = news.getDescription(), dataDesc = data.getContentDesc();
        if (!validatePropertySame(newsDesc, dataDesc)) {
            notConsistentInfo.add("详细描述不一致{志愿者平台：" + newsDesc +"，知社区平台：" + dataDesc + "}");
        }
        LocalDateTime newsPublishTime = news.getEffectiveDate(), dataPublishTime = data.getPublishTime();
        if (!validatePropertySame(newsPublishTime, dataPublishTime)) {
            notConsistentInfo.add("发布时间不一致{志愿者平台：" + (newsPublishTime == null ? "未知" : newsPublishTime.format(dtf)) +"，知社区平台：" + (dataPublishTime == null ? "未知" : dataPublishTime.format(dtf)) + "}");
        }
        String dataNewsType = data.getNewsType();
        if (!validatePropertySame(volunteerNewsType, dataNewsType)) {
            notConsistentInfo.add("新闻类型不一致{志愿者平台：" + volunteerNewsType +"，知社区平台：" + dataNewsType + "}");
        }
        Boolean newsUseLink = news.getUseCustomLinks(), dataUseLink = data.getOutsideLink();
        if (!validatePropertySame(newsUseLink, dataUseLink)) {
            notConsistentInfo.add("是否使用外链不一致{志愿者平台：" + newsUseLink +"，知社区平台：" + dataUseLink + "}");
        }
        String newsLink = news.getCustomLinks(), dataLink = data.getExternalLink();
        if (!validatePropertySame(newsLink, dataLink)) {
            notConsistentInfo.add("外链不一致{志愿者平台：" + newsLink +"，知社区平台：" + dataLink + "}");
        }
        return notConsistentInfo;
    }

    private boolean validatePropertySame(String propertyOne, String propertyTwo) {
        boolean bothEmpty = StringUtils.isEmpty(propertyOne) && StringUtils.isEmpty(propertyTwo);
        boolean bothNotEmpty = StringUtils.isNotEmpty(propertyOne) && propertyOne.equals(propertyTwo);
        return bothEmpty || bothNotEmpty;
    }

    private boolean validatePropertySame(Boolean propertyOne, Boolean propertyTwo) {
        boolean bothNull = propertyOne == null && propertyTwo == null;
        boolean bothNotNull = propertyOne != null && propertyOne.equals(propertyTwo);
        return bothNull || bothNotNull;
    }

    private boolean validatePropertySame(LocalDateTime propertyOne, LocalDateTime propertyTwo) {
        boolean bothNull = propertyOne == null && propertyTwo == null;
        boolean bothNotNull = propertyOne != null && propertyOne.equals(propertyTwo);
        return bothNull || bothNotNull;
    }
}
