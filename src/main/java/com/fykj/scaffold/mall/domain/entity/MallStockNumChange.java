package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

;

/**
 * 积分商城-库存变动
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_stock_num_change")
public class MallStockNumChange extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商品id
     */
    @TableField("stock_id")
    @ApiModelProperty(value = "物料id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stockId;

    /**
     * 变动前数量
     */
    @TableField("change_before_num")
    @ApiModelProperty(value = "变动前数量")
    private Integer changeBeforeNum;

    /**
     * 变动后数量
     */
    @TableField("change_after_num")
    @ApiModelProperty(value = "变动后数量")
    private Integer changeAfterNum;

    /**
     * 变动数量
     */
    @TableField("change_num")
    @ApiModelProperty(value = "变动数量")
    private Integer changeNum;

    /**
     * 原因
     */
    @TableField("remark")
    @ApiModelProperty(value = "原因")
    private String remark;

    /**
     * 变动方向
     */
    @TableField("change_type")
    @DictTrans(transTo = "changeTypeText")
    @ApiModelProperty(value = "变动方向")
    private String changeType;

    /**
     * 操作人用户id
     */
    @TableField("operate_user_id")
    @ApiModelProperty(value = "操作人用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operateUserId;

    /**
     * 操作人用户名称
     */
    @TableField("operate_user_name")
    @ApiModelProperty(value = "操作人用户名称")
    private String operateUserName;

    /**
     * 操作时间
     */
    @TableField("operate_date")
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "状态类型Text")
    private String changeTypeText;

}
