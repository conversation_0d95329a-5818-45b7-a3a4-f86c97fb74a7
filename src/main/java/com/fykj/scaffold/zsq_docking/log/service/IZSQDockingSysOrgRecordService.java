package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.dto.SysOrgDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingSysOrgRecord;
import fykj.microservice.core.base.IBaseService;
import result.Result;

import java.util.List;

public interface IZSQDockingSysOrgRecordService extends IBaseService<ZSQDockingSysOrgRecord> {

    /**
     * 获取组织对接记录列表
     *
     * @param orgId
     * @return
     */
    List<ZSQDockingSysOrgRecord> getSysOrgDockingRecords(Long sysOrgId);

    /**
     * 失败重试
     * @param orgId
     * @param recordId
     */
    void retry(Long orgId, Long recordId);
    /**
     * 获取对接记录详情
     * @param recordId
     * @return
     */
    SysOrgDockingRecordDetailDto getDockingRecordDetail(Long recordId);


    /**
     * 更新对接记录
     *
     * @param record
     * @param result
     */
    void updateDockingRecord(ZSQDockingSysOrgRecord record, Result result);

    /**
     * 更新对接记录
     *
     * @param recordId
     * @param result
     */
    void updateDockingRecord(Long recordId, Result result);
}
