package com.fykj.scaffold.portal_website.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cms_content")
public class VideoCategory extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 名称
     */
    @TableField("title")
    @NotBlank(message = "名称不能为空")
    @ApiModelProperty(value = "名称")
    private String title;

    /**
     * 所属区域
     */
    @TableField("district")
    @ApiModelProperty(value = "所属区域")
    @DictTrans
    private String district;

    /**
     * 图片
     */
    @TableField(value = "title_img_url", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "图片url")
    private String titleImgUrl;

    /**
     * 简介
     */
    @TableField(value = "brief_introduction", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "简介")
    private String briefIntroduction;

    /**
     * 内容
     */
    @TableField(value = "description", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "详细描述")
    private String description;

    /**
     * 生效时间
     */
    @TableField("effective_date")
    @NotNull(message = "生效时间不能为空")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)  //格式化前台日期参数注解
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    @ApiModelProperty(value = " 生效时间")
    private LocalDateTime effectiveDate;

    /**
     * 是否上架
     */
    @ApiModelProperty(value = "是否上架")
    @TableField("grounding")
    private Boolean grounding;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField("type")
    private String type;

    /**
     * 阅读量
     */
    @TableField("actual_reading")
    @ApiModelProperty(value = "阅读量")
    private Integer actualReading;

    /**
     * 排序
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "排序")
    private Integer sequence;

    /**
     * 栏目id
     */
    @ApiModelProperty(value = "栏目id")
    @TableField(exist = false)
    private List<String> categoryIds;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(exist = false)
    private String category;

    /**
     * 视频展示
     */
    @ApiModelProperty(value = "视频展示")
    @TableField(exist = false)
    private List<String> videoShow;
}
