package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.dto.ResourceDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingResourceRecord;
import fykj.microservice.core.base.IBaseService;
import result.Result;

import java.util.List;

public interface IZSQDockingResourceRecordService extends IBaseService<ZSQDockingResourceRecord> {

    /**
     * 获取资源对接记录列表
     *
     * @param resourceId
     * @return
     */
    List<ZSQDockingResourceRecord> getResourceDockingRecords(Long resourceId);

    /**
     * 失败重试
     * @param resourceId
     * @param recordId
     */
    void retry(Long resourceId, Long recordId);

    /**
     * 获取对接记录详情
     * @param recordId
     * @return
     */
    ResourceDockingRecordDetailDto getDockingRecordDetail(Long recordId);


    /**
     * 更新对接记录
     *
     * @param record
     * @param result
     */
    void updateDockingRecord(ZSQDockingResourceRecord record, Result result);

    /**
     * 更新对接记录
     *
     * @param recordId
     * @param result
     */
    void updateDockingRecord(Long recordId, Result result);
}
