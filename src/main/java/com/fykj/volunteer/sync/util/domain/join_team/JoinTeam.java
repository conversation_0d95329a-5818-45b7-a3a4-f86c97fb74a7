package com.fykj.volunteer.sync.util.domain.join_team;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 加入团队
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class JoinTeam {
    /**
     * 身份证编号(必填)
     */
    private String idcode;
    /**
     * 注册组织接口返回（必填）
     */
    private String orgid;
    /**
     * 格式：2016-06-14
     */
    private String jointime;
    /**
     * 是否专职,填写是或否(必填)
     */
    private String isfulltime;
    /**
     * 个人补充说明
     */
    private String description;
    /**
     * 唯一标识（必填）
     */
    private String uniquecode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "users")
    public static class Users {
        private List<JoinTeam> info;
    }

    public String buildXml() {
        Users users = new Users();
        users.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(users);
    }
}
