package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表
 *
 * <AUTHOR> @email ${email}
 * @date 2022-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_org")
public class SysOrg extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门code
     */
    @TableField("code")
    @ApiModelProperty(value = "部门code")
    private String code;

    /**
     * 部门code拼接
     */
    @TableField("code_prefix")
    @ApiModelProperty(value = "部门code拼接")
    private String codePrefix;

    /**
     * 部门名称
     */
    @TableField("name")
    @ApiModelProperty(value = "部门名称")
    private String name;


    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;

    /**
     * 层级（1协会，2分协会，3社区，4小区（预留））
     */
    @TableField("level")
    @ApiModelProperty(value = "层级（1协会，2分协会，3社区，4小区（预留））")
    private Integer level;

    /**
     * 父级部门ID
     */
    @TableField("parent_id")
    @ApiModelProperty(value = "父级部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 地址
     */
    @TableField("address")
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 人口
     */
    @TableField("population")
    @ApiModelProperty(value = "人口")
    private Integer population;

    /**
     * 介绍
     */
    @TableField("description")
    @ApiModelProperty(value = "介绍")
    private String description;

    /**
     * logo
     */
    @TableField("logo_url")
    @ApiModelProperty(value = "logo")
    private String logoUrl;

    /**
     * 经度
     */
    @TableField("longitude")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * gps84经度
     */
    @TableField("wsg_84_longitude")
    @ApiModelProperty(value = "gps84经度")
    private BigDecimal wsg84Longitude;

    /**
     * gps84纬度
     */
    @TableField("wsg_84_latitude")
    @ApiModelProperty(value = "gps84纬度")
    private BigDecimal wsg84Latitude;

    /**
     * 联系人
     */
    @TableField("link_man")
    @ApiModelProperty(value = "联系人")
    private String linkMan;

    /**
     * 联系电话
     */
    @TableField("link_mobile")
    @ApiModelProperty(value = "联系电话")
    private String linkMobile;

    /**
     * 联系人身份证
     */
    @TableField("link_id_card")
    @ApiModelProperty(value = "联系人身份证")
    private String linkIdCard;


    /**
     * 是否可以注册在此层级
     */
    @TableField("can_be_register")
    @ApiModelProperty(value = "是否可以注册在此层级")
    private Boolean canBeRegister;

    /**
     * 同步状态
     */
    @DictTrans(transTo = "syncText")
    @TableField("is_sync")
    @ApiModelProperty(value = "同步状态")
    private String isSync;

    @TableField(exist = false)
    private String syncText;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 同步备注
     */
    @TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 市局志愿者ID
     */
    @TableField("sync_id")
    @ApiModelProperty(value = "市局志愿者ID")
    private String syncId;

    /**
     * 职能部门id
     */
    @TableField("dep_id")
    @ApiModelProperty(value = "职能部门id")
    private String depId;

    /**
     * 职能部门中文
     */
    @TableField("dep_name")
    @ApiModelProperty(value = "市局志愿者ID")
    private String depName;

    /**
     * 行政区域code
     */
    @TableField("region_code")
    @ApiModelProperty(value = "行政区域code")
    private String regionCode;

    /**
     * 行政区域code
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "行政区域code")
    private String regionCodeLink;


    /**
     * 行政区域中文
     */
    @TableField("region_name")
    @ApiModelProperty(value = "行政区域中文")
    private String regionName;

    @TableField(exist = false)
    private List<SysOrg> children = new ArrayList<>();

    /**
     * 主要团队ID，同步文明办+社工部使用
     */
    @TableField(value = "main_team_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主要团队ID，同步文明办+社工部使用")
    private Long mainTeamId;

}
