package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 物料进补计划管理实体
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_stock_replenish_plan_detail")
public class MallStockReplenishPlanDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 进补计划单id
     */
    @TableField("plan_id")
    @ApiModelProperty(value = "进补计划单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;

    /**
     * 物料id
     */
    @TableField("stock_id")
    @ApiModelProperty(value = "物料id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stockId;

    /**
     * 物料名称
     */
    @TableField("stock_name")
    @ApiModelProperty(value = "物料名称")
    private String stockName;

    /**
     * 物料编号
     */
    @TableField("stock_sku")
    @ApiModelProperty(value = "物料编号")
    private String stockSku;

    /**
     * 物料单价
     */
    @TableField("unit_price")
    @ApiModelProperty(value = "物料单价")
    private BigDecimal unitPrice;

    /**
     * 库存数量
     */
    @TableField("inventory_quantity")
    @ApiModelProperty(value = "库存数量")
    private Integer inventoryQuantity;

    /**
     * 库存(总)数量(包括商品和兑换机中已分配的剩余)
     */
    @TableField("inventory_total_quantity")
    @ApiModelProperty(value = "库存(总)数量(包括商品和兑换机中已分配的剩余)")
    private Integer inventoryTotalQuantity;

    /**
     * 进补数量/预算数量
     */
    @TableField("replenish_quantity")
    @ApiModelProperty(value = "进补数量/预算数量")
    private Integer replenishQuantity;

    /**
     * 实际单位数量
     */
    @TableField("actual_unit_quantity")
    @ApiModelProperty(value = "实际单位数量")
    private Integer actualUnitQuantity;

    /**
     * 12个月数量集合
     */
    @TableField("month_num_set")
    @ApiModelProperty(value = "12个月数量集合")
    private String monthNumSet;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;

    /**
     * 物料是否存在
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "物料是否存在")
    private Boolean stockDeleted;
}
