package com.fykj.volunteer.sync.util.domain.sync_dep;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步团队信息
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncDep {

    /**
     * 职能部门名称
     */
    private String name;
    /**
     * 唯一号
     */
    private String uniCode;
    /**
     * 区划编号
     */
    private String regionCode;
    /**
     * 主管部门类别编号
     */
    private String typeId;
    /**
     * 上级部门编号
     */
    private String parentId;
    /**
     * 负责人姓名
     */
    private String contact;
    /**
     * 负责人电话
     */
    private String contactNo;
    /**
     * 介绍
     */
    private String intro;
    /**
     * 地址
     */
    private String mapAddress;
    /**
     * 腾讯坐标-经度
     */
    private String txMapLan;
    /**
     * 腾讯坐标-纬度
     */
    private String txMapLat;
    /**
     * 职能部门logo图片
     */
    private String picLogoUrl;
    /**
     * 接口调用类型
     */
    private String saveType;
    /**
     * 职能部门编号
     */
    private String dpId;

    /**
     * 保存类型
     */
    public static class SaveType {
        /**
         * 新增
         */
        public final static String ADD = "1";

        /**
         * 修改
         */
        public final static String MODIFY = "2";
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "dp")
    public static class Dp {
        private List<SyncDep> info;
    }

    public String buildXml() {
        Dp org = new Dp();
        org.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(org);
    }
}
