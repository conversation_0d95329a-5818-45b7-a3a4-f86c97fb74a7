package com.fykj.scaffold.zsq_docking.utils;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 鉴权请求头构建
 *
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class AuthHeaderUtil {

    /**
     * 里约网关密钥
     */
    private static final String LY_SECRET = "8BdaUjqRRdzhuwbjNE5EwW2W4LQJFMyA";
    /**
     * 里约网关标识
     */
    private static final String LY_APPID = "fengyuntech";


    /**
     * 鉴权请求头构建
     *
     * @return 组装好的请求头
     */
    public Headers authHeader() {
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String signature = SHA256Util.getSHA256(timestamp + LY_SECRET + nonce + timestamp);
        return new Headers.Builder()
                .add("x-tif-paasid", LY_APPID)
                .add("x-tif-timestamp", timestamp)
                .add("x-tif-nonce", nonce)
                .add("x-tif-signature", signature)
                .build();
    }

    /**
     * 鉴权请求头构建
     *
     * @return 组装好的请求头
     */
    public Map<String, String> addHeaders() {
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String signature = SHA256Util.getSHA256(timestamp + LY_SECRET + nonce + timestamp);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-tif-paasid", LY_APPID);
        headers.put("x-tif-timestamp", timestamp);
        headers.put("x-tif-nonce", nonce);
        headers.put("x-tif-signature", signature);
        headers.put("Content-Type", "application/json");
        return headers;
    }



}
