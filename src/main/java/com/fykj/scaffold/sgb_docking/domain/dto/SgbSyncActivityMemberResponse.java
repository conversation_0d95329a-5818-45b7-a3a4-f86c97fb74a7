package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 苏州社工部加入活动招募同步响应
 */
@Data
public class SgbSyncActivityMemberResponse {
    
    /**
     * 响应信息列表
     */
    private List<ActivityMemberResult> out;
    
    /**
     * 加入活动招募推送结果
     */
    @Data
    public static class ActivityMemberResult {

        /**
         * 响应状态码 - SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 唯一标识
         */
        private String uniCode;
    }
}
