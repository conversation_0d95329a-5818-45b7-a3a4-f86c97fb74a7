package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeInstance;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTransaction;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeTransactionParams;
import com.fykj.scaffold.zyz.domain.vo.PointsExchangeStatisticsVO;
import com.fykj.scaffold.zyz.domain.vo.TransactionStatisticsVO;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 勋章交易流水-服务类
 *
 * @date 2025-05-09
 */
public interface IZyzBadgeTransactionService extends IBaseService<ZyzBadgeTransaction> {

    /**
     * 批量创建勋章授权流水记录
     * @param instances 勋章实例列表
     * @param badgeId 勋章ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     */
    void batchCreateAwardTransactions(List<ZyzBadgeInstance> instances, Long badgeId, Long operatorId, String operatorName);
    
    /**
     * 自定义分页查询勋章交易流水
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<ZyzBadgeTransaction> queryPage(ZyzBadgeTransactionParams params);

    /**
     * 统计交易数量
     * @param params 查询参数
     * @return 统计结果
     */
    TransactionStatisticsVO statisticsTransaction(ZyzBadgeTransactionParams params);

    /**
     * 统计积分兑换档数量
     * @param params 查询参数
     * @return 统计结果
     */
    PointsExchangeStatisticsVO statisticsPointsExchange(ZyzBadgeTransactionParams params);
}

