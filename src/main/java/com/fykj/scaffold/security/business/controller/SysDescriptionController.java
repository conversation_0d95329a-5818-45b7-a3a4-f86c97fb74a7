package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.SysDescription;
import com.fykj.scaffold.security.business.domain.params.SysDescriptionParams;
import com.fykj.scaffold.security.business.service.ISysDescriptionService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;

/**
 * 系统规则配置
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-14
 */
@RestController
@RequestMapping("/admin/sys/description")
public class SysDescriptionController extends BaseController<ISysDescriptionService, SysDescription, SysDescriptionParams> {
    @SysLogMethod("新增修改系统规则描述")
    @ApiOperation(value = "新增修改系统规则描述")
    @PostMapping(value = "/saveOrUpdateDescription")
    public Result updateContent(@RequestBody @Validated SysDescription description) {
        baseService.saveOrUpdateDescription(description);
        return OK;
    }
}
