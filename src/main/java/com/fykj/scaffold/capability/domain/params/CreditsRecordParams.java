package com.fykj.scaffold.capability.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel("课程学分记录查询参数")
@EqualsAndHashCode(callSuper = true)
public class CreditsRecordParams extends BaseParams {

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

}
