//package com.fykj.scaffold.task.service;
//
//import com.fykj.scaffold.task.domain.dto.TaskMessingDto;
//import com.fykj.scaffold.task.domain.entity.CopyActivity;
//import com.fykj.scaffold.task.domain.entity.CopyActivityApply;
//import com.fykj.scaffold.task.domain.entity.ParameterCopyActivity;
//import com.fykj.scaffold.task.domain.params.AutoActivityApplyParam;
//import com.fykj.scaffold.task.domain.params.ParameterCopyActivityParams;
//import fykj.microservice.core.base.IBaseService;
//import org.springframework.data.domain.Page;
//
///**
// * 活动批量创建参数表
// *
// * 服务类
// * <AUTHOR>
// * @date 2023-09-25
// */
//public interface IParameterCopyActivityService extends IBaseService<ParameterCopyActivity> {
//    /**
//     * 活动分页根据任务id查询
//     * @param params
//     * @return
//     */
//    Page<CopyActivity> getPage(ParameterCopyActivityParams params);
//    /**
//     * 活动招募分页根据活动id查询
//     * @param params
//     * @return
//     */
//    Page<CopyActivityApply> getApplyPage(AutoActivityApplyParam params);
//
//    /**
//     * 查看实际的同步任务数据
//     * @param parameterId
//     * @return
//     */
//    TaskMessingDto queryByParameterId(Long parameterId);
//
//}
//
