package com.fykj.scaffold.zsq_docking.log.domain.params;


import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class ZSQDockingResourceRecordParams extends BaseParams {

    private static final long serialVersionUID = 2784701933826021395L;

    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    @ApiModelProperty(value = "对接记录状态")
    private Integer status;
}
