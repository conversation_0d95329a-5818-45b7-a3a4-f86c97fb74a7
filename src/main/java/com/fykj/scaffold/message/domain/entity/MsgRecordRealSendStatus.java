package com.fykj.scaffold.message.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("msg_record_real_send_status")
public class MsgRecordRealSendStatus extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @TableField("mobile")
    @ApiModelProperty(value = "手机号")
    private String Mobile;

    /**
     * 任务序列 id
     */
    @TableField("task_id")
    @ApiModelProperty(value = "任务序列 id")
    private String TaskID;

    /**
     * 状态报告
     */
    @TableField("status")
    @ApiModelProperty(value = "状态报告")
    private String Status;

    /**
     * 接收时间
     */
    @TableField("receive_time")
    @ApiModelProperty(value = "接收时间")
    private String ReceiveTime;

    /**
     * 上级网关返回值，不同网关返回值不同，仅作为参考
     */
    @TableField("error_code")
    @ApiModelProperty(value = "上级网关返回值，不同网关返回值不同，仅作为参考")
    private String ErrorCode;

    /**
     * 子号，即自定义扩展号
     */
    @TableField("ext_no")
    @ApiModelProperty(value = "子号，即自定义扩展号")
    private String ExtNo;
}
