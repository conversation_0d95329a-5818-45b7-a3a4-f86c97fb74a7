package com.fykj.scaffold.sgb_docking.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 数据同步状态记录-查询参数
 *
 * @date 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("数据同步状态记录-查询参数")
public class SgbSyncStatusRecordParams extends BaseParams {

    private static final long serialVersionUID = 7432472341237288640L;

    @ApiModelProperty("业务类型筛选")
    @MatchType(value = QueryType.EQ)
    private String bizType;

    @ApiModelProperty("业务ID")
    @MatchType(value = QueryType.EQ)
    private Long businessId;

    @ApiModelProperty("同步状态：0-未同步，1-已同步")
    @MatchType(value = QueryType.EQ)
    private Boolean syncStatus;

    @ApiModelProperty("同步备注，模糊匹配")
    @MatchType(value = QueryType.LIKE)
    private String syncRemark;

    @ApiModelProperty("社工部ID，模糊匹配")
    @MatchType(value = QueryType.LIKE)
    private String sgbId;

    @ApiModelProperty("同步时间开始")
    @JsonFormat(pattern = DATETIME_FORMAT)
    @MatchType(value = QueryType.GE, fieldName = {"syncTime"})
    private LocalDateTime syncTimeStart;

    @ApiModelProperty("同步时间结束")
    @JsonFormat(pattern = DATETIME_FORMAT)
    @MatchType(value = QueryType.LE, fieldName = {"syncTime"})
    private LocalDateTime syncTimeEnd;

}
