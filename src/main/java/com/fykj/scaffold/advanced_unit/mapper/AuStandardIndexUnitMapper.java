package com.fykj.scaffold.advanced_unit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitEvaluateListDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitScoreDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardIndexUnit;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardIndexUnitParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文明单位-指标标准-单位打分
 * <p>
 * Mapper 接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
public interface AuStandardIndexUnitMapper extends BaseMapper<AuStandardIndexUnit> {

    List<AuUnitEvaluateListDTO> queryEvaluatePage(
            IPage<AuUnitEvaluateListDTO> page,
            @Param("params") AuStandardIndexUnitParams params);

    List<AuStandardIndexUnit> queryIndexEvaluatePage(
            IPage<AuStandardIndexUnit> page,
            @Param("params") AuStandardIndexUnitParams params);
            
    /**
     * 查询所有单位的总分
     */
    List<AuUnitScoreDTO> queryAllUnitScores(
            @Param("params") AuStandardIndexUnitParams params);
}
