package com.fykj.scaffold.zyz.controller.mini;

import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.entity.SysUserAddress;
import com.fykj.scaffold.zyz.service.ISysUserAddressService;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/mini/sys/user/address")
@Api(tags = "微信小程序用户收获地址--需登录")
public class MiniSysUserAddressApiController {

    @Autowired
    private ISysUserAddressService userAddressService;

    @ApiOperation("保存或更新收货地址")
    @PostMapping("/saveOrUpdateAddress")
    public Result saveOrUpdateAddress(@RequestBody SysUserAddress sysUserAddress) {
        userAddressService.saveOrUpdateAddress(sysUserAddress);
        return new Result();
    }

    @ApiOperation("收货地址分页查询")
    @PostMapping("/getAddressList")
    public JsonResult<List<SysUserAddress>> getAddressList() {
        return new JsonResult<>(userAddressService.lambdaQuery().eq(SysUserAddress::getUserId, Oauth2Util.getUserId())
                .orderByDesc(SysUserAddress::getTacitlyApprove)
                .orderByDesc(BaseEntity::getCreateDate).list());
    }

    @ApiOperation("删除")
    @GetMapping("/removeById")
    public Result removeById(@RequestParam Long id) {
        userAddressService.removeById(id);
        return new Result();
    }

    @ApiOperation("收货地址详情")
    @GetMapping("/getById")
    public JsonResult<SysUserAddress> getById(@RequestParam Long id) {
        return new JsonResult<>(userAddressService.getById(id));
    }
}
