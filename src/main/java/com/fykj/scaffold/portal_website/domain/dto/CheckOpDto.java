package com.fykj.scaffold.portal_website.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CheckOpDto {
    /**
     * 是否点赞
     */
    @ApiModelProperty(value = " 是否点赞")
    private Boolean thumbsUp;
    /**
     * 是否收藏
     */
    @ApiModelProperty(value = " 是否收藏")
    private Boolean collect;
}
