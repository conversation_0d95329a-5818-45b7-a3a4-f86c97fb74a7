package com.fykj.scaffold.support.msg_send;

import java.util.List;

/**
 * 模版消息发送接口
 */
public interface ISendTmpMsg {

    /**
     * 发送模版消息给当前用户
     * @param code
     * @param params
     */
    void sendToCurrentUser(String code, Object... params);

    /**
     * 发送模版消息给指定用户
     * @param code
     * @param userIds
     * @param params
     */
    void sendToPointUser(String code, List<Long> userIds, Object... params);

    /**
     * 发送模版消息给团队联系人
     * @param code
     * @param teamIds
     * @param params
     */
    void sendToTeamLinkMan(String code, List<Long> teamIds, Object... params);

    /**
     * 发送模版消息给团队管理员
     * @param code
     * @param teamIds
     * @param params
     */
    void sendToTeamManager(String code, List<Long> teamIds, Object... params);

    /**
     * 发送模版消息给志愿者
     * @param code
     * @param volunteerIds
     * @param params
     */
    void sendToVolunteer(String code, List<Long> volunteerIds, Object... params);

    /**
     * 发送模版消息给审核组织负责人
     * @param code
     * @param orgCodes
     * @param params
     */
    void sendToAuditOrg(String code, List<String> orgCodes, Object... params);

    /**
     * 发送模版消息给活动发布人
     * @param code
     * @param activityIds
     * @param params
     */
    void sendToActivityPublisher(String code, List<Long> activityIds, Object... params);

    /**
     * 发送模版消息给需求发布人
     * @param code
     * @param requirementIds
     * @param params
     */
    void sendToRequirementPublisher(String code, List<Long> requirementIds, Object... params);

    /**
     * 发送模版消息给资源发布人
     * @param code
     * @param resourceIds
     * @param params
     */
    void sendToResourcePublisher(String code, List<Long> resourceIds, Object... params);

    /**
     * 发送模版消息给新闻发布人
     * @param code
     * @param newsIds
     * @param params
     */
    void sendToNewsPublisher(String code, List<Long> newsIds, Object... params);

    /**
     * 发送模版消息给活动公示发布人
     * @param code 信息编码
     * @param showIds 公示ids
     * @param params 其他参数
     */
    void sendToActivityShowPublisher(String code, List<Long> showIds, Object... params);

    /**
     * 发送模版消息给阵地计划发布人
     * @param code 信息编码
     * @param scheduleIds 计划ids
     * @param params 其他参数
     */
    void sendToActivitySchedulePublisher(String code, List<Long> scheduleIds, Object... params);

    /**
     * 发送模版消息给指定手机号的用户
     * @param code
     * @param phones
     * @param params
     */
    void commonSendByPhones(String code, List<String> phones, Object... params);
}
