<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.message.mapper.MsgSendMapper">

    <select id="pageForMsgSend" resultType="com.fykj.scaffold.message.domain.entity.MsgSend">
        SELECT
            ms.id AS id,
            ms.self_define AS selfDefine,
            ms.tmp_id AS tmpId,
            ms.self_define_msg AS selfDefineMsg,
            ms.title AS title,
            ms.send_type AS sendType,
            ms.status AS status,
            ms.send_code AS sendCode,
            ms.send_time AS sendTime,
            0 AS total,
            0 AS success,
            CASE WHEN ms.self_define = TRUE THEN ms.self_define_msg ELSE mt.tmp END AS msgContent
        FROM msg_send ms
        LEFT JOIN msg_tmp mt ON ms.tmp_id = mt.id
        WHERE
            ms.is_deleted = 0
        <if test="params.keyword != null and params.keyword != ''">
            AND (mt.code LIKE CONCAT('%',CONCAT(#{params.keyword}, '%'))
            OR mt.title LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
            OR mt.tmp LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
            OR ms.self_define_msg LIKE CONCAT('%', CONCAT(#{params.keyword}, '%')))
        </if>
        <if test="params.tmpId != null">
            AND ms.tmp_id = #{params.tmpId}
        </if>
        <if test="params.sendType != null and params.sendType != ''">
            AND ms.send_type LIKE CONCAT('%', CONCAT(#{params.sendType}, '%'))
        </if>
        <if test="params.status != null">
            AND ms.status = #{params.status}
        </if>
        <if test="params.selfDefine != null">
            AND ms.self_define = #{params.selfDefine}
        </if>
        ORDER BY ms.create_date DESC
    </select>

    <select id="totalSend" resultType="com.fykj.scaffold.message.domain.entity.MsgRecord">
        SELECT
            send_id,
            send_status
        FROM msg_record
        WHERE is_deleted = 0
        <if test="msgSendIds != null and msgSendIds.size > 0">
            AND send_id IN
            <foreach item="item" collection="msgSendIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
