package com.fykj.scaffold.security.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.XssTag;
import com.fykj.scaffold.security.business.domain.params.XssTagParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * XSS标签白名单
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
public interface IXssTagService extends IBaseService<XssTag> {
    /**
     * 模糊查询
     *
     * @param params
     * @return
     */
    IPage<XssTag> findByPage(XssTagParams params);

    /**
     * 新增
     *
     * @param info
     */
    void saveInfo(XssTag info);

    /**
     * 修改
     *
     * @param info
     */
    void updateInfo(XssTag info);

    /**
     * tag白名单
     *
     * @return String[] tags
     */
    String[] getTags();

    /**
     * tag和attribute白名单
     *
     * @return XssFilterUtil.Tag
     */
    List<XssTag> getAttrList();

    /**
     * tag和对应的attribute和enforcedValue白名单
     *
     * @return XssTag
     */
    List<XssTag> getEnforcedAttrList();

    /**
     * tag,attribute,protocol白名单
     *
     * @return XssFilterUtil.Tag
     */
    List<XssTag> getProtocolList();
}
