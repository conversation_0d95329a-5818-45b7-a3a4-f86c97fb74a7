package com.fykj.scaffold.mall.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * 将过期兑换券自动取消返还积分
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ExchangeCheckExpireTimeTask {
    @Autowired
    private IMallExchangeService mallExchangeService;

    @XxlJob("Exchange_change_sync_Handler")
    public void exchangeChangeSyncJobHandler() {
        XxlJobHelper.log("Exchange_change_sync_job, START!");
        // 获取待同步的活动数据以及活动结束时间三天内活动同步失败的数据
        List<MallExchange> exchangeList = mallExchangeService.lambdaQuery()
                .le(MallExchange::getExpireTime, LocalDate.now())
                .eq(MallExchange::getStatus, MallCons.MALL_EXCHANGE_STATUS_WRITTEN_EXCHANGE)
                .list();
        if(CollectionUtil.isEmpty(exchangeList)){
            return;
        }
        exchangeList.forEach(it->{
            mallExchangeService.cancelExchange(it.getId(),false);
        });
        XxlJobHelper.log("Exchange_change_sync_job, END!");
    }
}
