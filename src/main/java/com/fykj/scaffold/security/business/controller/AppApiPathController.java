package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.dto.ApiPathDto;
import com.fykj.scaffold.security.business.domain.entity.AppApiPath;
import com.fykj.scaffold.security.business.service.IAppApiPathService;
import com.fykj.scaffold.security.oauth2.filter.Oauth2FilterInvocationSecurityMetadataSource;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;
import result.ResultCode;

/**
 * 应用接口权限
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2021-05-28
 */
@RestController
@RequestMapping("/admin/app/path")
public class AppApiPathController extends BaseController<IAppApiPathService, AppApiPath, BaseParams> {

    @Autowired
    Oauth2FilterInvocationSecurityMetadataSource oauth2FilterInvocationSecurityMetadataSource;

    @ApiOperation("保存方法")
    @PostMapping({"/savePath"})
    public Result savePath(@RequestBody ApiPathDto dto) {
        boolean result = this.baseService.savePath(dto);
        if (result) {
            return new Result();
        }
        return new Result(ResultCode.FAIL);
    }
}
