package com.fykj.scaffold.mall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsNumChange;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsNumChangeParams;
import fykj.microservice.core.base.IBaseService;

/**
 * 
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-02-22
 */
public interface IMallScoreGoodsNumChangeService extends IService<MallScoreGoodsNumChange>, IBaseService<MallScoreGoodsNumChange> {
    /**
     * 分页查询--后端页面
     */
    IPage<MallScoreGoodsNumChange> getPage(MallScoreGoodsNumChangeParams params);
}

