package com.fykj.scaffold.race.controller;

import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.domain.entity.RacePrizePhotoJson;
import com.fykj.scaffold.race.domain.params.RacePrizePhotoJsonParams;
import com.fykj.scaffold.race.service.IRacePrizePhotoJsonService;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 摄影大赛-获奖作品json
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-06-08
 */
@RestController
@RequestMapping("/admin/race/prize/photo/json")
public class RacePrizePhotoJsonController extends BaseController<IRacePrizePhotoJsonService, RacePrizePhotoJson,RacePrizePhotoJsonParams> {


    @ApiOperation(value = "审核")
    @PostMapping(value = "/saveJson")
    public Result saveJson(@RequestBody RacePrizePhotoJson entity) {

        if (baseService.saveJson(entity)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }

    @ApiOperation(value = "获取获取作品")
    @GetMapping(value = "/getPrizePhoto")
    public JsonResult<List<Photo>> getPrizePhoto(@RequestParam Long gradeId) {
        List<Photo> result = baseService.getPrizePhoto(gradeId);
        return new JsonResult<>(result);
    }


    /**
     * 将七牛云的图片保存到本地
     *
     * @param
     * @return
     */
    @ApiOperation("将七牛云的图片保存到本地")
    @PostMapping(value = "/uploadPhotoFromQYN")
    public Result uploadPhotoFromQYN(Long gradeId) throws IOException {
        try {
            RacePrizePhotoJson json = baseService.getJson(gradeId);
            json.getPictureList().forEach(item -> {
                item.getPhotos().forEach(it -> {
                    //根据地址从七牛云下载图片
//                    String path = savePhotoByQYN(it.getPath(),it.getPath());
                   it.setPath(it.getPath());
//                    String slimPathAfter = StringUtils.substringBefore(it.getSlimPath(), "?");
                    String slimPath = savePhotoByQYN(it.getSlimPath(),it.getSlimPath());
                    it.setSlimPath(slimPath);
                });
            });
            baseService.updateById(json);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return OK;
    }

    private static String savePhotoByQYN(String url,String fileName) {
        return OssSaveUtil.save(url, OssCons.OSS_LOCAL,fileName).getUrl();
    }
}
