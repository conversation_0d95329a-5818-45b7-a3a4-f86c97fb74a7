<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzBadgeTradeListingMapper">

    <select id="pageQuery" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTradeListing">
        SELECT
            *,
            zb.`name` AS `name`
        FROM zyz_badge_trade_listing zbtl
        LEFT JOIN  zyz_badge zb ON zbtl.badge_id = zb.id
        <where>
            <if test="params.name != null and params.name != ''">
                AND zb.`name` LIKE CONCAT('%', #{params.name}, '%')
            </if>
            <if test="params.sellerName != null and params.sellerName != ''">
                AND zbtl.seller_name LIKE CONCAT('%', #{params.sellerName}, '%')
            </if>
            <if test="params.status != null">
                AND zbtl.`status` = #{params.status}
            </if>
            <if test="params.fromPlatform != null">
                AND zbtl.from_platform = #{params.fromPlatform}
            </if>
            <if test="params.publishDateStart != null">
                AND zbtl.create_date &gt;= #{params.publishDateStart}
            </if>
            <if test="params.publishDateEnd != null">
                AND zbtl.create_date &lt;= #{params.publishDateEnd}
            </if>
        </where>
        ORDER BY zbtl.create_date DESC
    </select>
    
    <!-- 查询交易大厅列表 -->
    <select id="queryTradingListings" resultType="com.fykj.scaffold.zyz.domain.vo.ZyzBadgeTradeHallVO">
        SELECT
            l.*,
            l.id AS listingId,
            b.name as badgeName,
            b.badge_file as badgeFile,
            b.preview_image as badgePreviewImage
        FROM zyz_badge_trade_listing l
        LEFT JOIN zyz_badge b ON l.badge_id = b.id
        <where>
            l.status = 0 <!-- 上架状态为发布中 -->
            AND l.is_deleted = 0
            AND b.is_deleted = 0
            <if test="params.badgeId != null">
                AND l.badge_id = #{params.badgeId}
            </if>
            <if test="params.name != null and params.name != ''">
                AND b.name LIKE CONCAT('%', #{params.name}, '%')
            </if>
            <if test="params.publishDateStart != null">
                AND l.create_date &gt;= #{params.publishDateStart}
            </if>
            <if test="params.publishDateEnd != null">
                AND l.create_date &lt;= #{params.publishDateEnd}
            </if>
        </where>
        ORDER BY l.create_date DESC
    </select>
    <resultMap id="entryMap" type="java.util.AbstractMap$SimpleEntry">
        <result column="key" property="key" javaType="int"/>
        <result column="value" property="value" javaType="int"/>
    </resultMap>

    <select id="getListingPointRange" resultMap="entryMap">
        SELECT
            IFNULL(MIN(l.points_before_fee), 0) AS `key`,
            IFNULL(MAX(l.points_before_fee), 0) AS `value`
        FROM zyz_badge_trade_listing l
        WHERE l.status = 0
          AND l.is_deleted = 0
          AND l.badge_id = #{badgeId}
    </select>


</mapper>