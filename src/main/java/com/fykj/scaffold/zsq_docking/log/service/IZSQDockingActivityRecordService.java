package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.dto.ActivityDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingActivityRecord;
import fykj.microservice.core.base.IBaseService;
import result.Result;

import java.util.List;

public interface IZSQDockingActivityRecordService extends IBaseService<ZSQDockingActivityRecord> {

    /**
     * 获取活动对接记录
     *
     * @param activityId
     * @return
     */
    List<ZSQDockingActivityRecord> getActivityDockingRecords(Long activityId);

    /**
     * 更新对接记录
     *
     * @param record
     * @param result
     */
    void updateDockingRecord(ZSQDockingActivityRecord record, Result result);

    /**
     * 更新对接记录
     *
     * @param recordId
     * @param result
     */
    void updateDockingRecord(Long recordId, Result result);

    /**
     * 失败重试
     * @param activityId
     * @param recordId
     */
    void retry(Long activityId, Long recordId);

    /**
     * 获取对接记录详情
     * @param recordId
     * @return
     */
    ActivityDockingRecordDetailDto getDockingRecordDetail(Long recordId);

}
