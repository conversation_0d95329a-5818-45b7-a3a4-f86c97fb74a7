package com.fykj.scaffold.security.business.service;


import com.fykj.scaffold.security.business.domain.dto.SsoUserImportDto;
import com.fykj.scaffold.security.business.domain.dto.ZsqUserSyncDto;
import com.fykj.scaffold.security.business.domain.entity.SsoUserMapping;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * SSO用户与平台用户关联关系-服务类
 * Date: 2025-04-29
 */
public interface ISsoUserMappingService extends IBaseService<SsoUserMapping> {

    /**
     * 根据sso平台和ssoUserId获取平台用户ID
     * @param ssoProvider sso平台
     * @param userId ssoUserId
     * @return platformUserId
     */
    Long getPlatformUserIdByProviderAndUserId(String ssoProvider, String userId);

    void updateSsoUserMappingAsync(String ssoProvider, String userId, String rawUserInfo);

    void deleteSsoUserMapping(String ssoProvider, String userId);

    /**
     * 根据sso平台和志愿者平台用户ID获取sso平台用户ID
     * @param ssoProvider sso平台
     * @param userId ssoUserId
     * @return platformUserId
     */
    String getSSOUserIdByProviderAndZYZUserId(String ssoProvider, Long userId);

    List<SsoUserImportDto> importSsoUser(MultipartFile excel, String ssoProvider);

    /**
     * 同步知社区用户
     * @return
     */
    List<ZsqUserSyncDto> syncZsqUser(String startDate, String endDate);

}

