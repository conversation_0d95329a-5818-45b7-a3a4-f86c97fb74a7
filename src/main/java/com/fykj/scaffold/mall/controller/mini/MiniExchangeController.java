package com.fykj.scaffold.mall.controller.mini;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallStarExchangeDto;
import com.fykj.scaffold.mall.domain.dto.PointExchangeDto;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.params.MallExchangeParams;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/mini/exchange")
@Api(tags = "微信小程序兑换信息接口")
public class MiniExchangeController {
    @Autowired
    private IMallExchangeService exchangeService;

    @ApiOperation("兑换")
    @GetMapping("/exchange")
    public Result exchange(@RequestParam Long id, @RequestParam Long addressId, @RequestParam String orderNo) {
        if (!exchangeService.exchange(id, addressId, orderNo, true)) {
            throw new BusinessException(ResultCode.FAIL, "当前网络拥挤，兑换失败请稍后再试");
        }
        return new Result();
    }

    @ApiOperation("PAD兑换")
    @GetMapping("/padGoodsExchange")
    public Result padGoodsExchange(@RequestParam Long id, @RequestParam String orderNo, @RequestParam String stationCode) {
        if (!exchangeService.padGoodsExchange(id, orderNo, stationCode)) {
            throw new BusinessException(ResultCode.FAIL, "当前网络拥挤，兑换失败请稍后再试");
        }
        return new Result();
    }

    @ApiOperation("兑换")
    @GetMapping("/pointCardReturnTip")
    public Result pointCardReturnTip() {
        return new JsonResult<>(exchangeService.pointCardReturnTip());
    }

    @ApiOperation("扫描机器二维码")
    @PostMapping(value = "/scanQrCode")
    public Result scanQrCode(@RequestBody PointExchangeDto dto) {
        exchangeService.exchangeByMachine(dto);
        return new Result();
    }

    @ApiOperation("核销")
    @GetMapping("/writeOff")
    public Result writeOff(@RequestParam Long id, @RequestParam String orderNo) {
        if (!exchangeService.exchange(id, null, orderNo, false)) {
            throw new BusinessException(ResultCode.FAIL, "当前网络拥挤，兑换失败请稍后再试");
        }
        return new Result();
    }


    @ApiOperation("取消兑换")
    @GetMapping("/cancelExchange")
    public Result cancelExchange(@RequestParam Long id) {
        if (!exchangeService.cancelExchange(id, true)) {
            throw new BusinessException(ResultCode.FAIL, "当前网络拥挤，取消兑换失败请稍后再试");
        }
        return new Result();
    }

    @ApiOperation("兑换记录分页查询")
    @PostMapping("/pageForApi")
    public JsonResult<IPage<MallExchange>> pageForApi(@RequestBody MallExchangeParams params) {
        return new JsonResult<>(exchangeService.pageForApi(params));
    }

    @ApiOperation("兑换页面详情")
    @GetMapping("/getDetail")
    public JsonResult<MallExchange> getDetail(@RequestParam Long id) {
        return new JsonResult<>(exchangeService.getExchangeForApi(id));
    }

    @ApiOperation("星光闪耀商品兑换")
    @GetMapping("/exchangeForStar")
    public JsonResult<MallStarExchangeDto> exchangeForStar(@RequestParam Long id, @RequestParam String orderNo) {
        return new JsonResult<>(exchangeService.exchangeForStar(id, orderNo));

    }
}
