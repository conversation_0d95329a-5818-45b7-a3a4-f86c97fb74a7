package com.fykj.scaffold.security.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.dto.NavDto;
import com.fykj.scaffold.security.business.domain.dto.PrivilegeChangeDto;
import com.fykj.scaffold.security.business.domain.dto.SaCaAccountPrivilegesDto;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.domain.params.UserParams;
import com.fykj.scaffold.zyz.domain.dto.CommunityAdminImportDto;
import com.fykj.scaffold.zyz.domain.dto.TeamAdminImportDto;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;
import result.Result;

import java.util.List;


/**
 * <p>
 * 用户服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public interface IUserService extends IBaseService<User> {


    /**
     * 根据主键获取用户显示名称
     *
     * @param id 用户主键
     * @return 用户名称
     */
    String getNameById(Long id);

    /**
     * 重置密码
     *
     * @param id
     * @return
     */
    String resetPassWord(Long id);

    /**
     * 修改密码
     *
     * @param oldPassWord 旧密码
     * @param newPassWord 新密码
     * @return
     */
    Result editPassWord(String username, String oldPassWord, String newPassWord);


    /**
     * 强制修改密码，无需旧密码
     *
     * @param newPassWord 新密码
     * @return
     */
    Result forceUpdatePwd(String username, String newPassWord);

    /**
     * 分页查询
     *
     * @param params 用户签到情况查询参数
     * @return 返回签到用户信息
     */
    IPage<User> page(BaseParams params);

    /**
     * 手机号是否存在
     * @param userPhone 手机号
     * @return 手机号是否存在
     */
    boolean userPhoneExists(String userPhone);

    /**
     * 获取用户菜单按钮权限
     *
     * @return
     */
    NavDto userMenusAndActionAuthorities(String type);

    /**
     * 刷新用户上下文信息
     *
     * @param username
     */
    void refreshUserContext(String username);

    /**
     * 刷新用户上下文信息
     *
     * @param userId
     */
    void refreshUserContext(Long userId);

    /**
     * 刷新用户上下文信息
     *
     * @param username
     */
    void refreshUserContext(String username, String clientId);

    /**
     * 用户强制下线
     *
     * @param username
     */
    void forceOfflineUser(String username);

    /**
     * 通过手机号强制用户
     *
     * @param phone
     */
    void forceOfflineUserByPhone(String phone);

    /**
     * 通过userId强制用户下线
     * @param userId
     */
    void forceOfflineUserByUserId(Long userId);

    /**
     * 更新openid
     *
     * @param openId openid
     */
    void updateOpenId(long userId, String openId);

    /**
     * 更新unionId
     *
     * @param unionId unionId
     */
    void updateUnionId(long userId, String unionId);


    /**
     * 根据openid获取用户信息
     *
     * @param openId openid
     * @return 用户
     */
    User getByOpenId(String openId);

    /**
     * 根据unionId获取用户信息
     *
     * @param unionId unionId
     * @return 用户
     */
    User getByUnionId(String unionId);


    /**
     * 生成onecode
     *
     * @param username 用户名
     * @return onecode
     */
    String genOneCode(String username);

    /**
     * 旧系统社区管理员账号导入
     * @param excel
     * @return
     */
    List<CommunityAdminImportDto> communityAdminImport(MultipartFile excel);

    /**
     * 就系统团队管理员账号导入
     * @param excel
     * @return
     */
    List<TeamAdminImportDto> teamAdminImport(MultipartFile excel);

    /**
     * 分协会管理员查看账号权限列表
     * @param params
     * @return
     */
    IPage<User> pageForSubAssociationAdmin(UserParams params);

    /**
     * 分协会管理员导出账号权限列表
     * @return
     */
    List<SaCaAccountPrivilegesDto> getListForSubAssociationAdmin(UserParams params);

    /**
     * 获取用户的所站权限
     * @param id
     * @return
     */
    List<String> getUserSACPrivileges(Long id);

    /**
     * 账号权限变更
     * @param data
     */
    void privilegesChange(PrivilegeChangeDto data);

    /**
     * 根据手机号获取用户
     * @param mobile 手机号
     * @return 用户
     */
    User getByMobile(String mobile);

    User getWithRoleAndExpandByMobile(String mobile);

    /**
     * 用户注销删除用户
     * @param ids
     */
    void userWriteOffRemove(List<Long> ids);
}
