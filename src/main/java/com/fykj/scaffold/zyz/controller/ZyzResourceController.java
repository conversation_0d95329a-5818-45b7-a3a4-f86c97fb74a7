package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.entity.ZyzResourceAppointment;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceParams;
import com.fykj.scaffold.zyz.service.IZyzResourceService;
import constants.Mark;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 资源表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-02
 */
@RestController
@RequestMapping("/admin/zyz/resource")
@Api(tags = "资源接口")
public class ZyzResourceController extends BaseController<IZyzResourceService, ZyzResource, ZyzResourceParams> {

    @ApiOperation("资源审核分页查询")
    @PostMapping({"/pagesForAudit"})
    public JsonResult<IPage<ZyzResource>> pagesForAudit(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        IPage<ZyzResource> result = this.baseService.pagesForAudit(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @AuditLog("资源-删除")
    @Override
    public Result removeByIds(String ids) {
        return super.removeByIds(ids);
    }

    @AuditLog("资源审核列表导出")
    @ApiOperation("资源审核列表导出")
    @PostMapping({"/exportForAudit"})
    public void exportForAudit(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        List<ResourceAuditExportDto> res = baseService.listForAuditExport(params);
        ExcelUtil.writeExcel(res, "需求列表", ResourceAuditExportDto.class);
    }

    @AuditLog("资源审核")
    @ApiOperation("资源审核")
    @GetMapping("/audit")
    public Result audit(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String remark) {
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.audit(idList, status, remark);
        return OK;
    }

    @ApiOperation("资源预约分页查询")
    @PostMapping({"/pagesForAppoint"})
    public JsonResult<IPage<ZyzResource>> pagesForAppoint(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        IPage<ZyzResource> result = this.baseService.pagesForAppoint(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("资源预约导出列表")
    @PostMapping({"/exportForAppoint"})
    public void exportForDocking(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        List<ResourceAppointmentExportDto> res = baseService.listForAppoint(params);
        ExcelUtil.writeExcel(res, "资源预约列表", ResourceAppointmentExportDto.class);
    }

    @ApiOperation("获取资源列表--分页查询")
    @PostMapping("/pagesForResource")
    public JsonResult<IPage<ZyzResource>> pagesForResource(@RequestBody ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        IPage<ZyzResource> iPage = baseService.pagesForResource(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }


    @AuditLog("审核通过资源上下架")
    @ApiOperation("上下架")
    @GetMapping("/updateAutoStatus")
    public Result updateAutoStatus(Long id) {
        if (!baseService.updateAutoStatus(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("批量上架下架")
    @ApiOperation("批量上架下架")
    @GetMapping("/updateAutoStatusByIds")
    public Result updateAutoStatusByIds(@RequestParam String ids, @RequestParam Boolean autoStatus){
        List<Long> idList = Arrays.stream(ids.split(Mark.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        baseService.updateAutoStatusByIds(idList, autoStatus);
        return OK;
    }

    @AuditLog("保存资源草稿")
    @ApiOperation("保存资源草稿")
    @PostMapping("/saveResource")
    public Result saveResource(@RequestBody ZyzResource entity) {
        baseService.saveResource(entity);
        return OK;
    }
    @AuditLog("修改资源草稿")
    @PostMapping("/updateResource")
    public Result updateResource(@RequestBody ZyzResource entity) {
        baseService.updateResource(entity);
        return OK;
    }

    @AuditLog("草稿提交")
    @ApiOperation("提交")
    @GetMapping("/renderById")
    public Result renderById(Long id) {
        if (!baseService.render(id)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("保存资源草稿并提交")
    @ApiOperation("提交")
    @PostMapping("/renderByResource")
    public Result renderByResource(@RequestBody ZyzResource entity) {
        if (!baseService.renderByResource(entity)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @AuditLog("资源列表导出")
    @ApiOperation("资源列表导出")
    @PostMapping({"/exportResource"})
    public void exportResource(@RequestBody(required = false) ZyzResourceParams params) {
        List<ResourceExportDto> res = baseService.listForResource(params);
        ExcelUtil.writeExcel(res, "需求列表", ResourceExportDto.class);
    }

    @ApiOperation("我的预约分页查询")
    @PostMapping({"/pagesForMyAppointment"})
    public JsonResult<IPage<ZyzResourceDto>> pagesForMyAppointment(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        IPage<ZyzResourceDto> result = this.baseService.pagesForMyAppointment(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @AuditLog("预约资源导出")
    @ApiOperation("预约资源导出")
    @PostMapping({"/exportForMyAppointment"})
    public void exportMyAppointment(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        List<MyAppointmentExportDto> res = baseService.listForMyAppointment(params);
        ExcelUtil.writeExcel(res, "我的预约资源列表", MyAppointmentExportDto.class);
    }

    @AuditLog("取消预约")
    @ResponseBody
    @RequestMapping(value = "/cancelAppointment", method = RequestMethod.POST)
    @ApiOperation(value = "取消预约")
    public Result cancelAppointment(long id, long appointmentId) {
        if (!baseService.cancelAppointment(id, appointmentId)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }

    @ResponseBody
    @RequestMapping(value = "/getAppointmentByResourceId", method = RequestMethod.POST)
    @ApiOperation(value = "根据id获取预约记录")
    public JsonResult<List<ZyzResourceAppointment>> getAppointmentByResourceId(long appointmentId) {
        return new JsonResult<>(this.baseService.getAppointmentByResourceId(appointmentId));
    }

    @ApiOperation("我的对接分页查询")
    @PostMapping({"/pagesForAppointment"})
    public JsonResult<IPage<ZyzResourceDto>> pagesForAppointment(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        IPage<ZyzResourceDto> result = this.baseService.pagesForAppointment(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @ApiOperation("预约资源导出")
    @PostMapping({"/exportForAppointment"})
    public void exportForAppointment(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        List<AppointmentExportDto> res = baseService.listForAppointment(params);
        ExcelUtil.writeExcel(res, "我的预约资源列表", AppointmentExportDto.class);
    }

    @ApiOperation("获取可以对接的资源列表")
    @GetMapping("/getEnableDocking")
    public JsonResult<List<ZyzResource>> getEnableDocking(@RequestParam(required = false) Long teamId, @RequestParam(required = false) String searchName, @RequestParam(required = false) Long pointResourceId) {
        return new JsonResult<>(baseService.getEnableDocking(teamId, searchName, pointResourceId));
    }

    @ApiOperation("获取资源(修改并同步时使用)")
    @GetMapping("/getResourceForUpdateSync")
    public Result getResourceForUpdateSync(@RequestParam Long id) {
        return new JsonResult<>(this.baseService.getResourceForUpdateSync(id));
    }

    @ApiOperation("管理员修改(重新同步使用)")
    @PostMapping("/updateForReSync")
    public Result updateForReSync(@RequestBody ZyzResourceForUpdateSyncDto data) {
        this.baseService.updateForReSync(data);
        return OK;
    }

    @ApiOperation("管理员修改后重新同步")
    @GetMapping("/reSyncForAdmin")
    public Result reSyncForAdmin(@RequestParam Long id) {
        this.baseService.reSyncForAdmin(id);
        return OK;
    }
}
