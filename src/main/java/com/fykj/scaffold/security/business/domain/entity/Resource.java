package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseTreeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 菜单，接口，按钮
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_resource")
public class Resource extends BaseTreeEntity {
    private static final long serialVersionUID = 4679862647516119691L;

    /**
     * 资源名称
     */
    @NotBlank(message = "资源名称不能为空")
    @ApiModelProperty(value = "资源名称", required = true)
    private String name;

    /**
     * 菜单的顺序
     */
    @ApiModelProperty(value = "资源排序")
    private Integer sequence;

    /**
     * 前端路由
     */
    @ApiModelProperty(value = "前端路由")
    private String url;

    /**
     * 类型，管理端或app
     */
    @NotBlank(message = "类型，管理端或app")
    @ApiModelProperty(value = "类型，管理端或app", required = true)
    private String type;

    /**
     * 后台权限管理路径
     */
    @NotBlank(message = "后台接口地址不能为空")
    @ApiModelProperty(value = "后台接口地址", required = true)
    private String path;

    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "菜单图标")
    private String icon;

    /**
     * 图标图标
     */
    @ApiModelProperty(value = "图标图标")
    private String iconPicture;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean status;

    /**
     * 是否系统保留
     */
    @TableField(value = "is_persist")
    @ApiModelProperty(value = "是否系统保留")
    private Boolean persist;

    /**
     * 打开方式
     */
    @ApiModelProperty(value = "打开方式")
    private String openTarget;

    @TableField(exist = false)
    List<SysAction> actionList = new ArrayList<>();
}
