<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzBadgeInstanceMapper">
    <!-- 查询当前用户勋章列表并关联勋章基础信息 -->
    <select id="getMyBadgesWithBadgeInfo" resultType="com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceVO">
        SELECT
            bi.*,
            bi.id AS badgeInstanceId,
            b.name AS badgeName,
            b.badge_file,
            b.preview_image,
            b.brief_intro
        FROM zyz_badge_instance bi
        LEFT JOIN zyz_badge b ON bi.badge_id = b.id
        WHERE bi.owner_id = #{ownerId}
        AND bi.recycle = false
        AND bi.is_deleted = 0
        ORDER BY bi.create_date DESC
    </select>
    
    <!-- 分页查询当前用户勋章列表并关联勋章基础信息 -->
    <select id="pageMyBadgesWithBadgeInfo" resultType="com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceVO">
        SELECT
            bi.*,
            bi.id AS badgeInstanceId,
            b.name AS badge_name,
            b.badge_file,
            b.preview_image,
            b.brief_intro
        FROM zyz_badge_instance bi
        LEFT JOIN zyz_badge b ON bi.badge_id = b.id
        WHERE bi.is_deleted = 0
        <if test="params.ownerId != null">
            AND bi.owner_id = #{params.ownerId}
        </if>
        <if test="params.recycle != null">
            AND bi.recycle = #{params.recycle}
        </if>
        <if test="params.keyword != null and params.keyword != ''">
            AND (
                b.name LIKE CONCAT('%', #{params.keyword}, '%')
                OR b.brief_intro LIKE CONCAT('%', #{params.keyword}, '%')
            )
        </if>
        ORDER BY bi.create_date DESC
    </select>
</mapper>