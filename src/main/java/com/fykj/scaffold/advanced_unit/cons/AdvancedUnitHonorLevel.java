package com.fykj.scaffold.advanced_unit.cons;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 2025/3/12
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
@AllArgsConstructor
public enum AdvancedUnitHonorLevel {
    // 国家级 省级 市级 市（区）级 乡镇（街道）级 无
    NATIONAL("AU_HONOR_LEVER_1"),
    PROVINCIAL("AU_HONOR_LEVER_2"),
    CITY_LEVEL("AU_HONOR_LEVER_3"),
    DISTRICT_LEVEL("AU_HONOR_LEVER_4"),
    TOWNSHIP_LEVEL("AU_HONOR_LEVER_5"),
    NONE("AU_HONOR_LEVER_0")
    ;

    private final String code;

    public final static String DICT_CODE = "AU_HONOR_LEVEL";
}
