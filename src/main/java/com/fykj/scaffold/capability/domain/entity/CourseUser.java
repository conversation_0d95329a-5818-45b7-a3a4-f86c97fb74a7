package com.fykj.scaffold.capability.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程用户管理
 * 表对应：course_user
 * 2025/2/25
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "cu_course_user", autoResultMap = true)
public class CourseUser extends BaseEntity {

    @TableField("course_id")
    @ApiModelProperty(value = "课程ID")
    private Long courseId;

    @TableField("user_id")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @TableField("should_credits")
    @ApiModelProperty(value = "应得学分")
    private Integer shouldCredits;

    @TableField("actual_credits")
    @ApiModelProperty(value = "实际学分")
    private Integer actualCredits;

    @TableField("complete_status")
    @ApiModelProperty(value = "完成情况")
    private Boolean completeStatus;

    @TableField("credits_status")
    @ApiModelProperty(value = "学分发放情况")
    private Boolean creditsStatus;

    @TableField("sign_up_at")
    @ApiModelProperty(value = "报名时间")
    private LocalDateTime signUpAt;

    @TableField("sign_in_at")
    @ApiModelProperty(value = "签到时间")
    private LocalDateTime signInAt;

    @TableField("sign_in_address")
    @ApiModelProperty("签到时间")
    private String signInAddress;

    @TableField("sign_in_longitude")
    @ApiModelProperty(value = "签到经度")
    private BigDecimal signInLongitude;

    @TableField("sign_in_latitude")
    @ApiModelProperty(value = "签到纬度")
    private BigDecimal signInLatitude;

    @TableField("completed_at")
    @ApiModelProperty(value = "完成时间")
    private LocalDateTime completedAt;

    @TableField("team_or_ent_name")
    @ApiModelProperty("团队/企业名称")
    private String teamOrEntName;

    @TableField("position")
    @ApiModelProperty("职务")
    private String position;
}
