package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsNumChange;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsNumChangeParams;
import com.fykj.scaffold.mall.service.IMallScoreGoodsNumChangeService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-22
 */
@RestController
@RequestMapping("/admin/mall/score-goods-num-change")
public class MallScoreGoodsNumChangeController extends BaseController<IMallScoreGoodsNumChangeService, MallScoreGoodsNumChange, MallScoreGoodsNumChangeParams> {

    @AuditLog("商品库存分页查询")
    @ApiOperation("商品库存分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallScoreGoodsNumChange>> getPages(@RequestBody MallScoreGoodsNumChangeParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }
}
