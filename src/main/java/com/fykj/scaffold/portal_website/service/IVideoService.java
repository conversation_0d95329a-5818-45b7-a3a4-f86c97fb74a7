package com.fykj.scaffold.portal_website.service;

import com.fykj.scaffold.portal_website.domain.entity.Video;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 视频资源服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface IVideoService extends IBaseService<Video> {

    /**
     * 上传文件
     *
     * @param file       文件
     * @param serverCode 文件存储服务器编号
     * @param id         资源id
     * @return 文件上传信息  {@link Video}
     */
    void upload(MultipartFile file, String serverCode, String id);

    /**
     * 更新状态
     *
     * @param id
     * @param status
     * @param failReason
     */
    void updateStatus(String id, Boolean status, String failReason);

    /**
     * 获取视频信息
     * @param id
     * @return
     */
    Video getVideoInfo(String id);

    /**
     * 更新文件名称
     *
     * @param id
     * @param name
     */
    void updateName(String id, String name);
}
