package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 团队同步补偿
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TeamSyncTask {

    @Autowired
    private IZyzTeamService teamService;

    @XxlJob("Team_Sync_Handler")
    public void teamSyncJobHandler() {
        XxlJobHelper.log("Team_Sync_Handler-Job, START!");

        // 原有同步逻辑
        List<Long> teamIds = teamService.getNeedSync();
        if (CollectionUtil.isNotEmpty(teamIds)) {
            try {
                teamService.syncBatch(teamIds);
            } catch (Exception e) {
                log.error("团队批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }

        XxlJobHelper.log("Team_Sync_Handler-Job, END!");
    }
}
