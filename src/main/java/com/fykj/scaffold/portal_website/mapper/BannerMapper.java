package com.fykj.scaffold.portal_website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.portal_website.domain.entity.Banner;
import com.fykj.scaffold.portal_website.domain.params.BannerParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  轮播图Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface BannerMapper extends BaseMapper<Banner> {

    /**
     * 分页查询新闻资讯
     * @param page
     * @param params
     * @return
     */
    IPage<Banner> pageForBanner(Page<Banner> page, @Param("params") BannerParams params);
    /**
     * 轮播图列表
     * @param categoryId
     * @return
     */
    List<Banner> pageForApi(@Param("categoryId") String categoryId);
}
