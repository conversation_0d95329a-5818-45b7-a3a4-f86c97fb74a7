package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.PointContributeSumDto;
import com.fykj.scaffold.mall.domain.entity.ZyzPointContributeRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointContributeRecordParams;
import com.fykj.scaffold.mall.service.IZyzPointContributeRecordService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 积分商城-积分捐赠记录
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@RestController
@RequestMapping("/admin/point/contribute-record")
@Api(tags = "积分商城-积分捐赠接口")
public class ZyzPointContributeRecordController extends BaseController<IZyzPointContributeRecordService, ZyzPointContributeRecord, ZyzPointContributeRecordParams> {
    @AuditLog("积分捐献记录分页查询")
    @ApiOperation("积分捐献记录分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzPointContributeRecord>> getPages(@RequestBody ZyzPointContributeRecordParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @ApiOperation("积分捐献记录统计")
    @PostMapping("/getSum")
    public JsonResult<PointContributeSumDto> getSum(@RequestBody ZyzPointContributeRecordParams params) {
        return new JsonResult<>(baseService.getSum(params));
    }
}
