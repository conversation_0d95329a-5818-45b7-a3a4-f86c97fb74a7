<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.ZyzPointContributeRecordMapper">
    <select id="getPageOrList" resultType="com.fykj.scaffold.mall.domain.entity.ZyzPointContributeRecord">
        select v.* from zyz_point_contribute_record v left join
        sys_org o on v.org_code=o.code
        where v.is_deleted=FALSE
        <if test="params.name != null and params.name != ''">
            and v.volunteer_name LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.certificateId != null and params.certificateId != ''">
            and v.certificate_id LIKE CONCAT('%',#{params.certificateId},'%')
        </if>
        <if test="params.maxPoint != null and params.maxPoint != ''">
            and v.contribute_point &lt;= #{params.maxPoint}
        </if>
        <if test="params.minPoint != null and params.minPoint != ''">
            and v.contribute_point &gt;= #{params.minPoint}
        </if>
        <if test="params.start != null and params.end != null">
            AND ( date(v.contribute_date) &gt;= #{params.start} AND date(v.contribute_date) &lt;= #{params.end} )
        </if>
        order by v.contribute_date desc
    </select>

    <select id="pageForPointContributePerson" resultType="com.fykj.scaffold.mall.domain.dto.PointContributePersonDto">
        SELECT
            RANK() OVER(ORDER BY contributePoint DESC) AS contributePointRank,
            personName,
            contributePoint
        FROM (
            SELECT
                volunteer_name AS personName,
                SUM( contribute_point ) AS contributePoint
            FROM zyz_point_contribute_record
            WHERE is_deleted = 0
            GROUP BY certificate_id, volunteer_name
            ORDER BY contributePoint DESC, volunteer_name
        ) AS nt
    </select>

    <select id="getTotalContributePoint" resultType="java.math.BigDecimal">
        SELECT
            SUM( contribute_point )
        FROM zyz_point_contribute_record
        WHERE is_deleted = 0
    </select>

    <select id="getTotalContributePerson" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM (
             SELECT
                 volunteer_name
             FROM zyz_point_contribute_record
             WHERE is_deleted = 0
             GROUP BY certificate_id, volunteer_name
         ) nt
    </select>
</mapper>
