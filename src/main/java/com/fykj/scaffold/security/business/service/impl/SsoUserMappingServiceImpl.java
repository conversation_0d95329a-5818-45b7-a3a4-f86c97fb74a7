package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.domain.dto.SsoUserImportDto;
import com.fykj.scaffold.security.business.domain.dto.ZsqUserSyncDto;
import com.fykj.scaffold.security.business.domain.entity.SsoUserMapping;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.domain.entity.SysUserExpand;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.mapper.SsoUserMappingMapper;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.security.business.util.ZSQSyncUserUtils;
import com.fykj.scaffold.support.conns.Cons;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static constants.Mark.COMMA;
import static constants.Mark.SEMICOLON;

/**
 * SSO用户与平台用户关联关系-服务实现类
 * Date: 2025-04-29
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SsoUserMappingServiceImpl extends BaseServiceImpl<SsoUserMappingMapper, SsoUserMapping>
        implements ISsoUserMappingService {

    @Autowired
    private IUserService userService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private ZSQSyncUserUtils zsqSyncUserUtils;

    @Override
    public Long getPlatformUserIdByProviderAndUserId(String ssoProvider, String userId) {
        return lambdaQuery().eq(SsoUserMapping::getSsoProvider, ssoProvider)
                .eq(SsoUserMapping::getUserId, userId)
                .select(SsoUserMapping::getId, SsoUserMapping::getPlatformUserId)
                .oneOpt()
                .map(SsoUserMapping::getPlatformUserId)
                .orElse(null);
    }

    @Async
    @Override
    public void updateSsoUserMappingAsync(String ssoProvider, String userId, String rawUserInfo) {
        lambdaUpdate().eq(SsoUserMapping::getSsoProvider, ssoProvider)
                .eq(SsoUserMapping::getUserId, userId)
                .set(SsoUserMapping::getLastLoginTime, LocalDateTime.now())
                .set(SsoUserMapping::getRawUserInfo, rawUserInfo)
                .update();
    }

    @Override
    public void deleteSsoUserMapping(String ssoProvider, String userId) {
        lambdaUpdate().eq(SsoUserMapping::getSsoProvider, ssoProvider)
                .eq(SsoUserMapping::getUserId, userId)
                .remove();
    }

    @Override
    public String getSSOUserIdByProviderAndZYZUserId(String ssoProvider, Long userId) {
        return lambdaQuery().eq(SsoUserMapping::getSsoProvider, ssoProvider)
                .eq(SsoUserMapping::getPlatformUserId, userId)
                .select(SsoUserMapping::getId, SsoUserMapping::getUserId)
                .oneOpt()
                .map(SsoUserMapping::getUserId)
                .orElse(null);
    }

    @Override
    public List<SsoUserImportDto> importSsoUser(MultipartFile excel, String ssoProvider) {
        // 验证excel
        log.info("用户导入-验证excel文件...");
        validateExcel(excel);
        log.info("用户导入-读取数据...");
        List<SsoUserImportDto> dataList = readDataListFromExcel(excel);
        log.info("用户导入-验证数据...");
        // 验证数据合理性
        List<SsoUserImportDto> failList = validateAndFormatDataList(dataList);
        if (!failList.isEmpty()) {
            return failList;
        }
        // 导入用户
        log.info("用户导入-导入用户开始...");
        importUser(dataList, ssoProvider);
        log.info("用户导入-导入用户完成.");
        return null;
    }

    /**
     * 处理完的用户列表
     *
     * @param dataList 包含要导入的用户详细信息的SsoUserImportDto对象列表
     * @param ssoProvider 用于链接导入用户的SSO提供程序的标识符
     */
    private void importUser(List<SsoUserImportDto> dataList, String ssoProvider) {
        for (int i = 0; i < dataList.size(); i++) {
            SsoUserImportDto dto = dataList.get(i);
            log.info("用户导入-导入用户序号{}用户名{}...", i + 1, dto.getName());
            // 构造用户
            User user = buildUser(dto);
            // 保存用户
            if(Objects.isNull(user.getId())) {
                userService.save(user);
                // 导入sso关联关系
                SsoUserMapping ssoUserMapping = new SsoUserMapping();
                ssoUserMapping.setSsoProvider(ssoProvider);
                ssoUserMapping.setUserId(dto.getUserId());
                ssoUserMapping.setPlatformUserId(user.getId());
                ssoUserMapping.setLastLoginTime(LocalDateTime.now());
                save(ssoUserMapping);
            } else {
                userService.updateById(user);
            }
        }
    }

    /**
     * 构造用户user
     * @param dto dto
     * @return user
     */
    private User buildUser(SsoUserImportDto dto) {
        User user = userService.getWithRoleAndExpandByMobile(dto.getMobile());
        if(Objects.isNull(user)) {
            user = new User();
            user.setMobile(dto.getMobile());
            user.setUsername(dto.getMobile());
            user.setName(dto.getName());
            user.setPassword(SsoCons.DEFAULT_PASSWORD);
            user.setStatus(true);
            // 赋予角色和身份
            List<String> roleCodes = new ArrayList<>();
            List<SysUserExpand> expandList = new ArrayList<>();
            if(StringUtils.isNotBlank(dto.getCommunityOrgCode())) {
                roleCodes.add(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
                SysUserExpand expand = new SysUserExpand();
                expand.setOrgCode(dto.getCommunityOrgCode());
                expand.setRoleCode(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
                expandList.add(expand);
            }else {
                roleCodes.add(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
                SysUserExpand expand = new SysUserExpand();
                expand.setOrgCode(dto.getStreetOrgCode());
                expand.setRoleCode(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
                expandList.add(expand);
            }
            user.setRoleCodes(roleCodes);
            user.setExpandList(expandList);
        }else {
            log.info("用户导入-用户{}存在, 开始赋予角色身份...", dto.getName());
            List<SysUserExpand> expandList = user.getExpandList();
            List<String> roleCodes = user.getRoleCodes();
            if(StringUtils.isNotBlank(dto.getCommunityOrgCode())) {
                if(!roleCodes.contains((Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN))) {
                    roleCodes.add(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
                    log.info("用户导入-用户存在, 赋予{}社区管理员角色...", dto.getName());
                }
                if(expandList.stream().map(SysUserExpand::getOrgCode)
                        .filter(Objects::nonNull)
                        .noneMatch(it -> it.equals(dto.getCommunityOrgCode()))){
                    SysUserExpand expand = new SysUserExpand();
                    expand.setOrgCode(dto.getCommunityOrgCode());
                    expand.setRoleCode(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
                    expandList.add(expand);
                    log.info("用户导入-用户存在, 赋予{}社区管理员身份{}...", dto.getName(),dto.getCommunityOrgCode());
                }
            }else {
                if(!roleCodes.contains((Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN))) {
                    roleCodes.add(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
                    log.info("用户导入-用户存在, 赋予{}分协会管理员角色...", dto.getName());
                }
                if(expandList.stream().map(SysUserExpand::getOrgCode)
                        .filter(Objects::nonNull)
                        .noneMatch(it -> it.equals(dto.getStreetOrgCode()))){
                    SysUserExpand expand = new SysUserExpand();
                    expand.setOrgCode(dto.getStreetOrgCode());
                    expand.setRoleCode(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
                    expandList.add(expand);
                    log.info("用户导入-用户存在, 赋予{}分协会管理员身份{}...", dto.getName(),dto.getStreetOrgCode());
                }
            }
        }
        return user;
    }

    private List<SsoUserImportDto> validateAndFormatDataList(List<SsoUserImportDto> dataList) {
        List<SsoUserImportDto> failList = new ArrayList<>();
        List<SysOrg> subOrg = orgService.getSubOrg(Cons.TOP_DEPT_CODE, true);
        Map<Integer, Map<String, String>> orgNamePrefix2codeGroupByLevel = subOrg.stream()
                .collect(Collectors.groupingBy(
                    SysOrg::getLevel,
                    Collectors.collectingAndThen(
                            Collectors.toList(),
                            list -> list.stream().collect(Collectors.toMap(
                                    x -> trimOrgNameSuffix(x.getName()),
                                    SysOrg::getCode
                            )))));
        Map<String, String> streetPrefix2code = orgNamePrefix2codeGroupByLevel.get(2);
        Map<String, String> communityPrefix2code = orgNamePrefix2codeGroupByLevel.get(3);
        for (SsoUserImportDto dto : dataList) {
            StringJoiner failReason = new StringJoiner(COMMA);
            String orgCode;
            // 必填项校验
            if(StringUtils.isBlank(dto.getName())) {
                failReason.add("姓名不能为空");
            }
            if(StringUtils.isBlank(dto.getUserId())) {
                failReason.add("政务通id不能为空");
            }
            if(StringUtils.isBlank(dto.getMobile())) {
                failReason.add("手机号不能为空");
            }
            // 检查街道匹配情况
            if(StringUtils.isBlank(dto.getStreet())) {
                failReason.add("街道不能为空");
            } else if (StringUtils.isBlank((orgCode = streetPrefix2code.get(dto.getStreet())))) {
                failReason.add("此街道不在本系统中");
            }else {
                dto.setStreetOrgCode(orgCode);
            }

            // 检查社区匹配情况
            if (StringUtils.isNotBlank(dto.getCommunity())) {
                if (StringUtils.isBlank((orgCode = communityPrefix2code.get(dto.getCommunity())))) {
                    failReason.add("此社区不在本系统中");
                } else {
                    dto.setCommunityOrgCode(orgCode);
                }
            }

            if(failReason.length() > 0) {
                dto.setReason(failReason.toString());
                failList.add(dto);
            }
        }
        return failList;
    }

    /**
     * eg:  娄葑街道新时代文明实践所 ->  娄葑街道
     * @param input  xxxx新时代文明实践(站|所)
     * @return xxxx
     */
    public static String trimOrgNameSuffix(String input) {
        if (input == null) {
            return null;
        }
        // (?:...) 表示非捕获分组，$ 表示字符串末尾
        return input.replaceAll("(?:新时代文明实践站|新时代文明实践所)$", "");
    }

    /**
     * 验证Excel文件
     *
     * @param excel 需要验证的Excel文件
     * @throws BusinessException 如果Excel文件为空或未上传，则抛出BusinessException异常
     */
    private void validateExcel(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "上传文件为空");
        }
    }

    /**
     * 从Excel文件中读取单位列表
     *
     * @param excel 包含单位数据的Excel文件
     * @return 包含单位数据的DTO列表
     * @throws BusinessException 如果导入的数据为空，抛出业务异常
     */
    private List<SsoUserImportDto> readDataListFromExcel(MultipartFile excel) {
        List<SsoUserImportDto> dataList = ExcelUtil.readExcel(
                excel, SsoUserImportDto.class, 0, 1);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        return dataList;
    }

    @Override
    public List<ZsqUserSyncDto> syncZsqUser(String startDate, String endDate) {
        String zsqToken = zsqSyncUserUtils.getToken();
        List<ZsqUserSyncDto> userList = zsqSyncUserUtils.getUserList(startDate, endDate, zsqToken);
        List<SysOrg> subOrg = orgService.getSubOrg(Cons.TOP_DEPT_CODE, true);
        Map<String, String> streetNameCodeMap = subOrg.stream().filter(it -> it.getLevel() == 2)
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().collect(Collectors.toMap(x -> trimOrgNameSuffix(x.getName()), SysOrg::getCode))));
        Map<String, String> communityNameCodeMap = subOrg.stream().filter(it -> it.getLevel() == 3)
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().collect(Collectors.toMap(x -> trimOrgNameSuffix(x.getName()), SysOrg::getCode))));
        userList.parallelStream().forEach(it -> {
            List<String> failReasons = new ArrayList<>();
            // 必填项校验
            String name = it.getName();
            if(StringUtils.isEmpty(name)) {
                failReasons.add("姓名不能为空");
            } else {
                try {
                    name = ZSQSyncUserUtils.decrypt(name);
                    it.setName(name);
                } catch (Exception e) {
                    failReasons.add("姓名解密失败，失败原因：{" + e.getMessage() + "}");
                }
            }
            String mobile = it.getMobile();
            if(StringUtils.isEmpty(name)) {
                failReasons.add("手机号不能为空");
            } else {
                try {
                    mobile = ZSQSyncUserUtils.decrypt(mobile);
                    it.setMobile(mobile);
                } catch (Exception e) {
                    failReasons.add("手机号解密失败，失败原因：{" + e.getMessage() + "}");
                }
            }
            if(StringUtils.isEmpty(it.getUserId())) {
                failReasons.add("用户id（政务通id）不能为空");
            }
            // 检查街道匹配情况
            String level = it.getLevel();
            if (StringUtils.isEmpty(level)) {
                failReasons.add("层级不能为空");
            } else {
                String gridName = it.getGridName();
                if (StringUtils.isEmpty(level)) {
                    failReasons.add("组织名称不能为空");
                } else  {
                    if ("街道".equals(level)) {
                        String street = streetNameCodeMap.get(gridName);
                        if (StringUtils.isEmpty(street)) {
                            failReasons.add("此街道不在本系统中");
                        } else {
                            it.setStreet(Boolean.TRUE);
                            it.setGridName(street);
                        }
                    } else if ("社区".equals(level)) {
                        String community = communityNameCodeMap.get(gridName);
                        if (StringUtils.isEmpty(community)) {
                            failReasons.add("此社区不在本系统中");
                        } else {
                            it.setStreet(Boolean.FALSE);
                            it.setGridName(community);
                        }
                    } else {
                        failReasons.add("层级只能为街道或社区");
                    }
                }
            }
            Boolean quit = it.getQuit();
            if (quit == null) {
                failReasons.add("是否离职不能为空");
            }
            if (!failReasons.isEmpty()) {
                it.setStatus("数据验证未通过");
                it.setRemark(String.join(SEMICOLON, failReasons));
                it.setValidatePass(Boolean.FALSE);
            } else {
                it.setValidatePass(Boolean.TRUE);
            }
        });
        if (userList.stream().noneMatch(ZsqUserSyncDto::getValidatePass)) {
            return userList;
        }
        userList.stream().filter(ZsqUserSyncDto::getValidatePass).forEach(it -> {
            try {
                importUser(it);
            } catch (Exception e) {
                it.setStatus("同步过程保存异常");
                it.setRemark(e.getMessage());
            }
        });
        return userList;
    }

    private void importUser(ZsqUserSyncDto data) {
        // 构造用户
        User user = buildUser(data);
        if (user == null) {
            if ("仅处理用户政务通账号信息".equals(data.getStatus()) || "仅处理没有设置知社区特定角色的用户".equals(data.getStatus())) {
                return;
            }
            data.setStatus("无需处理");
            data.setRemark("知社区平台已离职且志愿者平台本无该用户或该用户无指定管理权限");
            return;
        }
        // 保存用户
        if(Objects.isNull(user.getId())) {
            userService.save(user);
            // 导入sso关联关系
            SsoUserMapping ssoUserMapping = new SsoUserMapping();
            ssoUserMapping.setSsoProvider(SsoCons.SsoProvider.ZWT.name());
            ssoUserMapping.setUserId(data.getUserId());
            ssoUserMapping.setPlatformUserId(user.getId());
            ssoUserMapping.setLastLoginTime(LocalDateTime.now());
            save(ssoUserMapping);
            data.setStatus("处理成功");
            data.setRemark("用户创建成功");
        } else {
            userService.updateById(user);
            List<SsoUserMapping> exists = lambdaQuery()
                    .eq(SsoUserMapping::getPlatformUserId, user.getId())
                    .eq(SsoUserMapping::getSsoProvider, SsoCons.SsoProvider.ZWT.name())
                    .list();
            if (exists == null || exists.isEmpty()) {
                SsoUserMapping ssoUserMapping = new SsoUserMapping();
                ssoUserMapping.setSsoProvider(SsoCons.SsoProvider.ZWT.name());
                ssoUserMapping.setUserId(data.getUserId());
                ssoUserMapping.setPlatformUserId(user.getId());
                ssoUserMapping.setLastLoginTime(LocalDateTime.now());
                save(ssoUserMapping);
            } else if (exists.size() > 1){
                super.removeBatchByIds(exists);
                SsoUserMapping ssoUserMapping = new SsoUserMapping();
                ssoUserMapping.setSsoProvider(SsoCons.SsoProvider.ZWT.name());
                ssoUserMapping.setUserId(data.getUserId());
                ssoUserMapping.setPlatformUserId(user.getId());
                ssoUserMapping.setLastLoginTime(LocalDateTime.now());
                save(ssoUserMapping);
            } else {
                lambdaUpdate().eq(SsoUserMapping::getId, exists.get(0).getId()).set(SsoUserMapping::getUserId, data.getUserId()).update();
            }
            data.setStatus("处理成功");
            data.setRemark("用户角色身份更新成功");
        }
    }

    /**
     * 构造用户user
     * @param data
     * @return user
     */
    private User buildUser(ZsqUserSyncDto data) {
        User user = userService.getWithRoleAndExpandByMobile(data.getMobile());
        Boolean quit = data.getQuit();
        if(Objects.isNull(user)) {
            if (quit) {
                return null;
            }
            user = new User();
            user.setMobile(data.getMobile());
            user.setUsername(data.getMobile());
            user.setName(data.getName());
            user.setPassword(SsoCons.DEFAULT_PASSWORD);
            user.setStatus(Boolean.TRUE);
            // 赋予角色和身份
            List<String> roleCodes = new ArrayList<>();
            List<SysUserExpand> expandList = new ArrayList<>();
            roleCodes.add("SIP_ZSQ_USER");
//            if(!data.getStreet()) {
//                roleCodes.add(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
//                SysUserExpand expand = new SysUserExpand();
//                expand.setOrgCode(data.getGridName());
//                expand.setRoleCode(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
//                expandList.add(expand);
//            }else {
//                roleCodes.add(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
//                SysUserExpand expand = new SysUserExpand();
//                expand.setOrgCode(data.getGridName());
//                expand.setRoleCode(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
//                expandList.add(expand);
//            }
            user.setRoleCodes(roleCodes);
            user.setExpandList(expandList);
            return user;
        }
        if (quit) {
            List<String> roleCodes = user.getRoleCodes();
            List<SysUserExpand> expandList = user.getExpandList();
            String orgCode = data.getGridName();
            List<SysUserExpand> exists = expandList.stream().filter(it -> orgCode.equals(it.getOrgCode())).collect(Collectors.toList());
            if (exists.isEmpty()) {
                return null;
            }
            expandList = expandList.stream().filter(it -> !orgCode.equals(it.getOrgCode())).collect(Collectors.toList());
            if(!data.getStreet()) {
                List<SysUserExpand> existsCommunityAdmin = expandList.stream().filter(it -> Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN.equals(it.getRoleCode())).collect(Collectors.toList());
                if (!existsCommunityAdmin.isEmpty()) {
                    return null;
                }
                roleCodes.remove(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
            }else {
                List<SysUserExpand> existsSubAssociationAdmin = expandList.stream().filter(it -> Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(it.getRoleCode())).collect(Collectors.toList());
                if (!existsSubAssociationAdmin.isEmpty()) {
                    return null;
                }
                roleCodes.remove(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
            }
            user.setRoleCodes(roleCodes);
            user.setExpandList(expandList);
            return user;
        }
        List<SysUserExpand> expandList = user.getExpandList();
        List<String> roleCodes = user.getRoleCodes();
        if (!roleCodes.contains("SIP_ZSQ_USER")) {
            roleCodes.add("SIP_ZSQ_USER");
            user.setRoleCodes(roleCodes);
            user.setExpandList(expandList);
            return user;
        } else {
            data.setStatus("仅处理没有设置知社区特定角色的用户");
            data.setRemark("知社区平台在职且志愿者平台该用户设置知社区用户特定角色");
            return null;
        }
//        String orgCode = data.getGridName();
//        List<SysUserExpand> exists = expandList.stream().filter(it -> orgCode.equals(it.getOrgCode())).collect(Collectors.toList());
//        if (!exists.isEmpty()) {
//            List<SsoUserMapping> mappingExists = lambdaQuery()
//                    .eq(SsoUserMapping::getPlatformUserId, user.getId())
//                    .eq(SsoUserMapping::getSsoProvider, SsoCons.SsoProvider.ZWT.name())
//                    .list();
//            if (mappingExists == null || mappingExists.isEmpty()) {
//                SsoUserMapping ssoUserMapping = new SsoUserMapping();
//                ssoUserMapping.setSsoProvider(SsoCons.SsoProvider.ZWT.name());
//                ssoUserMapping.setUserId(data.getUserId());
//                ssoUserMapping.setPlatformUserId(user.getId());
//                ssoUserMapping.setLastLoginTime(LocalDateTime.now());
//                save(ssoUserMapping);
//            } else if (mappingExists.size() > 1){
//                super.removeBatchByIds(mappingExists);
//                SsoUserMapping ssoUserMapping = new SsoUserMapping();
//                ssoUserMapping.setSsoProvider(SsoCons.SsoProvider.ZWT.name());
//                ssoUserMapping.setUserId(data.getUserId());
//                ssoUserMapping.setPlatformUserId(user.getId());
//                ssoUserMapping.setLastLoginTime(LocalDateTime.now());
//                save(ssoUserMapping);
//            } else {
//                lambdaUpdate().eq(SsoUserMapping::getId, mappingExists.get(0).getId()).set(SsoUserMapping::getUserId, data.getUserId()).update();
//            }
//            data.setStatus("仅处理用户政务通账号信息");
//            data.setRemark("知社区平台在职且志愿者平台该用户已有指定管理权限");
//            return null;
//        }
//        if(!data.getStreet()) {
//            SysUserExpand expand = new SysUserExpand();
//            expand.setOrgCode(orgCode);
//            expand.setRoleCode(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
//            expandList.add(expand);
//            if (!roleCodes.contains(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN)) {
//                roleCodes.add(Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN);
//            }
//        }else {
//            SysUserExpand expand = new SysUserExpand();
//            expand.setOrgCode(orgCode);
//            expand.setRoleCode(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
//            expandList.add(expand);
//            if (!roleCodes.contains(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN)) {
//                roleCodes.add(Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN);
//            }
//        }
//        user.setRoleCodes(roleCodes);
//        user.setExpandList(expandList);
//        return user;
    }
}
