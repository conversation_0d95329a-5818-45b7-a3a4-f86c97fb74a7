package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.sync.service.IZyzSyncRegionDictService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 同步地域编码
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RegionSyncTask {

    @Autowired
    private IZyzSyncRegionDictService regionDictService;

    @XxlJob("Region_Sync_Handler")
    public void regionSyncJobHandler() {
        XxlJobHelper.log("Region_Sync_Handler-Job, START!");
        try {
            regionDictService.syncRegionDict();
        } catch (Exception e) {
            log.error("地域同步失败，失败原因：{}", e.getMessage(), e);
        }
        XxlJobHelper.log("Region_Sync_Handler-Job, END!");
    }
}
