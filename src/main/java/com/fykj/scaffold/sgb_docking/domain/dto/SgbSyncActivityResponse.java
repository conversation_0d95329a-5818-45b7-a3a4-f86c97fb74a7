package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 苏州社工部活动登记同步响应
 */
@Data
public class SgbSyncActivityResponse {
    
    /**
     * 响应信息列表
     */
    private List<ActivityResult> outActList;
    
    /**
     * 活动登记推送结果
     */
    @Data
    public static class ActivityResult {

        /**
         * 响应状态码 - SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 唯一标识
         */
        private String uniCode;

        /**
         * 活动编号 - SUCCESS时返回
         */
        private String apId;
    }
}
