package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsApplyParams;
import com.fykj.scaffold.mall.service.IMallScoreGoodsApplyService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 星光闪耀商品报名表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
@RestController
@RequestMapping("/admin/mall/score/goods/apply")
@Api(tags = "星光闪耀商品报名表接口")
public class MallScoreGoodsApplyController extends BaseController<IMallScoreGoodsApplyService, MallScoreGoodsApply, MallScoreGoodsApplyParams> {


    @AuditLog("报名记录分页查询")
    @ApiOperation("报名记录分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallScoreGoodsApplyDto>> getPages(@RequestBody MallScoreGoodsApplyParams params) {
        IPage<MallScoreGoodsApplyDto> page = baseService.getPage(params);
        return new JsonResult<>(page);
    }


    @AuditLog("报名记录导出数据到Excel")
    @PostMapping("/exportApply")
    @ApiOperation("报名记录导出数据到Excel")
    public void exportApply(@RequestBody MallScoreGoodsApplyParams params) {
        params.setPageSize(-1);
        IPage<MallScoreGoodsApplyDto> page = baseService.getPage(params);
        List<MallScoreGoodsApplyDto> result = page.getRecords();
        DictTransUtil.trans(result);
        ExcelUtil.fillExcel(result, "goods-apply-export.xlsx", MallScoreGoodsApplyDto.class);
    }

    @AuditLog("报名")
    @PostMapping("/apply")
    @ApiOperation("报名")
    public Result apply(@RequestBody MallScoreGoodsApply entity) {
        boolean result = this.baseService.saveOrUpdateApply(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @AuditLog("获取报名信息")
    @GetMapping("/getApplyInfo")
    @ApiOperation("获取报名信息")
    public Result getApplyInfo(@RequestParam Long exchangeId) {
        return new JsonResult<>(baseService.getApplyInfo(exchangeId));
    }
}
