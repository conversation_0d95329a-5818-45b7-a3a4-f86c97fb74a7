package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncLog;
import fykj.microservice.core.base.IBaseService;

public interface ISgbSyncLogService extends IBaseService<SgbSyncLog> {

    SgbSyncLog buildSyncLog(String bizType, String resultCode, String msg,
                            long syncBillId, long bizId, String objName,
                            String reqParam, String resp);
}
