<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzProjectDockingMapper">

    <select id="getListByProjectId" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzProjectDocking">
        select *,
               d.corp_name          as corpName,
               p.social_credit_code as socialCreditCode
        from zyz_project_docking d
        left join zyz_project_company p on d.corp_id = p.id
        where d.is_deleted = 0
          and d.project_id = #{projectId}
    </select>
    <select id="findByProjectIds" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectDockingExportDto">
        SELECT
        p.*,
        d.*,
        date_format(signing_date, '%Y-%m-%d') AS signingDateStr,
        date_format(payment_date, '%Y-%m-%d') AS paymentDateStr,
        d.project_demand_type AS projectDockingType
        FROM zyz_project p
            LEFT JOIN zyz_project_docking d
                ON d.project_id=p.id AND d.is_deleted = 0
        WHERE p.is_deleted = 0
        <if test="idList != null">
            AND p.id IN
            <foreach collection="idList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY p.create_date DESC
    </select>
    <select id="findDockingCompanyListByYear" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzProjectCompany">
        SELECT DISTINCT c.*
        FROM zyz_project_docking d
                 JOIN zyz_project_company c ON d.corp_id=c.id
                 JOIN zyz_project p ON p.id=d.project_id AND p.is_deleted=0 AND p.audit_status!='pro_stop'
        WHERE p.`year`=#{year}
        ORDER BY d.amount DESC
    </select>
    <select id="sumDockingAmountByDemandType" resultType="java.math.BigDecimal">
        SELECT
            sum( d.amount )
        FROM
            zyz_project_docking d
                LEFT JOIN `zyz_project` p ON d.project_id = p.id
        WHERE
            p.is_deleted = 0
          AND d.is_deleted = 0
          AND p.is_display = 1
          and d.project_demand_type = #{demandType}
          AND p.audit_status NOT IN ( 'pro_draft', 'pro_wait_audit', 'pro_reject' )
    </select>
</mapper>