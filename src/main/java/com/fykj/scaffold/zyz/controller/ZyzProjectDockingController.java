package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectDocking;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectDockingParams;
import com.fykj.scaffold.zyz.domain.params.ZyzRequirementParams;
import com.fykj.scaffold.zyz.service.IZyzProjectDockingService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;
import io.swagger.annotations.Api;
/**
 * 公益伙伴计划-项目对接企业中间表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
@RestController
@RequestMapping("/admin/project/docking")
@Api(tags = "公益伙伴计划-项目对接企业接口")
public class ZyzProjectDockingController extends BaseController<IZyzProjectDockingService, ZyzProjectDocking, ZyzProjectDockingParams> {

    @ApiOperation("获取对接公司列表")
    @GetMapping("/getListByProjectId")
    public JsonResult<List<ZyzProjectDocking>> getListByProjectId(Long projectId) {
        return new JsonResult<>(baseService.getListByProjectId(projectId));
    }




}
