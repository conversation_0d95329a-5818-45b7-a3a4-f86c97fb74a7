package com.fykj.scaffold.security.business.util;

import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import lombok.experimental.UtilityClass;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 2025/4/7
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@UtilityClass
public class SysOrgUtil {

    public static List<SysOrg> clearSensitiveInformation(List<SysOrg> childrenList) {
        if (!CollectionUtils.isEmpty(childrenList)) {
            childrenList.forEach(item -> {
                clearSensitiveInformation(item);
                clearSensitiveInformation(item.getChildren());
            });
        }
        return childrenList;
    }

    public static void clearSensitiveInformation(SysOrg item) {
        item.setLinkMan(null);
        item.setLinkIdCard(null);
        item.setLinkMobile(null);
        item.setLongitude(null);
        item.setLatitude(null);
        item.setWsg84Longitude(null);
        item.setWsg84Latitude(null);
    }
}
