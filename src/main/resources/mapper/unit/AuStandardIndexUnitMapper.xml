<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fykj.scaffold.advanced_unit.mapper.AuStandardIndexUnitMapper">

    <resultMap id="AuStandardIndexUnitMap"
               type="com.fykj.scaffold.advanced_unit.domain.entity.AuStandardIndexUnit" autoMapping="true">
        <result column="attachment" property="attachment"
                typeHandler="com.fykj.scaffold.zyz.domain.dto.AttachmentDto$AttachmentListTypeHandler" />
    </resultMap>
    <select id="queryEvaluatePage"
            resultType="com.fykj.scaffold.advanced_unit.domain.dto.AuUnitEvaluateListDTO">
        SELECT
            u.id AS orgId,
            u.unit_name AS unitName,
            u.unit_logo AS unitLogo,
            u.credit_code AS creditCode,
            IFNULL(SUM(siu_sub.self_count), 0) AS selfCount,
            IFNULL(SUM(siu_sub.self_score), 0) AS selfScore,
            IFNULL(SUM(siu_sub.final_count), 0) AS finalCount,
            IFNULL(SUM(siu_sub.final_score), 0) AS finalScore,
            MAX(siu_sub.update_date) AS updateDate
        FROM
            au_unit u
        LEFT JOIN (
            SELECT
                siu.org_id,
                siu.standard_id,
                siu.self_count,
                siu.self_score,
                siu.final_count,
                siu.final_score,
                siu.update_date
            FROM
                au_standard_index_unit siu
            INNER JOIN au_standard_index si ON siu.index_id = si.id AND si.is_deleted = 0
            <where>
                AND siu.is_deleted = 0
                <if test="params.standardId != null">
                    AND siu.standard_id = #{params.standardId}
                </if>
                <if test="params.itemId != null">
                    AND siu.item_id = #{params.itemId}
                </if>
                <if test="params.indexId != null">
                    AND siu.index_id = #{params.indexId}
                </if>
            </where>
        ) siu_sub ON u.id = siu_sub.org_id
        <where>
            u.is_deleted = 0
            AND u.audit_status = '${@<EMAIL>}'
            <if test="params.unitName != null and params.unitName != ''">
                AND u.unit_name LIKE CONCAT('%', #{params.unitName}, '%')
            </if>
            <if test="params.creditCode != null and params.creditCode != ''">
                AND u.credit_code LIKE CONCAT('%', #{params.creditCode}, '%')
            </if>
            <if test="params.orgId != null">
                AND u.id = #{params.orgId}
            </if>
            <if test="params.belongUserId != null">
                AND u.belong_user_id = #{params.belongUserId}
            </if>
        </where>
        GROUP BY
            u.id,
            u.unit_name,
            u.unit_logo,
            u.credit_code,
            u.create_date
        ORDER BY u.create_date DESC
    </select>
    <select id="queryIndexEvaluatePage"
            resultMap="AuStandardIndexUnitMap">
        SELECT
            siu.id,
            siu.version,
            siu.is_deleted,
            siu.create_date,
            siu.update_date,
            siu.creator,
            siu.updater,
            siu.org_id,
            siu.self_score,
            siu.final_score,
            siu.self_count,
            siu.final_count,
            siu.attachment,
            si.`name` AS `name`,
            si.`score` AS `score`,
            si.content AS content,
            si.order_num AS orderNum,
            si.id AS indexId,
            si.item_id AS itemId,
            si.standard_id AS standardId
        FROM au_standard_index si
        LEFT JOIN au_standard_index_unit siu ON siu.index_id = si.id
             AND siu.is_deleted = 0
            <if test="params.orgId != null">
                AND siu.org_id = #{params.orgId}
            </if>
        <where>
            AND si.is_deleted = 0
            <if test="params.standardId != null">
                AND si.standard_id = #{params.standardId}
            </if>
            <if test="params.itemId != null">
                AND si.item_id = #{params.itemId}
            </if>
            <if test="params.indexId != null">
                AND si.id = #{params.indexId}
            </if>
        </where>
        ORDER BY si.order_num DESC, si.create_date DESC
    </select>
    <select id="queryAllUnitScores" resultType="com.fykj.scaffold.advanced_unit.domain.dto.AuUnitScoreDTO">
        SELECT
            u.id AS orgId,
            IFNULL(SUM(siu.final_score), 0) AS totalScore
        FROM
            au_unit u
        LEFT JOIN au_standard_index_unit siu ON u.id = siu.org_id
            AND siu.is_deleted = 0
            <if test="params.standardId != null">
                AND siu.standard_id = #{params.standardId}
            </if>
            <if test="params.itemId != null">
                AND siu.item_id = #{params.itemId}
            </if>
            <if test="params.indexId != null">
                AND siu.index_id = #{params.indexId}
            </if>
        WHERE
            u.is_deleted = 0
            AND u.audit_status = '${@<EMAIL>}'
        GROUP BY
            u.id
    </select>
</mapper>
