package com.fykj.scaffold.mall.controller;


import com.fykj.scaffold.mall.domain.dto.StockReplenishPlanDto;
import com.fykj.scaffold.mall.domain.entity.MallStockReplenishPlan;
import com.fykj.scaffold.mall.domain.params.MallStockReplenishPlanParams;
import com.fykj.scaffold.mall.service.IMallStockReplenishPlanService;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 物料进补计划管理前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@RestController
@RequestMapping("/admin/mall/stock_replenish_plan")
@Api(tags = "物料进补计划管理接口")
public class MallStockReplenishPlanController extends BaseController<IMallStockReplenishPlanService, MallStockReplenishPlan, MallStockReplenishPlanParams> {

    @Autowired
    private ISysOrgService orgService;

    @ApiOperation("获取分协会组织列表")
    @GetMapping({"/getSubAssociationOrgList"})
    public JsonResult<List<SysOrg>> getSubAssociationOrgList() {
        return new JsonResult<>(orgService.getAreaSubAssociation(true, false));
    }

    @ApiOperation("获取计划列表")
    @PostMapping({"/getPlanList"})
    public JsonResult<List<StockReplenishPlanDto>> getPlanList(@RequestBody MallStockReplenishPlanParams params) {
        return new JsonResult<>(baseService.getPlanList(params));
    }

    @ApiOperation("获取计划信息")
    @GetMapping({"/getReplenishPlanById"})
    public JsonResult<MallStockReplenishPlan> getReplenishPlanById(@RequestParam Long id) {
        return new JsonResult<>(baseService.getReplenishPlanById(id));
    }

    @ApiOperation("保存草稿")
    @PostMapping({"/saveOrUpdateDraft"})
    public Result saveOrUpdateDraft(@RequestBody MallStockReplenishPlan entity) {
        boolean result = this.baseService.saveOrUpdateDraft(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @ApiOperation("提交")
    @PostMapping({"/submit"})
    public Result submit(@RequestBody MallStockReplenishPlan entity) {
        boolean result = this.baseService.submit(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @ApiOperation("仅传id提交")
    @GetMapping({"/submitById"})
    public Result submitById(@RequestParam Long id) {
        boolean result = this.baseService.submitById(id);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @ApiOperation("计划生成")
    @GetMapping({"/generatePlan"})
    public JsonResult<MallStockReplenishPlan> generatePlan(@RequestParam String type,
                                                           @RequestParam(required = false) BigDecimal budgetAmount,
                                                           @RequestParam(required = false) String year,
                                                           @RequestParam(required = false) Long machineId) {
        return new JsonResult<>(baseService.generatePlan(type, budgetAmount, year, machineId));
    }
}
