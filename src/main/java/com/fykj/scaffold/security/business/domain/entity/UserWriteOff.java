package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import static com.fykj.scaffold.support.conns.Cons.DATE_FORMAT;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 系统用户注销管理-实体类
 *
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_user_write_off")
public class UserWriteOff extends BaseEntity  {

	private static final long serialVersionUID = 4669124367722302155L;

	/**
	 * 志愿者ID
	 */
	@TableField("volunteer_id")
    @ApiModelProperty(value = "志愿者ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long volunteerId;

	/**
	 * 申请注销日期
	 */
	@TableField("apply_date")
    @ApiModelProperty(value = "申请注销日期")
	@JsonFormat(pattern = DATE_FORMAT)
	@DateTimeFormat(pattern = DATE_FORMAT)
	private LocalDate applyDate;

	/**
	 * 是否取消申请注销
	 */
	@TableField("cancel_apply")
    @ApiModelProperty(value = "是否取消申请注销")
	private Boolean cancelApply;

	/**
	 * 计划账号相关信息删除日期
	 */
	@TableField("plan_or_real_account_link_info_remove_date")
    @ApiModelProperty(value = "计划账号相关信息删除日期")
	@JsonFormat(pattern = DATE_FORMAT)
	@DateTimeFormat(pattern = DATE_FORMAT)
	private LocalDate planOrRealAccountLinkInfoRemoveDate;

	/**
	 * 账号相关信息是否被删除
	 */
	@TableField("removed")
    @ApiModelProperty(value = "账号相关信息是否被删除")
	private Boolean removed;

	/**
	 * 账号相关信息删除失败原因
	 */
	@TableField(value = "remove_fail_reason", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "账号相关信息删除失败原因")
	private String removeFailReason;

	/**
	 * 用户ID
	 */
	@TableField("user_id")
    @ApiModelProperty(value = "用户ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long userId;

	/**
	 * 用户名
	 */
	@TableField("username")
	@ApiModelProperty(value = "用户名")
	private String username;

	/**
	 * 用户姓名
	 */
	@TableField("name")
	@ApiModelProperty(value = "用户姓名")
	private String name;

	/**
	 * 用户角色身份
	 */
	@TableField("user_role")
	@ApiModelProperty(value = "用户角色身份")
	private String userRole;

	/**
	 * 用户手机号
	 */
	@TableField("mobile")
	@ApiModelProperty(value = "用户手机号")
	private String mobile;

	@TableField(exist = false)
	@ApiModelProperty(value = "志愿者姓名")
	private String volunteerName;

	@TableField(exist = false)
	@ApiModelProperty(value = "用户状态")
	private Boolean userStatus;

	@TableField(exist = false)
	@ApiModelProperty(value = "用户是否存在")
	private Boolean userExists;
}
