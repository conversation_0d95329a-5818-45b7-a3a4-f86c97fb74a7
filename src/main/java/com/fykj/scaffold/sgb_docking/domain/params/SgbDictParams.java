package com.fykj.scaffold.sgb_docking.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 苏州志愿者平台字典数据查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("苏州志愿者平台字典数据查询参数")
public class SgbDictParams extends BaseParams {

    private static final long serialVersionUID = 1L;

    /**
     * 字典值
     */
    @ApiModelProperty("字典值")
    @MatchType(QueryType.LIKE)
    private String value;

    /**
     * 字典标签
     */
    @ApiModelProperty("字典标签")
    @MatchType(QueryType.LIKE)
    private String label;

    /**
     * 字典类型
     */
    @ApiModelProperty("字典类型")
    @MatchType(QueryType.LIKE)
    private String type;
    
    /**
     * 字典描述
     */
    @ApiModelProperty("字典描述")
    @MatchType(QueryType.LIKE)
    private String describe;
} 