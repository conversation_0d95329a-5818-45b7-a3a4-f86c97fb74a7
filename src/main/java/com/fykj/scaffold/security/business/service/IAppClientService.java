package com.fykj.scaffold.security.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.security.business.domain.entity.AppClient;

/**
 * 应用客户端信息服务类
 *
 * <AUTHOR>
 * @date 2021-05-20
 */
public interface IAppClientService extends IService<AppClient> {

    /**
     * 获取指定 app详情
     *
     * @param appId AppId
     * @return app详情
     */
    AppClient getByAppId(String appId);

    /**
     * 重置密钥
     *
     * @param appId AppId
     * @return 密钥
     */
    String secretRest(String appId);
}

