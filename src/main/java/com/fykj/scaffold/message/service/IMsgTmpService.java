package com.fykj.scaffold.message.service;

import com.fykj.scaffold.message.domain.entity.MsgTmp;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 消息模版服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface IMsgTmpService extends IBaseService<MsgTmp> {

    /**
     * 校验code唯一性
     * @param id
     * @param code
     * @return
     */
    boolean checkCode(Long id, String code);

    /**
     * 获取已启用的消息模版列表
     * @return
     */
    List<MsgTmp> listOfEnable();

    /**
     *
     * 根据code获取模版内容
     * @param code
     * @return
     */
    MsgTmp getTmpByCode(String code);
}
