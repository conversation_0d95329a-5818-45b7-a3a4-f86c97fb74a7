package com.fykj.scaffold.zyz.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ZyzActivityScheduleDetailExportAllDto {

    // ========== zyz_activity_schedule表字段 ==========
    /**
     * 计划表主键ID
     */
    @ExcelProperty(value = "计划表主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long scheduleMainId;

    /**
     * 计划表版本号
     */
    @ExcelProperty(value = "计划表版本号")
    private Integer scheduleVersion;

    /**
     * 计划表是否删除
     */
    @ExcelProperty(value = "计划表是否删除")
    private Boolean scheduleIsDeleted;

    /**
     * 计划表创建时间
     */
    @ExcelProperty(value = "计划表创建时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime scheduleCreateDate;

    /**
     * 计划表更新时间
     */
    @ExcelProperty(value = "计划表更新时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime scheduleUpdateDate;

    /**
     * 计划表创建人
     */
    @ExcelProperty(value = "计划表创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long scheduleCreator;

    /**
     * 计划表更新人
     */
    @ExcelProperty(value = "计划表更新人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long scheduleUpdater;

    // ========== zyz_activity_schedule表业务字段 ==========
    /**
     * 计划类型（月计划，季度计划，年计划）
     */
    @DictTrans(transTo = "scheduleTypeText")
    @ExcelIgnore
    private String scheduleType;

    /**
     * 计划类型（月计划，季度计划，年计划）
     */
    @ExcelProperty("计划类型")
    @ApiModelProperty(value = "计划类型（月计划，季度计划，年计划）")
    private String scheduleTypeText;

    /**
     * 计划类型数据例如202405
     */
    @ExcelProperty("计划数值")
    private String scheduleTypeData;

    /**
     * 计划主题
     */
    @ExcelProperty(value = "计划主题")
    private String title;

    /**
     * 审核状态（数据字典）
     */
    @DictTrans(transTo = "auditStatusText")
    @ExcelIgnore
    private String auditStatus;

    /**
     * 审核状态（数据字典）
     */
    @ExcelProperty("审核状态")
    @ApiModelProperty(value = "审核状态（数据字典）")
    private String auditStatusText;

    /**
     * 发布组织code
     */
    @ExcelProperty(value = "发布组织code")
    @ApiModelProperty(value = "发布组织code")
    private String publishOrgCode;

    /**
     * 发布组织
     */
    @ExcelProperty(value = "发布组织")
    private String publishOrgName;

    /**
     * 阵地id（市区返回的）
     */
    @ExcelProperty(value = "阵地ID")
    @ApiModelProperty(value = "阵地id（市区返回的）")
    private String pbId;

    /**
     * 发布团队id
     */
    @ExcelProperty(value = "发布团队ID")
    @ApiModelProperty(value = "发布团队id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long publishTeamId;

    /**
     * 是否团队发布
     */
    @ExcelProperty(value = "是否团队发布")
    @ApiModelProperty(value = "是否团队发布")
    private Boolean teamPublish;

    /**
     * 审核的组织机构
     */
    @ExcelProperty(value = "审核组织code")
    @ApiModelProperty(value = "审核的组织机构")
    private String auditOrgCode;

    /**
     * 同步状态
     */
    @DictTrans(transTo = "syncText")
    @ExcelIgnore
    private String sync;

    /**
     * 同步状态
     */
    @ExcelProperty("同步状态")
    @ApiModelProperty(value = "同步状态")
    private String syncText;

    /**
     * 同步时间
     */
    @ExcelProperty(value = "同步时间")
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime syncTime;

    /**
     * 同步备注
     */
    @ExcelProperty(value = "同步备注")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 市区返回的计划id
     */
    @ExcelProperty(value = "市区计划ID")
    @ApiModelProperty(value = "市区返回的计划id")
    private String schId;

    /**
     * 上架状态
     */
    @ExcelProperty(value = "上架状态")
    @ApiModelProperty(value = "上架状态")
    private Boolean autoStatus;

    /**
     * 提交人
     */
    @ExcelProperty(value = "创建人")
    private String creatorName;

    // ========== zyz_activity_schedule_detail表字段 ==========
    /**
     * 计划id
     */
    @ExcelProperty(value = "计划ID")
    @ApiModelProperty(value = "计划id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long scheduleId;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动预告名称")
    private String name;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "活动开始时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "活动结束时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime endTime;

    /**
     * 数据字典（5大领域）
     */
    @ApiModelProperty(value = "数据字典（5大领域）")
    @DictTrans(transTo = "activityTypeText")
    @ExcelIgnore
    private String activityType;

    /**
     * 数据字典（5大领域）
     */
    @ExcelProperty(value = "服务领域")
    private String activityTypeText;

    /**
     * 实施主体
     */
    @ExcelProperty(value = "实施主体")
    private String subjects;

    /**
     * 参与对象
     */
    @DictTrans(transTo = "targetsText")
    @ExcelIgnore
    private String targets;

    /**
     * 参与对象
     */
    @ExcelProperty(value = "参与对象")
    private String targetsText;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String linkMan;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String linkPhone;

    /**
     * 活动地点
     */
    @ExcelProperty(value = "活动地点")
    private String address;

    /**
     * 市区返回的计划里面活动的id
     */
    @ExcelProperty(value = "市区活动ID")
    @ApiModelProperty(value = "市区返回的计划里面活动的id")
    private String preId;

    // ========== zyz_activity_schedule_detail表BaseEntity字段 ==========
    /**
     * 详情表主键ID
     */
    @ExcelProperty(value = "详情表主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 详情表版本号
     */
    @ExcelProperty(value = "详情表版本号")
    private Integer version;

    /**
     * 详情表是否删除
     */
    @ExcelProperty(value = "详情表是否删除")
    private Boolean isDeleted;

    /**
     * 详情表创建时间
     */
    @ExcelProperty(value = "详情表创建时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime createDate;

    /**
     * 详情表更新时间
     */
    @ExcelProperty(value = "详情表更新时间")
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime updateDate;

    /**
     * 详情表创建人
     */
    @ExcelProperty(value = "详情表创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long creator;

    /**
     * 详情表更新人
     */
    @ExcelProperty(value = "详情表更新人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long updater;

}
