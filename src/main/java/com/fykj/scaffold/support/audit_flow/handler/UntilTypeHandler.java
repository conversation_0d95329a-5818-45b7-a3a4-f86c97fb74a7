package com.fykj.scaffold.support.audit_flow.handler;

import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.audit_flow.AuditFlowHandleInstance;
import com.fykj.scaffold.support.audit_flow.AuditFlowInput;
import com.fykj.scaffold.support.audit_flow.AuditFlowOutput;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

/**
 * 直到某审核级审核处理引擎
 */
@Component
public class UntilTypeHandler extends AuditFlowHandleInstance {

    @Autowired
    private ISysOrgService orgService;

    @Override
    public AuditFlowOutput handle(AuditFlowInput input) {
        Integer untilLevel = input.getUntilLevel();
        if (ObjectUtil.isEmpty(untilLevel)) {
            throw new BusinessException(ResultCode.FAIL, "直到指定级数未知！");
        }
        // 提交实体的时候返回审核流转情况
        if (ObjectUtil.isEmpty(input.getPass())) {
            return submitHandle(input, untilLevel);
        }
        // 审核操作的时候返回审核流转情况
        return auditHandle(input, untilLevel);
    }

    private AuditFlowOutput submitHandle(AuditFlowInput input, Integer untilLevel) {
        if (ObjectUtil.isEmpty(input.getTeamPublish())) {
            throw new BusinessException(ResultCode.FAIL, "是否是团队发布未知！");
        }
        // 处理团队提交的数据
        if (input.getTeamPublish()) {
            return teamPublishDeal(input, untilLevel, true);
        }
        // 处理组织提交的数据
        return orgPublishDeal(input, untilLevel, true);
    }

    private AuditFlowOutput auditHandle(AuditFlowInput input, Integer untilLevel) {
        String currentAuditOrgCode = input.getCurrentOrgCode();
        if (StringUtil.isEmpty(currentAuditOrgCode)) {
            throw new BusinessException(ResultCode.FAIL, "当前审核组织未知！");
        }
        SysOrg currentAuditOrg = orgService.getByCode(currentAuditOrgCode);
        if (currentAuditOrg == null) {
            throw new BusinessException(ResultCode.FAIL, "当前审核组织编码有误或当前审核组织不存在！");
        }
        // 处理组织提交的数据
        return auditFlow(currentAuditOrg, input, untilLevel);
    }

    private AuditFlowOutput auditFlow(SysOrg currentAuditOrg, AuditFlowInput input, Integer untilLevel) {
        Integer currentAuditOrgLevel = currentAuditOrg.getLevel();
        if (currentAuditOrgLevel < untilLevel) {
            throw new BusinessException(ResultCode.FAIL, "您无需对这条数据审核");
        }
        AuditFlowOutput output = new AuditFlowOutput();
        int interval = currentAuditOrgLevel - untilLevel;
        Boolean pass = input.getPass();
        if (interval == 0) {
            output.setStatus(pass ? 1 : 2);
            return output;
        }
        if (!pass) {
            output.setStatus(2);
            return output;
        }
        SysOrg parentOrg = getParentOrgByOrg(currentAuditOrg.getCode());
        output.setStatus(0);
        output.setOrgCode(parentOrg.getCode());
        return output;
    }
}
