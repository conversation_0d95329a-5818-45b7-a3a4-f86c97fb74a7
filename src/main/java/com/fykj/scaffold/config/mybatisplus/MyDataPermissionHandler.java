package com.fykj.scaffold.config.mybatisplus;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ClassUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import constants.Mark;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.LikeExpression;
import net.sf.jsqlparser.schema.Column;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MyDataPermissionHandler implements DataPermissionHandler {

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        Method matchMethod = fetchMethod(mappedStatementId);
        //没有匹配的方法，则不需要做过滤
        if (matchMethod == null) {
            return where;
        }
        //当前sql数据所在部门字段
        final String deptCodeColumn = matchMethod.getAnnotation(DataFilter.class).deptCodeLinkColumn();
        final String teamIdColumn = matchMethod.getAnnotation(DataFilter.class).teamId();

        Expression expression;
        //系统超级管理员和协会用户角色全部数据
        if (Cons.RoleCode.ROLE_CODE_ASSOCIATION_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            return where;
        }
        //团队管理员强制等于
        else if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            expression = new EqualsTo(new Column(teamIdColumn), new LongValue(getTeamId()));
        }
        //社区强制等于
        else if (Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            expression = new EqualsTo(new Column(deptCodeColumn), new StringValue(getUserOrg()));
        }
        //分协会管理员查看下属所有社区及团队数据
        else if (Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            expression = new LikeExpression()
                    .withLeftExpression(new Column(deptCodeColumn))
                    .withRightExpression(new StringValue(getUserOrgPrefix() + "%"));
        }
        //没有超管，协会，分协会，社区，团队管理员身份，则不适用自定义数据权限
        else {
            throw new BusinessException(ResultCode.BAD_REQUEST, "权限不足，无法查看");
        }
        return new AndExpression(where, new Parenthesis(expression));
    }

    /**
     * 获取用户teamId
     */
    private long getTeamId() {
        Long teamId = Oauth2Util.getTeamId();
        if (teamId == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "权限不足，无法查看此数据。请联系管理员设置数据权限");
        }
        return teamId;
    }

    /**
     * 获取用户Org
     */
    private String getUserOrg() {
        String orgCode = Oauth2Util.getOrgCodePrefix();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "权限不足，无法查看此数据。请联系管理员设置数据权限");
        }
        return orgCode;
    }

    /**
     * 获取用户Org链
     */
    private String getUserOrgPrefix() {
        String orgCodePrefix = Oauth2Util.getOrgCodePrefix();
        if (StringUtil.isEmpty(orgCodePrefix)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "权限不足，无法查看此数据。请联系管理员设置数据权限");
        }
        return orgCodePrefix;
    }


    /**
     * 获取需要做数据权限匹配的方法
     */
    private Method fetchMethod(String mappedStatementId) {
        String className = mappedStatementId.substring(0, mappedStatementId.lastIndexOf(Mark.DOT));
        String methodName = mappedStatementId.substring(mappedStatementId.lastIndexOf(Mark.DOT) + 1);
        Class<?> cls = ClassUtil.loadClass(className, false);
        List<Method> matchMethodList = Arrays.stream(cls.getMethods())
                .filter(method -> method.getName().equals(methodName) && method.isAnnotationPresent(DataFilter.class))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(matchMethodList)) {
            return null;
        }
        if (matchMethodList.size() != 1) {
            log.warn("匹配到多个方法:{}，正常来说多个方法应该对应一个sql，数据权限处理应该是一样的", mappedStatementId);
        }
        return matchMethodList.get(0);
    }
}
