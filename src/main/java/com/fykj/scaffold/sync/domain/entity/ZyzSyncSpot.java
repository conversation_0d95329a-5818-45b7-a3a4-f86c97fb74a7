package com.fykj.scaffold.sync.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 市平台同步实践点
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_sync_spot")
public class ZyzSyncSpot extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 职能部门名称
     */
    @TableField("name")
    @ApiModelProperty(value = "职能部门名称")
    private String name;

    /**
     * 行政区划
     */
    @TableField("region_code")
    @ApiModelProperty(value = "行政区划")
    private String regionCode;


    /**
     * 行政区划
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "行政区划")
    private String regionName;

    /**
     * 负责人
     */
    @TableField("contact")
    @ApiModelProperty(value = "负责人")
    private String contact;

    /**
     * 负责人电话
     */
    @TableField("contact_no")
    @ApiModelProperty(value = "负责人电话")
    private String contactNo;

    /**
     * 简介
     */
    @TableField("intro")
    @ApiModelProperty(value = "简介")
    private String intro;

    /**
     * 详细地址
     */
    @TableField("map_address")
    @ApiModelProperty(value = "详细地址")
    private String mapAddress;

    /**
     * 纬度
     */
    @TableField("tx_map_lat")
    @ApiModelProperty(value = "纬度")
    private BigDecimal txMapLat;

    /**
     * 经度
     */
    @TableField("tx_map_lan")
    @ApiModelProperty(value = "经度")
    private BigDecimal txMapLan;

    /**
     * 图片
     */
    @TableField("pic_logo_url")
    @ApiModelProperty(value = "图片")
    private String picLogoUrl;

    /**
     * 同步状态
     */
    @DictTrans(transTo = "isSyncText")
    @TableField("is_sync")
    @ApiModelProperty(value = "同步状态")
    private String isSync;

    /**
     * 同步状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "同步状态")
    private String isSyncText;

    /**
     * 同步id
     */
    @TableField("sync_id")
    @ApiModelProperty(value = "同步id")
    private String syncId;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 同步备注
     */
    @TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 上/下架
     */
    @TableField("on_shelve")
    @ApiModelProperty(value = "上/下架")
    private Boolean onShelve;
}
