<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityScheduleDetailMapper">
    <select id="listForScheduleDetail" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail">
        SELECT
            d.*,
            a.id AS activityId,
            a.name AS activityName,
            a.start_time AS  activityStartTime,
            a.end_time AS activityEndTime,
            a.publish_org_name AS activityPublishOrgName,
            p.id AS planId,
            p.sync AS planSync,
            p.sync_time AS planSyncTime,
            p.sync_remark AS planSyncRemark
        FROM zyz_activity_schedule_detail d
                 LEFT JOIN `zyz_activity_schedule_act_plan` p ON p.schedule_detail_id=d.id AND p.is_deleted=0
                 LEFT JOIN `zyz_activity` a ON p.activity_id=a.id
        WHERE d.is_deleted=0 AND d.schedule_id=#{scheduleId}
        ORDER BY d.create_date DESC
    </select>
    <select id="listForScheduleDetailExport" resultType="com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportDto">
        SELECT
        s.title,
        s.schedule_type,
        s.schedule_type_data,
        s.publish_org_name,
        s.create_date,
        d.*,
        u.name as creatorName
        FROM  `zyz_activity_schedule` s
        LEFT JOIN zyz_activity_schedule_detail d ON s.id=d.schedule_id AND d.is_deleted=0
        LEFT JOIN sys_org o on s.publish_org_code = o.`code`
        LEFT JOIN sys_user u on u.id=s.creator
        <where>
            AND s.is_deleted=0
            <if test="params.key != null and params.key != ''">
                AND ( s.title LIKE CONCAT('%', CONCAT(#{params.key}, '%'))  )
            </if>
            <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
                and s.publish_org_code = #{params.publishOrgCode}
            </if>
            <if test="params.scheduleType != null and params.scheduleType != ''">
                AND s.schedule_type = #{params.scheduleType}
            </if>
            <if test="params.scheduleTypeData != null and params.scheduleTypeData != '' ">
                AND s.schedule_type_data = #{params.scheduleTypeData}
            </if>
            <if test="params.auditStatus != null and params.auditStatus != '' ">
                AND s.audit_status = #{params.auditStatus}
            </if>
            <if test="params.startTime != null and params.endTime != null">
                AND  (s.create_date &lt; #{params.endTime} AND  s.create_date &gt; #{params.startTime} )
            </if>
        </where>
        ORDER BY s.create_date DESC
    </select>
    <select id="listForScheduleDetailFrontend"
            resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail">
        SELECT
            d.*,
            a.id AS activityId,
            a.name AS activityName,
            a.start_time AS  activityStartTime,
            a.end_time AS activityEndTime,
            a.publish_org_name AS activityPublishOrgName,
            p.id AS planId,
            p.sync AS planSync,
            p.sync_time AS planSyncTime,
            p.sync_remark AS planSyncRemark
        FROM zyz_activity_schedule_detail d
                 LEFT JOIN `zyz_activity_schedule_act_plan` p ON p.schedule_detail_id=d.id AND p.is_deleted=0
                 LEFT JOIN `zyz_activity` a ON p.activity_id=a.id
        WHERE d.is_deleted=0 AND d.schedule_id=#{scheduleId}
        ORDER BY d.start_time
    </select>

    <select id="listForScheduleDetailExportAll" resultType="com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportAllDto">
        SELECT
        -- zyz_activity_schedule表字段
        s.id as scheduleMainId,
        s.version as scheduleVersion,
        s.is_deleted as scheduleIsDeleted,
        s.create_date as scheduleCreateDate,
        s.update_date as scheduleUpdateDate,
        s.creator as scheduleCreator,
        s.updater as scheduleUpdater,
        s.schedule_type,
        s.schedule_type_data,
        s.title,
        s.audit_status,
        s.publish_org_code,
        s.publish_org_name,
        s.pb_id,
        s.publish_team_id,
        s.team_publish,
        s.audit_org_code,
        s.sync,
        s.sync_time,
        s.sync_remark,
        s.sch_id,
        s.auto_status,
        u.name as creatorName,
        -- zyz_activity_schedule_detail表字段
        d.id,
        d.version,
        d.is_deleted,
        d.create_date,
        d.update_date,
        d.creator,
        d.updater,
        d.schedule_id,
        d.name,
        d.start_time,
        d.end_time,
        d.activity_type,
        d.subjects,
        d.targets,
        d.link_man,
        d.link_phone,
        d.address,
        d.pre_id
        FROM  `zyz_activity_schedule` s
        LEFT JOIN zyz_activity_schedule_detail d ON s.id=d.schedule_id AND d.is_deleted=0
        LEFT JOIN sys_org o on s.publish_org_code = o.`code`
        LEFT JOIN sys_user u on u.id=s.creator
        <where>
            AND s.is_deleted=0
            <if test="params.key != null and params.key != ''">
                AND ( s.title LIKE CONCAT('%', CONCAT(#{params.key}, '%'))  )
            </if>
            <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
                and s.publish_org_code = #{params.publishOrgCode}
            </if>
            <if test="params.auditStatus != null and params.auditStatus != ''">
                AND s.audit_status = #{params.auditStatus}
            </if>
            <if test="params.scheduleType != null and params.scheduleType != '' ">
                AND s.schedule_type = #{params.scheduleType}
            </if>
            <if test="params.scheduleTypeData != null and params.scheduleTypeData != '' ">
                AND s.schedule_type_data = #{params.scheduleTypeData}
            </if>
        </where>
        ORDER BY s.create_date DESC, d.create_date DESC
    </select>
</mapper>
