package com.fykj.scaffold.sgb_docking.util;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.sgb_docking.config.SgbApiProperties;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import result.ResultCode;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 苏州志愿者平台API工具类
 * 用于发送HTTP请求到苏州志愿者社会工作部平台
 */
@Slf4j
@Component
public class SgbApiUtil {
    
    @Autowired
    private SgbApiProperties sgbApiProperties;
    
    // 静态实例，用于在静态方法中使用
    private static SgbApiUtil instance;
    
    @PostConstruct
    public void init() {
        instance = this;
    }
    
    /**
     * 发送请求到平台并返回JSON结果
     * @param method 接口方法名
     * @param params 请求参数，JSON字符串
     * @return 响应字符串
     */
    public static String sendRequest(String method, String params) {
        // 生成唯一requestId，18位随机数
        String requestId = String.valueOf(System.currentTimeMillis()).substring(0, 10) + 
                String.format("%08d", new Random().nextInt(100000000));
        
        // 准备接口参数
        Map<String, Object> apiParams = new HashMap<>();
        apiParams.put("requestId", requestId);
        apiParams.put("content", params);
        
        // 获取当前时间毫秒数
        String requestTime = String.valueOf(System.currentTimeMillis());
        
        // 构建请求头
        Map<String, String> headers = new TreeMap<>();
        headers.put("algorithm", instance.sgbApiProperties.getAlgorithm());
        headers.put("appKey", instance.sgbApiProperties.getAppKey());
        headers.put("method", method);
        headers.put("requestTime", requestTime);
        headers.put("v", "1.0");
        
        // 构建待签名字符串
        String unsignedString = buildUnsignedString(headers, apiParams);
        
        // 生成签名
        String sign = generateSign(unsignedString, instance.sgbApiProperties.getSecretKey());
        
        // 添加签名到请求头
        headers.put("sign", sign);
        
        log.info("调用苏州志愿者平台接口开始，方法：{}，参数：{}", method, params);
        
        // 发送请求
        String response = doPost(instance.sgbApiProperties.getUrl(), headers, apiParams);
        
        log.info("调用苏州志愿者平台接口结束，响应：{}", response);
        
        return response;
    }
    
    /**
     * 构建待签名字符串
     * @param headers 请求头
     * @param apiParams 接口参数
     * @return 待签名字符串
     */
    private static String buildUnsignedString(Map<String, String> headers, Map<String, Object> apiParams) {
        // 合并所有参数
        Map<String, Object> allParams = new TreeMap<>();
        allParams.putAll(headers);
        allParams.putAll(apiParams);
        
        // 按照键名排序并构建字符串
        StringBuilder stringBuilder = new StringBuilder();
        boolean isFirst = true;
        
        for (Map.Entry<String, Object> entry : allParams.entrySet()) {
            if ("sign".equals(entry.getKey())) {
                continue;
            }
            
            if (!isFirst) {
                stringBuilder.append("&");
            }
            
            isFirst = false;
            String value = JSON.toJSONString(entry.getValue());
            stringBuilder.append(entry.getKey()).append("=").append(value);
        }
        
        return stringBuilder.toString();
    }
    
    /**
     * 生成签名
     * @param unsignedString 待签名字符串
     * @param secretKey 密钥
     * @return 签名
     */
    private static String generateSign(String unsignedString, String secretKey) {
        // 使用HmacSHA256算法生成签名
        log.info("开始生成加密sign，加密字符串：{}，密钥：{}", unsignedString, secretKey);
        HMac hmac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes());
        return hmac.digestHex(unsignedString);
    }
    
    /**
     * 执行POST请求
     * @param url 请求URL
     * @param headers 请求头
     * @param params 请求参数
     * @return 响应字符串
     */
    private static String doPost(String url, Map<String, String> headers, Map<String, Object> params) {
        try {

            LinkedHashMap<String, List<String>> header = new LinkedHashMap<>();
            for (Map.Entry<String, String> stringStringEntry : headers.entrySet()) {
                header.put(stringStringEntry.getKey(), Collections.singletonList(stringStringEntry.getValue()));
            }

            String body = JSON.toJSONString(params);
            log.info("POST请求, URL: {}, Headers: {}, Params: {}, body: {}", url, headers, params, body);
            // 创建POST请求
            HttpRequest request = HttpUtil.createPost(url)
                    .header(header, true)
                    .body(body)
                    .timeout(10 * 1000);
            HttpResponse response = request
                    .execute();
            
            // 检查响应状态
            if (HttpStatus.HTTP_OK != response.getStatus()) {
                log.error("请求苏州志愿者平台接口错误: {}", response);
                throw new BusinessException(ResultCode.SERVICE_FAIL, "请求平台接口失败！");
            }
            
            // 返回响应内容
            String responseBody = response.body();
            
            // 检查响应是否包含错误信息
            if (StringUtils.hasText(responseBody)) {
                JSONObject jsonObject = JSONUtil.parseObj(responseBody);
                // 检查info数组中是否有错误信息
                if (jsonObject.containsKey("info")) {
                    List<?> infoList = jsonObject.getBeanList("info", Object.class);
                    if (infoList != null && !infoList.isEmpty()) {
                        Object firstItem = infoList.get(0);
                        if (firstItem instanceof Map) {
                            Map<?, ?> itemMap = (Map<?, ?>) firstItem;
                            if (itemMap.containsKey("code") && !"SUCCESS".equals(itemMap.get("code"))) {
                                log.error("苏州志愿者平台接口返回错误: {}", responseBody);
                            }
                        }
                    }
                }
            }
            
            return responseBody;
        } catch (Exception e) {
            log.error("请求苏州志愿者平台接口错误", e);
            throw new BusinessException(ResultCode.SERVICE_FAIL, "请求平台接口失败！");
        }
    }
}