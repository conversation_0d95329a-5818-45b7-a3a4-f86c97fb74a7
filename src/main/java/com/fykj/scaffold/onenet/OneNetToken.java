package com.fykj.scaffold.onenet;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 一网通办token对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OneNetToken implements Serializable {

    private static final long serialVersionUID = 651455709596528571L;

    /**
     * 令牌
     */
    private String token;

    /**
     * 刷新token（用于令牌失效时，进行刷新）
     */
    private String refreshtoken;

    /**
     * 用户类型，0：后台用户，1:个人，2：法人，通过该值，第三方分别对应调用后台用户、个人法人用户信息获取接口
     */
    private String usertype;

    /**
     * 用户uuid
     */
    private String uuid;

}
