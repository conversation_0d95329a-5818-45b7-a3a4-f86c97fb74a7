package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.join_team.JoinTeam;
import com.fykj.volunteer.sync.util.domain.join_team.JoinTeamResult;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.LocalDateTimeUtil;

import java.time.LocalDateTime;

/**
 * 团队加入同步
 */
@Slf4j
@Component
public class TeamJoinSyncer {
    @Autowired
    private IZyzVolunteerTeamService volunteerTeamService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog sync(long teamId, long volunteerId, long syncBillId) {
        ZyzVolunteerTeam volunteerTeam = volunteerTeamService.getByVolunteerIdAndTeamId(volunteerId, teamId);
        if (Cons.PlatformSyncState.SUCCESS.equals(volunteerTeam.getIsSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此数据已同步成功，无需再次同步");
        }

        ZyzVolunteer volunteer = volunteerService.getById(volunteerId);
        if (volunteer == null || !Cons.PlatformSyncState.SUCCESS.equals(volunteer.getIsSync())) {
            log.warn("志愿者信息有误，无法同步：{}", volunteerId);
            throw new BusinessException(ResultCode.BAD_REQUEST, "志愿者信息有误，无法同步，请联系管理员处理");
        }

        ZyzTeam team = teamService.getById(teamId);
        if (team == null || !Cons.PlatformSyncState.SUCCESS.equals(team.getIsSync())) {
            log.warn("团队信息有误信息有误，无法同步：{}", teamId);
            throw new BusinessException(ResultCode.BAD_REQUEST, "团队信息有误信息有误，无法同步，请联系管理员处理");
        }

        JoinTeam join = new JoinTeam();
        join.setIdcode(volunteer.getCertificateId());
        join.setIsfulltime("是");
        join.setJointime(LocalDateTimeUtil.formatDate(volunteerTeam.getJoinTime().toLocalDate()));
        join.setOrgid(team.getSyncId());
        join.setUniquecode(String.valueOf(volunteerTeam.getId()));
        JoinTeamResult result = ApiWebService.joinTeam(join);

        String resultCode = result.getCode();
        String msg = result.getMsg();

        volunteerTeam.setSyncTime(LocalDateTime.now());
        volunteerTeam.setSyncRemark(msg);
        //成功
        if ("同步成功".equals(msg)) {
            volunteerTeam.setSyncId(result.getUniquecode());
            volunteerTeam.setIsSync(Cons.PlatformSyncState.SUCCESS);
        }
        //重复同步
        else if (msg.contains("同步失败：该成员已加入该组织")) {
            volunteerTeam.setIsSync(Cons.PlatformSyncState.SUCCESS);
            volunteerTeam.setSyncId(result.getUniquecode());
            volunteerTeam.setSyncRemark("市平台反馈已经在此组织，系统自定判定同步成功");
        }
        //其他一律失败
        else {
            volunteerTeam.setIsSync(Cons.PlatformSyncState.FAILURE);
        }

        //启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.TEAM_JOIN_SYNC, resultCode, msg
                , syncBillId, teamId, volunteer.getName() + "加入团队:" + team.getName()
                , JSONUtil.toJsonStr(join), JSONUtil.toJsonStr(result));

        //回写业务
        volunteerTeamService.updateById(volunteerTeam);

        return log;
    }
}
