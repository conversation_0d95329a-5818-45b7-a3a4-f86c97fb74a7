package com.fykj.scaffold.sgb_docking.cons;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public interface SgbCons {

    /**
     * 响应结果Code
     */
    @Getter
    @AllArgsConstructor
    enum SgbSaveType {
        // 成功
        INSERT("1"),
        // 失败
        UPDATE("2");

        private final String value;

    }

    /**
     * 志愿阵地类型枚举
     */
    @Getter
    @AllArgsConstructor
    enum PositionType {
        ASSOCIATION(1, "1001", "协会"),
        SUB_ASSOCIATION(2, "1002", "分协会"),
        COMMUNITY(3, "1003", "社区");

        private final Integer level;
        private final String typeId;
        private final String description;

        /**
         * 根据组织层级获取阵地类型ID
         *
         * @param level 组织层级
         * @return 阵地类型ID，默认返回社区类型
         */
        public static String getTypeIdByLevel(Integer level) {
            if (level == null) {
                return COMMUNITY.getTypeId();
            }

            return Arrays.stream(values())
                    .filter(type -> type.getLevel().equals(level))
                    .findFirst()
                    .map(PositionType::getTypeId)
                    .orElse(COMMUNITY.getTypeId());
        }
    }

    /**
     * 响应结果Code
     */
    enum SgbResponseCode {
        // 成功
        SUCCESS,
        // 失败
        FAIL
    }

    /**
     * 市平台同步业务码
     */
    interface PlatformSyncBiz {
        /**
         * 同步志愿者
         */
        String VOLUNTEER_SYNC = "sgb_sync_biz_volunteer";
        /**
         * 同步志愿阵地
         */
        String BASE_SYNC = "sgb_sync_biz_base_sync";
        /**
         * 同步团队
         */
        String TEAM_SYNC = "sgb_sync_biz_team";
        /**
         * 同步加入团队
         */
        String TEAM_JOIN_SYNC = "sgb_sync_biz_join_team";
        /**
         * 活动同步
         */
        String ACTIVITY_SYNC = "sgb_sync_biz_activity";
        /**
         * 活动发布招募同步
         */
        String ACTIVITY_RECRUIT_SYNC = "sgb_sync_biz_activity_recruit";
        /**
         * 活动加入招募同步
         */
        String ACTIVITY_MEMBER_SYNC = "sgb_sync_biz_activity_member";
        /**
         * 服务时长同步
         */
        String SERVICE_TIME_SYNC = "sgb_sync_biz_service_time";
    }

    /**
     * 苏州社工部API方法名常量
     */
    interface ApiMethod {
        /**
         * 推送志愿者信息
         */
        String SYNC_USER = "syncSgbUser";
        /**
         * 推送志愿阵地信息
         */
        String SYNC_POSITION = "syncSgbPosition";
        /**
         * 推送组织信息
         */
        String SYNC_TEAM = "syncSgbTeam";
        /**
         * 推送加入组织信息
         */
        String SYNC_ORG_MEMBER = "syncSgbOrgMember";
        /**
         * 推送活动信息
         */
        String SYNC_ACTIVITY = "syncSgbActp";
        /**
         * 推送活动招募信息
         */
        String SYNC_ACTIVITY_RECRUIT = "syncSgbActs";
        /**
         * 推送加入活动招募信息
         */
        String SYNC_ACTIVITY_MEMBER = "syncSgbActMember";
        /**
         * 推送服务时长信息
         */
        String SYNC_SERVICE_TIME = "syncSgbServiceTime";
        /**
         * 根据组织名称查询组织ID
         */
        String GET_TEAM_ID_BY_NAME = "getSgbTeamIdByName";
    }

    /**
     * 同步功能开关字典常量
     */
    interface SyncSwitch {
        /**
         * SGB同步功能开关
         */
        String SGB_SYNC_SWITCH = "SGB_SYNC_SWITCH";
        /**
         * 平台同步功能开关
         */
        String WMB_SYNC_SWITCH = "WMB_SYNC_SWITCH";
    }

    /**
     * 同步补偿天数配置字典常量
     */
    interface SyncCompensateDays {
        /**
         * SGB同步补偿天数
         */
        String SGB_SYNC_COMPENSATE_DAYS = "SGB_SYNC_COMPENSATE_DAYS";
        /**
         * WMB同步补偿天数
         */
        String WMB_SYNC_COMPENSATE_DAYS = "WMB_SYNC_COMPENSATE_DAYS";
    }

    /**
     * SGB同步成功消息列表
     */
    interface SuccessMessages {
        /**
         * 表示同步成功的消息列表
         */
        List<String> SUCCESS_MSG_LIST = Arrays.asList(
                "同步失败：该志愿者已存在",
                "同步失败：数据重复",
                "同步失败：该成员已加入该组织",
                "加入招募失败：该成员已加入该招募",
                "同步失败：该组织已存在",
                "同步失败：该组织已上传，请勿重复上传"
        );
    }
}
