package com.fykj.scaffold.zsq_docking.receive.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.domain.entity.SysUserExpand;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.audit_flow.AuditFlowInput;
import com.fykj.scaffold.support.audit_flow.AuditHandleResult;
import com.fykj.scaffold.support.audit_flow.AuditHandleUtil;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.support.utils.RegexUtils;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;
import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import com.fykj.scaffold.zsq_docking.cons.ActivityDataDockingTypeEnum;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingActivityRecord;
import com.fykj.scaffold.zsq_docking.receive.aop.ZSQDataOperationLog;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ActivityDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.RequirementTempSyncForZyzDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ResourceTempSyncForZyzDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQActivityInfoDto;
import com.fykj.scaffold.zsq_docking.receive.service.IActivityReceiveFromZSQService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.*;
import com.fykj.scaffold.zyz.service.*;
import exception.BusinessException;
import fykj.microservice.core.base.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.StrPool.LF;
import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;
import static com.fykj.scaffold.support.conns.Cons.PlatformSyncState.WAIT_SYNC;
import static com.fykj.scaffold.support.conns.Cons.TOP_DEPT_CODE;
import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_ACTIVITY_AUDIT;
import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_ACTIVITY_AUDIT_RESULT;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.ActivityDataDockingType.*;
import static com.fykj.scaffold.zyz.conns.ZyzCons.*;
import static constants.Mark.COMMA;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ActivityReceiveFromZSQServiceImpl implements IActivityReceiveFromZSQService {

    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern(DATETIME_FORMAT);
    @Autowired
    private IZyzActivityService activityService;
    @Autowired
    private ISysOrgService orgService;
    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private IZyzSyncBelongFieldDictService belongFieldDictService;

    @Autowired
    private IZyzRequirementTempService reqTempService;

    @Autowired
    private IZyzResourceTempService resTempService;

    @Autowired
    private IZyzRequirementService reqService;

    @Autowired
    private IZyzResourceService resService;

    @Autowired
    private IZyzResourceAppointmentService resAppointService;

    @Autowired
    private IZyzActivityTimePeriodService activityTimePeriodService;

    @Autowired
    private IZyzActivityLogService logService;

    @Autowired
    private AuditHandleUtil auditUtil;

    @Autowired
    private IZyzResourceLogService resLogService;

    @Autowired
    private IZyzRequirementLogService reqLogService;


    @Override
    @ZSQDataOperationLog
    public Result docking(ActivityDockingDto dockingInfo, ZSQActivityInfoDto data, ZSQDockingActivityRecord dockingRecord) {
        String operation = dockingInfo.getOperation();
        String zwtUserId = dockingInfo.getZwtUserId();
        switch (operation) {
            case DT_CREATE:
                // 活动创建
                return activitySave(zwtUserId, data, operation, false);
            case DT_UPDATE:
                // 活动编辑
                return activityUpdate(zwtUserId, data, operation);
            case DT_DELETE:
                // 活动删除
                Long activityId = dockingInfo.getActId();
                return activityDelete(zwtUserId, data, activityId);
            case DT_ON_OFF:
                // 活动上/下架
                Boolean onShelf = dockingInfo.getOnShelf();
                return activityOnOff(zwtUserId, data, onShelf);
            case DT_WITHDRAW:
                // 撤回
                return activityWithdraw(zwtUserId, data);
            default:
                throw new BusinessException(ResultCode.FAIL, "活动操作类型异常，异常类型【" + operation + "】！");
        }
    }

    private SysOrg validateActivityPublishOrg(String orgCode) {
        if (StringUtils.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "活动创建组织未知！");
        }
        SysOrg org = orgService.getByCode(orgCode);
        if (org == null) {
            throw new BusinessException(ResultCode.FAIL, "活动创建组织不存在！");
        }
        return org;
    }

    private List<SysUserExpand> validateZwtUser(String zwtUserId) {
        return new ArrayList<>();
    }


    private Result activitySave(String zwtUserId, ZSQActivityInfoDto data, String operation, Boolean update) {
        Long activityId = data.getBaseInfo().getId();
        // 校验活动创建操作活动是否已存在
        ZyzActivity activity = activityService.lambdaQuery().eq(ZyzActivity::getId, activityId).one();
        if (activity != null) {
            throw new BusinessException(ResultCode.FAIL, "活动已存在，与接收知社区操作【" + ActivityDataDockingTypeEnum.CREATE.getText() + "】冲突！");
        }
        // 获取活动发布组织的信息
        String publishOrgCode = data.getBaseInfo().getPublishOrgCode();
        SysOrg publishOrg = validateActivityPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
        // 获取当前操作人（政务通用户）的身份信息
//        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
//        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
//        // 校验发布组织是否在当前操作人的身份中
//        if (!userOrgCodes.contains(publishOrgCode)) {
//            throw new BusinessException(ResultCode.FAIL, "知社区用户【" + zwtUserId + "（政务通 userid ）】在志愿者平台没有该活动发布组织【" + publishOrg.getName() + "】的管理身份！");
//        }
        // 通过该活动的创建组织判断是否能通过知社区平台的账号进行创建操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该活动发布组织为【" + publishOrgName + "】，所以知社区平台用户无法在志愿者平台同步创建该活动！");
        }
        // 创建活动并保存/子活动/需求/资源等数据
        saveActivity(data, operation);
        // 发送提醒审核短信给审核组织负责人
        if (data.getBaseInfo().getAuditStatus().equals("act_wait_audit") && operation.equals(DT_CREATE)) {
            sendTmpMsg.sendToAuditOrg(SMT_ACTIVITY_AUDIT, Collections.singletonList(activity.getAuditOrgCode()), activity.getName(), publishOrgName);
        }
        return Result.get(true, "活动同步创建成功" + (update ? "（更新时新增）!" : "！"));

    }

    private Result activityUpdate(String zwtUserId, ZSQActivityInfoDto data, String operation) {
        Long activityId = data.getBaseInfo().getId();
        // 校验活动更新操作活动是否存在
        ZyzActivity activity = activityService.lambdaQuery().eq(ZyzActivity::getId, activityId).one();
        if (activity == null) {
            return activitySave(zwtUserId, data, DT_CREATE, true);
            //throw new BusinessException(ResultCode.FAIL, "活动不存在，与接收知社区操作【" + ActivityDataDockingTypeEnum.UPDATE.getText() + "】冲突！");
        }
        if (activity.getAuditStatus().equals("act_audit_success") || activity.getAuditStatus().equals("act_wait_audit")) {
            throw new BusinessException(ResultCode.FAIL, "活动已提交审核，无法修改！");
        }
        // 获取活动发布组织的信息
        String orgCode = activity.getPublishOrgCode();
        String publishOrgCode = data.getBaseInfo().getPublishOrgCode();
        if (!orgCode.equals(publishOrgCode)) {
            throw new BusinessException(ResultCode.FAIL, "志愿者端活动发布组织【" + orgCode + "】和知社区修改的活动所属组织【" + publishOrgCode + "】冲突！");
        }
        SysOrg publishOrg = validateActivityPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
        // 获取当前操作人（政务通用户）的身份信息
        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
//        // 校验发布组织是否在当前操作人的身份中
//        if (!userOrgCodes.contains(publishOrgCode)) {
//            throw new BusinessException(ResultCode.FAIL, "知社区用户【" + zwtUserId + "（政务通 userid ）】在志愿者平台没有该活动发布组织【" + publishOrg.getName() + "】的管理身份，无法同步修改该活动数据！");
//        }
        // 通过该活动的创建组织判断是否能通过知社区平台的账号进行修改操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该活动发布组织为【" + publishOrgName + "】，所以知社区平台用户无法在志愿者平台同步修改该活动！");
        }
        // 创建修改并保存其他活动相关的数据
        saveActivity(data, DT_UPDATE);
        //如果数据已经是待审核或者审核通过，是无法修改的，前面已经拦截了，此处默认是
        // 1 草稿或者审核拒绝后修改，提交审核
        // 2 直接提交审核
        if (data.getBaseInfo().getAuditStatus().equals("act_wait_audit")) {
            // 发送提醒审核短信给审核组织负责人
            sendTmpMsg.sendToAuditOrg(SMT_ACTIVITY_AUDIT, Collections.singletonList(activity.getAuditOrgCode()), activity.getName(), publishOrgName);
        }
        return Result.get(true, "活动同步修改成功");
    }

    private Result activityDelete(String zwtUserId, ZSQActivityInfoDto data, Long activityId) {
        // 校验删除操作是否仍能在知社区平台获取到该活动数据
//        if (data != null) {
//            throw new BusinessException(ResultCode.FAIL, "从知社区平台仍能获取活动信息，与接收知社区操作【" + ActivityDataDockingTypeEnum.DELETE.getText() + "】冲突！");
//        }
        ZyzActivity activity = activityService.lambdaQuery().eq(ZyzActivity::getId, activityId).one();
        // 校验是否志愿者平台已删除
        if (activity == null) {
            return Result.get(true, "该数据在知社区平台通知志愿者平台删除前已删除成功！");
        }
        if (activity.getAuditStatus().equals("act_audit_success") || activity.getAuditStatus().equals("act_wait_audit")) {
            throw new BusinessException(ResultCode.FAIL, "活动已提交审核，无法修改！");
        }
        // 获取活动发布组织的信息
        String publishOrgCode = activity.getPublishOrgCode();
        SysOrg publishOrg = validateActivityPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
//        // 获取当前操作人（政务通用户）的身份信息
//        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
//        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
//        // 校验当前操作人是否有权限删除该条数据
//        boolean enableDelete = false;
//        for (String it : publishOrgLink) {
//            if (userOrgCodes.contains(it)) {
//                enableDelete = true;
//                break;
//            }
//        }
//        if (!enableDelete) {
//            throw new BusinessException(ResultCode.FAIL, "知社区用户【" + zwtUserId + "（政务通 userid ）】在志愿者平台没有该活动发布组织【" + publishOrg.getName() + "】及其上级组织的任一管理身份，无法同步删除该活动数据！");
//        }
        // 通过该活动的创建组织判断是否能通过知社区平台的账号进行删除操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该活动发布组织为【" + publishOrgName + "】，所以知社区平台用户无法在志愿者平台同步删除该活动！");
        }
        List<Long> idList = new ArrayList<>();
        idList.add(activityId);
        removeReqResDockingInfo(idList);
        activityService.removeByIds(idList);
        return Result.get(true, "活动同步删除成功");
    }

    private Result activityOnOff(String zwtUserId, ZSQActivityInfoDto data, Boolean onShelf) {
        // 校验上下架操作是否通知了具体的上下架状态
        if (onShelf == null) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台通知志愿者平台活动上/下架，但未获取到具体的上/下架操作参数！");
        }
        Long activityId = data.getBaseInfo().getId();
        // 校验活动是否存在
        ZyzActivity activity = activityService.lambdaQuery().eq(ZyzActivity::getId, activityId).one();
        if (activity == null) {
            throw new BusinessException(ResultCode.FAIL, "活动不存在，与接收知社区操作【" + (onShelf ? "上架" : "下架") + "】冲突！");
        }
        Boolean activityCurrentOnShelf = activity.getWebShow();
        if (onShelf.equals(activityCurrentOnShelf)) {
            return Result.get(true, "该数据在知社区平台通知志愿者平台前已进行" + (onShelf ? "上架" : "下架") + "操作！");
        }
        String publishOrgCode = data.getBaseInfo().getPublishOrgCode();
        SysOrg publishOrg = validateActivityPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
//        // 获取当前操作人（政务通用户）的身份信息
//        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
//        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
        // 通过该活动的创建组织判断是否能通过知社区平台的账号进行上/下架操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该活动发布组织为【" + publishOrgName + "】，所以知社区平台用户无法在志愿者平台同步" + (onShelf ? "上架" : "下架") + "该活动！");
        }
        // 校验当前操作人是否有权限上/下架该条数据
        String publishOrgParentOrgCode = publishOrgLink.get(publishOrgLink.size() - 2);
        SysOrg publishOrgParentOrg = orgService.getByCode(publishOrgParentOrgCode);
        if (publishOrgParentOrg == null) {
            throw new BusinessException(ResultCode.FAIL, "志愿者平台不存在该活动发布组织的上级组织信息，无法对该活动进行" + (onShelf ? "上架" : "下架") + "操作！");
        }
//        if (!userOrgCodes.contains(publishOrgParentOrgCode)) {
//            throw new BusinessException(ResultCode.FAIL, "知社区用户【" + zwtUserId + "（政务通 userid ）】在志愿者平台没有该活动发布组织【" + publishOrg.getName() + "】的上级组织【" + publishOrgParentOrg.getName() + "】的管理身份，无法同步" + (onShelf ? "上架" : "下架") + "该活动数据！");
//        }
//            // 校验活动是否审核通过
        String auditStatus = activity.getAuditStatus();
        if (!ACTIVITY_STATUS_AUDIT_SUCCESS.equals(auditStatus)) {
            throw new BusinessException(ResultCode.FAIL, "该活动在志愿者平台尚未审核通过，无法对该活动进行" + (onShelf ? "上架" : "下架") + "操作！");
        }
        // 下架可直接修改保存
        if (!onShelf) {
            activity.setWebShow(Boolean.FALSE);
            activityService.updateById(activity);
            return Result.get(true);
        }
        List<String> notConsistentInfo = validatePlatformDataConsistent(data, activity);
        if (!notConsistentInfo.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台通知上架的活动数据与志愿者平台数据存在信息不一致，无法同步上架该活动！不一致信息如下：\n" + String.join(LF, notConsistentInfo));
        }
        // 上架修改保存
        activity.setWebShow(Boolean.TRUE);
        activityService.updateById(activity);
        return Result.get(true);
    }

    private Result activityWithdraw(String zwtUserId, ZSQActivityInfoDto data) {

        Long activityId = data.getBaseInfo().getId();
        // 校验活动是否存在
        ZyzActivity activity = activityService.lambdaQuery().eq(ZyzActivity::getId, activityId).one();
        if (activity == null) {
            throw new BusinessException(ResultCode.FAIL, "活动不存在！");
        }
        String status = activity.getAuditStatus();
        if (status.equals(ACTIVITY_STATUS_DRAFT)) {
            return Result.get(true, "该数据在知社区平台通知志愿者平台前已进行撤回操作！");
        }
        String publishOrgCode = data.getBaseInfo().getPublishOrgCode();
        SysOrg publishOrg = validateActivityPublishOrg(publishOrgCode);
        String publishOrgCodePrefix = publishOrg.getCodePrefix();
        List<String> publishOrgLink = StringUtils.isEmpty(publishOrgCodePrefix) ? new ArrayList<>() : Arrays.asList(publishOrgCodePrefix.split(COMMA));
//        // 获取当前操作人（政务通用户）的身份信息
//        List<SysUserExpand> userCapacityList = validateZwtUser(zwtUserId);
//        List<String> userOrgCodes = userCapacityList.stream().map(SysUserExpand::getOrgCode).collect(Collectors.toList());
        // 通过该活动的创建组织判断是否能通过知社区平台的账号进行上/下架操作
        String publishOrgName = publishOrg.getName();
        if ((publishOrgLink.size() != 2 && publishOrgLink.size() != 3) || !TOP_DEPT_CODE.equals(publishOrgLink.get(0))) {
            throw new BusinessException(ResultCode.FAIL, "由于该活动发布组织为【" + publishOrgName + "】，所以知社区平台用户无法在志愿者平台同步撤回该活动！");
        }
        // 校验当前操作人是否有权限上/下架该条数据
        String publishOrgParentOrgCode = publishOrgLink.get(publishOrgLink.size() - 2);
        SysOrg publishOrgParentOrg = orgService.getByCode(publishOrgParentOrgCode);
        if (publishOrgParentOrg == null) {
            throw new BusinessException(ResultCode.FAIL, "志愿者平台不存在该活动发布组织的上级组织信息，无法对该活动进行撤回操作！");
        }
//        if (!userOrgCodes.contains(publishOrgParentOrgCode)) {
//            throw new BusinessException(ResultCode.FAIL, "知社区用户【" + zwtUserId + "（政务通 userid ）】在志愿者平台没有该活动发布组织【" + publishOrg.getName() + "】的上级组织【" + publishOrgParentOrg.getName() + "】的管理身份，无法同步" + (onShelf ? "上架" : "下架") + "该活动数据！");
//        }
//
        //撤回，判断是否状态是否为审核通过，只有待审核状态才能撤回
        if (ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(activity.getAuditStatus())) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "该活动已审核通过，无法撤回！");
        }
        //撤回后无论什么状态都变成草稿
        activity.setAuditStatus(ZyzCons.ACTIVITY_STATUS_DRAFT);
        //log表添加操作
        logService.saveLog(activity.getId(), ZyzCons.ACT_RECALL, "活动撤回成功!");
        activityService.updateById(activity);
        return Result.get(true);
    }

    private List<String> validatePlatformDataConsistent(ZSQActivityInfoDto data, ZyzActivity activity) {
        List<String> notConsistentInfo = new ArrayList<>();
        String name = activity.getName(), dataName = data.getBaseInfo().getName();
        if (!validatePropertySame(name, dataName)) {
            notConsistentInfo.add("活动名称不一致【志愿者平台：" + dataName + "，知社区平台：" + name + "】");
        }
        String actPublishOrgCode = activity.getPublishOrgCode(), dataPublishOrgCode = data.getBaseInfo().getPublishOrgCode();
        if (!validatePropertySame(name, dataName)) {
            notConsistentInfo.add("发布组织不一致【志愿者平台：" + actPublishOrgCode + "，知社区平台：" + dataPublishOrgCode + "】");
        }
        String actImg = activity.getPicture(), dataImg = data.getBaseInfo().getPicture();
        if (!validatePropertySame(actImg, dataImg)) {
            notConsistentInfo.add("图片不一致【志愿者平台：" + actImg + "，知社区平台：" + dataImg + "】");
        }
        String actBrief = activity.getActSynopsis(), dataBrief = data.getBaseInfo().getActSynopsis();
        if (!validatePropertySame(actBrief, dataBrief)) {
            notConsistentInfo.add("简介不一致【志愿者平台：" + actBrief + "，知社区平台：" + dataBrief + "】");
        }
        String actRequirement = activity.getApplicationRequirements(), dataRequirement = data.getBaseInfo().getApplicationRequirements();
        if (!validatePropertySame(actRequirement, dataRequirement)) {
            notConsistentInfo.add("报名提交不一致【志愿者平台：" + actRequirement + "，知社区平台：" + dataRequirement + "】");
        }
        return notConsistentInfo;
    }

    private boolean validatePropertySame(String propertyOne, String propertyTwo) {
        boolean bothEmpty = StringUtils.isEmpty(propertyOne) && StringUtils.isEmpty(propertyTwo);
        boolean bothNotEmpty = StringUtils.isNotEmpty(propertyOne) && propertyOne.equals(propertyTwo);
        return bothEmpty || bothNotEmpty;
    }


    private void saveActivity(ZSQActivityInfoDto actDetailInfo, String operation) {
        Long actId = actDetailInfo.getBaseInfo().getId();
        ZyzActivity entity = operation.equals(DT_CREATE) ? new ZyzActivity() : activityService.getById(actId);
        saveActData(actDetailInfo, entity, operation);

        if (actDetailInfo.getBaseInfo().getAuditStatus().equals("act_draft")) {
            logService.saveLog(entity.getId(), ZyzCons.ACT_SAVE_DRAFT, "保存草稿成功!");
        }
        //如果数据已经是待审核或者审核通过，是无法修改的，前面已经拦截了，此处默认是
        // 1 草稿或者审核拒绝后修改，提交审核
        // 2 直接提交审核
        if (actDetailInfo.getBaseInfo().getAuditStatus().equals("act_wait_audit")) {
            logService.saveLog(entity.getId(), ZyzCons.ACT_SUBMIT, "提交审核成功!");
            renderById(actId);
        }

    }

    private void saveActData(ZSQActivityInfoDto actDetailInfo, ZyzActivity entity, String operation) {

        //图片格式验证，经纬度必填验证，联系人电话格式验证
        basicCheck(actDetailInfo);
        List<ZyzActivityTimePeriod> activityTimePeriods = actDetailInfo.getActTimePeriods();
        // 判断是否维护时间段
        if (CollectionUtils.isEmpty(activityTimePeriods)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请维护活动时间段！");
        }
        // 判断时间段是否交叉重复
        checkTimeOverlap(activityTimePeriods);
        // 判断活动时间是否超过最大限制时间
        checkTimeLimit(activityTimePeriods, actDetailInfo.getBaseInfo().getBelongFieldEnd());
        // 检查需求是否被其他活动对接过
        checkReqDocked(entity.getId(), actDetailInfo.getDockingInfo().getReqId());
        // dto数据转entity数据
        BeanUtils.copyProperties(actDetailInfo, entity);
        BeanUtils.copyProperties(actDetailInfo.getBaseInfo(), entity, "version");
        BeanUtils.copyProperties(actDetailInfo.getDockingInfo(), entity);
        entity.setActivityTimes(actDetailInfo.getActTimePeriods());
        // 处理活动维护者信息
        entity.setTeamPublish(false);

        SysOrg org = orgService.getByCode(actDetailInfo.getBaseInfo().getPublishOrgCode());
        if (ObjectUtil.isEmpty(org)) {
            throw new BusinessException(ResultCode.FAIL, "未获取到当前身份的组织架构信息");
        }
        entity.setTeamId(null);
        entity.setPublishOrgCode(org.getCode());
        entity.setPublishOrgName(org.getName());

        // 设置活动类型，活动对接类型为定值
        entity.setActType(ZYZ_ACTIVITY_TYPE_NEW_ERA);
        entity.setDockingType(ZYZ_ACTIVITY_DOCKING_TYPE_REQUIREMENT.concat(COMMA).concat(ZYZ_ACTIVITY_DOCKING_TYPE_RESOURCE));
        // 活动修改时处理不对接的临时数据
        if (ObjectUtil.isNotEmpty(entity.getId()) && operation.equals(DT_UPDATE)) {
            removeDockingTempData(entity);
        }
        //处理开始结束时间,报名截止时间
        dealActTimeInfo(entity);
        //处理所属领域
        entity.setBelongFieldNameTop(belongFieldDictService.getFieldName(entity.getBelongFieldTop()));
        entity.setBelongFieldNameEnd(belongFieldDictService.getFieldName(entity.getBelongFieldEnd()));
        entity.setSubmitTime(LocalDateTime.now());
        entity.setSync(WAIT_SYNC);
        entity.setAuditStatus(ZyzCons.ACTIVITY_STATUS_DRAFT);
        if (operation.equals(DT_CREATE)) {
            activityService.save(entity);
        } else {
            activityService.updateById(entity);
        }

        // 处理活动分时段数据
        dealTimePeriodsInfo(entity);
        //保存活动时段
        activityTimePeriodService.saveActivityTimePeriod(entity.getId(), entity.getActivityTimes());
        //保存资源需求的临时数据
        if (actDetailInfo.getDockingInfo().getResourceTempSyncForZyzDto() != null
                && Boolean.TRUE.equals(actDetailInfo.getDockingInfo().getDockingResCreate())) {
            if (operation.equals(DT_CREATE)) {
                saveResourceTemp(actDetailInfo.getDockingInfo().getResourceTempSyncForZyzDto(), actDetailInfo.getBaseInfo().getPublishOrgCode());
            } else if (operation.equals(DT_UPDATE)) {
                updateResourceTemp(actDetailInfo.getDockingInfo().getResourceTempSyncForZyzDto(), actDetailInfo.getBaseInfo().getPublishOrgCode());
            }

        }
        if (actDetailInfo.getDockingInfo().getRequirementTempSyncForZyzDto() != null
                && Boolean.TRUE.equals(actDetailInfo.getDockingInfo().getDockingReqCreate())) {

            if (operation.equals(DT_CREATE)) {
                saveRequirementTemp(actDetailInfo.getDockingInfo().getRequirementTempSyncForZyzDto(), actDetailInfo.getBaseInfo().getPublishOrgCode());
            } else if (operation.equals(DT_UPDATE)) {
                updateRequirementTemp(actDetailInfo.getDockingInfo().getRequirementTempSyncForZyzDto(), actDetailInfo.getBaseInfo().getPublishOrgCode());
            }
        }

    }

    private void basicCheck(ZSQActivityInfoDto actDetailInfo) {
        //判断图片格式是否正确
        //ImageUtil.checkPic(actDetailInfo.getBaseInfo().getPicture());
        //经纬度必填
        if (actDetailInfo.getBaseInfo().getLatitude() == null || actDetailInfo.getBaseInfo().getLongitude() == null) {
            throw new BusinessException(ResultCode.FAIL, "请点击地图定位，获取经纬度");
        }
        //验证联系电话
        if (!RegexUtils.isMobileExact(actDetailInfo.getBaseInfo().getContactPhone()) && !RegexUtils.isTel(actDetailInfo.getBaseInfo().getContactPhone())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "联系电话格式错误,须为手机号或区号+8位座机号例如051288888888或0512-88888888");
        }
    }

    private void checkTimeOverlap(List<ZyzActivityTimePeriod> activityTimes) {
        // 按照开始时间对时间段进行排序
        activityTimes.sort(Comparator.comparing(ZyzActivityTimePeriod::getStartTime));
        for (int i = 0; i < activityTimes.size() - 1; i++) {
            ZyzActivityTimePeriod objThis = activityTimes.get(i);
            LocalDateTime endThis = objThis.getEndTime();
            LocalDateTime startNext = activityTimes.get(i + 1).getStartTime();
            // 如果 obj1 的结束时间早于 obj2 的开始时间，那么就不需要进行比较
            if (endThis.isBefore(startNext)) {
                continue;
            }
            LocalDateTime startThis = objThis.getStartTime();
            LocalDateTime endNext = activityTimes.get(i + 1).getEndTime();
            throw new BusinessException(ResultCode.SERVICE_FAIL, "活动时间段中" + startThis + "--" + endThis + "与" + startNext + "--" + endNext + "时间段重叠，请重新选择时间段！");
        }
    }


    private void checkTimeLimit(List<ZyzActivityTimePeriod> activityTimes, String typeId) {
        ZyzSyncBelongFieldDict zyzSyncBelongFieldDict = belongFieldDictService.getByTypeId(typeId);
        Integer maxTimeLimit = zyzSyncBelongFieldDict.getMaxTimeLimit();
        Integer minTimeLimit = zyzSyncBelongFieldDict.getMinTimeLimit();
        for (ZyzActivityTimePeriod activityTime : activityTimes) {
            LocalDateTime startTime = activityTime.getStartTime();
            LocalDateTime endTime = activityTime.getEndTime();
            if (ObjectUtil.isNotEmpty(minTimeLimit) && startTime.plusHours(minTimeLimit).isAfter(endTime)) {
                throw new BusinessException(ResultCode.SERVICE_FAIL, "活动时间段中" + startTime + "--" + endTime + "小于最小限制时间，请重新选择时间段！");
            }
            if (ObjectUtil.isNotEmpty(maxTimeLimit) && startTime.plusHours(maxTimeLimit).isBefore(endTime)) {
                throw new BusinessException(ResultCode.SERVICE_FAIL, "活动时间段中" + startTime + "--" + endTime + "小于最小限制时间，请重新选择时间段！");
            }
        }
    }

    private void removeDockingTempData(ZyzActivity entity) {
        ZyzActivity exist = activityService.getById(entity.getId());
        boolean existResCreate = ObjectUtil.isNotEmpty(exist.getDockingResCreate()) && exist.getDockingResCreate();
        boolean currResNotCreate = ObjectUtil.isEmpty(entity.getDockingResCreate()) || !entity.getDockingResCreate();
        boolean currResCreateNotExistResCreate = ObjectUtil.isNotEmpty(entity.getDockingResCreate()) && entity.getDockingResCreate() && existResCreate && !entity.getResId().equals(exist.getResId());
        if (existResCreate && currResNotCreate) {
            entity.setDockingResCreate(false);
        }
        if ((existResCreate && currResNotCreate) || currResCreateNotExistResCreate) {
            try {
                resTempService.removeById(exist.getResId());
            } catch (Exception ignored) {
            }
        }
        boolean existReqCreate = ObjectUtil.isNotEmpty(exist.getDockingReqCreate()) && exist.getDockingReqCreate();
        boolean currReqNotCreate = ObjectUtil.isEmpty(entity.getDockingReqCreate()) || !entity.getDockingReqCreate();
        boolean currReqCreateNotExistReqCreate = ObjectUtil.isNotEmpty(entity.getDockingReqCreate()) && entity.getDockingReqCreate() && existReqCreate && !entity.getReqId().equals(exist.getReqId());
        if (existReqCreate && currReqNotCreate) {
            entity.setDockingReqCreate(false);
        }
        if ((existReqCreate && currReqNotCreate) || currReqCreateNotExistReqCreate) {
            try {
                reqTempService.removeById(exist.getReqId());
            } catch (Exception ignored) {
            }
        }
    }

    private void dealActTimeInfo(ZyzActivity activity) {
        boolean needDeal = ObjectUtil.isEmpty(activity.getStartTime()) || ObjectUtil.isEmpty(activity.getEndTime()) || ObjectUtil.isEmpty(activity.getApplyEndTime());
        if (needDeal) {
            List<ZyzActivityTimePeriod> timePeriods = activity.getActivityTimes();
            activity.setStartTime(getActivityTimesStart(timePeriods));
            activity.setEndTime(getActivityTimesEnd(timePeriods));
            activity.setApplyEndTime(getActivityTimesApplyEnd(timePeriods));
        }
    }

    private LocalDateTime getActivityTimesStart(List<ZyzActivityTimePeriod> activityTimes) {
        if (ObjectUtil.isEmpty(activityTimes)) {
            return null;
        }
        return activityTimes.stream().map(ZyzActivityTimePeriod::getStartTime).min(LocalDateTime::compareTo).orElse(null);
    }

    private LocalDateTime getActivityTimesEnd(List<ZyzActivityTimePeriod> activityTimes) {
        if (ObjectUtil.isEmpty(activityTimes)) {
            return null;
        }
        return activityTimes.stream().map(ZyzActivityTimePeriod::getEndTime).max(LocalDateTime::compareTo).orElse(null);
    }

    private LocalDateTime getActivityTimesApplyEnd(List<ZyzActivityTimePeriod> activityTimes) {
        if (ObjectUtil.isEmpty(activityTimes)) {
            return null;
        }
        return activityTimes.stream().map(ZyzActivityTimePeriod::getApplyEndTime).max(LocalDateTime::compareTo).orElse(null);
    }

    private void dealTimePeriodsInfo(ZyzActivity activity) {
        String recruitTargets = activity.getRecruitTargets();
        Boolean recruitNumDistinguish = activity.getRecruitNumDistinguish();
        if (recruitNumDistinguish) {
            activity.getActivityTimes().forEach(it -> {
                it.setRecruitmentNum(it.getVolunteerRecruitNum() + it.getMassesRecruitNum());
            });
            return;
        }
        if (StringUtil.isEmpty(recruitTargets)) {
            return;
        }
        if (ACT_RECRUIT_TARGET_VOLUNTEER.equals(recruitTargets)) {
            activity.getActivityTimes().forEach(it -> {
                it.setVolunteerRecruitNum(it.getRecruitmentNum());
            });
            return;
        }
        if (ACT_RECRUIT_TARGET_MASSES.equals(recruitTargets)) {
            activity.getActivityTimes().forEach(it -> {
                it.setMassesRecruitNum(it.getRecruitmentNum());
            });
        }
    }

    private void checkReqDocked(Long actId, Long reqId) {
        if (ObjectUtil.isEmpty(reqId)) {
            return;
        }
        if (activityService.lambdaQuery().eq(ZyzActivity::getReqId, reqId).ne(ObjectUtil.isNotEmpty(actId), ZyzActivity::getId, actId).exists()) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "您当前对接的需求已被其他活动对接或正在对接中，请更换对接的需求！");
        }
    }

    private boolean saveResourceTemp(ResourceTempSyncForZyzDto dto, String publishOrgCode) {
        ZyzResourceTemp entity = new ZyzResourceTemp();
        BeanUtils.copyProperties(dto, entity);
        if (resService.lambdaQuery().eq(ZyzResource::getName, entity.getName()).exists()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "同名资源已存在，请输入其他名称");
        }
        //若不是，则发布组织为组织
        entity.setTeamPublish(false);
        SysOrg org = StringUtil.isEmpty(publishOrgCode) ? null : orgService.getByCode(publishOrgCode);
        if (org == null) {
            throw new BusinessException(ResultCode.FAIL, "未获取到当前身份的组织架构信息");
        }
        entity.setPublishOrgCode(org.getCode());
        entity.setPublishOrgName(org.getName());

        entity.setPublishTime(LocalDateTime.now());
        //处理所属领域
        entity.setBelongFieldNameTop(belongFieldDictService.getFieldName(entity.getBelongFieldTop()));
        entity.setBelongFieldNameEnd(belongFieldDictService.getFieldName(entity.getBelongFieldEnd()));
        return resTempService.save(entity);
    }

    private boolean updateResourceTemp(ResourceTempSyncForZyzDto dto, String publishOrgCode) {
        ZyzResourceTemp entity = resTempService.getById(dto.getId());
        BeanUtils.copyProperties(dto, entity);
        if (resService.lambdaQuery().eq(ZyzResource::getName, entity.getName())
                .ne(BaseEntity::getId, dto.getId()).exists()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "同名资源已存在，请输入其他名称");
        }
        //处理所属领域
        entity.setBelongFieldNameTop(belongFieldDictService.getFieldName(entity.getBelongFieldTop()));
        entity.setBelongFieldNameEnd(belongFieldDictService.getFieldName(entity.getBelongFieldEnd()));
        return resTempService.updateById(entity);
    }


    private boolean saveRequirementTemp(RequirementTempSyncForZyzDto dto, String publishOrgCode) {

        ZyzRequirementTemp entity = new ZyzRequirementTemp();
        BeanUtils.copyProperties(dto, entity);
        //若不是，则发布组织为组织
        entity.setTeamPublish(false);
        SysOrg org = StringUtil.isEmpty(publishOrgCode) ? null : orgService.getByCode(publishOrgCode);
        if (org == null) {
            throw new BusinessException(ResultCode.FAIL, "未获取到当前身份的组织架构信息");
        }
        entity.setPublishOrgCode(org.getCode());
        entity.setPublishOrgName(org.getName());

        //处理所属领域
        entity.setBelongFieldNameTop(belongFieldDictService.getFieldName(entity.getBelongFieldTop()));
        entity.setBelongFieldNameEnd(belongFieldDictService.getFieldName(entity.getBelongFieldEnd()));
        return reqTempService.save(entity);
    }

    private boolean updateRequirementTemp(RequirementTempSyncForZyzDto dto, String publishOrgCode) {
        ZyzRequirementTemp entity = reqTempService.getById(dto.getId());
        BeanUtils.copyProperties(dto, entity);
        //处理所属领域
        entity.setBelongFieldNameTop(belongFieldDictService.getFieldName(entity.getBelongFieldTop()));
        entity.setBelongFieldNameEnd(belongFieldDictService.getFieldName(entity.getBelongFieldEnd()));
        return reqTempService.updateById(entity);
    }

    private boolean renderById(Long id) {
        //log表添加数据
        logService.saveLog(id, ZyzCons.ACT_SUBMIT, "提交成功!");
        //修改审核状态为待审核
        ZyzActivity activity = activityService.getById(id);
        activity.setSync(WAIT_SYNC);
        AuditFlowInput input = AuditFlowInput.builder().teamPublish(activity.getTeamPublish()).publishOrgCode(activity.getPublishOrgCode()).publishTeamId(activity.getTeamId()).build();
        AuditHandleResult auditResult = auditUtil.activityFlow(input, false);
        handleTempDockingData(activity, auditResult);
        activity.setSubmitTime(LocalDateTime.now());
        activity.setAuditStatus(auditResult.getStatus());
        //如果审核成功，则set报名开始时间
        if (ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(activity.getAuditStatus())) {
            activity.setApplyStartTime(LocalDateTime.now());
        }
        activity.setAuditOrgCode(auditResult.getOrgCode());
        activity.setWebShow(ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(activity.getAuditStatus()) && activity.getAuditShow());
        if (auditResult.isFinish()) {
            //发送信息给活动发布人
            List<Long> activityIds = new ArrayList<>();
            activityIds.add(id);
            sendTmpMsg.sendToActivityPublisher(SMT_ACTIVITY_AUDIT_RESULT, activityIds, "通过", activity.getName());
        } else {
            //发送信息给审核人
            List<String> orgCodes = new ArrayList<>();
            orgCodes.add(auditResult.getOrgCode());
            sendTmpMsg.sendToAuditOrg(SMT_ACTIVITY_AUDIT, orgCodes, activity.getName(), activity.getPublishOrgName());
        }
        return activityService.updateById(activity);
    }

    private void handleTempDockingData(ZyzActivity activity, AuditHandleResult result) {
        if (!result.isFinish() || !ACTIVITY_STATUS_AUDIT_SUCCESS.equals(result.getStatus())) {
            return;
        }
        Boolean dockingResCreate = activity.getDockingResCreate();
        Long resId = activity.getResId();
        Boolean dockingReqCreate = activity.getDockingReqCreate();
        Long reqId = activity.getReqId();
        if (ObjectUtil.isNotEmpty(resId) && (ObjectUtil.isEmpty(dockingResCreate) || !dockingResCreate)) {
            handleExistRes(resId, activity);
        }
        if (ObjectUtil.isNotEmpty(dockingResCreate) && dockingResCreate && ObjectUtil.isNotEmpty(resId)) {
            handleTempRes(resId, activity);
        }
        if (ObjectUtil.isNotEmpty(reqId) && (ObjectUtil.isEmpty(dockingReqCreate) || !dockingReqCreate)) {
            handleExistReq(reqId);
        }
        if (ObjectUtil.isNotEmpty(dockingReqCreate) && dockingReqCreate && ObjectUtil.isNotEmpty(reqId)) {
            handleTempReq(reqId, activity);
        }
    }

    private void handleExistRes(Long resId, ZyzActivity activity) {
        ZyzResource res = resService.lambdaQuery().eq(ZyzResource::getId, resId).one();
        if (ObjectUtil.isEmpty(res)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "资源不存在！");
        }
        res.setHasAppointmentNum(ObjectUtil.isEmpty(res.getHasAppointmentNum()) ? 1 : res.getHasAppointmentNum() + 1);
        resService.updateById(res);
        ZyzResourceAppointment appoint = new ZyzResourceAppointment();
        generateResAppoint(appoint, res, activity);
        resAppointService.save(appoint);
    }

    private void handleTempRes(Long resId, ZyzActivity activity) {
        ZyzResourceTemp resTemp = resTempService.lambdaQuery().eq(ZyzResourceTemp::getId, resId).one();
        if (ObjectUtil.isEmpty(resTemp)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "新创建的资源不存在！");
        }
        ZyzResource resource = new ZyzResource();
        BeanUtils.copyProperties(resTemp, resource);
        resource.setStatus(RESOURCE_STATUS_AUDIT_SUCCESS);
        resource.setHasAppointmentNum(1);
        resource.setSync(WAIT_SYNC);
        resource.setAutoStatus(resource.getAutoEnable());
        resService.save(resource);
        activity.setResId(resource.getId());
        resLogService.saveLog(resource.getId(), ZyzCons.RES_ACT_DOCKING_CREATE, "活动审核通过且创建资源成功!");
        ZyzResourceAppointment appoint = new ZyzResourceAppointment();
        appoint.setId(resource.getId());
        generateResAppoint(appoint, resource, activity);
        resAppointService.save(appoint);
        resTempService.removeById(resTemp.getId());
    }

    private void generateResAppoint(ZyzResourceAppointment appoint, ZyzResource resource, ZyzActivity activity) {
        appoint.setLinkActId(activity.getId());
        appoint.setResourceId(resource.getId());
        appoint.setResourceName(resource.getName());
        appoint.setOrgCode(resource.getPublishOrgCode());
        appoint.setOrgName(resource.getPublishOrgName());
        appoint.setTeamPublish(resource.getTeamPublish());
        appoint.setTeamId(resource.getTeamId());
        appoint.setApplyTime(LocalDateTime.now());
        appoint.setContactPerson(activity.getContactPerson());
        appoint.setContactPhone(activity.getContactPhone());
        appoint.setAppointmentRemark(activity.getActSynopsis());
        appoint.setAppointmentStartTime(activity.getStartTime());
        appoint.setAppointmentEndTime(activity.getEndTime());
        appoint.setAuditStatus(RESOURCE_APPOINTMENT_STATUS_AUDIT_SUCCESS);
        appoint.setAuditTime(LocalDateTime.now());
        appoint.setAuditStartTime(activity.getStartTime());
        appoint.setAuditEndTime(activity.getEndTime());
        appoint.setPublishActivity(Boolean.TRUE);
        appoint.setActivityAddress(activity.getAddress());
    }

    private void handleExistReq(Long reqId) {
        reqService.lambdaUpdate().eq(ZyzRequirement::getId, reqId).set(ZyzRequirement::getStatus, REQUIREMENT_STATUS_RAS_DOCKING_SUCCESS).update();
    }

    private void handleTempReq(Long reqId, ZyzActivity activity) {
        ZyzRequirementTemp reqTemp = reqTempService.lambdaQuery().eq(ZyzRequirementTemp::getId, reqId).one();
        if (ObjectUtil.isEmpty(reqTemp)) {
            throw new BusinessException(ResultCode.SERVICE_FAIL, "新创建的需求不存在！");
        }
        ZyzRequirement requirement = new ZyzRequirement();
        BeanUtils.copyProperties(reqTemp, requirement);
        requirement.setStatus(REQUIREMENT_STATUS_RAS_DOCKING_SUCCESS);
        requirement.setAutoStatus(requirement.getAutoEnable());
        requirement.setDockingTeam(activity.getTeamPublish());
        requirement.setDockingTeamId(activity.getTeamId());
        requirement.setDockingOrgCode(activity.getPublishOrgCode());
        requirement.setDockingOrgName(activity.getPublishOrgName());
        requirement.setPublishActivity(Boolean.TRUE);
        requirement.setDockingTime(LocalDateTime.now());
        requirement.setDockingContactPerson(activity.getContactPerson());
        requirement.setDockingContactPhone(activity.getContactPhone());
        requirement.setSync(WAIT_SYNC);
        reqService.save(requirement);
        activity.setReqId(requirement.getId());
        reqLogService.saveLog(requirement.getId(), ZyzCons.RO_ACT_DOCKING_CREATE, "活动审核通过且创建需求成功!");
        reqTempService.removeById(reqTemp.getId());
    }

    private void removeReqResDockingInfo(List<Long> idList) {
        List<ZyzActivity> activities = activityService.lambdaQuery().in(ZyzActivity::getId, idList).list();
        if (CollectionUtil.isEmpty(activities)) {
            return;
        }
        List<Long> reqIds = activities.stream().map(ZyzActivity::getReqId).collect(Collectors.toList());
        reqService.removeBatchByIds(reqIds);
        reqTempService.removeBatchByIds(reqIds);
        List<Long> resIds = activities.stream().map(ZyzActivity::getResId).collect(Collectors.toList());
        resAppointService.removeBatchByIds(resIds);
        resTempService.removeBatchByIds(resIds);
    }
}

