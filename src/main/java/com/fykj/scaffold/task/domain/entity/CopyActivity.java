package com.fykj.scaffold.task.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.utils.Desensitise;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Document(indexName = "copy_activity")
public class CopyActivity {
    private static final long serialVersionUID = 1L;

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 活动名称
     */
    @Field(type = FieldType.Text)
    private String name;

    /**
     * 活动地址
     */
    @Field(type = FieldType.Text)
    private String address;
    /**
     * 任务id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "任务id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parameterId;
    /**
     * 经度
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;
    @Field(type = FieldType.Date, format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_second")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private String createDate;
    /**
     * 招募总人数
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "招募总人数")
    private Integer recruitmentNum;

    /**
     * 报名通过人数
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名通过人数")
    private Integer applyNum;
    /**
     * 纬度
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * 联系人
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Desensitise
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 惠及人数
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "惠及人数")
    private Integer benefitPeopleNum;

    /**
     * 是否置顶
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否置顶")
    private Boolean top;

    /**
     * 活动类型
     */
    @Field(type = FieldType.Text)
//    @DictTrans(transTo = "actTypeText")
    @ApiModelProperty(value = "活动类型")
    private String actType;

/*
    @TableField(exist = false)
    @ApiModelProperty(value = "活动类型")
    private String actTypeText;*/


//    /**
//     * 招募类型
//     */
//    @TableField("recruit_type")
//    @DictTrans(transTo = "recruitTypeText")
//    @ApiModelProperty(value = "招募类型")
//    private String recruitType;
//
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "招募类型")
//    private String recruitTypeText;

    /**
     * 招募对象
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "招募对象")
    private String recruitTargets;

    /*    *//**
     * 招募对象
     *//*
    @TableField(exist = false)
    @ApiModelProperty(value = "招募对象")
    private String recruitTargetsText;*/

    /**
     * 招募人数是否区别控制
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "招募人数是否区别控制")
    private Boolean recruitNumDistinguish;

    /**
     * 所属领域一级
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "所属领域一级")
    private String belongFieldTop;

    /**
     * 所属领域名称一级
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "所属领域名称一级")
    private String belongFieldNameTop;

    /**
     * 所属领域二级
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "所属领域二级")
    private String belongFieldEnd;

    /**
     * 所属领域二级名称
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "所属领域二级名称")
    private String belongFieldNameEnd;

    /**
     * 提交时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_second")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "提交时间")
    private String submitTime;

    /**
     * 开始时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_second")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_second")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 图片
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "图片")
    private String picture;

    /**
     * 审核状态（数据字典）
     */
    @Field(type = FieldType.Text)
/*
    @DictTrans(transTo = "auditStatusText")
*/
    @ApiModelProperty(value = "审核状态（数据字典）")
    private String auditStatus;
/*
    @TableField(exist = false)
    @ApiModelProperty(value = "审核状态")
    private String auditStatusText;*/

/*    @TableField(exist = false)
    @ApiModelProperty(value = "审核备注Text")
    private String auditRemark;*/

    /**
     * 审核的组织机构
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "审核的组织机构")
    private String auditOrgCode;

/*    @TableField(exist = false)
    @ApiModelProperty(value = "当前审核组织机构")
    private String auditOrgCodeName;*/

    /**
     * 报名是否需要审核
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名是否需要审核")
    private Boolean applyCheck;

    /**
     * 活动内容
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "活动内容")
    private String actContents;

    /**
     * 报名开始时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_second")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报名开始时间")
    private String applyStartTime;

    /**
     * 报名结束时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_second")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报名结束时间")
    private String applyEndTime;

    /**
     * 报名条件
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "报名条件")
    private String applicationRequirements;

    /**
     * 发布组织code
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "发布组织code")
    private String publishOrgCode;

    /**
     * 发布组织
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "发布组织")
    private String publishOrgName;

    /**
     * 发布团队id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "发布团队id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    /**
     * 是否团队发布
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否团队发布")
    private Boolean teamPublish;

    /**
     * 上下架
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "上下架")
    private Boolean webShow;

    /**
     * 是否公开活动
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否公开活动")
    private Boolean open;


    /**
     * 是否审核通过后前端立即展示
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否审核通过后前端立即展示")
    private Boolean auditShow;

    /**
     * 是否签到定位
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否签到定位")
    private Boolean checkLocation;

    /**
     * 签到距离范围单位m
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "签到距离范围单位m")
    private Integer locationDistance;

    /**
     * 活动是否补录
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "活动是否补录")
    private Boolean addRecord;

    /**
     * 活动简介
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "活动简介")
    private String actSynopsis;

    /**
     * 对接类型
     */
    @Field(type = FieldType.Text)
/*
    @DictTrans(transTo = "dockingTypeText")
*/
    @ApiModelProperty(value = "对接类型")
    private String dockingType;
/*
    @TableField(exist = false)
    @ApiModelProperty(value = "对接类型")
    private String dockingTypeText;*/


/*    @TableField(exist = false)
    @DictTrans(transTo = "activityStatusText")
    @ApiModelProperty(value = "活动当前状态")
    private String activityStatus;*/

/*    @TableField(exist = false)
    @ApiModelProperty(value = "活动当前状态Text")
    private String activityStatusText;*/

    /**
     * 资源id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "资源预约的id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long resId;

    @ApiModelProperty(value = "资源名称")
    @TableField(exist = false)
    private String resName;

    /**
     * 需求id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "需求id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long reqId;

/*    @ApiModelProperty(value = "需求名称")
    @TableField(exist = false)
    private String reqName;*/

    /**
     * 微信分享消息
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "微信分享消息")
    private String shareDesc;

    /**
     * 微信二维码
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "微信二维码")
    private String qrCodeUrl;

    /**
     * 活动报名链接
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "活动报名链接")
    private String applyLinkUrl;

    /**
     * 同步状态
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步状态")
/*
    @DictTrans(transTo = "syncText")
*/
    private String sync;

    /*    *//**
     * 同步状态Text
     *//*
    @TableField(exist = false)
    @ApiModelProperty(value = "同步状态Text")
    private String syncText;*/

    /**
     * 同步时间
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String syncTime;

    /**
     * 同步备注
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 同步成功后市区返回的Id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步成功后市区返回的Id")
    private String syncId;
    /**
     * 同步成功后市区返回的招募Id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步成功后市区返回的招募Id")
    private String recruitSyncId;
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步状态")
/*
    @DictTrans(transTo = "syncText")
*/
    private String recruitSync;

    /*    *//**
     * 同步状态Text
     *//*
    @TableField(exist = false)
    @ApiModelProperty(value = "同步状态Text")
    private String syncText;*/

    /**
     * 同步时间
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String recruitSyncTime;

    /**
     * 同步备注
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "同步备注")
    private String recruitSyncRemark;
    /**
     * 是否为组织活动
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "是否为组织活动")
    private Boolean orgSelf;

    /*    *//**
     * 是否能同步
     *//*
    @TableField(exist = false)
    @ApiModelProperty(value = "是否能同步")
    private Boolean enableSync;*/

    /**
     * 对接资源是否新创建
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "对接资源是否新创建")
    private Boolean dockingResCreate;

    /**
     * 对接需求是否新创建
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "对接需求是否新创建")
    private Boolean dockingReqCreate;

    /**
     * 项目id
     */
    @Field(type = FieldType.Text)
    @ApiModelProperty(value = "项目id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long projectId;
}
