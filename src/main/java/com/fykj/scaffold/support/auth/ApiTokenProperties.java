package com.fykj.scaffold.support.auth;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * API Token配置属性
 * 用于映射yml中的token配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "api.token")
public class ApiTokenProperties {
    
    /**
     * 是否启用token验证
     */
    private boolean enabled = true;
    
    /**
     * token请求头名称
     */
    private String headerName = "X-API-TOKEN";
    
    /**
     * token映射，key为token值(uuid)，value为名称
     */
    private Map<String, String> keys = new HashMap<>();
} 