package com.fykj.scaffold.sgb_docking.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 苏州志愿者平台部门单位
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("苏州志愿者平台部门单位")
@TableName("sgb_dept")
public class SgbDept extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门单位编号
     */
    @TableField("dept_id")
    @ApiModelProperty("部门单位编号")
    private String deptId;

    /**
     * 部门单位名称
     */
    @TableField("dept_name")
    @ApiModelProperty("部门单位名称")
    private String deptName;

    /**
     * 所属区划编号
     */
    @TableField("region_code")
    @ApiModelProperty("所属区划编号")
    private String regionCode;
    
} 