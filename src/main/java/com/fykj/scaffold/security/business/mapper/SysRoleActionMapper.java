package com.fykj.scaffold.security.business.mapper;

import com.fykj.scaffold.security.business.domain.entity.SysRoleAction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;

/**
 * 系统权限-角色关联
 * <p>
 * Mapper 接口
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
public interface SysRoleActionMapper extends BaseMapper<SysRoleAction> {
    @Delete("delete from sys_role_action where role_id = #{roleId}")
    void deleteByRoleId(long roleId);
}
