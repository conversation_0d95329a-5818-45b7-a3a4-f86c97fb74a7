package com.fykj.scaffold.race.domain.dto;

import com.alibaba.fastjson.TypeReference;
import com.fykj.scaffold.support.utils.ListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 照片路径情况详情
 *
 * <AUTHOR> @email ${email}
 * @date 2022-10-08
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("照片路径")
public class PictureDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;


    @ApiModelProperty(value = "原图路径")
    private String path;

    @ApiModelProperty(value = "压缩后路径")
    private String slimPath;

    /**
     * json处理器
     */
    public static class pictureListTypeHandler extends ListTypeHandler<PictureDto> {

        @Override
        protected TypeReference<List<PictureDto>> specificType() {
            return new TypeReference<List<PictureDto>>() {
            };
        }

    }


}
