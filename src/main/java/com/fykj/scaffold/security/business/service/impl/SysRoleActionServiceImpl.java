package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.domain.entity.SysRoleAction;
import com.fykj.scaffold.security.business.mapper.SysRoleActionMapper;
import com.fykj.scaffold.security.business.service.ISysApiRoleService;
import com.fykj.scaffold.security.business.service.ISysRoleActionService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * 系统权限-角色关联
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysRoleActionServiceImpl extends BaseServiceImpl<SysRoleActionMapper, SysRoleAction> implements ISysRoleActionService {

    @Override
    public void saveRoleActionRelationship(long roleId, List<Long> actionIds) {
        baseMapper.deleteByRoleId(roleId);
        List<SysRoleAction> list = new ArrayList<>();
        actionIds.forEach(id -> {
            SysRoleAction bean = new SysRoleAction();
            bean.setActionId(id);
            bean.setRoleId(roleId);
            list.add(bean);
        });
        saveBatch(list);
    }
}
