<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.portal_website.mapper.BannerMapper">

    <select id="pageForBanner" resultType="com.fykj.scaffold.portal_website.domain.entity.Banner">
        SELECT
            cb.id AS id,
            cb.title AS title,
            cb.image_url AS imageUrl,
            ccc.category_list AS category,
            cb.grounding AS grounding,
            cb.link AS link,
            cb.start_time AS startTime,
            cb.end_time AS endTime,
            cb.sequence AS sequence
        FROM cms_banner cb
        LEFT JOIN cms_category_content ccc on cb.id = ccc.content_id AND ccc.is_deleted = 0 AND ccc.is_banner = 1
        WHERE cb.is_deleted = 0
        <if test="params.title != null and params.title != ''">
            AND cb.title LIKE CONCAT('%', CONCAT(#{params.title}, '%'))
        </if>
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        <if test="params.grounding != null">
            AND cb.grounding = #{params.grounding}
        </if>
        <if test="params.enabled != null">
            AND cb.enabled = #{params.enabled}
        </if>
        ORDER BY cb.sequence ASC, cb.create_date
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.portal_website.domain.entity.Banner">
        SELECT
            cb.id AS id,
            cb.title AS title,
            ccc.category_list AS category,
            cb.grounding AS grounding,
            cb.link AS link,
            cb.start_time AS startTime,
            cb.end_time AS endTime,
            cb.sequence AS sequence,
            cb.image_url as imageUrl
        FROM cms_banner cb
                 LEFT JOIN cms_category_content ccc on cb.id = ccc.content_id AND ccc.is_deleted = 0 AND ccc.is_banner = 1
        WHERE cb.is_deleted = 0 AND cb.grounding =1 and cb.start_time &lt;= CURDATE() and cb.end_time &gt;= CURDATE()
        <if test="categoryId != null and categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{categoryId}, '%'))
        </if>
        ORDER BY cb.sequence ASC, cb.create_date
    </select>
</mapper>
