package com.fykj.scaffold.security.business.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 审核模型
 *
 * <AUTHOR>
 * @date: 2022/2/24 9:30
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("接口绑定选择模型")
public class ApiListDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;

    @ApiModelProperty(value = "key")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long key;

    @ApiModelProperty(value = "名称")
    private String label;

    @ApiModelProperty(value = "是否禁用")
    private Boolean disabled;

}
