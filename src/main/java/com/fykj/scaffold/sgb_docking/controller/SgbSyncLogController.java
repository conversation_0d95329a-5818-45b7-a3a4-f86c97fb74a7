package com.fykj.scaffold.sgb_docking.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncLog;
import com.fykj.scaffold.sgb_docking.domain.params.SgbSyncLogParams;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncLogService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 市平台信息同步日志-接口控制器
 *
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/admin/sgb/sync-log")
@Api(tags = "市平台信息同步日志-管理接口")
public class SgbSyncLogController
        extends BaseController<ISgbSyncLogService, SgbSyncLog, SgbSyncLogParams> {

    @Override
    @ApiOperation("获取同步日志--分页查询")
    @PostMapping("/pages")
    public JsonResult<IPage<SgbSyncLog>> list(@RequestBody SgbSyncLogParams params) {
        IPage<SgbSyncLog> iPage = baseService.page(params);
        DictTransUtil.trans(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

}
