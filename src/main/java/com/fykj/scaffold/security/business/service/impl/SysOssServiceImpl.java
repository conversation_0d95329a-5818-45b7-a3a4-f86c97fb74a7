package com.fykj.scaffold.security.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.dto.SysOssDto;
import com.fykj.scaffold.security.business.domain.entity.OssLabel;
import com.fykj.scaffold.security.business.domain.entity.SysOss;
import com.fykj.scaffold.security.business.domain.params.OssParams;
import com.fykj.scaffold.security.business.mapper.SysOssMapper;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.IOssLabelService;
import com.fykj.scaffold.security.business.service.ISysOssService;
import com.fykj.scaffold.support.oss.FilePathDto;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import com.fykj.scaffold.support.utils.ImageUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.apache.pdfbox.io.RandomAccessBufferedFileInputStream;
import org.apache.pdfbox.io.RandomAccessRead;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
/**
 * 文件上传记录服务实现
 *
 * <AUTHOR>
 * @date 2019-03-04
 */
@Service
public class SysOssServiceImpl extends BaseServiceImpl<SysOssMapper, SysOss> implements ISysOssService {

    @Autowired
    private Environment environment;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IOssLabelService ossLabelService;

    @Override
    public SysOss upload(MultipartFile file, String serverCode, boolean media, String name, String[] labels) {
        SysOss oss = new SysOss();
        String filename = file.getOriginalFilename();
        String fileExt = ImageUtil.getExt(filename);
        List<String> extList = dictService.getOssExtList();
        if (!extList.contains(fileExt.toLowerCase())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "不支持的文件格式");
        }
        if ("pdf".equals(fileExt.toLowerCase())) {
            File tempFile = null;
            FileOutputStream fos = null;
            try {
                // 获取文件内容
                byte[] fileContent = file.getBytes();
                // 创建临时文件
                tempFile = File.createTempFile("temp", null);
                // 创建文件输出流
                fos = new FileOutputStream(tempFile);
                // 将文件内容写入临时文件
                fos.write(fileContent);
                if (containsJavaScript(tempFile)) {
                    throw new BusinessException(ResultCode.BAD_REQUEST, "文件内容包含不合法内容");
                }
                if (!tempFile.delete()) {
                    log.error("文件删除失败");
                }

            } catch (Exception e) {
                log.error("文件转换：" + e.getMessage());
                throw new BusinessException(ResultCode.BAD_REQUEST, "文件内容包含不合法内容");
            }
            finally {

                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException e) {
                        log.error("文件关闭流失败：" + e.getMessage());
                    }
                }
            }
        }
        oss.setType(dictService.findFileTypeByValue(fileExt));
        oss.setFileName(filename);
        oss.setName(name);
        oss.setFileExt(fileExt);
        oss.setFileSize(file.getSize());
        oss.setPath(getPath(file, serverCode).getUrl());
        oss.setServerCode(serverCode);
        oss.setMedia(media);
        save(oss);
        if (labels !=null) {
            for (String label : labels) {
                OssLabel ossLabel = new OssLabel();
                ossLabel.setOssId(oss.getId());
                ossLabel.setLabelId(Long.valueOf(label));
                ossLabelService.save(ossLabel);
            }
        }
        return oss;
    }

    /**
     * 获取文件访问路径
     *
     * @param file       上传文件兑现
     * @param serverCode 存储服务器编码
     * @param fileExt    文件扩展名
     * @return 文件访问路径
     */

    private boolean containsJavaScript(File file) throws IOException {

        RandomAccessRead is = new RandomAccessBufferedFileInputStream(file);
        try {
            PDFParser parser = new PDFParser( is);
            parser.parse();
            PDDocument doc = parser.getPDDocument();
            String CosName = doc.getDocument().getTrailer().toString();
            if (CosName.contains("COSName{JavaScript}") || CosName.contains("COSName{JS}")) {
                return true;
            }
        } catch (Exception e) {
            log.error("PDF效验异常：" + e.getMessage());
            return true;
        } finally {
            is.close();
        }
        return false;
    }
    @Override
    public IPage<SysOss> page(BaseParams params) {
        return super.page(params).convert(this::convert);
    }

    @Override
    public SysOss getById(Serializable id) {
        return convert(super.getById(id));
    }

    private SysOss convert(SysOss sysOss) {
        sysOss.setTypeText(dictService.getNameByCode(sysOss.getType()));
        sysOss.setServerText(dictService.getNameByCode(sysOss.getServerCode()));
        List<Long> labels = ossLabelService.getByOssId(sysOss.getId());
        sysOss.setLabels(labels.toArray(new String[0]));
        return sysOss;
    }

    /**
     * 获取文件访问路径
     *
     * @param file       上传文件兑现
     * @param serverCode 存储服务器编码
     * @return 文件访问路径
     */
    private FilePathDto getPath(MultipartFile file, String serverCode) {
        return OssSaveUtil.save(file, serverCode);
    }


    @Override
    public SysOss getFileUpload(String path) {
        SysOss upload = lambdaQuery().eq(SysOss::getPath, path).one();
        if (upload == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "未找到对应的文件");
        }
        return upload;
    }

    @Override
    public IPage<SysOssDto> getListWithQuery(IPage<SysOssDto> page, OssParams ossParams) {
        return baseMapper.getListWithQuery(page,ossParams);
    }

    /**
     * 根据名称获取最新的地址
     *
     * @param name
     * @return
     */
    @Override
    public String getNewestPdfPath(String name) {
        SysOss sysOss = lambdaQuery().eq(SysOss::getName,name).
                eq(SysOss::getFileExt,"pdf").
                orderByDesc(SysOss::getCreateDate).one();
        if(ObjectUtils.isEmpty(sysOss)){
            return null;
        }
        if(OssCons.OSS_LOCAL.equals(sysOss.getServerCode())){
            //配置文件里配置域名
            return environment.getProperty("system.domain")+sysOss.getPath();
        }
        return sysOss.getPath();
    }
}
