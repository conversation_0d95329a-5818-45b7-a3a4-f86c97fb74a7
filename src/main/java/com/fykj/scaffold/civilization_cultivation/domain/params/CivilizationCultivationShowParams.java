package com.fykj.scaffold.civilization_cultivation.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 文明培育展示-管理
 * 查询参数
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明培育展示-管理查询参数")
public class CivilizationCultivationShowParams extends BaseParams {

    private static final long serialVersionUID = -9000862728811406541L;

    @ApiModelProperty("文明培育类型")
    @MatchType(value = QueryType.EQ, fieldName = "type")
    private String type;

    @ApiModelProperty("年份")
    @MatchType(value = QueryType.EQ, fieldName = "year")
    private Integer year;

    @ApiModelProperty("联系电话")
    @MatchType(value = QueryType.LIKE, fieldName = "phone")
    private String phone;

    @ApiModelProperty("所属街道社区")
    @MatchType(value = QueryType.EQ, fieldName = "belong_community_code")
    private String orgCode;

    @ApiModelProperty("状态")
    @MatchType(value = QueryType.EQ, fieldName = "on_shelve")
    private Boolean onShelve;
}
