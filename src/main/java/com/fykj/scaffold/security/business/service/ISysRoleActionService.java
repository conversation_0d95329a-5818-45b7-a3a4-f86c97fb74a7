package com.fykj.scaffold.security.business.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.security.business.domain.entity.SysRoleAction;

import java.util.List;

/**
 * 系统权限-角色关联
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
public interface ISysRoleActionService extends IBaseService<SysRoleAction> {

    /**
     * 保存角色和按钮关系
     *
     * @param roleId
     * @param actionIds
     */
    void saveRoleActionRelationship(long roleId, List<Long> actionIds);
}

