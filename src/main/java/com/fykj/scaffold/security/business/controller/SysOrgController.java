package com.fykj.scaffold.security.business.controller;

import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.security.business.domain.dto.SysOrgDto;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.domain.params.SysOrgParams;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2022-10-12
 */
@RestController
@RequestMapping("/admin/org")
public class SysOrgController extends BaseController<ISysOrgService, SysOrg, SysOrgParams> {
    @Override
    public Result save(@RequestBody SysOrg entity) {
        baseService.saveOrg(entity);
        return new JsonResult<>(entity);
    }

    @Override
    public Result update(@RequestBody SysOrg entity) {
        baseService.updateOrg(entity);
        return new JsonResult<>(entity);
    }

    @GetMapping("/getOrgPages")
    @ApiOperation("获取orgPages")
    public Result getOrgPages() {
        return new JsonResult<>(DictTransUtil.trans(baseService.lambdaQuery().orderByAsc(SysOrg::getSequence).list()));
    }

    @GetMapping("/getOrg")
    @ApiOperation("获取org")
    public Result getOrg(long orgId) {
        return new JsonResult<>(baseService.getOrgById(orgId));
    }

    @GetMapping("/getOrgTree")
    @ApiOperation("获取级联树，根据当前登录人身份，展示其及其下属的组织")
    public JsonResult<List<SysOrg>> getOrgTree() {
        String myTopDept;
        //协会和超管看全部
        if (Oauth2Util.isAdmin() || Cons.RoleCode.ROLE_CODE_ASSOCIATION_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            myTopDept = Cons.TOP_DEPT_CODE;
        } else {
            myTopDept = Oauth2Util.getOrgCode();
        }
        List<SysOrg> orgList = new ArrayList<>();
        if (StringUtil.isNotEmpty(myTopDept)) {
            orgList = baseService.getOrgTree(myTopDept, true);
        }
        return new JsonResult<>(orgList);
    }

    @GetMapping("/getOrgTreeOnlyContainFiveSubAssociation")
    @ApiOperation("获取级联树，根据当前登录人身份，展示其及其下属的组织(街道/社区）")
    public JsonResult<List<SysOrg>> getOrgTreeOnlyContainFiveSubAssociation() {
        String myTopDept;
        //协会和超管看全部
        if (Oauth2Util.isAdmin() || Cons.RoleCode.ROLE_CODE_ASSOCIATION_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            myTopDept = Cons.TOP_DEPT_CODE;
        } else {
            myTopDept = Oauth2Util.getOrgCode();
        }
        List<SysOrg> orgList = new ArrayList<>();
        if (StringUtil.isNotEmpty(myTopDept)) {
            orgList = baseService.getOrgTreeOnlyContainFiveSubAssociation(myTopDept, true);
        }
        return new JsonResult<>(orgList);
    }

    @GetMapping("/getOrgTreeOfAll")
    @ApiOperation("获取级联树-从根节点开始，展示全部的")
    public JsonResult<List<SysOrgDto>> getOrgTreeOfAll() {
        List<SysOrg> list = baseService.getOrgTree(Cons.TOP_DEPT_CODE, true);
        //把数据类型转成SysOrgDto
        List<SysOrgDto> orgDtoList = convertToDtoList(list);
        return new JsonResult<>(orgDtoList);
    }

    private SysOrgDto convertToDto(SysOrg org) {
        if (org == null) {
            return null;
        }

        SysOrgDto dto = new SysOrgDto();
        dto.setId(org.getId());
        dto.setCode(org.getCode());
        dto.setName(org.getName());
        dto.setLevel(org.getLevel());
        dto.setParentId(org.getParentId());

        if (org.getChildren() != null && !org.getChildren().isEmpty()) {
            List<SysOrgDto> childrenDto = new ArrayList<>();
            for (SysOrg child : org.getChildren()) {
                childrenDto.add(convertToDto(child)); // 递归调用
            }
            dto.setChildren(childrenDto);
        }

        return dto;
    }

    private List<SysOrgDto> convertToDtoList(List<SysOrg> orgList) {
        if (orgList == null || orgList.isEmpty()) {
            return new ArrayList<>();
        }

        List<SysOrgDto> dtoList = new ArrayList<>();
        for (SysOrg org : orgList) {
            dtoList.add(convertToDto(org));
        }
        return dtoList;
    }

    @GetMapping("/getFiveAreaSubAssociationOrg")
    @ApiOperation("获取5大街道")
    public JsonResult<List<SysOrg>> getFiveAreaSubAssociationOrg(@RequestParam Boolean withTop, @RequestParam(required = false) Boolean all) {
        if (withTop == null) {
            withTop = false;
        }
        if (ObjectUtil.isEmpty(all)) {
            all = true;
        }
        return new JsonResult<>(baseService.getAreaSubAssociation(withTop, all));
    }

    @GetMapping("/getCommunityBySACode")
    @ApiOperation("通过分协会code获取社区列表")
    public JsonResult<List<SysOrg>> getCommunityBySACode() {
        String capacity = Oauth2Util.getManagerCapacity();
        if (StringUtil.isEmpty(capacity) || !Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            throw new BusinessException(ResultCode.FAIL, "您不是分协会管理员，无法获取社区列表！");
        }
        String orgCode = Oauth2Util.getOrgCode();
        if (StringUtil.isEmpty(orgCode)) {
            throw new BusinessException(ResultCode.FAIL, "无法获取您当前的分协会组织！");
        }
        return new JsonResult<>(baseService.getSubOrg(orgCode, false));
    }

    @GetMapping("/getOrgTreeForSubAssociation")
    @ApiOperation("获取分协会管理员下的级联树")
    public JsonResult<List<SysOrg>> getOrgTreeForSubAssociation() {
        String capacity = Oauth2Util.getManagerCapacity();
        if (StringUtil.isEmpty(capacity) || !Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(capacity)) {
            return new JsonResult<>(null);
        }
        String myTopDept = Oauth2Util.getOrgCode();
        List<SysOrg> orgList = new ArrayList<>();
        if (StringUtil.isNotEmpty(myTopDept)) {
            orgList = baseService.getOrgTree(myTopDept, true);
        }
        return new JsonResult<>(orgList);
    }
}
