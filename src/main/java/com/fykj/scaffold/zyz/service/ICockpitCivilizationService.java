package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.security.business.domain.dto.OrgMapDto;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.zyz.domain.dto.HomePagePracticeRankDto;
import com.fykj.scaffold.zyz.domain.dto.SpiritCockpitDto;
import com.fykj.scaffold.zyz.domain.dto.SpiritMonActDto;
import com.fykj.scaffold.zyz.domain.dto.SpiritOrgDetailDto;

import java.util.List;

/**
 * 驾驶舱
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-14
 */
public interface ICockpitCivilizationService {


    /**
     * 精神文明概览
     *
     * @return
     */
    SpiritCockpitDto spiritOverview();


    /**
     * 近6个月实践活动情况
     *
     * @return
     */
    List<SpiritMonActDto> sixMonthActOverview();


    /**
     * 组织列表
     * @return
     */
    List<OrgMapDto> orgList();


    /**
     * 组织详情
     * @param orgCode
     * @return
     */
    SpiritOrgDetailDto orgDetail(String orgCode);


    /**
     * 阵地活跃情况
     * @return
     */
    List<HomePagePracticeRankDto> rank();
}

