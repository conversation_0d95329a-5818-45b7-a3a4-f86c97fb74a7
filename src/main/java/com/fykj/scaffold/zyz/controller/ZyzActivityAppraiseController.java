package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzActivityAppraise;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityAppraiseParams;
import com.fykj.scaffold.zyz.service.IZyzActivityAppraiseService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动-评价内容
 *
 * 前端控制器
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-07
 */
@RestController
@RequestMapping("/zyzactivityappraise")
@Api(tags = "活动--评价接口")
public class ZyzActivityAppraiseController extends BaseController<IZyzActivityAppraiseService, ZyzActivityAppraise,ZyzActivityAppraiseParams> {

}
