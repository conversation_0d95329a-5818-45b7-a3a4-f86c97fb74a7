package com.fykj.volunteer.sync.util.domain.sync_act_member;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <actMember>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>加入招募失败：招募id不存在</msg>
 *     <uniCode>102327</uniCode>
 *   </info>
 * </actMember>
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActMemberResult {
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "actMember")
    public static class ActMember {
        private List<SyncActMemberResult> info;
    }
}
