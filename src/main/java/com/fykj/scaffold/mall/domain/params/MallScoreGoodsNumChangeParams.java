package com.fykj.scaffold.mall.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MallScoreGoodsNumChangeParams extends BaseParams {

    private Long goodsId;

    /**
     * 变动方向
     */
    @ApiModelProperty(value = "变动方向")
    private String changeType;
}
