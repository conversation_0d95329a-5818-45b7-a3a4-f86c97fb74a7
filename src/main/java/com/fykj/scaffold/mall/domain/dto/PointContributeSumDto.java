package com.fykj.scaffold.mall.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author：yangxu
 * @Date：2025/6/20 10:48
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointContributeSumDto implements Serializable {

    private static final long serialVersionUID = 9061305045998391402L;

    @ApiModelProperty(value = "捐赠人数")
    private int personNum;

    @ApiModelProperty(value = "捐赠人次")
    private int contributeSum;

    @ApiModelProperty(value = "捐赠积分总数")
    private BigDecimal contributePointSum = BigDecimal.ZERO;
}
