package com.fykj.scaffold.sgb_docking.service.impl;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncLog;
import com.fykj.scaffold.sgb_docking.mapper.SgbSyncLogMapper;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncLogService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class SgbSyncLogServiceImpl extends BaseServiceImpl<SgbSyncLogMapper, SgbSyncLog>
        implements ISgbSyncLogService {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public SgbSyncLog buildSyncLog(String bizType, String resultCode, String msg,
                                   long syncBillId, long bizId, String objName,
                                   String reqParam, String resp) {
        SgbSyncLog syncLog = new SgbSyncLog();
        syncLog.setResultCode(resultCode);
        syncLog.setMessage(msg);
        syncLog.setBizType(bizType);
        syncLog.setBizId(bizId);
        syncLog.setSyncObjectName(objName);
        syncLog.setSyncTime(LocalDateTime.now());
        syncLog.setSyncBillId(syncBillId);
        syncLog.setRequestParam(reqParam);
        syncLog.setResponse(resp);
        save(syncLog);
        return syncLog;
    }

}
