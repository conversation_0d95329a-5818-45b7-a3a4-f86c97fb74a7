package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.zyz.domain.dto.ProjectDockingExportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectCompany;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectDocking;
import fykj.microservice.core.base.IBaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 公益伙伴计划-项目对接企业中间表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
public interface IZyzProjectDockingService extends IBaseService<ZyzProjectDocking> {

    List<ZyzProjectDocking> getListByProjectId(Long projectId);

    /**
     * 根据id列表查询对接信息
     *
     * @param idList 参数
     * @return 结果
     */
    List<ProjectDockingExportDto> findByProjectIds(List<String> idList);

    /**
     * 获取年度对接企业列表
     *
     * @param year 年份
     * @return 列表
     */
    List<ZyzProjectCompany> findDockingCompanyListByYear(Integer year);

    /**
     * 根据对接需求类型统计对接金额
     *
     * @param demandType 对接需求类型
     * @return
     */
    BigDecimal sumDockingAmountByDemandType(String demandType);
}

