package com.fykj.scaffold.sync.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：yangxu
 * @Date：2025/5/21 15:34
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncSpotImportDto implements Serializable {

    private static final long serialVersionUID = -490620516354601525L;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("负责人姓名")
    private String contact;

    @ExcelProperty("负责人电话")
    private String contactNo;

    @ExcelProperty("描述")
    private String intro;

    @ExcelProperty("详细地址")
    private String mapAddress;

    @ExcelProperty("导入失败原因")
    private String failReason;
}
