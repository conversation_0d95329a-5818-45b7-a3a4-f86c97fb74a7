package com.fykj.scaffold.onenet;

import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2020/3/10 13:25
 */
@Slf4j
public class OneNetSHA256Util {
    public static void main(String[] args) {
        String secret = "Nu4WmXGBauU99NsNuwXjbV7hO2xwEef91Rp9rKX6WatcmiwWqXyk";
        String headerTimestamp = "1523340394";
        String headerSeq = "ab570e0a:015a1d0fbef0:00cb3d";
        String headerSign = "B6CD9D9377EDB58CA445B4A6A753F884C156DBD6344F058E7B88658E9AF54F71";
        String sign = getSHA256(headerTimestamp + secret + headerSeq + headerTimestamp);
        System.out.println(sign);
        System.out.print(sign.equals(headerSign));

    }

    public static String getSHA256(String encode) {
        log.info("------------encode:" + encode + "------------");
        MessageDigest messageDigest;
        String sha = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(encode.getBytes(StandardCharsets.UTF_8));
            sha = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            throw new BusinessException(ResultCode.FAIL, "生成签名失败");
        }
        return sha.toUpperCase();
    }

    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }
}
