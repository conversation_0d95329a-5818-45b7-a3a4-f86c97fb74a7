package com.fykj.scaffold.zyz.xxl_job;

import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.business.domain.dto.ZsqUserSyncDto;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;


/**
 * 知社区用户定时同步
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ZSQUserSyncTask {

    @Autowired
    private ISsoUserMappingService userMappingService;

    @XxlJob("ZSQ_User_Sync_Handler")
    public void zsqUserSyncJobHandler() {
        XxlJobHelper.log("ZSQ_User_Sync_Handler-Job, START!");
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        List<ZsqUserSyncDto> syncRecords = userMappingService.syncZsqUser(yesterday.toString(), today.toString());
        log.info("同步数据情况如下：");
        syncRecords.forEach(it -> {
            log.info(JSONObject.toJSONString(it));
        });
        XxlJobHelper.log("ZSQ_User_Sync_Handler-Job, END!");
    }
}
