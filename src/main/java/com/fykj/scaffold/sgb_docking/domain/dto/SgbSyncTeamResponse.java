package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 苏州社工部组织同步响应
 */
@Data
public class SgbSyncTeamResponse {
    
    /**
     * 响应信息列表
     */
    private List<TeamResult> info;
    
    /**
     * 组织推送结果
     */
    @Data
    public static class TeamResult {

        /**
         * 响应状态码 - SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 组织名称
         */
        private String orgName;

        /**
         * 组织id - SUCCESS时返回，组织在本平台生成的唯一标识
         */
        private String orgId;

        /**
         * 第三方平台唯一键
         */
        private String uniqueCode;
    }
}
