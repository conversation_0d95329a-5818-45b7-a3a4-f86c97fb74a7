package com.fykj.scaffold.civilization_cultivation.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationImportDto;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationShowExportDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationShowParams;
import com.fykj.scaffold.civilization_cultivation.mapper.CivilizationCultivationShowMapper;
import com.fykj.scaffold.civilization_cultivation.service.ICivilizationCultivationShowService;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.util.SystemUtil;
import joptsimple.internal.Strings;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.fykj.scaffold.civilization_cultivation.cons.CivilizationCultivationCons.*;


/**
 * 文明培育展示-管理
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CivilizationCultivationShowServiceImpl extends BaseServiceImpl<CivilizationCultivationShowMapper, CivilizationCultivationShow> implements ICivilizationCultivationShowService {

    @Autowired
    private ISysOrgService orgService;

    @Override
    public List<Integer> getYears(String type) {
        return baseMapper.getYears(type);
    }

    @Override
    public IPage<CivilizationCultivationShow> pageShow(CivilizationCultivationShowParams params) {
        return baseMapper.pageShow(params.getPage(), params);
    }

    @Override
    public IPage<CivilizationCultivationShow> pageShowProd(CivilizationCultivationShowParams params) {
        return baseMapper.pageShowProd(params.getPage());
    }

    @Override
    public CivilizationCultivationShow getDetail(Long id) {
        return super.getById(id);
    }

    @Override
    public void showInfoSaveOrUpdate(CivilizationCultivationShow showInfo) {
        setBelongOrgInfo(showInfo);
        super.saveOrUpdate(showInfo);
    }

    private void setBelongOrgInfo(CivilizationCultivationShow entity) {
        String belongCommunityCode = entity.getBelongCommunityCode();
        if (StringUtil.isEmpty(belongCommunityCode)) {
            entity.setBelongCommunityCode(null);
            entity.setBelongCommunityName(null);
            entity.setBelongStreetCode(null);
            entity.setBelongStreetName(null);
            return;
        }
        SysOrg community = orgService.getByCode(belongCommunityCode);
        if (community == null) {
            entity.setBelongCommunityCode(null);
            entity.setBelongCommunityName(null);
            entity.setBelongStreetCode(null);
            entity.setBelongStreetName(null);
            return;
        }
        entity.setBelongCommunityName(community.getName());
        Long streetId = community.getParentId();
        if (streetId == null) {
            entity.setBelongStreetCode(null);
            entity.setBelongStreetName(null);
            return;
        }
        SysOrg street = orgService.lambdaQuery().eq(SysOrg::getId, streetId).one();
        if (street == null) {
            entity.setBelongStreetCode(null);
            entity.setBelongStreetName(null);
            return;
        }
        entity.setBelongStreetCode(street.getCode());
        entity.setBelongStreetName(street.getName());
    }

    @Override
    public IPage<CivilizationCultivationShow> page(BaseParams params) {
        IPage<CivilizationCultivationShow> result = super.page(params);
        DictTransUtil.trans(result.getRecords());
        return result;
    }

    @Override
    public void onShelveChange(Long id) {
        CivilizationCultivationShow showInfo = super.getById(id);
        if (showInfo == null) {
            throw new BusinessException(ResultCode.FAIL, "文明培育展示数据不存在！");
        }
        showInfo.setOnShelve(!showInfo.getOnShelve());
        super.updateById(showInfo);
    }

    @Override
    public List<CivilizationCultivationImportDto> dataImport(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请上传文件！");
        }
        List<CivilizationCultivationImportDto> dataList = ExcelUtil.readExcel(excel, CivilizationCultivationImportDto.class, 0, 1);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        List<CivilizationCultivationImportDto> failList = new ArrayList<>();
        List<CivilizationCultivationShow> needSave = new ArrayList<>();
        dataList.forEach(it -> {
            CivilizationCultivationShow showInfo = new CivilizationCultivationShow();
            BeanUtils.copyProperties(it, showInfo);
            String type = getTypeCode(it.getType());
            if (StringUtil.isEmpty(type)) {
                it.setFailMsg("类型不存在！");
                failList.add(it);
                return;
            }
            showInfo.setType(type);
            String communityCode = getCommunityCode(it.getCommunity());
            showInfo.setBelongCommunityCode(communityCode);
            setBelongOrgInfo(showInfo);
            String outLinkAddress = it.getOutLinkAddress();
            if (StringUtil.isNotEmpty(outLinkAddress)) {
                showInfo.setOutLink(Boolean.TRUE);
            }
            needSave.add(showInfo);
        });
        if (!needSave.isEmpty()) {
            super.saveBatch(needSave);
        }
        return failList;
    }

    private String getTypeCode(String typeName) {
        if (StringUtil.isEmpty(typeName)) {
            return null;
        }
        switch (typeName) {
            case "道德模范":
                return CC_TYPE_DDMF;
            case "时代新人":
                return CC_TYPE_SDXR;
            case "先进典型":
                return CC_TYPE_XJDX;
            case "园区好人":
                return CC_TYPE_YQHR;
            default:
                return null;
         }
    }

    private String getCommunityCode(String communityName) {
        if (StringUtil.isEmpty(communityName)) {
            return null;
        }
        String[] streetCommunity = communityName.split("--");
        if (streetCommunity.length != 2) {
            return null;
        }
        communityName = streetCommunity[1];
        SysOrg community = orgService.lambdaQuery().eq(SysOrg::getName, communityName).one();
        if (community == null) {
            return null;
        }
        return community.getCode();
    }

    @Override
    public List<CivilizationCultivationShowExportDto> export(BaseParams params) {
        List<CivilizationCultivationShow> records = super.list(params);
        if (records == null || records.isEmpty()) {
            return null;
        }
        DictTransUtil.trans(records);
        List<CivilizationCultivationShowExportDto> result = new ArrayList<>();
        records.forEach(it -> {
            CivilizationCultivationShowExportDto dto = new CivilizationCultivationShowExportDto();
            BeanUtils.copyProperties(it, dto);
            String belongStreetName = it.getBelongStreetName();
            String belongCommunityName = it.getBelongCommunityName();
            if (StringUtils.isNotEmpty(belongStreetName) && StringUtils.isNotEmpty(belongCommunityName)) {
                dto.setBelongStreetCommunity(belongStreetName + "--" + belongCommunityName);
            } else if (StringUtils.isNotEmpty(belongStreetName)) {
                dto.setBelongStreetCommunity(belongStreetName);
            } else if (StringUtils.isNotEmpty(belongCommunityName)) {
                dto.setBelongStreetCommunity(belongCommunityName);
            } else {
                dto.setBelongStreetCommunity(Strings.EMPTY);
            }
            String picture = it.getPicture();
            if (StringUtils.isNotEmpty(picture)) {
                dto.setPicture(SystemUtil.getProperty("httpAttachmentUrl") + picture);
            }
            result.add(dto);
        });
        return result;
    }

    @Override
    public List<CivilizationCultivationShow> homeBannerShow(CivilizationCultivationShowParams params) {
        int currentYear = LocalDate.now().getYear();
        params.setYear(currentYear);
        List<CivilizationCultivationShow> result = baseMapper.homeBannerShow(params);
        params.setYear(currentYear - 1);
        while (result.isEmpty() && currentYear - params.getYear() < 10) {
            result = baseMapper.homeBannerShow(params);
            if (!result.isEmpty()) {
                break;
            }
            params.setYear(params.getYear() - 1);
        }
        return result;
    }

    @Override
    public List<CivilizationCultivationShow> homeBannerShowProd() {
        return baseMapper.homeBannerShowProd();
    }

    @Override
    public List<CivilizationCultivationDashboardDto> cockpitData(String type) {
        Integer year = baseMapper.getMaxYear(type);
        if (StringUtil.isEmpty(year)) {
            return Collections.emptyList();
        }
        return baseMapper.cockpitData(type, year);
    }

    @Override
    public List<CivilizationCultivationDashboardDto> cockpitDataProd() {
        return baseMapper.cockpitDataProd();
    }
}
