package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbRegion;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 苏州志愿者平台区划信息服务接口
 */
public interface ISgbRegionService extends IBaseService<SgbRegion> {
    
    /**
     * 根据区划编号查询区划信息
     * @param regionCode 区划编号
     * @return 区划信息
     */
    SgbRegion getByRegionCode(String regionCode);
    
    /**
     * 同步区划信息
     */
    void syncRegion();

    /**
     * 获取区划树结构
     * @return 区划树列表
     */
    List<SgbRegion> getRegionTree();

    /**
     * 获取区划级联选择树结构
     * @return 级联选择树列表
     */
    List<CascaderExtraDto> getRegionCascaderTree();
} 