package com.fykj.scaffold.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.mall.domain.dto.MallScoreGoodsApplyDto;
import com.fykj.scaffold.mall.domain.entity.MallScoreGoodsApply;
import com.fykj.scaffold.mall.domain.params.MallScoreGoodsApplyParams;
import org.apache.ibatis.annotations.Param;

/**
 * 星光闪耀商品报名表
 * <p>
 * Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
public interface MallScoreGoodsApplyMapper extends BaseMapper<MallScoreGoodsApply> {

    /**
     * 分页查询--后端页面，有权限限制
     *
     * @return
     */
    @DataFilter
    IPage<MallScoreGoodsApplyDto> getPage(IPage<MallScoreGoodsApply> page, @Param("params") MallScoreGoodsApplyParams params);

}
