package com.fykj.scaffold.race.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.race.domain.dto.WinPhotoDto;
import com.fykj.scaffold.race.domain.entity.Activity;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 摄影大赛-活动表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
public interface IRaceActivityService extends IService<Activity>, IBaseService<Activity> {


    /**
     * 设置启用/禁用
     *
     * @param id id
     */
    boolean setEnable(Long id);

    /**
     * 设置是否显示
     *
     * @param id id
     */
    boolean setShow(Long id);

    /**
     * 设置是否可以上传
     *
     * @param id id
     */
    boolean setUpload(Long id);


    /**
     * 获取启用的活动
     *
     * @return
     */
    Activity getActivityByEnable();


    /**
     * 获取可展示的活动
     *
     * @return
     */
    List<Activity> getActivityByShow();

    /**
     * 获取活动的获奖作品
     * @param activityId
     * @return
     */
    List<WinPhotoDto> getActivityWinPhoto(Long activityId);
}

