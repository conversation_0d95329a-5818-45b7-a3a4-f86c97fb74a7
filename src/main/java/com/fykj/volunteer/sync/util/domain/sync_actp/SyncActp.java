package com.fykj.volunteer.sync.util.domain.sync_actp;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 活动登记
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActp {
    /**
     * 唯一标识
     * 必填
     */
    private String uniCode;
    /**
     * 组织编号
     * 必填
     */
    private String orgId;
    /**
     * 活动名称
     * 必填
     */
    private String name;
    /**
     * 服务领域代码
     * 必填
     */
    private String serviceField;
    /**
     * 活动区划代码 必须到社区级，区划编号长度为12位
     * 必填
     */
    private String regionCode;
    /**
     * 活动简介
     * 必填
     */
    private String descs;
    /**
     * 活动详细地址
     * 非必填
     */
    private String address;
    /**
     * 活动开始日期 格式：2016-09-30
     * 必填
     */
    private String startDate;
    /**
     * 活动结束日期 格式：2016-09-30
     * 必填
     */
    private String endDate;
    /**
     * 活动开始时间 格式：HH:mm
     * 必填
     */
    private String startTime;
    /**
     * 活动结束时间 格式：HH:mm
     * 必填
     */
    private String endTime;
    /**
     * 活动联系人
     * 非必填
     */
    private String linkName;
    /**
     * 活动联系人电话
     * 非必填
     */
    private String linkPhone;
    /**
     * 服务时长记录方式 记录方式1.按次，2.按时间段
     * 必填
     */
    private String recordWay;
    /**
     * 按次扫码每次记录多长时间
     * 非必填 当recordWay=1时必填，且是0.5的正整数倍
     */
    private String serviceTime;
    /**
     * 腾讯坐标-经度
     * 必填
     */
    private String txMapLan;
    /**
     * 腾讯坐标-纬度
     * 必填
     */
    private String txMapLat;
    /**
     * 活动logo图片 图片外网url地址转换为base64编码（必须带http或者https全路径）
     * 必填
     */
    private String picLogoUrl;
    /**
     * 活动服务对象
     * 必填
     */
    private String serviceTarget;

    public static class recordWay {
        /**
         * 按次
         */
        public final static String NUM = "1";

        /**
         * 时间段
         */
        public final static String TIME_SLOT = "2";
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "actPlan")
    public static class ActPlan {
        private List<SyncActp> info;
    }

    public String buildXml() {
        SyncActp.ActPlan actPlan = new SyncActp.ActPlan();
        actPlan.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(actPlan);
    }
}
