<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.portal_website.mapper.VideoMapper">

    <select id="getForUpdate" resultType="com.fykj.scaffold.portal_website.domain.entity.Video">
        SELECT *
        FROM video_upload
        WHERE is_deleted = 0
        AND id IN
        <foreach item="item" collection="ids" index="" separator="," open="(" close=")">
          #{item}
        </foreach>
        FOR UPDATE
    </select>

    <select id="pageForVideo" resultType="com.fykj.scaffold.portal_website.domain.entity.Video">
        SELECT *
        FROM video_upload
        WHERE is_deleted = 0
        <if test="params.name != null and params.name != ''">
            AND (name <PERSON>IKE CONCAT('%', CONCAT(#{params.name}, '%')) OR file_name LIKE CONCAT('%', CONCAT(#{params.name}, '%')))
        </if>
        <if test="params.serverCode != null and params.serverCode != ''">
            AND server_code = #{params.serverCode}
        </if>
        <if test="params.importStatus != null">
            <if test="params.importStatus == 0">
                AND success = 0
            </if>
            <if test="params.importStatus == 1">
                AND success = 1
            </if>
            <if test="params.importStatus == 2">
                AND success IS NULL
            </if>
        </if>
        ORDER BY create_date DESC
    </select>
</mapper>
