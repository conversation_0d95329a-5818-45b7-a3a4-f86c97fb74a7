package com.fykj.scaffold.mall.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MallExchangeDto {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 类型数据字典值标明是机器还是线上
     */
    @ApiModelProperty(value = "兑换来源数据字典值标明是机器还是线上")
    @DictTrans(transTo = "exchangeTypeText")
    private String exchangeType;
    @ExcelProperty("兑换来源")
    @ApiModelProperty(value = "兑换来源Text")
    private String exchangeTypeText;
    /**
     * 兑换人用户名称
     */
    @ExcelProperty("兑换人")
    @ApiModelProperty(value = "兑换人用户名称")
    private String exchangeUserName;

    /**
     * 兑换人手机号码
     */
    @ExcelProperty("联系方式")
    @ApiModelProperty(value = "兑换人手机号码")
    private String exchangeUserLinkPhone;

    /**
     * 兑换时间
     */
    @ExcelProperty("兑换时间")
    @ApiModelProperty(value = "兑换时间")
    private LocalDateTime exchangeTime;
    /**
     * 兑换时间
     */
    @ExcelProperty("商品名称")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    /**
     * 组织机构名称
     */
    @ExcelProperty("组织机构名称")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;
    /**
     * 积分单价
     */
    @ExcelProperty("所需积分/件")
    @ApiModelProperty(value = "所需积分/件")
    private BigDecimal goodsPoint;

    /**
     * 兑换积分
     */
    @ExcelProperty("兑换积分")
    @ApiModelProperty(value = "兑换积分")
    private BigDecimal exchangePoint;

    /**
     * 兑换数量
     */
    @ExcelProperty("兑换数量")
    @ApiModelProperty(value = "兑换数量")
    private Integer exchangeNum;

    /**
     * 兑换之后的积分
     */
    @ExcelProperty("剩余积分")
    @ApiModelProperty(value = "剩余积分")
    private BigDecimal exchangeAfterScore;

    /**
     * 状态数据字典值
     */
    @ApiModelProperty(value = "状态数据字典值")
    @DictTrans(transTo = "statusText")
    private String status;
    @ExcelProperty("状态")
    @ApiModelProperty(value = "状态类型Text")
    private String statusText;

    /**
     * 兑换途径
     */
    @ApiModelProperty(value = "快递状态")
    @DictTrans(transTo = "emsStatusText")
    private String emsStatus;
    @ExcelProperty("快递状态")
    @ApiModelProperty(value = "状态类型Text")
    private String emsStatusText;
}
