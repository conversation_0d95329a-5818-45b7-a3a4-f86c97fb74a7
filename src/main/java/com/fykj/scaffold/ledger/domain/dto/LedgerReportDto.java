package com.fykj.scaffold.ledger.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("上报模型")
public class LedgerReportDto implements Serializable {

    private static final long serialVersionUID = -5948923545900796937L;

    @NotNull(message = "指标项不能为空")
    @ApiModelProperty(value = "指标项id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long indexItemId;

    @ApiModelProperty(value = "局办id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long departmentId;

    /**
     * 附件地址
     */
    @ApiModelProperty(value = "附件地址")
    @NotEmpty(message = "请上传附件")
    private String attachmentUrl;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    @NotEmpty(message = "请上传附件")
    private String attachmentName;


    @ApiModelProperty(value = "上报状态")
    private Boolean status;
}
