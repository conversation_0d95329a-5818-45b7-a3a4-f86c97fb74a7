package com.fykj.scaffold.zyz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerCertificate;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerCertificateParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 志愿者证书关联表
 * <p>
 * Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface ZyzVolunteerCertificateMapper extends BaseMapper<ZyzVolunteerCertificate> {

    /**
     * 获取志愿者证书列表
     *
     * @param phone
     * @return
     */
    List<ZyzVolunteerCertificate> getAwardList(@Param("phone") String phone);

    /**
     * 证书分页获取
     *
     * @param page 分页参数
     * @param params 查询参数
     * @return 分页数据
     */
    IPage<ZyzVolunteerCertificate> getPages(IPage<ZyzVolunteerCertificate> page,
                                            @Param("params") ZyzVolunteerCertificateParams params);
}
