package com.fykj.scaffold.zyz.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fykj.scaffold.security.business.domain.entity.Dict;
import com.fykj.scaffold.security.business.domain.entity.OssConfig;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.IOssConfigService;
import com.fykj.scaffold.support.oss.IOssSaver;
import com.fykj.scaffold.support.oss.impl.LocalOssSaver;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import com.fykj.scaffold.zyz.domain.dto.AnnualAccountDataDto;
import com.fykj.scaffold.zyz.domain.entity.AnnualAccountSum;
import com.fykj.scaffold.zyz.domain.entity.AnnualAccountVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IAnnualAccountSumService;
import com.fykj.scaffold.zyz.service.IAnnualAccountVolunteerService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import exception.BusinessException;
import fykj.microservice.core.support.util.SpringContextUtil;
import fykj.microservice.core.support.util.SystemUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.ResultCode;
import utils.StringUtil;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 年度账单接口
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-11-30
 */
@RestController
@RequestMapping("/admin/annual_account")
@Slf4j
@Api(tags = "年度账单接口")
public class AnnualAccountController {

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IOssConfigService ossConfigService;

    @Autowired
    private IAnnualAccountSumService aaSumService;

    @Autowired
    private IAnnualAccountVolunteerService aaVolunteerService;

    @ApiOperation("获取年度账单数据")
    @GetMapping("/getData")
    public JsonResult<AnnualAccountDataDto> getData() {
        String phone = Oauth2Util.getMobile();
        ZyzVolunteer volunteer = volunteerService.getByPhone(phone);
        if (ObjectUtil.isEmpty(volunteer)) {
            throw new BusinessException(ResultCode.FAIL, "您尚未注册成为志愿者，请先进入小程序注册成为志愿者！");
        }
        Long volunteerId = volunteer.getId();
        AnnualAccountSum sum = aaSumService.getDataSum(Boolean.FALSE);
        AnnualAccountVolunteer volunteerSum = aaVolunteerService.volunteerSum(volunteerId);
        AnnualAccountDataDto result = AnnualAccountDataDto.builder()
                .volunteerId(volunteerId)
                .volunteerName(volunteerSum.getVolunteerName())
                .volunteerNum(sum.getVolunteerNum())
                .actNum(sum.getActNumThisYear())
                .serviceTime(sum.getServiceTimeThisYear())
                .fiveStarVolunteerNum(sum.getFiveStarVolunteerNum())
                .starVolunteerNum(sum.getStarVolunteerNum())
                .foundedDayNum((int)volunteerSum.getFounded().toLocalDate().until(LocalDate.now(), ChronoUnit.DAYS))
                .joinActNum(volunteerSum.getActNum())
                .achieveServiceTime(volunteerSum.getServiceTime())
                .starLevel(getStarLevel(volunteerSum.getServiceTime()))

                .teamNum(sum.getTeamNum())
                .subType1ActNumYear(sum.getSubType1ActNumYear())
                .subType1ServiceTimeYear(sum.getSubType1ServiceTimeYear())
                .subType2ActNumYear(sum.getSubType2ActNumYear())
                .subType2ServiceTimeYear(sum.getSubType2ServiceTimeYear())
                .subType3ActNumYear(sum.getSubType3ActNumYear())
                .subType3ServiceTimeYear(sum.getSubType3ServiceTimeYear())

                .actNumYear(volunteerSum.getActNumYear())
                .serviceTimeYear(volunteerSum.getServiceTimeYear())
                .joinTeam(volunteerSum.getJoinTeam())
                .pointYear(volunteerSum.getPointYear())
                .build();
        return new JsonResult<>(result);
    }

    private int getStarLevel(BigDecimal serviceTime) {
        if (!(serviceTime.compareTo(BigDecimal.valueOf(1500)) < 0)) {
            return 5;
        }
        if (!(serviceTime.compareTo(BigDecimal.valueOf(1000)) < 0)) {
            return 4;
        }
        if (!(serviceTime.compareTo(BigDecimal.valueOf(600)) < 0)) {
            return 3;
        }
        if (!(serviceTime.compareTo(BigDecimal.valueOf(300)) < 0)) {
            return 2;
        }
        if (!(serviceTime.compareTo(BigDecimal.valueOf(100)) < 0)) {
            return 1;
        }
        return 0;
    }

    @ApiOperation(value = "创建年度账单小程序二维码")
    @GetMapping(value = "/createQrCode")
    public void createQrCode(@RequestParam String appId, @RequestParam Boolean prod) {
        Dict annualAccountQrcode = dictService.lambdaQuery().eq(Dict::getCode, "ANNUAL_ACCOUNT_QRCODE").eq(Dict::getStatus, true).one();
        if (ObjectUtil.isEmpty(annualAccountQrcode)) {
            throw new BusinessException(ResultCode.FAIL, "年度账单二维码暂无法生成！");
        }
        String qrCodePath = annualAccountQrcode.getValue();
        if (StringUtil.isNotEmpty(qrCodePath)) {
            File qrCodeFile = new File(virtualPathToRealPath(qrCodePath));
            if (qrCodeFile.exists()) {
                try {
                    write(qrCodeFile);
                    return;
                } catch (Exception e) {
                    log.error("年度账单小程序二维码下载失败，请联系管理员", e);
                    throw new BusinessException(ResultCode.BAD_REQUEST, "年度账单小程序二维码下载失败，请联系管理员!");
                }
            }
        }
        try {
            final WxMaService wxMaService = WxMaConfiguration.getMaService(appId);
            File qrCodeFile;
            if (prod) {
                qrCodeFile = wxMaService.getQrcodeService().createWxaCode("pages_sub1/pages/annual/blank", "release", 430, true, null, true);
            } else {
                qrCodeFile = wxMaService.getQrcodeService().createWxaCode("pages_sub1/pages/annual/blank", "trial", 430, true, null, true);
            }
            ImageIO.write(ImageIO.read(qrCodeFile), "png", qrCodeFile);
            IOssSaver ossSaver = SpringContextUtil.getBean(LocalOssSaver.class);
            String path = ossSaver.save(FileUtil.getInputStream(qrCodeFile), "年度账单二维码.png").getUrl();
            annualAccountQrcode.setValue(path);
            dictService.updateById(annualAccountQrcode);
            write(new File(virtualPathToRealPath(path)));
        } catch (WxErrorException | IOException e) {
            log.error("年度账单小程序二维码下载失败，请联系管理员", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "年度账单小程序二维码下载失败，请联系管理员!");
        }
    }

    private String virtualPathToRealPath(String virtualPath) {
        OssConfig conf = ossConfigService.getConfig();
        String virtualUrl = conf.getUrl();
        if (virtualPath.contains(virtualUrl)) {
            return virtualPath.replace(virtualUrl, conf.getStorageLocation());
        }
        return virtualPath;
    }

    private void write(File file) {
        HttpServletResponse response = SystemUtil.getResponse();
        try (FileInputStream in = new FileInputStream(file);
             OutputStream out = response.getOutputStream()) {
            int length = in.available();
            byte[] data = new byte[length];
            response.setContentLength(length);
            response.setContentType("application/octet-stream");
            while (in.read(data) > 0) {
                out.write(data);
            }
            out.flush();
        } catch (IOException e) {
            throw new BusinessException(ResultCode.FAIL, "读取文件失败", e);
        }
    }
}
