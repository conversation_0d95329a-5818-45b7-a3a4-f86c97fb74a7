package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.mall.domain.dto.AddPointRecordDto;
import com.fykj.scaffold.zyz.domain.dto.HomePageVolunteerServiceDto;
import com.fykj.scaffold.zyz.domain.dto.MiniVolunteerDto;
import com.fykj.scaffold.zyz.domain.dto.RadarMapDto;
import com.fykj.scaffold.zyz.domain.dto.VolunteerImportDto;
import com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto;
import com.fykj.scaffold.zyz.domain.dto.report_form.VolunteerOrgSumDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerDirty;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.ReportFormParams;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 志愿者表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-01
 */
public interface IZyzVolunteerDirtyService extends IService<ZyzVolunteerDirty>, IBaseService<ZyzVolunteerDirty> {


}

