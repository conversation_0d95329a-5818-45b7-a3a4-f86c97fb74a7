<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fykj.scaffold.capability.mapper.CourseMapper">
    <select id="query" resultType="com.fykj.scaffold.capability.domain.dto.CourseListDTO">
        SELECT
            cc.id AS id,
            cc.course_name AS courseName,
            cc.`address` AS `address`,
            cc.course_type AS courseType,
            cc.course_category AS courseCategory,
            cc.applicable_credit_range AS applicableCreditRange,
            cc.course_date AS courseDate,
            cc.course_start_time AS courseStartTime,
            cc.course_end_time AS courseEndTime,
            cc.course_credits AS courseCredits,
            cc.order_num AS orderNum,
            cc.create_date AS createDate,
            cc.publish_org AS publishOrg,
            so.`name` AS publishOrgName,
            cc.outer_display AS outerDisplay,
            cc.online_course_deadline AS onlineCourseDeadline,
            cc.long_term_valid AS longTermValid,
            cc.top AS top,
            cc.image AS image
        FROM cu_course cc
        LEFT JOIN sys_org so ON cc.publish_org = so.`code`
        <where>
            AND cc.is_deleted = 0
            <if test="params.outerDisplay != null">
                AND cc.outer_display = #{params.outerDisplay}
            </if>
            <if test="params.courseType != null and params.courseType != ''">
                AND cc.course_type = #{params.courseType}
            </if>
            <if test="params.courseName != null and params.courseName != ''">
                AND cc.course_name LIKE CONCAT('%',#{params.courseName},'%')
            </if>
            <if test="params.overStatus != null and params.overStatus == false">
                AND (
                    (cc.course_type = 'OFFLINE'
                        AND CONCAT(cc.course_date, ' ', cc.course_end_time) >= NOW())
                    OR
                    (cc.course_type = 'ONLINE'
                        AND cc.online_course_deadline >= NOW())
                )
            </if>
        </where>
        ORDER BY cc.top DESC, cc.order_num DESC
    </select>
    <select id="queryMine" resultType="com.fykj.scaffold.capability.domain.dto.CourseListDTO">
        SELECT
            cc.id AS id,
            cc.course_name AS courseName,
            cc.`address` AS `address`,
            cc.course_type AS courseType,
            cc.course_category AS courseCategory,
            cc.course_date AS courseDate,
            cc.course_start_time AS courseStartTime,
            cc.course_end_time AS courseEndTime,
            cc.course_credits AS courseCredits,
            cc.order_num AS orderNum,
            cc.create_date AS createDate,
            cc.image AS image,
            cc.publish_org AS publishOrg,
            cc.outer_display AS outerDisplay,
            cc.online_course_deadline AS onlineCourseDeadline,
            cc.top AS top,
            ccu.complete_status AS completeStatus,
            IF(cc.course_type = 'ONLINE',
                 IF(cc.online_course_deadline >= NOW() OR cc.long_term_valid, 'processing', 'over'),
                    IF(CONCAT(cc.course_date, ' ', course_start_time) > NOW(), 'not_start',
                        IF(CONCAT(cc.course_date, ' ', course_end_time) &lt; NOW(), 'over', 'processing'))
            ) AS `status`
        FROM cu_course cc
        LEFT JOIN cu_course_user ccu ON cc.id = ccu.course_id
        <where>
            AND cc.is_deleted = 0
            AND ccu.is_deleted = 0
            AND ccu.user_id = #{params.userId}
            <if test="params.outerDisplay != null">
                AND cc.outer_display = #{params.outerDisplay}
            </if>
            <if test="params.courseType != null and params.courseType != ''">
                AND cc.course_type = #{params.courseType}
            </if>
            <if test="params.courseName != null and params.courseName != ''">
                AND cc.course_name LIKE('%',#{params.courseName},'%')
            </if>
            <if test="params.completeStatus != null">
                AND ccu.complete_status = ${params.completeStatus}
            </if>
        </where>
        ORDER BY ccu.sign_up_at DESC
    </select>

    <select id="getNeedRemindCourses" resultType="com.fykj.scaffold.capability.domain.entity.Course">
        SELECT *
        FROM cu_course
        WHERE is_deleted = 0
        AND TIMESTAMP(course_date, course_start_time) IS NOT NULL
        AND TIMESTAMP(course_date, course_start_time) &gt; #{start}
        AND TIMESTAMP(course_date, course_start_time) &lt;= #{end}
    </select>
</mapper>
