package com.fykj.scaffold.advanced_unit.domain.dto;

import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitAuditOperation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 2025/3/12
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class UnitAuditDTO {

    @ApiModelProperty("主键")
    private @NotEmpty(
            message = "主键必填"
    ) List<Long> ids;

    @ApiModelProperty("审核操作 字典CODE: " + AdvancedUnitAuditOperation.DICT_CODE)
    @NotBlank(
            message = "审核操作"
    )
    private String auditOperation;



}
