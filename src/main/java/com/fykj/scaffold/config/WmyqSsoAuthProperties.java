package com.fykj.scaffold.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 2023/4/3
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "wmyq.sso-auth")
public class WmyqSsoAuthProperties  {

    /**
     * OSS 密钥信息
     */
    private String key;

    /**
     * OSS 密钥信息
     */
    private String secret;

    /**
     * OSS 域名信息
     */
    private String domain;

    /**
     * OSS 登录接口信息
     */
    private String loginApi;

    /**
     * OSS 资源接口信息
     */
    private String resourceApi;

}
