<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.AnnualAccountVolunteerMapper">

    <insert id="init">
        INSERT INTO annual_account_volunteer (
                id,version,is_deleted,create_date,update_date,creator,updater,
                volunteer_id,volunteer_name,volunteer_phone,founded,act_num,service_time,refresh_time,act_num_year,service_time_year,join_team,point_year
        )
        SELECT
            v.id AS id,
            0 AS version,
            0 AS is_deleted,
            NOW() AS create_date,
            NOW() AS update_date,
            NULL AS creator,
            NULL AS updater,
            v.id AS volunteer_id,
            v.name AS volunteer_name,
            v.phone AS volunteer_phone,
            v.founded,
            IFNULL(aa.actNum, 0) AS act_num,
            IFNULL(v.service_long, 0) AS service_time,
            NOW() AS refresh_time,
            IFNULL(aa.actNumYear, 0) AS act_num_year,
            IFNULL(v.service_long_this_year, 0) AS service_time_year,
            bb.teamNum AS join_team,
            IFNULL(aa.pointYear, 0) AS point_year
        FROM zyz_volunteer v
        LEFT JOIN (
            SELECT
                volunteer_id,
                COUNT(DISTINCT activity_id) AS actNum,
                COUNT(DISTINCT IF(time_period_start_time > #{start}, activity_id, NULL)) AS actNumYear,
                SUM(IF(time_period_start_time > #{start} AND is_exchange=TRUE, service_long, 0))/2 AS pointYear
            FROM zyz_activity_apply
            WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
            GROUP BY volunteer_id
        ) aa ON v.id = aa.volunteer_id
            LEFT JOIN (
            SELECT
                volunteer_id,
                COUNT(DISTINCT team_id) AS teamNum
                FROM zyz_volunteer_team
                WHERE is_deleted = 0   GROUP BY volunteer_id
                    ) bb ON v.id = bb.volunteer_id
        WHERE v.is_deleted = 0 AND v.write_off = 0
        ORDER BY v.founded
    </insert>

    <select id="getNeedUpdateVolunteerData" resultType="com.fykj.scaffold.zyz.domain.entity.AnnualAccountVolunteer">
        SELECT
            v.id AS volunteerId,
            v.name AS volunteerName,
            v.phone AS volunteerPhone,
            v.founded,
            IFNULL(aa.actNum, 0) AS actNum,
            IFNULL(v.service_long, 0) AS serviceTime,
            IFNULL(aa.actNumYear, 0) AS actNumYear,
            IFNULL(v.service_long_this_year, 0) AS serviceTimeYear,
            bb.teamNum AS joinTeam,
            IFNULL(aa.pointYear, 0) AS pointYear
        FROM zyz_volunteer v
        LEFT JOIN (
            SELECT
                volunteer_id,
                COUNT(DISTINCT activity_id) AS actNum,
                COUNT(DISTINCT IF(time_period_start_time > #{start}, activity_id, NULL)) AS actNumYear,
                SUM(IF(time_period_start_time > #{start} AND is_exchange=TRUE, service_long, 0))/2 AS pointYear
            FROM zyz_activity_apply
            WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
              AND volunteer_id IN (SELECT volunteer_id FROM zyz_activity_apply WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL AND time_period_end_time &gt;= DATE_SUB(NOW(), INTERVAL 100 day))
            GROUP BY volunteer_id
        ) aa ON v.id = aa.volunteer_id
             LEFT JOIN (
            SELECT
                volunteer_id,
                COUNT(DISTINCT team_id) AS teamNum
                FROM zyz_volunteer_team
                WHERE is_deleted = 0
                    ) bb ON v.id = bb.volunteer_id
        WHERE v.is_deleted = 0 AND v.write_off = 0 AND (v.founded &gt;= DATE_SUB(NOW(), INTERVAL 10 day) OR aa.volunteer_id IS NOT NULL)
        ORDER BY v.founded
    </select>

    <select id="getVolunteerSumByVolunteerId" resultType="com.fykj.scaffold.zyz.domain.entity.AnnualAccountVolunteer">
        SELECT
            v.id AS volunteerId,
            v.name AS volunteerName,
            v.phone AS volunteerPhone,
            v.founded,
            IFNULL(aa.actNum, 0) AS actNum,
            IFNULL(v.service_long, 0) AS serviceTime,
            NOW() AS refreshTime,
            IFNULL(aa.actNumYear, 0) AS actNumYear,
            IFNULL(v.service_long_this_year, 0) AS serviceTimeYear,
            bb.teamNum AS joinTeam,
            IFNULL(aa.pointYear, 0) AS pointYear
        FROM zyz_volunteer v
        LEFT JOIN (
            SELECT
                volunteer_id,
                COUNT(DISTINCT activity_id) AS actNum,
                COUNT(DISTINCT IF(time_period_start_time >= #{start}, activity_id, NULL)) AS actNumYear,
                SUM(IF(time_period_start_time > #{start} AND is_exchange=TRUE, service_long, 0))/2 AS pointYear
            FROM zyz_activity_apply
            WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL AND volunteer_id = #{volunteerId}
        ) aa ON v.id = aa.volunteer_id
          LEFT JOIN (
            SELECT
                volunteer_id,
                COUNT(DISTINCT team_id) AS teamNum
                FROM zyz_volunteer_team
                WHERE is_deleted = 0
                    ) bb ON v.id = bb.volunteer_id
        WHERE v.is_deleted = 0 AND v.write_off = 0 AND v.id = #{volunteerId}
    </select>

    <select id="selectByVolunteerId" resultType="com.fykj.scaffold.zyz.domain.entity.AnnualAccountVolunteer">
        SELECT *
        FROM annual_account_volunteer
        WHERE is_deleted = 0 AND volunteer_id = #{volunteerId}
    </select>
</mapper>
