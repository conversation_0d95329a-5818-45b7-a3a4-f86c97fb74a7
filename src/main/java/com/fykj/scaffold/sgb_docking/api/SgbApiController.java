package com.fykj.scaffold.sgb_docking.api;

import com.fykj.scaffold.sgb_docking.service.ISgbDictService;
import com.fykj.scaffold.sgb_docking.service.ISgbServiceTypeService;
import com.fykj.scaffold.support.domain.dto.CascaderExtraDto;
import fykj.microservice.core.beans.vo.IdTextVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 社工部同步平台对外API接口
 */
@RestController
@RequestMapping("/api/sgb")
@Api(tags = "社工部同步-对外API接口")
public class SgbApiController {

    @Autowired
    private ISgbDictService sgbDictService;
    
    @Autowired
    private ISgbServiceTypeService sgbServiceTypeService;

    @ApiOperation("获取社工部指定编码的字典")
    @GetMapping("/dict/parent")
    public JsonResult<List<IdTextVo>> getSgbDictByCode(@RequestParam String code) {
        return new JsonResult<>(sgbDictService.getIdTextByType(code));
    }
    
    @ApiOperation("获取服务领域级联选择树结构")
    @GetMapping("/service-type/tree")
    public JsonResult<List<CascaderExtraDto>> getServiceTypeCascaderTree() {
        return new JsonResult<>(sgbServiceTypeService.getServiceTypeCascaderTree());
    }
}
