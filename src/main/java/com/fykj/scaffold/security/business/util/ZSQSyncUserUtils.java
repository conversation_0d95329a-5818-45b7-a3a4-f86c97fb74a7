package com.fykj.scaffold.security.business.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.business.domain.dto.ZsqUserSyncDto;
import com.fykj.scaffold.security.login.cons.ZsqConfig;
import com.fykj.scaffold.zsq_docking.utils.AuthHeaderUtil;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import result.ResultCode;
import java.util.*;

import static cn.hutool.core.text.StrPool.C_SPACE;


/**
 * @Author：yangxu
 * @Date：2024/12/10 18:12
 * @Description：
 */
@Component
@Slf4j
public class ZSQSyncUserUtils {

    @Autowired
    private ZsqConfig zsqConfig;

    private static final String RESPONSE_CODE = "code";

    private static final String RESPONSE_OBJECT = "obj";

    private static final String TOKEN_GET_PARAMS_APPID = "appId";

    private static final String TOKEN_GET_PARAMS_SECRET = "secret";

    private static final String TOKEN_GET_RESPONSE_ACCESS_TOKEN = "access_token";

    private static final String TOKEN_GET_RESPONSE_TOKEN_TYPE = "token_type";

    private static final String USER_GET_HEADER_AUTHORIZATION = "Authorization";

    private static final String USER_GET_PARAMS_START_DATE = "startDate";

    private static final String USER_GET_PARAMS_END_DATE = "endDate";

    private String getTokenUrl() {
        return zsqConfig.getBaseUrl() + zsqConfig.getAuthTokenUrl();
    }

    private String getUserUrl() {
        return zsqConfig.getBaseUrl() + zsqConfig.getUserListUrl();
    }

    public String getToken() {
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN_GET_PARAMS_APPID, zsqConfig.getAppId());
        params.put(TOKEN_GET_PARAMS_SECRET, zsqConfig.getSecret());
        JSONObject result = doPost(getTokenUrl(), params, null);
        int code= result.getIntValue(RESPONSE_CODE);
        if (code != 0) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台获取token失败，异常 code：{" + code + "}");
        }
        String accessToken;
        try {
            String tokenType = result.getJSONObject(RESPONSE_OBJECT).getString(TOKEN_GET_RESPONSE_TOKEN_TYPE);
            String token = result.getJSONObject(RESPONSE_OBJECT).getString(TOKEN_GET_RESPONSE_ACCESS_TOKEN);
            accessToken = tokenType + C_SPACE + token;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台获取token异常，异常原因：{" + e.getMessage() + "}");
        }
        if (StringUtils.isEmpty(accessToken)) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台获取token失败，token接口返回数据：" + result.toJSONString());
        }
        return accessToken;
    }

    public List<ZsqUserSyncDto> getUserList(String startDate, String endDate, String token) {
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(startDate)) {
            params.put(USER_GET_PARAMS_START_DATE, startDate);
        }
        if (StringUtils.isNotEmpty(endDate)) {
            params.put(USER_GET_PARAMS_END_DATE, endDate);
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(USER_GET_HEADER_AUTHORIZATION, token);
        JSONObject result = doPost(getUserUrl(), params, headerMap);
        int code= result.getIntValue(RESPONSE_CODE);
        if (code != 0) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台获取用户数据失败，异常 code：{" + code + "}");
        }
        List<ZsqUserSyncDto> userList;
        try {
            userList = JSONObject.parseArray(result.getJSONArray(RESPONSE_OBJECT).toJSONString(), ZsqUserSyncDto.class);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台取用户数据异常，异常原因：{" + e.getMessage() + "}");
        }
        if (userList == null || userList.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "知社区平台未获取用户数据，接口返回数据：" + result.toJSONString());
        }
        return userList;
    }

    /**
     * 发送 POST 请求
     * @param url 请求地址
     * @param params 请求参数
     * @return 响应结果 JSONObject
     */
    public static JSONObject doPost(String url, Map<String, Object> params, Map<String, String> headerMap) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (headerMap != null && !headerMap.isEmpty()) {
            headerMap.putAll(AuthHeaderUtil.addHeaders());
        } else {
            headerMap = AuthHeaderUtil.addHeaders();
        }
        if (headerMap != null && !headerMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                headers.add(entry.getKey(), entry.getValue());
            }
        }
        // 将参数 map 转为 JSON 字符串
        String jsonBody = JSONObject.toJSONString(params);
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        return JSONObject.parseObject(response.getBody());
    }

    private static final String privateKey = "372f0bd7c8f84dac9963ede85e7b1e0b";

    /**
     * 解密
     *
     * @param ciphertext
     * @return
     */
    public static String decrypt(String ciphertext) {
        SymmetricCrypto sm4 = SmUtil.sm4(HexUtil.decodeHex(privateKey));
        return sm4.decryptStr(ciphertext);
    }
}
