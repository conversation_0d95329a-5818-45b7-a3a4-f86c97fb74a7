<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.sgb_docking.mapper.SgbDictMapper">

    <!-- 查询结果映射 -->
    <resultMap id="BaseResultMap" type="com.fykj.scaffold.sgb_docking.domain.entity.SgbDict">
        <id column="id" property="id" />
        <result column="value" property="value" />
        <result column="label" property="label" />
        <result column="type" property="type" />
        <result column="describe" property="describe" />
        <result column="is_deleted" property="deleted" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 清空所有数据 -->
    <update id="clearAll">
        DELETE FROM sgb_dict
    </update>

    <!-- 清空指定类型的字典数据 -->
    <update id="clearByType">
        DELETE FROM sgb_dict WHERE type = #{dictType}
    </update>

    <!-- 根据字典类型查询字典数据列表 -->
    <select id="selectByDictType" resultMap="BaseResultMap">
        SELECT * FROM sgb_dict WHERE type = #{dictType} AND is_deleted = 0
    </select>

    <!-- 根据字典编码查询字典数据 -->
    <select id="selectByDictCode" resultMap="BaseResultMap">
        SELECT * FROM sgb_dict WHERE value = #{dictCode} AND is_deleted = 0 LIMIT 1
    </select>

</mapper> 