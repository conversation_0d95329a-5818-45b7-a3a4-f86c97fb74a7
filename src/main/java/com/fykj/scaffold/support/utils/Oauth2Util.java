package com.fykj.scaffold.support.utils;

import com.fykj.scaffold.security.business.domain.BackendUserDetail;
import com.fykj.scaffold.support.conns.Cons;
import exception.BusinessException;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import result.ResultCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Oauth2工具类
 *
 * <AUTHOR>
 */
@UtilityClass
public class Oauth2Util {

    public static BackendUserDetail getUser() {
        Authentication authentication = Optional
                .ofNullable(SecurityContextHolder.getContext())
                .map(SecurityContext::getAuthentication)
                .orElse(null);
        if (authentication instanceof OAuth2Authentication) {
            return (BackendUserDetail) authentication.getPrincipal();
        } else if (authentication instanceof BackendUserDetail) {
            return (BackendUserDetail) authentication;
        } else {
            return null;
        }
    }

    public static String getRequestClientId() {
        Authentication authentication = Optional
                .ofNullable(SecurityContextHolder.getContext())
                .map(SecurityContext::getAuthentication)
                .orElse(null);
        if (authentication instanceof OAuth2Authentication) {
            return ((OAuth2Authentication)authentication).getOAuth2Request().getClientId();
        }
        return null;
    }

    public static Serializable getUserId() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getId)
                .orElse(null);
    }

    public static String getMobile() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getMobile)
                .orElse(null);
    }

    public static List<Long> getRoleIds() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getRoleIds)
                .orElse(new ArrayList<>());
    }

    public static List<String> getRoleCodes() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getRoleCodes)
                .orElse(new ArrayList<>());
    }

    public static String getName() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getNickName)
                .orElse(null);
    }

    public static String getOrgCode() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getOrgCode)
                .orElse(null);
    }

    public static Long getTeamId() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getTeamId)
                .orElse(null);
    }

    public static String getOrgCodePrefix() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getOrgCodePrefix)
                .orElse(null);
    }

    public static String getManagerCapacity() {
        return Optional.ofNullable(getUser())
                .map(BackendUserDetail::getManagerCapacity)
                .orElse(null);
    }

    public static boolean hasRole(String roleCode) {
        return Optional.ofNullable(getUser())
                .map(it -> it.isHasRole(roleCode))
                .orElse(false);
    }


    public static boolean isAdmin() {
        return getUser() != null && hasRole(Cons.RoleCode.ROLE_CODE_ADMIN);
    }

}
