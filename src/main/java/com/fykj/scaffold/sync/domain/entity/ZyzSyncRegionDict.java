package com.fykj.scaffold.sync.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 市平台地域数据字典
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_sync_region_dict")
public class ZyzSyncRegionDict extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 地域code
     */
    @TableField("region_code")
    @ApiModelProperty(value = "地域code")
    private String regionCode;

    /**
     * 地域名
     */
    @TableField("region_name")
    @ApiModelProperty(value = "地域名")
    private String regionName;

    /**
     * 父类code
     */
    @TableField("parent_code")
    @ApiModelProperty(value = "父类code")
    private String parentCode;

    /**
     * 组织架构链
     */
    @TableField("code_link")
    @ApiModelProperty(value = "组织架构链")
    private String codeLink;
}
