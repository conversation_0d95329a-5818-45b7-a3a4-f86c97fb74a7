package com.fykj.scaffold.security.business.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.security.business.domain.entity.SysUserExpand;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-28
 */
public interface ISysUserExpandService extends IBaseService<SysUserExpand> {
    /**
     * 保存用户扩展
     *
     * @param userId
     * @param expandList
     */
    void saveExpands(Long userId, List<SysUserExpand> expandList);

    /**
     * 根据用户id查询用户扩展信息
     *
     * @param userId
     * @return
     */
    List<SysUserExpand> getByUserId(Long userId);

    /**
     * 更新默认身份
     *
     * @param userId
     * @param expandId
     */
    void updateDefault(long userId, long expandId);

    /**
     * 通过角色和组织（组织code/团队id）获取用户
     * @param roles
     * @param type
     * @param teamIds
     * @param orgCodes
     * @return
     */
    List<Long> getUserByRoleAndOrg(List<String> roles, String type, List<Long> teamIds, List<String> orgCodes);


    /**
     * 移除团队管理员角色
     *
     * @param userId
     * @param teamId
     */
    void removeTeamAdminExpand(long userId, long teamId);
}

