<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.ledger.mapper.LedgerDepartmentMapper">

    <select id="pages" resultType="com.fykj.scaffold.ledger.domain.entity.LedgerDepartment">
        SELECT
            d.*,
            u.username,
            u.`name`,
            u.mobile
        FROM
            ledger_department d
                LEFT JOIN sys_user u ON d.user_id = u.id
        WHERE
            d.is_deleted = 0
        <if test="params.departmentName != null and params.departmentName != ''">
            and d.department_name LIKE CONCAT('%',#{params.departmentName},'%')
        </if>
        <if test="params.contactPerson != null and params.contactPerson != ''">
            and d.contact_person LIKE CONCAT('%',#{params.contactPerson},'%')
        </if>
        <if test="params.account != null and params.account != ''">
            AND (u.name LIKE CONCAT('%', CONCAT(#{params.account}, '%'))
            OR u.mobile LIKE CONCAT('%', CONCAT(#{params.account}, '%'))
            )
        </if>
    </select>
    <select id="getDepartmentName" resultType="java.lang.String">
        SELECT
            department_name
        FROM
            ledger_department
        WHERE
            is_deleted = 0
    </select>
    <select id="getMobileByDepartmentIdList" resultType="java.lang.String">
        SELECT
            u.mobile
        FROM
            ledger_department d
                LEFT JOIN sys_user u ON d.user_id = u.id
        WHERE
            d.is_deleted = 0
        <if test="departmentIdList != null">
            AND d.id IN
            <foreach collection="departmentIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>