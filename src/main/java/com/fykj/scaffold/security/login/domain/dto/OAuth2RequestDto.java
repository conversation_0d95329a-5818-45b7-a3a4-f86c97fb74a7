package com.fykj.scaffold.security.login.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel
public class OAuth2RequestDto extends Oauth2BaseDto {
    @NotNull(message = "username必填")
    @ApiModelProperty(value = "用户名")
    private String username;

    @NotNull(message = "password必填")
    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "图片验证码识别码")
    private String uuid;

    @ApiModelProperty(value = "图片验证码")
    private String captcha;

    @ApiModelProperty(value = "开放平台绑定key")
    private String unionIdBindKey;

    @ApiModelProperty(value = "客户端id")
    private String clientId;

    @ApiModelProperty(value = "企业责任联盟登录")
    private Boolean csrLogin;
}
