package com.fykj.scaffold.capability.domain.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.utils.Desensitise;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class CourseSignInDTO {

    @ApiModelProperty(value = "志愿者ID")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long volunteerId;

    @ApiModelProperty(value = "课程ID")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long courseId;

    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long userId;

    /**
     * 志愿者姓名
     */
    @ApiModelProperty(value = "志愿者姓名")
    private String name;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 身份证号
     */
    @Desensitise
    @ApiModelProperty(value = "身份证号")
    private String certificateId;

    /**
     * 组织架构CODE（社区层级）
     */
    @ApiModelProperty(value = "组织架构CODE（社区层级）")
    private String orgCode;

    @ApiModelProperty(value = "上级组织")
    private String orgName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 报名时间
     */
    @ApiModelProperty(value = "报名时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime signUpAt;

    @ApiModelProperty("实际学分")
    private Integer actualCredits;

    @ApiModelProperty("签到时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime signInAt;

    @ApiModelProperty("签到地点")
    private String signInAddress;

    @ApiModelProperty("签到经度")
    private BigDecimal signInLongitude;

    @ApiModelProperty("签到纬度")
    private BigDecimal signInLatitude;

    @ApiModelProperty(value = "完成情况")
    private Boolean completeStatus;

    @ApiModelProperty("完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime completedAt;

    @ApiModelProperty("课程开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN)
    private LocalDateTime courseStartAt;

}
