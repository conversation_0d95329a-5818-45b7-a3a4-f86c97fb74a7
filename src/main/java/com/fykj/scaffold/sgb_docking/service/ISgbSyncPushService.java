package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncLog;

import java.util.List;

/**
 * 苏州志愿者平台推送服务接口
 */
public interface ISgbSyncPushService {

    /**
     * 推送志愿者信息
     * @param bizId 业务ID
     * @param syncId 同步请求ID
     * @return 推送结果
     */
    SgbSyncLog syncVolunteer(long bizId, long syncId);

    /**
     * 注册组织 - 推送志愿服务组织信息到苏州社工部平台
     * @param bizId 组织业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    SgbSyncLog syncOrg(long bizId, long syncId);

    /**
     * 注册组织 - 推送志愿服务组织信息到苏州社工部平台
     * @param bizId 组织业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    SgbSyncLog syncTeam(long bizId, long syncId);

    /**
     * 加入团队 - 推送志愿者加入团队的关系信息
     * @param bizId 加入团队记录业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    SgbSyncLog syncTeamVolunteer(long bizId, long syncId);

    /**
     * 活动登记 - 推送志愿服务活动基本信息
     * @param bizId 活动业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    SgbSyncLog syncActivity(long bizId, long syncId);

    /**
     * 发布活动招募 - 推送活动招募信息
     * @param bizId 活动招募业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    SgbSyncLog syncActivityRecruit(long bizId, long syncId);

    /**
     * 加入活动招募 - 推送志愿者参与活动招募的信息
     * @param bizId 活动参与记录业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    SgbSyncLog syncActivityMember(long bizId, long syncId);

    /**
     * 服务时长上传 - 推送志愿者服务时长记录
     * @param bizId 服务时长记录业务ID
     * @param syncId 同步请求ID
     * @return 同步日志记录
     */
    SgbSyncLog syncServiceTime(long bizId, long syncId);

    // ==================== 批量同步方法 ====================

    /**
     * 批量推送志愿者信息
     * @param bizIds 业务ID列表
     */
    void syncVolunteerBatch(List<Long> bizIds);

    /**
     * 批量推送志愿阵地信息
     * @param bizIds 业务ID列表
     */
    void syncOrgBatch(List<Long> bizIds);

    /**
     * 批量推送团队信息
     * @param bizIds 业务ID列表
     */
    void syncTeamBatch(List<Long> bizIds);

    /**
     * 批量推送加入团队信息
     * @param bizIds 业务ID列表（ZyzVolunteerTeam的ID）
     */
    void syncTeamVolunteerBatch(List<Long> bizIds);

    /**
     * 批量推送活动信息
     * @param bizIds 业务ID列表
     */
    void syncActivityBatch(List<Long> bizIds);

    /**
     * 批量推送活动招募信息
     * @param bizIds 业务ID列表
     */
    void syncActivityRecruitBatch(List<Long> bizIds);

    /**
     * 批量推送加入活动招募信息
     * @param bizIds 业务ID列表
     */
    void syncActivityMemberBatch(List<Long> bizIds);

    /**
     * 批量推送服务时长信息
     * @param bizIds 业务ID列表
     */
    void syncServiceTimeBatch(List<Long> bizIds);
}