package com.fykj.scaffold.sgb_docking.service;

import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.sgb_docking.domain.dto.*;
import com.fykj.scaffold.zyz.domain.entity.*;

public interface ISgbFieldMappingService {

    /**
     * 志愿者系统数据 -> 社工部要求志愿者数据
     * @param entity 志愿者系统数据
     * @return 社工部要求志愿者数据
     */
    SgbSyncVolunteerRequest mappingVolunteer(ZyzVolunteer entity);

    /**
     * 志愿阵地数据 -> 社工部志愿阵地数据
     * @param entity 志愿阵地数据（系统组织）
     * @return 社工部志愿阵地数据
     */
    SgbSyncPositionRequest mappingPosition(SysOrg entity);

    /**
     * 团队数据 -> 社工部组织数据
     * @param entity 团队数据
     * @return 社工部组织数据
     */
    SgbSyncTeamRequest mappingTeam(ZyzTeam entity);

    /**
     * 志愿者加入团队关系 -> 社工部加入组织数据
     * @param entity 志愿者团队关系数据
     * @return 社工部加入组织数据
     */
    SgbSyncOrgMemberRequest mappingOrgMember(ZyzVolunteerTeam entity);

    /**
     * 活动数据 -> 社工部活动登记数据
     * @param entity 活动数据
     * @return 社工部活动登记数据
     */
    SgbSyncActivityRequest mappingActivity(ZyzActivity entity);

    /**
     * 活动时段数据 -> 社工部活动招募数据
     * @param entity 活动时段数据
     * @return 社工部活动招募数据
     */
    SgbSyncActivityRecruitRequest mappingActivityRecruit(ZyzActivityTimePeriod entity);

    /**
     * 活动报名数据 -> 社工部加入活动招募数据
     * @param entity 活动报名数据
     * @return 社工部加入活动招募数据
     */
    SgbSyncActivityMemberRequest mappingActivityMember(ZyzActivityApply entity);

    /**
     * 活动报名数据 -> 社工部服务时长上传数据
     * @param entity 活动报名数据（包含服务时长信息）
     * @return 社工部服务时长上传数据
     */
    SgbSyncServiceTimeRequest mappingServiceTime(ZyzActivityApply entity);

}
