package com.fykj.volunteer.sync.util.domain.sync_user;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncUserResult {
    /**
     * 身份证
     */
    private String idCode;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 消息
     */
    private String msg;
    /**
     * 唯一编号
     */
    private String uId;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "vol")
    public static class Vol {
        private List<SyncUserResult> info;
    }
}
