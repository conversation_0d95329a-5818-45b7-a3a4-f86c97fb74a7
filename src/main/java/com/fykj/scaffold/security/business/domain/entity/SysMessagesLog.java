package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 * 
 * <AUTHOR> @email ${email}
 * @date 2023-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_messages_log")
public class SysMessagesLog extends BaseEntity  {

	private static final long serialVersionUID = 1L;

	/**
	 * 发送短信的目标手机号
	 */
	@TableField("send_mobile")
    @ApiModelProperty(value = "发送短信的目标手机号")
		private String sendMobile;

	/**
	 * 发送时间
	 */
	@TableField("send_time")
    @ApiModelProperty(value = "发送时间")
		private LocalDateTime sendTime;

	/**
	 * 发送的结果
	 */
	@TableField("send_result")
    @ApiModelProperty(value = "发送的结果")
		private String sendResult;


}
