package com.fykj.scaffold.race.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.race.domain.dto.JsonPictureDto;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.domain.entity.RacePrizePhotoJson;
import com.fykj.scaffold.race.mapper.RacePrizePhotoJsonMapper;
import com.fykj.scaffold.race.service.IRacePrizePhotoJsonService;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import utils.StringUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 摄影大赛-获奖作品json
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-06-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RacePrizePhotoJsonServiceImpl extends BaseServiceImpl<RacePrizePhotoJsonMapper, RacePrizePhotoJson> implements IRacePrizePhotoJsonService {


    @Override
    public boolean saveJson(RacePrizePhotoJson entity) {
        List<JsonPictureDto> res = new ArrayList<>();
        entity.getPictureList().forEach(item -> {
            //如果item.getWorkNum()在list存在，就添加到res中
            entity.getIdList().forEach(id -> {
                if (item.getWorkNum().equals(id)) {
                    res.add(item);
                }
            });
        });
        RacePrizePhotoJson racePrizePhotoJson = new RacePrizePhotoJson();
        racePrizePhotoJson.setGradeId(entity.getGradeId());
        racePrizePhotoJson.setPictureList(res);
        save(racePrizePhotoJson);
        return true;
//        List<JsonPictureDto> objects = new ArrayList<>();
//        List<JsonPictureDto> object1 = new ArrayList<>();
//        List<JsonPictureDto> object2 = new ArrayList<>();
//        List<JsonPictureDto> object3 = new ArrayList<>();
//        List<JsonPictureDto> object4 = new ArrayList<>();
//        List<JsonPictureDto> object5 = new ArrayList<>();
//
//        entity.getPictureList().forEach(item -> {
////          if (item.getCategory().equals("新时代文明实践志愿服务")){
////              objects.add(item);
////          }
////            if (item.getCategory().equals("永远跟党走")){
////                object1.add(item);
////            }
////            if (item.getCategory().equals("魅力园区")){
////                object2.add(item);
////            }
////            if (item.getCategory().equals("活力园区")){
////                object3.add(item);
////            }
////            if (item.getCategory().equals("我为群众办实事")){
////                object4.add(item);
////            }
////            if (item.getCategory().equals("文明城市创建")){
////                object5.add(item);
////            }
//            if (item.getPrize().equals("金奖")) {
//                objects.add(item);
//            }
//            if (item.getPrize().equals("银奖")) {
//                object1.add(item);
//            }
//            if (item.getPrize().equals("最佳视觉奖")) {
//                object2.add(item);
//            }
//            if (item.getPrize().equals("优秀奖")) {
//                object3.add(item);
//            }
//            if (item.getPrize().equals("入围")) {
//                object4.add(item);
//            }
//
//
//        });
//        savePicture(objects, Long.valueOf("1666356009211719681"));
//        savePicture(object1, Long.valueOf("1666356045496643585"));
//        savePicture(object2, Long.valueOf("1666356110856482817"));
//        savePicture(object3, Long.valueOf("1666356148613607426"));
//        savePicture(object4, Long.valueOf("1666356193610100738"));
////        savePicture(objects,Long.valueOf("1666355871797932033"));
////        savePicture(object1,Long.valueOf("1666355458256334849"));
////        savePicture(object2,Long.valueOf("1666355603362476033"));
////        savePicture(object3,Long.valueOf("1666355761965887489"));
////        savePicture(object4,Long.valueOf("1666355542293409793"));
////        savePicture(object5,Long.valueOf("1666355669468901377"));
//
//        return true;
    }

    @Override
    public List<Photo> getPrizePhoto(Long gradeId) {
        RacePrizePhotoJson photoJson = lambdaQuery().eq(RacePrizePhotoJson::getGradeId, gradeId).one();
        List<Photo> res = new ArrayList<>();
        if (photoJson == null) {
            return res;
        }
        photoJson.getPictureList().forEach(item -> {
            Photo photo = new Photo();
            photo.setName(item.getWorkName());
            if(StringUtil.isNotEmpty(item.getWorkNum())){
                photo.setNumberStr(item.getWorkNum());
            }

//            item.getPhotos().forEach(it ->{

//                it.setPath(it.getPath());
//            });
            //todo 照片路径待处理
            photo.setPictureList(item.getPhotos());
            photo.setPlace(item.getPhotoAddress());
            photo.setAuthor(item.getAuthor());
            res.add(photo);
        });
        return res;
    }

    @Override
    public RacePrizePhotoJson getJson(Long gradeId) {
        return lambdaQuery().eq(RacePrizePhotoJson::getGradeId, gradeId).one();
    }

    private void savePicture(List<JsonPictureDto> list, Long gradeId) {
        RacePrizePhotoJson racePrizePhotoJson = new RacePrizePhotoJson();
        racePrizePhotoJson.setGradeId(gradeId);
        racePrizePhotoJson.setPictureList(list);
        save(racePrizePhotoJson);
    }


    @Override
    public RacePrizePhotoJson getById(Serializable id) {
        RacePrizePhotoJson res = super.getById(id);
        res.getPictureList().forEach(item -> {
            res.setIds(StringUtil.isEmpty(res.getIds()) ? item.getWorkNum() : res.getIds().concat(",").concat(item.getWorkNum()));
        });
        return res;
    }

    @Override
    public IPage<RacePrizePhotoJson> page(BaseParams params) {
        IPage<RacePrizePhotoJson> page = super.page(params);
        page.getRecords().forEach(item -> {
            item.getPictureList().forEach(picture -> {
                item.setIds(StringUtil.isEmpty(item.getIds()) ? picture.getWorkNum() : item.getIds().concat(",").concat(picture.getWorkNum()));
            });
        });
        return page;
    }
}