package com.fykj.scaffold.support.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 文件类型类别
 * <AUTHOR> 2019.5.18
 *
 */
public class FileTypeUtil {



	private FileTypeUtil() {
	};

	private static final Map<String, String> FILE_TYPE_MAP;

	static {
		FILE_TYPE_MAP = new ConcurrentHashMap<>();

//		FILE_TYPE_MAP.put("ffd8ffe000104a464946", "jpg"); // JPEG (jpg)
		FILE_TYPE_MAP.put("ffd8ffe", "jpg"); // JPEG (jpg)
		FILE_TYPE_MAP.put("FFD8FF", "jpg"); // JPEG (jpg)
		FILE_TYPE_MAP.put("89504e470d0a1a0a", "png"); // PNG (png)
		FILE_TYPE_MAP.put("4749463839612602", "gif"); // GIF (gif)
		FILE_TYPE_MAP.put("49492a0022710500", "tif"); // TIFF (tif)
		FILE_TYPE_MAP.put("424d228c01000000", "bmp"); // 16色位图(bmp)
		FILE_TYPE_MAP.put("424d824009000000", "bmp"); // 24位位图(bmp)
		FILE_TYPE_MAP.put("424d8e1b03000000", "bmp"); // 256色位图(bmp)
		FILE_TYPE_MAP.put("4143313031350000", "dwg"); // CAD (dwg)
		FILE_TYPE_MAP.put("3c21444f43545950", "html"); // HTML (html)
		FILE_TYPE_MAP.put("3c21646f63747970", "htm"); // HTM (htm)
		FILE_TYPE_MAP.put("48544d4c207b0d0a", "css"); // css
		FILE_TYPE_MAP.put("696b2e71623d696b", "js"); // js
		FILE_TYPE_MAP.put("7b5c727466315c61", "rtf"); // Rich Text Format (rtf)
		FILE_TYPE_MAP.put("3842505300010000", "psd"); // Photoshop (psd)
		FILE_TYPE_MAP.put("46726f6d3a203d3f", "eml"); // Email [Outlook Express 6] (eml)
		FILE_TYPE_MAP.put("d0cf11e0a1b11ae1", "doc"); // MS Excel 注意：word、msi 和 excel的文件头一样
		FILE_TYPE_MAP.put("d0cf11e0a1b11ae1", "vsd"); // Visio 绘图
		FILE_TYPE_MAP.put("5374616E64617264", "mdb"); // MS Access (mdb)
		FILE_TYPE_MAP.put("252150532D41646F", "ps");
		FILE_TYPE_MAP.put("255044462d312e", "pdf"); // Adobe Acrobat (pdf)
		FILE_TYPE_MAP.put("2e524d4600000012", "rmvb"); // rmvb/rm相同
		FILE_TYPE_MAP.put("464c560105000000", "flv"); // flv与f4v相同
		FILE_TYPE_MAP.put("0000002066747970", "mp4");
		FILE_TYPE_MAP.put("0000001866747970", "mp4");
		FILE_TYPE_MAP.put("4944330300000000", "mp3");
		FILE_TYPE_MAP.put("4944330400000000", "mp3"); // 49443304000000000023545353450000000F0000034C61766635372E
		FILE_TYPE_MAP.put("000001ba21000100", "mpg"); //
		FILE_TYPE_MAP.put("3026b2758e66cf11", "wmv"); // wmv与asf相同
		FILE_TYPE_MAP.put("52494646e2780700", "wav"); // Wave (wav)
		FILE_TYPE_MAP.put("52494646d07d6007", "avi");
		FILE_TYPE_MAP.put("4d54686400000006", "mid"); // MIDI (mid)
		FILE_TYPE_MAP.put("526172211a0700cf", "rar");// WinRAR
		FILE_TYPE_MAP.put("52617221", "rar");// WinRAR
		FILE_TYPE_MAP.put("235468697320636f", "ini");
		FILE_TYPE_MAP.put("504B03040a000000", "jar");
		FILE_TYPE_MAP.put("504B030414000800", "jar");
		FILE_TYPE_MAP.put("504B030414000600", "docx");// docx文件
		FILE_TYPE_MAP.put("504B0304140006000800", "xlsx");// docx文件
		FILE_TYPE_MAP.put("D0CF11E0A1B11AE10", "xls");// xls文件
		FILE_TYPE_MAP.put("504B0304", "zip");
		FILE_TYPE_MAP.put("4d5a9000030000000400", "exe");// 可执行文件
		FILE_TYPE_MAP.put("3c25402070616765206c", "jsp");// jsp文件
		FILE_TYPE_MAP.put("4d616e69666573742d56", "mf");// MF文件
		FILE_TYPE_MAP.put("3c3f786d6c2076657273", "xml");// xml文件
		FILE_TYPE_MAP.put("494e5345525420494e54", "sql");// xml文件
		FILE_TYPE_MAP.put("7061636b616765207765", "java");// java文件
		FILE_TYPE_MAP.put("406563686f206f66660d", "bat");// bat文件
		FILE_TYPE_MAP.put("1f8b0800000000000000", "gz");// gz文件
		FILE_TYPE_MAP.put("6c6f67346a2e726f6f74", "properties");// bat文件
		FILE_TYPE_MAP.put("cafebabe0000002e0041", "class");// bat文件
		FILE_TYPE_MAP.put("49545346030000006000", "chm");// bat文件
		FILE_TYPE_MAP.put("04000000010000001300", "mxp");// bat文件
		FILE_TYPE_MAP.put("d0cf11e0a1b11ae10000", "wps");// WPS文字wps、表格et、演示dps都是一样的
		FILE_TYPE_MAP.put("6431303a637265617465", "torrent");
		FILE_TYPE_MAP.put("6D6F6F76", "mov"); // Quicktime (mov)
		FILE_TYPE_MAP.put("FF575043", "wpd"); // WordPerfect (wpd)
		FILE_TYPE_MAP.put("CFAD12FEC5FD746F", "dbx"); // Outlook Express (dbx)
		FILE_TYPE_MAP.put("2142444E", "pst"); // Outlook (pst)
		FILE_TYPE_MAP.put("AC9EBD8F", "qdf"); // Quicken (qdf)
		FILE_TYPE_MAP.put("E3828596", "pwl"); // Windows Password (pwl)
		FILE_TYPE_MAP.put("2E7261FD", "ram"); // Real Audio (ram)

		FILE_TYPE_MAP.put("52494646", "riff");//riff格式
	}

	/**
	 * 增加文件类型映射<br>
	 * 如果已经存在将覆盖之前的映射
	 *
	 * @param fileStreamHexHead 文件流头部Hex信息
	 * @param extName 文件扩展名
	 * @return 之前已经存在的文件扩展名
	 */
	public static String putFileType(String fileStreamHexHead, String extName) {
		return FILE_TYPE_MAP.put(fileStreamHexHead.toLowerCase(), extName);
	}

	/**
	 * 移除文件类型映射
	 *
	 * @param fileStreamHexHead 文件流头部Hex信息
	 * @return 移除的文件扩展名
	 */
	public static String removeFileType(String fileStreamHexHead) {
		return FILE_TYPE_MAP.remove(fileStreamHexHead.toLowerCase());
	}

	/**
	 * 根据文件流的头部信息获得文件类型
	 *
	 * @param fileStreamHexHead 文件流头部16进制字符串
	 * @return 文件类型，未找到为<code>null</code>
	 */
	public static String getType(String fileStreamHexHead) {
		for (Entry<String, String> fileTypeEntry : FILE_TYPE_MAP.entrySet()) {
			if(StrUtil.startWithIgnoreCase(fileStreamHexHead, fileTypeEntry.getKey())) {
				return fileTypeEntry.getValue();
			}
		}
		return null;
	}

	/**
	 * 根据文件流的头部信息获得文件类型
	 *
	 * @param in {@link InputStream}
	 * @return 类型，文件的扩展名，未找到为<code>null</code>
	 * @throws IORuntimeException 读取流引起的异常
	 */
	public static String getType(InputStream in) throws IORuntimeException {
		return getType(IoUtil.readHex(in,28,false));
	}

	/**
	 * 根据文件流的头部信息获得文件类型
	 *
	 * @param file 文件 {@link File}
	 * @return 类型，文件的扩展名，未找到为<code>null</code>
	 * @throws IORuntimeException 读取文件引起的异常
	 */
	public static String getType(File file) throws IORuntimeException {
		FileInputStream in = null;
		try {
			in = IoUtil.toStream(file);
			return getType(in);
		} finally {
			IoUtil.close(in);
		}
	}

	/**
	 * 通过路径获得文件类型
	 *
	 * @param path 路径，绝对路径或相对ClassPath的路径
	 * @return 类型
	 * @throws IORuntimeException 读取文件引起的异常
	 */
	public static String getTypeByPath(String path) throws IORuntimeException {
		return getType(FileUtil.file(path));
	}

}
