package com.fykj.scaffold.portal_website.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 新闻活动选择模型
 *

 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("新闻活动选择模型")
public class ActivityForNewsDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long value;

    @ApiModelProperty(value = "名称")
    private String label;

}
