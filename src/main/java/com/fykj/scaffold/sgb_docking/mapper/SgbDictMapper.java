package com.fykj.scaffold.sgb_docking.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 苏州志愿者平台字典数据Mapper接口
 */
public interface SgbDictMapper extends BaseMapper<SgbDict> {
    
    /**
     * 清空所有数据
     */
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
    
    /**
     * 清空指定类型的字典数据
     * @param dictType 字典类型
     */
    @InterceptorIgnore(blockAttack = "true")
    void clearByType(@Param("dictType") String dictType);
    
    /**
     * 根据字典类型查询字典数据列表
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<SgbDict> selectByDictType(@Param("dictType") String dictType);
    
    /**
     * 根据字典编码查询字典数据
     * @param dictCode 字典编码
     * @return 字典数据
     */
    SgbDict selectByDictCode(@Param("dictCode") String dictCode);
} 