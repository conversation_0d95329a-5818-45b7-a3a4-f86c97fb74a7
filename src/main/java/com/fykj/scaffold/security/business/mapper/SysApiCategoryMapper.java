package com.fykj.scaffold.security.business.mapper;

import com.fykj.scaffold.security.business.domain.dto.CascaderDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 接口目录表
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface SysApiCategoryMapper extends BaseMapper<SysApiCategory> {

    List<CascaderDto> findByParent(Long parentId);
}
