package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.SysWeakPw;
import com.fykj.scaffold.security.business.domain.params.SysWeakPwParams;
import com.fykj.scaffold.security.business.service.ISysWeakPwService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * 用户弱口令列表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/admin/sys/weak/pw")
public class SysWeakPwController extends BaseController<ISysWeakPwService, SysWeakPw, SysWeakPwParams> {

}
