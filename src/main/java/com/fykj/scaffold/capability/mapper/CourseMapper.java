package com.fykj.scaffold.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.capability.domain.dto.CourseListDTO;
import com.fykj.scaffold.capability.domain.entity.Course;
import com.fykj.scaffold.capability.domain.params.CourseParams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程管理 Mapper 接口
 * 表对应：cu_course
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface CourseMapper extends BaseMapper<Course> {
    /**
     * 分页查询课程列表
     * @param page 分页对象
     * @param params 参数封装类
     * @return 返回课程列表信息
     */
    List<CourseListDTO> query(IPage<CourseListDTO> page, @Param("params") CourseParams params);

    /**
     * 分页查询课程列表
     * @param params 参数封装类
     * @return 返回课程列表信息
     */
    List<CourseListDTO> query(@Param("params") CourseParams params);

    /**
     * 查询我的课程列表
     * @param page 分页对象
     * @param params 参数封装类
     * @return 返回课程列表信息
     */
    List<CourseListDTO> queryMine(IPage<CourseListDTO> page, @Param("params") CourseParams params);

    /**
     * 查询我的课程列表
     * @param params 参数封装类
     * @return 返回课程列表信息
     */
    List<CourseListDTO> queryMine(@Param("params") CourseParams params);

    /**
     * 获取需要进行报名提醒的课程
     * @param start
     * @param end
     * @return
     */
    List<Course> getNeedRemindCourses(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);
}
