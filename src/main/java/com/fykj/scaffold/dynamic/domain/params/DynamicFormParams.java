package com.fykj.scaffold.dynamic.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 模板信息-查询参数
 *
 * @date 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("模板信息-查询参数")
public class DynamicFormParams extends BaseParams {

    private static final long serialVersionUID = 2360923473443834325L;

    @ApiModelProperty("关键字查询，模糊匹配")
    @MatchType(value = QueryType.LIKE)
    private String keyword;

    @ApiModelProperty("模块code")
    @MatchType(value = QueryType.EQ)
    private String moduleCode;

    @ApiModelProperty(value = "状态")
    private Boolean statusFlag;

}
