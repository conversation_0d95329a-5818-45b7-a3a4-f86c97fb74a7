package com.fykj.scaffold.zsq_docking.push_mq.event;


import com.fykj.scaffold.zsq_docking.push_mq.dto.ActZyzSyncBelongRegionDto;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegionAllDockingEvent extends BaseDockingMqEvent {

    /**
     * 对接记录 id
     */
    private Long dockingRecordId;

    /**
     * 社区 id
     */
    private Long regionId;

    /**
     * 消息 id
     */
    private String operation;

    /**
     * 消息 id
     */
    private String zwtUserId;

    /**
     * 上/下架
     */
    private Boolean onShelf;

    /**
     * 所属所有社区列表信息
     */
    private List<ActZyzSyncBelongRegionDto> regionAllList;

    /**
     * 志愿者平台用户 id
     */
    private Serializable zyzUserId;

}
