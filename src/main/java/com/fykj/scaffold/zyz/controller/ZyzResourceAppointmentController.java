package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.AppointmentAuditExportDto;
import com.fykj.scaffold.zyz.domain.dto.ResourceAppointmentDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzResourceAppointment;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceAppointmentParams;
import com.fykj.scaffold.zyz.service.IZyzResourceAppointmentService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.annotations.Api;
/**
 * 资源预约表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-03-02
 */
@RestController
@RequestMapping("/admin/zyz/resource/appointment")
@Api(tags = "资源预约接口")
public class ZyzResourceAppointmentController extends BaseController<IZyzResourceAppointmentService, ZyzResourceAppointment,ZyzResourceAppointmentParams> {

    @SysLogMethod("新增预约")
    @ApiOperation("新增预约")
    @PostMapping({"/saveAppointment"})
    public Result saveAppointment(@RequestBody ZyzResourceAppointment entity) {
        boolean result = this.baseService.saveAppointment(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @ApiOperation("资源预约审核分页查询")
    @PostMapping({"/pagesForAudit"})
    public JsonResult<IPage<ZyzResourceAppointment>> pagesForAudit(@RequestBody(required = false) ZyzResourceAppointmentParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceAppointmentParams();
        }
        IPage<ZyzResourceAppointment> result = this.baseService.pagesForAudit(params);
        DesensitiseUtil.desensitise(result.getRecords());
        return new JsonResult<>(result);
    }

    @AuditLog("资源预约审核")
    @ApiOperation("资源预约审核")
    @PostMapping("/audit")
    public Result audit(@RequestBody ResourceAppointmentDto appointmentDto) {
        baseService.audit(appointmentDto);
        return OK;
    }

    @AuditLog("资源预约审核导出")
    @ApiOperation("资源预约审核导出")
    @PostMapping({"/exportForAppointmentAudit"})
    public void exportForAppointmentAudit(@RequestBody(required = false) ZyzResourceAppointmentParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceAppointmentParams();
        }
        List<AppointmentAuditExportDto> res = baseService.listForAppointmentAudit(params);
        ExcelUtil.writeExcel(res, "资源预约审核列表", AppointmentAuditExportDto.class);
    }
//    @ApiOperation("判断是否预约过该资源")
//    @GetMapping({"/validateAppointed"})
//    public JsonResult<Boolean> validateAppointed(@RequestParam String resourceId) {
//        return new JsonResult<>(this.baseService.validateAppointed(resourceId));
//    }

    @ApiOperation("查询资源预约记录")
    @GetMapping({"/getAppointmentByResourceId"})
    public JsonResult<List<ZyzResourceAppointment>> getAppointmentByResourceId(Long resourceId) {
        List<ZyzResourceAppointment> result = this.baseService.getAppointmentByResourceId(resourceId);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }

    @ApiOperation("查询资源预约记录")
    @GetMapping({"/getAppointmentById"})
    public Result getAppointmentById(Long id) {
        ZyzResourceAppointment result = this.baseService.getAppointmentById(id);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }

    @AuditLog("评价")
    @ApiOperation("评价")
    @GetMapping("/updateRemark")
    public Result updateRemark(Long id, BigDecimal evaluateStar, String evaluateRemark) {
        if (!baseService.updateRemark(id, evaluateRemark, evaluateStar)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }
}
