package com.fykj.scaffold.sgb_docking.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbRegion;

/**
 * 苏州志愿者平台区划信息Mapper接口
 */
public interface SgbRegionMapper extends BaseMapper<SgbRegion> {
    
    /**
     * 清空所有数据
     */
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
}