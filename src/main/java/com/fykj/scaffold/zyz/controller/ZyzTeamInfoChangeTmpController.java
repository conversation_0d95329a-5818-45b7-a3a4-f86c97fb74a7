package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzTeamInfoChangeTmp;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamInfoChangeTmpParams;
import com.fykj.scaffold.zyz.service.IZyzTeamInfoChangeTmpService;
import fykj.microservice.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 志愿者团队信息变更临时表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-08-22
 */
@Slf4j
@RestController
@RequestMapping("/admin/zyz/team_info_change")
public class ZyzTeamInfoChangeTmpController extends BaseController<IZyzTeamInfoChangeTmpService, ZyzTeamInfoChangeTmp, ZyzTeamInfoChangeTmpParams> {

}
