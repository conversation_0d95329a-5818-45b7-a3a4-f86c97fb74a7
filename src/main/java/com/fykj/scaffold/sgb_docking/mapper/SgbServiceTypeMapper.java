package com.fykj.scaffold.sgb_docking.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbServiceType;

/**
 * 苏州志愿者平台服务领域Mapper接口
 */
public interface SgbServiceTypeMapper extends BaseMapper<SgbServiceType> {
    
    /**
     * 清空所有数据
     */
    @InterceptorIgnore(blockAttack = "true")
    void clearAll();
} 