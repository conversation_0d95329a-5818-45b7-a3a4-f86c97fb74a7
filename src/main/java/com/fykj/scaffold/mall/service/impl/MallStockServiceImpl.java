package com.fykj.scaffold.mall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.entity.MallStock;
import com.fykj.scaffold.mall.domain.entity.MallStockNumChange;
import com.fykj.scaffold.mall.domain.params.MallStockParams;
import com.fykj.scaffold.mall.mapper.MallStockMapper;
import com.fykj.scaffold.mall.service.IMallScoreGoodsService;
import com.fykj.scaffold.mall.service.IMallStockNumChangeService;
import com.fykj.scaffold.mall.service.IMallStockService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料信息管理服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Service
public class MallStockServiceImpl extends BaseServiceImpl<MallStockMapper, MallStock> implements IMallStockService {

    @Autowired
    private IMallStockNumChangeService mallStockNumChangeService;

    @Autowired
    private IMallScoreGoodsService goodsService;

    @Override
    public IPage<MallStock> getPage(MallStockParams params) {
        if (params == null) {
            params = new MallStockParams();
        }
        IPage<MallStock> iPage = baseMapper.getPage(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public void reduceStock(long stockId, int changeNum, String remark) {
        changeStock(stockId, -Math.abs(changeNum), remark);
    }

    @Override
    public void addStock(long stockId, int changeNum, String remark) {
        changeStock(stockId, Math.abs(changeNum), remark);
    }

    @Override
    public void addStockTotalQuantity(String sku, int num) {
        MallStock stock = lambdaQuery().eq(MallStock::getSku, sku).one();
        stock.setInventoryTotalQuantity(stock.getInventoryTotalQuantity() + Math.abs(num));
        updateById(stock);
    }

    @Override
    public void reduceStockTotalQuantity(String sku, int num) {
        MallStock stock = lambdaQuery().eq(MallStock::getSku, sku).one();
        stock.setInventoryTotalQuantity(stock.getInventoryTotalQuantity() - Math.abs(num));
        updateById(stock);
    }

    @Override
    public void changeStock(long stockId, int stockNum) {
        if (stockNum < 0) {
            changeStock(stockId, stockNum, "物料出库");
        } else {
            changeStock(stockId, stockNum, "物料入库");
        }
    }

    @Override
    public void changeStock(long stockId, int changeNum, String remark) {
        if (changeNum == 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请输入具体库存变动数量");
        }
        MallStock stock = getById(stockId);
        if (stock.getInventoryQuantity() + changeNum < 0) {
            throw new BusinessException(ResultCode.FAIL, "请核对，物料数量不足！");
        }
        logStockChange(stockId, changeNum, remark);
        final int newRemainStockNum = stock.getInventoryQuantity() + changeNum;
        stock.setInventoryQuantity(newRemainStockNum);
        //重新计算可用总物料库存
        stock.setInventoryTotalQuantity(newRemainStockNum + goodsService.sumGoodsCountBySku(stock.getSku()));
        updateById(stock);
    }

    private void logStockChange(long stockId, int stockNum, String remark) {
        MallStock stock = getById(stockId);
        MallStockNumChange stockNumChange = new MallStockNumChange();
        stockNumChange.setChangeBeforeNum(stock.getInventoryQuantity());
        stockNumChange.setChangeNum(Math.abs(stockNum));
        stockNumChange.setChangeAfterNum(stock.getInventoryQuantity() + stockNum);
        stockNumChange.setOperateDate(LocalDateTime.now());
        stockNumChange.setStockId(stockId);
        stockNumChange.setOperateUserId((Long) Oauth2Util.getUserId());
        stockNumChange.setOperateUserName(StringUtil.isNotEmpty(Oauth2Util.getUserId()) ? Oauth2Util.getUser().getNickName() : "兑换机器");
        stockNumChange.setRemark(remark);
        if (stockNum < 0) {
            stockNumChange.setChangeType(MallCons.MALL_SCORE_GOODS_NUM_CHANGE_TYPE_OUT);
        } else {
            stockNumChange.setChangeType(MallCons.MALL_SCORE_GOODS_NUM_CHANGE_TYPE_IN);
        }
        mallStockNumChangeService.save(stockNumChange);
    }

    @Override
    public MallStock saveMallStock(MallStock mallStock) {
        if (lambdaQuery().eq(MallStock::getSku, mallStock.getSku()).count() > 0) {
            throw new BusinessException(ResultCode.FAIL, "物料编码已存在");
        }
        if (!Cons.RoleCode.ROLE_CODE_ASSOCIATION_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            mallStock.setOrgCode(Oauth2Util.getOrgCode());
        }
        int initStockNum = mallStock.getInventoryTotalQuantity();
        mallStock.setInventoryQuantity(0);
        mallStock.setInventoryTotalQuantity(0);
        save(mallStock);

        if (initStockNum > 0) {
            //物料库存变更
            addStock(mallStock.getId(), Math.abs(initStockNum), "物料初始化");
        }
        return mallStock;
    }

    @Override
    public List<MallStock> getStockByOrg() {
        return lambdaQuery().orderByDesc(BaseEntity::getCreateDate).eq(MallStock::getOrgCode, Oauth2Util.getOrgCode()).list();
    }
}
