package com.fykj.volunteer.sync.util.domain.join_team;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 同步团队的结果
 * <msgs>
 *   <info>
 *     <org_outinfo>
 *       <uniquecode>sb12382138</uniquecode>
 *       <code>SUCCESS</code>
 *       <msg>同步成功</msg>
 *       <spcode>130017</spcode>
 *     </org_outinfo>
 *   </info>
 * </msgs>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class JoinTeamResult {
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniquecode;
    /**
     * 消息
     */
    private String msg;
    /**
     * 业务状态码
     */
    private String spcode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "org_outinfo")
    public static class OrgOutinfo {
        private List<JoinTeamResult> org_outinfo;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "msgs")
    public static class Msgs {
        private List<OrgOutinfo> info;
    }
}
