package com.fykj.scaffold.message.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.param.MsgSendParams;
import com.fykj.scaffold.message.service.impl.MsgSendServiceImpl;
import com.fykj.scaffold.support.utils.MessagesApiUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

import javax.servlet.http.HttpServletResponse;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@RestController
@RequestMapping("/admin/msg_send")
public class MsgSendController extends BaseController<MsgSendServiceImpl, MsgSend, MsgSendParams> {

    @ApiOperation("消息发送")
    @PostMapping("/sendMessage")
    public Result sendMessage(@RequestParam("sendId") Long sendId) {
        return new JsonResult<>(baseService.sendMessage(sendId));
    }

    @ApiOperation("消息发送分页查询")
    @PostMapping("/pageForMsgSend")
    public Result pageForMsgSend(@RequestBody(required = false) MsgSendParams params) {
        IPage<MsgSend> result = baseService.pageForMsgSend(params);
        return new JsonResult<>(result);
    }

    /**
     * 发送对象excel模版下载
     */
    @ApiOperation("发送对象excel模版下载")
    @GetMapping("/receiverExcelTemplate")
    public void receiverExcelTemplate(HttpServletResponse response) {
        downloadFile(response, "/data/excelTemplate/receiver_template.xlsx");
    }

    /**
     * 批量导入发送对象
     */
    @ApiOperation("批量导入发送对象")
    @PostMapping("/receiverImport")
    public Result excelImport(@RequestParam(name = "excel") MultipartFile excel, @RequestParam(name = "siteUse") Boolean siteUse, @RequestParam(required = false, name = "sendId")Long sendId) {
        if (excel == null || excel.isEmpty()) {
            return new Result(ResultCode.BAD_REQUEST.code(), "请选择要上传的文件");
        }

        return baseService.excelImport(excel, siteUse, sendId);
    }

    /**
     * 获取短信平台相关数据
     */
    @ApiOperation("获取短信平台相关数据")
    @GetMapping("/getSmsPlatformInfoData")
    public Result getSmsPlatformInfoData() {
        MessagesApiUtil.OverageResult data;
        try {
            data = MessagesApiUtil.overage();
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "获取短信平台相关数据失败！");
        }
        if (MessagesApiUtil.ReturnStatus.FAIL.equals(data.getReturnStatus())) {
            throw new BusinessException(ResultCode.FAIL, data.getMessage());
        }
        return new JsonResult<>(data);
    }

    @ApiOperation("接收者维护提交")
    @GetMapping("/receiverMaintainSubmit")
    public Result receiverMaintainSubmit(@RequestParam(required = false) String filePath, @RequestParam(required = false) Long sendId) {
        baseService.receiverMaintainSubmit(filePath, sendId);
        return OK;
    }
}
