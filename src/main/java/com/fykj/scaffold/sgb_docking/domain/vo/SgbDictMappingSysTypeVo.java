package com.fykj.scaffold.sgb_docking.domain.vo;

import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 系统字典类型聚合VO
 */
@Data
@ApiModel("系统字典类型聚合VO")
public class SgbDictMappingSysTypeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统字典类型
     */
    @ApiModelProperty("系统字典类型")
    @DictTrans(transTo = "text")
    private String id;

    /**
     * 系统字典类型名称（翻译后）
     */
    @ApiModelProperty("系统字典类型名称")
    private String text;

    public SgbDictMappingSysTypeVo() {
    }

    public SgbDictMappingSysTypeVo(String id, String text) {
        this.id = id;
        this.text = text;
    }
}
