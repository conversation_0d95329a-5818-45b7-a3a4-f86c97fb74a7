package com.fykj.scaffold.security.business.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 消息模版
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MsgTemplateParams extends BaseParams {

    @MatchType(value = QueryType.LIKE, fieldName = {"code", "title"})
    @ApiModelProperty("模版code/title模糊查询")
    private String keyword;

    @MatchType(value = QueryType.EQ, fieldName = "status")
    @ApiModelProperty("状态")
    private Boolean status;

    @MatchType(value = QueryType.EQ, fieldName = "push_way")
    @ApiModelProperty("推送方式")
    private String pushWay;

    @MatchType(value = QueryType.EQ, fieldName = "module")
    @ApiModelProperty("模块code")
    private String module;
}
