package com.fykj.scaffold.mall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.dto.AddPointRecordDto;
import com.fykj.scaffold.mall.domain.dto.PointContributePersonDto;
import com.fykj.scaffold.mall.domain.dto.PointContributeSumDto;
import com.fykj.scaffold.mall.domain.entity.ZyzPointContributeRecord;
import com.fykj.scaffold.mall.domain.params.ZyzPointContributeRecordParams;
import com.fykj.scaffold.mall.mapper.ZyzPointContributeRecordMapper;
import com.fykj.scaffold.mall.service.IZyzPointContributeRecordService;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * 积分商城-积分捐赠记录
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzPointContributeRecordServiceImpl extends BaseServiceImpl<ZyzPointContributeRecordMapper, ZyzPointContributeRecord> implements IZyzPointContributeRecordService {
    @Autowired
    private IZyzVolunteerService volunteerService;

    @Override
    public IPage<ZyzPointContributeRecord> getPage(ZyzPointContributeRecordParams params) {
        if (params == null) {
            params = new ZyzPointContributeRecordParams();
        }
        IPage<ZyzPointContributeRecord> iPage = baseMapper.getPageOrList(params.getPage(), params);
        DictTransUtil.trans(iPage.getRecords());
        return iPage;
    }

    @Override
    public PointContributeSumDto getSum(ZyzPointContributeRecordParams params) {
        List<ZyzPointContributeRecord> list = baseMapper.getPageOrList(params);
        PointContributeSumDto result = new PointContributeSumDto();
        if (list == null || list.isEmpty()) {
            return result;
        }

        // 统计捐赠人数（根据志愿者ID去重）
        long personNum = list.stream()
                .map(ZyzPointContributeRecord::getVolunteerId)
                .distinct()
                .count();

        // 捐赠人次就是记录总数
        int contributeSum = list.size();

        // 计算捐赠积分总数
        BigDecimal contributePointSum = list.stream()
                .map(ZyzPointContributeRecord::getContributePoint)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 设置结果
        result.setPersonNum((int) personNum);
        result.setContributeSum(contributeSum);
        result.setContributePointSum(contributePointSum);

        return result;

    }

    @Override
    public boolean savePointContribute(BigDecimal contributePoint, String orgCode, String orgName) {
        ZyzVolunteer volunteer = volunteerService.getCurrentUserVolunteerInfo();
        ZyzPointContributeRecord record = new ZyzPointContributeRecord();
        record.setContributePoint(contributePoint);
        record.setCertificateId(volunteer.getCertificateId());
        record.setContributeDate(LocalDateTime.now());
        record.setOrgCode(orgCode);
        record.setOrgName(orgName);
        record.setPhone(volunteer.getPhone());
        record.setVolunteerId(volunteer.getId());
        record.setVolunteerName(volunteer.getName());
        save(record);
        String remark = "志愿者：" + volunteer.getName() + ",向" + orgName + "捐赠了" + contributePoint + "分";
        //减少积分
        AddPointRecordDto addPointRecordDto = AddPointRecordDto
                .builder()
                .changePoint(contributePoint)
                .bizId(record.getId())
                .bizType(MallCons.POINT_CHANGE_BIZ_TYPE_CONTRIBUTE)
                .remark(remark)
                .volunteerId(volunteer.getId())
                .build();
        volunteerService.reducePoint(addPointRecordDto);
        return true;
    }

    @Override
    public IPage<PointContributePersonDto> pageForPointContributePerson(BaseParams params) {
        if (params == null) {
            params = new BaseParams();
        }
        Page<PointContributePersonDto> page = Page.of(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForPointContributePerson(page);
    }

    @Override
    public List<Object> getContributeSum() {
        BigDecimal totalContributePoint = baseMapper.getTotalContributePoint();
        Integer totalContributePerson = baseMapper.getTotalContributePerson();
        return Arrays.asList(totalContributePoint, totalContributePerson);
    }
}
