package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMember;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMemberResult;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 活动加入招募同步
 */
@Component
@Slf4j
public class ActivityMemberSyncer {

    @Autowired
    private IZyzActivityApplyService applyService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private IZyzSyncLogService logService;

    public ZyzSyncLog syncOne(ActSyncRecordDto applyRecord) {
        SyncActMember syncActMember = generateParams(applyRecord);

        SyncActMemberResult syncActMemberResult = ApiWebService.syncActMember(syncActMember);

        return dealSyncResult(syncActMemberResult, syncActMember, applyRecord);
    }

    public void syncBatch(List<ActSyncRecordDto> applyRecords) {
        if (CollectionUtil.isEmpty(applyRecords)) {
            return;
        }
        List<SyncActMember> syncActMembers = new ArrayList<>();
        applyRecords.forEach(it -> {
            syncActMembers.add(generateParams(it));
        });

        List<SyncActMemberResult> syncActMemberResults = ApiWebService.syncActMemberBatch(syncActMembers);
        syncActMemberResults.forEach(it -> {
            transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(@NonNull TransactionStatus transactionStatus) {
                try {
                    ActSyncRecordDto applyRecord = applyRecords.stream().filter(im -> it.getUniCode().equals(String.valueOf(im.getApplyId()))).findAny().orElse(new ActSyncRecordDto());
                    SyncActMember params = syncActMembers.stream().filter(im -> it.getUniCode().equals(im.getUniCode())).findAny().orElse(new SyncActMember());
                    dealSyncResult(it, params, applyRecord);
                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();
                    log.error("id为[{}]的活动申请同步异常！异常原因：{}", it.getUniCode(), e.getMessage(), e);
                }
                }
            });
        });
    }

    private SyncActMember generateParams(ActSyncRecordDto applyRecord) {
        SyncActMember syncActMember = new SyncActMember();
        syncActMember.setUniCode(String.valueOf(applyRecord.getApplyId()));
        syncActMember.setActId(applyRecord.getActRecruitSyncId());
        syncActMember.setIdCode(applyRecord.getVolunteerCertId());
//        syncActMember.setActId(null);
//        syncActMember.setIdCode("320586199508082000");
        syncActMember.setJoinTime(applyRecord.getApplyTime().format(DateTimeFormatter.ofPattern(Cons.DATE_FORMAT)));
        return syncActMember;
    }

    private ZyzSyncLog dealSyncResult(SyncActMemberResult result, SyncActMember params, ActSyncRecordDto applyRecord) {
        ZyzActivityApply apply = applyService.getById(applyRecord.getApplyId());
        apply.setApplySyncTime(LocalDateTime.now());
        apply.setApplySyncRemark(result.getMsg());
        //成功
        if (result.getMsg().contains("招募成功")) {
            apply.setApplyIsSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (result.getMsg().contains("已加入")) {
            apply.setApplyIsSync(Cons.PlatformSyncState.SUCCESS);
            apply.setApplySyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            apply.setApplyIsSync(Cons.PlatformSyncState.FAILURE);
        }
        String startTime = applyRecord.getActStartTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        String endTime = applyRecord.getActEndTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        // 启用单独事务处理日志
        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.ACTIVITY_MEMBER_SYNC, result.getCode(), result.getMsg()
                , IdUtil.getSnowflake(1, 1).nextId(), apply.getId(), applyRecord.getVolunteerName() + "加入【" + applyRecord.getActName() + "（" + startTime + "--" + endTime + "）" + "】活动招募"
                , JSONUtil.toJsonStr(params), JSONUtil.toJsonStr(result));

        // 回写业务
        applyService.updateById(apply);

        return log;
    }
}
