package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.entity.SysUserAddress;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_exchange")
public class MallExchange extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @TableField("order_num")
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 类型数据字典值标明是机器还是线上
     */
    @TableField("exchange_type")
    @ApiModelProperty(value = "类型数据字典值标明是机器还是线上")
    @DictTrans(transTo = "exchangeTypeText")
    private String exchangeType;

    @TableField(exist = false)
    @ApiModelProperty(value = "类型数据Text")
    private String exchangeTypeText;
    /**
     * 兑换人用户id
     */
    @TableField("exchange_volunteer_id")
    @ApiModelProperty(value = "兑换人用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeVolunteerId;

    /**
     * 兑换人用户名称
     */
    @TableField("exchange_user_name")
    @ApiModelProperty(value = "兑换人用户名称")
    private String exchangeUserName;

    /**
     * 兑换人手机号码
     */
    @TableField("exchange_user_link_phone")
    @ApiModelProperty(value = "兑换人手机号码")
    private String exchangeUserLinkPhone;

    /**
     * 商品id
     */
    @TableField("exchange_goods_id")
    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeGoodsId;

    /**
     * 兑换积分（锁定积分）
     */
    @TableField("exchange_point")
    @ApiModelProperty(value = "兑换积分（锁定积分）")
    private BigDecimal exchangePoint;

    /**
     * 积分单价
     */
    @TableField("goods_point")
    @ApiModelProperty(value = "积分单价")
    private BigDecimal goodsPoint;

    /**
     * 兑换数量
     */
    @TableField("exchange_num")
    @ApiModelProperty(value = "兑换数量")
    private Integer exchangeNum;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 机器编号
     */
    @TableField("machine_no")
    @ApiModelProperty(value = "机器编号")
    private String machineNo;

    /**
     * PAD兑换站点编码
     */
    @TableField("station_code")
    @ApiModelProperty(value = "PAD兑换站点编码")
    private String stationCode;

    /**
     * 兑换时间
     */
    @TableField("exchange_time")
    @ApiModelProperty(value = "兑换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exchangeTime;

    /**
     * 组织机构code
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织机构code")
    private String orgCode;

    /**
     * 组织机构名称
     */
    @TableField("org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 兑换之前的积分
     */
    @TableField("exchange_before_score")
    @ApiModelProperty(value = "兑换之前的积分")
    private BigDecimal exchangeBeforeScore;

    /**
     * 兑换之后的积分
     */
    @TableField("exchange_after_score")
    @ApiModelProperty(value = "兑换之后的积分")
    private BigDecimal exchangeAfterScore;

    /**
     * 状态数据字典值
     */
    @TableField("status")
    @ApiModelProperty(value = "状态数据字典值")
    @DictTrans(transTo = "statusText")
    private String status;
    @TableField(exist = false)
    @ApiModelProperty(value = "状态类型Text")
    private String statusText;

    /**
     * 核销用户id
     */
    @TableField("verify_user_id")
    @ApiModelProperty(value = "核销用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long verifyUserId;

    /**
     * 核销用户姓名
     */
    @TableField("verify_user_name")
    @ApiModelProperty(value = "核销用户姓名")
    private String verifyUserName;

    /**
     * 核销时间
     */
    @TableField("verify_time")
    @ApiModelProperty(value = "核销时间")
    private LocalDateTime verifyTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    @ApiModelProperty(value = "过期时间")
    @JsonSerialize(using = ToStringSerializer.class)
    private LocalDateTime expireTime;

    /**
     * 兑换途径
     */
    @TableField("exchange_channel")
    @ApiModelProperty(value = "兑换途径")
    private String exchangeChannel;
    /**
     * 用户收货地址id
     */
    @TableField("user_address_id")
    @ApiModelProperty(value = "用户收货地址Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userAddressId;
    /**
     * 用户收货地址
     */
    @TableField("address")
    @ApiModelProperty(value = "用户收货地址")
    private String address;
    /**
     * 兑换途径
     */
    @TableField("ems_status")
    @ApiModelProperty(value = "快递状态")
    @DictTrans(transTo = "emsStatusText")
    private String emsStatus;
    @TableField(exist = false)
    @ApiModelProperty(value = "状态类型Text")
    private String emsStatusText;

    /**
     * 商品Logo
     */
    @TableField("goods_logo")
    @ApiModelProperty(value = "商品Logo")
    private String goodsLogo;
    /**
     * 收货地址
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "收货地址")
    private SysUserAddress sysUserAddress;

    /**
     * 收件人
     */
    @TableField("recipient")
    @ApiModelProperty(value = "收件人")
    private String recipient;
    /**
     * 收件人联系方式
     */
    @TableField("recipient_phone")
    @ApiModelProperty(value = "收件人联系方式")
    private String recipientPhone;
    /**
     * 快递编号
     */
    @TableField("ems_code")
    @ApiModelProperty(value = "快递编号")
    private String emsCode;
    /**
     * 省
     */
    @TableField("prov")
    @ApiModelProperty(value = "省")
    private String prov;
    /**
     * 市
     */
    @TableField("city")
    @ApiModelProperty(value = "市")
    private String city;
    /**
     * 区
     */
    @TableField("county")
    @ApiModelProperty(value = "区")
    private String county;
    /**
     * 邮政发货失败日志
     */
    @TableField(value = "ems_remark", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "邮政发货失败日志")
    private String emsRemark;
    /**
     * 核销码
     */
    @TableField("verification_code")
    @ApiModelProperty(value = "核销码")
    private String verificationCode;

    /**
     * 是否需要报名
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否需要报名")
    private Boolean needApply;

    /**
     * 报名表id
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "报名表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long applyId;

    /**
     * 报名表类型
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "报名表类型")
    @JsonSerialize(using = ToStringSerializer.class)
    private String applyType;
}
