package com.fykj.scaffold.zyz.controller.api;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieve;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieveAuditLog;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamRetrieveParams;
import com.fykj.scaffold.zyz.service.IZyzTeamRetrieveAuditLogService;
import com.fykj.scaffold.zyz.service.IZyzTeamRetrieveService;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static com.fykj.scaffold.zyz.util.DownloadFileUtil.downloadFile;

/**
 * 团队找回控制类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/zyz/team/retrieve")
@Api(tags = "小程序团队找回控制类 --无需登录")
public class TeamRetrieveApi {

    @Autowired
    private IZyzTeamRetrieveService teamRetrieveService;

    @Autowired
    private IZyzTeamRetrieveAuditLogService teamRetrieveAuditLogService;

    @ApiOperation(value = "获取团队找回信息")
    @GetMapping(value = "/getById")
    public Result getById(@RequestParam Long id) {
        return new JsonResult<>(teamRetrieveService.getTeamById(id));
    }

    @ApiOperation(value = "团队找回信息核实提交")
    @PostMapping(value = "/checkSubmit")
    public Result checkSubmit(@RequestBody ZyzTeamRetrieve teamRetrieve) {
        teamRetrieveService.checkSubmit(teamRetrieve);
        return new JsonResult<>(ResultCode.OK);
    }

    @ApiOperation(value = "找回团队列表")
    @PostMapping(value = "/getList")
    public JsonResult<List<ZyzTeamRetrieve>> getList(@RequestBody ZyzTeamRetrieveParams params) {
        List<ZyzTeamRetrieve> result = teamRetrieveService.getList(params);
        if (CollectionUtil.isEmpty(result)) {
            return new JsonResult<>();
        }
        DictTransUtil.trans(result);
        DesensitiseUtil.desensitise(result);
        result.forEach(it -> {
            if (ZyzCons.TEAM_AUDIT_STATUS_WAIT_CHECK.equals(it.getTeamStatus())) {
                it.setTeamStatusText("待核实");
            }
        });
        return new JsonResult<>(result);
    }

    @ApiOperation("获取审核操作记录")
    @GetMapping("/getLogsByTeamId")
    public JsonResult<List<ZyzTeamRetrieveAuditLog>> getLogsByTeamId(Long teamId) {
        List<ZyzTeamRetrieveAuditLog> res = teamRetrieveAuditLogService.getLogsByTeamId(teamId);
        DictTransUtil.trans(res);
        return new JsonResult<>(res);
    }

    /**
     * 团队信息登记表下载
     */
    @ApiOperation("团队信息登记表下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/docxTemplate/志愿服务团队信息登记表.docx");
    }
}
