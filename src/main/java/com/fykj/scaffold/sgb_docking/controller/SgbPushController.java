package com.fykj.scaffold.sgb_docking.controller;

import cn.hutool.core.util.StrUtil;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncPushService;
import com.fykj.scaffold.support.syslog.AuditLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SGB推送接口控制器
 * <p>
 * 提供手动推送各类数据到苏州社工部平台的Web接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "SGB推送接口")
@RestController
@RequestMapping({"/admin/sgb/push", "/api/admin/sgb/push"})
public class SgbPushController {

    @Autowired
    private ISgbSyncPushService sgbSyncPushService;

    @AuditLog("手动推送志愿者到SGB平台")
    @ApiOperation("推送志愿者")
    @PostMapping("/syncVolunteer")
    public Result syncVolunteer(@RequestParam String volunteerId) {
        List<Long> ids = parseIds(volunteerId);
        sgbSyncPushService.syncVolunteerBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    @AuditLog("手动推送志愿阵地到SGB平台")
    @ApiOperation("推送志愿阵地")
    @PostMapping("/syncOrg")
    public Result syncOrg(@RequestParam String orgId) {
        List<Long> ids = parseIds(orgId);
        sgbSyncPushService.syncOrgBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    @AuditLog("手动推送团队到SGB平台")
    @ApiOperation("推送团队")
    @PostMapping("/syncTeam")
    public Result syncTeam(@RequestParam String teamId) {
        List<Long> ids = parseIds(teamId);
        sgbSyncPushService.syncTeamBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    @AuditLog("手动推送加入组织到SGB平台")
    @ApiOperation("推送加入组织")
    @PostMapping("/syncTeamVolunteer")
    public Result syncTeamVolunteer(@RequestParam String volunteerTeamId) {
        List<Long> ids = parseIds(volunteerTeamId);
        sgbSyncPushService.syncTeamVolunteerBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    @AuditLog("手动推送活动到SGB平台")
    @ApiOperation("推送活动")
    @PostMapping("/syncActivity")
    public Result syncActivity(@RequestParam String activityId) {
        List<Long> ids = parseIds(activityId);
        sgbSyncPushService.syncActivityBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    @AuditLog("手动推送活动招募到SGB平台")
    @ApiOperation("推送活动招募")
    @PostMapping("/syncActivityRecruit")
    public Result syncActivityRecruit(@RequestParam String activityTimePeriodId) {
        List<Long> ids = parseIds(activityTimePeriodId);
        sgbSyncPushService.syncActivityRecruitBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    @AuditLog("手动推送加入活动招募到SGB平台")
    @ApiOperation("推送加入活动招募")
    @PostMapping("/syncActivityMember")
    public Result syncActivityMember(@RequestParam String activityApplyId) {
        List<Long> ids = parseIds(activityApplyId);
        sgbSyncPushService.syncActivityMemberBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    @AuditLog("手动推送服务时长到SGB平台")
    @ApiOperation("推送服务时长")
    @PostMapping("/syncServiceTime")
    public Result syncServiceTime(@RequestParam String activityApplyId) {
        List<Long> ids = parseIds(activityApplyId);
        sgbSyncPushService.syncServiceTimeBatch(ids);
        return new JsonResult<>("批量同步完成");
    }

    /**
     * 解析逗号分隔的ID字符串为Long列表
     * @param ids 逗号分隔的ID字符串
     * @return Long类型的ID列表
     */
    private List<Long> parseIds(String ids) {
        return Arrays.stream(ids.split(","))
                .filter(StrUtil::isNotBlank)
                .map(String::trim)
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

}