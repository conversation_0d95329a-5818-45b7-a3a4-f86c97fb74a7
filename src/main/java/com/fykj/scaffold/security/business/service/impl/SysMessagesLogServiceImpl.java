package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.support.utils.MessagesApiUtil;
import com.fykj.scaffold.security.business.domain.entity.SysMessagesLog;
import com.fykj.scaffold.security.business.mapper.SysMessagesLogMapper;
import com.fykj.scaffold.security.business.service.ISysMessagesLogService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;


/**
 * 
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2023-03-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysMessagesLogServiceImpl extends BaseServiceImpl<SysMessagesLogMapper, SysMessagesLog> implements ISysMessagesLogService {


    @Override
    public void sendMessages(String mobile, String content) {
        MessagesApiUtil.Result result=MessagesApiUtil.sendShortMessage(content,mobile);
        SysMessagesLog log=new SysMessagesLog();
        log.setSendMobile(mobile);
        log.setSendResult(result.getResult());
        log.setSendTime(LocalDateTime.now());
        save(log);
    }
}