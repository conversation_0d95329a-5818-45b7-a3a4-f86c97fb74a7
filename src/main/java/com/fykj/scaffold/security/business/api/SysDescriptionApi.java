package com.fykj.scaffold.security.business.api;

import com.fykj.scaffold.security.business.domain.entity.SysDescription;
import com.fykj.scaffold.security.business.service.ISysDescriptionService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/api/sys/description")
public class SysDescriptionApi {

    @Autowired
    private ISysDescriptionService descriptionService;

    @ApiOperation("根据栏位获取可以展示的app列表")
    @GetMapping(value = "/getByCode")
    public Result getByCode(String code) {
        return new JsonResult<>(descriptionService.lambdaQuery().eq(SysDescription::getCode, code).one());
    }
}
