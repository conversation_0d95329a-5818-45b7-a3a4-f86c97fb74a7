package com.fykj.scaffold.zsq_docking.push_mq.listener;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingMqFail;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingMqFailService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingNewsRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.event.NewsDockingEvent;
import com.fykj.scaffold.zsq_docking.push_mq.service.IZSQDataPushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.DOCKING_RECORD_STATUS_FAIL;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.TOPIC_NEWS_DOCKING;

/**
 * 新闻创建推送监听
 *
 * <AUTHOR>
 * @date 2025/04/21
 */
@Slf4j
@Service
@RocketMQMessageListener(consumerGroup = "${spring.application.name}" + TOPIC_NEWS_DOCKING, topic = TOPIC_NEWS_DOCKING)
public class NewsDockingListener implements RocketMQListener<NewsDockingEvent>, AutoPushZSHMonitor {

    @Autowired
    private IZSQDockingNewsRecordService newsDockingRecordService;

    @Autowired
    private IZSQDataPushService pushService;

    @Autowired
    private IZSQDockingMqFailService pushFailService;

    @Override
    public void onMessage(NewsDockingEvent notice) {
        ZSQDockingMqFail fail = new ZSQDockingMqFail();
        Long dockingRecordId = notice.getDockingRecordId();
        fail.setDockingRecordId(dockingRecordId);
        fail.setMessageId(notice.getMsgId());
        fail.setContent(JSONUtil.toJsonStr(notice));
        fail.setMsgTime(notice.getMsgCreateTime());
        Long newsId = notice.getNewsId();
        if (newsId == null) {
            fail.setExceptionTrace("新闻 id 未获取到！");
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingNewsRecord record = newsDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【新闻 id 未获取到】！");
                newsDockingRecordService.updateById(record);
            }
            return;
        }
        try {
            pushService.newsDocking(notice);
        } catch (Exception e) {
            fail.setExceptionTrace(ExceptionUtil.stacktraceToString(e));
            pushFailService.save(fail);
            if (dockingRecordId != null) {
                ZSQDockingNewsRecord record = newsDockingRecordService.getById(dockingRecordId);
                record.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
                record.setDockingMsg("MQ消费异常，异常原因：【" + ExceptionUtil.stacktraceToString(e) + "】！");
                newsDockingRecordService.updateById(record);
            }
        }
    }
}
