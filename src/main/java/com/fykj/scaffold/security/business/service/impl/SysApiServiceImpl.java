package com.fykj.scaffold.security.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.cms.domain.entity.CmsCategoryContent;
import com.fykj.scaffold.cms.domain.entity.CmsContent;
import com.fykj.scaffold.cms.domain.vo.CategoryContentVo;
import com.fykj.scaffold.cms.domain.vo.CmsContentVo;
import com.fykj.scaffold.security.business.domain.dto.ApiListDto;
import com.fykj.scaffold.security.business.domain.dto.ProtectedApiOAuthDto;
import com.fykj.scaffold.security.business.domain.entity.SysApi;
import com.fykj.scaffold.security.business.domain.entity.SysApiCategory;
import com.fykj.scaffold.security.business.domain.params.SysApiParams;
import com.fykj.scaffold.security.business.mapper.SysApiMapper;
import com.fykj.scaffold.security.business.service.ISysApiCategoryService;
import com.fykj.scaffold.security.business.service.ISysApiService;
import constants.Mark;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.support.util.BeanUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import result.JsonResult;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 系统资源-API接口
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysApiServiceImpl extends BaseServiceImpl<SysApiMapper, SysApi> implements ISysApiService {

    @Autowired
    private ISysApiCategoryService apiCategoryService;

    @Override
    public SysApi getById(Serializable id) {
        SysApi api = super.getById(id);
        api.setApiCategories(Arrays.asList(api.getApiCategory().split(Mark.COMMA)));
        return api;
    }
    @Override
    public List<ProtectedApiOAuthDto> getRoleApiPathList() {
        return baseMapper.getRoleApiPathList();
    }

    @Override
    public boolean save(SysApi api) {
        api.setApiCategory(String.join(Mark.COMMA, api.getApiCategories()));
        return super.save(api);
    }

    @Override
    public boolean updateById(SysApi api) {
        api.setApiCategory(String.join(Mark.COMMA, api.getApiCategories()));
        return super.updateById(api);
    }

    @Override
    public IPage<SysApi> page(BaseParams params) {
        List<SysApiCategory> categories = apiCategoryService.list();
        if (ObjectUtils.isEmpty(params)) {
            params = new SysApiParams();
        }
        if("1".equals(((SysApiParams)params).getCategoryId())) {
            ((SysApiParams) params).setCategoryId(null);
        }
        return super.page(params).convert(it -> convert(it, categories));
    }

    private SysApi convert (SysApi entity, List<SysApiCategory> categories) {
        List<String> categoryTexts = new ArrayList<>();
        Arrays.stream(entity.getApiCategory().split(Mark.COMMA)).forEach(it -> {
            List<SysApiCategory> exists = categories.stream().filter(im -> String.valueOf(im.getId()).equals(it)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(exists)) {
                categoryTexts.add(exists.get(0).getName());
            }
        });
        entity.setApiCategoriesText(String.join(Mark.LINE, categoryTexts));
        return entity;
    }

    @Override
    public List<ApiListDto> bindList() {
        List<SysApi> apis =  lambdaQuery().list();
        List<ApiListDto> result = new ArrayList<>();
        apis.forEach(it -> {
            ApiListDto dto = new ApiListDto();
            dto.setKey(it.getId());
            dto.setLabel(it.getApiPath().concat("-").concat(it.getApiName()));
            dto.setDisabled(!it.getStatus());
            result.add(dto);
        });
        return result;
    }
}