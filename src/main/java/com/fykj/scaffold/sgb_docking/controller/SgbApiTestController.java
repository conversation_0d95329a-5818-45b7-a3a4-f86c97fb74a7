package com.fykj.scaffold.sgb_docking.controller;

import com.fykj.scaffold.sgb_docking.util.SgbApiUtil;
import com.fykj.scaffold.support.syslog.AuditLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

/**
 * 苏州志愿者平台API测试控制器
 */
@Api(tags = "苏州志愿者平台-API测试")
@RestController
@RequestMapping("/admin/sgb/api-test")
public class SgbApiTestController {

    /**
     * 测试API连通性
     * @param method 接口方法名
     * @param params 接口参数JSON
     * @return 接口响应
     */
    @AuditLog("测试苏州志愿者平台API")
    @ApiOperation("测试苏州志愿者平台API")
    @PostMapping("/test")
    public JsonResult<String> testApi(@RequestParam String method, @RequestBody String params) {
        String response = SgbApiUtil.sendRequest(method, params);
        return new JsonResult<>(response);
    }
} 