package com.fykj.scaffold.sync.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 实践单
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("practice_sheet")
public class PracticeSheet extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 实践单类型
     */
    @TableField("type")
    @ApiModelProperty(value = "实践单类型")
    private String type;

    /**
     * 需求/资源/活动名称
     */
    @TableField("content")
    @ApiModelProperty(value = "需求/资源/活动名称")
    private String content;

    /**
     * 唯一编号
     */
    @TableField("unique_code")
    @ApiModelProperty(value = "唯一编号")
    private String uniqueCode;

    /**
     * 需求/资源/活动id
     */
    @TableField("link_id")
    @ApiModelProperty(value = "需求/资源/活动id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long linkId;

    /**
     * 实践阵地编号
     */
    @TableField("pb_id")
    @ApiModelProperty(value = "实践阵地编号")
    private String pbId;

    /**
     * 要求完成日期
     */
    @TableField("finish_date")
    @ApiModelProperty(value = "要求完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate finishDate;

    /**
     * 实践单对接方类型 1:实践阵地（实践中心、所、站） 2:职能部门 3:实践点 4:组织团队
     */
    @TableField("dock_type")
    @ApiModelProperty(value = "实践单对接方类型 1:实践阵地（实践中心、所、站） 2:职能部门 3:实践点 4:组织团队")
    private String dockType;

    /**
     * 实践单对接方编号
     */
    @TableField("dock_id")
    @ApiModelProperty(value = "实践单对接方编号")
    private String dockId;

    /**
     * 对接时间
     */
    @TableField("dock_time")
    @ApiModelProperty(value = "对接时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dockTime;

    /**
     * 完成时间
     */
    @TableField("finish_time")
    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;

    /**
     * 完成备注
     */
    @TableField("finish_remark")
    @ApiModelProperty(value = "完成备注")
    private String finishRemark;

    /**
     * 同步状态
     */
    @DictTrans(transTo = "syncText")
    @TableField("is_sync")
    @ApiModelProperty(value = "同步状态")
    private String sync;

    /**
     * 同步状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "同步状态")
    private String syncText;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 同步备注
     */
    @TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 同步成功后市区返回的Id
     */
    @TableField("sync_id")
    @ApiModelProperty(value = "同步成功后市区返回的Id")
    private String syncId;

    /**
     * 活动关联资源id
     */
    @TableField("act_link_res_id")
    @ApiModelProperty(value = "活动关联资源id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actLinkResId;

    /**
     * 活动关联资源名称
     */
    @TableField("act_link_res_name")
    @ApiModelProperty(value = "活动关联资源名称")
    private String actLinkResName;

    /**
     * 活动关联需求id
     */
    @TableField("act_link_req_id")
    @ApiModelProperty(value = "活动关联需求id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actLinkReqId;

    /**
     * 活动关联需求名称
     */
    @TableField("act_link_req_name")
    @ApiModelProperty(value = "活动关联需求名称")
    private String actLinkReqName;
}
