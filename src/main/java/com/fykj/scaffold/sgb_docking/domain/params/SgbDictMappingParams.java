package com.fykj.scaffold.sgb_docking.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统字典值与三方字典值映射查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("系统字典值与三方字典值映射查询参数")
public class SgbDictMappingParams extends BaseParams {

    private static final long serialVersionUID = 1L;

    /**
     * 本系统字典类型
     */
    @ApiModelProperty("本系统字典类型")
    @MatchType(QueryType.LIKE)
    private String sysType;

    /**
     * 本系统字典值
     */
    @ApiModelProperty("本系统字典值")
    @MatchType(QueryType.LIKE)
    private String sysValue;

    /**
     * 第三方字典类型
     */
    @ApiModelProperty("第三方字典类型")
    @MatchType(QueryType.LIKE)
    private String thirdType;

    /**
     * 第三方字典值
     */
    @ApiModelProperty("第三方字典值")
    @MatchType(QueryType.LIKE)
    private String thirdValue;
} 