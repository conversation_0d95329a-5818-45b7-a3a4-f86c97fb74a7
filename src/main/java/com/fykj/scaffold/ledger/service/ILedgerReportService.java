package com.fykj.scaffold.ledger.service;

import com.fykj.scaffold.ledger.domain.dto.LedgerReportDto;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.ledger.domain.entity.LedgerReport;
import result.Result;

/**
 * 台账上报-服务类
 *
 * @date 2025-05-26
 */
public interface ILedgerReportService extends IBaseService<LedgerReport> {

    /**
     * 上报
     *
     * @param reportDto
     * @return
     */
    Result report(LedgerReportDto reportDto);

    /**
     * 管理员上报
     *
     * @param reportDto
     * @return
     */
    Result reportAdmin(LedgerReportDto reportDto);
}

