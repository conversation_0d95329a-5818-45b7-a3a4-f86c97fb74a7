package com.fykj.scaffold.mall.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation;
import com.fykj.scaffold.mall.domain.params.MallPadExchangeStationParams;
import com.fykj.scaffold.mall.service.IMallPadExchangeStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

/**
 * pad兑换站点微信前端接口
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@RestController
@RequestMapping("/api/mall/pad-exchange-station")
@Api(tags="pad兑换站点接口无需登录")
public class MallPadExchangeStationApi {

    @Autowired
    private IMallPadExchangeStationService padExchangeStationService;

    @ApiOperation("pad兑换站点分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallPadExchangeStation>> getPages(@RequestBody MallPadExchangeStationParams params) {
        params.setStatus(1);
        return new JsonResult<>(padExchangeStationService.pageForApi(params));
    }

    @ApiOperation("pad兑换站点查询详情")
    @GetMapping({"/get"})
    public JsonResult<MallPadExchangeStation> get(@RequestParam String id) {
        return new JsonResult<>(padExchangeStationService.getById(id));
    }
}
