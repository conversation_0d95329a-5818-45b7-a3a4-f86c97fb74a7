package com.fykj.scaffold.mall.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallPadExchangeStation;
import com.fykj.scaffold.mall.domain.params.MallPadExchangeStationParams;
import com.fykj.scaffold.mall.mapper.MallPadExchangeStationMapper;
import com.fykj.scaffold.mall.service.IMallPadExchangeStationService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.util.List;


/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MallPadExchangeStationServiceImpl extends BaseServiceImpl<MallPadExchangeStationMapper, MallPadExchangeStation> implements IMallPadExchangeStationService {

    @Override
    public IPage<MallPadExchangeStation> getPage(MallPadExchangeStationParams params) {
        if (params == null) {
            params = new MallPadExchangeStationParams();
        }
        return baseMapper.getPage(params.getPage(), params);
    }

    @Override
    public IPage<MallPadExchangeStation> pageForApi(MallPadExchangeStationParams params) {
        if (params == null) {
            params = new MallPadExchangeStationParams();
        }
        return baseMapper.pageForApi(params.getPage(), params);
    }

    @Override
    public boolean save(MallPadExchangeStation station) {
        if (lambdaQuery().eq(MallPadExchangeStation::getCode, station.getCode()).exists()) {
            throw new BusinessException(ResultCode.EXIST, "站点code重复");
        }
        return super.save(station);
    }

    @Override
    public boolean updateById(MallPadExchangeStation station) {
        if (lambdaQuery().eq(MallPadExchangeStation::getCode, station.getCode()).ne(MallPadExchangeStation::getId, station.getId()).exists()) {
            throw new BusinessException(ResultCode.EXIST, "站点code重复");
        }
        return super.updateById(station);
    }

    @Override
    public List<MallPadExchangeStation> getStationsByCurrOrg() {
        List<MallPadExchangeStation> result = baseMapper.getStationsByCurrOrg();
        String orgCode = Oauth2Util.getOrgCode();
        result.forEach(it -> {
            it.setEnableSelect(StringUtil.isNotEmpty(orgCode) && orgCode.equals(it.getOrgCode()));
        });
        return result;
    }

    @Override
    public void statusChange(String id) {
        MallPadExchangeStation station = getById(id);
        station.setStatus(station.getStatus() == 0 ? 1 : 0);
        super.updateById(station);
    }
}
