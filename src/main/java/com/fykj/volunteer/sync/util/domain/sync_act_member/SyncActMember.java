package com.fykj.volunteer.sync.util.domain.sync_act_member;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 加入活动招募
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActMember {
    /**
     * 活动招募id
     * 必填
     */
    private String actId;
    /**
     * 身份证编号
     * 必填
     */
    private String idCode;
    /**
     * 加入时间 格式：2016-09-30
     * 必填
     */
    private String joinTime;
    /**
     * 唯一标识
     * 必填
     */
    private String uniCode;
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "actMember")
    public static class ActMember{
        private List<SyncActMember> info;
    }
    public String buildXml() {
        SyncActMember.ActMember act = new SyncActMember.ActMember();
        act.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(act);
    }
}
