package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_ACTIVITY_AUDIT_TIMEOUT;


/**
 * 活动审核即将超时提醒
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityAuditTimeOutSendTask {

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private IZyzActivityService activityService;

    @XxlJob("Activity_Audit_Timeout_Send_Tmp")
    public void activityAuditTimeOutSend() {
        XxlJobHelper.log("ACTIVITY_AUDIT_TIMEOUT_SendTmp-Job, START!");

        LocalDateTime tomorrow = LocalDateTime.now().plusDays(1);
        //获取活动开始前一天还未审核的活动
        activityService.lambdaQuery().gt(ZyzActivity::getStartTime, LocalDateTime.now()).le(ZyzActivity::getStartTime, tomorrow).eq(ZyzActivity::getAuditStatus, ZyzCons.ACTIVITY_STATUS_WAIT_AUDIT).list().forEach(it -> {
            List<String> orgCodes = new ArrayList<>();
            orgCodes.add(it.getAuditOrgCode());
            sendTmpMsg.sendToAuditOrg(SMT_ACTIVITY_AUDIT_TIMEOUT, orgCodes, it.getName(), it.getPublishOrgName());
        });
        XxlJobHelper.log("ACTIVITY_AUDIT_TIMEOUT_SendTmp-Job, END!");
    }
}
