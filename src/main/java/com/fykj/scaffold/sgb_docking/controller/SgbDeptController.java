package com.fykj.scaffold.sgb_docking.controller;

import fykj.microservice.core.base.BaseController;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbDept;
import com.fykj.scaffold.sgb_docking.domain.params.SgbDeptParams;
import com.fykj.scaffold.sgb_docking.service.ISgbDeptService;
import com.fykj.scaffold.support.syslog.AuditLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;

/**
 * 苏州志愿者平台部门单位控制器
 */
@Api(tags = "苏州志愿者平台-部门单位")
@RestController
@RequestMapping("/admin/sgb/dept")
public class SgbDeptController extends BaseController<ISgbDeptService, SgbDept, SgbDeptParams> {

    /**
     * 同步部门单位信息
     */
    @AuditLog("手动同步部门单位信息")
    @ApiOperation("同步部门单位信息")
    @PostMapping("/sync")
    public Result sync() {
        baseService.syncDept();
        return new Result();
    }
} 