package com.fykj.volunteer.sync.util.domain.res;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 *
 * <res>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>同步失败：资源类型不正确</msg>
 *     <uniCode>102034</uniCode>
 *   </info>
 * </res>
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncResResult {
    /**
     * 资源编号
     */
    private String resId;
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "res")
    public static class Res {
        private List<SyncResResult> info;
    }
}
