package com.fykj.scaffold.zyz.controller.api;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.zyz.domain.dto.DictVolunteerBlockChainDto;
import com.fykj.scaffold.zyz.domain.dto.ServiceLongForBlockChainParamsDto;
import com.fykj.scaffold.zyz.domain.dto.block_chain.ActivityApplyLongBlockChainDto;
import com.fykj.scaffold.zyz.domain.dto.block_chain.VolunteerBlockChainDto;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.google.gson.Gson;
import exception.BusinessException;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 区块链
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/api/sip/block-chain")
@Slf4j
public class ForBlockchainApi {


    @Autowired
    private IZyzVolunteerService volunteerService;


    @Autowired
    private IZyzActivityApplyService activityApplyService;

    @Autowired
    private IDictService dictService;

    // 密钥：A2#&15x29
    String key = "A2#&15x29";

    @ApiOperation("获取志愿者")
    @PostMapping(value = "/volunteer-list")
    public Result getVolunteerForBlockchain(@RequestBody ServiceLongForBlockChainParamsDto dto) {
        if (dto.getStartTime() == null || dto.getEndTime() == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请填写开始结束时间");
        }
        List<VolunteerBlockChainDto> volunteerForBlockchain = volunteerService.getVolunteerForBlockchain(dto);
        //使用des加密
        Gson gson = new Gson();
        return new JsonResult<>(SecureUtil.des(key.getBytes()).encryptBase64(gson.toJson(volunteerForBlockchain)));
    }


    @ApiOperation("获取活动时长")
    @PostMapping(value = "/service-long-list")
    public Result getServiceLongForBlockchain(@RequestBody ServiceLongForBlockChainParamsDto dto) {
        if (dto.getStartTime() == null || dto.getEndTime() == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请填写开始结束时间");
        }
        long diffDays = LocalDateTimeUtil.between(dto.getStartTime(), dto.getEndTime(), ChronoUnit.DAYS);
        if (diffDays > 30) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "请勿超过30天");
        }
        List<ActivityApplyLongBlockChainDto> res = activityApplyService.getServiceLongForBlockchain(dto);
        return new JsonResult<>(SecureUtil.des(key.getBytes()).encryptBase64(JSONUtil.toJsonStr(res)));
    }

    @ApiOperation("获取志愿者相关数据字典")
    @GetMapping(value = "/dict")
    public Result getDictForBlockChain() {
        List<DictVolunteerBlockChainDto> volunteerForBlockchain = volunteerService.getDictForBlockChain();
        //使用des加密
        return new JsonResult<>(SecureUtil.des(key.getBytes()).encryptBase64(JSONUtil.toJsonStr(volunteerForBlockchain)));
    }
}
