<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzProjectMapper">
    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzProject">
        select
        *,
        is_display AS display
        from zyz_project
        where is_deleted = 0
        <if test="params.projectName != null and params.projectName != ''">
            and (project_name like concat('%', #{params.projectName}, '%') OR project_code like concat('%', #{params.projectName}, '%'))
        </if>
        <if test="params.typeName != null and params.typeName != ''">
            and type_name like concat('%', #{params.typeName}, '%')
        </if>
        <if test="params.teamName != null and params.teamName != ''">
            and team_name like concat('%', #{params.teamName}, '%')
        </if>
        <if test="params.year != null and params.year != ''">
            and `year` = #{params.year}
        </if>
        <if test="params.contactPhone != null and params.contactPhone != ''">
            and contact_phone like concat('%', #{params.contactPhone}, '%')
        </if>
        <if test="params.contactName != null and params.contactName != ''">
            and contact_name like concat('%', #{params.contactName}, '%')
        </if>
        <if test="params.projectDemandType != null and params.projectDemandType != ''">
            and project_demand_type = #{params.projectDemandType}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            and audit_status = #{params.auditStatus}
        </if>
        <if test="params.teamId != null and params.teamId != ''">
            and team_id = #{params.teamId}
        </if>
        <if test="params.auditStatus != 'pro_wait_audit' and (params.teamId == null or params.teamId == '')">
            AND audit_status != 'pro_draft' AND audit_status != 'pro_wait_audit' AND audit_status != 'pro_reject'
        </if>
        order by create_date desc
    </select>
    <select id="getPagesForPc" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzProject">
        select
        *
        from zyz_project
        where is_deleted = 0 AND is_display=1
        and audit_status not in ('pro_draft', 'pro_wait_audit', 'pro_reject')
        <if test="params.projectName != null and params.projectName != ''">
            and project_name like concat('%', #{params.projectName}, '%')
        </if>
        <if test="params.typeName != null and params.typeName != ''">
            and type_name like concat('%', #{params.typeName}, '%')
        </if>
        <if test="params.teamName != null and params.teamName != ''">
            and team_name like concat('%', #{params.teamName}, '%')
        </if>
        <if test="params.year != null and params.year != ''">
            and year = #{params.year}
        </if>
        <if test="params.contactPhone != null and params.contactPhone != ''">
            and contact_phone like concat('%', #{params.contactPhone}, '%')
        </if>
        <if test="params.contactName != null and params.contactName != ''">
            and contact_name like concat('%', #{params.contactName}, '%')
        </if>
        <if test="params.projectDemandType != null and params.projectDemandType != ''">
            and project_demand_type = #{params.projectDemandType}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            and audit_status = #{params.auditStatus}
        </if>
        <if test="params.teamId != null">
            and team_id = #{params.teamId}
        </if>
        order by sequence asc, create_date asc
    </select>
    <select id="getList" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectExportDto">
        select
        *
        from zyz_project
        where is_deleted = 0
        <if test="params.projectName != null and params.projectName != ''">
            and project_name like concat('%', #{params.projectName}, '%')
        </if>
        <if test="params.typeName != null and params.typeName != ''">
            and type_name like concat('%', #{params.typeName}, '%')
        </if>
        <if test="params.teamName != null and params.teamName != ''">
            and team_name like concat('%', #{params.teamName}, '%')
        </if>
        <if test="params.year != null and params.year != ''">
            and year = #{params.year}
        </if>
        <if test="params.contactPhone != null and params.contactPhone != ''">
            and contact_phone like concat('%', #{params.contactPhone}, '%')
        </if>
        <if test="params.contactName != null and params.contactName != ''">
            and contact_name like concat('%', #{params.contactName}, '%')
        </if>
        <if test="params.projectDemandType != null and params.projectDemandType != ''">
            and project_demand_type = #{params.projectDemandType}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            and audit_status = #{params.auditStatus}
        </if>
        <if test="params.teamId != null and params.teamId != ''">
            and team_id = #{params.teamId}
        </if>
        order by create_date desc
    </select>
    <select id="sumNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_project
        WHERE is_deleted = 0 AND audit_status != 'pro_draft' AND audit_status != 'pro_wait_audit' AND audit_status != 'pro_reject'
        AND is_display = 1
        <if test="params.start != null">
            AND create_date &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND create_date &lt;= #{params.end}
        </if>
    </select>

    <select id="getMaterialTotalAmount" resultType="java.math.BigDecimal">
        SELECT SUM(IFNULL(mp.expenses, 0))
        FROM zyz_project p
        LEFT JOIN zyz_project_money_material_plan mp ON p.id = mp.project_id
        WHERE p.is_deleted = 0 AND p.audit_status != 'pro_draft' AND p.audit_status != 'pro_wait_audit' AND p.audit_status != 'pro_reject'
        <if test="params.start != null">
            AND p.create_date &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND p.create_date &lt;= #{params.end}
        </if>
    </select>

    <select id="getProjectSumReport" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectSumReportDto">
        SELECT
            p.id,
            p.type_name,
            p.project_name,
            p.project_code,
            p.project_summary,
            p.project_demand_type,
            p.funds_required,
            p.audit_status,
            t.`name`,
            t.services,
            t.introduction,
            t.founded,
            t.civil_registration,
            t.curator_name,
            t.curator_contact,
            d.corp_name,
            d.amount,
            p.attachment_list,
            p.create_date,
            p.apply_unit_name,
            p.contact_name,
            p.contact_phone,
            IFNULL(p.sequence,0) AS sequence,
            CASE WHEN p.is_display=1  THEN '是' ELSE '否' END AS display
        FROM `zyz_project`p
        LEFT JOIN zyz_team t ON p.team_id=t.id AND t.is_deleted=0
        LEFT JOIN zyz_project_docking d ON d.project_id=p.id AND d.is_deleted=0
        WHERE p.is_deleted=0 AND type_name is not null
        <if test="params.year != null and params.year != ''">
            and p.`year` = #{params.year}
        </if>
        <if test="params.typeName != null and params.typeName != ''">
            and type_name like concat('%', #{params.typeName}, '%')
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND audit_status = #{params.auditStatus}
        </if>
        <if test="params.teamId != null and params.teamId != ''">
            and team_id = #{params.teamId}
        </if>
        ORDER BY p.create_date DESC
    </select>
    <select id="getListForExport" resultType="com.fykj.scaffold.zyz.domain.dto.ProjExportDto">
        SELECT
        p.*,
        um.corpNames AS corpNames,
        um.dockingAmount AS dockingAmount,
        dockingAmountDetail AS dockingAmountDetail,
        t.curator_name AS teamContacts
        from zyz_project p
        LEFT JOIN (
        SELECT project_id, GROUP_CONCAT( `corp_name`  SEPARATOR ';' ) AS corpNames,
        GROUP_CONCAT( `corp_name`,':',`amount`  SEPARATOR ';' ) AS dockingAmountDetail,
        sum(amount) as dockingAmount FROM zyz_project_docking GROUP BY project_id
        ) AS um ON um.project_id = p.id
        LEFT JOIN zyz_team t ON p.team_id=t.id AND t.is_deleted=0
        where p.is_deleted = 0
        <if test="params.projectName != null and params.projectName != ''">
            and project_name like concat('%', #{params.projectName}, '%')
        </if>
        <if test="params.typeName != null and params.typeName != ''">
            and type_name like concat('%', #{params.typeName}, '%')
        </if>
        <if test="params.teamName != null and params.teamName != ''">
            and team_name like concat('%', #{params.teamName}, '%')
        </if>
        <if test="params.year != null and params.year != ''">
            and year = #{params.year}
        </if>
        <if test="params.contactPhone != null and params.contactPhone != ''">
            and contact_phone like concat('%', #{params.contactPhone}, '%')
        </if>
        <if test="params.contactName != null and params.contactName != ''">
            and contact_name like concat('%', #{params.contactName}, '%')
        </if>
        <if test="params.projectDemandType != null and params.projectDemandType != ''">
            and project_demand_type = #{params.projectDemandType}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            and audit_status = #{params.auditStatus}
        </if>
        <if test="params.teamId != null and params.teamId != ''">
            and team_id = #{params.teamId}
        </if>
        order by create_date desc
    </select>
    <select id="selectMaxProjectSeed" resultType="java.lang.String">
        SELECT MAX(SUBSTR(project_code FROM 7 FOR 4))
        FROM `zyz_project`
        WHERE `year`=#{year}
        AND SUBSTR(project_code FROM 5 FOR 2)=#{month}
    </select>
    <select id="getYearList" resultType="java.lang.String">
        SELECT distinct `year` FROM zyz_project WHERE is_display=1
        ORDER BY `year` DESC
    </select>
    <select id="getMaxYear" resultType="java.lang.String">
        SELECT MAX(year)
        FROM zyz_project
        WHERE is_deleted = 0
    </select>
    <select id="cockpitList" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectCockpitListDto">
        SELECT
            p.id,
            p.project_name,
            p.project_demand_type,
            p.type_name,
            t.NAME AS team_name,
            p.audit_status,
            d.amount,
            d.corp_name
        FROM
            `zyz_project` p
            LEFT JOIN zyz_team t ON p.team_id = t.id
            LEFT JOIN zyz_project_docking d ON d.project_id = p.id AND d.is_deleted = 0
        WHERE
            p.is_deleted = 0
          AND p.is_display = 1
          AND p.`year` = #{year}
          AND p.audit_status NOT IN ( 'pro_draft', 'pro_wait_audit', 'pro_reject' )
        ORDER BY
            p.start_time DESC
    </select>

    <select id="cockpitTypeCount" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectCockpitDto$TypeNumDto">
        SELECT
            t.type_code as code,
            IFNULL(count( p.id ),0) AS num
        FROM
            zyz_project_type_project t
                LEFT JOIN zyz_project p ON t.project_id = p.id
        WHERE
            t.is_deleted = 0
          AND p.is_deleted = 0
          AND p.is_display = 1
          AND p.`year` = #{year}
          AND p.audit_status NOT IN ( 'pro_draft', 'pro_wait_audit', 'pro_reject' )
        GROUP BY
            t.type_code
    </select>
    <select id="cockpitOverview" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectCockpitDto">
        SELECT
            IFNULL(SUM(IF( p.year = #{year}, 1, 0)),0) AS currentYearCount,
            IFNULL(count( DISTINCT team_id ),0) AS teamCount,
            IFNULL(count( p.id ),0) AS totalCount
        FROM
            zyz_project p
        WHERE
            p.is_deleted = 0
          AND p.is_display = 1
          AND p.audit_status NOT IN ( 'pro_draft', 'pro_wait_audit', 'pro_reject' )
    </select>
</mapper>
