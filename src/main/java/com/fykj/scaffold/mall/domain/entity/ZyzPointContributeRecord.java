package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

;

/**
 * 积分商城-积分捐赠记录
 * 
 * <AUTHOR> @email ${email}
 * @date 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_point_contribute_record")
public class ZyzPointContributeRecord extends BaseEntity  {

	private static final long serialVersionUID = 1L;

	/**
	 * 志愿者用户id
	 */
	@TableField("volunteer_id")
    @ApiModelProperty(value = "志愿者用户id")
		@JsonSerialize(using = ToStringSerializer.class)
		private Long volunteerId;

	/**
	 * 志愿者用户名称
	 */
	@TableField("volunteer_name")
    @ApiModelProperty(value = "志愿者用户名称")
		private String volunteerName;

	/**
	 * 捐赠积分
	 */
	@TableField("contribute_point")
    @ApiModelProperty(value = "捐赠积分")
		private BigDecimal contributePoint;

	/**
	 * 受捐机构id
	 */
	@TableField("org_code")
    @ApiModelProperty(value = "受捐机构id")
		private String orgCode;

	/**
	 * 受捐机构
	 */
	@TableField("org_name")
    @ApiModelProperty(value = "受捐机构")
		private String orgName;

	/**
	 * 捐赠时间
	 */
	@TableField("contribute_date")
    @ApiModelProperty(value = "捐赠时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime contributeDate;

	/**
	 * 证件编号
	 */
	@TableField("certificate_id")
	@ApiModelProperty(value = "证件编号")
	private String certificateId;
	/**
	 * 联系方式
	 */
	@TableField("phone")
	@ApiModelProperty(value = "联系方式")
	private String phone;
}
