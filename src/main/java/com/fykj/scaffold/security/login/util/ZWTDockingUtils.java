package com.fykj.scaffold.security.login.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.domain.entity.SsoUserMapping;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.security.login.cons.ZwtConfig;
import com.fykj.scaffold.security.login.service.LoginService;
import com.fykj.scaffold.support.utils.AesUtil;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import result.ResultCode;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.baomidou.mybatisplus.core.toolkit.StringPool.UTF_8;
import static com.fykj.scaffold.security.login.cons.ZwtCons.*;

/**
 * @Author：yangxu
 * @Date：2024/12/10 18:12
 * @Description：
 */
@Component
@Slf4j
public class ZWTDockingUtils {

    @Autowired
    private RedisService redisService;

    @Autowired
    private IUserService userService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;
    
    @Autowired
    private ZwtConfig zwtConfig;

    public String getRedirectLoginUrl() throws UnsupportedEncodingException {
        String redirectUrlEncoded = URLEncoder.encode(zwtConfig.getOauth2AuthorizeRedirectUri(), UTF_8);
        return String.format(zwtConfig.getZwtOauth2AuthorizeUrl(), zwtConfig.getCorpId(), redirectUrlEncoded, zwtConfig.getAgentId());
    }

    public Object govLogin(String code, String verifyCode) {
        Long loginMs = Long.valueOf(AesUtil.decryptZeroPadding(verifyCode, zwtConfig.getValidateSecretKey()));
        Long currentMs = System.currentTimeMillis();
        if ((currentMs - loginMs) / 1000.00 > 60) {
            throw new BusinessException(ResultCode.FAIL, "登录凭证已过期！");
        }
        String accessToken = getAccessToken(false);
        String userId = getUserId(accessToken, code, true);
        JSONObject userInfo = getUserInfo(accessToken, userId, true);
        log.info("userInfo: {}", userInfo.toJSONString());
        String username;
        try {
            username = getUsername(userInfo);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "获取政务通用户手机换取科协系统用户名失败，失败原因：{" + e.getMessage() + "}");
        }
        String oneCode = userService.genOneCode(username);
//        String username = "xst";
        return loginService.oneCodeLogin(username, oneCode);
    }

    private String getUsername(JSONObject userInfo) {
        String zwtUserId = userInfo.getString("userid");
        String mobile = userInfo.getString("mobile");
        if (StringUtils.isBlank(mobile)) {
            throw new BusinessException(ResultCode.FAIL, "获取政务通用户信息失败，失败原因：用户信息接口未返回用户手机号！");
        }
        if (StringUtils.isBlank(zwtUserId)) {
            throw new BusinessException(ResultCode.FAIL, "获取政务通用户信息失败，失败原因：用户信息接口未返回用户唯一标识！");
        }
        User user;
        // 查询sso关联表
        Long platformUserId = ssoUserMappingService.getPlatformUserIdByProviderAndUserId(
                SsoCons.SsoProvider.ZWT.name(), zwtUserId);
        // 第一次登录
        if(platformUserId == null) {
            // 根据手机号去库里找
            user = fistLoginGetUser(userInfo, mobile);
        }else {
            user = secondLoginGetUser(userInfo, platformUserId);
        }
        return user.getUsername();
    }

    private User secondLoginGetUser(JSONObject userInfo, Long platformUserId) {
        User user;
        user = userService.getById(platformUserId);
        if(Objects.isNull(user)) {
            // 之前存的用户在平台中不存在, 删除原来的关联关系, 重新走第一次登录逻辑
            ssoUserMappingService.deleteSsoUserMapping(SsoCons.SsoProvider.ZWT.name(),
                    userInfo.getString("userid"));
            fistLoginGetUser(userInfo, userInfo.getString("mobile"));
        }
        // 更新sso关联
        updateSsoMapping(userInfo);
        return user;
    }

    private User fistLoginGetUser(JSONObject userInfo, String mobile) {
        User user;
        user = userService.getByMobile(mobile);
        if(Objects.isNull(user)) {
            throw new BusinessException(ResultCode.FAIL, "获取获取平台用户信息失败, 失败原因: 该用户在本平台不存在！");
        }
        // 创建sso关联
        createSsoMapping(userInfo, user.getId());
        return user;
    }

    private void createSsoMapping(JSONObject userInfo, Long platformUserId) {
        SsoUserMapping ssoUserMapping = new SsoUserMapping();
        ssoUserMapping.setSsoProvider(SsoCons.SsoProvider.ZWT.name());
        ssoUserMapping.setUserId(userInfo.getString("userid"));
        ssoUserMapping.setPlatformUserId(platformUserId);
        ssoUserMapping.setLastLoginTime(LocalDateTime.now());
        ssoUserMapping.setRawUserInfo(userInfo.toJSONString());
        ssoUserMappingService.save(ssoUserMapping);
    }

    private void updateSsoMapping(JSONObject userInfo) {
        ssoUserMappingService.updateSsoUserMappingAsync(
                SsoCons.SsoProvider.ZWT.name(),
                userInfo.getString("userid"),
                userInfo.toJSONString()
        );
    }

    private String getAccessToken(Boolean tokenExpired) {
        if (!tokenExpired && redisService.exists(ZWT_ACCESS_TOKEN_REDIS_KEY)) {
            String accessToken = redisService.get(ZWT_ACCESS_TOKEN_REDIS_KEY);
            if (StringUtils.isNotEmpty(accessToken)) {
                return accessToken;
            }
        }
        String url = String.format(zwtConfig.getAccessTokenGetUrl(), zwtConfig.getCorpId(), zwtConfig.getCorpSecret());
        JSONObject tokenResult = httpRequestForAccessToken(url);
        int errCode = tokenResult.getInteger(ZWT_URL_RSP_ERROR_CODE);
        if (errCode != 0) {
            String errorMsg = tokenResult.getString(ZWT_URL_RSP_ERROR_MSG);
            throw new BusinessException(ResultCode.FAIL, "access_token get error, error reason: {" +  errorMsg + "}");
        }
        String accessToken = tokenResult.getString(ACCESS_TOKEN_GET_URL_RSP_ACCESS_TOKEN);
        long expiresIn = tokenResult.getLong(ACCESS_TOKEN_GET_URL_RSP_EXPIRES_IN);
        redisService.set(ZWT_ACCESS_TOKEN_REDIS_KEY, accessToken, expiresIn);
        return accessToken;
    }

    private JSONObject httpRequestForAccessToken(String url) {
        RestTemplate restTemplate = new RestTemplate();
        try {
            String userInfo = restTemplate.getForObject(url, String.class);
            return JSON.parseObject(userInfo);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "access_token get error, error reason: {" +  e.getMessage() + "}");
        }
    }

    private String getUserId(String accessToken, String code, Boolean accessTokenRetry) {
        String url = String.format(zwtConfig.getUserIdGetUrl(), accessToken, code);
        JSONObject userIdResult = httpRequestForUserId(url);
        int errCode = userIdResult.getInteger(ZWT_URL_RSP_ERROR_CODE);
        if (errCode != 0) {
            String errorMsg = userIdResult.getString(ZWT_URL_RSP_ERROR_MSG);
            if (errCode == 40014 && accessTokenRetry) {
                log.error("user_id get error, error reason: {}", errorMsg);
                log.warn("access_token will fetch again……(because of user_id get error)");
                String newAccessToken = getAccessToken(true);
                return getUserId(newAccessToken, code, false);
            }
            throw new BusinessException(ResultCode.FAIL, "user_id get error, error reason: {" +  errorMsg + "}");
        }
        return userIdResult.getString(USER_ID_GET_URL_RSP_USER_ID);
    }

    private JSONObject httpRequestForUserId(String url) {
        RestTemplate restTemplate = new RestTemplate();
        try {
            String userInfo = restTemplate.getForObject(url, String.class);
            return JSON.parseObject(userInfo);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "user_id get error, error reason: {" +  e.getMessage() + "}");
        }
    }

    private JSONObject getUserInfo(String accessToken, String userId, Boolean accessTokenRetry) {
        String url = String.format(zwtConfig.getUserInfoGetUrl(), accessToken, userId);
        JSONObject userInfoResult = httpRequestForUserInfo(url);
        int errCode = userInfoResult.getInteger(ZWT_URL_RSP_ERROR_CODE);
        if (errCode != 0) {
            String errorMsg = userInfoResult.getString(ZWT_URL_RSP_ERROR_MSG);
            if (errCode == 40014 && accessTokenRetry) {
                log.error("user_info get error, error reason: {}", errorMsg);
                log.warn("access_token will fetch again……(because of user_info get error)");
                String newAccessToken = getAccessToken(true);
                return getUserInfo(newAccessToken, userId, false);
            }
            throw new BusinessException(ResultCode.FAIL, "user_info get error, error reason: {" +  errorMsg + "}");
        }
        return userInfoResult;
    }

    private JSONObject httpRequestForUserInfo(String url) {
        RestTemplate restTemplate = new RestTemplate();
        try {
            String userInfo = restTemplate.getForObject(url, String.class);
            return JSON.parseObject(userInfo);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "user_info get error, error reason: {" +  e.getMessage() + "}");
        }
    }
}
