package com.fykj.scaffold.security.business.api;

import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IUserService;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.HashMap;
import java.util.Map;

/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/api/sys/user")
public class SysUserApi {

    @Autowired
    private IUserService userService;

    @Autowired
    private RedisService redisService;

    @ApiOperation("code换取用户信息")
    @GetMapping("/userInfoCodeToUserInfo")
    public Result userInfoCodeToUserInfo(String userInfoCode) {
        final String redisKey = "USER-INFO-CODE" + userInfoCode;
        Long userId = redisService.get(redisKey);
        if (userId == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "code无效或已失效");
        }
        redisService.remove(redisKey);
        User user = userService.getById(userId);
        Map<String, Object> map = new HashMap<>();
        map.put("userId", user.getId());
        map.put("name", user.getName());
        map.put("mobile", user.getMobile());
        return new JsonResult<>(map);
    }
}
