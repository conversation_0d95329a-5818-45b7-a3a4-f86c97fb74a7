<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.weixin.mp.mapper.WechatMpMenuMapper">
    <select id="findFatherButtonByCode" resultType="com.fykj.scaffold.weixin.mp.domain.entity.WechatMpMenu">
        select * from wechat_mp_menu where is_deleted = false and  mp_code=#{code} and parent_id is null order by sequence asc
    </select>

    <select id="findSubButtonByCode" resultType="com.fykj.scaffold.weixin.mp.domain.entity.WechatMpMenu">
        select * from wechat_mp_menu where is_deleted =false and mp_code=#{code} and parent_id =#{parentId} order by sequence asc
    </select>
</mapper>
