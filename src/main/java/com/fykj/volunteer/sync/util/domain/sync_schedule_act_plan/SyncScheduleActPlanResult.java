package com.fykj.volunteer.sync.util.domain.sync_schedule_act_plan;

import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShowResult;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncScheduleActPlanResult {

    /**
     * 成功失败标志
     */
    private String code;
    /**
     * 消息
     */
    private String msg;
    /**
     * 唯一编号
     */
    private String uniCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "scheduleplan")
    public static class ScheduleActPlan {
        private List<SyncScheduleActPlanResult> info;
    }
}
