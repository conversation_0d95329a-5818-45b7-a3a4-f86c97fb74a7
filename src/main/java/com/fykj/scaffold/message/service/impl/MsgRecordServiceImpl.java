package com.fykj.scaffold.message.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.domain.dto.MsgRecordExportDto;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.entity.MsgSend;
import com.fykj.scaffold.message.domain.param.MsgRecordParams;
import com.fykj.scaffold.message.mapper.MsgRecordMapper;
import com.fykj.scaffold.message.service.IMsgRecordService;
import com.fykj.scaffold.message.service.IMsgSendService;
import com.fykj.scaffold.mq.cons.TopicCons;
import com.fykj.scaffold.mq.event.MsgSendEvent;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.utils.MessagesApiUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.support.utils.RegexUtils;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.config.RedisService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.message.conns.MsgCons.*;
import static constants.Mark.COMMA;

/**
 * 消息记录实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Service
@Slf4j
public class MsgRecordServiceImpl extends BaseServiceImpl<MsgRecordMapper, MsgRecord> implements IMsgRecordService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IMsgSendService sendService;

    @Autowired
    private IZyzVolunteerService volunteerService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Autowired
    private RedisService redisService;

    @Value("${send-message}")
    private Boolean isSendMsg = false;

    @Override
    public void handleEverySendTypeData(String sendType, MsgRecord receiver, MsgSend entity, List<MsgRecord> records) {
        List<String> sendTypeList = Arrays.asList(sendType.split(COMMA));
        sendTypeList.forEach(im -> {
            MsgRecord record = new MsgRecord();
            record.setReceiver(receiver.getReceiver());
            record.setReceiverName(receiver.getReceiverName());
            record.setReceiverMobile(receiver.getReceiverMobile());
            handleContent(receiver, entity, record);
            record.setSendType(im).setStatus(Boolean.FALSE).setSendStatus(MSG_PUSH_STATUS_WAIT).setSelfDefine(entity.getSelfDefine())
                    .setTitle(entity.getTitle()).setSendId(entity.getId()).setSendCode(entity.getSendCode())
                    .setSender((Long) Oauth2Util.getUserId()).setSendFailReason(null).setSendTime(null);
            records.add(record);
        });
    }

    private void handleContent(MsgRecord receiver, MsgSend entity, MsgRecord record) {
        String rnReplaceStr = StringUtil.isEmpty(receiver.getReceiverName()) ? receiver.getReceiverMobile() : receiver.getReceiverName();
        String puReplaceStr = StringUtil.isEmpty(entity.getPageUrl()) ? Strings.EMPTY : entity.getPageUrl();
        if (!entity.getSelfDefine()) {
            record.setContent(entity.getMsgContent().replace("%rn", rnReplaceStr).replace("%pu", puReplaceStr));
        } else {
            record.setContent(entity.getSelfDefineMsg().replace("%rn", rnReplaceStr).replace("%pu", puReplaceStr));
        }
    }

    @Override
    public IPage<MsgRecord> page(BaseParams params) {
        if (ObjectUtils.isEmpty(params)) {
            params = new MsgRecordParams();
        }
        IPage<MsgRecord> result = baseMapper.listOrPage(params.getPage(), (MsgRecordParams) params).convert(this::convert);
        DictTransUtil.trans(result.getRecords());
        return result;
    }

    private MsgRecord convert(MsgRecord entity) {
        if (ObjectUtil.isNotEmpty(entity.getSender())) {
            entity.setSenderName(userService.getById(entity.getSender()).getName());
        }
        entity.setReceiverVolunteer(ObjectUtil.isNotEmpty(entity.getReceiver()));
        return entity;
    }

    @Override
    public boolean readBatch(String ids) {
        String[] idList = ids.split(Mark.COMMA);
        List<MsgRecord> records = new ArrayList<>();
        for (String id : idList) {
            MsgRecord record = super.getById(id);
            record.setStatus(true);
            records.add(record);
        }
        return updateBatchById(records);
    }

    @Override
    public List<MsgRecordExportDto> export(MsgRecordParams params) {
        if (ObjectUtils.isEmpty(params)) {
            params = new MsgRecordParams();
        }
        List<MsgRecord> records = baseMapper.listOrPage(params);
        if (CollectionUtil.isEmpty(records)) {
            return null;
        }
        List<MsgRecordExportDto> result = new ArrayList<>();
        records.forEach(it -> {
            convert(it);
            MsgRecordExportDto dto = new MsgRecordExportDto();
            BeanUtil.copyProperties(it, dto);
            dto.setSendStatus(MSG_PUSH_STATUS_WAIT.equals(it.getSendStatus()) ? "待推送" : (MSG_PUSH_STATUS_SUCCESS.equals(it.getSendStatus()) ? "推送成功" : "推送失败"));
            dto.setSmsTaskId(it.getTaskId());
            dto.setSmsSendStatus(MSG_REAL_SEND_STATUS_SUCCESS.equals(it.getSmsSendStatus()) ? "发送成功" : (MSG_REAL_SEND_STATUS_FAILED.equals(it.getSmsSendStatus()) ? "发送失败" : it.getSmsSendStatus()));
            result.add(dto);
        });
        return result;
    }

    @Override
    public MsgRecord getById(Serializable id) {
        MsgRecord record = super.getById(id);
        return convert(record);
    }

    @Override
    public Map<String, Integer> sendProgress(Long sendId) {
        List<MsgRecord> records = lambdaQuery().eq(MsgRecord::getSendId, sendId).list();
        Map<String, Integer> result = new HashMap<>();
        result.put(MSG_PUSH_SUMMARY_DIMENSION_TOTAL, records.size());
        result.put(MSG_PUSH_SUMMARY_DIMENSION_DONE, (int) records.stream().filter(it -> !MSG_PUSH_STATUS_WAIT.equals(it.getSendStatus())).count());
        result.put(MSG_PUSH_SUMMARY_DIMENSION_SUCCESS, (int) records.stream().filter(it -> MSG_PUSH_STATUS_SUCCESS.equals(it.getSendStatus())).count());
        result.put(MSG_PUSH_SUMMARY_DIMENSION_FAIL, (int) records.stream().filter(it -> MSG_PUSH_STATUS_FAIL.equals(it.getSendStatus())).count());
        return result;
    }

    @Override
    public void removeNotSendRecord(Long sendId) {
        baseMapper.removeNotSendRecord(sendId);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void sendMessage(Long sendId) {
        List<MsgRecord> needSend = lambdaQuery().eq(MsgRecord::getSendId, sendId).eq(MsgRecord::getSendStatus, MSG_PUSH_STATUS_WAIT).list();
        if (CollectionUtil.isEmpty(needSend)) {
            sendService.lambdaUpdate().eq(MsgSend::getId, sendId).set(MsgSend::getStatus, MSG_SEND_STATUS_DONE).update();
            return;
        }
        List<MsgRecord> siteRecords = needSend.stream().filter(it -> MSG_PUSH_TYPE_SITE.equals(it.getSendType())).collect(Collectors.toList());
        List<MsgRecord> smsRecords = needSend.stream().filter(it -> MSG_PUSH_TYPE_SMS.equals(it.getSendType())).collect(Collectors.toList());
        // 老版-线程处理消息发送
//        dealMsgRecordsPrevious(siteRecords, smsRecords, sendId);
        // 新版-mq处理消息发送
        dealMsgRecords(siteRecords, smsRecords, sendId);
    }

    private void dealMsgRecordsPrevious(List<MsgRecord> siteRecords, List<MsgRecord> smsRecords, Long sendId) {
        if (CollectionUtil.isNotEmpty(siteRecords)) {
            sendSiteMsg(siteRecords);
        }
        if (CollectionUtil.isNotEmpty(smsRecords)) {
            sendSmsMsg(smsRecords);
        }
        updateSendStatus(sendId);
    }

    private void dealMsgRecords(List<MsgRecord> siteRecords, List<MsgRecord> smsRecords, Long sendId) {
        if (CollectionUtil.isNotEmpty(siteRecords)) {
            sendSiteMsg(siteRecords);
        }
        if (CollectionUtil.isEmpty(smsRecords)) {
            updateSendStatus(sendId);
            return;
        }
        if (!isSendMsg) {
            updateRecordSendStatus(null, smsRecords.stream().map(MsgRecord::getId).collect(Collectors.toList()), MSG_PUSH_STATUS_FAIL, "短信发送功能关闭！", null);
            updateSendStatus(sendId);
            return;
        }
        redisService.set(MSG_SEND_REDIS_KEY_PREFIX.concat(String.valueOf(sendId)), smsRecords.size());
        smsRecords.forEach(it -> mqTemplate.send(TopicCons.MSG_SEND, MessageBuilder.withPayload(MsgSendEvent.builder().recordId(it.getId()).build()).build()));
    }

    private void sendSiteMsg(List<MsgRecord> records) {
        records.forEach(it -> {
            Long id = it.getId();
            if (StringUtil.isEmpty(it.getReceiver())) {
                updateRecordSendStatus(id, null, MSG_PUSH_STATUS_FAIL, "用户账号未知！", null);
                return;
            }
            if (ObjectUtils.isEmpty(volunteerService.lambdaQuery().eq(ZyzVolunteer::getId, it.getReceiver()).eq(ZyzVolunteer::getWriteOff, false).one())) {
                updateRecordSendStatus(id, null, MSG_PUSH_STATUS_FAIL, "用户账号不存在！", null);
                return;
            }
            updateRecordSendStatus(id, null, MSG_PUSH_STATUS_SUCCESS, null, null);
        });
    }

    private void sendSmsMsg(List<MsgRecord> records) {
        if (!isSendMsg) {
            updateRecordSendStatus(null, records.stream().map(MsgRecord::getId).collect(Collectors.toList()), MSG_PUSH_STATUS_FAIL, "短信发送功能关闭！", null);
            return;
        }
        records.forEach(it -> dealSmsSendRecord(it, false));
    }

    private void dealSmsSendRecord(MsgRecord record, Boolean updateRedis) {
        Long id = record.getId();
        if (StringUtil.isEmpty(record.getReceiverMobile())) {
            updateRecordSendStatus(id, null, MSG_PUSH_STATUS_FAIL, "接收者手机号未知！", null);
            updateMsgSendRedis(record.getSendId(), updateRedis);
            return;
        }
        if (!RegexUtils.isMobileExact(record.getReceiverMobile())) {
            updateRecordSendStatus(id, null, MSG_PUSH_STATUS_FAIL, "手机号格式有误，请核查！", null);
            updateMsgSendRedis(record.getSendId(), updateRedis);
            return;
        }
        dealSmsSendResult(id, record, updateRedis);
    }

    private void dealSmsSendResult(Long id, MsgRecord record, Boolean updateRedis) {
        try {
            MessagesApiUtil.Result result = MessagesApiUtil.sendShortMessage(record.getContent(), record.getReceiverMobile());
            boolean resultFlag = MessagesApiUtil.ReturnStatus.SUCCESS.equals(result.getReturnstatus());
            Integer status = resultFlag ? MSG_PUSH_STATUS_SUCCESS : MSG_PUSH_STATUS_FAIL;
            String failReason = resultFlag ? null : result.getMessage();
            updateRecordSendStatus(id, null, status, failReason, result.getTaskID());
            updateMsgSendRedis(record.getSendId(), updateRedis);
        } catch (Exception e) {
            String failReason = e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage();
            updateRecordSendStatus(id, null, MSG_PUSH_STATUS_FAIL, failReason, null);
            updateMsgSendRedis(record.getSendId(), updateRedis);
        }
    }

    private void updateRecordSendStatus(Long id, List<Long> ids, Integer status, String failReason, String taskId) {
        lambdaUpdate().eq(ObjectUtil.isNotEmpty(id), MsgRecord::getId, id)
                .in(CollectionUtil.isNotEmpty(ids), MsgRecord::getId, ids)
                .set(MsgRecord::getSendStatus, status)
                .set(MsgRecord::getSendFailReason, failReason)
                .set(MsgRecord::getSendTime, LocalDateTime.now())
                .set(MsgRecord::getTaskId, taskId).update();
    }

    private void updateMsgSendRedis(Long sendId, Boolean updateRedis) {
        if (!updateRedis) {
            return;
        }
        String key = MSG_SEND_REDIS_KEY_PREFIX.concat(String.valueOf(sendId));
        long value = redisService.decrementAndGet(key);
        if (value > 0) {
            return;
        }
        redisService.remove(key);
        updateSendStatus(sendId);
    }

    private void updateSendStatus(Long sendId) {
        sendService.lambdaUpdate().eq(MsgSend::getId, sendId).set(MsgSend::getStatus, MSG_SEND_STATUS_DONE).update();
    }

    @Override
    public void sendMsgRecord(Long recordId) {
        MsgRecord record = getById(recordId);
        dealSmsSendRecord(record, true);
    }

    @Override
    public void dealMqConsumeFailRecord(Long recordId, String consumeFailReason) {
        MsgRecord record = getById(recordId);
        updateRecordSendStatus(recordId, null, MSG_PUSH_STATUS_FAIL, "消息推送mq数据消费失败！失败原因：" + consumeFailReason, null);
        updateMsgSendRedis(record.getSendId(), true);
    }

    @Override
    public IPage<MsgRecord> pageForMaintain(MsgRecordParams params) {
        if (ObjectUtils.isEmpty(params) || ObjectUtils.isEmpty(params.getSendId())) {
            throw new BusinessException(ResultCode.FAIL, "发送批次未知！");
        }
        return baseMapper.pageForMaintain(params.getPage(), params);
    }

    @Override
    public void updateRecord(MsgRecord record) {
        MsgRecord exist = null;
        List<MsgRecord> exists = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(record.getId())) {
            exist = super.getById(record.getId());
            exists = lambdaQuery().eq(MsgRecord::getSendId, exist.getSendId()).eq(MsgRecord::getReceiverMobile, exist.getReceiverMobile()).list();
        }
        boolean flag = lambdaQuery()
                .notIn(CollectionUtil.isNotEmpty(exists), MsgRecord::getId,
                        exists.stream().map(MsgRecord::getId).collect(Collectors.toList()))
                .eq(MsgRecord::getReceiverMobile, record.getReceiverMobile())
                .eq(MsgRecord::getSendId, record.getSendId())
                .exists();
        if (flag) {
            throw new BusinessException(ResultCode.FAIL, "该接收者手机号已存在，无需重复添加！");
        }
        if (ObjectUtil.isNotEmpty(record.getId())) {
            dealUpdate(record, exists);
            return;
        }
        dealSave(record);
    }

    private void dealUpdate(MsgRecord record, List<MsgRecord> needUpdate) {
        needUpdate.forEach(it -> {
            it.setReceiver(record.getReceiver());
            it.setReceiverMobile(record.getReceiverMobile());
            it.setReceiverName(record.getReceiverName());
        });
        super.updateBatchById(needUpdate);
    }

    private void dealSave(MsgRecord record) {
        MsgSend send = sendService.lambdaQuery().eq(MsgSend::getId, record.getSendId()).one();
        if (ObjectUtil.isEmpty(send)) {
            throw new BusinessException(ResultCode.FAIL, "消息发送批次不存在!");
        }
        List<MsgRecord> needSave = new ArrayList<>();
        handleEverySendTypeData(send.getSendType(), record, send, needSave);
        if (CollectionUtil.isNotEmpty(needSave)) {
            super.saveBatch(needSave);
        }
    }

    @Override
    public void removeRecord(Long id) {
        MsgRecord exist = super.getById(id);
        List<MsgRecord> needRemove = lambdaQuery().eq(MsgRecord::getSendId, exist.getSendId())
                .eq(MsgRecord::getReceiverMobile, exist.getReceiverMobile()).list();
        super.removeBatchByIds(needRemove);
    }

    @Override
    public List<MsgRecord> getListByTitle(String title) {
        return lambdaQuery().like(MsgRecord::getTitle, title).list();
    }
}

