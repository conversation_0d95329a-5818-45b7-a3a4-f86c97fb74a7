package com.fykj.scaffold.onenet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hanweb.common.sviolet.smcrypto.SmCryptoUtil;
import exception.BusinessException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import result.ResultCode;
import utils.LocalDateTimeUtil;
import utils.StringUtil;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 一网通办接口调用类
 *
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class OneNetSupport {

    /**
     * 一网通办平台AppMark
     */
    private static final String APP_MARK = "YuanQuZhiYuanFuWu";

    /**
     * 一网通办平台App密钥
     */
    private static final String APP_WORD = "ZXVwNzJTvgUkgriA";
    /**
     * 里约网关密钥
     */
    private static final String LY_SECRET = "LKUOkSiAzYosZoTPme9PMzIx8nLNwWM6";
    /**
     * 里约网关标识
     */
    private static final String LY_APPID = "sipzyz_wechatminiprogram";

    /**
     * 一网通办平台数据加密公钥
     */
    private static final String DATA_PUBLIC_KEY = "***********a1933aaf063df754031de65672347bd9631b1c3a8ecc420cf1fbd3bcddb36d091a175f3227db23a4190f1f0ade673ecad68412478d64c1477ac7af9";

    /**
     * 一网通办平台数据加密私钥
     */
    private static final String DATA_PRIVATE_KEY = "009f61d3546ac5126445785e7cd9e6cce4b6811677bd93ae4cc9d0a1a8bb9c8d9e";

    /**
     * 一网通办平台接口请求地址
     */
    private static final String OAUTH_API = "https://zwyyone.sipac.gov.cn/ebus/ywtbsfrz/usercenter/interfaces/jissso_packaging.do?txwg_appid=sipzyz_wechatminiprogram";


    /**
     * 从ticket换取token
     *
     * @param ticket ticket凭证
     * @return token对象
     */
    public static OneNetToken getToken(String ticket) {
        JSONObject ticketJson = new JSONObject();
        ticketJson.put("ticket", ticket);
        OneNetParams params = getParam("ticketValidate", ticketJson.toJSONString());
        String result = execute(params, OAUTH_API);
        return JSON.parseObject(result, OneNetToken.class);
    }

    /**
     * 从token换取用户信息
     *
     * @param token token字符串
     * @return 用户信息
     */
    public static OneNetUser getUser(String token) {
        JSONObject tokenJson = new JSONObject();
        tokenJson.put("token", token);
        OneNetParams params = getParam("findOutsideUserByToken", tokenJson.toJSONString());
        String result = SmCryptoUtil.sm2Decode(execute(params, OAUTH_API), DATA_PRIVATE_KEY);
        return JSON.parseObject(result, OneNetUser.class);
    }

//    /**
//     * 保存办件
//     *
//     * @param jsonString
//     * @param apiUrl
//     * @return
//     */
//    public static Result saveBn(String jsonString, String apiUrl) {
//        JSONObject jsonObject = JSON.parseObject(jsonString);
//        OneNetObject params = getObjectParam("wtjk", jsonObject);
//        return execute(params, apiUrl);
//    }

    /**
     * 鉴权请求头构建
     *
     * @return 组装好的请求头
     */
    public static OneNetParams getParam(String serviceName, String params) {
        String timeSpan = LocalDateTimeUtil.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss");
        String sign = DigestUtils.md5Hex(APP_MARK.concat(APP_WORD).concat(timeSpan));
        OneNetParams param = new OneNetParams();
        param.setAppmark(APP_MARK);
        param.setSign(sign);
        param.setTime(timeSpan);
        param.setServicename(serviceName);
        param.setParams(params);
        return param;
    }

    /**
     * 鉴权请求头构建
     *
     * @return 组装好的请求头
     */
    private static Headers authHeader() {
        String nonce = "nonce";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String signature = OneNetSHA256Util.getSHA256(timestamp + LY_SECRET + nonce + timestamp);
        return new Headers.Builder()
                .add("x-tif-paasid", LY_APPID)
                .add("x-tif-timestamp", timestamp)
                .add("x-tif-nonce", nonce)
                .add("x-tif-signature", signature)
                .build();
    }

    /**
     * 执行请求，返回请求体
     *
     * @param params
     * @param url
     * @return
     */
    public static String execute(OneNetParams params, String url) {
        RequestBody requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), JSON.toJSON(params).toString());
        Request request = new Request.Builder()
                .headers(authHeader())
                .post(requestBody)
                .url(url).build();
        try {
            Response response = new OkHttpClient().newCall(request).execute();
            if (response.body() == null) {
                log.error("请求一网通办出错,请求体为空:{}", response);
                throw new BusinessException(ResultCode.BAD_REQUEST, "请求一网通办出错");
            }
            String json = response.body().string();
            JSONObject obj = JSON.parseObject(json);
            if (!StringUtil.equals(obj.getString("retcode"), "000000")) {
                log.error("请求一网通办出错,请求体为空:{}", json);
                throw new BusinessException(ResultCode.FAIL, "请求一网通办出错,请求体为空:" + obj.getString("msg"));
            }
            String data = obj.getString("data");
            log.info("请求一网通办成功:{}", data);
            return data;
        } catch (IOException e) {
            log.error("请求一网通办出错", e);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求一网通办出错", e);
        }
    }
}
