package com.fykj.scaffold.mall.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallSelfMachine;
import com.fykj.scaffold.mall.domain.params.MallSelfMachineParams;
import com.fykj.scaffold.mall.service.IMallSelfMachineService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

import java.util.List;

/**
 *
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@RestController
@RequestMapping("/admin/mall/self-machine")
public class MallSelfMachineController extends BaseController<IMallSelfMachineService, MallSelfMachine, MallSelfMachineParams> {

    @ApiOperation("兑换机分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallSelfMachine>> getPages(@RequestBody MallSelfMachineParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @ApiOperation("获取当前登录人组织及下级组织下的兑换机")
    @GetMapping("/getMachinesByCurrOrg")
    public JsonResult<List<MallSelfMachine>> getMachinesByCurrOrg() {
        return new JsonResult<>(baseService.getMachinesByCurrOrg());
    }
}
