package com.fykj.scaffold.ledger.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.ledger.domain.dto.DepartmentImportDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerDepartment;
import com.fykj.scaffold.ledger.domain.params.LedgerDepartmentParams;
import com.fykj.scaffold.ledger.service.ILedgerDepartmentService;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.beans.vo.IdTextVo;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 台账责任局办-接口控制器
 *
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/admin/ledger/department")
@Api(tags = "台账责任局办-管理接口")
public class LedgerDepartmentController extends BaseController<ILedgerDepartmentService, LedgerDepartment, LedgerDepartmentParams> {

    @ApiOperation("获取下拉列表")
    @GetMapping("/selectList")
    public JsonResult<List<IdTextVo>> selectList() {
        return new JsonResult<>(baseService.selectList());
    }


    @ApiOperation("分页查询")
    @PostMapping({"/pages"})
    public JsonResult<IPage<LedgerDepartment>> list(@RequestBody(required = false) LedgerDepartmentParams params) {
        IPage<LedgerDepartment> result = this.baseService.pages(params);
        return new JsonResult(result);
    }

    @ApiOperation("根据id获取")
    @GetMapping
    public Result get(@RequestParam Long id) {
        return new JsonResult(this.baseService.get(id));
    }


    @SysLogMethod("新增")
    @ApiOperation("保存方法")
    @PostMapping({"/save"})
    public Result save(@RequestBody @Validated({BaseEntity.Add.class}) LedgerDepartment entity) {
        boolean result = this.baseService.saveDepartment(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @SysLogMethod("编辑")
    @ApiOperation("更新方法")
    @PostMapping({"/update"})
    public Result update(@RequestBody @Validated({BaseEntity.Modify.class}) LedgerDepartment entity) {
        return this.baseService.updateDepartmentById(entity) ? OK : new Result(ResultCode.DATA_EXPIRED);
    }

    @ApiOperation("指标项导入Excel模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/ledger_index_department_template.xlsx");
    }

    @ApiOperation("导入")
    @PostMapping(value = "/import")
    public void dataImport(@RequestParam("excel") MultipartFile excel) {
        List<DepartmentImportDto> failureList = baseService.dataImport(excel);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "ledger_index_department_error.xlsx", DepartmentImportDto.class);
        }
    }

    @ApiOperation("上下架")
    @GetMapping("/setStatus")
    public Result setStatus(Long id) {
        baseService.setStatus(id);
        return OK;
    }

    @ApiOperation("删除方法")
    @GetMapping(value = "/deleteById")
    public Result deleteById(@RequestParam Long id) {
        boolean result = baseService.deleteById(id);
        if (result) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }
}
