package com.fykj.scaffold.security.business.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 审核模型
 *
 * <AUTHOR>
 * @date: 2022/2/24 9:30
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("级联选择模型")
public class CascaderDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long value;

    @ApiModelProperty(value = "名称")
    private String label;

    @ApiModelProperty(value = "子节点")
    private List<CascaderDto> children;

}
