package com.fykj.scaffold.zyz.controller.mini.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.dynamic.domain.entity.DynamicFieldSnapshot;
import com.fykj.scaffold.dynamic.service.IDynamicFieldSnapshotService;
import com.fykj.scaffold.portal_website.conns.PortalWebsiteCons;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;
import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ActivityApiDto;
import com.fykj.scaffold.zyz.domain.dto.ApplyAppraiseDto;
import com.fykj.scaffold.zyz.domain.dto.TeamApiDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApplyPublic;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirement;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityParams;
import com.fykj.scaffold.zyz.service.*;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static fykj.microservice.core.base.AbstractController.OK;

/**
 * 前端活动相关接口（无需token）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/activity")
@Api(value = "前端活动相关接口（无需token）")

public class MiniActivityApi {

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzSyncBelongFieldDictService belongFieldDictService;

    @Autowired
    private INewsService newsService;

    @Autowired
    private IZyzActivityAppraiseService appraiseService;


    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private IZyzActivityApplyPublicService applyPublicService;

    @Autowired
    private IZyzActivityApplyService applyService;

    @Autowired
    private IDynamicFieldSnapshotService fieldSnapshotService;

    @Autowired
    private IZyzTeamService teamService;

    @ApiOperation("筛选活动-所属领域（活动大厅）")
    @GetMapping({"/getBelongFieldList"})
    public JsonResult<List<ZyzSyncBelongFieldDict>> getBelongFieldList() {
        return new JsonResult<>(belongFieldDictService.lambdaQuery().eq(ZyzSyncBelongFieldDict::getNodePath, 1).list());
    }

    @ApiOperation("活动分页查询（活动大厅）")
    @PostMapping({"/pages"})
    public JsonResult<IPage<ActivityApiDto>> pages(@RequestBody ZyzActivityParams params) {
        return new JsonResult<>(activityService.pageForActHall(params));
    }

    @ApiOperation("活动信息（活动详情）")
    @GetMapping({"/getById"})
    public JsonResult<ActivityApiDto> getById(@RequestParam Long id) {
        ActivityApiDto activity = activityService.getByIdForActDetail(id);

        // 如果是团队发布且teamId不为空，查询团队信息
        if (Boolean.TRUE.equals(activity.getTeamPublish()) && activity.getTeamId() != null) {
            TeamApiDto teamInfo = teamService.getTeamApiDtoById(activity.getTeamId());
            activity.setTeamInfo(teamInfo);
        }

        return new JsonResult<>(activity);
    }

    @ApiOperation("相关活动推荐")
    @GetMapping({"/getByFieldId"})
    public JsonResult<List<ActivityApiDto>> getByFieldId(@RequestParam String fileId) {
        List<ActivityApiDto> apis = activityService.lambdaQuery().eq(ZyzActivity::getWebShow, true)
                .apply("(belong_field_top = " + fileId + " OR belong_field_end =" + fileId + ")")
                .eq(ZyzActivity::getAuditStatus, ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS)
                .orderByDesc(ZyzActivity::getStartTime).list().stream().limit(6)
                .collect(Collectors.toList()).stream().map(it -> {
                    ActivityApiDto dto = new ActivityApiDto();
                    BeanUtils.copyProperties(it, dto);
                    dto.setActId(it.getId());
                    return dto;
                }).collect(Collectors.toList());
        return new JsonResult<>(apis);
    }

    @ApiOperation(value = "获取活动关联的需求")
    @GetMapping(value = "/getActivityRequirement")
    public JsonResult<ZyzRequirement> getActivityRequirement(long activityId) {
        ZyzActivity activity = activityService.getById(activityId);
        if (activity.getReqId() == null ) {
            return new JsonResult<>();
        }
        ZyzRequirement requirement = requirementService.getById(activity.getReqId());
        DesensitiseUtil.desensitise(requirement);
        return new JsonResult<>(requirement);
    }

    @ApiOperation(value = "获取活动关联的资源")
    @GetMapping(value = "/getActivityResource")
    public JsonResult<ZyzResource> getActivityResource(long activityId) {
        ZyzActivity activity = activityService.getById(activityId);
        if (activity.getResId() == null) {
            return new JsonResult<>();
        }
        ZyzResource resource = resourceService.getById(activity.getResId());
        DesensitiseUtil.desensitise(resource);
        return new JsonResult<>(resource);
    }


    @ApiOperation(value = "获取活动关联的新闻")
    @GetMapping(value = "/getActivityNews")
    public JsonResult<News> getActivityNews(long activityId) {
        News news = newsService.lambdaQuery().eq(News::getActivityId, activityId).eq(News::getAuditStatus, PortalWebsiteCons.NAS_PASS).one();
        if (news == null) {
            return new JsonResult<>();
        }
        return new JsonResult<>(news);
    }

    @ApiOperation(value = "群众报名接口")
    @PostMapping(value = "/saveActivityForPublic")
    public Result saveActivityForPublic(@RequestBody ZyzActivityApplyPublic entity) {
        applyPublicService.saveActivityForPublic(entity);
        return OK;
    }

    @ApiOperation(value = "获取公开招募表单字段")
    @GetMapping(value = "/getPublicApplyFormFields")
    public JsonResult<List<DynamicFieldSnapshot>> saveActivityForPublic(
           @ApiParam("公开招募表单快照ID") Long applyPublicFormSnapshotId) {
        List<DynamicFieldSnapshot> fields = fieldSnapshotService.getBySnapshotIdAndModuleCodeForApi(
                applyPublicFormSnapshotId, ZyzCons.ZYZ_ACTIVITY_PUBLIC_APPLY_MODULE_CODE);
        return new JsonResult<>(DictTransUtil.trans(fields));
    }

    //取消报名
    @ApiOperation(value = "取消报名")
    @GetMapping(value = "/cancelApply")
    public Result cancelApply(String applyPublicIds, Long timePeriodId) {
        List<Long> ids = Arrays.stream(applyPublicIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        applyPublicService.cancelApply(ids, timePeriodId);
        return OK;
    }

    @ApiOperation(value = "获取群众报名信息")
    @GetMapping(value = "/getApplyPublic")
    public JsonResult<ZyzActivityApplyPublic> getApplyPublic(Long applyPublicId) {
        return new JsonResult<>(applyPublicService.getById(applyPublicId));
    }

    @ApiOperation(value = "获取活动评价")
    @GetMapping(value = "/getActivityEvaluate")
    public JsonResult<List<ApplyAppraiseDto>> getActivityEvaluate(long activityId) {
        return new JsonResult<>(appraiseService.getActivityEvaluate(activityId));
    }

    @ApiOperation(value = "PAD获取团队活动列表")
    @PostMapping(value = "/getTeamActivityForPad")
    public JsonResult<IPage<ZyzActivity>> getTeamActivityForPad(@RequestBody ZyzActivityParams params) {
        return new JsonResult<>(activityService.getTeamActivityForPad(params));
    }

}
