package com.fykj.volunteer.sync.util.domain.sync_order;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <order>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>同步失败：实践单对接方编号不能为空</msg>
 *     <uniCode>102036</uniCode>
 *   </info>
 * </order>
 *
 *
 * <order>
 *   <info>
 *     <code>FAIL</code>
 *     <msg>同步失败：该实践单阵地不存在</msg>
 *     <uniCode>102036</uniCode>
 *   </info>
 * </order>
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncOrderResult {
    /**
     * 实践单编号
     */
    private String orderId;
    /**
     * 唯一编号
     */
    private String uniCode;
    /**
     * 消息
     */
    private String msg;
    /**
     * 成功失败标志
     */
    private String code;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "order")
    public static class Order {
        private List<SyncOrderResult> info;
    }
}
