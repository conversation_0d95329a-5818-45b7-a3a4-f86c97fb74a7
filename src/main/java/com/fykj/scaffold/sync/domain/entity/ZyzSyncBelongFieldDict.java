package com.fykj.scaffold.sync.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 市平台所属领域数据字典
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_sync_belong_field_dict")
public class ZyzSyncBelongFieldDict extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 类型id
     */
    @TableField("type_id")
    @ApiModelProperty(value = "类型id")
    private String typeId;

    /**
     * 服务领域名称
     */
    @TableField("type_name")
    @ApiModelProperty(value = "服务领域名称")
    private String typeName;

    /**
     * 父节点id
     */
    @TableField("father_id")
    @ApiModelProperty(value = "父节点id")
    private String fatherId;

    /**
     * 层级
     */
    @TableField("node_path")
    @ApiModelProperty(value = "层级")
    private String nodePath;

    @TableField(exist = false)
    @ApiModelProperty(value = "二级领域")
    private List<ZyzSyncBelongFieldDict> children;


    /**
     * 最小时间限制
     */
    @TableField(value = "min_time_limit", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "最小时间限制")
    private Integer minTimeLimit;

    /**
     * 最大时间限制
     */
    @TableField(value = "max_time_limit", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "最大时间限制")
    private Integer maxTimeLimit;
}
