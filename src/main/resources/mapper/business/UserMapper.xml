<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.security.business.mapper.UserMapper">
    <select id="findByPage" resultType="com.fykj.scaffold.security.business.domain.entity.User">
        SELECT
            *
        FROM (
            SELECT
                u.*,
                CONCAT(',', GROUP_CONCAT(DISTINCT r.code ORDER BY r.code SEPARATOR ','), ',') AS roleCodesStr,
                GROUP_CONCAT(DISTINCT r.name ORDER BY r.code SEPARATOR ',') AS roleNames
                FROM sys_user u
                LEFT JOIN sys_user_role ur ON u.id = ur.user_id AND ur.is_deleted = 0
                LEFT JOIN sys_role r ON ur.role_id = r.id
                GROUP BY u.id
        ) AS nt
        WHERE nt.is_deleted = 0
        <if test="params.keyword != null and params.keyword != ''">
            AND (nt.name LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
                OR nt.username LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
                OR nt.email LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
                OR nt.mobile LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
                )
        </if>
        <if test="params.username != null and params.username != ''">
            AND nt.username LIKE CONCAT('%', CONCAT(#{params.username}, '%'))
        </if>
        <if test="params.name != null and params.name != ''">
            AND nt.name LIKE CONCAT('%', CONCAT(#{params.name}, '%'))
        </if>
        <if test="params.mobile != null and params.mobile != ''">
            AND nt.mobile LIKE CONCAT('%', CONCAT(#{params.mobile}, '%'))
        </if>
        <if test="params.status != null">
            AND nt.status = #{params.status}
        </if>
        <if test="params.managerAccount != null">
            AND nt.is_manager_account = #{params.managerAccount}
        </if>
        <if test="params.adminType != null and params.adminType != ''">
            AND (nt.roleCodesStr LIKE CONCAT('%', CONCAT(#{params.adminType}, ',%'))
                OR nt.roleCodesStr = #{params.adminType}
                )
        </if>
        <if test="params.roleCodes != null and params.roleCodes.size > 0">
            AND
            <foreach item="item" collection="params.roleCodes" separator="OR" open="(" close=")" index="">
                roleCodesStr LIKE CONCAT('%,', #{item}, ',%')
            </foreach>
        </if>
        ORDER BY nt.create_date DESC
    </select>

    <select id="pageOrListForSubAssociationAdmin" resultType="com.fykj.scaffold.security.business.domain.entity.User">
        SELECT
            nt.*
        FROM (
            SELECT
                u.*,
                GROUP_CONCAT(DISTINCT CASE WHEN o1.id IS NOT NULL THEN 'SUB_ASSOCIATION_ADMIN'
                                        WHEN o2.id IS NOT NULL THEN 'COMMUNITY_ADMIN'
                                        ELSE NULL END SEPARATOR ',') AS roleCodesStr,
                GROUP_CONCAT(DISTINCT CASE WHEN o1.id IS NOT NULL THEN '分协会管理员'
                                        WHEN o2.id IS NOT NULL THEN '社区管理员'
                                        ELSE NULL END SEPARATOR ',') AS roleNames,
                GROUP_CONCAT(DISTINCT o1.code ORDER BY o1.sequence SEPARATOR ',') AS saCodes,
                GROUP_CONCAT(DISTINCT o2.code ORDER BY o2.sequence SEPARATOR ',') AS communityCodes,
                GROUP_CONCAT(DISTINCT o1.name ORDER BY o1.sequence SEPARATOR ',') AS saNames,
                GROUP_CONCAT(DISTINCT o2.name ORDER BY o2.sequence SEPARATOR ',') AS communityNames
            FROM sys_user u
            INNER JOIN (
                SELECT
                    user_id,
                    org_code
                FROM sys_user_expand
                WHERE is_deleted = 0 AND org_code IN
                <foreach collection="params.orgCodes" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            ) AS ue ON u.id = ue.user_id
            LEFT JOIN sys_org o1 ON ue.org_code = o1.code AND o1.level = 2
            LEFT JOIN sys_org o2 ON ue.org_code = o2.code AND o2.level = 3
            GROUP BY u.id
        ) AS nt
        WHERE nt.is_deleted = 0 AND nt.is_manager_account = 1
        <if test="params.keyword != null and params.keyword != ''">
            AND (nt.name LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
            OR nt.username LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
            OR nt.mobile LIKE CONCAT('%', CONCAT(#{params.keyword}, '%'))
            )
        </if>
        <if test="params.adminType != null and params.adminType != ''">
            AND (nt.roleCodesStr LIKE CONCAT('%', CONCAT(#{params.adminType}, '%'))
                OR nt.roleCodesStr = #{params.adminType})
        </if>
        <if test="params.community != null and params.community != ''">
            AND (nt.communityCodes LIKE CONCAT('%', CONCAT(#{params.community}, '%'))
            OR nt.communityCodes = #{params.community})
        </if>
        ORDER BY nt.create_date DESC
    </select>

    <delete id="userWriteOffRemove">
        DELETE FROM sys_user WHERE id IN
        <foreach item="item" collection="ids" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>
</mapper>
