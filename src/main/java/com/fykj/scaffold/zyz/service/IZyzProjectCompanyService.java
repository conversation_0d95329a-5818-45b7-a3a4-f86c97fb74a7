package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.dto.IdNameDto;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectCompany;
import com.fykj.scaffold.zyz.domain.params.ProjectCompanyBindParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 公益伙伴计划-项目资助企业
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-24
 */
public interface IZyzProjectCompanyService extends IBaseService<ZyzProjectCompany> {

    /**
     * 获取项目资助企业列表
     *
     * @return
     */
    List<ZyzProjectCompany> getCompanyList();

    /**
     * 按年导出企业工艺伙伴参与计划
     *
     * @param year 年份，不传就查全部
     * @return 数据
     */
    List<ProjectCompanyContributeDto> exportCompanyContribute(Integer year);


    /**
     * 保存企业并返回企业信息
     *
     * @param entity 企业信息
     * @return 企业信息
     */
    ZyzProjectCompany saveCompany(ZyzProjectCompany entity);

    /**
     * 按年导出企业工艺伙伴参与计划
     *
     * @param year 年份，不传就查全部
     * @return 数据
     */
    List<CompanyDockingExportDto> getCompanyDockingList(Integer year);

    /**
     * 爱心企业关联项目查询
     *
     * @param params 参数
     * @return 分页结果
     */
    IPage<ProjectCompanyBindPageDto> bindProjectPage(ProjectCompanyBindParams params);


    /**
     * excel数据导入
     *
     * @param excelList 数据
     * @return 结果
     */
    List<ProjectCompanyImportDto> excelImport(List<ProjectCompanyImportDto> excelList);

    /**
     * 驾驶舱最新年份爱心企业
     *
     * @return
     */
    List<IdNameDto> cockpitCorpList();

    /**
     * 爱心企业数量
     * @return
     */
    int countLoveCorp();

    /**
     * 企业责任联盟爱心文明单位
     * @param creditCode
     * @return
     */
    CockpitCorpDto csrCockpit(String creditCode);
}

