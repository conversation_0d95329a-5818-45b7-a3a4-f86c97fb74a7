package com.fykj.scaffold.ledger.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fykj.scaffold.support.utils.Desensitise;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 台账责任局办-实体类
 *
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("ledger_department")
public class LedgerDepartment extends BaseEntity  {

	private static final long serialVersionUID = 7868989448992607647L;

	/**
	 * 局办名称
	 */
	@TableField("department_name")
    @ApiModelProperty(value = "局办名称")
	private String departmentName;

	/**
	 * 联系人
	 */
	@TableField("contact_person")
    @ApiModelProperty(value = "联系人")
	private String contactPerson;

	/**
	 * 联系方式
	 */
	@TableField("contact")
    @ApiModelProperty(value = "联系方式")
	private String contact;

	/**
	 * 用户id
	 */
	@TableField("user_id")
    @ApiModelProperty(value = "用户id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long userId;

	/**
	 * 状态
	 */
	@TableField("status")
    @ApiModelProperty(value = "状态")
	private Boolean status;
	/**
	 * 用户名
	 */
	@TableField(exist = false)
    @ApiModelProperty(value = "用户名")
	private String username;
	/**
	 * 用户名
	 */
	@Desensitise
	@TableField(exist = false)
    @ApiModelProperty(value = "手机号码")
	private String mobile;
	/**
	 * 名称
	 */
	@TableField(exist = false)
    @ApiModelProperty(value = "名称")
	private String name;

}
