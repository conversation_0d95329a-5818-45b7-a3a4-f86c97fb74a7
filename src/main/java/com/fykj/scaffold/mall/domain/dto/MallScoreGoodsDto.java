package com.fykj.scaffold.mall.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MallScoreGoodsDto {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 组织机构名称
     */
    @ExcelProperty("组织机构名称")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;
    /**
     * 地址
     */
    @ExcelProperty("兑换地址")
    @ApiModelProperty(value = "兑换地址")
    private String exchangeAddress;
    /**
     * 快递寄送积分
     */
    @ExcelProperty("快递寄送积分")
    @ApiModelProperty(value = "商品价格积分")
    private BigDecimal requiredScore;
    /**
     * 线下兑换积分
     */
    @ExcelProperty("线下兑换积分")
    @ApiModelProperty(value = "商品价格积分")
    private BigDecimal offlineScore;
    /**
     * 上架下架
     */
    @ExcelProperty("状态")
    @ApiModelProperty(value = "上架下架")
    private String statusText;
    /**
     * 负责人名称
     */
    @ExcelProperty("负责人名称")
    @ApiModelProperty(value = "负责人名称")
    private String linkPersonName;

    /**
     * 负责人联系电话
     */
    @ExcelProperty("负责人联系电话")
    @ApiModelProperty(value = "负责人联系电话")
    private String linkPhone;

    /**
     * 商品数量
     */
    @ExcelProperty("商品数量")
    @ApiModelProperty(value = "商品数量")
    private Integer goodsCount;





}
