package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzTypeTeamRelevance;

import java.util.List;

/**
 * 团队特色与团队关联表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-21
 */
public interface IZyzTypeTeamRelevanceService extends IService<ZyzTypeTeamRelevance>, IBaseService<ZyzTypeTeamRelevance> {

    /**
     * 保存团队的标签
     *
     * @param teamId     团队id
     * @param typeIdList 类型id列表
     * @return
     */
    boolean saveByTeamId(Long teamId, List<Long> typeIdList);

    IPage<ZyzTeam> getPagesByType(ZyzTeamParams params);

    List<ZyzTypeTeamRelevance> getAllRelevance();

    /**
     * 根据团队返回标签，带名称
     * @param teamId
     * @return
     */
    List<ZyzTypeTeamRelevance> getRelevanceByTeamId(long teamId);
}

