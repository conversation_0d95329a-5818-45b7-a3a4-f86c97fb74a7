package com.fykj.scaffold.zsq_docking.receive.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingLogService;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingNewsRecordService;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.NewsDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import com.fykj.scaffold.zsq_docking.receive.service.INewsReceiveFromZSQService;
import com.fykj.scaffold.zsq_docking.utils.ZSQHttpRequestUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.Result;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import static cn.hutool.core.text.StrPool.LF;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.NewsDataDockingType.DT_DELETE;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/api/zsq/receive/news")
@Api(tags = "知社区新闻接口接收")
public class NewsReceiveController {

    @Autowired
    private IZSQDockingLogService logService;

    @Autowired
    private IZSQDockingNewsRecordService newsDockingRecordService;

    @Autowired
    private INewsReceiveFromZSQService newsService;

    @Autowired
    private ZSQHttpRequestUtils zsqHttpRequestUtils;

    @ApiOperation("新闻对接")
    @PostMapping({"/docking"})
    public Result docking(@RequestBody NewsDockingDto dockingInfo) {
        Long dockingRecordId = dockingInfo.getDockingRecordId();
        ZSQDockingNewsRecord dockingRecord = null;
        if (dockingRecordId != null){
            dockingRecord = newsDockingRecordService.lambdaQuery().eq(ZSQDockingNewsRecord::getId, dockingRecordId).one();
            if (dockingRecord == null){
                return new Result(ResultCode.NOT_FOUND.code(), "对接id{" + dockingRecordId + "}对应的对接记录不存在！");
            }
        }
        if (dockingRecord == null) {
            dockingRecord = new ZSQDockingNewsRecord();
            dockingRecord.setDockingType(DOCKING_TYPE_RECEIVE);
            dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_PENDING);
            dockingRecord.setDockingTime(LocalDateTime.now());
            dockingRecord.setDockingData(JSONObject.toJSONString(dockingInfo));
            dockingRecord.setRetry(Boolean.FALSE);
            newsDockingRecordService.save(dockingRecord);
        }
        Long newsId = dockingInfo.getNewsId();
        List<String> validFailMsgList = new ArrayList<>();
        if (newsId == null) {
            validFailMsgList.add("新闻id未获取到！");
        }
        String operation = dockingInfo.getOperation();
        if (StringUtils.isEmpty(operation)) {
            validFailMsgList.add("新闻操作类型未获取到！");
        }
        String zwtUserId = dockingInfo.getZwtUserId();
        if (StringUtils.isEmpty(zwtUserId)) {
            validFailMsgList.add("新闻操作人政务通userid未获取到！");
        }
        ZSQNewsInfoDto data = null;
        if (newsId != null) {
            data = validNewsDetailResponse(validFailMsgList, newsId, operation);
        }
        dockingRecord.setNewsId(newsId);
        dockingRecord.setDockingOperation(operation);
        if (!validFailMsgList.isEmpty()) {
            String validFailMsg = String.join(LF, validFailMsgList);
            dockingRecord.setDockingStatus(2);
            dockingRecord.setDockingMsg(validFailMsg);
            newsDockingRecordService.updateById(dockingRecord);
            return Result.get(false, validFailMsg);
        }
        newsDockingRecordService.updateById(dockingRecord);
        return newsService.docking(dockingInfo, data, dockingRecord);
    }

    private ZSQNewsInfoDto validNewsDetailResponse(List<String> validFailMsgList, Long newsId, String operation) {
        JSONObject responseData;
        try {
            responseData = zsqHttpRequestUtils.getNewsDetail(newsId);
        } catch (Exception e) {
            validFailMsgList.add("知社区新闻详情接口调用异常，异常原因：" + e.getMessage()+ "！");
            return null;
        }
        if (responseData == null) {
            validFailMsgList.add("知社区新闻详情接口响应为空！");
            return null;
        }
        Boolean success = responseData.getBoolean(ZSQ_API_RES_SUCCESS);
        if (success == null || !success) {
            validFailMsgList.add("知社区新闻详情接口响应异常！【" + JSON.toJSONString(responseData) + "】");
            return null;
        }
        JSONObject obj = responseData.getJSONObject(ZSQ_API_RES_OBJ);
        if (obj == null && !DT_DELETE.equals(operation)) {
            validFailMsgList.add("知社区新闻详情接口响应obj为空（非删除操作）！【" + JSON.toJSONString(responseData) + "】");
            return null;
        }
        try {
            return JSONObject.toJavaObject(responseData.getJSONObject(ZSQ_API_RES_OBJ), ZSQNewsInfoDto.class);
        } catch (Exception e) {
            validFailMsgList.add("知社区新闻详情接口响应obj信息转换异常，异常原因：" + e.getMessage() + "！【" + JSON.toJSONString(responseData) + "】");
            return null;
        }
    }
}
