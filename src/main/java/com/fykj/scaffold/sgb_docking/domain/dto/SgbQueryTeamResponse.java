package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

/**
 * 苏州社工部根据组织名称查询组织ID响应
 */
@Data
public class SgbQueryTeamResponse {

    /**
     * 响应状态码 - SUCCESS或FAIL
     */
    private String code;

    /**
     * 失败信息
     */
    private String msg;

    /**
     * 组织ID
     */
    private String teamId;

    /**
     * 组织名称
     */
    private String teamName;

    /**
     * 判断查询是否成功
     * @return true表示成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code);
    }

    /**
     * 判断是否找到组织
     * @return true表示找到组织
     */
    public boolean hasTeam() {
        return isSuccess() && teamId != null && !teamId.trim().isEmpty();
    }
}
