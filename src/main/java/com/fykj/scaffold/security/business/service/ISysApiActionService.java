package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.dto.ActionOrRoleApiPathDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiAction;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 接口-资源映射表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface ISysApiActionService extends IBaseService<SysApiAction> {

    /**
     * 获取按钮已绑定的接口列表
     * @param actionId
     * @return
     */
    List<String> actionApi(Long actionId);

    /**
     * 保存授权
     * @param dto
     * @return
     */
    boolean savePath(ActionOrRoleApiPathDto dto);
}

