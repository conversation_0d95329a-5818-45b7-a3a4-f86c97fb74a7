package com.fykj.scaffold.ledger.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fykj.scaffold.ledger.domain.entity.LedgerDepartment;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndex;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndexItem;
import com.fykj.scaffold.ledger.mapper.LedgerIndexMapper;
import com.fykj.scaffold.ledger.service.ILedgerDepartmentService;
import com.fykj.scaffold.ledger.service.ILedgerIndexItemService;
import com.fykj.scaffold.ledger.service.ILedgerIndexService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import result.ResultCode;
import utils.StringUtil;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 台账指标-服务实现类
 *
 * @date 2025-05-26
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class LedgerIndexServiceImpl extends BaseServiceImpl<LedgerIndexMapper, LedgerIndex> implements ILedgerIndexService {

    @Autowired
    private ILedgerIndexItemService indexItemService;

    @Autowired
    private ILedgerDepartmentService departmentService;

    @Override
    public List<LedgerIndex> tree() {
        return buildTree(list());
    }

    @Override
    public LedgerIndex get(Long id) {
        LedgerIndex index = super.getById(id);
        Long pid = index.getParentId();
        if (pid != null) {
            LedgerIndex parent = super.getById(pid);
            index.setParentName(parent.getName());
        }
        return index;
    }

    @Override
    public List<LedgerIndex> list() {
        return list(new QueryWrapper<LedgerIndex>().orderByAsc("sequence"));
    }

    @Override
    public boolean deleteById(Long id) {
        LedgerIndex index = super.getById(id);
        if (StringUtil.isEmpty(index.getParentId())
                && lambdaQuery().eq(LedgerIndex::getParentId, id).exists()) {
           throw new BusinessException(ResultCode.FAIL, "存在子节点，无法删除！");
        }
        boolean valid = indexItemService.lambdaQuery().eq(LedgerIndexItem::getIndexId, id).exists();
        if (valid) {
            throw new BusinessException(ResultCode.FAIL, "存在指标项，无法删除！");
        }
        return super.removeById(id);
    }

    @Override
    public List<LedgerIndex> getUserIndexTree() {
        Long userId = (Long) Oauth2Util.getUserId();
        // 获取用户对应的局办室信息
        LedgerDepartment department = departmentService.getByUserId(userId);
        if (department == null) {
            return Collections.emptyList();
        }
        String departmentId = String.valueOf(department.getId());
        // 查询局办的指标项对应的指标id
        List<String> indexIdList = indexItemService.getIndexIdByDepartmentId(departmentId);
        if (CollectionUtils.isEmpty(indexIdList)) {
            return Collections.emptyList();
        }
        // 查询获取所有相关 LedgerIndex
        List<LedgerIndex> indexList = lambdaQuery()
                .in(LedgerIndex::getId, indexIdList)
                .list();
        // 提取需要查询的父节点ID（去除 null、排除已存在）
        Set<Long> existingIds = indexList.stream()
                .map(LedgerIndex::getId)
                .collect(Collectors.toSet());

        Set<Long> parentIdSet = indexList.stream()
                .map(LedgerIndex::getParentId)
                .filter(Objects::nonNull)
                .filter(id -> !existingIds.contains(id))
                .collect(Collectors.toSet());


        if (!parentIdSet.isEmpty()) {
            indexList.addAll(lambdaQuery().in(LedgerIndex::getId, parentIdSet).list());
        }
        return buildTree(indexList);
    }

    @Override
    public Map<String, Long> getIndexNameMap() {
        return list().stream().collect(Collectors.toMap(
                LedgerIndex::getName,
                LedgerIndex::getId,
                (a, b) -> b));
    }
}
