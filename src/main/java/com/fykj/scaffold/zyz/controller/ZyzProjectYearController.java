package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzProjectType;
import com.fykj.scaffold.zyz.domain.entity.ZyzProjectYear;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectYearParams;
import com.fykj.scaffold.zyz.service.IZyzProjectYearService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * 公益伙伴计划-年份表
 */
@RestController
@RequestMapping("/admin/zyz/project-year")
public class ZyzProjectYearController extends BaseController<IZyzProjectYearService, ZyzProjectYear, ZyzProjectYearParams> {

    @ApiOperation("年份列表")
    @GetMapping({"/yearsList"})
    public Result yearsList() {
        return new JsonResult<>(baseService.lambdaQuery().orderByDesc(ZyzProjectYear::getYear).list());
    }

    @Override
    public Result save(@RequestBody ZyzProjectYear entity) {
        if (baseService.lambdaQuery().eq(ZyzProjectYear::getYear, entity.getYear()).exists()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "年份已存在");
        }
        baseService.save(entity);
        return OK;
    }
}

