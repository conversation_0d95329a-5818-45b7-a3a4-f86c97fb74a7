package com.fykj.scaffold.advanced_unit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitEvaluateListDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitIndexEvaluateCountDTO;
import com.fykj.scaffold.advanced_unit.domain.dto.AuUnitScoreDTO;
import com.fykj.scaffold.advanced_unit.domain.entity.AuStandardIndexUnit;
import com.fykj.scaffold.advanced_unit.domain.params.AuStandardIndexUnitParams;
import com.fykj.scaffold.advanced_unit.mapper.AuStandardIndexUnitMapper;
import com.fykj.scaffold.advanced_unit.service.IAuStandardIndexUnitService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 文明单位-指标标准-单位打分
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AuStandardIndexUnitServiceImpl
        extends BaseServiceImpl<AuStandardIndexUnitMapper, AuStandardIndexUnit>
        implements IAuStandardIndexUnitService {

    @Override
    public void evaluate(AuStandardIndexUnit unit) {
        AuStandardIndexUnit entity = getOrCreateAuStandardIndexUnitByOrgIdAndIndexId(
                unit.getOrgId(), unit.getIndexId());
        // 自评或复评, 如果分数变动, 则记为对应次数+1
        if (!Objects.equals(entity.getSelfScore(), unit.getSelfScore())) {
            entity.setSelfCount(entity.getSelfCount() + 1);
        }
        if (!Objects.equals(entity.getFinalScore(), unit.getFinalScore())) {
            entity.setFinalCount(entity.getFinalCount() + 1);
        }
        BeanUtil.copyProperties(unit, entity, CopyOptions.create().setIgnoreProperties(
                AuStandardIndexUnit::getFinalCount,
                AuStandardIndexUnit::getSelfCount,
                AuStandardIndexUnit::getId));
        saveOrUpdate(entity);
    }

    @Override
    public void evaluate(List<AuStandardIndexUnit> unitList) {
        unitList.forEach(this::evaluate);
    }

    @Override
    public IPage<AuUnitEvaluateListDTO> queryEvaluatePage(AuStandardIndexUnitParams params) {
        IPage<AuUnitEvaluateListDTO> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        List<AuUnitEvaluateListDTO> list = baseMapper.queryEvaluatePage(page, params);
        page.setRecords(list);
        return page;
    }

    @Override
    public IPage<AuStandardIndexUnit> queryIndexEvaluatePage(AuStandardIndexUnitParams params) {
        IPage<AuStandardIndexUnit> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        List<AuStandardIndexUnit> list = baseMapper.queryIndexEvaluatePage(page, params);
        page.setRecords(list);
        return page;
    }

    @Override
    public AuUnitIndexEvaluateCountDTO countIndexEvaluate(AuStandardIndexUnitParams params) {
        IPage<AuUnitEvaluateListDTO> page = new Page<>(1,1);
        List<AuUnitEvaluateListDTO> list = baseMapper.queryEvaluatePage(page, params);
        List<AuUnitScoreDTO> scoreList = baseMapper.queryAllUnitScores(params);

        AuUnitIndexEvaluateCountDTO dto = new AuUnitIndexEvaluateCountDTO();
        if(list.isEmpty()){
            return dto;
        }
        BeanUtil.copyProperties(list.get(0), dto, CopyOptions.create());

        if (scoreList != null && !scoreList.isEmpty()) {
            scoreList.sort((a, b) -> b.getTotalScore().compareTo(a.getTotalScore()));

            String currentOrgId = dto.getOrgId();
            for (int i = 0; i < scoreList.size(); i++) {
                if (scoreList.get(i).getOrgId().equals(currentOrgId)) {
                    dto.setFinalRanking(i + 1);
                    break;
                }
            }
        }
        return dto;
    }

    private AuStandardIndexUnit getOrCreateAuStandardIndexUnitByOrgIdAndIndexId(
            Long orgId, Long indexId) {
        return Optional.ofNullable(lambdaQuery()
                .eq(AuStandardIndexUnit::getOrgId, orgId)
                .eq(AuStandardIndexUnit::getIndexId, indexId).one())
                .orElseGet(() -> new AuStandardIndexUnit()
                        .setOrgId(orgId).setIndexId(indexId)
                        .setFinalCount(0).setSelfCount(0));
    }

}
