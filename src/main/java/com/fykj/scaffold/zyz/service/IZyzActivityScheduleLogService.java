package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleLog;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 阵地计划操作记录表
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
public interface IZyzActivityScheduleLogService extends IBaseService<ZyzActivityScheduleLog> {
    /**
     * 保存阵地计划审核记录
     *
     * @param scheduleId  计划id
     * @param operateType 操作类型
     * @param remark      备注
     * @return 是否成功
     */
    boolean saveLog(Long scheduleId, String operateType, String remark);


    /**
     * 获取阵地计划操作历史记录
     *
     * @param scheduleId 计划id
     * @return 数据列表
     */
    List<ZyzActivityScheduleLog> getLogsByScheduleId(Long scheduleId);
}

