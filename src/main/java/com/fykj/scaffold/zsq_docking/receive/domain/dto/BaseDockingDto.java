package com.fykj.scaffold.zsq_docking.receive.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：yangxu
 * @Date：2025/4/23 14:23
 * @Description：
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseDockingDto implements Serializable {

    private static final long serialVersionUID = -2986308051446794325L;

    /**
     * 对接记录 id(重试时会有)
     */
    @ApiModelProperty(value = "对接记录 id(重试时会有)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dockingRecordId;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operation;

    /**
     * 政务通user_id
     */
    @ApiModelProperty(value = " 政务通user_id")
    private String zwtUserId;
}

