package com.fykj.scaffold.retrieve.api;

import com.fykj.scaffold.retrieve.domain.dto.RetrieveTeamDto;
import com.fykj.scaffold.retrieve.domain.dto.RetrieveTeamResultDto;
import com.fykj.scaffold.retrieve.service.IRetrieveTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;

/**
 * 找回团队账号管理权
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/retrieve/team")
@Api(tags = "找回团队账号管理权")
public class RetrieveTeamApi {

    @Autowired
    private IRetrieveTeamService retrieveTeamService;

//    @ApiOperation("找回团队账号信息提交")
//    @PostMapping("/retrieveTeam")
//    public JsonResult<RetrieveTeamResultDto> retrieveTeam(@RequestBody RetrieveTeamDto dto) {
//        return new JsonResult<>(retrieveTeamService.retrieveTeam(dto));
//    }

    @ApiOperation(value = "创建小程序二维码")
    @GetMapping(value = "/createQrCode")
    public Result createQrCode(@RequestParam String appId) {
        return new JsonResult<>(retrieveTeamService.createQrCode(appId));
    }
}
