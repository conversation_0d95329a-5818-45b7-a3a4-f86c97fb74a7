package com.fykj.scaffold.dynamic.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 字段信息-查询参数
 *
 * @date 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("字段信息-查询参数")
public class DynamicFieldParams extends BaseParams {

    private static final long serialVersionUID = 6973456805482807711L;

    @ApiModelProperty("字段名称")
    @MatchType(value = QueryType.LIKE)
    private String label;

    @ApiModelProperty("字段输入提示")
    @MatchType(value = QueryType.LIKE)
    private String placeholder;

    @ApiModelProperty("模块code")
    @MatchType(value = QueryType.EQ)
    private String moduleCode;

    @ApiModelProperty("常用字段")
    @MatchType(value = QueryType.EQ)
    private Boolean commonFlag;

    @ApiModelProperty("系统字段")
    @MatchType(value = QueryType.EQ)
    private Boolean systemFlag;

}
