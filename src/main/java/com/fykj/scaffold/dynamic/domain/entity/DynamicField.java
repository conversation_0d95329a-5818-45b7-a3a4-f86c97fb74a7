package com.fykj.scaffold.dynamic.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fykj.scaffold.dynamic.cons.DynamicFormCons;
import com.fykj.scaffold.support.utils.StringListTypeHandler;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 字段信息-实体类
 *
 * @date 2025-05-19
 */
@Data
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "dynamic_field", autoResultMap = true)
public class DynamicField extends BaseEntity  {

	private static final long serialVersionUID = 5561285907620319032L;

	/**
	 * 模块标识
	 */
	@NotBlank
	@TableField("module_code")
    @ApiModelProperty(value = "模块标识")
	private String moduleCode;

	/**
	 * 字段标识符(下划线分隔，如user_name)
	 */
	@TableField("field_name")
    @ApiModelProperty(value = "字段标识符(下划线分隔，如user_name)")
	private String fieldName;

	/**
	 * 字段名称
	 */
	@NotBlank
	@TableField("label")
    @ApiModelProperty(value = "字段名称")
	private String label;

	/**
	 * 字段输入提示
	 */
	@TableField("placeholder")
    @ApiModelProperty(value = "字段输入提示")
	private String placeholder;

	/**
	 * 字段类型
	 */
	@TableField("field_type")
	@DictTrans(transTo = "fieldTypeText")
    @ApiModelProperty(value = "字段类型 字典" + DynamicFormCons.FieldType.DICT_CODE)
	private String fieldType;

	/**
	 * 字段类型
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "字段类型中文")
	private String fieldTypeText;

	/**
	 * 校验类型(手机号, 身份证, 正则
	 */
	@TableField("validate_type")
	@ApiModelProperty(value = "校验类型(手机号PHONE_NUMBER, 身份证ID_CARD, 正则REGEX  字典"
			+ DynamicFormCons.FieldValidateType.DICT_CODE)
	private String validateType;

	/**
	 * 正则校验规则
	 */
	@TableField("regex_rule")
    @ApiModelProperty(value = "正则校验规则")
	private String regexRule;

	/**
	 * 字段选项
	 */
	@TableField(value = "field_option", typeHandler = StringListTypeHandler.class)
    @ApiModelProperty(value = "字段选项")
	private List<String> fieldOption;

	/**
	 * 系统字段
	 */
	@TableField("system_flag")
    @ApiModelProperty(value = "系统字段")
	private Boolean systemFlag;

	/**
	 * 常用字段
	 */
	@TableField("common_flag")
    @ApiModelProperty(value = "常用字段")
	private Boolean commonFlag;

	/**
	 * 是否必填：1-是，0-否
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "是否必填")
	private Boolean requireFlag;

	/**
	 * 在模板中的显示顺序
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "在模板中的显示顺序")
	private Integer fieldOrder;

}
