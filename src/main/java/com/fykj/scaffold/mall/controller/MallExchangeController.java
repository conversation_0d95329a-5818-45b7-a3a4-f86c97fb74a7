package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.dto.MallExchangeDto;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.params.MallExchangeParams;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.fykj.scaffold.support.ems.EmsExpressUtil;
import com.fykj.scaffold.support.syslog.AuditLog;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 *
 *
 * 前端控制器
 * <AUTHOR>
 * @date 2023-02-15
 */
@RestController
@RequestMapping("/admin/mall/exchange")
@Api(tags = "礼品兑换接口")
public class MallExchangeController extends BaseController<IMallExchangeService, MallExchange, MallExchangeParams> {

    @AuditLog("兑换记录分页查询")
    @ApiOperation("兑换记录分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallExchange>> getPages(@RequestBody MallExchangeParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }

    @AuditLog("兑换记录导出数据到Excel")
    @PostMapping("/exportExchange")
    @ApiOperation("导出数据到Excel")
    public void exportExchange(@RequestBody MallExchangeParams params) {
        List<MallExchangeDto> list = baseService.getExchangeList(params);
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "exchanges.xlsx", MallExchangeDto.class);
    }

    @ApiOperation("取消兑换")
    @GetMapping("/cancelExchange")
    public Result cancelExchange(@RequestParam Long id){
        if(!baseService.cancelExchange(id,false)){
            throw new BusinessException(ResultCode.FAIL, "当前网络拥挤，兑换失败请稍后再试");
        }
        return new Result();
    }

    @ApiOperation("获取面单")
    @GetMapping("/getNoddleList")
    public JsonResult<String> getNoddleList(@RequestParam Long id){
        MallExchange exchange= baseService.getById(id);
        String base64Result= EmsExpressUtil.freshetQuery(exchange.getEmsCode());
        return new JsonResult<>(base64Result);
    }

    @ApiOperation("物流信息查询")
    @GetMapping("/logisticsSearch")
    public Result logisticsSearch(@RequestParam String emsCode){
        return new JsonResult<>(EmsExpressUtil.traceWaybill(emsCode));
    }

    @ResponseBody
    @RequestMapping(value = "/writeOffVerificationCode", method = RequestMethod.POST)
    @ApiOperation(value = "核销核销码")
    public Result writeOffVerificationCode(long exchangeId, String verificationCode) {
        if (!baseService.writeOffVerificationCode(exchangeId, verificationCode)) {
            return new Result(ResultCode.DATA_EXPIRED);
        }
        return OK;
    }
}
