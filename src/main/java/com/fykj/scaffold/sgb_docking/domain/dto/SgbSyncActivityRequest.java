package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;

/**
 * 苏州社工部活动登记同步请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbSyncActivityRequest {

    /**
     * 活动类型 - 1志愿类 2培训类
     */
    private String type;

    /**
     * 唯一标识
     */
    private String uniCode;

    /**
     * 组织编号 - 活动所属的组织，注册组织接口返回
     */
    private String orgId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 服务领域代码 - 必须是第二级
     */
    private String serviceField;

    /**
     * 活动区划代码 - 必须到社区级，区划编号长度为12位
     */
    private String regionCode;

    /**
     * 活动简介
     */
    private String descs;

    /**
     * 活动详细地址
     */
    private String address;

    /**
     * 活动开始日期 - 格式:2016-09-30
     */
    private String startDate;

    /**
     * 活动结束日期 - 格式:2016-09-30
     */
    private String endDate;

    /**
     * 活动开始时间 - 格式:HH:mm
     */
    private String startTime;

    /**
     * 活动结束时间 - 格式:HH:mm
     */
    private String endTime;

    /**
     * 活动联系人
     */
    private String linkName;

    /**
     * 活动联系人电话
     */
    private String linkPhone;

    /**
     * 服务时长记录方式 - 1.按次 2.按时间段
     */
    private String recordWay;

    /**
     * 按次扫码每次记录多长时间 - 当recordWay=1时必填，且是0.5的正整数倍
     */
    private Double serviceTime;

    /**
     * 腾讯坐标-经度
     */
    private String txMapLan;

    /**
     * 腾讯坐标-纬度
     */
    private String txMapLat;

    /**
     * 活动logo图片 - base64编码
     */
    private String picLogoUrl;

    /**
     * 活动服务对象 - 活动类型type=1时必传，最多5个值，多个则以英文逗号分割
     */
    private String serviceTarget;

    /**
     * 活动培训对象 - 活动类型type=2时必传，最多5个值，多个则以英文逗号分割
     */
    private String trainTarget;

    /**
     * 活动形式 - 活动类型type=2时必传，最多5个值，多个则以英文逗号分割
     */
    private String actpForm;

    /**
     * 志愿招募人数 - 10万内的正整数
     */
    private String recruitNum;

    /**
     * 受众招募人数 - 10万内的正整数
     */
    private String audienceNum;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncActivityInfo acts = new SyncActivityInfo();
        acts.setActs(Collections.singletonList(this));
        return JSON.toJSONString(acts);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncActivityInfo {
        private java.util.List<SgbSyncActivityRequest> acts;
    }
}
