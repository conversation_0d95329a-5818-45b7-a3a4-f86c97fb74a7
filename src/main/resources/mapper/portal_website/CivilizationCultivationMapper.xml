<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.portal_website.mapper.CivilizationCultivationMapper">

    <select id="pageForCivilizationCultivation" resultType="com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation">
        SELECT
        cc.id AS id,
        cc.title AS title,
        cc.big_screen_title AS bigScreenTitle,
        cc.title_img_url AS titleImgUrl,
        ccc.category_list AS category,
        cc.effective_date AS effectiveDate,
        cc.grounding AS grounding,
        ccc.sequence AS sequence
        FROM cms_content cc
        INNER JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0 AND ccc.is_banner = 0
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        WHERE cc.is_deleted = 0 AND cc.type = 'CIVILIZATION_CULTIVATION'
        <if test="params.grounding != null">
            AND cc.grounding = #{params.grounding}
        </if>
        <if test="params.title != null and params.title != ''">
            AND cc.title LIKE CONCAT('%', CONCAT(#{params.title}, '%'))
        </if>
        <if test="params.publishDate != null and params.publishDate != ''">
            AND DATE_FORMAT(cc.effective_date, '%Y-%m-%d') = #{params.publishDate}
        </if>
        ORDER BY cc.effective_date DESC, ccc.sequence ASC
    </select>

    <select id="pageForCivilizationCultivationApi" resultType="com.fykj.scaffold.portal_website.domain.entity.CivilizationCultivation">
        SELECT
        cc.id AS id,
        cc.title AS title,
        cc.big_screen_title AS bigScreenTitle,
        cc.title_img_url AS titleImgUrl,
        cc.effective_date AS effectiveDate,
        cc.brief_introduction AS briefIntroduction,
        cc.custom_links as customLinks
        FROM cms_content cc
        INNER JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        WHERE cc.is_deleted = 0 AND cc.type = 'CIVILIZATION_CULTIVATION' AND cc.grounding = 1
        <if test="params.year != null and params.year != ''">
            AND DATE_FORMAT(cc.effective_date, '%Y') = #{params.year}
        </if>
        <if test="params.publishDate != null and params.publishDate != ''">
            AND DATE_FORMAT(cc.effective_date, '%Y-%m-%d') = #{params.publishDate}
        </if>
        ORDER BY cc.effective_date DESC, ccc.sequence ASC
    </select>

    <select id="getYears" resultType="java.lang.String">
        SELECT
        DISTINCT (DATE_FORMAT(cc.effective_date, '%Y')) AS year
        FROM cms_content cc
        LEFT JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0
        WHERE cc.is_deleted = 0 AND cc.type = 'CIVILIZATION_CULTIVATION' AND cc.grounding = 1
        <if test="categoryId != null and categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{categoryId}, '%'))
        </if>
        ORDER BY year DESC
    </select>

    <select id="getScreenDataList" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.CivilizationCultivateDto">
        SELECT
            big_screen_title AS name,
            title_img_url AS pic,
            brief_introduction AS content,
            custom_links AS link
        FROM cms_content cc
        INNER JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0 AND ccc.is_banner = 0
        <if test="params.categoryId != null and params.categoryId != ''">
            AND ccc.category_list LIKE CONCAT('%', CONCAT(#{params.categoryId}, '%'))
        </if>
        WHERE cc.is_deleted = 0 AND cc.type = 'CIVILIZATION_CULTIVATION' AND cc.grounding = 1
<!--        <if test="params.start != null">-->
<!--            AND cc.effective_date &gt;= #{params.start}-->
<!--        </if>-->
<!--        <if test="params.end != null">-->
<!--            AND cc.effective_date &lt;= #{params.end}-->
<!--        </if>-->
        ORDER BY ccc.sequence ASC, cc.effective_date DESC
    </select>
    <select id="mienCockpitData" resultType="com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto">
        SELECT
            cc.id,
            cc.title AS name,
            DATE_FORMAT(cc.effective_date, '%Y') as year,
            cc.title_img_url AS picture,
            cc.brief_introduction AS achievements,
            cc.custom_links AS link
        FROM cms_content cc
        INNER JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0
        AND ccc.category_list LIKE CONCAT('%', CONCAT(#{categoryId}, '%'))
            AND YEAR(cc.effective_date) = #{year}
        WHERE cc.is_deleted = 0 AND cc.type = 'CIVILIZATION_CULTIVATION' AND cc.grounding = 1
        ORDER BY ccc.sequence ASC, cc.effective_date DESC
    </select>
    <select id="getMaxYears" resultType="java.lang.String">
        SELECT
            DISTINCT (DATE_FORMAT(max(cc.effective_date), '%Y')) AS year
        FROM cms_content cc
            LEFT JOIN cms_category_content ccc on cc.id = ccc.content_id AND ccc.is_deleted = 0
        WHERE cc.is_deleted = 0 AND cc.type = 'CIVILIZATION_CULTIVATION' AND cc.grounding = 1
          AND ccc.category_list LIKE CONCAT('%', CONCAT(#{categoryId}, '%'))
    </select>
</mapper>
