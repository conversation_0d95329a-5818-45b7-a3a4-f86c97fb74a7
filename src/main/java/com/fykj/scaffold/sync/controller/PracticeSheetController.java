package com.fykj.scaffold.sync.controller;


import com.fykj.scaffold.sync.domain.entity.PracticeSheet;
import com.fykj.scaffold.sync.domain.params.PracticeSheetParams;
import com.fykj.scaffold.sync.service.IPracticeSheetService;
import com.fykj.scaffold.zyz.xxl_job.PracticeSheetSyncTask;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@RestController
@RequestMapping("/admin/zyz/platform/practice_sheet")
@Api(tags = "实践单管理接口")
public class PracticeSheetController extends BaseController<IPracticeSheetService, PracticeSheet, PracticeSheetParams> {

    @Autowired
    private PracticeSheetSyncTask practiceSheetSyncTask;

    @ApiOperation("同步（单条）")
    @GetMapping("/syncOne")
    public Result syncOne(@RequestParam Long id, @RequestParam String type) {
        if (StringUtil.isEmpty(id)) {
            throw new BusinessException(ResultCode.FAIL, "请传入id");
        }
        baseService.syncOne(id, type);
        return new Result(ResultCode.OK);
    }

    @ApiOperation("实践单同步")
    @GetMapping("/sync")
    public Result sync() {
        practiceSheetSyncTask.practiceSheetSyncJobHandler();
        return OK;
    }
}
