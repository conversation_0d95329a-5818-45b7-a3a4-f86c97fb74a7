package com.fykj.scaffold.mall.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.entity.SysUserAddress;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MallStarExchangeDto {

    /**
     * 兑换id
     */

    @ApiModelProperty(value = "兑换id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 类型数据字典值标明是机器还是线上
     */
    @ApiModelProperty(value = "类型数据字典值标明是机器还是线上")
    @DictTrans(transTo = "exchangeTypeText")
    private String exchangeType;

    @TableField(exist = false)
    @ApiModelProperty(value = "类型数据Text")
    private String exchangeTypeText;
    /**
     * 兑换人用户id
     */
    @ApiModelProperty(value = "兑换人用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeVolunteerId;

    /**
     * 兑换人用户名称
     */
    @ApiModelProperty(value = "兑换人用户名称")
    private String exchangeUserName;

    /**
     * 兑换人手机号码
     */

    @ApiModelProperty(value = "兑换人手机号码")
    private String exchangeUserLinkPhone;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeGoodsId;

    /**
     * 兑换积分（锁定积分）
     */
    @ApiModelProperty(value = "兑换积分（锁定积分）")
    private BigDecimal exchangePoint;

    /**
     * 积分单价
     */
    @ApiModelProperty(value = "积分单价")
    private BigDecimal goodsPoint;

    /**
     * 兑换数量
     */
    @ApiModelProperty(value = "兑换数量")
    private Integer exchangeNum;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 机器编号
     */
    @ApiModelProperty(value = "机器编号")
    private String machineNo;

    /**
     * PAD兑换站点编码
     */
    @ApiModelProperty(value = "PAD兑换站点编码")
    private String stationCode;

    /**
     * 兑换时间
     */
    @ApiModelProperty(value = "兑换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exchangeTime;

    /**
     * 组织机构code
     */
    @ApiModelProperty(value = "组织机构code")
    private String orgCode;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 兑换之前的积分
     */
    @ApiModelProperty(value = "兑换之前的积分")
    private BigDecimal exchangeBeforeScore;

    /**
     * 兑换之后的积分
     */
    @ApiModelProperty(value = "兑换之后的积分")
    private BigDecimal exchangeAfterScore;

    /**
     * 状态数据字典值
     */
    @ApiModelProperty(value = "状态数据字典值")
    @DictTrans(transTo = "statusText")
    private String status;

    @ApiModelProperty(value = "状态类型Text")
    private String statusText;

    /**
     * 核销用户id
     */
    @ApiModelProperty(value = "核销用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long verifyUserId;

    /**
     * 核销用户姓名
     */
    @ApiModelProperty(value = "核销用户姓名")
    private String verifyUserName;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    private LocalDateTime verifyTime;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    @JsonSerialize(using = ToStringSerializer.class)
    private LocalDateTime expireTime;

    /**
     * 兑换途径
     */
    @ApiModelProperty(value = "兑换途径")
    private String exchangeChannel;
    /**
     * 用户收货地址id
     */
    @ApiModelProperty(value = "用户收货地址Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userAddressId;
    /**
     * 用户收货地址
     */
    @ApiModelProperty(value = "用户收货地址")
    private String address;
    /**
     * 兑换途径
     */
    @ApiModelProperty(value = "快递状态")
    @DictTrans(transTo = "emsStatusText")
    private String emsStatus;

    @ApiModelProperty(value = "状态类型Text")
    private String emsStatusText;

    /**
     * 商品Logo
     */
    @ApiModelProperty(value = "商品Logo")
    private String goodsLogo;
    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    private SysUserAddress sysUserAddress;

    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    private String recipient;
    /**
     * 收件人联系方式
     */
    @ApiModelProperty(value = "收件人联系方式")
    private String recipientPhone;
    /**
     * 快递编号
     */
    @ApiModelProperty(value = "快递编号")
    private String emsCode;
    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String prov;
    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String county;
    /**
     * 邮政发货失败日志
     */

    @ApiModelProperty(value = "邮政发货失败日志")
    private String emsRemark;
    /**
     * 核销码
     */
    @ApiModelProperty(value = "核销码")
    private String verificationCode;

    /**
     * 是否需要报名
     */
    @ApiModelProperty(value = "是否需要报名")
    private Boolean needApply;

    /**
     * 报名表id
     */
    @ApiModelProperty(value = "报名表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long applyId;

    /**
     * 兑换成功信息
     */
    @ApiModelProperty(value = "兑换成功信息")
    private String successMsg;
}
