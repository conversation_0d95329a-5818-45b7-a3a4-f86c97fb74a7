package com.fykj.scaffold.portal_website.controller;


import com.fykj.scaffold.portal_website.domain.entity.Video;
import com.fykj.scaffold.portal_website.domain.params.VideoParams;
import com.fykj.scaffold.portal_website.service.IVideoService;
import com.fykj.scaffold.support.syslog.AuditLog;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;
import utils.StringUtil;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/admin/portal_website/video")
@Api(tags = "视频资源接口")
public class VideoController extends BaseController<IVideoService, Video, VideoParams> {

    @Value("${defaultServerCode}")
    public String defaultServerCode;

    @ApiOperation("保存方法")
    @SysLogMethod("视频保存方法")
    @PostMapping({"/saveResource"})
    public Result save(@RequestBody Video entity) {
        if (StringUtil.isEmpty(entity.getServerCode())) {
            entity.setServerCode(defaultServerCode);
        }
        boolean result = this.baseService.save(entity);
        return result ? new JsonResult<>(String.valueOf(entity.getId())) : new Result(ResultCode.FAIL);
    }

    @AuditLog("视频文件上传操作")
    @ApiOperation("上传文件")
    @PostMapping(value = "/upload")
    public Result upload(@RequestParam(name = "file") MultipartFile file,
                         @RequestParam(name = "serverCode", required = false) String serverCode,
                         @RequestParam(name = "id") String id) {
        try {
            if (file == null || file.isEmpty()) {
                return new Result(ResultCode.BAD_REQUEST.code(), "请选择要上传的文件");
            }
            if (StringUtil.isEmpty(serverCode) || "null".equals(serverCode)) {
                serverCode = defaultServerCode;
            }
            baseService.upload(file, serverCode, id);
            return new Result(ResultCode.OK);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, e.getMessage());
        }
    }

    @AuditLog("视频文件上传状态更新操作")
    @ApiOperation("更新资源状态")
    @GetMapping({"/updateStatus"})
    public Result updateStatus(@RequestParam String id, @RequestParam Boolean status, @RequestParam(required = false) String failReason) {
        baseService.updateStatus(id, status, failReason);
        return new Result(ResultCode.OK);
    }

    @ApiOperation("获取视频信息")
    @GetMapping({"/getVideoInfo"})
    public JsonResult<Video> getVideoInfo(@RequestParam String id) {
        return new JsonResult<>(baseService.getVideoInfo(id));
    }

    @AuditLog("视频文件名称更新操作")
    @ApiOperation("更新资源状态")
    @GetMapping({"/updateName"})
    public Result updateName(@RequestParam String id, @RequestParam String name) {
        baseService.updateName(id, name);
        return new Result(ResultCode.OK);
    }
}
