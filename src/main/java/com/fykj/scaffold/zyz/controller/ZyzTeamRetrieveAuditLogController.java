package com.fykj.scaffold.zyz.controller;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieveAuditLog;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamRetrieveAuditLogParams;
import com.fykj.scaffold.zyz.service.IZyzTeamRetrieveAuditLogService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 团队找回审核操作记录表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-08-08
 */
@RestController
@RequestMapping("/admin/team/retrieve/audit/log")
@Api(tags = "团队找回审核操作记录接口")
public class ZyzTeamRetrieveAuditLogController extends BaseController<IZyzTeamRetrieveAuditLogService, ZyzTeamRetrieveAuditLog, ZyzTeamRetrieveAuditLogParams> {


    @ApiOperation("获取审核操作记录")
    @GetMapping("/getLogsByTeamId")
    public JsonResult<List<ZyzTeamRetrieveAuditLog>> getLogsByTeamId(Long teamId) {
        List<ZyzTeamRetrieveAuditLog> res = baseService.getLogsByTeamId(teamId);
        DictTransUtil.trans(res);
        return new JsonResult<>(res);
    }
}
