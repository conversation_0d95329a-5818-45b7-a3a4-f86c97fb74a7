package com.fykj.scaffold.sgb_docking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncStatusRecord;
import com.fykj.scaffold.sgb_docking.domain.params.SgbSyncStatusRecordParams;
import org.apache.ibatis.annotations.Param;

/**
 * 数据同步状态记录-Mapper接口
 *
 * @date 2025-06-24
 */
public interface SgbSyncStatusRecordMapper extends BaseMapper<SgbSyncStatusRecord> {

    /**
     * 分页查询同步状态记录（包含业务数据名称）
     * @param page 分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<SgbSyncStatusRecord> selectPageWithBizName(Page<SgbSyncStatusRecord> page, @Param("params") SgbSyncStatusRecordParams params);

}
