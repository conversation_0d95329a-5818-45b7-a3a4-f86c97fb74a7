package com.fykj.scaffold.mall.cons;

public class MallCons {
    /**
     * 入库
     */
    public static final String MALL_SCORE_GOODS_NUM_CHANGE_TYPE_IN = "mall_score_goods_num_change_type_in";
    /**
     * 出库
     */
    public static final String MALL_SCORE_GOODS_NUM_CHANGE_TYPE_OUT = "mall_score_goods_num_change_type_out";
    /**
     * 兑换成功
     */
    public static final String MALL_EXCHANGE_STATUS_SUCCESS="mall_exchange_status_success";
    /**
     * 取消兑换
     */
    public static final String MALL_EXCHANGE_STATUS_CANCEL_EXCHANGE="mall_exchange_status_cancel_exchange";
    /**
     * 商品类型机器
     */
    public static final String MALL_SCORE_GOODS_TYPE_MACHINE="mall_score_goods_type_machine";

    /**
     * 商品类型pad
     */
    public static final String MALL_SCORE_GOODS_TYPE_PAD="mall_score_goods_type_pad";
    /**
     * 出货中
     */
    public static final String MALL_EXCHANGE_STATUS_SHIPPING="mall_exchange_status_shipping";
    /**
     * 待核销
     */
    public static final String MALL_EXCHANGE_STATUS_WRITTEN_EXCHANGE="mall_exchange_status_written_exchange";
    /**
     * 出货失败
     */
    public static final String MALL_EXCHANGE_STATUS_SHIPMENT_FAIL="mall_exchange_status_shipment_fail";
    /**
     * 出货成功
     */
    public static final String MALL_EXCHANGE_STATUS_SHIPPED="mall_exchange_status_shipped";
    /**
     * 发货失败
     */
    public static final String MALL_EXCHANGE_EMS_STATUS_ESS_FAILURE="mall_exchange_ems_status_ess_failure";
    /**
     * 取消发货
     */
    public static final String MALL_EXCHANGE_EMS_STATUS_ESS_CANCEL="mall_exchange_ems_status_ess_cancel";
    /**
     * 待发货
     */
    public static final String MALL_EXCHANGE_EMS_STATUS_DELIVERY="mall_exchange_ems_status_delivery";
    /**
     * 发货成功
     */
    public static final String MALL_EXCHANGE_STATUS_SENT="mall_exchange_status_sent";
    /**
     * 邮政定时任务取件配置code
     */
    public static final String SYSTEM_CONFIG_EMS_TIME="system_config_ems_time";
    /**
     * 线下核销过期时间配置code
     */
    public static final String SYSTEM_CONFIG_OFFLINE_TIME="system_config_offline_time";

    public static final String MALL_SELF_MACHINE_STATUS_NORMAL="mall_self_machine_status_normal";
    public static final String MALL_EXCHANGE_TYPE_ONLINE="mall_exchange_type_online";
    public static final String MALL_EXCHANGE_TYPE_OFFLINE="mall_exchange_type_offline";
    public static final String MALL_EXCHANGE_TYPE_MACHINE="mall_exchange_type_machine";
    public static final String  MALL_EXCHANGE_TYPE_THIRD_PART="mall_exchange_type_third_part";
    public static final String MALL_EXCHANGE_TYPE_PAD="mall_exchange_type_pad";
    /**
     * 积分历史表类型产生积分
     */
    public static final String ZYZ_POINT_RECORD_TYPE_ADD = "zyz_point_record_type_add";
    /**
     * 积分历史表类型花费积分
     */
    public static final String ZYZ_POINT_RECORD_TYPE_SUB = "zyz_point_record_type_sub";
    /**
     * 积分变动业务类型-服务时长转换
     */
    public static final String POINT_CHANGE_BIZ_TYPE_SERVICE_LONG_TRANS = "pr_biz_type_service_long_trans";
    /**
     * 积分变动业务类型-兑换
     */
    public static final String POINT_CHANGE_BIZ_TYPE_EXCHANGE = "pr_biz_type_exchange";
    /**
     * 积分变动业务类型-捐赠
     */
    public static final String POINT_CHANGE_BIZ_TYPE_CONTRIBUTE = "pr_biz_type_contribute";

    /**
     * 物料进补计划类型-普通
     */
    public static final String STOCK_REPLENISH_PLAN_TYPE_COMMON = "srpt_common";

    /**
     * 物料进补计划类型-兑换机
     */
    public static final String STOCK_REPLENISH_PLAN_TYPE_MACHINE = "srpt_machine";

    /**
     * 兑换频次-每月一次
     */
    public static final String GOOD_EXCHANGE_FREQUENCY_MONTH_1 = "good_exchange_frequency_month_1";

    /**
     * 兑换频次-每月一次
     */
    public static final String GOOD_EXCHANGE_FREQUENCY_YEAR_1 = "good_exchange_frequency_year_1";

    /**
     * 兑换频次-不设限
     */
    public static final String GOOD_EXCHANGE_FREQUENCY_NONE = "good_exchange_frequency_none";

    /**
     * 商品展示状态-敬请期待
     */
    public static final String GOODS_DISPLAY_STATUS_PLAN = "goods_display_status_plan";

    /**
     * 商品展示状态-可兑换
     */
    public static final String GOODS_DISPLAY_STATUS_EXCHANGEABLE = "goods_display_status_exchangeable";


    /**
     * 积分变动业务类型-勋章兑换积分
     */
    public static final String POINT_CHANGE_BIZ_TYPE_BADGE_EXCHANGE = "pr_biz_type_badge_exchange";

    /**
     * 积分变动业务类型-勋章购买
     */
    public static final String POINT_CHANGE_BIZ_TYPE_BADGE_PURCHASE = "pr_biz_type_badge_purchase";

    /**
     * 积分变动业务类型-勋章售出
     */
    public static final String POINT_CHANGE_BIZ_TYPE_BADGE_SELL = "pr_biz_type_badge_sell";
}
