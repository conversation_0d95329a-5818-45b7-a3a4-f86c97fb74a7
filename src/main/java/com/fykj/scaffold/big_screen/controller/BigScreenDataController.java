package com.fykj.scaffold.big_screen.controller;


import com.fykj.scaffold.big_screen.domain.entity.BigScreenData;
import com.fykj.scaffold.big_screen.domain.params.BigScreenDataParams;
import com.fykj.scaffold.big_screen.service.impl.BigScreenDataServiceImpl;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.Result;
import result.ResultCode;


/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@RestController
@RequestMapping("/admin/big_screen_data")
@Api(tags = "大屏数据创造")
public class BigScreenDataController extends BaseController<BigScreenDataServiceImpl, BigScreenData, BigScreenDataParams> {

    @AuditLog("大屏数据创造禁启用操作")
    @ApiOperation("大屏数据创造禁启用")
    @GetMapping({"/statusChange"})
    public Result statusChange(@RequestParam Long id) {
        baseService.statusChange(id);
        return new Result(ResultCode.OK);
    }
}
