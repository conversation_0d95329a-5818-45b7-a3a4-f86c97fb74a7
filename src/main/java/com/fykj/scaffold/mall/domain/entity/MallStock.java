package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <p>
 * 物料信息管理实体
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_stock")
public class MallStock extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 物料名称
     */
    @TableField("name")
    @NotBlank(message = "物料名称不能为空")
    @ApiModelProperty(value = "物料名称")
    private String name;

    /**
     * 物料编号
     */
    @TableField("sku")
    @NotBlank(message = "物料编号不能为空")
    @ApiModelProperty(value = "物料编号")
    private String sku;

    /**
     * 物料单价
     */
    @TableField("unit_price")
    @ApiModelProperty(value = "物料单价")
    private BigDecimal unitPrice;

    /**
     * 库存数量
     */
    @TableField("inventory_quantity")
    @ApiModelProperty(value = "库存数量")
    private Integer inventoryQuantity;

    /**
     * 库存(总)数量(包括商品和兑换机中已分配的剩余)
     */
    @TableField("inventory_total_quantity")
    @ApiModelProperty(value = "库存(总)数量(包括商品和兑换机中已分配的剩余)")
    private Integer inventoryTotalQuantity;
    /**
     * 物料描述
     */
    @TableField("description")
    @ApiModelProperty(value = "物料描述")
    private String description;

    /**
     * 组织机构code
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织机构code")
    private String orgCode;

    /**
     * 组织名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "组织名称")
    private String orgName;
}
