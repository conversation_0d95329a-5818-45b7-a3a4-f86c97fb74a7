package com.fykj.scaffold.message.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.message.domain.dto.MsgRecordExportDto;
import com.fykj.scaffold.message.domain.entity.MsgRecord;
import com.fykj.scaffold.message.domain.param.MsgRecordParams;
import com.fykj.scaffold.message.service.impl.MsgRecordServiceImpl;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;
import java.util.Map;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@RestController
@RequestMapping("/admin/msg_record")
@Api(tags = "消息记录")
public class MsgRecordController extends BaseController<MsgRecordServiceImpl, MsgRecord, MsgRecordParams> {

    @ApiOperation(value = "批量已读")
    @GetMapping(value = "/readBatch")
    public Result setBatchStatusTrue(@RequestParam String ids) {
        if (baseService.readBatch(ids)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }

    @PostMapping("/export_all")
    @ApiOperation("导出(全)")
    public void exportAll(@RequestBody MsgRecordParams params) {
        List<MsgRecordExportDto> result = baseService.export(params);
        ExcelUtil.fillExcel(result, "msg_record_all.xlsx", MsgRecordExportDto.class);
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(@RequestBody MsgRecordParams params) {
        List<MsgRecordExportDto> result = baseService.export(params);
        ExcelUtil.fillExcel(result, "msg_record.xlsx", MsgRecordExportDto.class);
    }


    @ApiOperation(value = "消息发送进程获取")
    @GetMapping(value = "/sendProgress")
    public JsonResult<Map<String, Integer>> sendProgress(@RequestParam Long sendId) {
        return new JsonResult<>(baseService.sendProgress(sendId));
    }

    @PostMapping("/pageForMaintain")
    @ApiOperation("接收者维护页分页")
    public JsonResult<IPage<MsgRecord>> pageForMaintain(@RequestBody MsgRecordParams params) {
        return new JsonResult<>(baseService.pageForMaintain(params));
    }

    @PostMapping("/updateRecord")
    @ApiOperation("修改记录")
    public Result updateRecord(@RequestBody MsgRecord record) {
        baseService.updateRecord(record);
        return OK;
    }

    @GetMapping("/removeRecord")
    @ApiOperation("删除记录")
    public Result removeRecord(@RequestParam Long id) {
        baseService.removeRecord(id);
        return OK;
    }
}
