package com.fykj.scaffold.advanced_unit.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 文明单位-申请表
 * 查询参数
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "文明单位-申请-筛选参数")
public class AuStandardUnitApplyParams extends BaseParams {

    @ApiModelProperty(value = "单位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    private Long orgId;

    @ApiModelProperty(value = "测评版本ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(QueryType.EQ)
    private Long standardId;

}
