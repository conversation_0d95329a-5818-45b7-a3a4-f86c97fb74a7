package com.fykj.scaffold.race.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 摄影大赛-作品表
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class PhotoParams extends BaseParams {

    @ApiModelProperty("审核状态，精确查询")
    @MatchType(value = QueryType.EQ, fieldName = "audit_status")
    private String auditStatus;

    @ApiModelProperty("标题，模糊查询")
    @MatchType(value = QueryType.LIKE, fieldName = "name")
    private String key;

    @ApiModelProperty("活动id，模糊查询")
    @MatchType(value = QueryType.EQ, fieldName = "activity_id")
    private Long activityId;



}
