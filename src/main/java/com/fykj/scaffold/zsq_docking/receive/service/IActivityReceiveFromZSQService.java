package com.fykj.scaffold.zsq_docking.receive.service;

import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingActivityRecord;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ActivityDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.NewsDockingDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQActivityInfoDto;
import com.fykj.scaffold.zsq_docking.receive.domain.dto.ZSQNewsInfoDto;
import result.Result;

public interface IActivityReceiveFromZSQService {

    /**
     * 活动对接
     *
     * @param dockingInfo
     * @param data
     */
    Result docking(ActivityDockingDto dockingInfo, ZSQActivityInfoDto data, ZSQDockingActivityRecord dockingRecord);
}
