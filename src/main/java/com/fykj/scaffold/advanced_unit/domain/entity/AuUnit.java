package com.fykj.scaffold.advanced_unit.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitAuditStatus;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitHonorLevel;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitNature;
import com.fykj.scaffold.advanced_unit.cons.AdvancedUnitType;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.excel.annotation.BoolConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDateTime;

/**
 * 文明单位-申请表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-07
 */
@Data
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("au_unit")
public class AuUnit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 单位名称
     */
    @TableField("unit_name")
    @ApiModelProperty(value = "单位名称")
    @ExcelProperty(value = "单位名称")
    private String unitName;

    /**
     * 统一社会信用代码（18位）
     */
    @TableField("credit_code")
    @ApiModelProperty(value = "统一社会信用代码（18位）")
    @ExcelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 联系人
     */
    @TableField("contact_person")
    @ApiModelProperty(value = "联系人")
    @ExcelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系方式
     */
    @TableField("contact_phone")
    @ApiModelProperty(value = "联系方式")
    @ExcelProperty(value = "联系方式")
    private String contactPhone;

    /**
     * 详细地址
     */
    @TableField("address")
    @ApiModelProperty(value = "详细地址")
    @ExcelProperty(value = "详细地址")
    private String address;

    /**
     * 经纬度（经度,纬度）
     */
    @TableField("coordinates")
    @ApiModelProperty(value = "经纬度（经度,纬度）")
    private String coordinates;

    /**
     * 荣誉等级
     */
    @TableField("honor_level")
    @ApiModelProperty(value = "荣誉等级 字典CODE: " + AdvancedUnitHonorLevel.DICT_CODE)
    @DictTrans(transTo = "honorLevelName")
    private String honorLevel;

    @TableField(exist = false)
    @ApiModelProperty(value = "荣誉等级 字典CODE: " + AdvancedUnitHonorLevel.DICT_CODE)
    @ExcelProperty(value = "荣誉等级")
    private String honorLevelName;

    /**
     * 单位性质（党政机关/事业单位等）
     */
    @TableField("unit_nature")
    @ApiModelProperty(value = "单位性质（党政机关/事业单位等） 字典CODE: " + AdvancedUnitNature.DICT_CODE)
    @DictTrans(transTo = "unitNatureName")
    private String unitNature;

    @TableField(exist = false)
    @ApiModelProperty(value = "单位性质（党政机关/事业单位等） 字典CODE: " + AdvancedUnitNature.DICT_CODE)
    @ExcelProperty(value = "单位性质")
    private String unitNatureName;

    /**
     * 单位类型
     */
    @TableField("unit_type")
    @ApiModelProperty(value = "单位类型 字典CODE: " + AdvancedUnitType.DICT_CODE)
    @DictTrans(transTo = "unitTypeName")
    private String unitType;

    @TableField(exist = false)
    @ApiModelProperty(value = "单位类型 字典CODE: " + AdvancedUnitType.DICT_CODE)
    @ExcelProperty(value = "单位类型")
    private String unitTypeName;

    /**
     * 主管部门
     */
    @TableField("governing_department")
    @ApiModelProperty(value = "主管部门")
    @ExcelProperty(value = "主管部门")
    private String governingDepartment;

    @TableField("governing_department_name")
    @ApiModelProperty(value = "主管部门名称")
    private String governingDepartmentName;

    /**
     * 单位简介
     */
    @TableField("introduction")
    @ApiModelProperty(value = "单位简介")
    @ExcelProperty(value = "单位简介")
    private String introduction;

    /**
     * 关联团队
     */
    @TableField("related_teams")
    @ApiModelProperty(value = "关联团队")
    private String relatedTeams;

    /**
     * 单位logo
     */
    @TableField("unit_logo")
    @ApiModelProperty(value = "单位logo")
    private String unitLogo;

    /**
     * 审核状态
     */
    @TableField("audit_status")
    @ApiModelProperty(value = "审核状态 字典CODE: " + AdvancedUnitAuditStatus.DICT_CODE)
    @DictTrans(transTo = "auditStatusName")
    private String auditStatus;

    @TableField(exist = false)
    @ApiModelProperty(value = "审核状态 字典CODE: " + AdvancedUnitAuditStatus.DICT_CODE)
    private String auditStatusName;

    /**
     * 审核时间
     */
    @TableField("audit_at")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditAt;

    /**
     * 审核人
     */
    @TableField("audit_user")
    @ApiModelProperty(value = "审核人")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long auditUser;

    /**
     * 展示状态
     */
    @TableField("show_status")
    @BoolConvert(trueText = "已上架", falseText = "已下架")
    @ApiModelProperty(value = "展示状态")
    private Boolean showStatus;

    /**
     * 爬取ID
     */
    @TableField("spider_id")
    @ApiModelProperty(value = "原始ID", hidden = true)
    private String spiderId;

    @TableField(exist = false)
    @ApiModelProperty(value = "导入失败原因", hidden = true)
    @ExcelProperty(value = "导入失败原因")
    private String reason;

    @TableField("belong_user_id")
    @ApiModelProperty(value = "所属用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long belongUserId;

    /**
     * 规模
     */
    @TableField("scale")
    @ApiModelProperty(value = "规模")
    @ExcelProperty(value = "规模")
    private String scale;

}
