package com.fykj.scaffold.security.login.service;

import com.alibaba.fastjson.JSON;
import com.fykj.scaffold.security.login.domain.dto.OAuth2ErrorDto;
import com.fykj.scaffold.support.conns.Oauth2AuthorizeCons;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import result.ResultCode;

/**
 * 2025/4/29
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class LoginService {

    @Autowired
    private ClientCredentialsResourceDetails clientCredentialsResourceDetails;

    public Object oneCodeLogin(String username, String oneCode) {
        // 组装登录信息
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add(Oauth2AuthorizeCons.GRANT_TYPE, Oauth2AuthorizeCons.ONE_CODE);
        formData.add(Oauth2AuthorizeCons.USERNAME, username);
        formData.add(Oauth2AuthorizeCons.PASSWORD, oneCode);
        formData.add(Oauth2AuthorizeCons.CLIENT_ID, clientCredentialsResourceDetails.getClientId());
        formData.add(Oauth2AuthorizeCons.CLIENT_SECRET, clientCredentialsResourceDetails.getClientSecret());
        // 组装登录请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // restTemplate调用
        RestTemplate restTemplate = new RestTemplate();
        try {
            String tokenResult = restTemplate.exchange(clientCredentialsResourceDetails.getAccessTokenUri(),
                    HttpMethod.POST, new HttpEntity<>(formData, headers), String.class).getBody();
            return JSON.parse(tokenResult);
        } catch (HttpClientErrorException e) {
            // 把SpringSecurityOAuth2的错误转换成我们封装的错误
            OAuth2ErrorDto errorDto = JSON.parseObject(e.getResponseBodyAsString(), OAuth2ErrorDto.class);
            throw new BusinessException(ResultCode.FAIL, errorDto.getError_description());
        }
    }
}
