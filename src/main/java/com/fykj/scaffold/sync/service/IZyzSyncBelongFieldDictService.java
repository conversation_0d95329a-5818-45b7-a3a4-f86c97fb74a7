package com.fykj.scaffold.sync.service;

import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;

import java.util.List;

/**
 * 市平台所属领域数据字典
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-23
 */
public interface IZyzSyncBelongFieldDictService extends IBaseService<ZyzSyncBelongFieldDict> {
    /**
     * 清空
     */
    void clearAll();

    /**
     * 同步所属领域
     */
    void syncBelongFieldDict();

    /**
     * 根据父类获取
     *
     * @param fatherId
     * @return
     */
    List<ZyzSyncBelongFieldDict> getByFatherId(String fatherId);

    /**
     * 根据类型获取ID获取
     *
     * @return
     */
    ZyzSyncBelongFieldDict getByTypeId(String typeId);

    /**
     * 级联获取领域
     *
     * @return
     */
    List<ZyzSyncBelongFieldDict> cascade();

    /**
     * 获取type的name
     * @param typeId
     * @return
     */
    String getFieldName(String typeId);

    /**
     * 所属领域限制时长
     * @param id
     * @param minTimeLimit
     * @param maxTimeLimit
     * @return
     */
    boolean updateLimitTime(Long id, Integer minTimeLimit, Integer maxTimeLimit);
}

