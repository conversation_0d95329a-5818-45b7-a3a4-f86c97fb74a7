package com.fykj.volunteer.sync.util.api;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.zyz.domain.dto.ActScheduleResultDto;
import com.fykj.volunteer.sync.util.domain.cms.SyncCms;
import com.fykj.volunteer.sync.util.domain.cms.SyncCmsResult;
import com.fykj.volunteer.sync.util.domain.dep_type.DepTypeResult;
import com.fykj.volunteer.sync.util.domain.join_team.JoinTeam;
import com.fykj.volunteer.sync.util.domain.join_team.JoinTeamResult;
import com.fykj.volunteer.sync.util.domain.region.RegionQuery;
import com.fykj.volunteer.sync.util.domain.region.RegionResult;
import com.fykj.volunteer.sync.util.domain.requirement.SyncRequirement;
import com.fykj.volunteer.sync.util.domain.requirement.SyncRequirementResult;
import com.fykj.volunteer.sync.util.domain.res.SyncRes;
import com.fykj.volunteer.sync.util.domain.res.SyncResResult;
import com.fykj.volunteer.sync.util.domain.service_type.ServiceCodeResult;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMember;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMemberBuildXmlUtil;
import com.fykj.volunteer.sync.util.domain.sync_act_member.SyncActMemberResult;
import com.fykj.volunteer.sync.util.domain.sync_act_schedule.SyncActSchedule;
import com.fykj.volunteer.sync.util.domain.sync_act_schedule.SyncActScheduleResult;
import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShow;
import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShowResult;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActp;
import com.fykj.volunteer.sync.util.domain.sync_actp.SyncActpResult;
import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActs;
import com.fykj.volunteer.sync.util.domain.sync_acts.SyncActsResult;
import com.fykj.volunteer.sync.util.domain.sync_base.SyncBase;
import com.fykj.volunteer.sync.util.domain.sync_base.SyncBaseResult;
import com.fykj.volunteer.sync.util.domain.sync_dep.SyncDep;
import com.fykj.volunteer.sync.util.domain.sync_dep.SyncDepResult;
import com.fykj.volunteer.sync.util.domain.sync_order.SyncOrder;
import com.fykj.volunteer.sync.util.domain.sync_order.SyncOrderResult;
import com.fykj.volunteer.sync.util.domain.sync_schedule_act_plan.SyncScheduleActPlan;
import com.fykj.volunteer.sync.util.domain.sync_schedule_act_plan.SyncScheduleActPlanResult;
import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTime;
import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTimeBuildXmlUtil;
import com.fykj.volunteer.sync.util.domain.sync_service_time.SyncServiceTimeResult;
import com.fykj.volunteer.sync.util.domain.sync_spot.SyncSpot;
import com.fykj.volunteer.sync.util.domain.sync_spot.SyncSpotResult;
import com.fykj.volunteer.sync.util.domain.sync_team.SyncTeam;
import com.fykj.volunteer.sync.util.domain.sync_team.SyncTeamResult;
import com.fykj.volunteer.sync.util.domain.sync_user.SyncUser;
import com.fykj.volunteer.sync.util.domain.sync_user.SyncUserResult;
import com.fykj.volunteer.sync.util.util.ApiWebUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import result.ResultCode;

import java.util.List;

/**
 * 调用市平台接口工具类
 */
@Slf4j
public class ApiWebService {

    /**
     * 同步志愿者
     *
     * @param syncUser 志愿者对象
     * @return 同步结果
     */
    public static SyncUserResult syncUser(SyncUser syncUser) {
        String result = ApiWebUtil.sendHttpRequest("syncUser", syncUser.buildXml());
        SyncUserResult.Vol vol = XmlUtils.parseBean(result, SyncUserResult.Vol.class);
        if (vol == null || CollectionUtil.isEmpty(vol.getInfo())) {
            log.error("（同步志愿者）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return vol.getInfo().get(0);
    }

    /**
     * 同步职能部门
     *
     * @param syncDep 职能部门信息
     * @return 同步结果
     */
    public static SyncDepResult syncDep(SyncDep syncDep) {
        String result = ApiWebUtil.sendHttpRequest("syncDep", syncDep.buildXml());
        SyncDepResult.Dp dp = XmlUtils.parseBean(result, SyncDepResult.Dp.class);
        if (dp == null || CollectionUtil.isEmpty(dp.getInfo())) {
            log.error("（同步职能部门）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return dp.getInfo().get(0);
    }

    /**
     * 同步实践阵地
     *
     * @param syncBase 实践阵地信息
     * @return 同步结果
     */
    public static SyncBaseResult syncBase(SyncBase syncBase) {
        String result = ApiWebUtil.sendHttpRequest("syncBase", syncBase.buildXml());
        SyncBaseResult.Pb pb = XmlUtils.parseBean(result, SyncBaseResult.Pb.class);
        if (pb == null || CollectionUtil.isEmpty(pb.getInfo())) {
            log.error("（同步实践阵地）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return pb.getInfo().get(0);
    }

    /**
     * 同步实践点
     *
     * @param syncSpot 实践点信息
     * @return 同步结果
     */
    public static SyncSpotResult syncSpot(SyncSpot syncSpot) {
        String result = ApiWebUtil.sendHttpRequest("syncSpot", syncSpot.buildXml());
        SyncSpotResult.Ps ps = XmlUtils.parseBean(result, SyncSpotResult.Ps.class);
        if (ps == null || CollectionUtil.isEmpty(ps.getInfo())) {
            log.error("（同步实践点）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return ps.getInfo().get(0);
    }
    /**
     * 同步资源
     *
     * @param syncRes 资源信息
     * @return 同步结果
     */
    public static SyncResResult syncRes(SyncRes syncRes) {
        String result = ApiWebUtil.sendHttpRequest("syncRes", syncRes.buildXml());
        SyncResResult.Res res = XmlUtils.parseBean(result, SyncResResult.Res.class);
        if (res == null || CollectionUtil.isEmpty(res.getInfo())) {
            log.error("（同步资源）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return res.getInfo().get(0);
    }

    /**
     * 同步团队
     *
     * @param syncTeam 团队信息
     * @return 同步结果
     */
    public static SyncTeamResult syncTeam(SyncTeam syncTeam) {
        String result = ApiWebUtil.sendHttpRequest("syncTeam", syncTeam.buildXml());
        SyncTeamResult.Org org = XmlUtils.parseBean(result, SyncTeamResult.Org.class);
        if (org == null || CollectionUtil.isEmpty(org.getInfo())) {
            log.error("（同步团队）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return org.getInfo().get(0);
    }

    /**
     * 志愿者加入团队
     *
     * @param joinTeam 志愿者加入团队的信息
     * @return 同步结果
     */
    public static JoinTeamResult joinTeam(JoinTeam joinTeam) {
        String result = ApiWebUtil.sendHttpRequest("syncOrgMember", joinTeam.buildXml());
        JoinTeamResult.Msgs msgs = XmlUtils.parseBean(result, JoinTeamResult.Msgs.class);
        if (msgs == null || CollectionUtil.isEmpty(msgs.getInfo()) || CollectionUtil.isEmpty(msgs.getInfo().get(0).getOrg_outinfo())) {
            log.error("（志愿者加入团队）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return msgs.getInfo().get(0).getOrg_outinfo().get(0);
    }

    /**
     * 同步实践单
     *
     * @param syncOrder 同步实践单的信息
     * @return 同步结果
     */
    public static SyncOrderResult syncOrder(SyncOrder syncOrder) {
        String result = ApiWebUtil.sendHttpRequest("syncOrder", syncOrder.buildXml());
        SyncOrderResult.Order order = XmlUtils.parseBean(result, SyncOrderResult.Order.class);
        if (order == null || CollectionUtil.isEmpty(order.getInfo()) ) {
            log.error("（同步实践单）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return order.getInfo().get(0);
    }

    /**
     * 同步cms新闻
     *
     * @param syncCms 新闻信息
     * @return 同步结果
     */
    public static SyncCmsResult syncCms(SyncCms syncCms) {
        return syncCms(syncCms, false);
    }

    /**
     * 同步cms新闻CBC加密
     *
     * @param syncCms 新闻信息
     * @return 同步结果
     */
    public static SyncCmsResult syncCmsCBC(SyncCms syncCms) {
        return syncCms(syncCms, true);
    }

    /**
     * 同步cms新闻
     *
     * @param syncCms 新闻信息
     * @return 同步结果
     */
    private static SyncCmsResult syncCms(SyncCms syncCms, boolean withCbc) {
        if (withCbc) {
            ApiWebUtil.syncCmsCBCDs3Encoding(syncCms);
        }
        String result = ApiWebUtil.sendHttpRequest("syncNewsInfo", syncCms.buildXml(), "xml", withCbc);
        SyncCmsResult.Msgs msgs = XmlUtils.parseBean(result, SyncCmsResult.Msgs.class);
        if (msgs == null || CollectionUtil.isEmpty(msgs.getInfo()) || CollectionUtil.isEmpty(msgs.getInfo().get(0).getInfo())) {
            log.error("（同步cms新闻）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return msgs.getInfo().get(0).getInfo().get(0);
    }

    /**
     * 同步需求
     *
     * @param syncRequirement 需求信息
     * @return 同步结果
     */
    public static SyncRequirementResult syncRequirement(SyncRequirement syncRequirement) {
        String result = ApiWebUtil.sendHttpRequest("syncReq", syncRequirement.buildXml());
        SyncRequirementResult.Req req = XmlUtils.parseBean(result, SyncRequirementResult.Req.class);
        if (req == null || CollectionUtil.isEmpty(req.getInfo())) {
            log.error("（同步需求）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return req.getInfo().get(0);
    }
    /**
     * 注册活动
     *
     * @param syncActp 活动信息
     * @return 同步结果
     */
    public static SyncActpResult syncActivity(SyncActp syncActp) {
        String result = ApiWebUtil.sendHttpRequest("syncActp", syncActp.buildXml());
        SyncActpResult.ActPlan actPlan = XmlUtils.parseBean(result, SyncActpResult.ActPlan.class);
        if (actPlan == null || actPlan.getInfo().getInfo()==null) {
            log.error("（注册活动）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return actPlan.getInfo().getInfo();
    }
    /**
     * 发布活动招募
     *
     * @param syncActs 发布活动招募信息
     * @return 同步结果
     */
    public static SyncActsResult syncActivityRecruit(SyncActs syncActs) {
        String result = ApiWebUtil.sendHttpRequest("syncActs", syncActs.buildXml());
        SyncActsResult.Act act = XmlUtils.parseBean(result, SyncActsResult.Act.class);
        if (act == null || CollectionUtil.isEmpty(act.getInfo())) {
            log.error("（发布活动招募）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return act.getInfo().get(0);
    }
    /**
     * 加入活动招募
     *
     * @param syncActMember 加入活动招募信息
     * @return 同步结果
     */
    public static SyncActMemberResult syncActMember(SyncActMember syncActMember) {
        String result = ApiWebUtil.sendHttpRequest("syncActMember", syncActMember.buildXml());
        SyncActMemberResult.ActMember actMember = XmlUtils.parseBean(result, SyncActMemberResult.ActMember.class);
        if (actMember == null || CollectionUtil.isEmpty(actMember.getInfo())) {
            log.error("（加入活动招募）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return actMember.getInfo().get(0);
    }

    /**
     * 加入活动招募(批量)
     *
     * @param syncActMembers 加入活动招募信息
     * @return 同步结果
     */
    public static List<SyncActMemberResult> syncActMemberBatch(List<SyncActMember> syncActMembers) {
        String result = ApiWebUtil.sendHttpRequest("syncActMember", SyncActMemberBuildXmlUtil.buildXml(syncActMembers));
        SyncActMemberResult.ActMember actMember = XmlUtils.parseBean(result, SyncActMemberResult.ActMember.class);
        if (actMember == null || CollectionUtil.isEmpty(actMember.getInfo())) {
            log.error("（加入活动招募）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return actMember.getInfo();
    }

    /**
     * 服务时长上传
     *
     * @param syncServiceTime 服务时长上传信息
     * @return 同步结果
     */
    public static SyncServiceTimeResult syncServiceTime(SyncServiceTime syncServiceTime) {
        String result = ApiWebUtil.sendHttpRequest("syncServiceTime", syncServiceTime.buildXml());
        SyncServiceTimeResult.ServerTime serverTime = XmlUtils.parseBean(result, SyncServiceTimeResult.ServerTime.class);
        if (serverTime == null || CollectionUtil.isEmpty(serverTime.getInfo().getInfo())) {
            log.error("（服务时长上传）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return serverTime.getInfo().getInfo().get(0);
    }

    /**
     * 服务时长上传(批量)
     *
     * @param serviceTimeList 服务时长上传信息
     * @return 同步结果
     */
    public static List<SyncServiceTimeResult> syncServiceTimeBatch(List<SyncServiceTime> serviceTimeList) {
        String result = ApiWebUtil.sendHttpRequest("syncServiceTime", SyncServiceTimeBuildXmlUtil.buildXml(serviceTimeList));
        SyncServiceTimeResult.ServerTime serverTime = XmlUtils.parseBean(result, SyncServiceTimeResult.ServerTime.class);
        if (serverTime == null || CollectionUtil.isEmpty(serverTime.getInfo().getInfo())) {
            log.error("（服务时长上传）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return serverTime.getInfo().getInfo();
    }


    /**
     * 服务领域查询
     *
     * @return 获取服务领域的数据字典
     */
    public static List<ServiceCodeResult> getServiceType() {
        String result = ApiWebUtil.sendHttpRequest("getZyzServiceType", "");
        ServiceCodeResult.ServiceCodePs ps = XmlUtils.parseBean(result, ServiceCodeResult.ServiceCodePs.class);
        if (ps == null) {
            log.error("（服务领域查询）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return ps.getInfo();
    }

    /**
     * 服务领域查询,园区320508
     *
     * @return 获取服务领域的数据字典
     */
    public static List<RegionResult> getRegion(String parentCode) {
        RegionQuery query = new RegionQuery();
        query.setParentCode(parentCode);
        String result = ApiWebUtil.sendHttpRequest("getRegion", query.buildXml());
        RegionResult.Ps ps = XmlUtils.parseBean(result, RegionResult.Ps.class);
        if (ps == null) {
            log.error("（服务领域查询）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return ps.getInfo();
    }

    /**
     * 主管部门类别查询
     *
     * @return 获取服务领域的数据字典
     */
    public static List<DepTypeResult> getDepType() {
        String result = ApiWebUtil.sendHttpRequest("getDepType", "");
        DepTypeResult.DepType depType = XmlUtils.parseBean(result, DepTypeResult.DepType.class);
        if (depType == null) {
            log.error("（ 主管部门类别查询）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return depType.getInfo();
    }
    public static SyncActShowResult syncActShow(SyncActShow syncActShow) {
        String result = ApiWebUtil.sendHttpRequest("syncActShow", syncActShow.buildXml());
        SyncActShowResult.ActShow act = XmlUtils.parseBean(result, SyncActShowResult.ActShow.class);
        if (act == null || CollectionUtil.isEmpty(act.getInfo())) {
            log.error("（发布活动公示）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return act.getInfo().get(0);
    }

    public static SyncActScheduleResult syncActSchedule(SyncActSchedule syncActSchedule) {
        String result = ApiWebUtil.sendHttpRequestJson("syncActSchedule", syncActSchedule.buildXml());
        ActScheduleResultDto dto = JSONObject.parseObject(result, ActScheduleResultDto.class);
        if (dto == null || CollectionUtil.isEmpty(dto.getOut())) {
            log.error("（阵地计划）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return dto.getOut().get(0);
    }

    public static SyncScheduleActPlanResult syncScheduleActPlan(SyncScheduleActPlan syncScheduleActPlan) {
        String result = ApiWebUtil.sendHttpRequest("syncScheduleActPlan", syncScheduleActPlan.buildXml());
        SyncScheduleActPlanResult.ScheduleActPlan plan= XmlUtils.parseBean(result, SyncScheduleActPlanResult.ScheduleActPlan.class);
        if (plan == null || CollectionUtil.isEmpty(plan.getInfo())) {
            log.error("（阵地计划活动关联）市平台调用成功，返回的xml无法解析:{}", result);
            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
        }
        return plan.getInfo().get(0);
    }
}
