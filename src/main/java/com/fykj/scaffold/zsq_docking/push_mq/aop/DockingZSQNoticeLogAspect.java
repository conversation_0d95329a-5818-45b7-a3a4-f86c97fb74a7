package com.fykj.scaffold.zsq_docking.push_mq.aop;

import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.zsq_docking.log.service.*;
import com.fykj.scaffold.zsq_docking.push_mq.event.*;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzRequirementService;
import com.fykj.scaffold.zyz.service.IZyzResourceService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.JsonResult;
import result.Result;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;

@Aspect
@Component
@Slf4j
public class DockingZSQNoticeLogAspect {

    @Autowired
    private INewsService newsService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzRequirementService requirementService;

    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private ISysOrgService sysOrgService;

    @Autowired
    private IZSQDockingLogService logService;

    @Autowired
    private IZSQDockingNewsRecordService newsDockingRecordService;

    @Autowired
    private IZSQDockingActivityRecordService activityDockingRecordService;

    @Autowired
    private IZSQDockingRequirementRecordService requirementDockingRecordService;

    @Autowired
    private IZSQDockingResourceRecordService resourceDockingRecordService;

    @Autowired
    private IZSQDockingBelongFieldRecordService belongFieldDockingRecordService;

    @Autowired
    private IZSQDockingRegionAllRecordService regionAllDockingRecordService;

    @Autowired
    private IZSQDockingSysOrgRecordService sysOrgDockingRecordService;

    // 正常返回时记录日志
    @AfterReturning(pointcut = "@annotation(com.fykj.scaffold.zsq_docking.push_mq.aop.DockingZSQNoticeLog)", returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, Object result) {
        logNotice(joinPoint, result);
    }

    // 提取公共日志记录逻辑
    private void logNotice(JoinPoint joinPoint, Object result) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        Object notice = null;
        for (int i = 0; i < paramNames.length; i++) {
            String paramName = paramNames[i];
            if (ZSQ_DOCKING_PUSH_PARAM_NOTICE.equals(paramName)) {
                notice = args[i];
            }
        }
        if (notice == null) {
            String methodName = ((MethodSignature) joinPoint.getSignature()).getMethod().getName();
            if (ZSQ_NEWS_DOCKING_PUSH_NOTICE_METHOD.equals(methodName)) {
                notice = new NewsDockingEvent();
                ((BaseDockingMqEvent)notice).setMsgId(0);
            }
            if (ZSQ_ACTIVITY_DOCKING_PUSH_NOTICE_METHOD.equals(methodName)) {
                notice = new ActivityDockingEvent();
                ((BaseDockingMqEvent)notice).setMsgId(0);
            }
            if (ZSQ_REQUIREMENT_DOCKING_PUSH_METHOD.equals(methodName)) {
                notice = new RequirementDockingEvent();
                ((BaseDockingMqEvent)notice).setMsgId(0);
            }
            if (ZSQ_RESOURCE_DOCKING_PUSH_METHOD.equals(methodName)) {
                notice = new ResourceDockingEvent();
                ((BaseDockingMqEvent)notice).setMsgId(0);
            }
            if (ZSQ_BELONG_FIELD_DOCKING_PUSH_METHOD.equals(methodName)) {
                notice = new BelongFieldDockingEvent();
                ((BaseDockingMqEvent)notice).setMsgId(0);
            }
            if (ZSQ_REGION_ALL_DOCKING_PUSH_METHOD.equals(methodName)) {
                notice = new RegionAllDockingEvent();
                ((BaseDockingMqEvent)notice).setMsgId(0);
            }
            if (ZSQ_SYS_ORG_DOCKING_PUSH_METHOD.equals(methodName)) {
                notice = new SysOrgDockingEvent();
                ((BaseDockingMqEvent)notice).setMsgId(0);
            }
        }
        Object data = null;
        if (result instanceof JsonResult) {
            data = ((JsonResult<?>)result).getObj();
        }
        if (data == null && notice != null) {
           if (notice instanceof NewsDockingEvent) {
               Long newsId = ((NewsDockingEvent) notice).getNewsId();
               try {
                   data = newsId == null ? null : newsService.getNewsDetail(newsId);
               } catch (Exception e) {
                   data = "新闻查询异常，异常原因：【" + e.getMessage() + "】！";
               }
           }
            if (notice instanceof ActivityDockingEvent) {
                Long actId = ((ActivityDockingEvent) notice).getActId();
                try {
                    data = actId == null ? null : activityService.getActivityDetail(actId);
                } catch (Exception e) {
                    data = "活动查询异常，异常原因：【" + e.getMessage() + "】！";
                }
            }
            if (notice instanceof RequirementDockingEvent) {
                Long reqId = ((RequirementDockingEvent) notice).getRequirementId();
                try {
                    data = reqId == null ? null : requirementService.getRequirementById(reqId);
                } catch (Exception e) {
                    data = "需求查询异常，异常原因：【" + e.getMessage() + "】！";
                }
            }
            if (notice instanceof ResourceDockingEvent) {
                Long  resId = ((ResourceDockingEvent) notice).getResourceId();
                try {
                    data = resId == null ? null : resourceService.getById(resId);
                } catch (Exception e) {
                    data = "资源查询异常，异常原因：【" + e.getMessage() + "】！";
                }
            }
            if (notice instanceof BelongFieldDockingEvent) {

            }
            if (notice instanceof RegionAllDockingEvent) {

            }

            if (notice instanceof SysOrgDockingEvent) {
                Long  orgId = ((SysOrgDockingEvent) notice).getOrgId();
                try {
                    data = orgId == null ? null : sysOrgService.getById(orgId);
                } catch (Exception e) {
                    data = "组织查询异常，异常原因：【" + e.getMessage() + "】！";
                }
            }
        }
        if (notice instanceof NewsDockingEvent) {
            Long dockingRecordId = ((NewsDockingEvent) notice).getDockingRecordId();
            try {
                newsDockingRecordService.updateDockingRecord(dockingRecordId, (Result)result);
            } catch (Exception e) {
                log.error("知社区新闻对接-新闻数据推送-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
            try {
                logService.saveNewsPushLog((NewsDockingEvent) notice, result, data, dockingRecordId);
            } catch (Exception e) {
                log.error("知社区新闻对接-新闻数据推送-AOP保存新闻数据推送日志异常，异常原因：【{}】!", e.getMessage());
            }
        }
        if (notice instanceof ActivityDockingEvent) {
            Long dockingRecordId = ((ActivityDockingEvent) notice).getDockingRecordId();
            try {
                activityDockingRecordService.updateDockingRecord(dockingRecordId, (Result)result);
            } catch (Exception e) {
                log.error("知社区活动对接-活动数据推送-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
            try {
                logService.saveActivityPushLog((ActivityDockingEvent) notice, result, data, dockingRecordId);
            } catch (Exception e) {
                log.error("知社区活动对接-活动数据推送-AOP保存活动数据推送日志异常，异常原因：【{}】!", e.getMessage());
            }
        }
        if (notice instanceof RequirementDockingEvent) {
            Long dockingRecordId = ((RequirementDockingEvent) notice).getDockingRecordId();
            try {
                requirementDockingRecordService.updateDockingRecord(dockingRecordId, (Result)result);
            } catch (Exception e) {
                log.error("知社区需求对接-需求数据推送-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
            try {
                logService.saveRequirementPushLog((RequirementDockingEvent) notice, result, data, dockingRecordId);
            } catch (Exception e) {
                log.error("知社区需求对接-需求数据推送-AOP保存需求数据推送日志异常，异常原因：【{}】!", e.getMessage());
            }
        }
        if (notice instanceof ResourceDockingEvent) {
            Long dockingRecordId = ((ResourceDockingEvent) notice).getDockingRecordId();
            try {
                resourceDockingRecordService.updateDockingRecord(dockingRecordId, (Result)result);
            } catch (Exception e) {
                log.error("知社区资源对接-资源数据推送-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
            try {
                logService.saveResourcePushLog((ResourceDockingEvent) notice, result, data, dockingRecordId);
            } catch (Exception e) {
                log.error("知社区资源对接-资源数据推送-AOP保存资源数据推送日志异常，异常原因：【{}】!", e.getMessage());
            }
        }
        if (notice instanceof BelongFieldDockingEvent) {
            Long dockingRecordId = ((BelongFieldDockingEvent) notice).getDockingRecordId();
            try {
                belongFieldDockingRecordService.updateDockingRecord(dockingRecordId, (Result)result);
            } catch (Exception e) {
                log.error("知社区所属领域对接-所属领域数据推送-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
            try {
                logService.saveBelongFieldPushLog((BelongFieldDockingEvent) notice, result, data, dockingRecordId);
            } catch (Exception e) {
                log.error("知社区所属领域对接-所属领域数据推送-AOP保存所属领域数据推送日志异常，异常原因：【{}】!", e.getMessage());
            }
        }
        if (notice instanceof RegionAllDockingEvent) {
            Long dockingRecordId = ((RegionAllDockingEvent) notice).getDockingRecordId();
            try {
                regionAllDockingRecordService.updateDockingRecord(dockingRecordId, (Result)result);
            } catch (Exception e) {
                log.error("知社区所有社区对接-所有社区数据推送-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
            try {
                logService.saveRegionAllPushLog((RegionAllDockingEvent) notice, result, data, dockingRecordId);
            } catch (Exception e) {
                log.error("知社区所有社区对接-所有社区数据推送-AOP保存所有社区数据推送日志异常，异常原因：【{}】!", e.getMessage());
            }
        }

        if (notice instanceof SysOrgDockingEvent) {
            Long dockingRecordId = ((SysOrgDockingEvent) notice).getDockingRecordId();
            try {
                sysOrgDockingRecordService.updateDockingRecord(dockingRecordId, (Result)result);
            } catch (Exception e) {
                log.error("知社区组织对接-组织数据推送-AOP更新对接记录信息异常，异常原因：【{}】!", e.getMessage());
            }
            try {
                logService.saveSysOrgPushLog((SysOrgDockingEvent) notice, result, data, dockingRecordId);
            } catch (Exception e) {
                log.error("知社区组织对接-组织数据推送-AOP保存组织数据推送日志异常，异常原因：【{}】!", e.getMessage());
            }
        }
    }
}
