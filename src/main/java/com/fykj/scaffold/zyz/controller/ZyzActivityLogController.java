package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityLog;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirementLog;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityLogParams;
import com.fykj.scaffold.zyz.service.IZyzActivityLogService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

/**
 * 资源操作记录表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-03-09
 */
@RestController
@RequestMapping("/admin/zyz/activity/log")
@Api(tags = "活动日志接口")
public class ZyzActivityLogController extends BaseController<IZyzActivityLogService, ZyzActivityLog,ZyzActivityLogParams> {


    @ApiOperation("查询活动日志")
    @GetMapping({"/getLogsByActivityId"})
    public JsonResult<List<ZyzActivityLog>> getLogsByActivityId(Long activityId) {
        List<ZyzActivityLog> result = this.baseService.getLogsByActivityId(activityId);
        DictTransUtil.trans(result);
        return new JsonResult<>(result);
    }
}
