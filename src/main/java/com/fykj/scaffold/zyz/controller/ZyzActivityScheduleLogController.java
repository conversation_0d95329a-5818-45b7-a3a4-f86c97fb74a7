package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleLog;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleLogParams;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleLogService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 阵地计划操作记录表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@RestController
@RequestMapping("/admin/zyz/activity/schedule/log")
@Api(tags = " 阵地计划操作记录表接口")
public class ZyzActivityScheduleLogController extends BaseController<IZyzActivityScheduleLogService, ZyzActivityScheduleLog, ZyzActivityScheduleLogParams> {

    @Autowired
    private IDictService dictService;

    @ApiOperation("查询阵地计划日志")
    @GetMapping({"/getLogsByScheduleId"})
    public JsonResult<List<ZyzActivityScheduleLog>> getLogsByScheduleId(Long scheduleId) {
        List<ZyzActivityScheduleLog> result = this.baseService.getLogsByScheduleId(scheduleId);
        result.forEach(x -> x.setOperateTypeText(dictService.getNameByCode(x.getOperateType())));
        return new JsonResult<>(result);
    }
}
