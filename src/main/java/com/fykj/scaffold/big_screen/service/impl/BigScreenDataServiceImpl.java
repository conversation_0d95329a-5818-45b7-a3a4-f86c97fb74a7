package com.fykj.scaffold.big_screen.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.big_screen.domain.entity.BigScreenData;
import com.fykj.scaffold.big_screen.mapper.BigScreenDataMapper;
import com.fykj.scaffold.big_screen.service.IBigScreenDataService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

/**
 * <p>
 * 大屏数据创造服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class BigScreenDataServiceImpl extends BaseServiceImpl<BigScreenDataMapper, BigScreenData> implements IBigScreenDataService {

    @Autowired
    private ISysOrgService orgService;

    public static final String DIMENSION_OV = "BSDD_OVERVIEW";

    public static final String DIMENSION_OS = "BSDD_ORG_SUM";

    public static final String DIMENSION_CP = "BSDD_CIVILIZATION_PRACTICE";

    @Override
    public boolean save(BigScreenData entity) {
        if (entity.getStatus()) {
            if (!validateStatus(null, entity)) {
                throw new BusinessException(ResultCode.FAIL, "同一维度数据存在启用中的自造数据，请先禁用！");
            }
        }
        return super.save(entity);
    }

    @Override
    public boolean updateById(BigScreenData entity) {
        if (entity.getStatus()) {
            if (!validateStatus(entity.getId(), entity)) {
                throw new BusinessException(ResultCode.FAIL, "同一维度数据存在启用中的自造数据，请先禁用！");
            }
        }
        return super.updateById(entity);
    }

    @Override
    public IPage<BigScreenData> page(BaseParams params) {
        IPage<BigScreenData> result = super.page(params);
        DictTransUtil.trans(result.getRecords());
        result.getRecords().forEach(it -> {
            if (StringUtil.isNotEmpty(it.getOrg())) {
                it.setOrgName(orgService.getOrgName(it.getOrg()));
            }
        });
        return result;
    }

    @Override
    public void statusChange(Long id) {
        BigScreenData data = getById(id);
        if (!data.getStatus()) {
            if (!validateStatus(id, data)) {
                throw new BusinessException(ResultCode.FAIL, "同一维度数据存在启用中的自造数据，请先禁用！");
            }
        }
        data.setStatus(!data.getStatus());
        super.updateById(data);
    }

    private boolean validateStatus(Long id, BigScreenData data) {
        return !lambdaQuery().eq(BigScreenData::getDimension, data.getDimension())
                .eq(StringUtil.isNotEmpty(data.getOrg()), BigScreenData::getOrg, data.getOrg())
                .eq(StringUtil.isNotEmpty(data.getType()), BigScreenData::getType, data.getType())
                .ne(ObjectUtil.isNotEmpty(id), BigScreenData::getId, data.getId())
                .eq(BigScreenData::getStatus, Boolean.TRUE).exists();
    }
}
