package com.fykj.scaffold.sync.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.conns.Cons;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

;

/**
 * 市平台信息同步日志
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
@Data
public class ZyzSyncLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 幂等id
     */
    @ApiModelProperty(value = "幂等id")
    private String syncBillId;


    /**
     * 同步对象
     */
    @ApiModelProperty(value = "同步对象")
    private String syncObjectName;

    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private String resultCode;

    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private String resultName;

    /**
     * 市平台返回的消息
     */
    @ApiModelProperty(value = "市平台返回的消息")
    private String message;

    /**
     * 同步时间
     */
    @ApiModelProperty(value = "同步时间")
    private String syncTime;

    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String spCode;


}
