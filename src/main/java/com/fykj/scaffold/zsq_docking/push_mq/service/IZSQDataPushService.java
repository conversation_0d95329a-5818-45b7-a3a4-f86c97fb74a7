package com.fykj.scaffold.zsq_docking.push_mq.service;

import com.fykj.scaffold.zsq_docking.push_mq.event.*;
import result.Result;

/**
 * 知社区数据推送服务接口
 *
 * @Author：yangxu
 * @Date：2025/4/21 16:28
 */
public interface IZSQDataPushService {

    /**
     * 新闻对接
     *
     * @param notice
     */
    Result newsDocking(NewsDockingEvent notice);

    /**
     * 活动对接
     *
     * @param notice
     */
    Result activityDocking(ActivityDockingEvent notice);

    /**
     * 资源对接
     *
     * @param notice
     */
    Result resourceDocking(ResourceDockingEvent notice);

    /**
     * 需求对接
     *
     * @param notice
     */
    Result requirementDocking(RequirementDockingEvent notice);

    /**
     * 组织对接
     *
     * @param notice
     */
    Result sysOrgDocking(SysOrgDockingEvent notice);

    /**
     * 所属领域对接
     *
     * @param notice
     */
    Result belongFieldDocking(BelongFieldDockingEvent notice);

    /**
     * 所有社区对接
     *
     * @param notice
     */
    Result regionAllDocking(RegionAllDockingEvent notice);
}
