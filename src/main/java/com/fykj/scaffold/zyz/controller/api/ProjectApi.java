package com.fykj.scaffold.zyz.controller.api;

import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.service.IZyzProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

@RestController
@RequestMapping("/api/zyz/project")
@Api(tags = "pc--公益伙伴前端接口")
public class ProjectApi {

    @Autowired
    private IZyzProjectService projectService;
    @AuditLog("前端展示年份列表")
    @ApiOperation("前端展示年份列表")
    @GetMapping("/getYearList")
    public JsonResult<List<String>> getYearList() {
        return new JsonResult<>(projectService.getYearList());
    }

}
