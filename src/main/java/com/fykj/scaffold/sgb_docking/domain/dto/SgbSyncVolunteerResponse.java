package com.fykj.scaffold.sgb_docking.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 推送志愿者信息响应
 */
@Data
public class SgbSyncVolunteerResponse {
    
    /**
     * 响应信息列表
     */
    private List<VolunteerResult> info;
    
    /**
     * 志愿者信息推送结果
     */
    @Data
    public static class VolunteerResult {

        /**
         * 响应状态码
         * SUCCESS或FAIL
         */
        private String code;
        
        /**
         * 失败信息
         */
        private String msg;
        
        /**
         * 身份证号码
         */
        private String idCode;

        /**
         * 志愿者编号
         */
        private String uId;

    }
} 