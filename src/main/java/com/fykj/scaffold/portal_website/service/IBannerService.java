package com.fykj.scaffold.portal_website.service;

import com.fykj.scaffold.portal_website.domain.entity.Banner;
import fykj.microservice.core.base.IBaseService;

import java.util.List;


/**
 * <p>
 * 轮播图服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface IBannerService extends IBaseService<Banner> {

    /**
     * 风采上下架
     * @param id
     */
    void ground(String id);
    /**
     * API查询
     * @param categoryCode
     * @return
     */
    List<Banner> pageForApi(String categoryCode);
}
