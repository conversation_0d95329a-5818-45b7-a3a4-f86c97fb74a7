package com.fykj.scaffold.race.controller;


import cn.hutool.core.img.Img;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.race.cons.Constants;
import com.fykj.scaffold.race.domain.dto.PictureDto;
import com.fykj.scaffold.race.domain.entity.Photo;
import com.fykj.scaffold.race.domain.params.PhotoParams;
import com.fykj.scaffold.race.service.IPhotoService;
import com.fykj.scaffold.security.business.domain.entity.OssConfig;
import com.fykj.scaffold.security.business.service.IOssConfigService;
import com.fykj.scaffold.support.oss.FilePathDto;
import com.fykj.scaffold.support.oss.OssCons;
import com.fykj.scaffold.support.oss.OssSaveUtil;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.util.SystemUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;

/**
 * 摄影大赛-作品表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@RestController
@Slf4j
@RequestMapping("/admin/race/photo")
public class PhotoController extends BaseController<IPhotoService, Photo, PhotoParams> {


    @Autowired
    private IOssConfigService ossConfigService;


    /**
     * 审核，通过给作品编号赋值
     * /admin/oss/upload
     *
     * @param
     */
    @ApiOperation(value = "审核")
    @GetMapping(value = "/updateStatus")
    public Result setEnable(@RequestParam String ids, @RequestParam Boolean status, @RequestParam(required = false) String auditRemark) {
        if (baseService.updateStatus(ids, status, auditRemark)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }

    @ApiOperation(value = "获取活动参赛作品列表")
    @GetMapping(value = "/getPhoto")
    public JsonResult<List<Photo>> getPhoto(@RequestParam Long activityId) {
        List<Photo> result = baseService.getPhoto(activityId);
        return new JsonResult<>(result);
    }

    @ApiOperation("参赛作品分页查询--网页端")
    @PostMapping("/getPagesForWeb")
    public JsonResult<IPage<Photo>> getPagesForWeb(@RequestBody PhotoParams params) {
        return new JsonResult<>(baseService.getPagesForWeb(params));
    }

    @ApiOperation("参赛作品分页查询--后端页面")
    @PostMapping("/getPages")
    public JsonResult<IPage<Photo>> getPages(@RequestBody PhotoParams params) {
        IPage<Photo> res = baseService.getPages(params);
        DesensitiseUtil.desensitise(res.getRecords());
        return new JsonResult<>(res);
    }

    @ApiOperation(value = "获取我的参赛作品列表")
    @GetMapping(value = "/getMyPhoto")
    public JsonResult<List<Photo>> getMyPhoto() {
        List<Photo> result = baseService.getMyPhoto();
        return new JsonResult<>(result);
    }


    @ApiOperation("上传文件")
    @PostMapping(value = "/uploadPhoto")
    public Result uploadPhoto(@RequestParam(name = "file") MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return new Result(ResultCode.BAD_REQUEST.code(), "请选择要上传的文件");
        }
        FilePathDto pathFile = OssSaveUtil.save(file.getInputStream(), OssCons.OSS_LOCAL, file.getOriginalFilename());
        String path = pathFile.getUrl();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        Img.from(file.getInputStream()).setQuality(0.8).write(os);
        ByteArrayInputStream in = new ByteArrayInputStream(os.toByteArray());
        FilePathDto slimPathFile = OssSaveUtil.save(in, OssCons.OSS_LOCAL, file.getOriginalFilename());
        String slimPath = slimPathFile.getUrl();
        PictureDto pictureDto = new PictureDto();
        pictureDto.setPath(path);
        pictureDto.setSlimPath(slimPath);
        return new JsonResult<>(pictureDto);
    }

    /**
     * 导出作品，在临时文件夹生成zip文件,最后删除文件
     *
     * @param activityId
     */
    @ApiOperation("导出作品")
    @GetMapping("/exportPhoto-deprecate")
    public void exportPhoto(Long activityId) {
        List<Photo> list = baseService.getPhoto(activityId);
        // 获取临时目录并打印。
        String property = "java.io.tmpdir";
//        String tempDir = System.getProperty(property);
        String files = "/home/<USER>/syds";
        OssConfig config = ossConfigService.getConfig();
        log.info("开始循环复制");
        list.parallelStream().forEach(item -> {
            String targetFile = files + File.separator + item.getNumber().toString().concat("-")
                    .concat(item.getAuthor())
                    .concat("-")
                    .concat(item.getName().substring(0, Math.min(item.getName().length(), 30)));
            int index = 1;
            for (PictureDto record : item.getPictureList()) {
                String realPath = record.getPath().replace(config.getUrl(), config.getStorageLocation());
                File temp = FileUtil.file(realPath);
                BufferedInputStream in = FileUtil.getInputStream(temp.getPath());
                String res;
                if (item.getPictureList().size() > 1) {
                    res = targetFile.concat("（").concat(String.valueOf(index)).concat(")").concat(".").concat(FileUtil.extName(temp));
                } else {
                    res = targetFile.concat(".").concat(FileUtil.extName(temp));
                }
                File resFile = new File(res);
                log.info("开始写一个文件");
                FileUtil.writeFromStream(in, resFile);
                log.info("写一个文件结束");
//                writeFile(resFile, in);
                index++;
            }
            log.info("循环复制一个完成");
        });
        log.info("循环复制结束");
        //压缩文件夹
        log.info("开始zip");
        File zipFile = ZipUtil.zip(files);
        log.info("zip结束");
        HttpServletResponse response = SystemUtil.getResponse();
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;success=true;filename=" + zipFile.getPath());
        response.setContentType("application/zip");
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            BufferedInputStream inputStream = FileUtil.getInputStream(zipFile);
            FileCopyUtils.copy(inputStream, os);
            outputStream.write(os.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
//        //删除文件
//        zipFile.delete();
    }

//    /**
//     * 创建文件夹
//     *
//     * @param tempDir
//     * @return
//     */
//    private String getVirtualPath(String tempDir) {
//        String path;
//        if (tempDir.endsWith(File.separator)) {
//            path = tempDir.concat("摄影大赛参赛作品").concat(File.separator).concat(String.valueOf(System.currentTimeMillis()));
//        } else {
//            path = tempDir.concat(File.separator).concat("摄影大赛参赛作品").concat(File.separator).concat(String.valueOf(System.currentTimeMillis()));
//        }
//        //获取文件分类所在文件夹，分类文件夹里每天再分一个文件夹,如果没有则先创建
//        File dir = new File(path);
//        if (dir.exists() || dir.mkdirs()) {
//            return path;
//        }
//        throw new BusinessException(ResultCode.FAIL, "创建文件夹失败：" + path);
//    }


    /**
     * 写文件
     *
     * @param destFile 目标文件夹
     * @param is       输入流
     */
    private void writeFile(File destFile, InputStream is) {
        try (FileOutputStream fos = new FileOutputStream(destFile)) {
            Img.from(is).write(fos);
        } catch (IOException e) {
            throw new BusinessException(ResultCode.ERROR, "文件保存失败io异常");
        }
    }

    /**
     * 将七牛云的图片保存到本地
     *
     * @param
     * @return
     */
    @ApiOperation("将七牛云的图片保存到本地")
    @PostMapping(value = "/uploadPhotoFromQYN")
    public Result uploadPhotoFromQYN(Long activityId) throws IOException {
        try {
            List<Photo> photos = baseService.getPhoto(activityId);
            photos.forEach(item -> {
                try {
                    item.getPictureList().forEach(it -> {
                        //根据地址从七牛云下载图片
                        String path = savePhotoByQYN(it.getPath(), it.getPath());
                        it.setPath(path);
//                        String slimPath = savePhotoByQYN(it.getSlimPath(),it.getSlimPath());
                        it.setSlimPath(it.getSlimPath());
                    });
                    baseService.updateById(item);
                } catch (Exception e) {
                    log.error("保存图片失败" + item.getNumber(), e);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

        return OK;
    }

    private static String savePhotoByQYN(String url, String fileName) {
        return OssSaveUtil.save(url, OssCons.OSS_LOCAL, fileName).getUrl();
    }

    @ApiOperation(value = "PC端重新提交")
    @PostMapping(value = "/pcUpdate")
    public Result pcUpdate(@RequestBody @Validated({BaseEntity.Modify.class}) Photo entity) {
        entity.setAuditStatus(Constants.AUDIT_STATUS_CHECK_PENDING);
        if (baseService.updateById(entity)) {
            return OK;
        }
        return new Result(ResultCode.FAIL);
    }


}
