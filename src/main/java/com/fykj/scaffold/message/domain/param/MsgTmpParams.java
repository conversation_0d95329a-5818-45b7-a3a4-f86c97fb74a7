package com.fykj.scaffold.message.domain.param;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date: 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("消息模版查询参数")
public class MsgTmpParams extends BaseParams {

    @ApiModelProperty("模糊匹配")
    @MatchType(value = QueryType.LIKE,fieldName = {"code", "title", "tmp"})
    private String keyword;

    @ApiModelProperty("精确匹配")
    @MatchType(value = QueryType.EQ,fieldName = "status")
    private Boolean status;
}
