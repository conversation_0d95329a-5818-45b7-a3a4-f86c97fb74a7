package com.fykj.scaffold.security.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.domain.entity.SysUserExpand;
import com.fykj.scaffold.security.business.mapper.SysUserExpandMapper;
import com.fykj.scaffold.security.business.service.IRoleService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.security.business.service.ISysUserExpandService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import constants.Mark;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.Cons.USER_PRI_TYPE_TEAM;


/**
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysUserExpandServiceImpl extends BaseServiceImpl<SysUserExpandMapper, SysUserExpand> implements ISysUserExpandService {

    @Autowired
    private IRoleService roleService;

    @Autowired
    private ISysOrgService orgService;

    @Autowired
    private IZyzTeamService teamService;

    @Override
    public void saveExpands(Long userId, List<SysUserExpand> expandList) {
        baseMapper.deleteByUserId(userId);
        if (CollectionUtil.isEmpty(expandList)) {
            return;
        }
        expandList.forEach(it -> it.setUserId(userId));
        //第一个设为默认
        expandList.get(0).setDefaultSetting(true);
        saveBatch(expandList);
    }

    @Override
    public List<SysUserExpand> getByUserId(Long userId) {
        List<SysUserExpand> userExpandList = lambdaQuery().eq(SysUserExpand::getUserId, userId).list();
        userExpandList.forEach(e -> {
            String roleName = roleService.getRoleByCode(e.getRoleCode()).getName();
            e.setRoleName(roleName);
            if (Cons.RoleCode.ROLE_CODE_SUB_ASSOCIATION_ADMIN.equals(e.getRoleCode())
                    || Cons.RoleCode.ROLE_CODE_COMMUNITY_ADMIN.equals(e.getRoleCode())
                    || Cons.RoleCode.ROLE_CODE_ASSOCIATION_ADMIN.equals(e.getRoleCode())) {
                SysOrg org = orgService.getByCode(e.getOrgCode());
                String orgName = org != null ? org.getName() : "未知部门";
                roleName = roleName + Mark.LINE + orgName;
                e.setDeptName(orgName);
            }
            if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(e.getRoleCode())) {
                ZyzTeam team = teamService.getById(e.getTeamId());
                String teamName = team != null ? team.getName() : "未知团队";
                //团队
                roleName = roleName + Mark.LINE + teamName;
                e.setDeptName(teamName);
                // 设置团队信息完善标识（取反：如果团队已完善则用户不需要完善）
                // 如果团队不存在，不需要完善
                e.setTeamNeedPerfectFlag(Optional.ofNullable(team)
                        .map(x -> !ZyzCons.TEAM_AUDIT_STATUS_WAIT_CHECK.equals(x.getInfoChangeAuditStatus())
                                && !Boolean.TRUE.equals(x.getPerfectFlag()))
                        .orElse(Boolean.FALSE));
            }
            e.setCapacityFullName(roleName);
        });
        return userExpandList;
    }

    @Override
    public void updateDefault(long userId, long expandId) {
        lambdaUpdate().eq(SysUserExpand::getUserId, userId).set(SysUserExpand::getDefaultSetting, false).update();
        lambdaUpdate().eq(SysUserExpand::getUserId, userId).eq(SysUserExpand::getId, expandId).set(SysUserExpand::getDefaultSetting, true).update();
    }

    @Override
    public List<Long> getUserByRoleAndOrg(List<String> roles, String type, List<Long> teamIds, List<String> orgCodes) {
        List<SysUserExpand> records;
        if (USER_PRI_TYPE_TEAM.equals(type)) {
            records = lambdaQuery()
                    .in(SysUserExpand::getRoleCode, roles)
                    .in(SysUserExpand::getTeamId, teamIds)
                    .list();
        } else {
            records = lambdaQuery()
                    .in(SysUserExpand::getRoleCode, roles)
                    .in(SysUserExpand::getOrgCode, orgCodes)
                    .list();
        }
        return CollectionUtil.isEmpty(records) ? null : records.stream().map(SysUserExpand::getUserId).collect(Collectors.toList());
    }

    @Override
    public void removeTeamAdminExpand(long userId, long teamId) {
        List<SysUserExpand> expandList = getByUserId(userId);
        boolean isRemove = expandList.removeIf(it -> it.getTeamId() != null && teamId == it.getTeamId());
        if (isRemove) {
            saveExpands(userId, expandList);
        }
    }

}
