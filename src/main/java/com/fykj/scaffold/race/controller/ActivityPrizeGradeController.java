package com.fykj.scaffold.race.controller;

import com.fykj.scaffold.race.domain.entity.ActivityPrizeGrade;
import com.fykj.scaffold.race.domain.params.ActivityPrizeGradeParams;
import com.fykj.scaffold.race.service.IActivityPrizeGradeService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 摄影大赛-活动奖项等级表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-05
 */
@RestController
@RequestMapping("/admin/race/activity/prize/grade")
public class ActivityPrizeGradeController extends BaseController<IActivityPrizeGradeService, ActivityPrizeGrade, ActivityPrizeGradeParams> {


    @ApiOperation(value = "获取活动奖项")
    @GetMapping(value = "/getPrize")
    public JsonResult<List<ActivityPrizeGrade>> getPrize(@RequestParam Long activityId) {
        List<ActivityPrizeGrade> result = baseService.getPrize(activityId);
        return new JsonResult<>(result);
    }
}
