package com.fykj.scaffold.sync.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncBelongFieldDict;
import com.fykj.scaffold.sync.mapper.ZyzSyncBelongFieldDictMapper;
import com.fykj.scaffold.sync.service.IZyzSyncBelongFieldDictService;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingBelongFieldRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingBelongFieldRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.dto.ActZyzSyncBelongFieldDto;
import com.fykj.scaffold.zsq_docking.push_mq.event.BelongFieldDockingEvent;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.service_type.ServiceCodeResult;
import fykj.microservice.core.base.BaseServiceImpl;
import joptsimple.internal.Strings;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import utils.StringUtil;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.ActivityDataDockingType.DT_CREATE;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;


/**
 * 市平台所属领域数据字典
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzSyncBelongFieldDictServiceImpl extends BaseServiceImpl<ZyzSyncBelongFieldDictMapper, ZyzSyncBelongFieldDict> implements IZyzSyncBelongFieldDictService {


    @Autowired
    private IZSQDockingBelongFieldRecordService belongFieldRecordService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;

    @Override
    public void clearAll() {
        baseMapper.clearAll();
    }

    @Override
    public List<ZyzSyncBelongFieldDict> getByFatherId(String fatherId) {
        return lambdaQuery().eq(ZyzSyncBelongFieldDict::getFatherId, fatherId).orderByAsc(ZyzSyncBelongFieldDict::getTypeId).list();
    }

    @Override
    public ZyzSyncBelongFieldDict getByTypeId(String typeId) {
        return lambdaQuery().eq(ZyzSyncBelongFieldDict::getTypeId, typeId).one();
    }

    @Override
    public void syncBelongFieldDict() {
        List<ZyzSyncBelongFieldDict> allList = lambdaQuery().list();
        //key为typeId，value为minTimeLimit
        Map<String, Integer> minLimitMap = allList.stream().filter(it -> ObjectUtil.isNotEmpty(it.getMinTimeLimit())).collect(Collectors.toMap(ZyzSyncBelongFieldDict::getTypeId, ZyzSyncBelongFieldDict::getMinTimeLimit));
        //key为typeId，value为maxTimeLimit
        Map<String, Integer> maxLimitMap = allList.stream().filter(it -> ObjectUtil.isNotEmpty(it.getMaxTimeLimit())).collect(Collectors.toMap(ZyzSyncBelongFieldDict::getTypeId, ZyzSyncBelongFieldDict::getMaxTimeLimit));
        List<ServiceCodeResult> serviceCodeResultList = ApiWebService.getServiceType();
        List<ZyzSyncBelongFieldDict> dicts = serviceCodeResultList.stream().map(it -> {
            ZyzSyncBelongFieldDict dict = new ZyzSyncBelongFieldDict();
            BeanUtil.copyProperties(it, dict);
            dict.setMinTimeLimit(minLimitMap.get(dict.getTypeId()));
            dict.setMaxTimeLimit(maxLimitMap.get(dict.getTypeId()));
            return dict;
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(dicts)) {
            baseMapper.clearAll();
            saveBatch(dicts);
            dockingZSQ(dicts, DT_CREATE);
        }
    }

    @Override
    public List<ZyzSyncBelongFieldDict> cascade() {
        List<ZyzSyncBelongFieldDict> fields = lambdaQuery().list();
        if (CollectionUtil.isEmpty(fields)) {
            return Collections.emptyList();
        }
        List<ZyzSyncBelongFieldDict> topFields = fields.stream().filter(it -> "1".equals(it.getNodePath())).sorted(Comparator.comparing(ZyzSyncBelongFieldDict::getTypeId)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(topFields)) {
            return fields;
        }
        fields.removeAll(topFields);
        topFields.forEach(it -> {
            it.setChildren(fields.stream().filter(im -> im.getFatherId().equals(it.getTypeId())).sorted(Comparator.comparing(ZyzSyncBelongFieldDict::getTypeId)).collect(Collectors.toList()));
        });
        return topFields;
    }

    @Override
    public String getFieldName(String typeId) {
        ZyzSyncBelongFieldDict dict = getByTypeId(typeId);
        return dict == null ? "" : dict.getTypeName();
    }

    @Override
    public boolean updateLimitTime(Long id, Integer minTimeLimit, Integer maxTimeLimit) {
        ZyzSyncBelongFieldDict belongFieldDict = getById(id);
        belongFieldDict.setMinTimeLimit(minTimeLimit);
        belongFieldDict.setMaxTimeLimit(maxTimeLimit);
        return updateById(belongFieldDict);
    }

    public void dockingZSQ(List<ZyzSyncBelongFieldDict> list, String operation) {
        ZSQDockingBelongFieldRecord dockingRecord = new ZSQDockingBelongFieldRecord();
        dockingRecord.setDockingType(DOCKING_TYPE_PUSH);
        dockingRecord.setDockingOperation(operation);
        dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_PENDING);
        dockingRecord.setDockingTime(LocalDateTime.now());
        dockingRecord.setRetry(Boolean.FALSE);
        List<ActZyzSyncBelongFieldDto> zsqBelongFieldList = new ArrayList<>();
        list.forEach(it -> {
            ActZyzSyncBelongFieldDto dto = new ActZyzSyncBelongFieldDto();
            BeanUtils.copyProperties(it, dto);
            zsqBelongFieldList.add(dto);
        });
        Serializable userId = Oauth2Util.getUserId();
        String zwtUserId = userId == null ? null : ssoUserMappingService.getSSOUserIdByProviderAndZYZUserId(SsoCons.SsoProvider.ZWT.name(), (Long) userId);
        if (StringUtil.isEmpty(zwtUserId)) {
            dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
            dockingRecord.setDockingMsg("当前操作人" + (userId == null ? Strings.EMPTY : "{" + userId + "}") + "未获取政务通 userId ！");
            belongFieldRecordService.save(dockingRecord);
            BelongFieldDockingEvent event = BelongFieldDockingEvent.builder().dockingRecordId(dockingRecord.getId()).operation(operation).zyzUserId(Oauth2Util.getUserId()).belongFieldList(zsqBelongFieldList).build();
            dockingRecord.setDockingData(JSONObject.toJSONString(event));
            belongFieldRecordService.updateById(dockingRecord);
            return;
        }
        belongFieldRecordService.save(dockingRecord);
        BelongFieldDockingEvent event = BelongFieldDockingEvent.builder().dockingRecordId(dockingRecord.getId()).operation(operation).zwtUserId(zwtUserId).belongFieldList(zsqBelongFieldList).build();
        dockingRecord.setDockingData(JSONObject.toJSONString(event));
        belongFieldRecordService.updateById(dockingRecord);
        mqTemplate.send(TOPIC_BELONG_FIELD_DOCKING, MessageBuilder.withPayload(event).build());
    }
}
