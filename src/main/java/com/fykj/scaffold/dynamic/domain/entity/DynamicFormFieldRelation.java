package com.fykj.scaffold.dynamic.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 模板-字段关联-实体类
 *
 * @date 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dynamic_form_field_relation")
public class DynamicFormFieldRelation extends BaseEntity  {

	private static final long serialVersionUID = 190768337954620416L;

	/**
	 * 模块标识
	 */
	@TableField("module_code")
    @ApiModelProperty(value = "模块标识")
	private String moduleCode;

	/**
	 * 模板ID
	 */
	@TableField("form_id")
    @ApiModelProperty(value = "模板ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long formId;

	/**
	 * 字段ID
	 */
	@TableField("field_id")
    @ApiModelProperty(value = "字段ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long fieldId;

	/**
	 * 是否必填：1-是，0-否
	 */
	@TableField("require_flag")
    @ApiModelProperty(value = "是否必填：1-是，0-否")
	private Boolean requireFlag;

	/**
	 * 在模板中的显示顺序
	 */
	@TableField("field_order")
    @ApiModelProperty(value = "在模板中的显示顺序")
	private Integer fieldOrder;

}
