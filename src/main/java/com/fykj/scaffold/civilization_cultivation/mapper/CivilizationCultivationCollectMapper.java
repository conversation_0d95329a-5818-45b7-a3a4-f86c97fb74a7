package com.fykj.scaffold.civilization_cultivation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationCollect;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationCollectParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文明培育征集-管理
 * <p>
 * Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface CivilizationCultivationCollectMapper extends BaseMapper<CivilizationCultivationCollect> {

    /**
     * 我的征集提交分页查询
     * @param page
     * @param params
     * @return
     */
    IPage<CivilizationCultivationCollect> myCollectSubmit(IPage<CivilizationCultivationCollect> page, @Param("params") CivilizationCultivationCollectParams params);

    /**
     * 获取数据库最大编号
     *
     * @param year 年
     * @return 最大编码
     */
    String selectMaxCode(@Param("year") Integer year);

    /**
     * 管理员后台分页查询
     *
     * @param page   分页信息
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<CivilizationCultivationCollect> pagesForGoodPerson(IPage<CivilizationCultivationCollect> page, @Param("params") CivilizationCultivationCollectParams params);


    /**
     * pc/小程序展示分页查询（已核实）
     *
     * @param page   分页信息
     * @param params 查询参数
     * @return 分页列表
     */
    IPage<CivilizationCultivationCollect> pagesForPC(IPage<CivilizationCultivationCollect> page, @Param("params") CivilizationCultivationCollectParams params);

}
