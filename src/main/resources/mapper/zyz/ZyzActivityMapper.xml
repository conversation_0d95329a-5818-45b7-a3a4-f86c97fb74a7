<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityMapper">

    <select id="pagesForActivity" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivity">
        SELECT t.*,
        t.is_sync AS sync,
        t.is_open AS open,
        t.is_top AS top,
        t.sync_time AS syncTime,
        t.sync_remark AS syncRemark,
        t.is_org_self as orgSelf,
        t.audit_org_code as auditOrgCode,
        sp.name as auditOrgCodeName,
        zyz_activity_schedule_detail.`name` as scheduleDetailName,
        CASE WHEN t.team_publish = TRUE THEN FALSE
        ELSE IF(o.level = 2 OR o.level = 3, TRUE, FALSE) END AS dockingZSQ
        FROM `zyz_activity` t
        LEFT JOIN sys_org o on t.publish_org_code = o.`code`
        left join sys_org sp on t.audit_org_code=sp.code
        left join zyz_activity_schedule_act_plan
            on zyz_activity_schedule_act_plan.activity_id = t.id
        left join zyz_activity_schedule_detail
            on zyz_activity_schedule_act_plan.schedule_detail_id = zyz_activity_schedule_detail.id
        WHERE t.is_deleted = FALSE
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.address
            LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.publish_org_name LIKE CONCAT('%', CONCAT(#{params.key},
            '%')))
        </if>
        <if test="params.actType != null and params.actType != ''">
            AND t.act_type = #{params.actType}
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and t.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.top != null and params.top != ''">
            and t.is_top = #{params.top}
        </if>
        <if test="params.dockingType != null and params.dockingType != ''">
            AND t.docking_type = #{params.dockingType}
        </if>
        <if test="params.auditStatus != null">
            AND t.audit_status = #{params.auditStatus}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (t.belong_field_top = #{params.fieldId} OR t.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT (t.end_time &lt; #{params.startTime} OR t.start_time &gt; #{params.endTime} )
        </if>
        <if test="params.open != null">
            AND t.is_open = #{params.open}
        </if>
        <if test="params.recruitTarget != null and params.recruitTarget != ''">
            and t.recruit_targets LIKE CONCAT('%', CONCAT(#{params.recruitTarget}, '%'))
        </if>
        <if test="params.submitStartTime!= null and params.submitEndTime != null">
            AND NOT ( date(t.submit_time) &lt; #{params.submitStartTime} OR date(t.submit_time) &gt; #{params.submitEndTime} )
        </if>
        <if test="params.resourceId != null">
            AND t.res_id = #{params.resourceId}
        </if>
        <if test="params.scheduleDetailId != null">
            AND EXISTS (
                SELECT 1 FROM zyz_activity_schedule_act_plan p
                WHERE p.activity_id = t.id AND p.schedule_detail_id = #{params.scheduleDetailId} AND p.is_deleted = 0
            )
        </if>
        <if test="params.actMonths != null and params.actMonths.size > 0">
            AND
            <foreach item="item" collection="params.actMonths" separator="OR" open="(" close=")" index="">
                (t.submit_time &gt;= #{item.startTime} AND t.submit_time &lt;= #{item.endTime} )
            </foreach>
        </if>
        ORDER BY t.start_time DESC
    </select>
    <select id="pageForAudit" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivity">
        SELECT
        zr.id AS id,
        zr.name AS name,
        zr.recruit_targets AS recruitTargets,
        zr.docking_type as dockingType,
        zr.belong_field_name_top AS belongFieldNameTop,
        zr.belong_field_name_end AS belongFieldNameEnd,
        zr.publish_org_name AS publishOrgName,
        zr.contact_person AS contactPerson,
        zr.contact_phone AS contactPhone,
        zr.start_time AS startTime,
        zr.end_time AS endTime,
        zr.apply_start_time as applyStartTime,
        zr.apply_end_time as applyEndTime,
        zr.audit_status AS auditStatus,
        zr.submit_time AS submitTime,
        zr.audit_org_code as auditOrgCode,
        zr.docking_res_create AS dockingResCreate,
        zr.docking_req_create AS dockingReqCreate,
        zr.is_open AS open,
        sp.name as auditOrgCodeName
        FROM zyz_activity AS zr
        LEFT JOIN sys_org so ON zr.publish_org_code = so.code
        left join sys_org sp on zr.audit_org_code=sp.code
        WHERE zr.is_deleted = 0
        <if test="params.needAudit != null and params.needAudit == true ">
            AND zr.audit_org_code = #{params.myOrgCode}
            AND zr.audit_status = 'act_wait_audit'
        </if>
        <if test="params.key != null and params.key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%'))
            OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR zr.address LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND zr.publish_org_code = #{params.publishOrgCode}
        </if>

        <if test="params.actType != null and params.actType != ''">
            AND zr.act_type = #{params.actType}
        </if>
        <if test="params.dockingType != null and params.dockingType != ''">
            AND zr.docking_type = #{params.dockingType}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND zr.audit_status = #{params.auditStatus}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( zr.end_time &lt; #{params.startTime} OR zr.start_time &gt; #{params.endTime} )
        </if>
        <if test="params.submitStartTime != null and params.submitEndTime != null">
            AND ( zr.submit_time &gt;= #{params.submitStartTime} AND zr.submit_time &lt;= #{params.submitEndTime} )
        </if>
        ORDER BY zr.submit_time DESC
    </select>

    <select id="getActivityApplyAuditCount" resultType="com.fykj.scaffold.zyz.domain.dto.MiniWSActivityApplyCountDto">
        select * from
        (SELECT
        ifnull(count(*),0) as total
        FROM
        (
        SELECT
        1
        FROM
        zyz_activity_apply AS zry
        LEFT JOIN zyz_activity zr ON zr.id = zry.activity_id
        LEFT JOIN sys_org sp ON sp.CODE = zr.publish_org_code
        WHERE
        zr.is_deleted = 0 AND zry.is_deleted = 0
        <if test="myOrgCode != null and myOrgCode != ''">
            and zry.org_code =#{myOrgCode}
            and zry.team_publish = 0
        </if>
        <if test="myTeamId != null">
            AND zry.team_id = #{myTeamId}
            and zry.team_publish = 1
        </if>
        <if test="key != null and key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{key}, '%')) OR zr.contact_person LIKE CONCAT('%', CONCAT(#{key},
            '%'))
            OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{key}, '%'))
            OR zr.address LIKE CONCAT('%', CONCAT(#{key}, '%')))
        </if>
        LIMIT 1000
        )t)t1 inner join
        (SELECT
        ifnull(count(*),0) as wait_audit
        FROM
        (
        SELECT
        1
        FROM
        zyz_activity_apply AS zry
        LEFT JOIN zyz_activity zr ON zr.id = zry.activity_id
        LEFT JOIN sys_org sp ON sp.CODE = zr.publish_org_code
        WHERE
        zr.is_deleted = 0 AND zry.is_deleted = 0 and zry.audit_status = 'act_apply_wait_audit'
        <if test="myOrgCode != null and myOrgCode != ''">
            and zry.org_code =#{myOrgCode}
            and zry.team_publish = 0
        </if>
        <if test="myTeamId != null">
            AND zry.team_id = #{myTeamId}
            and zry.team_publish = 1
        </if>
        <if test="key != null and key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{key}, '%')) OR zr.contact_person LIKE CONCAT('%', CONCAT(#{key},
            '%'))
            OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{key}, '%'))
            OR zr.address LIKE CONCAT('%', CONCAT(#{key}, '%')))
        </if>
        LIMIT 1000
        )t)t2
    </select>

    <select id="getActWaitAuditNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_activity
        WHERE is_deleted = 0
          AND audit_status = #{status}
          AND audit_org_code = #{myOrgCode}
    </select>

    <select id="pageForActHall" resultType="com.fykj.scaffold.zyz.domain.dto.ActivityApiDto">
        SELECT
        a.id AS actId,
        a.picture AS picture,
        a.name AS name,
        a.address AS address,
        a.start_time AS startTime,
        a.end_time AS endTime,
        a.belong_field_name_top AS belongFieldNameTop,
        a.apply_end_time AS applyEndTime,
        a.publish_org_name as publisherName,
        a.recruit_targets as recruitTargets,
        a.is_open as open
        FROM zyz_activity a
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            LEFT JOIN sys_org o on a.publish_org_code = o.code
        </if>
        WHERE a.is_deleted = 0 AND a.audit_status = 'act_audit_success' AND a.web_show = 1
        <if test="params.key != null and params.key != ''">
            AND a.name LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
        </if>
        <if test="params.activityName != null and params.activityName != ''">
            AND a.name LIKE CONCAT('%', CONCAT(#{params.activityName}, '%'))
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( a.end_time &lt; #{params.startTime} OR a.start_time &gt; #{params.endTime} )
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (a.belong_field_top = #{params.fieldId} OR a.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.actType != null and params.actType != ''">
            AND a.act_type = #{params.actType}
        </if>
        <if test="params.recruitTargets != null and params.recruitTargets != ''">
            AND a.recruit_targets LIKE CONCAT('%', CONCAT(#{params.recruitTargets}, '%'))
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            <if test="params.publishOrgCode != 'top_dept'">
                AND o.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
            </if>
            <if test="params.publishOrgCode == 'top_dept'">
                AND o.code_prefix not LIKE CONCAT('top_dept,loufeng','%')
                AND o.code_prefix not LIKE CONCAT('top_dept,xietang','%')
                AND o.code_prefix not LIKE CONCAT('top_dept,weiting','%')
                AND o.code_prefix not LIKE CONCAT('top_dept,shengpu','%')
                AND o.code_prefix not LIKE CONCAT('top_dept,jinjihu','%')
            </if>
        </if>
        <if test="params.status != null and params.status != ''">
            <if test="params.status == 'act_recruit_finish'">
                and now() &gt;= a.apply_end_time
            </if>
            <if test="params.status == 'act_enlist'">
                AND now() &lt;= a.apply_end_time
            </if>
        </if>
        <if test="params.teamId != null and params.teamId != ''">
            AND a.team_id = #{params.teamId}
        </if>
        ORDER BY a.is_top DESC, a.start_time DESC
    </select>

    <select id="pageForMyApi" resultType="com.fykj.scaffold.zyz.domain.dto.MyActivityApiDto">
        select a.id as activityId,a.name as name,
        a.address as address,
        a.start_time as startTime,a.end_time as endTime,a.picture as picture
        from zyz_activity a inner join
        cms_user_op_log l on a.id=l.cms_cate_cont_id and
        l.source='cms_op_user_log_source_activity' and l.user_id=#{params.userId}
        and l.type='cms_user_op_type_collect' and l.is_deleted = 0
        WHERE a.is_deleted = 0 and web_show=1
        <if test="params.activityName != null and params.activityName != ''">
            AND a.name LIKE CONCAT('%', CONCAT(#{params.activityName}, '%'))
        </if>
        order by l.op_time desc
    </select>

    <select id="getResActivityPagesForMini" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivity">
        SELECT *
        from zyz_activity
        WHERE is_deleted = 0
          AND web_show = 1
          AND audit_status not in ('act_wait_audit', 'act_draft')
          AND res_id = #{resId}
        order by submit_time desc LIMIT 10
    </select>

    <select id="listForTeamActivity" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivity">
        SELECT *
        from zyz_activity
        WHERE is_deleted = 0
          AND team_id = #{teamId}
          AND web_show = 1
          AND audit_status not in ('act_wait_audit', 'act_draft')
          AND (project_id = #{projectId} OR project_id IS NULL)
        order by submit_time desc
    </select>

    <select id="getRecordsBetweenDateRange" resultType="java.time.LocalDateTime">
        SELECT
            time_period_start_time
        FROM
            zyz_activity_apply
        WHERE is_deleted = 0 AND audit_status = #{status} AND sign_time IS NOT NULL
        <if test="start != null">
            AND time_period_start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND time_period_start_time &lt;= #{end}
        </if>
        <if test="teamPublish != null">
            AND team_publish = #{teamPublish}
            <if test="teamPublish == true">
                AND team_id = #{publishTeam}
            </if>
            <if test="teamPublish == false">
                AND org_code = #{publishOrg}
            </if>
        </if>
        GROUP BY time_period_id
    </select>

    <select id="getTopBelongFieldDataSum"
            resultType="com.fykj.scaffold.zyz.domain.dto.dashboard_sum.TopBelongFieldDataSumDto">
        SELECT
        bf.type_name AS topBelongField,
        IFNULL(outer_nt.count, 0) AS dataNum,
        CONCAT(ROUND( IFNULL(outer_nt.count, 0) / outer_total.count * 100, 2 ), '%') AS percentage
        FROM zyz_sync_belong_field_dict bf
        LEFT JOIN (
        SELECT belong_field_top, SUM(count) AS count FROM (
        SELECT belong_field_top, COUNT(id) AS count FROM zyz_resource WHERE is_deleted = 0
        <if test="orgCode != null and orgCode != ''">
            AND publish_org_code = #{orgCode}
        </if>
        GROUP BY belong_field_top
        UNION ALL
        SELECT belong_field_top, COUNT(id) AS count FROM zyz_requirement WHERE is_deleted = 0
        <if test="orgCode != null and orgCode != ''">
            AND publish_org_code = #{orgCode}
        </if>
        GROUP BY belong_field_top
        UNION ALL
        SELECT belong_field_top, COUNT(id) AS count FROM zyz_activity WHERE is_deleted = 0
        <if test="orgCode != null and orgCode != ''">
            AND publish_org_code = #{orgCode}
        </if>
        GROUP BY belong_field_top
        ) AS inner_nt
        GROUP BY belong_field_top
        ) AS outer_nt ON bf.type_id = outer_nt.belong_field_top
        INNER JOIN (
        SELECT SUM(count) AS count FROM (
        SELECT COUNT(id) AS count FROM zyz_resource WHERE is_deleted = 0
        <if test="orgCode != null and orgCode != ''">
            AND publish_org_code = #{orgCode}
        </if>
        UNION ALL
        SELECT COUNT(id) AS count FROM zyz_requirement WHERE is_deleted = 0
        <if test="orgCode != null and orgCode != ''">
            AND publish_org_code = #{orgCode}
        </if>
        UNION ALL
        SELECT COUNT(id) AS count FROM zyz_activity WHERE is_deleted = 0
        <if test="orgCode != null and orgCode != ''">
            AND publish_org_code = #{orgCode}
        </if>
        ) AS inner_total
        ) AS outer_total
        WHERE bf.node_path = 1
        ORDER BY dataNum DESC, bf.type_id ASC
        LIMIT 10
    </select>

    <select id="getResActList" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivity">
        SELECT
        id AS id,
        res_id AS resId
        FROM zyz_activity
        WHERE is_deleted = 0
        AND web_show = 1
        AND audit_status not in ('act_wait_audit','act_draft')
        AND res_id IN (<foreach item="item" collection="resAppointIds" index="" separator=",">#{item}</foreach>)
    </select>

    <select id="getReqActList" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivity">
        SELECT
        id AS id,
        req_id AS reqId
        FROM zyz_activity
        WHERE is_deleted = 0
        AND web_show = 1
        AND audit_status not in ('act_wait_audit','act_draft')
        AND zyz_activity.req_id IN (<foreach item="item" collection="reqIds" index="" separator=",">#{item}</foreach>)
    </select>

    <select id="actFormReportByBelongField" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.ActivityFormDto">
        <foreach item="item" collection="params.bfIds" index="" separator="UNION ALL">
            SELECT
            CASE WHEN act_bf_top IS NULL THEN #{item} ELSE act_bf_top END AS actBfTop,
            act_bf_top_name AS actBfTopName,
            act_bf_end AS actBfEnd,
            act_bf_end_name AS actBfEndName,
            act_bf_top_name AS belongField,
            COUNT(DISTINCT act_id) AS actNumTotal,
            CASE WHEN SUM(tp_duration) IS NULL THEN 0 ELSE SUM(floor(tp_duration/0.5)*0.5) END AS actTimeTotal,
            SUM(IF(tp_join_volunteer_id IS NULL, 0, 1)) AS actJoinNumTotal,
            COUNT(DISTINCT tp_join_volunteer_id) AS actJoinNum,
            COUNT(DISTINCT act_publish_team_id) AS actJoinTeamNum
            FROM
            activity_form_report
            WHERE act_bf_top = #{item}
            <if test="params.startTime != null">
                AND act_time &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND act_time &lt;= #{params.endTime}
            </if>
            <if test="params.year != null">
                AND YEAR(act_time) = #{params.year}
            </if>
            <if test="params.month != null">
                AND date_format(act_time,'%Y-%m') = #{params.month}
            </if>
        </foreach>
    </select>

    <select id="pageOrListForActSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.ActSumFormDto">
        SELECT
        act_name AS actName,
        act_start_time AS actStartTime,
        act_end_time AS actEndTime,
        act_publisher_name AS actPublisherName,
        act_contact_person AS actContactPerson,
        act_contact_phone AS actContactPhone
        FROM activity_form_report
        WHERE act_bf_top = #{params.belongField}
        <if test="params.startTime != null">
            AND act_time &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null">
            AND act_time &lt;= #{params.endTime}
        </if>
        GROUP BY act_id
        ORDER BY act_time DESC
    </select>

    <select id="pageOrListForTeamSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.TeamSumFormDto">
        SELECT
        act_publish_team_id AS teamId,
        team_name AS teamName,
        team_volunteer_num AS teamVolunteerNum,
        team_org_name AS teamOrgName,
        team_this_year_lively_volunteer_num AS teamThisYearLivelyVolunteerNum,
        team_this_year_service_duration_total AS teamThisYearServiceDurationTotal
        FROM activity_form_report
        WHERE act_bf_top = #{params.belongField} AND act_publish_team_id IS NOT NULL
        <if test="params.startTime != null">
            AND act_time &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null">
            AND act_time &lt;= #{params.endTime}
        </if>
        GROUP BY act_publish_team_id
    </select>

    <select id="getTeamActNumForActFormReportTeamSum"
            resultType="com.fykj.scaffold.zyz.domain.dto.report_form.TeamSumFormDto">
        SELECT
        act_publish_team_id AS teamId,
        COUNT(DISTINCT act_id) AS teamThisYearActNum
        FROM activity_form_report
        WHERE act_publish_team_id IN
        <foreach item="item" collection="teamIds" index="" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND DATE_FORMAT(act_time, '%Y') = #{year}
        GROUP BY act_publish_team_id
    </select>

    <select id="sumNumByOrg" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto">
        SELECT
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            publish_org_code AS orgCode,
        </if>
        COUNT(id) AS sumNum
        FROM zyz_activity
        WHERE is_deleted = 0 AND audit_status = 'act_audit_success'
        <if test="params.start != null">
            AND submit_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND submit_time &lt;= #{params.end}
        </if>
        <if test="params.ignoreOrgCodes != null and params.ignoreOrgCodes.length > 0">
            <if test="params.ignoreOrgCodes.length == 1">
                AND publish_org_code != #{params.ignoreOrgCodes[0]}
            </if>
            <if test="params.ignoreOrgCodes.length > 1">
                AND publish_org_code NOT IN
                <foreach item="item" collection="params.ignoreOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND publish_org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND publish_org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            GROUP BY publish_org_code
        </if>
    </select>

    <select id="sumBelongFieldRRANumPercent" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.NumPercentSumDto">
        SELECT
        bf.type_name AS name,
        (IFNULL(act.actCount, 0) + IFNULL(req.reqCount, 0) + IFNULL(res.resCount, 0)) AS num
        FROM (
        SELECT type_id, type_name
        FROM zyz_sync_belong_field_dict
        WHERE is_deleted = 0 AND node_path = 1
        ) AS bf
        LEFT JOIN (
        SELECT belong_field_top, COUNT(id) AS actCount
        FROM zyz_activity
        WHERE is_deleted = 0 AND audit_status = 'act_audit_success'
        <if test="params.start != null">
            AND submit_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND submit_time &lt;= #{params.end}
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND publish_org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND publish_org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        GROUP BY belong_field_top
        ) AS act ON bf.type_id = act.belong_field_top
        LEFT JOIN (
        SELECT belong_field_top, COUNT(id) AS reqCount
        FROM zyz_requirement
        WHERE is_deleted = 0 AND (status = 'ras_wait_docking' OR status = 'ras_docking_success')
        <if test="params.start != null">
            AND create_date &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND create_date &lt;= #{params.end}
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND publish_org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND publish_org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        GROUP BY belong_field_top
        ) AS req ON bf.type_id = req.belong_field_top
        LEFT JOIN (
        SELECT belong_field_top, COUNT(id) AS resCount
        FROM zyz_resource
        WHERE is_deleted = 0 AND status = 'res_audit_success'
        <if test="params.start != null">
            AND publish_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND publish_time &lt;= #{params.end}
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND publish_org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND publish_org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        GROUP BY belong_field_top
        ) AS res ON bf.type_id = res.belong_field_top
        ORDER BY num DESC
    </select>

    <select id="getScreenDataList" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.ReqResActTeamDataDto">
        SELECT
        name,
        belong_field_name_top AS belongField,
        submit_time AS publishTime
        FROM zyz_activity
        WHERE is_deleted = 0 AND audit_status = 'act_audit_success'
        <if test="params.start != null">
            AND submit_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND submit_time &lt;= #{params.end}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND publish_org_code = #{params.orgCode}
        </if>
        ORDER BY submit_time DESC
        LIMIT 100
    </select>

    <select id="getReqDockingNum" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT req_id)
        FROM zyz_activity
        WHERE is_deleted = 0 AND audit_status = 'act_audit_success' AND req_id IS NOT NULL
        <if test="params.start != null">
            AND submit_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND submit_time &lt;= #{params.end}
        </if>
    </select>

    <select id="getResUseNum" resultType="java.lang.Integer">
        SELECT
        COUNT(res_id)
        FROM zyz_activity
        WHERE is_deleted = 0 AND audit_status = 'act_audit_success' AND res_id IS NOT NULL
        <if test="params.start != null">
            AND submit_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND submit_time &lt;= #{params.end}
        </if>
    </select>

    <select id="getIngActPositionList" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.ActPositionInfoDto">
        SELECT name AS actName,
               longitude,
               latitude
        FROM zyz_activity
        WHERE is_deleted = 0
          AND audit_status = 'act_audit_success'
          AND start_time &lt;= NOW()
          AND end_time &gt;= NOW()
    </select>

    <select id="getPossibleNeedUpdateData"
            resultType="com.fykj.scaffold.zyz.domain.dto.report_form.ActivityFormReportDto">
        SELECT it.*,
               nt.name                                             AS team_name,
               IFNULL(nt.team_volunteer_num, 0),
               nt.team_org_code,
               nt.team_org_name,
               IFNULL(nt.team_this_year_lively_volunteer_num, 0)   AS team_this_year_lively_volunteer_num,
               IFNULL(nt.team_this_year_service_duration_total, 0) AS team_this_year_service_duration_total,
               aa.volunteer_id                                     AS tp_join_volunteer_id
        FROM (
                 SELECT a.id                                                                            AS act_id,
                        a.submit_time                                                                   AS act_time,
                        a.name                                                                          AS act_name,
                        a.start_time                                                                    AS act_start_time,
                        a.end_time                                                                      AS act_end_time,
                        a.belong_field_top                                                              AS act_bf_top,
                        a.belong_field_name_top                                                         AS act_bf_top_name,
                        a.belong_field_end                                                              AS act_bf_end,
                        a.belong_field_name_end                                                         AS act_bf_end_name,
                        IFNULL(a.team_publish, 0)                                                       AS act_team_publish,
                        a.publish_org_code                                                              AS act_publish_org_code,
                        a.team_id                                                                       AS act_publish_team_id,
                        a.publish_org_name                                                              AS act_publisher_name,
                        a.contact_person                                                                AS act_contact_person,
                        a.contact_phone                                                                 AS act_contact_phone,
                        atp.id                                                                          AS tp_id,
                        atp.start_time                                                                  AS tp_start_time,
                        atp.end_time                                                                    AS tp_end_time,
                        IFNULL(ROUND(TIMESTAMPDIFF(SECOND, atp.start_time, atp.end_time) / 3600, 2), 0) AS tp_duration
                 FROM zyz_activity a
                          LEFT JOIN zyz_activity_time_period atp ON a.id = atp.activity_id AND atp.is_deleted = 0
                 WHERE a.audit_status = 'act_audit_success'
                   AND atp.start_time >= #{firstTimeOfLastMonth}
             ) AS it
                 LEFT JOIN (
            SELECT t.id,
                   t.name,
                   t.org_code                                                             AS team_org_code,
                   o.name                                                                 AS team_org_name,
                   SUM(IF(v.id IS NULL, 0, 1))                                            AS team_volunteer_num,
                   SUM(IF(v.is_lively IS NULL OR v.is_lively = 0, 0, 1))                  AS team_this_year_lively_volunteer_num,
                   SUM(IF(v.service_long_this_year IS NULL, 0, v.service_long_this_year)) AS team_this_year_service_duration_total
            FROM zyz_team t
                     LEFT JOIN sys_org o ON t.org_code = o.code
                     LEFT JOIN zyz_volunteer_team vt ON t.id = vt.team_id AND vt.is_deleted = 0
                     LEFT JOIN zyz_volunteer v ON vt.volunteer_id = v.id AND v.is_deleted = 0 AND v.write_off = 0
            GROUP BY t.id
        ) nt ON it.act_publish_team_id = nt.id
                 LEFT JOIN zyz_activity_apply aa ON it.tp_id = aa.time_period_id AND aa.is_deleted = 0 AND
                                                    aa.audit_status = 'act_apply_audit_success'
        ORDER BY it.act_bf_end
    </select>

    <select id="getExistsActFormReportData"
            resultType="com.fykj.scaffold.zyz.domain.dto.report_form.ActivityFormReportDto">
        SELECT *
        FROM activity_form_report
        WHERE tp_id IN (
            SELECT atp.id
            FROM zyz_activity a
                     LEFT JOIN zyz_activity_time_period atp ON a.id = atp.activity_id AND atp.is_deleted = 0
            WHERE a.audit_status = 'act_audit_success'
              AND atp.start_time >= #{firstTimeOfLastMonth}
        )
    </select>

    <insert id="insertBatchActFormReport"
            parameterType="com.fykj.scaffold.zyz.domain.dto.report_form.ActivityFormReportDto">
        INSERT INTO activity_form_report (
        act_id,
        act_time,
        act_name,
        act_start_time,
        act_end_time,
        act_bf_top,
        act_bf_top_name,
        act_bf_end,
        act_bf_end_name,
        act_team_publish,
        act_publish_org_code,
        act_publish_team_id,
        act_publisher_name,
        act_contact_person,
        act_contact_phone,
        team_name,
        team_volunteer_num,
        team_org_code,
        team_org_name,
        team_this_year_lively_volunteer_num,
        team_this_year_service_duration_total,
        tp_id,
        tp_start_time,
        tp_end_time,
        tp_duration,
        tp_join_volunteer_id
        )
        values
        <foreach collection="needInsert" separator="," item="data">
            (
            #{data.actId}, #{data.actTime}, #{data.actName}, #{data.actStartTime}, #{data.actEndTime},
            #{data.actBfTop}, #{data.actBfTopName}, #{data.actBfEnd}, #{data.actBfEndName}, #{data.actTeamPublish},
            #{data.actPublishOrgCode}, #{data.actPublishTeamId}, #{data.actPublisherName}, #{data.actContactPerson},
            #{data.actContactPhone},
            #{data.teamName}, #{data.teamVolunteerNum}, #{data.teamOrgCode}, #{data.teamOrgName},
            #{data.teamThisYearLivelyVolunteerNum},
            #{data.teamThisYearServiceDurationTotal}, #{data.tpId}, #{data.tpStartTime}, #{data.tpEndTime},
            #{data.tpDuration},
            #{data.tpJoinVolunteerId}
            )
        </foreach>
    </insert>

    <delete id="deleteBatchActFormReport"
            parameterType="com.fykj.scaffold.zyz.domain.dto.report_form.ActivityFormReportDto">
        <foreach collection="needUpdate" item="data" separator=";">
            DELETE FROM activity_form_report
            WHERE tp_id = #{data.tpId}
            <if test="data.tpJoinVolunteerId != null">
                AND tp_join_volunteer_id = #{data.tpJoinVolunteerId}
            </if>
            <if test="data.tpJoinVolunteerId == null">
                AND tp_join_volunteer_id IS NULL
            </if>
        </foreach>
    </delete>

    <delete id="emptyActFormReport">
        DELETE
        FROM activity_form_report
    </delete>

    <insert id="initActFormReport">
        INSERT INTO activity_form_report (act_id,
                                          act_time,
                                          act_name,
                                          act_start_time,
                                          act_end_time,
                                          act_bf_top,
                                          act_bf_top_name,
                                          act_bf_end,
                                          act_bf_end_name,
                                          act_team_publish,
                                          act_publish_org_code,
                                          act_publish_team_id,
                                          act_publisher_name,
                                          act_contact_person,
                                          act_contact_phone,
                                          team_name,
                                          team_volunteer_num,
                                          team_org_code,
                                          team_org_name,
                                          team_this_year_lively_volunteer_num,
                                          team_this_year_service_duration_total,
                                          tp_id,
                                          tp_start_time,
                                          tp_end_time,
                                          tp_duration,
                                          tp_join_volunteer_id)
        SELECT a.id                                                                            AS act_id,
               a.submit_time                                                                   AS act_time,
               a.name                                                                          AS act_name,
               a.start_time                                                                    AS act_start_time,
               a.end_time                                                                      AS act_end_time,
               a.belong_field_top                                                              AS act_bf_top,
               a.belong_field_name_top                                                         AS act_bf_top_name,
               a.belong_field_end                                                              AS act_bf_end,
               a.belong_field_name_end                                                         AS act_bf_end_name,
               IFNULL(a.team_publish, 0)                                                       AS act_team_publish,
               a.publish_org_code                                                              AS act_publish_org_code,
               a.team_id                                                                       AS act_publish_team_id,
               a.publish_org_name                                                              AS act_publisher_name,
               a.contact_person                                                                AS act_contact_person,
               a.contact_phone                                                                 AS act_contact_phone,
               nt.name                                                                         AS team_name,
               IFNULL(nt.team_volunteer_num, 0),
               nt.team_org_code,
               nt.team_org_name,
               IFNULL(nt.team_this_year_lively_volunteer_num, 0)                               AS team_this_year_lively_volunteer_num,
               IFNULL(nt.team_this_year_service_duration_total, 0)                             AS team_this_year_service_duration_total,
               atp.id                                                                          AS tp_id,
               atp.start_time                                                                  AS tp_start_time,
               atp.end_time                                                                    AS tp_end_time,
               IFNULL(ROUND(TIMESTAMPDIFF(SECOND, atp.start_time, atp.end_time) / 3600, 2), 0) AS tp_duration,
               aa.volunteer_id                                                                 AS tp_join_volunteer_id
        FROM zyz_activity a
                 LEFT JOIN (
            SELECT t.id,
                   t.name,
                   t.org_code                                                             AS team_org_code,
                   o.name                                                                 AS team_org_name,
                   SUM(IF(v.id IS NULL, 0, 1))                                            AS team_volunteer_num,
                   SUM(IF(v.is_lively IS NULL OR v.is_lively = 0, 0, 1))                  AS team_this_year_lively_volunteer_num,
                   SUM(IF(v.service_long_this_year IS NULL, 0, v.service_long_this_year)) AS team_this_year_service_duration_total
            FROM zyz_team t
                     LEFT JOIN sys_org o ON t.org_code = o.code
                     LEFT JOIN zyz_volunteer_team vt ON t.id = vt.team_id AND vt.is_deleted = 0
                     LEFT JOIN zyz_volunteer v ON vt.volunteer_id = v.id AND v.is_deleted = 0
            GROUP BY t.id
        ) nt ON a.team_id = nt.id
                 LEFT JOIN zyz_activity_time_period atp ON a.id = atp.activity_id AND atp.is_deleted = 0
                 LEFT JOIN zyz_activity_apply aa ON atp.id = aa.time_period_id AND aa.is_deleted = 0 AND
                                                    aa.audit_status = 'act_apply_audit_success'
        WHERE a.audit_status = 'act_audit_success'
        ORDER BY a.belong_field_end
    </insert>

    <select id="getOrgActInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            IFNULL(nt.actNum, 0) AS actNum,
            IFNULL(nt.applyNum, 0) AS actJoinVolunteerNum,
            IFNULL(nt.serviceTime, 0) AS totalServiceTime
        FROM sys_org o
        LEFT JOIN (
            SELECT
                CASE WHEN #{params.parentOrgCode} = 'top_dept' THEN SUBSTRING_INDEX(org_code, '_', 1) ELSE org_code END AS orgCode,
                COUNT(DISTINCT time_period_id) AS actNum,
                COUNT(id) AS applyNum,
                SUM(FLOOR(IFNULL(ROUND(TIMESTAMPDIFF(SECOND, IF(sign_time > time_period_start_time, sign_time, time_period_start_time), time_period_end_time) / 3600, 2), 0)/0.5)*0.5) AS serviceTime
            FROM zyz_activity_apply
            WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                AND org_code != #{params.parentOrgCode} AND SUBSTRING_INDEX(org_code, '_', 1) = #{params.parentOrgCode}
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                AND SUBSTRING_INDEX(org_code, '_', 1) IN ('jinjihu', 'loufeng', 'xietang', 'shengpu', 'weiting')
            </if>
            <if test="params.start != null">
                AND time_period_start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND time_period_start_time &lt;= #{params.end}
            </if>
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                GROUP BY org_code
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                GROUP BY SUBSTRING_INDEX(org_code, '_', 1)
            </if>
        ) nt
        ON o.code = nt.orgCode
    <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
        WHERE o.code IN
        <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </if>
        ORDER BY o.sequence
    </select>

    <select id="getOrgActInfoSumPublic" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
        o.code AS orgCode,
        o.name AS orgName,
        IFNULL(nt.actNum, 0) AS actNumPublic,
        IFNULL(nt.applyNum, 0) AS actJoinVolunteerNumPublic
        FROM sys_org o
        LEFT JOIN (
        SELECT
        CASE WHEN #{params.parentOrgCode} = 'top_dept' THEN SUBSTRING_INDEX(a.publish_org_code, '_', 1) ELSE a.publish_org_code END AS orgCode,
        COUNT(DISTINCT time_period_id) AS actNum,
        COUNT(p.id) AS applyNum
        FROM zyz_activity_apply_public p join zyz_activity a ON a.id=p.activity_id
        WHERE p.is_deleted = 0 AND p.status = 1
        <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
            AND a.publish_org_code != #{params.parentOrgCode} AND SUBSTRING_INDEX(a.publish_org_code, '_', 1) = #{params.parentOrgCode}
        </if>
        <if test="params.parentOrgCode == 'top_dept'">
            AND SUBSTRING_INDEX(a.publish_org_code, '_', 1) IN ('jinjihu', 'loufeng', 'xietang', 'shengpu', 'weiting')
        </if>
        <if test="params.start != null">
            AND p.start_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND p.start_time &lt;= #{params.end}
        </if>
        <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
            GROUP BY a.publish_org_code
        </if>
        <if test="params.parentOrgCode == 'top_dept'">
            GROUP BY SUBSTRING_INDEX(a.publish_org_code, '_', 1)
        </if>
        ) nt
        ON o.code = nt.orgCode
        <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
            WHERE o.code IN
            <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY o.sequence
    </select>

    <select id="getOrgActiveVolunteerSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            IFNULL(nt.activeNum, 0) AS activeNum
        FROM sys_org o
        LEFT JOIN (
            SELECT
                CASE WHEN #{params.parentOrgCode} = 'top_dept' THEN SUBSTRING_INDEX(org_code, '_', 1) ELSE org_code END AS orgCode,
                COUNT(DISTINCT volunteer_id) AS activeNum
            FROM zyz_activity_apply
            WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                AND org_code != #{params.parentOrgCode} AND SUBSTRING_INDEX(org_code, '_', 1) = #{params.parentOrgCode}
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                AND SUBSTRING_INDEX(org_code, '_', 1) IN ('jinjihu', 'loufeng', 'xietang', 'shengpu', 'weiting')
            </if>
            <if test="params.start != null">
                AND time_period_start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND time_period_start_time &lt;= #{params.end}
            </if>
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                GROUP BY org_code
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                GROUP BY SUBSTRING_INDEX(org_code, '_', 1)
            </if>
        ) nt ON o.code = nt.orgCode
    <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
        WHERE o.code IN
        <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </if>
        ORDER BY o.sequence
    </select>

    <select id="getOrgSelfActInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            COUNT(DISTINCT time_period_id) AS actNum,
            COUNT(id) AS actJoinVolunteerNum,
            COUNT(DISTINCT volunteer_id) AS activeNum,
            IFNULL(SUM(FLOOR(IFNULL(ROUND(TIMESTAMPDIFF(SECOND, IF(sign_time > time_period_start_time, sign_time,
            time_period_start_time), time_period_end_time) / 3600, 2), 0)/0.5)*0.5), 0) AS totalServiceTime
        FROM zyz_activity_apply
        WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL AND org_code = #{orgCoe}
        <if test="start != null">
            AND time_period_start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND time_period_start_time &lt;= #{end}
        </if>
    </select>
    <select id="getOrgSelfActInfoSumPublic" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
        COUNT(DISTINCT p.time_period_id) AS actNum,
        COUNT(p.id) AS actJoinVolunteerNum
        FROM zyz_activity_apply_public p join zyz_activity a ON a.id=p.activity_id
        WHERE p.is_deleted = 0 AND p.status = 1 AND  a.publish_org_code = #{orgCoe}
        <if test="start != null">
            AND p.start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND p.start_time &lt;= #{end}
        </if>
    </select>
    <select id="getOrgSummaryActiveVolunteerNum" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT aa.volunteer_id)
        FROM zyz_activity_apply aa
        LEFT JOIN sys_org o ON aa.org_code = o.code
        WHERE aa.is_deleted = 0 AND aa.audit_status = 'act_apply_audit_success' AND aa.sign_time IS NOT NULL AND o.code_prefix LIKE CONCAT(#{codePrefix}, '%')
        <if test="start != null">
            AND aa.time_period_start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND aa.time_period_start_time &lt;= #{end}
        </if>
    </select>

    <select id="getOrgActDockingInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            SUM(IFNULL(nt.dockingResNum, 0)) AS resourceDockingNum,
            SUM(IFNULL(nt.dockingReqNum, 0)) AS requirementDockingNum
        FROM sys_org o
        LEFT JOIN (
            SELECT
                o.code_prefix,
                COUNT(DISTINCT a.res_id) AS dockingResNum,
                COUNT(DISTINCT a.req_id) AS dockingReqNum
            FROM zyz_activity_time_period atp
            INNER JOIN zyz_activity a ON atp.activity_id = a.id AND a.is_deleted = 0 AND a.audit_status = 'act_audit_success'
            LEFT JOIN sys_org o ON a.publish_org_code = o.code
            WHERE atp.is_deleted = 0
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                AND a.publish_org_code LIKE CONCAT(#{params.parentOrgCode}, '%')
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                AND a.publish_org_code != 'top_dept'
            </if>
            <if test="params.start != null">
                AND atp.start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND atp.start_time &lt;= #{params.end}
            </if>
            GROUP BY o.code_prefix
        ) nt ON nt.code_prefix LIKE CONCAT(o.code_prefix, '%')
        <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
            WHERE o.code IN
            <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY o.code
        ORDER BY o.sequence
    </select>

    <select id="getOrgSelfActDockingInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            COUNT(DISTINCT a.res_id) AS resourceDockingNum,
            COUNT(DISTINCT a.req_id) AS requirementDockingNum
        FROM zyz_activity_time_period atp
        INNER JOIN zyz_activity a ON atp.activity_id = a.id AND a.is_deleted = 0 AND a.audit_status = 'act_audit_success'
        WHERE atp.is_deleted = 0 AND a.publish_org_code = #{orgCoe}
        <if test="start != null">
            AND atp.start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND atp.start_time &lt;= #{end}
        </if>
    </select>

    <select id="getOtherOrgActDockingInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            COUNT(DISTINCT a.res_id) AS resourceDockingNum,
            COUNT(DISTINCT a.req_id) AS requirementDockingNum
        FROM zyz_activity_time_period atp
        INNER JOIN zyz_activity a ON atp.activity_id = a.id AND a.is_deleted = 0 AND a.audit_status = 'act_audit_success'
        WHERE atp.is_deleted = 0
        AND a.publish_org_code NOT LIKE 'jinjihu%'
        AND a.publish_org_code NOT LIKE 'loufeng%'
        AND a.publish_org_code NOT LIKE 'xietang%'
        AND a.publish_org_code NOT LIKE 'shengpu%'
        AND a.publish_org_code NOT LIKE 'weiting%'
        AND a.publish_org_code != 'top_dept'
        <if test="start != null">
            AND atp.start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND atp.start_time &lt;= #{end}
        </if>
    </select>

    <select id="getTeamActInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            t.id AS teamId,
            t.name AS teamName,
            IFNULL(nt.actNum, 0) AS actNum,
            IFNULL(nt.actJoinVolunteerNum, 0) AS actJoinVolunteerNum,
            IFNULL(nt.activeNum, 0) AS activeNum,
            IFNULL(nt.serviceTime, 0) AS totalServiceTime
        FROM zyz_team t
        LEFT JOIN (
            SELECT
                team_id,
                COUNT(DISTINCT time_period_id) AS actNum,
                COUNT(id) AS actJoinVolunteerNum,
                COUNT(DISTINCT volunteer_id) AS activeNum,
                SUM(FLOOR(IFNULL(ROUND(TIMESTAMPDIFF(SECOND, IF(sign_time > time_period_start_time, sign_time, time_period_start_time), time_period_end_time) / 3600, 2), 0)/0.5)*0.5) AS serviceTime
            FROM zyz_activity_apply
            WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL AND team_publish = 1
            <if test="params.start != null">
                AND time_period_start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND time_period_start_time &lt;= #{params.end}
            </if>
            GROUP BY team_id
        ) nt ON t.id = nt.team_id
        WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.parentOrgCode != 'top_dept'">
                <if test=" params.all != null and  params.all==true">
                    AND t.org_code LIKE CONCAT('%', CONCAT(#{params.parentOrgCode}, '%'))
                </if>
                <if test=" params.all == null or  params.all==false">
                AND t.org_code = #{params.parentOrgCode}
                </if>
            </if>
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
        </if>
        ORDER BY actNum DESC
    </select>
    <select id="getTeamActInfoSumPublic" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
        t.id AS teamId,
        t.name AS teamName,
        IFNULL(nt.actNum, 0) AS actNumPublic,
        IFNULL(nt.actJoinVolunteerNum, 0) AS actJoinVolunteerNumPublic
        FROM zyz_team t
        LEFT JOIN (
        SELECT
        team_id,
        COUNT(DISTINCT time_period_id) AS actNum,
        COUNT(p.id) AS actJoinVolunteerNum
        FROM zyz_activity_apply_public p join zyz_activity a ON a.id=p.activity_id
        WHERE p.is_deleted = 0 AND p.status = 1
        <if test="params.start != null">
            AND p.start_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND p.start_time &lt;= #{params.end}
        </if>
        GROUP BY team_id
        ) nt ON t.id = nt.team_id
        WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.parentOrgCode != 'top_dept'">
                <if test=" params.all != null and  params.all==true">
                    AND t.org_code LIKE CONCAT('%', CONCAT(#{params.parentOrgCode}, '%'))
                </if>
                <if test=" params.all == null or  params.all==false">
                    AND t.org_code = #{params.parentOrgCode}
                </if>
            </if>
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
        </if>
        ORDER BY actNumPublic DESC
    </select>
    <select id="getTeamActDockingInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            t.id AS teamId,
            t.name AS teamName,
            IFNULL(nt.dockingResNum, 0) AS resourceDockingNum,
            IFNULL(nt.dockingReqNum, 0) AS requirementDockingNum
        FROM zyz_team t
        LEFT JOIN (
            SELECT
                a.team_id,
                COUNT(DISTINCT a.res_id) AS dockingResNum,
                COUNT(DISTINCT a.req_id) AS dockingReqNum
            FROM zyz_activity_time_period atp
            INNER JOIN zyz_activity a ON atp.activity_id = a.id AND a.is_deleted = 0 AND a.audit_status = 'act_audit_success' AND a.team_publish = 1
            WHERE atp.is_deleted = 0
            <if test="params.start != null">
                AND atp.start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND atp.start_time &lt;= #{params.end}
            </if>
            GROUP BY a.team_id
        ) nt ON t.id = nt.team_id
        WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
            <if test="params.teamIdList != null and params.teamIdList.size > 0">
                AND t.id IN
                <foreach item="item" collection="params.teamIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>

    <select id="getOtherOrgActSumData" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            COUNT(DISTINCT time_period_id) AS actNum,
            COUNT(id) AS actJoinVolunteerNum,
            COUNT(DISTINCT volunteer_id) AS activeNum,
            IFNULL(FLOOR(SUM(IFNULL(ROUND(TIMESTAMPDIFF(SECOND, IF(sign_time > time_period_start_time, sign_time, time_period_start_time), time_period_end_time) / 3600, 2), 0))/0.5)*0.5, 0) AS totalServiceTime
        FROM zyz_activity_apply
        WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
          AND org_code NOT LIKE 'jinjihu%'
          AND org_code NOT LIKE 'loufeng%'
          AND org_code NOT LIKE 'xietang%'
          AND org_code NOT LIKE 'shengpu%'
          AND org_code NOT LIKE 'weiting%'
          AND org_code != 'top_dept'
        <if test="start != null">
            AND time_period_start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND time_period_start_time &lt;= #{end}
        </if>
    </select>
    <select id="getOtherOrgActSumDataPublic" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
        COUNT(DISTINCT time_period_id) AS actNum,
        COUNT(p.id) AS actJoinVolunteerNum
        FROM zyz_activity_apply_public p join zyz_activity a ON a.id=p.activity_id
        WHERE p.is_deleted = 0 AND p.status=1
        AND a.publish_org_code NOT LIKE 'jinjihu%'
        AND a.publish_org_code NOT LIKE 'loufeng%'
        AND a.publish_org_code NOT LIKE 'xietang%'
        AND a.publish_org_code NOT LIKE 'shengpu%'
        AND a.publish_org_code NOT LIKE 'weiting%'
        AND a.publish_org_code != 'top_dept'
        <if test="start != null">
            AND p.start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND p.start_time &lt;= #{end}
        </if>
    </select>
    <select id="getBelongFieldSumData" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.BelongFieldFormDto">
        SELECT
            a.belong_field_name_top AS belongFieldName,
            COUNT(DISTINCT a.team_id) AS joinTeamNum,
            COUNT(aa.id) AS joinVolunteerNum,
            COUNT(DISTINCT aa.time_period_id) AS actNum,
            SUM(FLOOR(IFNULL(ROUND(TIMESTAMPDIFF(SECOND, IF(aa.sign_time > aa.time_period_start_time, aa.sign_time, aa.time_period_start_time), aa.time_period_end_time) / 3600, 2), 0)/0.5)*0.5) AS totalServiceTime,
            COUNT(DISTINCT aa.volunteer_id) AS distinctJoinVolunteerNum
        FROM zyz_activity a
        INNER JOIN zyz_activity_apply aa ON aa.activity_id = a.id AND aa.sign_time IS NOT NULL AND aa.is_deleted = 0
        WHERE a.is_deleted = 0 AND a.audit_status = 'act_audit_success'
        <if test="params.queryType == 'team' and params.teamIdList != null and  params.teamIdList.size > 0">
            AND a.team_id IN
            <foreach item="item" collection="params.teamIdList" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.queryType == 'org'">
            <if test="params.orgCode != 'top_dept'">
                AND a.publish_org_code LIKE CONCAT(#{params.orgCode}, '%')
            </if>
        </if>
        <if test="params.start != null">
            AND aa.time_period_start_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND aa.time_period_start_time &lt;= #{params.end}
        </if>
        <if test="params.actName != null and params.actName != ''">
            AND a.name LIKE CONCAT('%', CONCAT(#{params.actName}, '%'))
        </if>
        GROUP BY a.belong_field_top
        ORDER BY a.belong_field_top
    </select>

    <select id="getBfSummaryDistinctJoinVolunteerNum" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT aa.volunteer_id)
        FROM zyz_activity a
        LEFT JOIN zyz_activity_apply aa ON aa.activity_id = a.id AND aa.sign_time IS NOT NULL AND aa.is_deleted = 0
        WHERE a.is_deleted = 0 AND a.audit_status = 'act_audit_success'
        <if test="params.queryType == 'team' and params.teamIdList != null and  params.teamIdList.size > 0">
            AND a.team_id IN
            <foreach item="item" collection="params.teamIdList" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.queryType == 'org'">
            <if test="params.orgCode != 'top_dept'">
                AND a.publish_org_code LIKE CONCAT(#{params.orgCode}, '%')
            </if>
        </if>
        <if test="params.start != null">
            AND aa.time_period_start_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND aa.time_period_start_time &lt;= #{params.end}
        </if>
        <if test="params.actName != null and params.actName != ''">
            AND a.name LIKE CONCAT('%', CONCAT(#{params.actName}, '%'))
        </if>
    </select>

    <select id="getAgeRangeSumData" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.AgeRangeFormDto">
        SELECT
            ad.name AS ageRange,
            COUNT(nt2.id) AS registerNum,
            SUM(IF(nt2.serviceTime IS NULL, 0, 1)) AS activeNum,
            SUM(IFNULL(nt2.serviceTime, 0)) AS totalServiceTime
        FROM dimension_age ad
        LEFT JOIN (
            SELECT
                v.id,
                SUBSTR(NOW(), 1, 4) - SUBSTR(v.certificate_id, 7, 4) - (DATE_FORMAT(NOW(), '%m%d') - SUBSTR(v.certificate_id, 11, 4) &lt; 0 ) AS age,
                nt.serviceTime
            FROM zyz_volunteer v
            LEFT JOIN (
                SELECT
                    volunteer_id,
                    IFNULL(SUM(FLOOR(IFNULL(ROUND(TIMESTAMPDIFF(SECOND, IF(sign_time &gt; time_period_start_time, sign_time, time_period_start_time), time_period_end_time) / 3600, 2), 0)/0.5)*0.5), 0) AS serviceTime
                FROM zyz_activity_apply
                WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
                <if test="params.start != null">
                    AND time_period_start_time &gt;= #{params.start}
                </if>
                <if test="params.end != null">
                    AND time_period_start_time &lt;= #{params.end}
                </if>
                GROUP BY volunteer_id
            ) nt ON v.id = nt.volunteer_id
        <if test="params.queryType == 'team'">
            LEFT JOIN zyz_volunteer_team vt ON v.id = vt.volunteer_id
        </if>
            WHERE v.is_deleted = 0
                AND v.certificate_id IS NOT NULL
                AND LENGTH( v.certificate_id ) = 18
            <if test="params.queryType == 'org'">
              <if test="params.orgCode != 'top_dept'">
                AND v.org_code LIKE CONCAT(#{params.orgCode}, '%')
              </if>
                AND v.founded &lt;= NOW()
            </if>
            <if test="params.queryType == 'team'">
              <if test="params.teamIdList != null and params.teamIdList.size > 0">
                AND vt.team_id IN
                <foreach item="item" collection="params.teamIdList" index="index" separator="," open="(" close=")">
                  #{item}
                </foreach>
              </if>
                AND vt.join_time &lt;= NOW()
            </if>
            GROUP BY v.id
        ) nt2 ON nt2.age &gt;= ad.start AND nt2.age &lt; ad.end
        WHERE ad.code != 'OTHERS'
        GROUP BY ad.code
        ORDER BY ad.start
    </select>

    <select id="getOtherAgeRangeSumData" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.AgeRangeFormDto">
        SELECT
            '其他' AS ageRange,
            COUNT(nt2.id) AS registerNum,
            IFNULL(SUM(IF(nt2.serviceTime IS NULL, 0, 1)), 0) AS activeNum,
            IFNULL(SUM(IFNULL(nt2.serviceTime, 0)), 0) AS totalServiceTime
        FROM (
            SELECT
                v.id,
                nt.serviceTime
            FROM zyz_volunteer v
            LEFT JOIN (
                SELECT
                    volunteer_id,
                    IFNULL(SUM(FLOOR(IFNULL(ROUND(TIMESTAMPDIFF(SECOND, IF(sign_time > time_period_start_time, sign_time, time_period_start_time), time_period_end_time) / 3600, 2), 0)/0.5)*0.5), 0) AS serviceTime
                FROM zyz_activity_apply
                WHERE is_deleted = 0 AND audit_status = 'act_apply_audit_success' AND sign_time IS NOT NULL
                <if test="params.start != null">
                    AND time_period_start_time &gt;= #{params.start}
                </if>
                <if test="params.end != null">
                    AND time_period_start_time &lt;= #{params.end}
                </if>
                GROUP BY volunteer_id
            ) nt ON v.id = nt.volunteer_id
        <if test="params.queryType == 'team'">
            LEFT JOIN zyz_volunteer_team vt ON v.id = vt.volunteer_id
        </if>
            WHERE v.is_deleted = 0
            AND (v.certificate_id IS NULL
            OR LENGTH( v.certificate_id ) != 18
            OR (SUBSTR(NOW(), 1, 4) - SUBSTR(v.certificate_id, 7, 4) - (DATE_FORMAT(NOW(), '%m%d') - SUBSTR(v.certificate_id, 11, 4) &lt; 0 ) &lt; 0))
            <if test="params.queryType == 'org'">
                <if test="params.orgCode != 'top_dept'">
                    AND v.org_code LIKE CONCAT(#{params.orgCode}, '%')
                </if>
                AND v.founded &lt;= NOW()
            </if>
            <if test="params.queryType == 'team'">
                <if test="params.teamIdList != null and params.teamIdList.size > 0">
                    AND vt.team_id IN
                    <foreach item="item" collection="params.teamIdList" index="index" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                AND vt.join_time &lt;= NOW()
            </if>
            GROUP BY v.id
        ) nt2
    </select>
    <select id="findByProjectIds" resultType="com.fykj.scaffold.zyz.domain.dto.ProjectActivityNewExportDto">
        SELECT
            p.*,
            um.acName AS activitys,
            n.title AS news
        FROM zyz_project p
                 LEFT JOIN (
            SELECT project_id, GROUP_CONCAT( `name`,'-','%s',id SEPARATOR ';' ) AS acName FROM zyz_activity GROUP BY project_id
        ) AS um ON um.project_id = p.id
                 LEFT JOIN (
            SELECT project_id, GROUP_CONCAT( `title`,'-','%s',id SEPARATOR ';' ) AS title FROM cms_content GROUP BY project_id
        ) AS n ON n.project_id = p.id
        WHERE p.is_deleted=0
        <if test="idList != null">
            AND p.id IN
            <foreach collection="idList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY p.create_date DESC
    </select>
    <select id="getTeamActivityForPad" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivity">
        SELECT *
        from zyz_activity
        WHERE is_deleted = 0
          AND team_id = #{params.myTeamId}
          AND web_show = 1
          AND audit_status NOT IN ('act_wait_audit', 'act_draft')
        ORDER BY start_time DESC
    </select>
    <select id="getActivityForFrontend" resultType="com.fykj.scaffold.zyz.domain.dto.ActivityApiDto">
        SELECT
            za.*,
            za.id AS actId
        from zyz_activity za
        <where>
            AND za.is_deleted = 0
            AND za.web_show = 1
            AND za.audit_status = 'act_audit_success'
            <if test="params.teamId != null">
                AND za.team_id = #{params.teamId}
            </if>
            <if test="params.publishOrgCodeList != null and params.publishOrgCodeList.size > 0">
                AND za.publish_org_code IN
                <foreach item="item" collection="params.publishOrgCodeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY za.start_time DESC
    </select>

    <!-- 根据需求ID分页查询活动 -->
    <select id="pageByRequirementId" resultType="com.fykj.scaffold.zyz.domain.dto.ActivityByRequirementDto">
        SELECT
            a.id,
            a.picture,
            a.name,
            a.recruit_targets as recruitTargets,
            a.address,
            a.start_time as startTime,
            a.end_time as endTime
        FROM zyz_activity a
        INNER JOIN zyz_requirement r ON a.req_id = r.id
        WHERE r.id = #{params.reqId}
          AND audit_status NOT IN ('act_wait_audit', 'act_draft')
        ORDER BY a.start_time DESC
    </select>
    <select id="getYearMonNumByRecruitTargetsAndDate"
            resultType="com.fykj.scaffold.zyz.domain.dto.SpiritMonActDto$YearMonNumDto">
        SELECT
            DATE_FORMAT( start_time, '%Y-%m' ) AS yearMon,
            count( 0 ) AS num
        FROM
            zyz_activity
        WHERE
            is_deleted = 0
          AND web_show = 1
          AND audit_status = 'act_audit_success'
          AND start_time >= '2025-01-01'
        <if test="startDate != null and startDate != ''">
            AND start_time >= #{startDate}
        </if>
        <if test="recruitTargets != null and recruitTargets != ''">
            AND recruit_targets LIKE CONCAT('%', CONCAT(#{recruitTargets}, '%'))
        </if>
        GROUP BY
            DATE_FORMAT( start_time, '%Y-%m' )
    </select>

    <select id="getSgbNeedSync" resultType="java.lang.Long">
        SELECT a.id
        FROM zyz_activity a
        LEFT JOIN sgb_sync_status_record ssr
                  ON ssr.business_type = 'sgb_sync_biz_activity'
                      AND ssr.business_id = a.id
                      AND ssr.is_deleted = 0
        WHERE a.is_deleted = 0
          AND a.audit_status = 'activity_status_audit_success'
          AND a.start_time &lt;= #{now}
          AND a.start_time &gt;= #{failStartAt}
          AND (
                ssr.id IS NULL
                OR ssr.sync_status = 0
          )
    </select>
</mapper>
