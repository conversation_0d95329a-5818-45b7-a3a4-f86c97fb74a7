package com.fykj.scaffold.zyz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzTypeTeamRelevance;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamParams;
import com.fykj.scaffold.zyz.domain.params.ZyzTypeTeamRelevanceParams;
import com.fykj.scaffold.zyz.service.IZyzTypeTeamRelevanceService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;

/**
 * 团队特色与团队关联表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-02-21
 */
@RestController
@RequestMapping("/admin/zyz/type/team/relevance")
@Api(tags = "团队特色与团队关联接口")
public class ZyzTypeTeamRelevanceController extends BaseController<IZyzTypeTeamRelevanceService, ZyzTypeTeamRelevance,ZyzTypeTeamRelevanceParams> {

    @ApiOperation("根据团队类型获取团队--分页查询")
    @PostMapping("/getPagesByType")
    public JsonResult<IPage<ZyzTeam>> getPagesByType(@RequestBody ZyzTeamParams params) {
        IPage<ZyzTeam> iPage = baseService.getPagesByType(params);
        DictTransUtil.trans(iPage.getRecords());
        return new JsonResult<>(iPage);
    }
}
