package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 系统资源-API接口
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_api")
public class SysApi extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 接口编码
     */
    @TableField("api_code")
    @ApiModelProperty(value = "接口编码")
    private String apiCode;

    /**
     * 接口名称
     */
    @TableField("api_name")
    @ApiModelProperty(value = "接口名称")
    private String apiName;

    /**
     * 接口分类:default-默认分类
     */
    @TableField("api_category")
    @ApiModelProperty(value = "接口分类:default-默认分类")
    private String apiCategory;

    /**
     * 接口分类:default-默认分类
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接口分类:default-默认分类")
    private List<String> apiCategories;

    /**
     * 接口分类:default-默认分类
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "接口分类:default-默认分类")
    private String apiCategoriesText;

    /**
     * 资源描述
     */
    @TableField("api_desc")
    @ApiModelProperty(value = "资源描述")
    private String apiDesc;

    /**
     * 请求路径
     */
    @TableField("api_path")
    @ApiModelProperty(value = "请求路径")
    private String apiPath;

    /**
     * 状态:0-无效 1-有效
     */
    @TableField("status")
    @ApiModelProperty(value = "状态:0-无效 1-有效")
    private Boolean status;

    /**
     * 保留数据0-否 1-是 不允许删除
     */
    @TableField("is_persist")
    @ApiModelProperty(value = "保留数据0-否 1-是 不允许删除")
    private Boolean persist;

    /**
     * 是否需要认证: 0-无认证 1-身份认证 默认:1
     */
    @TableField("is_auth")
    @ApiModelProperty(value = "是否需要认证: 0-无认证 1-身份认证 默认:1")
    private Boolean auth;

    /**
     * 是否公开: 0-内部的 1-公开的
     */
    @TableField("is_open")
    @ApiModelProperty(value = "是否公开: 0-内部的 1-公开的")
    private Boolean open;

    /**
     * 请求方式
     */
    @TableField("request_method")
    @ApiModelProperty(value = "请求方式")
    private String requestMethod;

    /**
     * 响应类型
     */
    @TableField("content_type")
    @ApiModelProperty(value = "响应类型")
    private String contentType;

    /**
     * 服务ID
     */
    @TableField("service_id")
    @ApiModelProperty(value = "服务ID")
    private String serviceId;

    /**
     * 优先级
     */
    @TableField("priority")
    @ApiModelProperty(value = "优先级")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long priority;

    /**
     * 类名
     */
    @TableField("class_name")
    @ApiModelProperty(value = "类名")
    private String className;

    /**
     * 方法名
     */
    @TableField("method_name")
    @ApiModelProperty(value = "方法名")
    private String methodName;


}
