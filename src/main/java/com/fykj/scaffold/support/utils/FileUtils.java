package com.fykj.scaffold.support.utils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


public class FileUtils {

  private FileUtils(){}

    /**
     * 检查图片格式
     *
     * @param file
     * @return
     */
    public static boolean checkImageType(File file) {
        List<String> imageTypeList = new ArrayList<>();
        imageTypeList.add("jpg");
        imageTypeList.add("png");
        imageTypeList.add("gif");
        imageTypeList.add("tif");
        String type = FileTypeUtil.getType(file);
        return type != null && imageTypeList.contains(type);
    }

}
