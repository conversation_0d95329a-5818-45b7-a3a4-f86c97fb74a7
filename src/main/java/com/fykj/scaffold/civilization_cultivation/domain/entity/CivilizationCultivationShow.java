package com.fykj.scaffold.civilization_cultivation.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 文明培育展示-管理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "civilization_cultivation_show")
public class CivilizationCultivationShow extends BaseEntity {

    private static final long serialVersionUID = 5805626225402733851L;

    /**
     * 文明培育类型（道德模范CCT_DDMF、时代新人CCT_SDXR、先进典型CCT_XJDX、园区好人CCT_YQHR）
     */
    @TableField("type")
    @ApiModelProperty(value = "文明培育类型（道德模范CCT_DDMF、时代新人CCT_SDXR、先进典型CCT_XJDX、园区好人CCT_YQHR）")
    @DictTrans(transTo = "typeText")
    private String type;

    /**
     * 文明培育类型text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "文明培育类型text")
    private String typeText;

    /**
     * 姓名
     */
    @TableField("name")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 联系电话
     */
    @TableField(value = "phone", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 年份
     */
    @TableField("year")
    @ApiModelProperty(value = "年份")
    private Integer year;

    /**
     * 所属街道code
     */
    @TableField(value = "belong_street_code", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "所属街道code")
    private String belongStreetCode;

    /**
     * 所属街道名称
     */
    @TableField(value = "belong_street_name", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "所属街道名称")
    private String belongStreetName;

    /**
     * 所属社区code
     */
    @TableField(value = "belong_community_code", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "所属社区code")
    private String belongCommunityCode;

    /**
     * 所属社区名称
     */
    @TableField(value = "belong_community_name", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "所属社区名称")
    private String belongCommunityName;

    /**
     * 地址（道德模范、时代新人、先进典型）
     */
    @TableField(value = "address", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "地址（道德模范、时代新人、先进典型）")
    private String address;

    /**
     * 经度（道德模范、时代新人、先进典型）
     */
    @TableField(value = "longitude", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "经度（道德模范、时代新人、先进典型）")
    private BigDecimal longitude;

    /**
     * 纬度（道德模范、时代新人、先进典型）
     */
    @TableField(value = "latitude", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "纬度（道德模范、时代新人、先进典型）")
    private BigDecimal latitude;

    /**
     * 事迹/成就
     */
    @TableField(value = "achievements", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "事迹/成就")
    private String achievements;

    /**
     * 封面图
     */
    @TableField("picture")
    @ApiModelProperty(value = "封面图")
    private String picture;

    /**
     * 是否外链
     */
    @TableField("out_link")
    @ApiModelProperty(value = "是否外链")
    private Boolean outLink;

    /**
     * 外链地址
     */
    @TableField(value = "out_link_address", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "外链地址")
    private String outLinkAddress;

    /**
     * 介绍
     */
    @TableField(value = "description", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "介绍")
    private String description;

    /**
     * 是否上下架
     */
    @TableField("on_shelve")
    @ApiModelProperty(value = "是否上下架")
    private Boolean onShelve;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;
}
