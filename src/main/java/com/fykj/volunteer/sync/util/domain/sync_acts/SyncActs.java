package com.fykj.volunteer.sync.util.domain.sync_acts;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.volunteer.sync.util.util.XmlUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 发布招募
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class SyncActs {
    /**
     * 活动id
     * 必填
     */
    private String apId;
    /**
     * 活动日期 格式：2016-09-30
     * 必填
     */
    private String actDate;
    /**
     * 开始时间 格式：HH:mm,24小时制
     * 必填
     */
    private String startTime;
    /**
     * 结束时间 格式：HH:mm,24小时制
     * 必填
     */
    private String endTime;
    /**
     * 唯一标识
     * 必填
     */
    private String uniCode;
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "act")
    public static class Act {
        private List<SyncActs> info;
    }
    public String buildXml() {
        SyncActs.Act act = new SyncActs.Act();
        act.setInfo(CollectionUtil.newArrayList(this));
        return XmlUtils.toStr(act);
    }
}
