package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.entity.Role;
import com.fykj.scaffold.security.business.domain.entity.SysUserRole;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 系统角色-用户关联
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2022-02-17
 */
public interface IUserRoleService extends IBaseService<SysUserRole> {

    /**
     * 获取此人的角色列表
     *
     * @param userId
     * @return
     */
    List<Role> getByUserId(long userId);

    /**
     * 保存用户角色关系
     *
     * @param userId
     * @param roleCodes
     */
    void saveUserRoleRelationShip(long userId, List<String> roleCodes);


    /**
     * 添加角色
     *
     * @param userId
     * @param roleCode
     */
    void addRole(long userId, String roleCode);


    /**
     * 移除角色
     *
     * @param userId
     * @param roleCode
     */
    void removeRole(long userId, String roleCode);

    /**
     * 添加团队管理员角色
     *
     * @param userId
     * @param teamId
     */
    void addTeamAdminRole(long userId, long teamId);

    /**
     * 移除团队管理员角色
     *
     * @param userId
     * @param teamId
     */
    void removeTeamAdminRole(long userId, long teamId);

}

