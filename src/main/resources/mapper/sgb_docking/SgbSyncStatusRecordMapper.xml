<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fykj.scaffold.sgb_docking.mapper.SgbSyncStatusRecordMapper">

    <!-- 分页查询同步状态记录（包含业务数据名称） -->
    <select id="selectPageWithBizName" resultType="com.fykj.scaffold.sgb_docking.domain.entity.SgbSyncStatusRecord">
        SELECT
            r.id,
            r.version,
            r.create_date,
            r.update_date,
            r.creator,
            r.updater,
            r.business_type,
            r.business_id,
            r.sync_status,
            r.sync_remark,
            r.sgb_id,
            r.sync_time,
            <choose>
                <!-- 不传bizType时不查询业务数据名称 -->
                <when test="params.bizType == null or params.bizType == ''">
                    NULL
                </when>
                <!-- 志愿者同步 -->
                <when test="params.bizType == 'sgb_sync_biz_volunteer'">
                    v.name
                </when>
                <!-- 团队同步 -->
                <when test="params.bizType == 'sgb_sync_biz_team'">
                    t.name
                </when>
                <!-- 加入团队同步 -->
                <when test="params.bizType == 'sgb_sync_biz_join_team'">
                    CONCAT(IFNULL(vt_v.name, ''), '-', IFNULL(vt_t.name, ''))
                </when>
                <!-- 活动同步 -->
                <when test="params.bizType == 'sgb_sync_biz_activity'">
                    a.name
                </when>
                <!-- 活动招募同步 -->
                <when test="params.bizType == 'sgb_sync_biz_activity_recruit'">
                    atp_a.name
                </when>
                <!-- 活动成员同步 -->
                <when test="params.bizType == 'sgb_sync_biz_activity_member'">
                    CONCAT(IFNULL(aa_v.name, ''), '-', IFNULL(aa_a.name, ''))
                </when>
                <!-- 服务时长同步 -->
                <when test="params.bizType == 'sgb_sync_biz_service_time'">
                    CONCAT(IFNULL(st_v.name, ''), '-', IFNULL(st_a.name, ''))
                </when>
                <!-- 阵地同步 -->
                <when test="params.bizType == 'sgb_sync_biz_base_sync'">
                    o.name
                </when>
                <otherwise>
                    '未知业务类型'
                </otherwise>
            </choose> as bizName
        FROM sgb_sync_status_record r
        <if test="params.bizType != null and params.bizType != ''">
            <if test="params.bizType == 'sgb_sync_biz_volunteer'">
                <!-- 志愿者表连接 -->
                LEFT JOIN zyz_volunteer v ON r.business_id = v.id AND v.is_deleted = 0
            </if>
            <if test="params.bizType == 'sgb_sync_biz_team'">
                <!-- 团队表连接 -->
                LEFT JOIN zyz_team t ON r.business_id = t.id AND t.is_deleted = 0
            </if>
            <if test="params.bizType == 'sgb_sync_biz_join_team'">
                <!-- 志愿者团队关联表连接 -->
                LEFT JOIN zyz_volunteer_team vt ON r.business_id = vt.id AND vt.is_deleted = 0
                LEFT JOIN zyz_volunteer vt_v ON vt.volunteer_id = vt_v.id AND vt_v.is_deleted = 0
                LEFT JOIN zyz_team vt_t ON vt.team_id = vt_t.id AND vt_t.is_deleted = 0
            </if>
            <if test="params.bizType == 'sgb_sync_biz_activity'">
                <!-- 活动表连接 -->
                LEFT JOIN zyz_activity a ON r.business_id = a.id AND a.is_deleted = 0
            </if>
            <if test="params.bizType == 'sgb_sync_biz_activity_recruit'">
                <!-- 活动时段表连接 -->
                LEFT JOIN zyz_activity_time_period atp ON r.business_id = atp.id AND atp.is_deleted = 0
                LEFT JOIN zyz_activity atp_a ON atp.activity_id = atp_a.id AND atp_a.is_deleted = 0
            </if>
            <if test="params.bizType == 'sgb_sync_biz_activity_member'">
                <!-- 活动申请表连接（活动成员） -->
                LEFT JOIN zyz_activity_apply aa ON r.business_id = aa.id AND aa.is_deleted = 0
                LEFT JOIN zyz_volunteer aa_v ON aa.volunteer_id = aa_v.id AND aa_v.is_deleted = 0
                LEFT JOIN zyz_activity aa_a ON aa.activity_id = aa_a.id AND aa_a.is_deleted = 0
            </if>
            <if test="params.bizType == 'sgb_sync_biz_service_time'">
                <!-- 活动申请表连接（服务时长） -->
                LEFT JOIN zyz_activity_apply st ON r.business_id = st.id AND st.is_deleted = 0
                LEFT JOIN zyz_volunteer st_v ON st.volunteer_id = st_v.id AND st_v.is_deleted = 0
                LEFT JOIN zyz_activity st_a ON st.activity_id = st_a.id AND st_a.is_deleted = 0
            </if>
            <if test="params.bizType == 'sgb_sync_biz_base_sync'">
                <!-- 组织表连接 -->
                LEFT JOIN sys_org o ON r.business_id = o.id AND o.is_deleted = 0
            </if>
        </if>
        WHERE r.is_deleted = 0
        <if test="params.bizType != null and params.bizType != ''">
            AND r.business_type = #{params.bizType}
        </if>
        <if test="params.businessId != null">
            AND r.business_id = #{params.businessId}
        </if>
        <if test="params.syncStatus != null">
            AND r.sync_status = #{params.syncStatus}
        </if>
        <if test="params.syncRemark != null and params.syncRemark != ''">
            AND r.sync_remark LIKE CONCAT('%', #{params.syncRemark}, '%')
        </if>
        <if test="params.sgbId != null and params.sgbId != ''">
            AND r.sgb_id LIKE CONCAT('%', #{params.sgbId}, '%')
        </if>
        <if test="params.syncTimeStart != null">
            AND r.sync_time >= #{params.syncTimeStart}
        </if>
        <if test="params.syncTimeEnd != null">
            AND r.sync_time &lt;= #{params.syncTimeEnd}
        </if>
        ORDER BY r.sync_time DESC
    </select>

</mapper>