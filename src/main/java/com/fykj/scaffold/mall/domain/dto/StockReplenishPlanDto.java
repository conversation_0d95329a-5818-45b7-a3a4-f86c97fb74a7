package com.fykj.scaffold.mall.domain.dto;

import com.fykj.scaffold.mall.domain.entity.MallStockReplenishPlan;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class StockReplenishPlanDto {

    private String orgCode;

    private String orgName;

    private List<MallStockReplenishPlan> planList = new ArrayList<>();
}
