package com.fykj.scaffold.support.utils;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * pdf导出工具类
 */
@Slf4j
@Service
public class PdfUtil {

    private PdfUtil() {
    }

    /**
     * 生成pdf
     *
     * @param datas    数据集
     * @param template 模板
     */
    public static InputStream generatePdf(Map<String, Object> datas, String template) {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            PdfReader reader = new PdfReader(template);
            PdfStamper ps = new PdfStamper(reader, bos);
            AcroFields s = ps.getAcroFields();
            // 检查pdf是否有可填充项
            if (!s.getFields().isEmpty()) {
                s.setGenerateAppearances(true);
                Map<String, String> textMap = new HashMap<>();
                Map<String, String> imgMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : datas.entrySet()) {
                    String key = entry.getKey();
                    Object obj = entry.getValue();
                    if (!key.contains("img")) {
                        textMap.put(key, String.valueOf(obj));
                    } else {
                        imgMap.put(key, String.valueOf(obj));
                    }
                }
                log.info("textMap:{}", textMap);
                log.info("AcroFields:{}", s.getFields());
                textFill(textMap, s);
                imageFill(imgMap, ps, s);
            }
            // 如果为false那么生成的PDF文件还能编辑，一定要设为true
            ps.setFormFlattening(true);
            ps.close();
            return new ByteArrayInputStream(bos.toByteArray());
        } catch (IOException | DocumentException e) {
            log.error("生成pdf失败了，请检查", e);
        }
        return null;
    }

    private static void textFill(Map<String, String> data, AcroFields form) throws IOException, DocumentException {
        for (Map.Entry<String, String> entry : data.entrySet()) {
            log.info("key:{},value:{}", entry.getKey(), entry.getValue());
            form.setField(entry.getKey(), entry.getValue());
        }
    }

    private static void imageFill(Map<String, String> imgMap, PdfStamper stamper, AcroFields form) throws IOException, DocumentException {
        for (Map.Entry<String, String> entry : imgMap.entrySet()) {
            String imgpath = entry.getValue();
            int pageNo = form.getFieldPositions(entry.getKey()).get(0).page;
            Rectangle signRect = form.getFieldPositions(entry.getKey()).get(0).position;
            float x = signRect.getLeft();
            float y = signRect.getBottom();
            //根据路径读取图片
            Image image;
            if (imgpath.contains("http")) {
                image = Image.getInstance(new URL(imgpath));
            } else {
                image = Image.getInstance(imgpath);
            }
            //获取图片页面
            PdfContentByte under = stamper.getOverContent(pageNo);
            //图片大小自适应
            image.scaleToFit(signRect.getWidth(), signRect.getHeight());
            //添加图片
            image.setAbsolutePosition(x, y);
            under.addImage(image);
        }
    }



}
