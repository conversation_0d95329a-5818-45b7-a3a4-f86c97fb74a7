package com.fykj.scaffold.security.business.api;

import com.fykj.scaffold.security.business.domain.dto.MiniOrgDto;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.security.business.util.SysOrgUtil;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/api/sys/org")
public class SysOrgApi {

    @Autowired
    private ISysOrgService orgService;

    @ApiOperation("志愿地图获取所有点位")
    @GetMapping("/allOrgForVolunteerMap")
    public Result allOrgForVolunteerMap() {
        List<SysOrg> orgList = orgService.list().stream().filter(x -> x.getCodePrefix().equals("top_dept")
                || x.getCodePrefix().contains("top_dept,xietang")
                || x.getCodePrefix().contains("top_dept,loufeng")
                || x.getCodePrefix().contains("top_dept,weiting")
                || x.getCodePrefix().contains("top_dept,jinjihu")
                || x.getCodePrefix().contains("top_dept,shengpu")
        ).collect(Collectors.toList());
        return new JsonResult<>(orgList);
    }

    @ApiOperation("获取5大街道分协会下所有社区")
    @GetMapping(value = "/getFiveStreetCommunity")
    public JsonResult<List<MiniOrgDto>> getFiveStreetCommunity() {
        List<Long> parentIds = orgService.lambdaQuery()
                .select(BaseEntity::getId)
                .in(SysOrg::getCode, Cons.FIVE_AREA_SUB_ASSOCIATION)
                .list()
                .stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toList());
        return new JsonResult<>(
                orgService.lambdaQuery()
                        .eq(SysOrg::getLevel, 3)
                        .in(SysOrg::getParentId, parentIds)
                        .orderByAsc(SysOrg::getSequence)
                        .list()
                        .stream()
                        .map(it -> new MiniOrgDto(it.getCode(), it.getName(), it.getCodePrefix()))
                        .collect(Collectors.toList()));
    }


    @ApiOperation("获取5大街道分协会信息")
    @GetMapping(value = "/getFiveAreaSubAssociationOrg")
    public JsonResult<List<MiniOrgDto>> getFiveAreaSubAssociationOrg(Boolean withTop) {
        if (withTop == null) {
            withTop = false;
        }
        return new JsonResult<>(orgService.getAreaSubAssociation(withTop, true)
                .stream()
                .map(it -> new MiniOrgDto(it.getCode(), it.getName(), it.getCodePrefix()))
                .collect(Collectors.toList()));
    }

    @GetMapping("/getOrgTree")
    @ApiOperation("获取级联树，根据当前登录人身份，展示其及其下属的组织")
    public JsonResult<List<SysOrg>> getOrgTree() {
        String myTopDept = Cons.TOP_DEPT_CODE;
        List<SysOrg> categories = orgService.getOrgTreeOnlyContainFiveSubAssociation(myTopDept, true);
        SysOrgUtil.clearSensitiveInformation(categories);
        return new JsonResult<>(categories);
    }

    @ApiOperation("获取积分商城5大街道分协会信息")
    @GetMapping(value = "/getMallFiveSubAssociation")
    public JsonResult<List<MiniOrgDto>> getMallFiveSubAssociation() {
        return new JsonResult<>(orgService
                .lambdaQuery()
                .in(SysOrg::getCode, Cons.FIVE_AREA_SUB_ASSOCIATION)
                .orderByAsc(SysOrg::getSequence)
                .list()
                .stream()
                .map(it -> new MiniOrgDto(it.getCode(), it.getName(), it.getCodePrefix()))
                .collect(Collectors.toList()));
    }

    @ApiOperation("获取协会分协会信息")
    @GetMapping(value = "/getASAOrgInfo")
    public JsonResult<List<MiniOrgDto>> getASAOrgInfo() {
        return new JsonResult<>(orgService.getAreaSubAssociation(true, false)
                .stream()
                .map(it -> new MiniOrgDto(it.getCode(), it.getName(), it.getCodePrefix()))
                .collect(Collectors.toList()));
    }
}
