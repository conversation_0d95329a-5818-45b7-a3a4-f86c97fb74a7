package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ActSchedulePreviewDataDto;
import com.fykj.scaffold.zyz.domain.dto.ActScheduleTypeDataDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivitySchedule;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleDetailService;
import com.fykj.scaffold.zyz.service.IZyzActivityScheduleService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_act_schedule.SyncActSchedule;
import com.fykj.volunteer.sync.util.domain.sync_act_schedule.SyncActScheduleResult;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.fykj.scaffold.support.conns.Cons.DATE_FORMAT;
import static com.fykj.scaffold.support.conns.Cons.TIME_FORMAT;

@Component
public class ActScheduleSyncer {

    @Autowired
    private IZyzSyncLogService logService;

    @Autowired
    private IZyzActivityScheduleService scheduleService;
    @Autowired
    private IZyzActivityScheduleDetailService scheduleDetailService;

    @Autowired
    private IDictService dictService;

    public ZyzSyncLog sync(long scheduleId, long syncBillId) {
        ZyzActivitySchedule schedule = scheduleService.getDetail(scheduleId);
        if (!ZyzCons.ACTIVITY_SCHEDULE_STATUS_AUDIT_SUCCESS.equals(schedule.getAuditStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "计划还未审核通过");
        }
        if (Cons.PlatformSyncState.SUCCESS.equals(schedule.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此计划已同步成功，无需再次同步");
        }
        if (CollUtil.isEmpty(schedule.getDetailList())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此计划详情为空，无需同步");
        }
        SyncActSchedule syncActSchedule = new SyncActSchedule();
        syncActSchedule.setUniCode(schedule.getId().toString());
        syncActSchedule.setPbId(schedule.getPbId());
        syncActSchedule.setTitle(schedule.getTitle());
        syncActSchedule.setTypeData(formatTypeData(schedule));
        syncActSchedule.setPreviewData(formatPreviewData(schedule));

        SyncActScheduleResult syncActScheduleResult = ApiWebService.syncActSchedule(syncActSchedule);
        //解析调用结果并保存
        schedule.setSyncRemark(syncActScheduleResult.getMsg());
        schedule.setSyncTime(LocalDateTime.now());
        //成功
        if (syncActScheduleResult.getMsg().contains("同步成功") || syncActScheduleResult.getMsg().contains("数据重复")) {
            schedule.setSync(Cons.PlatformSyncState.SUCCESS);
            for (ZyzActivityScheduleDetail detail : schedule.getDetailList()) {
                syncActScheduleResult.getPreData().stream().filter(x -> x.getUniCode().equals(detail.getId().toString())).findFirst().ifPresent(data -> detail.setPreId(data.getPreId()));
            }
        }
        //其他一律失败
        else {
            schedule.setSync(Cons.PlatformSyncState.FAILURE);
        }
        if (syncActScheduleResult.getMsg().contains("数据重复")) {
            schedule.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        scheduleService.updateById(schedule);
        scheduleDetailService.updateBatchById(schedule.getDetailList());

        //记录日志
        return logService.buildSyncLog(Cons.PlatformSyncBiz.ACT_SCHEDULE_SYNC, syncActScheduleResult.getCode(), syncActScheduleResult.getMsg()
                , syncBillId, scheduleId, schedule.getTitle()
                , JSONUtil.toJsonStr(syncActSchedule), JSONUtil.toJsonStr(syncActScheduleResult));
    }

    private int convertType(String scheduleType) {
        int type;
        switch (scheduleType) {
            case ZyzCons.ACTIVITY_SCHEDULE_TYPE_YEAR:
                type = 4;
                break;
            case ZyzCons.ACTIVITY_SCHEDULE_TYPE_QUARTER:
                type = 3;
                break;
            default:
                type = 2;
                break;
        }
        return type;

    }

    private String formatTypeData(ZyzActivitySchedule schedule) {
        int type = convertType(schedule.getScheduleType());
        List<ActScheduleTypeDataDto> typeDataList = new ArrayList<>();
        ActScheduleTypeDataDto dto = new ActScheduleTypeDataDto();
        dto.setType(type);
        dto.setValue(schedule.getScheduleTypeData());
        typeDataList.add(dto);
        return JSONUtil.toJsonStr(typeDataList);
    }

    private String formatPreviewData(ZyzActivitySchedule schedule) {
        List<ActSchedulePreviewDataDto> dataList = new ArrayList<>();
        for (ZyzActivityScheduleDetail detail : schedule.getDetailList()) {
            ActSchedulePreviewDataDto dto = new ActSchedulePreviewDataDto();
            dto.setUniCode(detail.getId().toString());
            dto.setName(detail.getName());
            dto.setActDate(detail.getStartTime().format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
            dto.setActTime(detail.getStartTime().format(DateTimeFormatter.ofPattern(TIME_FORMAT)).concat("-").concat(detail.getEndTime().format(DateTimeFormatter.ofPattern(TIME_FORMAT))));
            dto.setServiceType(detail.getActivityType());
            dto.setSubjects(detail.getSubjects());
            dto.setTargets(dictService.getByCode(detail.getTargets()).getName());
            dto.setLinkMan(detail.getLinkMan());
            dto.setContant(detail.getLinkPhone());
            dto.setAddress(detail.getAddress());
            dataList.add(dto);
        }
        return JSONUtil.toJsonStr(dataList);
    }

}
