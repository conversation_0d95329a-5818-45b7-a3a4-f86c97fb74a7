package com.fykj.scaffold.sync.service.syncer;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.service.IZyzSyncLogService;
import com.fykj.scaffold.sync.util.SyncUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShow;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodShowService;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShow;
import com.fykj.volunteer.sync.util.domain.sync_act_show.SyncActShowResult;
import constants.Mark;
import exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;
/**
 * 活动公式同步
 */
@Component
public class ActShowSyncer {

    @Autowired
    private IZyzSyncLogService logService;

    @Autowired
    private IZyzActivityTimePeriodShowService showService;

    public ZyzSyncLog sync(long actShowId, long syncBillId) {
        ZyzActivityTimePeriodShow show = showService.getById(actShowId);
        if (!ZyzCons.ACTIVITY_SHOW_STATUS_AUDIT_SUCCESS.equals(show.getAuditStatus())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动公告还未审核通过");
        }
        if (StringUtil.isEmpty(show.getRecruitSyncId())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动招募还未同步");
        }
        if (Cons.PlatformSyncState.SUCCESS.equals(show.getSync())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "此活动时间段活动公示已同步成功，无需再次同步");
        }
        //拼装请求
        SyncActShow syncActShow = new SyncActShow();
        syncActShow.setUniCode(String.valueOf(show.getId()));
        syncActShow.setActId(show.getRecruitSyncId());
        syncActShow.setContent(show.getContent());
        //拼接图片地址
        String picUrl = SyncUtil.getPicPath("");
        if (!StringUtil.isEmpty(show.getPicsUrl())) {
            List<String> list = new ArrayList<>();
            List<String> picUrlList = Arrays.asList(show.getPicsUrl().split(Mark.COMMA));
            picUrlList.forEach(x -> list.add((Base64.encode(SyncUtil.getPicPath(x)))));
            picUrl = String.join(Mark.COMMA, list);
        }
        syncActShow.setPicsUrl(picUrl);
        // 调用同步接口
        SyncActShowResult syncActShowResult = ApiWebService.syncActShow(syncActShow);
        // 保存返回值
        show.setSyncTime(LocalDateTime.now());
        show.setSyncRemark(syncActShowResult.getMsg());
        //成功
        if (syncActShowResult.getMsg().contains("同步成功")) {
            show.setSync(Cons.PlatformSyncState.SUCCESS);
        }
        //数据重复
        else if (syncActShowResult.getMsg().contains("数据重复")) {
            show.setSync(Cons.PlatformSyncState.SUCCESS);
            show.setSyncRemark("市平台反馈数据重复，系统自动判定成功");
        }
        //其他一律失败
        else {
            show.setSync(Cons.PlatformSyncState.FAILURE);
        }

        // 启用单独事务处理日志
        String startTime = show.getStartTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        String endTime = show.getEndTime().format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        String actName = show.getActivityName();
        actName += ("【" + startTime + "--" + endTime + "】");

        ZyzSyncLog log = logService.buildSyncLog(Cons.PlatformSyncBiz.ACTIVITY_SHOW_SYNC, syncActShowResult.getCode(), syncActShowResult.getMsg()
                , syncBillId, show.getId(), actName + "时间段活动公示"
                , JSONUtil.toJsonStr(syncActShow), JSONUtil.toJsonStr(syncActShowResult));
        // 回写业务
        showService.updateById(show);

        return log;
    }
}
