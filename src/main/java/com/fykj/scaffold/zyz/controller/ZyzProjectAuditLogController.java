package com.fykj.scaffold.zyz.controller;

import com.fykj.scaffold.zyz.domain.entity.ZyzProjectAuditLog;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectAuditLogParams;
import com.fykj.scaffold.zyz.service.IZyzProjectAuditLogService;
import fykj.microservice.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;
import io.swagger.annotations.Api;
/**
 * 项目操作记录表
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-06-01
 */
@RestController
@RequestMapping("/admin/project/audit/log")
@Api(tags = "项目操作记录接口")
public class ZyzProjectAuditLogController extends BaseController<IZyzProjectAuditLogService, ZyzProjectAuditLog,ZyzProjectAuditLogParams> {


    //根据id获取详情
    @RequestMapping("/getLogListByProjectId")
    public JsonResult<List<ZyzProjectAuditLog>> getLogListByProjectId(Long projectId,@RequestParam(required = false) String auditType){
        return new JsonResult<>(baseService.getLogListByProjectId(projectId,auditType));
    }
}
