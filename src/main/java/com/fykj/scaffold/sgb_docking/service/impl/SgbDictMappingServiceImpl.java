package com.fykj.scaffold.sgb_docking.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fykj.scaffold.sgb_docking.domain.entity.SgbDictMapping;
import com.fykj.scaffold.sgb_docking.domain.vo.SgbDictMappingSysTypeVo;
import com.fykj.scaffold.sgb_docking.mapper.SgbDictMappingMapper;
import com.fykj.scaffold.sgb_docking.service.ISgbDictMappingService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static constants.Mark.COMMA;

/**
 * 系统字典值与三方字典值映射服务实现类
 */
@Service
public class SgbDictMappingServiceImpl extends BaseServiceImpl<SgbDictMappingMapper, SgbDictMapping>
        implements ISgbDictMappingService {

    @Override
    public Map<String, String> multiGetSgbDictStrBySysDictValue(List<String> sysValues) {
        if(ObjectUtils.isEmpty(sysValues)) {
            return Collections.emptyMap();
        }
        sysValues = sysValues.stream()
                .filter(StrUtil::isNotBlank)
                .map(x -> x.split(COMMA))
                .flatMap(Arrays::stream)
                .collect(Collectors.toList());
        return lambdaQuery().in(SgbDictMapping::getSysValue, sysValues).list().stream()
                .collect(Collectors.groupingBy(
                        SgbDictMapping::getSysValue,
                        Collectors.mapping(
                                SgbDictMapping::getThirdValue,
                                Collectors.joining(COMMA)
                        )
                ));
    }

    @Override
    public String getSgbDictStrBySysDictValue(String sysDictValue) {
        if(ObjectUtils.isEmpty(sysDictValue)) {
            return null;
        }
        return lambdaQuery().eq(SgbDictMapping::getSysValue, sysDictValue).list().stream()
                .map(SgbDictMapping::getThirdValue)
                .collect(Collectors.joining(COMMA));
    }

    @Override
    public List<SgbDictMappingSysTypeVo> getSysTypeList() {
        // 查询所有不重复的sysType
        LambdaQueryWrapper<SgbDictMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SgbDictMapping::getSysType)
                .groupBy(SgbDictMapping::getSysType);

        List<SgbDictMapping> mappings = this.list(queryWrapper);

        // 转换为SgbDictMappingSysTypeVo列表
        List<SgbDictMappingSysTypeVo> result = mappings.stream()
                .map(mapping -> new SgbDictMappingSysTypeVo(mapping.getSysType(), mapping.getSysType()))
                .collect(Collectors.toList());

        // 使用字典翻译处理text字段
        DictTransUtil.trans(result);

        return result;
    }
}