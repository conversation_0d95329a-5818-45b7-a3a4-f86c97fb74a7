package com.fykj.scaffold.security.business.controller;

import com.fykj.scaffold.security.business.domain.entity.UserWriteOff;
import com.fykj.scaffold.security.business.domain.params.UserWriteOffParams;
import com.fykj.scaffold.security.business.service.IUserWriteOffService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import result.JsonResult;
import result.Result;

/**
 * 系统用户注销管理-接口控制器
 *
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/admin/user/write_off")
@Api(tags = "系统用户注销管理-管理接口")
public class UserWriteOffController extends BaseController<IUserWriteOffService, UserWriteOff, UserWriteOffParams> {

    @ApiOperation("分页查询用户注销记录")
    @PostMapping("/pageQuery")
    public Result pageQuery(@RequestBody UserWriteOffParams params) {
        return new JsonResult<>(baseService.pageQuery(params));
    }

    @ApiOperation("注销前获取用户志愿者信息和管理团队信息")
    @GetMapping("/getUserVolunteerAndTeamAdminInfo")
    public Result getUserVolunteerAndTeamAdminInfo() {
        return new JsonResult<>(baseService.getUserVolunteerAndTeamAdminInfo(null));
    }

    @ApiOperation("用户注销")
    @GetMapping("/submit")
    public Result writeOff(@RequestParam(required = false) Long userId) {
        baseService.writeOff(userId);
        return OK;
    }

    @ApiOperation("用户取消注销")
    @GetMapping("/cancel")
    public Result writeOffCancel(@RequestParam(required = false) Long id) {
        baseService.writeOffCancel(id);
        return OK;
    }

    @ApiOperation("最终用户注销处理")
    @GetMapping("/finalWriteOffDeal")
    public Result finalWriteOffDeal(Long id) {
        baseService.finalWriteOffDeal(id);
        return OK;
    }
}
