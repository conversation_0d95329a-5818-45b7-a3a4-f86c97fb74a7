package com.fykj.scaffold.civilization_cultivation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationDashboardDto;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationImportDto;
import com.fykj.scaffold.civilization_cultivation.domain.dto.CivilizationCultivationShowExportDto;
import com.fykj.scaffold.civilization_cultivation.domain.entity.CivilizationCultivationShow;
import com.fykj.scaffold.civilization_cultivation.domain.params.CivilizationCultivationShowParams;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文明培育展示-管理
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface ICivilizationCultivationShowService extends IBaseService<CivilizationCultivationShow> {

    /**
     * 获取文明培育各类型的可筛查年份
     * @param type
     * @return
     */
    List<Integer> getYears(String type);

    /**
     * 文明培育分页展示
     * @param params
     * @return
     */
    IPage<CivilizationCultivationShow> pageShow(CivilizationCultivationShowParams params);

    /**
     * 文明培育分页展示（正式环境）
     * @param params
     * @return
     */
    IPage<CivilizationCultivationShow> pageShowProd(CivilizationCultivationShowParams params);

    /**
     * 获取文明培育详情
     * @param id
     * @return
     */
    CivilizationCultivationShow getDetail(Long id);

    /**
     * 新增/修改
     * @param showInfo
     */
    void showInfoSaveOrUpdate(CivilizationCultivationShow showInfo);

    /**
     * 上/下架
     * @param id
     */
    void onShelveChange(Long id);

    /**
     * 文明培育导入数据
     * @param excel
     * @return
     */
    List<CivilizationCultivationImportDto> dataImport(MultipartFile excel);

    /**
     * 文明培育导出数据
     * @param params
     * @return
     */
    List<CivilizationCultivationShowExportDto> export(BaseParams params);

    /**
     * 首页轮播展示
     * @param params
     * @return
     */
    List<CivilizationCultivationShow> homeBannerShow(CivilizationCultivationShowParams params);

    /**
     * 首页轮播展示(prod环境)
     * @return
     */
    List<CivilizationCultivationShow> homeBannerShowProd();

    /**
     * 大屏数据
     *
     * @param type
     * @return
     */
    List<CivilizationCultivationDashboardDto> cockpitData(String type);

    /**
     * 大屏数据(正式环境)
     *
     * @return
     */
    List<CivilizationCultivationDashboardDto> cockpitDataProd();
}

