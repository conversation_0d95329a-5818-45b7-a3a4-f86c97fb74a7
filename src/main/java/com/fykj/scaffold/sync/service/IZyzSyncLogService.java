package com.fykj.scaffold.sync.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto;
import com.fykj.scaffold.sync.domain.dto.SyncWaringListDto;
import com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncLog;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogParams;
import com.fykj.scaffold.sync.domain.params.ZyzSyncLogStatisticsParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 市平台信息同步日志
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-10
 */
public interface IZyzSyncLogService extends IBaseService<ZyzSyncLog> {
    /**
     * 保存同步日志
     */
    ZyzSyncLog buildSyncLog(String bizType, String resultCode, String msg, long syncBillId, long bizId, String objName, String reqParam, String resp);


    /**
     * 统计分页数据
     *
     * @param params
     * @return
     */
    IPage<SyncStatisticsDto> statisticsPage(ZyzSyncLogStatisticsParams params);

    /**
     * 失败原因分页查询
     *
     * @param params
     * @return
     */
    List<SyncFailStatisticsDto> statisticsFailList(ZyzSyncLogStatisticsParams params);

    /**
     * 上报数据预警列表
     *
     * @param date
     * @return
     */
    List<SyncWaringListDto> statisticsFailWaringList(String date);

    /**
     * 导出统计数据
     *
     * @param params
     * @return
     */
    List<ZyzSyncLogDto> exportStatistics(ZyzSyncLogStatisticsParams params);


    void sendMsg();
}

