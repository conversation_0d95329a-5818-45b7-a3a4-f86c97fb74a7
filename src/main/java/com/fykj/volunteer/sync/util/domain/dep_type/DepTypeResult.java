package com.fykj.volunteer.sync.util.domain.dep_type;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

/**
 * <info>
 *     <code>SUCCESS</code>
 *     <msg>成功</msg>
 *     <regionCode>3205</regionCode>
 *     <regionName>苏州市</regionName>
 *     <parentCode>32</parentCode>
 *   </info>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class DepTypeResult {
    //类型编号
    private String depId;
    //类型名称
    private String depName;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "depType")
    public static class DepType {
        private List<DepTypeResult> info = new ArrayList<>();
    }
}
