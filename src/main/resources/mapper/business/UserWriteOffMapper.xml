<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.security.business.mapper.UserWriteOffMapper">

    <select id="pageQuery" resultType="com.fykj.scaffold.security.business.domain.entity.UserWriteOff">
        SELECT
            nt.*
        FROM (
            SELECT
                uwo.id,
                uwo.is_deleted,
                uwo.user_id,
                uwo.volunteer_id,
                uwo.apply_date,
                uwo.cancel_apply,
                uwo.plan_or_real_account_link_info_remove_date,
                uwo.removed,
                uwo.remove_fail_reason,
                uwo.user_role,
                IF(u.id IS NULL, uwo.username, u.username) AS username,
                IF(u.id IS NULL, uwo.name, u.name)         AS name,
                IF(u.id IS NULL, uwo.mobile, u.mobile)     AS mobile,
                IF(u.id IS NULL, FALSE, TRUE)              AS userExists,
                IF(u.id IS NULL, NULL, u.status)           AS userStatus,
                v.name                                     AS volunteerName
            FROM sys_user_write_off uwo
            LEFT JOIN sys_user u ON uwo.user_id = u.id AND u.is_deleted = 0
            LEFT JOIN zyz_volunteer v ON uwo.volunteer_id = v.id AND v.is_deleted = 0
        ) AS nt
        WHERE nt.is_deleted = 0
        <if test="params.keyword != null and params.keyword != ''">
            AND (nt.username LIKE CONCAT('%', #{params.keyword}, '%')
                     OR nt.name LIKE CONCAT('%', #{params.keyword}, '%')
                     OR nt.mobile LIKE CONCAT('%', #{params.keyword}, '%')
                     OR nt.volunteerName LIKE CONCAT('%', #{params.keyword}, '%'))
        </if>
        <if test="params.canceled != null">
            AND nt.cancel_apply = #{params.canceled}
        </if>
        <if test="params.removed != null">
            AND nt.removed = #{params.removed}
        </if>
        <if test="params.removeFailed != null and params.removeFailed == true">
            AND nt.remove_fail_reason IS NOT NULL
        </if>
        <if test="params.applyDateStart != null">
            AND nt.apply_date &gt;= #{params.applyDateStart}
        </if>
        <if test="params.applyDateEnd != null">
            AND nt.apply_date &lt;= #{params.applyDateEnd}
        </if>
        <if test="params.daysDuration != null">
            AND CURDATE() = DATE_SUB(nt.plan_or_real_account_link_info_remove_date, INTERVAL #{params.daysDuration} DAY)
        </if>
    </select>

    <select id="getUserRoleDescription" resultType="java.lang.String">
        SELECT
            GROUP_CONCAT(IF(orgOrTeamInfo IS NULL, name, CONCAT(name, '(', orgOrTeamInfo, ')')) SEPARATOR '\n')
        FROM (
            SELECT
                r.name,
                GROUP_CONCAT(DISTINCT IFNULL(o.name, t.name) SEPARATOR ', ') AS orgOrTeamInfo
            FROM sys_user_role ur
            LEFT JOIN sys_role r ON ur.role_id = r.id AND r.is_deleted = 0
            LEFT JOIN sys_user_expand ue ON ur.user_id = ue.user_id AND r.code = ue.role_code AND ue.is_deleted = 0
            LEFT JOIN sys_org o ON ue.org_code = o.code AND o.is_deleted = 0
            LEFT JOIN zyz_team t ON ue.team_id = t.id AND t.is_deleted = 0
            WHERE ur.is_deleted = 0 AND ur.user_id = #{userId}
            GROUP BY ur.role_id
            ORDER BY IF(r.code = 'VOLUNTEER', 5, IF(r.code = 'ASSOCIATION_ADMIN', 4, IF(r.code = 'SUB_ASSOCIATION_ADMIN', 3, IF(r.code = 'COMMUNITY_ADMIN', 2, IF(r.code = 'TEAM_ADMIN', 1, 0))))) DESC
        ) AS nt
    </select>
</mapper>
