package com.fykj.scaffold.onenet;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/11/17 13:14
 */
@Data
public class ItemReslt {

    private Status status;

    private JSONObject custom;

    @Data
    public static class Status implements Serializable {

        private static final long serialVersionUID = -3958200508847248326L;

        public String code;

        public String text;

    }
}
