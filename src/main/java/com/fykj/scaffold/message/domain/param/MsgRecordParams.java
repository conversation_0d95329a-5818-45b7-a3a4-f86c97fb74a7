package com.fykj.scaffold.message.domain.param;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date: 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("消息记录查询参数")
public class MsgRecordParams extends BaseParams {

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("发送id")
    private Long sendId;

    @ApiModelProperty("发送方式")
    private String sendType;

    @ApiModelProperty("消息状态")
    private Boolean status;

    @ApiModelProperty("发送状态")
    private Integer sendStatus;

    @ApiModelProperty("是否是自定义消息")
    private Boolean selfDefine;
}
