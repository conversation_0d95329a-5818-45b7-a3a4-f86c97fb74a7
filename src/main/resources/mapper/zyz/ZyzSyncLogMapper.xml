<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.sync.mapper.ZyzSyncLogMapper">
    <select id="statisticsPage" resultType="com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto">
        SELECT
            DATE_FORMAT( sync_time, '%Y-%m' ) AS year_mon,
            count( 0 ) AS add_num,
            sum(IF( result_code = 'SUCCESS', 1, 0 )) as success_num,
            sum(IF( result_code = 'FAIL', 1, 0 )) as fail_num
        FROM
            zyz_sync_log
        WHERE
            is_deleted = 0
          AND biz_type = #{params.bizType}
        <if test="params.start != null and params.start != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &gt;= #{params.start}
        </if>
        <if test="params.end != null and params.end != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &lt;= #{params.end}
        </if>
        GROUP BY
            DATE_FORMAT( sync_time, '%Y-%m' )
        ORDER BY
            year_mon DESC
    </select>
    <select id="exportStatistics" resultType="com.fykj.scaffold.sync.domain.dto.ZyzSyncLogDto">
        SELECT
            sync_bill_id,
            sync_object_name,
            result_code,
            IF( result_code = 'SUCCESS', '成功', '失败' ) as result_name,
            sync_time,
            sp_code,
            message
        FROM
            zyz_sync_log
        WHERE
            is_deleted = 0
            AND biz_type = #{params.bizType}
            AND result_code = #{params.resultCode}
            <if test="params.yearMon != null and params.yearMon != ''">
                AND DATE_FORMAT( sync_time, '%Y-%m' ) = #{params.yearMon}
            </if>
            <if test="params.start != null and params.start != ''">
                AND DATE_FORMAT( sync_time, '%Y-%m' ) &gt;= #{params.start}
            </if>
            <if test="params.end != null and params.end != ''">
                AND DATE_FORMAT( sync_time, '%Y-%m' ) &lt;= #{params.end}
            </if>
            <if test="params.message != null and params.message != ''">
                AND message = #{params.message}
            </if>
        ORDER BY sync_time DESC
    </select>
    <select id="statisticsFailList" resultType="com.fykj.scaffold.sync.domain.dto.SyncFailStatisticsDto">
        SELECT
            message,
            count( 0 ) AS fail_num
        FROM
            zyz_sync_log
        WHERE
            is_deleted = 0
        AND biz_type = #{params.bizType}
        AND result_code = 'FAIL'
        <if test="params.start != null and params.start != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &gt;= #{params.start}
        </if>
        <if test="params.end != null and params.end != ''">
            AND DATE_FORMAT( sync_time, '%Y-%m' ) &lt;= #{params.end}
        </if>
        GROUP BY
            message
        ORDER BY
            fail_num DESC
            LIMIT 10
    </select>
    <select id="getWaringList" resultType="com.fykj.scaffold.sync.domain.dto.SyncWaringListDto">
        SELECT
            `code` as bizType,
            `name` as bizTypeName
        FROM
            `sys_dict`
        WHERE
            `parent_id` = #{parentId}
          AND CODE NOT IN (
                           'sync_schedule_act_plan',
                           'sync_biz_news_sync')
        ORDER BY `sequence` ASC
    </select>
    <select id="getByYearMon" resultType="com.fykj.scaffold.sync.domain.dto.SyncStatisticsDto">
        SELECT
        biz_type AS biz_type,
        count( 0 ) AS add_num,
        sum(IF( result_code = 'SUCCESS', 1, 0 )) as success_num,
        sum(IF( result_code = 'FAIL', 1, 0 )) as fail_num
        FROM
        zyz_sync_log
        WHERE
        is_deleted = 0
        AND DATE_FORMAT( sync_time, '%Y-%m' ) = #{yearMon}
        GROUP BY
            biz_type
    </select>

</mapper>
