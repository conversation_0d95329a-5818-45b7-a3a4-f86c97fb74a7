package com.fykj.scaffold.sync.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.business.cons.SsoCons;
import com.fykj.scaffold.security.business.service.ISsoUserMappingService;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.sync.domain.entity.ZyzSyncRegionDictAll;
import com.fykj.scaffold.sync.mapper.ZyzSyncRegionDictAllMapper;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictAllService;
import com.fykj.scaffold.sync.service.IZyzSyncRegionDictService;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingRegionAllRecord;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingRegionAllRecordService;
import com.fykj.scaffold.zsq_docking.push_mq.dto.ActZyzSyncBelongRegionDto;
import com.fykj.scaffold.zsq_docking.push_mq.event.RegionAllDockingEvent;
import com.fykj.volunteer.sync.util.api.ApiWebService;
import com.fykj.volunteer.sync.util.domain.region.RegionResult;
import constants.Mark;
import fykj.microservice.core.base.BaseServiceImpl;
import joptsimple.internal.Strings;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import utils.StringUtil;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.ActivityDataDockingType.DT_CREATE;
import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;


/**
 * 市平台地域数据字典_所有的（苏州大市）
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzSyncRegionDictAllServiceImpl extends BaseServiceImpl<ZyzSyncRegionDictAllMapper, ZyzSyncRegionDictAll> implements IZyzSyncRegionDictAllService {

    @Autowired
    private IZyzSyncRegionDictService syncRegionDictService;

    @Autowired
    private IZSQDockingRegionAllRecordService regionAllRecordService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Autowired
    private ISsoUserMappingService ssoUserMappingService;

    @Override
    public void syncRegionDictAll() {
        List<RegionResult> regionResultList = ApiWebService.getRegion("");
        //市区返回的数据里面有 一条脏数据 region code=320506403133
        List<ZyzSyncRegionDictAll> dicts = regionResultList.stream().filter(x -> !x.getRegionCode().equals("320506403133")).map(it -> {
            ZyzSyncRegionDictAll dict = new ZyzSyncRegionDictAll();
            BeanUtil.copyProperties(it, dict);
            return dict;
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(dicts)) {
            return;
        }
        Map<String, ZyzSyncRegionDictAll> parentMap = dicts.stream().collect(Collectors.toMap(ZyzSyncRegionDictAll::getRegionCode, a -> a, (k1, k2) -> k1));
        dicts.forEach(dict -> {
            List<String> codePrefix = new ArrayList<>();
            //先把自己添加进去
            codePrefix.add(dict.getRegionCode());
            ZyzSyncRegionDictAll previousOrg = parentMap.get(dict.getParentCode());
            while (previousOrg != null) {
                codePrefix.add(previousOrg.getRegionCode());
                previousOrg = parentMap.get(previousOrg.getParentCode());
            }
            //倒叙变为字符串
            dict.setCodeLink(StringUtil.join(CollectionUtil.reverse(codePrefix), Mark.COMMA));
        });
        if (CollectionUtil.isNotEmpty(dicts)) {
            baseMapper.clearAll();
            saveBatch(dicts);
            dockingZSQ(dicts, DT_CREATE);
        }
    }

    private void dockingZSQ(List<ZyzSyncRegionDictAll> list, String operation) {
        ZSQDockingRegionAllRecord dockingRecord = new ZSQDockingRegionAllRecord();
        dockingRecord.setDockingType(DOCKING_TYPE_PUSH);
        dockingRecord.setDockingOperation(operation);
        dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_PENDING);
        dockingRecord.setDockingTime(LocalDateTime.now());
        dockingRecord.setRetry(Boolean.FALSE);
        List<ActZyzSyncBelongRegionDto> zsqRegionAllList = new ArrayList<>();
        list.forEach(it -> {
            ActZyzSyncBelongRegionDto dto = new ActZyzSyncBelongRegionDto();
            BeanUtils.copyProperties(it, dto);
            zsqRegionAllList.add(dto);
        });

        Serializable userId = Oauth2Util.getUserId();
        String zwtUserId = userId == null ? null : ssoUserMappingService.getSSOUserIdByProviderAndZYZUserId(SsoCons.SsoProvider.ZWT.name(), (Long) userId);
        if (StringUtil.isEmpty(zwtUserId)) {
            dockingRecord.setDockingStatus(DOCKING_RECORD_STATUS_FAIL);
            dockingRecord.setDockingMsg("当前操作人" + (userId == null ? Strings.EMPTY : "{" + userId + "}") + "未获取政务通 userId ！");
            regionAllRecordService.save(dockingRecord);
            RegionAllDockingEvent event = RegionAllDockingEvent.builder().dockingRecordId(dockingRecord.getId()).operation(operation).zyzUserId(Oauth2Util.getUserId()).regionAllList(zsqRegionAllList).build();
            dockingRecord.setDockingData(JSONObject.toJSONString(event));
            regionAllRecordService.updateById(dockingRecord);
            return;
        }
        regionAllRecordService.save(dockingRecord);
        RegionAllDockingEvent event = RegionAllDockingEvent.builder().dockingRecordId(dockingRecord.getId()).operation(operation).zwtUserId(zwtUserId).regionAllList(zsqRegionAllList).build();
        dockingRecord.setDockingData(JSONObject.toJSONString(event));
        regionAllRecordService.updateById(dockingRecord);
        mqTemplate.send(TOPIC_BELONG_FIELD_DOCKING, MessageBuilder.withPayload(event).build());

    }

}
