package com.fykj.scaffold.zyz.controller.mini;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.dto.ZyzResourceDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.entity.ZyzResourceAppointment;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteer;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceParams;
import com.fykj.scaffold.zyz.service.IZyzResourceAppointmentService;
import com.fykj.scaffold.zyz.service.IZyzResourceService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.Arrays;


@Slf4j
@RestController
@RequestMapping("/admin/mini/resource")
@Api(tags = "微信小程序资源接口--需登录")
public class MiniResourceController {


    @Autowired
    private IZyzResourceAppointmentService appointmentService;
    @Autowired
    private IZyzResourceService resourceService;

    @Autowired
    private IZyzVolunteerService volunteerService;


    /**
     * 1.判断是否需要预约 --预约的次数>0 判断是否为管理员账号
     * 2.预约  -- 志愿者和游客无法预约
     */

    @ApiOperation("是否展示预约按钮 -- 小程序")
    @GetMapping("/isShow")
    public Result isShow(Long resId) {
        if (resId != null) {
            // if判断下null, 兼容性保障
            ZyzResource resource = resourceService.getById(resId);
            if (resource.getAppointmentNum() < resource.getHasAppointmentNum()) {
                return new JsonResult<>(new Result(-1, "已约完"));
            }
        }
        if (Arrays.stream(Cons.RoleCode.MANAGER_CAPACITY_ROLE).noneMatch(it -> it.equals(Oauth2Util.getManagerCapacity()))) {
            return new Result(-4, "个人身份无法预约");
        }
        ZyzVolunteer volunteer = volunteerService.getByPhone(Oauth2Util.getMobile());
        if (volunteer == null || !volunteer.getPerfect()) {
            return new JsonResult<>(new Result(-2, "去完善信息"));
        }
        if (!volunteer.getCertification()) {
            return new JsonResult<>(new Result(-3, "前往实名"));
        }
        return new Result();
    }

    @SysLogMethod("预约")
    @ApiOperation("预约")
    @PostMapping({"/saveAppointmentForMini"})
    public Result saveAppointmentForMini(@RequestBody ZyzResourceAppointment entity) {
        boolean result = appointmentService.saveAppointment(entity);
        return result ? new Result() : new Result(ResultCode.FAIL);
    }


    @ApiOperation("我的预约分页查询")
    @PostMapping({"/pagesForMyAppointment"})
    public JsonResult<IPage<ZyzResourceDto>> pagesForMyAppointment(@RequestBody(required = false) ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        IPage<ZyzResourceDto> result = resourceService.pagesForMyAppointment(params);
        return new JsonResult<>(result);
    }


}
