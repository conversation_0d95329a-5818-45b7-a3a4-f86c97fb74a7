package com.fykj.scaffold.security.business.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.security.business.domain.entity.MsgTemplate;
import com.fykj.scaffold.security.business.mapper.MsgTemplateMapper;
import com.fykj.scaffold.security.business.service.IMsgTemplateService;
import com.fykj.scaffold.security.business.service.ISysMessagesLogService;
import com.fykj.scaffold.weixin.mini.config.WxMaConfiguration;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static cn.hutool.core.text.CharSequenceUtil.*;
import static cn.hutool.core.text.StrPool.COMMA;
import static com.fykj.scaffold.support.conns.Cons.*;


/**
 * 模版消息
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MsgTemplateServiceImpl extends BaseServiceImpl<MsgTemplateMapper, MsgTemplate> implements IMsgTemplateService {

    @Autowired
    private ISysMessagesLogService smsService;

    @Override
    public boolean save(MsgTemplate entity) {
        generateParamsNum(entity);
        return super.save(entity);
    }

    @Override
    public boolean updateById(MsgTemplate entity) {
        generateParamsNum(entity);
        return super.updateById(entity);
    }

    private void generateParamsNum(MsgTemplate entity) {
        String template = entity.getTemplate();
        String prompt = entity.getMsgPrompt();
        String link = entity.getJumpLink();
        if (StringUtil.isEmpty(template)) {
            throw new BusinessException(ResultCode.FAIL, "模版内容不能为空！");
        }
        if (MPW_WECHAT.equals(entity.getPushWay()) && StringUtil.isEmpty(prompt)) {
            throw new BusinessException(ResultCode.FAIL, "微信推送消息提示不能为空！");
        }
        entity.setTemplateParamsNum(StrUtil.split(template, MTM_SPLIT_CHAR).size() - 1);
        if (MPW_WECHAT.equals(entity.getPushWay())) {
            entity.setPromptParamsNum(StrUtil.split(prompt, MTM_SPLIT_CHAR).size() - 1);
        } else {
            entity.setPromptParamsNum(0);
        }
        if (StringUtil.isEmpty(link)) {
            entity.setLinkParamsNum(0);
        } else {
            entity.setLinkParamsNum(StrUtil.split(link, MTM_SPLIT_CHAR).size() - 1);
        }
    }

    @Override
    public IPage<MsgTemplate> page(BaseParams params) {
        return super.page(params).convert(this::convert);
    }

    private MsgTemplate convert(MsgTemplate record) {
        record = DictTransUtil.trans(record);
        return record;
    }

    @Override
    public Boolean validateCode(String code, Long id) {
        return !lambdaQuery().eq(MsgTemplate::getCode, code).ne(ObjectUtil.isNotEmpty(id), MsgTemplate::getId, id).exists();
    }

    @Override
    public void ground(Long id) {
        MsgTemplate tmp = getById(id);
        lambdaUpdate().eq(MsgTemplate::getId, id).set(MsgTemplate::getStatus, !tmp.getStatus()).update();
    }

    @Override
    public MsgTemplate getByCode(String code) {
        MsgTemplate template = lambdaQuery().eq(MsgTemplate::getCode, code).one();
        if (ObjectUtil.isEmpty(template)) {
            throw new BusinessException(ResultCode.FAIL, "code【" + code + "】有误，无法获取相应消息模版！");
        }
        if (!template.getStatus()) {
            throw new BusinessException(ResultCode.FAIL, "该code【" + code + "】对应消息模版未启用！");
        }
        return template;
    }

    @Override
    public void sendTmpMsg(String code, List<String> receiver, Object... params) {
        if (CollectionUtil.isEmpty(receiver)) {
            log.error("消息接收者列表不能为空！");
            throw new BusinessException(ResultCode.FAIL, "消息接收者列表不能为空！");
        }
        MsgTemplate msgTemplate = lambdaQuery().eq(MsgTemplate::getCode, code).one();
        int tpn = msgTemplate.getTemplateParamsNum();
        int ppn = msgTemplate.getPromptParamsNum();
        int lpn = msgTemplate.getLinkParamsNum();
        int needParamsNum = tpn + ppn + lpn;
        if (params.length != needParamsNum) {
            log.error("模版消息参数异常！");
            throw new BusinessException(ResultCode.FAIL, "模版消息参数异常！");
        }
        String template = msgTemplate.getTemplate();
        String prompt = msgTemplate.getMsgPrompt();
        String link = msgTemplate.getJumpLink();
        String formatStr = template;
        if (StringUtil.isNotEmpty(prompt)) {
            formatStr = formatStr.concat(MTM_TMP_PMP_JL_SPLIT_CHAR).concat(prompt);
        }
        if (StringUtil.isNotEmpty(link)) {
            formatStr = formatStr.concat(MTM_TMP_PMP_JL_SPLIT_CHAR).concat(link);
        }
        formatStr = StrUtil.format(formatStr, params).replaceAll(NULL, EMPTY);
        String[] tmpPmpLmp = formatStr.split(MTM_TMP_PMP_JL_SPLIT_CHAR);
        template = tmpPmpLmp[0];
        if (tmpPmpLmp.length > 1) {
            if (StringUtil.isNotEmpty(prompt)) {
                prompt = tmpPmpLmp[1];
            } else {
                link = tmpPmpLmp[1];
            }
        }
        if (tmpPmpLmp.length > 2) {
            link = tmpPmpLmp[2];
        }
        String pushWay = msgTemplate.getPushWay();
        if (MPW_SMS.equals(pushWay)) {
            log.info("短信发送手机号：{}", String.join(COMMA, receiver));
            if (StringUtil.isNotEmpty(link)) {
                template = template.concat(link);
            }
            smsService.sendMessages(String.join(COMMA, receiver), template);
            return;
        }
        if (template.length() > 20 || prompt.length() > 20) {
            log.error("微信公众号消息的模版数据不能超过20个字符！");
            template = template.length() > 20 ? template.substring(0, 20) : template;
            prompt = prompt.length() > 20 ? prompt.substring(0, 20) : prompt;
            //throw new BusinessException(ResultCode.FAIL, "微信公众号消息的模版数据不能超过20个字符！");
        }
        log.info("微信消息发送账号：{}", String.join(COMMA, receiver));
        for (String it : receiver) {
            sendWechatMsg(it, template, prompt, link);
        }
    }

    private void sendWechatMsg(String receiverOpenId, String template, String prompt, String page) {
        WxMaService wxMaService = WxMaConfiguration.getMaService(APP_ID);
        //创建模板消息map
        List<WxMaSubscribeMessage.MsgData> data = Arrays.asList(
                //最多20字
                new WxMaSubscribeMessage.MsgData(WECHAT_MSG_DATA_ONE_KEY, template),
                new WxMaSubscribeMessage.MsgData(WECHAT_MSG_DATA_TIME_KEY, LocalDateTimeUtil.formatNormal(LocalDateTime.now())),
                new WxMaSubscribeMessage.MsgData(WECHAT_MSG_DATA_TWO_KEY, prompt)
        );
        WxMaSubscribeMessage msg = WxMaSubscribeMessage.builder()
                .templateId(TEMPLATE_ID)
                .toUser(receiverOpenId)
                .data(data)
                .page(page)
                .build();
        try {
            wxMaService.getSubscribeService().sendSubscribeMsg(msg);
        } catch (WxErrorException e) {
            log.error("微信公众号消息发送失败异常WxErrorException！", e);
            throw new BusinessException(ResultCode.FAIL, "微信公众号消息发送失败！");
        } catch (Exception e) {
            log.error("微信公众号消息发送失败异常类型未知！", e);
            throw new BusinessException(ResultCode.FAIL, "微信公众号消息发送失败！");
        }
    }
}
