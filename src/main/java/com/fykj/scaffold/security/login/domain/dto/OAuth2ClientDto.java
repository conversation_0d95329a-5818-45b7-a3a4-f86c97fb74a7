package com.fykj.scaffold.security.login.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel
public class OAuth2ClientDto extends Oauth2BaseDto {

    @NotNull(message = "AppId必填")
    @ApiModelProperty(value = "AppId")
    private String appId;

    @NotNull(message = "clientSecret必填")
    @ApiModelProperty(value = "开放密钥")
    private String clientSecret;

}
