<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzResourceMapper">

    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT
        t.*,
        t.is_sync AS sync,
        CASE WHEN t.team_publish = TRUE THEN FALSE
        ELSE IF(o.level = 2 OR o.level = 3, TRUE, FALSE) END AS dockingZSQ
        FROM
        zyz_resource t
        LEFT JOIN sys_org o ON o.code = t.publish_org_code
        WHERE
        t.is_deleted = FALSE
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.type != null and params.type != ''">
            AND t.type = #{params.type}
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and t.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.status != null and params.status != ''">
            AND t.status = #{params.status}
        </if>
        <if test="params.autoStatus != null">
            AND t.auto_status = #{params.autoStatus}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (t.belong_field_top = #{params.fieldId} OR t.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( t.end_time &lt; #{params.startTime} OR t.start_time &gt; #{params.endTime} )
        </if>
        <if test="params.publishStartTime!= null and params.publishEndTime != null">
            AND NOT ( t.publish_time &lt; #{params.publishStartTime} OR t.publish_time &gt; #{params.publishEndTime} )
        </if>
        <if test="params.myOrgCode != null and params.myOrgCode != ''">
            and t.publish_org_code =#{params.myOrgCode}
            and t.team_publish = 0
        </if>
        <if test="params.myTeamId != null">
            AND t.team_id = #{params.myTeamId}
            and t.team_publish = 1
        </if>
        ORDER BY create_date DESC
    </select>

    <select id="pageForAudit" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT
        zr.id AS id,
        zr.name AS name,
        zr.type AS type,
        zr.belong_field_name_top AS belongFieldNameTop,
        zr.belong_field_name_end AS belongFieldNameEnd,
        zr.publish_org_name AS publishOrgName,
        zr.contact_person AS contactPerson,
        zr.contact_phone AS contactPhone,
        zr.start_time AS startTime,
        zr.end_time AS endTime,
        zr.audit_org_code as auditOrgCode,
        soa.name as auditOrgName,
        zr.status AS status,
        zr.publish_time AS publishTime
        FROM zyz_resource AS zr
        LEFT JOIN sys_org so ON zr.publish_org_code = so.code
        LEFT JOIN sys_org soa ON zr.audit_org_code = soa.code
        WHERE zr.is_deleted = 0 AND zr.status != 'res_draft'
        <if test="params.key != null and params.key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.needAudit != null and params.needAudit == true ">
            AND zr.audit_org_code = #{params.myOrgCode} AND zr.status = 'res_wait_audit'
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND zr.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.type != null and params.type != ''">
            AND zr.type = #{params.type}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.status != null and params.status != ''">
            AND zr.status = #{params.status}
        </if>
        <if test="params.publishStartTime != null and params.publishEndTime != null">
            AND ( zr.publish_time &gt;= #{params.publishStartTime} AND zr.publish_time &lt;= #{params.publishEndTime} )
        </if>
        ORDER BY zr.publish_time DESC
    </select>

    <select id="getResWaitAuditNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_resource
        WHERE is_deleted = 0
          AND status = #{status}
          AND audit_org_code = #{myOrgCode}
    </select>

    <select id="listOrPageForResourceAppoint" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT
        zr.id AS id,
        zr.name AS name,
        zr.type AS type,
        zr.belong_field_name_top AS belongFieldNameTop,
        zr.belong_field_name_end AS belongFieldNameEnd,
        zr.publish_org_name AS publishOrgName,
        zr.contact_person AS contactPerson,
        zr.contact_phone AS contactPhone,
        zr.start_time AS startTime,
        zr.end_time AS endTime,
        zr.appointment_num AS appointmentNum,
        zr.has_appointment_num AS hasAppointmentNum
        FROM zyz_resource AS zr
        LEFT JOIN sys_org so ON zr.publish_org_code = so.code
        WHERE zr.is_deleted = 0 AND zr.status = 'res_audit_success' AND zr.auto_status = 1
        <if test="params.key != null and params.key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND so.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
        </if>
        <if test="params.type != null and params.type != ''">
            AND zr.type = #{params.type}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( zr.end_time &lt; #{params.startTime} OR zr.start_time &gt; #{params.endTime} )
        </if>
        ORDER BY zr.publish_time DESC
    </select>
    <select id="listOrPageForMyAppoint" resultType="com.fykj.scaffold.zyz.domain.dto.ZyzResourceDto">
        SELECT
        r.id AS id,
        ra.id as appointmentId,
        r.name AS name,
        r.type AS type,
        r.belong_field_top AS belongFieldTop,
        r.belong_field_end AS belongFieldEnd,
        r.belong_field_name_top AS belongFieldNameTop,
        r.belong_field_name_end AS belongFieldNameEnd,
        r.publish_org_name AS publishOrgName,
        r.picture as picture,
        ra.org_name as orgName,
        r.contact_person AS contactPerson,
        r.contact_phone AS contactPhone,
        ra.audit_status as auditStatus,
        ra.appointment_start_time AS appointmentStartTime,
        ra.appointment_end_time AS appointmentEndTime,
        ra.audit_end_time AS auditEndTime,
        ra.audit_start_time AS auditStartTime
        FROM zyz_resource_appointment ra
        left join zyz_resource r on r.id=ra.resource_id
        left join sys_org so on so.code=ra.org_code
        left join sys_org sp on sp.code=r.publish_org_code
        WHERE ra.is_deleted = 0 and r.is_deleted =0
        <if test="params.myOrgCode != null and params.myOrgCode != ''">
            and so.code_prefix =#{params.myOrgCode}
        </if>
        <if test="params.myTeamId != null">
            AND ra.team_id = #{params.myTeamId}
        </if>
        <if test="params.teamPublish != null">
            AND ra.team_publish = #{params.teamPublish}
        </if>
        <if test="params.key != null and params.key != ''">
            AND ( r.name LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR r.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR r.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND sp.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
        </if>
        <if test="params.type != null and params.type != ''">
            AND r.type = #{params.type}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (r.belong_field_top = #{params.fieldId} OR r.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( ra.appointment_end_time &lt; #{params.startTime} OR ra.appointment_start_time &gt;
            #{params.endTime} )
        </if>
        ORDER BY ra.create_date DESC
    </select>

    <select id="listOrPageForAppoint" resultType="com.fykj.scaffold.zyz.domain.dto.ZyzResourceDto">
        SELECT
        r.id AS id,
        r.name AS name,
        r.type AS type,
        r.belong_field_name_top AS belongFieldNameTop,
        r.belong_field_name_end AS belongFieldNameEnd,
        r.publish_org_name AS publishOrgName,
        ra.org_name as orgName,
        ra.id as appointmentId,
        ra.contact_person AS contactPerson,
        ra.contact_phone AS contactPhone,
        ra.audit_status as auditStatus,
        ra.appointment_start_time AS appointmentStartTime,
        ra.appointment_end_time AS appointmentEndTime,
        ra.audit_end_time AS auditEndTime,
        ra.audit_start_time AS auditStartTime,
        ra.evaluate_star as activityAppraise
        FROM zyz_resource_appointment ra
        left join zyz_resource r on r.id=ra.resource_id
        left join sys_org so on so.code=ra.org_code
        left join sys_org sp on sp.code=r.publish_org_code
        WHERE ra.is_deleted = 0 and r.is_deleted =0
        <if test="params.myOrgCode != null and params.myOrgCode != ''">
            and sp.code_prefix LIKE CONCAT(#{params.myOrgCode},'%')
        </if>
        <if test="params.myTeamId != null">
            AND ra.team_id = #{params.myTeamId}
        </if>
        <if test="params.key != null and params.key != ''">
            AND ( r.name LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR r.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR r.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR ra.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR ra.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND sp.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
        </if>
        <if test="params.appointmentOrgCode != null and params.appointmentOrgCode != ''">
            and so.code_prefix LIKE CONCAT(#{params.appointmentOrgCode},'%')
        </if>
        <if test="params.type != null and params.type != ''">
            AND r.type = #{params.type}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND ra.audit_status = #{params.auditStatus}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (r.belong_field_top = #{params.fieldId} OR r.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( ra.appointment_end_time &lt; #{params.startTime} OR ra.appointment_start_time &gt;
            #{params.endTime} )
        </if>

        <if test="params.auditEndTimeStartTime != null and params.auditEndTimeEndTime != null">
            AND NOT ( ra.audit_end_time &lt; #{params.startTime} OR ra.audit_end_time &gt; #{params.endTime} )
        </if>
        ORDER BY ra.create_date DESC
    </select>

    <select id="getByIdForUpdate" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT *,
               is_auto_enable as autoEnable
        FROM zyz_resource
        WHERE is_deleted = 0
          AND id = #{id}
            FOR UPDATE
    </select>

    <select id="getNeedSync" resultType="java.lang.Long">
        SELECT zr.id
        FROM zyz_resource zr
        INNER JOIN (
            SELECT resource_id, MAX(operate_time) AS time
            FROM zyz_resource_log
            WHERE operate_type = 'res_audit' OR operate_type = 'res_submit' OR operate_type = 'res_act_docking_create'
            GROUP BY resource_id
        ) AS nt ON zr.id = nt.resource_id
        WHERE zr.is_deleted = 0
          AND zr.status = 'res_audit_success'
          AND nt.time &lt;= #{now}
          AND (zr.is_sync = 'sync_wait' OR (zr.is_sync = 'sync_failure' AND nt.time &gt;= #{threeDaysBeforeNow}))
    </select>

    <select id="getPagesForMini" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT
        t.*,
        t.is_sync AS sync
        FROM
        zyz_resource t
        LEFT JOIN sys_org o ON o.code = t.publish_org_code
        WHERE
        t.is_deleted = FALSE
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%',
            CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.type != null and params.type != ''">
            AND t.type = #{params.type}
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            <if test="params.publishOrgCode != 'top_dept'">
                and o.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
            </if>
            <if test="params.publishOrgCode == 'top_dept'">
                and o.code_prefix not LIKE CONCAT('top_dept,loufeng','%')
                and o.code_prefix not LIKE CONCAT('top_dept,xietang','%')
                and o.code_prefix not LIKE CONCAT('top_dept,weiting','%')
                and o.code_prefix not LIKE CONCAT('top_dept,shengpu','%')
                and o.code_prefix not LIKE CONCAT('top_dept,jinjihu','%')
            </if>
        </if>
        <if test="params.status != null and params.status != ''">
            AND t.status = #{params.status}
        </if>
        <if test="params.autoStatus != null">
            AND t.auto_status = #{params.autoStatus}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (t.belong_field_top = #{params.fieldId} OR t.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( t.end_time &lt; #{params.startTime} OR t.start_time &gt; #{params.endTime} )
        </if>
        <if test="params.publishStartTime!= null and params.publishEndTime != null">
            AND NOT ( t.publish_time &lt; #{params.publishStartTime} OR t.publish_time &gt; #{params.publishEndTime} )
        </if>
        ORDER BY create_date DESC
    </select>

    <select id="getResRecordPagesForMini" resultType="com.fykj.scaffold.zyz.domain.dto.AppointmentRecordPc">
        SELECT r.org_name               as orgName,
               r.appointment_start_time as startTime,
               r.appointment_end_time   as endTime,
               act.id                   as actId
        from zyz_resource_appointment r
                 left join zyz_activity act on r.id = act.res_id
        WHERE r.is_deleted = 0
          and r.audit_status = 'res_appointment_audit_success'
          and r.resource_id = #{params.resourceId}
        order by r.appointment_start_time desc
    </select>

    <select id="getResRecordListForMini" resultType="com.fykj.scaffold.zyz.domain.dto.AppointmentRecordPc">
        SELECT r.org_name               as orgName,
               r.appointment_start_time as startTime,
               r.appointment_end_time   as endTime,
               act.id                   as actId
        from zyz_resource_appointment r
                 left join zyz_activity act on r.link_act_id = act.id
        WHERE r.is_deleted = 0
          and r.audit_status = 'res_appointment_audit_success'
          and r.resource_id = #{resId}
        order by r.appointment_start_time desc
    </select>

    <select id="getRecordsBetweenDateRange" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT publish_time AS publishTime
        FROM zyz_resource
        WHERE is_deleted = 0
          AND status = #{status}
        <if test="start != null">
            AND publish_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND publish_time &lt;= #{end}
        </if>
        <if test="teamPublish != null">
            AND team_publish = #{teamPublish}
            <if test="teamPublish == true">
                AND team_id = #{publishTeam}
            </if>
            <if test="teamPublish == false">
                AND publish_org_code = #{publishOrg}
            </if>
        </if>
    </select>

    <select id="getPublishResNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_resource
        WHERE is_deleted = 0
        AND status = #{status}
        <if test="teamPublish != null">
            AND team_publish = #{teamPublish}
            <if test="teamPublish == true">
                AND team_id = #{teamId}
            </if>
            <if test="teamPublish == false">
                AND publish_org_code = #{orgCode}
            </if>
        </if>
    </select>

    <select id="getEnableDocking" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT
            *,
            CASE WHEN
            <if test="teamId != null">
                team_publish = 1 AND team_id = #{teamId} AND id != 999999999999999999 THEN 2
            </if>
            <if test="orgCode != null and orgCode != ''">
                team_publish = 0 AND publish_org_code = #{orgCode} AND id != 999999999999999999 THEN 2
            </if>
            WHEN id = 999999999999999999 THEN 1
            ELSE 0 END AS sort
        FROM zyz_resource
        WHERE
            is_deleted = 0 AND status = 'res_audit_success' AND auto_status = TRUE
        AND ( id = 999999999999999999
        <if test="teamId != null">
            OR (team_publish = 1 AND team_id = #{teamId})
        </if>
        <if test="orgCode != null and orgCode != ''">
            OR (team_publish = 0 AND publish_org_code = #{orgCode})
        </if>
        <if test="searchName != null and searchName != ''">
            OR name LIKE CONCAT('%', CONCAT(#{searchName}, '%'))
        </if>
        <if test="pointResourceId != null">
            OR id = #{pointResourceId}
        </if>
        )
        <if test="searchName != null and searchName != ''">
            AND name LIKE CONCAT('%', CONCAT(#{searchName}, '%'))
        </if>
        <if test="pointResourceId != null">
            AND id = #{pointResourceId}
        </if>
        ORDER BY sort DESC, publish_time DESC
    </select>

    <select id="sumNumByOrg" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto">
        SELECT
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            publish_org_code AS orgCode,
        </if>
            COUNT(id) AS sumNum
        FROM zyz_resource
        WHERE is_deleted = 0 AND status = 'res_audit_success' AND auto_status = 1
        <if test="params.start != null">
            AND publish_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND publish_time &lt;= #{params.end}
        </if>
        <if test="params.ignoreOrgCodes != null and params.ignoreOrgCodes.length > 0">
            <if test="params.ignoreOrgCodes.length == 1">
                AND publish_org_code != #{params.ignoreOrgCodes[0]}
            </if>
            <if test="params.ignoreOrgCodes.length > 1">
                AND publish_org_code NOT IN
                <foreach item="item" collection="params.ignoreOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND publish_org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND publish_org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            GROUP BY publish_org_code
        </if>
    </select>

    <select id="getScreenDataList" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.ReqResActTeamDataDto">
        SELECT
            name,
            type,
            belong_field_name_top AS belongField
        FROM zyz_resource
        WHERE is_deleted = 0 AND status = 'res_audit_success'
        <if test="params.start != null">
            AND publish_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND publish_time &lt;= #{params.end}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND publish_org_code = #{params.orgCode}
        </if>
        ORDER BY publish_time DESC
        LIMIT 100
    </select>

    <select id="getOrgResInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            SUM(IFNULL(nt.resNum, 0)) AS resourceNum
        FROM sys_org o
        LEFT JOIN (
            SELECT
                o.code_prefix,
                COUNT(r.id) AS resNum
            FROM zyz_resource r
            LEFT JOIN sys_org o ON r.publish_org_code = o.code
            WHERE r.is_deleted = 0 AND r.status = 'res_audit_success'
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                AND r.publish_org_code LIKE CONCAT(#{params.parentOrgCode}, '%')
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                AND r.publish_org_code != 'top_dept'
            </if>
            <if test="params.start != null">
                AND r.publish_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND r.publish_time &lt;= #{params.end}
            </if>
            GROUP BY o.code_prefix
        ) nt ON nt.code_prefix LIKE CONCAT(o.code_prefix, '%')
        <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
            WHERE o.code IN
            <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY o.code
        ORDER BY o.sequence
    </select>

    <select id="getTeamResInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            t.id AS teamId,
            t.name AS teamName,
            IFNULL(nt.resNum, 0) AS resourceNum
        FROM zyz_team t
        LEFT JOIN (
            SELECT
                r.team_id,
                COUNT(r.id) AS resNum
            FROM zyz_resource r
            WHERE r.is_deleted = 0 AND r.status = 'res_audit_success' AND r.team_publish = 1
            <if test="params.start != null">
                AND r.publish_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND r.publish_time &lt;= #{params.end}
            </if>
            GROUP BY r.team_id
        ) nt ON t.id = nt.team_id
        WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
            <if test="params.teamIdList != null and params.teamIdList.size > 0">
                AND t.id IN
                <foreach item="item" collection="params.teamIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>

    <select id="getOrgSelfResInfoSum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM zyz_resource
        WHERE is_deleted = 0 AND status = 'res_audit_success' AND publish_org_code = #{orgCode}
        <if test="start != null">
            AND publish_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND publish_time &lt;= #{end}
        </if>
    </select>

    <select id="getOtherOrgResInfoSum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM zyz_resource
        WHERE is_deleted = 0 AND status = 'res_audit_success'
            AND publish_org_code NOT LIKE 'jinjihu%'
            AND publish_org_code NOT LIKE 'loufeng%'
            AND publish_org_code NOT LIKE 'xietang%'
            AND publish_org_code NOT LIKE 'shengpu%'
            AND publish_org_code NOT LIKE 'weiting%'
            AND publish_org_code != 'top_dept'
        <if test="start != null">
            AND publish_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND publish_time &lt;= #{end}
        </if>
    </select>

    <select id="pageForApi" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT
            zr.id AS id,
            zr.`name` AS `name`,
            zr.`type` AS `type`,
            zr.belong_field_name_top AS belongFieldNameTop,
            zr.belong_field_name_end AS belongFieldNameEnd,
            zr.publish_org_code AS publishOrgCode,
            zr.publish_org_name AS publishOrgName,
            zr.start_time AS startTime,
            zr.end_time AS endTime,
            zr.publish_time AS publishTime,
            zr.picture AS picture
        FROM zyz_resource AS zr
        LEFT JOIN sys_org so ON zr.publish_org_code = so.code
        <where>
            AND zr.is_deleted = 0
            AND zr.status = 'res_audit_success'
            AND zr.auto_status = 1
            <if test="params.key != null and params.key != ''">
                AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%',
                CONCAT(#{params.key}, '%')) OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
            </if>
            <if test="params.needAudit != null and params.needAudit == true ">
                AND zr.audit_org_code = #{params.myOrgCode} AND zr.status = 'res_wait_audit'
            </if>
            <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
                AND zr.publish_org_code = #{params.publishOrgCode}
            </if>
            <if test="params.type != null and params.type != ''">
                AND zr.type = #{params.type}
            </if>
            <if test="params.fieldId != null and params.fieldId != ''">
                AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
            </if>
            <if test="params.status != null and params.status != ''">
                AND zr.status = #{params.status}
            </if>
            <if test="params.publishStartTime != null and params.publishEndTime != null">
                AND (
                    zr.publish_time &gt;= #{params.publishStartTime}
                    AND
                    zr.publish_time &lt;= #{params.publishEndTime}
                )
            </if>
            <if test="params.publishOrgCodeList != null and params.publishOrgCodeList.size > 0">
                AND zr.publish_org_code IN
                <foreach item="item" collection="params.publishOrgCodeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY zr.publish_time DESC
    </select>
    <select id="listNearby" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzResource">
        SELECT
            t.*,
            t.is_sync AS sync,
            ST_Distance_Sphere(
                point(t.longitude, t.latitude),
                point(#{params.longitude}, #{params.latitude})
            ) AS distance
        FROM zyz_resource t
        <where>
            AND t.is_deleted = 0
            AND t.status = 'res_audit_success'
            AND t.auto_status = 1
            <if test="params.key != null and params.key != ''">
                AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%',
                CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
            </if>
            <if test="params.type != null and params.type != ''">
                AND t.type = #{params.type}
            </if>
            <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
                and t.publish_org_code = #{params.publishOrgCode}
            </if>
            <if test="params.status != null and params.status != ''">
                AND t.status = #{params.status}
            </if>
            <if test="params.autoStatus != null">
                AND t.auto_status = #{params.autoStatus}
            </if>
            <if test="params.fieldId != null and params.fieldId != ''">
                AND (t.belong_field_top = #{params.fieldId} OR t.belong_field_end = #{params.fieldId})
            </if>
            <if test="params.startTime != null and params.endTime != null">
                AND NOT ( t.end_time &lt; #{params.startTime} OR t.start_time &gt; #{params.endTime} )
            </if>
            <if test="params.publishStartTime!= null and params.publishEndTime != null">
                AND NOT ( t.publish_time &lt; #{params.publishStartTime} OR t.publish_time &gt; #{params.publishEndTime} )
            </if>
            <if test="params.myOrgCode != null and params.myOrgCode != ''">
                and t.publish_org_code =#{params.myOrgCode}
                and t.team_publish = 0
            </if>
            <if test="params.myTeamId != null">
                AND t.team_id = #{params.myTeamId}
                and t.team_publish = 1
            </if>
            <if test="params.longitude != null and params.latitude != null and params.distance != null">
                AND ST_Distance_Sphere(
                    point(t.longitude, t.latitude),
                    point(#{params.longitude}, #{params.latitude})
                ) &lt;= 1000 * #{params.distance}
            </if>
            <if test="params.publishOrgCodeList != null and params.publishOrgCodeList.size > 0">
                AND t.publish_org_code IN
                <foreach item="item" collection="params.publishOrgCodeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY t.create_date DESC
    </select>
    <select id="countNearbyByType" resultType="com.fykj.scaffold.zyz.domain.dto.ZyzResourceNearbyCountDTO">
        SELECT
            t.`type` AS typeCode,
            COUNT(t.id) AS num,
            IFNULL(SUM(t.has_appointment_num),0) AS hasAppointmentNum
        FROM zyz_resource t
        <where>
            AND t.is_deleted = 0
            AND t.status = 'res_audit_success'
            AND t.auto_status = 1
            <if test="params.key != null and params.key != ''">
                AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%',
                CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
            </if>
            <if test="params.type != null and params.type != ''">
                AND t.type = #{params.type}
            </if>
            <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
                and t.publish_org_code = #{params.publishOrgCode}
            </if>
            <if test="params.status != null and params.status != ''">
                AND t.status = #{params.status}
            </if>
            <if test="params.autoStatus != null">
                AND t.auto_status = #{params.autoStatus}
            </if>
            <if test="params.fieldId != null and params.fieldId != ''">
                AND (t.belong_field_top = #{params.fieldId} OR t.belong_field_end = #{params.fieldId})
            </if>
            <if test="params.startTime != null and params.endTime != null">
                AND NOT ( t.end_time &lt; #{params.startTime} OR t.start_time &gt; #{params.endTime} )
            </if>
            <if test="params.publishStartTime!= null and params.publishEndTime != null">
                AND NOT ( t.publish_time &lt; #{params.publishStartTime} OR t.publish_time &gt; #{params.publishEndTime} )
            </if>
            <if test="params.myOrgCode != null and params.myOrgCode != ''">
                and t.publish_org_code =#{params.myOrgCode}
                and t.team_publish = 0
            </if>
            <if test="params.myTeamId != null">
                AND t.team_id = #{params.myTeamId}
                and t.team_publish = 1
            </if>
            <if test="params.longitude != null and params.latitude != null and params.distance != null">
                AND ST_Distance_Sphere(
                    point(t.longitude, t.latitude),
                    point(#{params.longitude}, #{params.latitude})
                ) &lt;= 1000 * #{params.distance}
            </if>
            <if test="params.publishOrgCodeList != null and params.publishOrgCodeList.size > 0">
                AND t.publish_org_code IN
                <foreach item="item" collection="params.publishOrgCodeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY t.`type`
    </select>
</mapper>
