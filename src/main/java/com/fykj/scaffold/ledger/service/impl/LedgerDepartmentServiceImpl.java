package com.fykj.scaffold.ledger.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.ledger.domain.dto.DepartmentImportDto;
import com.fykj.scaffold.ledger.domain.entity.LedgerDepartment;
import com.fykj.scaffold.ledger.domain.entity.LedgerIndexItem;
import com.fykj.scaffold.ledger.domain.params.LedgerDepartmentParams;
import com.fykj.scaffold.ledger.mapper.LedgerDepartmentMapper;
import com.fykj.scaffold.ledger.service.ILedgerDepartmentService;
import com.fykj.scaffold.ledger.service.ILedgerIndexItemService;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IUserRoleService;
import com.fykj.scaffold.security.business.service.IUserService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.support.utils.Oauth2Util;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import fykj.microservice.core.beans.vo.IdTextVo;
import fykj.microservice.core.support.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;
import utils.StringUtil;
import utils.UUIDUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.Cons.RoleCode.ROLE_CODE_DEPART_BUREAU_ADMIN;


/**
 * 台账责任局办-服务实现类
 *
 * @date 2025-05-26
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class LedgerDepartmentServiceImpl extends BaseServiceImpl<LedgerDepartmentMapper, LedgerDepartment> implements ILedgerDepartmentService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private ILedgerIndexItemService indexItemService;

    @Override
    public IPage<LedgerDepartment> pages(LedgerDepartmentParams params) {
        IPage<LedgerDepartment> result = baseMapper.pages(params.getPage(), params);
        DesensitiseUtil.desensitise(result.getRecords());
        return result;
    }

    @Override
    public LedgerDepartment get(Long id) {
        LedgerDepartment entity = super.getById(id);
        User user = userService.getById(entity.getUserId());
        entity.setName(user.getName());
        entity.setUsername(user.getUsername());
        entity.setMobile(user.getMobile());
        return entity;
    }

    @Override
    public boolean saveDepartment(LedgerDepartment department) {
        boolean exist = checkName(department.getDepartmentName(), null);
        if (exist) {
            throw new BusinessException(ResultCode.FAIL, "该责任局办已存在，请重新输入!");
        }
        boolean check = checkUserId(department.getUserId(), null);
        if (check) {
            throw new BusinessException(ResultCode.FAIL, "该用户已关联局办，请重新选择!");
        }
        userRoleService.addRole(department.getUserId(), ROLE_CODE_DEPART_BUREAU_ADMIN);
        return save(department);
    }

    @Override
    public boolean updateDepartmentById(LedgerDepartment department) {
        boolean exist = checkName(department.getDepartmentName(), department.getId());
        if (exist) {
            throw new BusinessException(ResultCode.FAIL, "该责任局办已存在，请重新输入!");
        }
        boolean check = checkUserId(department.getUserId(), department.getId());
        if (check) {
            throw new BusinessException(ResultCode.FAIL, "该用户已关联局办，请重新选择!");
        }
        userRoleService.addRole(department.getUserId(), ROLE_CODE_DEPART_BUREAU_ADMIN);
        return updateById(department);
    }

    private boolean checkName(String name, Long id) {
        return lambdaQuery().eq(LedgerDepartment::getDepartmentName, name)
                .ne(StringUtil.isNotEmpty(id), LedgerDepartment::getId, id).count() > 0;
    }

    @Override
    public Map<Long, String> getDepartmentMap() {
        return list().stream().collect(Collectors.toMap(
                LedgerDepartment::getId,
                LedgerDepartment::getDepartmentName,
                (a, b) -> b));
    }

    @Override
    public Map<String, Long> getDepartmentNameMap() {
        return list().stream().collect(Collectors.toMap(
                LedgerDepartment::getDepartmentName,
                LedgerDepartment::getId,
                (a, b) -> b));
    }

    @Override
    public boolean validUser() {
       /* if (!Oauth2Util.hasRole(ROLE_CODE_DEPART_BUREAU_ADMIN)) {
            throw new BusinessException(ResultCode.FAIL,"该账号无需测评台账报送");
        }*/
        LedgerDepartment department = getByUserId((Long) Oauth2Util.getUserId());
        if (department == null || !department.getStatus()) {
            throw new BusinessException(ResultCode.FAIL,"该账号无需测评台账报送");
        }
        return true;
    }

    public LedgerDepartment getByUserId(Long userId) {
        return lambdaQuery().eq(LedgerDepartment::getUserId, userId).one();
    }

    private boolean checkUserId(Long userId, Long id) {
        return lambdaQuery().eq(LedgerDepartment::getUserId, userId)
                .ne(StringUtil.isNotEmpty(id), LedgerDepartment::getId, id).count() > 0;
    }

    @Override
    public void setStatus(Long id) {
        LedgerDepartment department = getById(id);
        if (department.getStatus()) {
            boolean valid = indexItemService.lambdaQuery().eq(LedgerIndexItem::getDepartmentId, id).exists();
            if (valid) {
                throw new BusinessException(ResultCode.FAIL, "指标项中关联该局办，无法下架！");
            }
        }
        department.setStatus(!department.getStatus());
        updateById(department);
    }

    @Override
    public boolean deleteById(Long id) {
        boolean valid = indexItemService.lambdaQuery().eq(LedgerIndexItem::getDepartmentId, id).exists();
        if (valid) {
            throw new BusinessException(ResultCode.FAIL, "指标项中关联该局办，无法删除！");
        }
        return super.removeById(id);
    }


    @Override
    public List<DepartmentImportDto> dataImport(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "上传文件为空");
        }
        List<DepartmentImportDto> dataList = ExcelUtil.readExcel(
                excel, DepartmentImportDto.class, 0);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "导入数据为空，请确认Excel文件！");
        }
        List<String> nameList = baseMapper.getDepartmentName();

        // 收集手机号并统计出现次数
        Map<String, Long> mobileCountMap = dataList.stream()
                .map(DepartmentImportDto::getMobile)
                .filter(StringUtil::isNotEmpty)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        // 收集局办名称并统计出现次数
        Map<String, Long> deptNameCountMap = dataList.stream()
                .map(DepartmentImportDto::getDepartmentName)
                .filter(StringUtil::isNotEmpty)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        List<String> mobileList = new ArrayList<>(mobileCountMap.keySet());

        // 查询手机号对应的用户信息，并存入手机号和用户的映射关系
        Map<String, User> mobileUserMap = Optional.ofNullable(
                        userService.lambdaQuery().in(User::getMobile, mobileList).list()
                ).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(User::getMobile, Function.identity(), (a, b) -> a));


        // 系统中已使用的用户ID集合，用于校验手机号是否已被使用
        Set<Long> usedUserIds = list().stream()
                .map(LedgerDepartment::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<DepartmentImportDto> errorList = new ArrayList<>();
        List<LedgerDepartment> resultList = new ArrayList<>();
        for (DepartmentImportDto dto : dataList) {
            String error = validateRequired(dto, nameList, mobileUserMap, usedUserIds, mobileCountMap, deptNameCountMap);
            if (StringUtil.isNotEmpty(error)) {
                dto.setReason(error);
                errorList.add(dto);
                continue;
            }

            User user = getOrCreateUser(mobileUserMap, dto.getMobile(), dto.getDepartmentName());

            LedgerDepartment department = new LedgerDepartment();
            department.setDepartmentName(dto.getDepartmentName());
            department.setContactPerson(dto.getContactPerson());
            department.setContact(dto.getContact());
            department.setUserId(user.getId());

            resultList.add(department);
        }

        if (!CollectionUtils.isEmpty(resultList)) {
            saveBatch(resultList);
        }
        return errorList;
    }

    private String validateRequired(DepartmentImportDto dto,
                                    List<String> nameList,
                                    Map<String, User> mobileUserMap,
                                    Set<Long> usedUserIds,
                                    Map<String, Long> mobileCountMap,
                                    Map<String, Long> deptNameCountMap) {
        StringBuilder error = new StringBuilder();

        if (StringUtil.isEmpty(dto.getDepartmentName())) {
            error.append("局办名称不能为空;");
        }  else {
            if (nameList.contains(dto.getDepartmentName())) {
                error.append("局办名称已存在;");
            }
            Long count = deptNameCountMap.getOrDefault(dto.getDepartmentName(), 0L);
            if (count > 1) {
                error.append("局办名称在导入文件中重复;");
            }
        }

        if (StringUtil.isEmpty(dto.getContactPerson())) {
            error.append("联系人不能为空;");
        }

        if (StringUtil.isEmpty(dto.getContact())) {
            error.append("联系方式不能为空;");
        }

        String mobile = dto.getMobile();
        if (StringUtil.isEmpty(mobile)) {
            error.append("关联账号手机号不能为空;");
            return error.toString();
        }
        // 手机格式校验
        if (!PhoneUtil.isMobile(mobile)) {
            error.append("手机号格式不正确;");
        }
        // Excel中手机号重复校验
        Long count = mobileCountMap.get(mobile);
        if (count != null && count > 1) {
            error.append("手机号在导入文件中重复;");
        }

        // 已关联局办用户校验
        User existingUser = mobileUserMap.get(mobile);
        if (existingUser != null && usedUserIds.contains(existingUser.getId())) {
            error.append("该账号已被关联为局办用户;");
        }
        return error.toString();
    }


    private User getOrCreateUser(Map<String, User> mobileUserMap, String mobile, String departmentName) {
        User user = mobileUserMap.get(mobile);
        if (user != null) {
            // 已存在则赋予角色
            userRoleService.addRole(user.getId(), Cons.RoleCode.ROLE_CODE_DEPART_BUREAU_ADMIN);
            return user;
        }

        // 不存在则创建
        user = new User();
        user.setMobile(mobile);
        user.setUsername(mobile);
        user.setStatus(true);
        user.setPassword(UUIDUtils.generateUuid());
        user.setName(departmentName);
        user.setRoleCodes(CollectionUtil.newArrayList(Cons.RoleCode.ROLE_CODE_DEPART_BUREAU_ADMIN));
        user.setForceUpdatePwd(true);

        userService.save(user);
        // 同步更新缓存
        mobileUserMap.put(mobile, user);

        return user;
    }

    @Override
    public List<IdTextVo> selectList() {
        return lambdaQuery().eq(LedgerDepartment::getStatus, true)
                .orderByAsc(LedgerDepartment::getCreateDate).list().stream()
                .map(it -> new IdTextVo(it.getId(), it.getDepartmentName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getMobileByDepartmentIdList(List<String> departmentIdList) {
        return baseMapper.getMobileByDepartmentIdList(departmentIdList);
    }
}
