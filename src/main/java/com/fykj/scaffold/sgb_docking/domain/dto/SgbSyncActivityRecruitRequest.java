package com.fykj.scaffold.sgb_docking.domain.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 苏州社工部发布活动招募同步请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SgbSyncActivityRecruitRequest {

    /**
     * 唯一标识
     */
    private String uniCode;

    /**
     * 活动ID - 活动登记接口返回
     */
    private String apId;

    /**
     * 活动日期 - 格式：2016-09-30
     */
    private String actDate;

    /**
     * 开始时间 - 格式：HH:mm,24小时制
     */
    private String startTime;

    /**
     * 结束时间 - 格式：HH:mm,24小时制
     */
    private String endTime;

    /**
     * 构建请求JSON字符串
     * @return JSON字符串
     */
    public String buildJson() {
        // 构造嵌套的JSON结构
        SyncActivityRecruitInfo relacts = new SyncActivityRecruitInfo();
        relacts.setRelacts(this);
        return JSON.toJSONString(relacts);
    }

    /**
     * 嵌套的请求信息类
     */
    @Data
    public static class SyncActivityRecruitInfo {
        private SgbSyncActivityRecruitRequest relacts;
    }
}
