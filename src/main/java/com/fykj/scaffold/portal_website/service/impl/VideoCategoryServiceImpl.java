package com.fykj.scaffold.portal_website.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.cms.domain.entity.CmsCategory;
import com.fykj.scaffold.cms.domain.entity.CmsCategoryContent;
import com.fykj.scaffold.cms.service.ICmsCategoryContentService;
import com.fykj.scaffold.cms.service.ICmsCategoryService;
import com.fykj.scaffold.portal_website.domain.dto.VideoCategoryDto;
import com.fykj.scaffold.portal_website.domain.entity.Video;
import com.fykj.scaffold.portal_website.domain.entity.VideoCategory;
import com.fykj.scaffold.portal_website.domain.params.VideoCategoryParams;
import com.fykj.scaffold.portal_website.mapper.VideoCategoryMapper;
import com.fykj.scaffold.portal_website.service.IVideoCategoryService;
import com.fykj.scaffold.portal_website.service.IVideoService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import result.ResultCode;
import utils.StringUtil;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.fykj.scaffold.portal_website.conns.PortalWebsiteCons.*;

/**
 * <p>
 * 培训视频服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class VideoCategoryServiceImpl extends BaseServiceImpl<VideoCategoryMapper, VideoCategory> implements IVideoCategoryService {

    @Value("${httpAttachmentUrl}")
    public String httpAttachmentUrl;

    @Autowired
    private ICmsCategoryService categoryService;

    @Autowired
    private ICmsCategoryContentService categoryContentService;

    @Autowired
    private IVideoService videoService;

    @Override
    public boolean save(VideoCategory entity) {
        CmsCategory category;
        if (VIDEO_TYPE_TRAIN.equals(entity.getType())) {
            category = categoryService.getByCode(TRAIN_VIDEO_CMS_CODE);
            entity.setCategoryIds(Collections.singletonList(String.valueOf(category.getId())));
        } else {
            category = categoryService.getByCode(RESOURCE_VIDEO_CMS_CODE);
        }
        entity.setActualReading(0);
        super.save(entity);
        saveCategoryContent(entity, category);
        return true;
    }

    @Override
    public boolean updateById(VideoCategory entity) {
        categoryContentService.removeByContentId(entity.getId(), false);
        CmsCategory category;
        if (VIDEO_TYPE_TRAIN.equals(entity.getType())) {
            category = categoryService.getByCode(TRAIN_VIDEO_CMS_CODE);
            entity.setCategoryIds(Collections.singletonList(String.valueOf(category.getId())));
        } else {
            category = categoryService.getByCode(RESOURCE_VIDEO_CMS_CODE);
        }
        saveCategoryContent(entity, category);
        super.updateById(entity);
        return true;
    }

    private void saveCategoryContent(VideoCategory entity, CmsCategory category) {
        List<String> categoryIdList = entity.getCategoryIds();
        int size = categoryIdList.size();
        Long categoryId = Long.valueOf(categoryIdList.get(size - 1));
        CmsCategoryContent categoryContent = new CmsCategoryContent();
        categoryContent.setCategoryId(categoryId);
        categoryContent.setCategoryCode(categoryService.getById(categoryContent.getCategoryId()).getCode());
        categoryContent.setParentId(size == 1 ? (categoryIdList.get(0).equals(String.valueOf(category.getId())) ? null : category.getId()) : Long.valueOf(categoryIdList.get(size - 2)));
        categoryContent.setCategoryList(categoryIdList.contains(String.valueOf(category.getId())) ? categoryIdList.stream().map(String::valueOf).collect(Collectors.joining(Mark.COMMA)) : String.valueOf(category.getId()).concat(Mark.COMMA).concat(categoryIdList.stream().map(String::valueOf).collect(Collectors.joining(Mark.COMMA))));
        categoryContent.setContentId(entity.getId());
        categoryContent.setSequence(ObjectUtil.isEmpty(entity.getSequence()) ? 999 : entity.getSequence());
        categoryContentService.save(categoryContent);
    }

    @Override
    public VideoCategory getById(Serializable id) {
        VideoCategory videoContent = super.getById(id);
        List<CmsCategoryContent> categoryContents = categoryContentService.getByContentId(id, false);
        if (CollectionUtil.isEmpty(categoryContents)) {
            throw new BusinessException(ResultCode.FAIL, "数据类型获取失败！");
        }
        CmsCategoryContent categoryContent = categoryContents.get(0);
        List<String> categoryList = Arrays.asList(categoryContent.getCategoryList().split(Mark.COMMA));
        videoContent.setCategoryIds(categoryList.subList(1, categoryList.size()));
        videoContent.setSequence(categoryContent.getSequence());
        return dealVideoPath(videoContent);
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        super.removeByIds(list);
        list.forEach(it -> {
            categoryContentService.removeByContentId((Long)it, false);
        });
        return true;
    }

    @Override
    public IPage<VideoCategory> page(BaseParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new VideoCategoryParams();
        }
        Page<VideoCategory> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForVideoContent(page, (VideoCategoryParams) params).convert(this::convert);
    }

    private VideoCategory convert(VideoCategory record) {
        DictTransUtil.trans(record);
        if (StringUtil.isEmpty(record.getCategory())) {
            return dealVideoPath(record);
        }
        List<String> categoryIds = Arrays.asList(record.getCategory().split(Mark.COMMA));
        List<String> categoryName = new ArrayList<>();
        categoryIds.subList(1, categoryIds.size()).forEach(it -> {
            CmsCategory category = categoryService.getById(it);
            if (ObjectUtil.isNotEmpty(category)) {
                categoryName.add(categoryService.getById(it).getName());
            }
        });
        if (CollectionUtil.isNotEmpty(categoryName)) {
            record.setCategory(String.join(Mark.LINE, categoryName));
        }
        return dealVideoPath(record);
    }

    private VideoCategory dealVideoPath(VideoCategory record) {
        List<String> videos = StringUtil.isNotEmpty(record.getDescription()) ? Arrays.asList(record.getDescription().split(Mark.COMMA)) : null;
        if (videos != null && videos.size() > 0) {
            record.setVideoShow(new ArrayList<>());
            videos.forEach(it -> {
                Video video = videoService.lambdaQuery().eq(Video::getId, it).one();
                record.getVideoShow().add(ObjectUtil.isEmpty(video) ? it : httpAttachmentUrl.concat(video.getPath()));
            });
        }
        return record;
    }

    @Override
    public void ground(String id) {
        VideoCategory vc = getById(id);
        vc.setGrounding(!vc.getGrounding());
        super.updateById(vc);
    }

    @Override
    public IPage<VideoCategory> pageForApi(VideoCategoryParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new VideoCategoryParams();
        }
        Page<VideoCategory> page = new Page<>(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageForApi(page, params).convert(this::convert);
    }

    @Override
    public List<VideoCategoryDto> getTrainVideoForMini() {
        return baseMapper.getTrainVideo();
    }
}
