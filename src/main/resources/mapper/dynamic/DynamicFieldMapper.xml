<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.dynamic.mapper.DynamicFieldMapper">

    <select id="isFieldNameExist" resultType="java.lang.Boolean">
        SELECT EXISTS (
            SELECT 1
            FROM dynamic_field
            WHERE dynamic_field.field_name = #{fieldName}
        )
    </select>
    <select id="getByFieldName" resultType="com.fykj.scaffold.dynamic.domain.entity.DynamicField">
        SELECT *
        FROM dynamic_field
        WHERE dynamic_field.field_name = #{fieldName}
    </select>
</mapper>