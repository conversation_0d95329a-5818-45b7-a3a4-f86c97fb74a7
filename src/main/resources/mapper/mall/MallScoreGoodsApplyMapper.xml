<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.mall.mapper.MallScoreGoodsApplyMapper">
    <select id="getPage" resultType="com.fykj.scaffold.mall.domain.dto.MallScoreGoodsApplyDto">
        SELECT
            app.*,
            v.exchange_time,
            v.exchange_point,
            vo.service_long,
            v.exchange_before_score,
            v.exchange_after_score
        FROM  mall_score_goods_apply app
           JOIN  mall_exchange v ON app.exchange_id=v.id  AND v.is_deleted=0
          JOIN zyz_volunteer vo ON vo.id=app.volunteer_id AND vo.is_deleted=0 AND vo.write_off = 0
        where app.is_deleted=FALSE
        <if test="params.keyWord != null and params.keyWord != ''">
            AND (app.goods_name LIKE CONCAT('%',#{params.keyWord},'%')
            OR app.volunteer_name LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.guardian_name LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.guardian_phone LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.guardian_certificate_id LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.guardian_work_unit LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.participant_name LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.participant_phone LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.participant_certificate_id LIKE CONCAT('%',#{params.keyWord},'%')
            OR  app.participant_school LIKE CONCAT('%',#{params.keyWord},'%')
            )
        </if>
        <if test="params.startTime != null  ">
            AND v.exchange_time <![CDATA[ >= ]]> #{params.startTime}
        </if>
        <if test="params.endTime != null  ">
            AND v.exchange_time  <![CDATA[ <= ]]> #{params.endTime}
        </if>
        ORDER BY v.exchange_time DESC
    </select>
</mapper>
