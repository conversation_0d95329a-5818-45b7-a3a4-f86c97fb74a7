package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import static com.fykj.scaffold.support.conns.SysMsgTmpCons.ACT_APPLY_NOTICE_PUBLIC;


/**
 * 活动签到提醒
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityApplyPublicTask {

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzActivityService activityService;



    @XxlJob("Activity_Notice_Public_Apply_Tmp")
    public void activitySignNoticeSend() {
        XxlJobHelper.log("Activity_Notice_Public_Apply_Tmp-Job, START!");
        LocalDateTime timeStart = LocalDateTime.now().plusMinutes(25);
        LocalDateTime timeEnd = LocalDateTime.now().plusMinutes(30);
        actTimePeriodService.lambdaQuery()
                .gt(ZyzActivityTimePeriod::getStartTime, timeStart)
                .le(ZyzActivityTimePeriod::getStartTime, timeEnd)
                .list()
                .forEach(it -> sendTmpMsg.sendToActivityPublisher(ACT_APPLY_NOTICE_PUBLIC, Collections.singletonList(it.getActivityId()), activityService.getById(it.getActivityId()).getName()));
        XxlJobHelper.log("Activity_Notice_Public_Apply_Tmp-Job, END!");
    }
}
