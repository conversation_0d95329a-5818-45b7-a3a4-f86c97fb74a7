package com.fykj.scaffold.security.business.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 系统规则配置
 *
 * <AUTHOR> @email ${email}
 * @date 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_description")
public class SysDescription extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField("name")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * code
     */
    @TableField("code")
    @ApiModelProperty(value = "code")
    private String code;

    /**
     * 内容
     */
    @TableField("content")
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 类型，1文本，2富文本
     */
    @TableField("type")
    @ApiModelProperty(value = "类型")
    private String type;
}
