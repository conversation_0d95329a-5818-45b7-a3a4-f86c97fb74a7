package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.zyz.domain.entity.ZyzRequirementLog;

import java.util.List;

/**
 * 需求操作记录表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2023-02-24
 */
public interface IZyzRequirementLogService extends IService<ZyzRequirementLog>, IBaseService<ZyzRequirementLog> {


    /**
     * 保存操作记录
     * @param requirementId 需求Id, operateType 操作类型， remark 操作结果
     * @return
     */
     boolean saveLog(Long requirementId,String operateType,String remark);


    /**
     * 获取需求日志
     * @param requirementId 需求id
     * @return
     */
    List<ZyzRequirementLog> getLogsByRequirementId(Long requirementId);
}

