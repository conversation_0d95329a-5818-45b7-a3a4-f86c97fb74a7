package com.fykj.scaffold.portal_website.controller;


import com.fykj.scaffold.portal_website.domain.dto.ActivityForNewsDto;
import com.fykj.scaffold.portal_website.domain.dto.NewsAuditDto;
import com.fykj.scaffold.portal_website.domain.entity.News;
import com.fykj.scaffold.portal_website.domain.params.NewsParams;
import com.fykj.scaffold.portal_website.service.INewsService;
import com.fykj.scaffold.security.business.service.IDictService;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.MessagesApiUtil;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/admin/portal_website/news")
@Api(tags = "新闻资讯接口")
public class NewsController extends BaseController<INewsService, News, NewsParams> {

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private ISysOrgService orgService;

    @AuditLog("保存新闻")
    @Override
    public Result save(@RequestBody News entity) {
        boolean result = this.baseService.saveNews(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    @AuditLog("更新新闻")
    @Override
    public Result update(@RequestBody News entity) {
        boolean result = this.baseService.updateNews(entity);
        return result ? OK : new Result(ResultCode.FAIL);
    }

    /**
     * 设置置顶/取消置顶
     *
     * @param id id
     */
    @AuditLog("新闻资讯置顶操作")
    @ApiOperation(value = "设置置顶/取消置顶")
    @GetMapping(value = "/setStick")
    public Result setStick(@RequestParam Long id) {
        baseService.setStick(id);
        return new Result(ResultCode.OK);
    }

    @AuditLog("新闻资讯审核操作")
    @ApiOperation("审核")
    @PostMapping({"/audit"})
    public Result audit(@RequestBody NewsAuditDto audit) {
        baseService.audit(audit);
        return new Result(ResultCode.OK);
    }

    @AuditLog("新闻资讯批量审核操作")
    @ApiOperation("批量审核")
    @GetMapping({"/auditBatch"})
    public Result auditBatch(@RequestParam String ids, @RequestParam(required = false) String memo) {
        return baseService.auditBatch(ids, memo);
    }

    @AuditLog("新闻资讯上下架操作")
    @ApiOperation("新闻资讯上下架")
    @GetMapping({"/ground"})
    public Result ground(@RequestParam String id) {
        baseService.ground(id);
        return new Result(ResultCode.OK);
    }


    @ApiOperation("根据项目id查询新闻资讯")
    @GetMapping({"/getNewsByProjectId"})
    public JsonResult<List<News>> getNewsByProjectId(@RequestParam Long projectId) {
        return new JsonResult<>(baseService.getNewsByProjectId(projectId));
    }

    @ApiOperation("查询未关联项目的的团队发布新闻资讯列表")
    @GetMapping({"/getNewsByTeam"})
    public JsonResult<List<News>> getNewsByTeam(@RequestParam Long projectId) {
        return new JsonResult<>(baseService.getNewsByTeam(projectId));
    }


    @GetMapping("getActivityListForNews")
    @ApiOperation("通过当前组织的活动列表")
    public JsonResult<List<ActivityForNewsDto>> getActivityListForNews(Long newsId) {
        List<ActivityForNewsDto> res = activityService.getActivityListForNews(newsId);
        return new JsonResult<>(res);
    }

    @GetMapping("getActivityByNewsId")
    @ApiOperation("通过新闻关联的活动")
    public JsonResult<List<ActivityForNewsDto>> getActivityByNewsId(Long newsId) {
        List<ActivityForNewsDto> res = activityService.getActivityByNewsId(newsId);
        return new JsonResult<>(res);
    }


    @ApiOperation("推送同领域新闻")
    @GetMapping("/sendTagMessage")
    public Result sendTagMessage(long id) {
        //临时使用
        News news = baseService.getForApi(id);
        String content = "%s发布了%s新闻，如您感兴趣，可打开志愿服务小程序观看";
        content = String.format(content, news.getPublisher(), news.getTitle());
        MessagesApiUtil.sendShortMessage(content, dictService.getByCode("temp-tag-message").getValue());
        return OK;
    }
}
