<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzRequirementMapper">

    <select id="listOrPageForDocking" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT
            zr.id AS id,
            zr.name AS name,
            zr.type AS type,
            zr.belong_field_top AS belongFieldTop,
            zr.belong_field_end AS belongFieldEnd,
            zr.belong_field_name_top AS belongFieldNameTop,
            zr.belong_field_name_end AS belongFieldNameEnd,
            zr.service_obj AS serviceObj,
            zr.publish_org_name AS publishOrgName,
            zr.contact_person AS contactPerson,
            zr.contact_phone AS contactPhone,
            zr.start_time AS startTime,
            zr.end_time AS endTime,
            zr.status AS status,
            zr.docking_org_name AS dockingOrgName,
            zr.docking_time AS dockingTime
        FROM zyz_requirement AS zr
        LEFT JOIN sys_org so ON zr.publish_org_code = so.code
        WHERE zr.is_deleted = 0 AND ( zr.status = 'ras_wait_docking' OR zr.status = 'ras_docking_success' ) AND zr.auto_status = TRUE
        <if test="params.key != null and params.key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and so.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
        </if>
<!--        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">-->
<!--            AND publish_org_code = #{params.publishOrgCode}-->
<!--        </if>-->
        <if test="params.type != null and params.type != ''">
            AND zr.type = #{params.type}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.serviceObj != null and params.serviceObj != ''">
            AND zr.service_obj = #{params.serviceObj}
        </if>
        <if test="params.status != null and params.status != ''">
            AND zr.status = #{params.status}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( zr.end_time &lt; #{params.startTime} OR zr.start_time &gt; #{params.endTime} )
        </if>
        ORDER BY zr.create_date DESC
    </select>

    <select id="getPages" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT
            t.*,
            t.is_sync AS sync,
            CASE WHEN dr.req_id IS NULL THEN FALSE ELSE TRUE END AS actDocked,
            CASE WHEN t.team_publish = TRUE THEN FALSE
            ELSE IF(o.level = 2 OR o.level = 3, TRUE, FALSE) END AS dockingZSQ
        FROM
            zyz_requirement t
        LEFT JOIN sys_org o ON o.code = t.publish_org_code
        LEFT JOIN (SELECT req_id FROM zyz_activity WHERE is_deleted = 0 AND req_id IS NOT NULL) dr ON t.id = dr.req_id
        WHERE
            t.is_deleted = FALSE
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.type != null and params.type != ''">
            AND t.type = #{params.type}
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            and t.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.serviceObj != null and params.serviceObj != ''">
            AND t.service_obj = #{params.serviceObj}
        </if>
        <if test="params.status != null and params.status != ''">
            AND t.status = #{params.status}
        </if>
        <if test="params.autoStatus != null and params.autoStatus != ''">
            AND t.auto_status = #{params.autoStatus}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (t.belong_field_top = #{params.fieldId} OR t.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( t.end_time &lt; #{params.startTime} OR t.start_time &gt; #{params.endTime} )
        </if>
        ORDER BY create_date DESC
    </select>

    <select id="listOrPageForMyDocking" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT
            zr.id AS id,
            zr.name AS name,
            zr.type AS type,
            zr.belong_field_top AS belongFieldTop,
            zr.belong_field_end AS belongFieldEnd,
            zr.belong_field_name_top AS belongFieldNameTop,
            zr.belong_field_name_end AS belongFieldNameEnd,
            zr.service_obj AS serviceObj,
            zr.publish_org_name AS publishOrgName,
            zr.contact_person AS contactPerson,
            zr.contact_phone AS contactPhone,
            zr.start_time AS startTime,
            zr.end_time AS endTime,
            zr.picture AS picture,
            zr.docking_time AS dockingTime
        FROM zyz_requirement AS zr
        LEFT JOIN sys_org so ON zr.publish_org_code = so.code
        WHERE zr.is_deleted = 0 AND zr.status = 'ras_docking_success'
        <if test="params.myOrgCode != null and params.myOrgCode != ''">
            AND zr.docking_org_code = #{params.myOrgCode}
        </if>
        <if test="params.dockingTeam != null">
            AND zr.docking_team = #{params.dockingTeam}
        </if>
        <if test="params.myTeamId != null">
            AND zr.docking_team_id = #{params.myTeamId}
        </if>
        <if test="params.key != null and params.key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND so.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
        </if>
        <if test="params.type != null and params.type != ''">
            AND zr.type = #{params.type}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.serviceObj != null and params.serviceObj != ''">
            AND zr.service_obj = #{params.serviceObj}
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( zr.end_time &lt; #{params.startTime} OR zr.start_time &gt; #{params.endTime} )
        </if>
        ORDER BY zr.create_date DESC
    </select>

    <select id="pageForAudit" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT
        zr.id AS id,
        zr.name AS name,
        zr.type AS type,
        zr.belong_field_top AS belongFieldTop,
        zr.belong_field_end AS belongFieldEnd,
        zr.belong_field_name_top AS belongFieldNameTop,
        zr.belong_field_name_end AS belongFieldNameEnd,
        zr.service_obj AS serviceObj,
        zr.publish_org_name AS publishOrgName,
        zr.contact_person AS contactPerson,
        zr.contact_phone AS contactPhone,
        zr.start_time AS startTime,
        zr.end_time AS endTime,
        zr.status AS status,
        zr.audit_org_code as auditOrgCode,
        ao.name as auditOrgName,
        zr.create_date AS createDate
        FROM zyz_requirement AS zr
        LEFT JOIN sys_org so ON zr.publish_org_code = so.code
        LEFT JOIN sys_org ao ON zr.audit_org_code = ao.code
        WHERE zr.is_deleted = 0 AND zr.status != 'ras_draft'
        <if test="params.key != null and params.key != ''">
            AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.needAudit != null and params.needAudit == true ">
            AND zr.audit_org_code = #{params.myOrgCode} AND zr.status = 'ras_wait_audit'
        </if>
        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            AND zr.publish_org_code = #{params.publishOrgCode}
        </if>
        <if test="params.type != null and params.type != ''">
            AND zr.type = #{params.type}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.serviceObj != null and params.serviceObj != ''">
            AND zr.service_obj = #{params.serviceObj}
        </if>
        <if test="params.status != null and params.status != ''">
            AND zr.status = #{params.status}
<!--          <if test="params.status == 'ras_wait_docking'">-->
<!--            AND (zr.status = 'ras_wait_docking' OR zr.status = 'ras_docking_success')-->
<!--          </if>-->
<!--          <if test="params.status != 'ras_wait_docking'">-->
<!--            AND zr.status = #{params.status}-->
<!--          </if>-->
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( zr.end_time &lt; #{params.startTime} OR zr.start_time &gt; #{params.endTime} )
        </if>
        ORDER BY zr.create_date DESC
    </select>

    <select id="getReqWaitAuditNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_requirement
        WHERE is_deleted = 0 AND status = #{status} AND audit_org_code = #{myOrgCode}
    </select>

    <select id="getEnableDocking" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT * FROM zyz_requirement
        WHERE
          is_deleted = 0 AND status = 'ras_wait_docking'
          AND id NOT IN (
              SELECT req_id
              FROM zyz_activity
              WHERE is_deleted = 0 AND audit_status != ''
              <if test="actId != null">
                  AND id != #{actId}
              </if>
               AND req_id IS NOT NULL
              <if test="reqIdDocked != null">
                  AND req_id != #{reqIdDocked}
              </if>
              )
          AND ((team_publish = 0 AND publish_org_code = #{parentOrgCode})
        <if test="teamId != null">
            OR (team_publish = 1 AND team_id = #{teamId}))
        </if>
        <if test="orgCode != null and orgCode != ''">
            OR (team_publish = 0 AND publish_org_code = #{orgCode}))
        </if>
    </select>
    <select id="getPagesForMini" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT
        t.*,
        CASE WHEN t.status = 'ras_wait_docking' THEN 1 ELSE 0 END AS sort,
        za.id AS linkActivityId,
        za.name AS linkActivityName,
        za.apply_end_time AS linkActivityApplyEndTime,
        za.audit_status AS activityStatus,
        t.is_sync AS sync
        FROM
        zyz_requirement t
        LEFT JOIN sys_org o ON o.code = t.publish_org_code
        left join zyz_activity za on za.req_id = t.id
        WHERE
        t.is_deleted = FALSE
        AND (t.status = 'ras_wait_docking' OR t.status = 'ras_docking_success')
        <if test="params.key != null and params.key != ''">
            AND ( t.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR t.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
        </if>
        <if test="params.type != null and params.type != ''">
            AND t.type = #{params.type}
        </if>

        <if test="params.publishOrgCode != null and params.publishOrgCode != ''">
            <if test="params.publishOrgCode != 'top_dept'">
                and o.code_prefix LIKE CONCAT(#{params.publishOrgCode},'%')
            </if>
            <if test="params.publishOrgCode == 'top_dept'">
                and o.code_prefix not LIKE CONCAT('top_dept,loufeng','%')
                and o.code_prefix not LIKE CONCAT('top_dept,xietang','%')
                and o.code_prefix not LIKE CONCAT('top_dept,weiting','%')
                and o.code_prefix not LIKE CONCAT('top_dept,shengpu','%')
                and o.code_prefix not LIKE CONCAT('top_dept,jinjihu','%')
            </if>
        </if>
        <if test="params.affiliatedOrgCode != null and params.affiliatedOrgCode != ''">
            AND t.publish_org_code = #{params.affiliatedOrgCode}
        </if>
        <if test="params.serviceObj != null and params.serviceObj != ''">
            AND t.service_obj = #{params.serviceObj}
        </if>
        <if test="params.status != null and params.status != ''">
            AND t.status = #{params.status}
        </if>
        <if test="params.autoStatus != null and params.autoStatus != ''">
            AND t.auto_status = #{params.autoStatus}
        </if>
        <if test="params.fieldId != null and params.fieldId != ''">
            AND (t.belong_field_top = #{params.fieldId} OR t.belong_field_end = #{params.fieldId})
        </if>
        <if test="params.startTime != null and params.endTime != null">
            AND NOT ( t.end_time &lt; #{params.startTime} OR t.start_time &gt; #{params.endTime} )
        </if>
        ORDER BY sort DESC, create_date DESC
    </select>

    <select id="getNeedSync" resultType="java.lang.Long">
        SELECT zr.id
        FROM zyz_requirement zr
        INNER JOIN (
            SELECT requirement_id, MAX(operate_time) AS time
            FROM zyz_requirement_log
            WHERE operate_type = 'RO_audit' OR operate_type = 'RO_submit' OR operate_type = 'RO_act_docking_create'
            GROUP BY requirement_id
        ) AS nt ON zr.id = nt.requirement_id
        WHERE zr.is_deleted = 0
          AND zr.status = 'ras_wait_docking'
          AND nt.time &lt;= #{now}
          AND (zr.is_sync = 'sync_wait' OR (zr.is_sync = 'sync_failure' AND nt.time &gt;= #{threeDaysBeforeNow}))
    </select>

    <select id="getRecordsBetweenDateRange" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT create_date AS createDate
        FROM zyz_requirement
        WHERE is_deleted = 0 AND status = #{waitDockingStatus}
        <if test="start != null">
            AND create_date &gt;= #{start}
        </if>
        <if test="end != null">
            AND create_date &lt;= #{end}
        </if>
        <if test="teamPublish != null">
            AND team_publish = #{teamPublish}
            <if test="teamPublish == true">
                AND team_id = #{publishTeam}
            </if>
            <if test="teamPublish == false">
                AND publish_org_code = #{publishOrg}
            </if>
        </if>
    </select>

    <select id="getDockingReqNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_requirement
        WHERE is_deleted = 0 AND status = #{dockingSuccessStatus}
        <if test="teamDocking != null">
            AND docking_team = #{teamDocking}
            <if test="teamDocking == true">
                AND docking_team_id = #{teamId}
            </if>
            <if test="teamDocking == false">
                AND docking_org_code = #{orgCode}
            </if>
        </if>
    </select>

    <select id="getPublishReqNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM zyz_requirement
        WHERE is_deleted = 0 AND (status = #{waitDockingStatus} OR status = #{dockingSuccessStatus})
        <if test="teamPublish != null">
            AND team_publish = #{teamPublish}
            <if test="teamPublish == true">
                AND team_id = #{teamId}
            </if>
            <if test="teamPublish == false">
                AND publish_org_code = #{orgCode}
            </if>
        </if>
    </select>

    <select id="sumNumByOrg" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.OrgSumDto">
        SELECT
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            publish_org_code AS orgCode,
        </if>
            COUNT(id) AS sumNum
        FROM zyz_requirement
        WHERE is_deleted = 0 AND (status = 'ras_wait_docking' OR status = 'ras_docking_success')
        <if test="params.start != null">
            AND create_date &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND create_date &lt;= #{params.end}
        </if>
        <if test="params.ignoreOrgCodes != null and params.ignoreOrgCodes.length > 0">
            <if test="params.ignoreOrgCodes.length == 1">
                AND publish_org_code != #{params.ignoreOrgCodes[0]}
            </if>
            <if test="params.ignoreOrgCodes.length > 1">
                AND publish_org_code NOT IN
                <foreach item="item" collection="params.ignoreOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
            <if test="params.containOrgCodes.length == 1">
                AND publish_org_code = #{params.containOrgCodes[0]}
            </if>
            <if test="params.containOrgCodes.length > 1">
                AND publish_org_code IN
                <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="(params.needGroupBy == null or params.needGroupBy == true) and (params.containOrgCodes == null or params.containOrgCodes.length == 0 or params.containOrgCodes.length > 1)">
            GROUP BY publish_org_code
        </if>
    </select>

    <select id="sumReqResTypePercent" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.NumPercentSumDto">
        SELECT
            rt.name,
            IFNULL(req.num, 0) + IFNULL(res.num, 0) AS num
        FROM (
            SELECT code, name
            FROM sys_dict
            WHERE is_deleted = 0 AND parent_id = ( SELECT id FROM sys_dict WHERE CODE = 'REQUIREMENT_TYPE' )
        ) AS rt
        LEFT JOIN (
            SELECT type, COUNT(id) AS num
            FROM zyz_requirement
            WHERE is_deleted = 0 AND (status = 'ras_wait_docking' OR status = 'ras_docking_success')
            <if test="params.start != null">
                AND create_date &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND create_date &lt;= #{params.end}
            </if>
            <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
                <if test="params.containOrgCodes.length == 1">
                    AND publish_org_code = #{params.containOrgCodes[0]}
                </if>
                <if test="params.containOrgCodes.length > 1">
                    AND publish_org_code IN
                    <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
            </if>
            GROUP BY type
        ) AS req ON rt.code = req.type
        LEFT JOIN (
            SELECT type, COUNT(id) AS num
            FROM zyz_resource
            WHERE is_deleted = 0 AND status = 'res_audit_success'
            <if test="params.start != null">
                AND publish_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND publish_time &lt;= #{params.end}
            </if>
            <if test="params.containOrgCodes != null and params.containOrgCodes.length > 0">
                <if test="params.containOrgCodes.length == 1">
                    AND publish_org_code = #{params.containOrgCodes[0]}
                </if>
                <if test="params.containOrgCodes.length > 1">
                    AND publish_org_code IN
                    <foreach item="item" collection="params.containOrgCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
            </if>
            GROUP BY type
        ) AS res ON rt.code = res.type
        ORDER BY num DESC
    </select>

    <select id="getScreenDataList" resultType="com.fykj.scaffold.zyz.domain.dto.data_screen.ReqResActTeamDataDto">
        SELECT
            name,
            type,
            belong_field_name_top AS belongField
        FROM zyz_requirement
        WHERE is_deleted = 0 AND (status = 'ras_wait_docking' OR status = 'ras_docking_success')
        <if test="params.start != null">
            AND create_date &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND create_date &lt;= #{params.end}
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            AND publish_org_code = #{params.orgCode}
        </if>
        ORDER BY create_date DESC
        LIMIT 100
    </select>

    <select id="getOrgReqInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            o.code AS orgCode,
            o.name AS orgName,
            SUM(IFNULL(nt.reqNum, 0)) AS requirementNum
        FROM sys_org o
        LEFT JOIN (
            SELECT
                o.code_prefix,
                COUNT(r.id) AS reqNum
            FROM zyz_requirement r
            LEFT JOIN sys_org o ON r.publish_org_code = o.code
            WHERE r.is_deleted = 0 AND (r.status = 'ras_wait_docking' OR r.status = 'ras_docking_success')
            <if test="params.parentOrgCode == 'jinjihu' or params.parentOrgCode == 'loufeng' or params.parentOrgCode == 'xietang' or params.parentOrgCode == 'weiting' or params.parentOrgCode == 'shengpu'">
                AND r.publish_org_code LIKE CONCAT(#{params.parentOrgCode}, '%')
            </if>
            <if test="params.parentOrgCode == 'top_dept'">
                AND r.publish_org_code != 'top_dept'
            </if>
            <if test="params.start != null">
                AND r.start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND r.start_time &lt;= #{params.end}
            </if>
            GROUP BY o.code_prefix
        ) nt ON nt.code_prefix LIKE CONCAT(o.code_prefix, '%')
        <if test="params.subOrgCodes != null and params.subOrgCodes.size > 0">
            WHERE o.code IN
            <foreach item="item" collection="params.subOrgCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY o.code
        ORDER BY o.sequence
    </select>

    <select id="getTeamReqInfoSum" resultType="com.fykj.scaffold.zyz.domain.dto.report_form.OrgDataFormDto">
        SELECT
            t.id AS teamId,
            t.name AS teamName,
            IFNULL(nt.reqNum, 0) AS requirementNum
        FROM zyz_team t
        LEFT JOIN (
            SELECT
                r.team_id,
                COUNT(r.id) AS reqNum
            FROM zyz_requirement r
            WHERE r.is_deleted = 0  AND (r.status = 'ras_wait_docking' OR r.status = 'ras_docking_success') AND r.team_publish = 1
            <if test="params.start != null">
                AND r.start_time &gt;= #{params.start}
            </if>
            <if test="params.end != null">
                AND r.start_time &lt;= #{params.end}
            </if>
            GROUP BY r.team_id
        ) nt ON t.id = nt.team_id
        WHERE t.is_deleted = 0 AND t.team_status = 'team_audit_pass'
        <if test="params.teamId != null">
            AND t.id = #{params.teamId}
        </if>
        <if test="params.teamId == null">
            <if test="params.teamName != null and params.teamName != ''">
                AND t.name LIKE CONCAT('%', CONCAT(#{params.teamName}, '%'))
            </if>
            <if test="params.teamIdList != null and params.teamIdList.size > 0">
                AND t.id IN
                <foreach item="item" collection="params.teamIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>

    <select id="getOrgSelfReqInfoSum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM zyz_requirement
        WHERE is_deleted = 0 AND (status = 'ras_wait_docking' OR status = 'ras_docking_success') AND publish_org_code = #{orgCode}
        <if test="start != null">
            AND start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND start_time &lt;= #{end}
        </if>
    </select>

    <select id="getOtherOrgReqInfoSum" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM zyz_requirement
        WHERE is_deleted = 0 AND (status = 'ras_wait_docking' OR status = 'ras_docking_success')
        AND publish_org_code NOT LIKE 'jinjihu%'
        AND publish_org_code NOT LIKE 'loufeng%'
        AND publish_org_code NOT LIKE 'xietang%'
        AND publish_org_code NOT LIKE 'shengpu%'
        AND publish_org_code NOT LIKE 'weiting%'
        AND publish_org_code != 'top_dept'
        <if test="start != null">
            AND start_time &gt;= #{start}
        </if>
        <if test="end != null">
            AND start_time &lt;= #{end}
        </if>
    </select>
    <select id="pageForApi" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzRequirement">
        SELECT
            zr.id AS id,
            zr.`name` AS `name`,
            zr.`type` AS `type`,
            zr.belong_field_top AS belongFieldTop,
            zr.belong_field_end AS belongFieldEnd,
            zr.belong_field_name_top AS belongFieldNameTop,
            zr.belong_field_name_end AS belongFieldNameEnd,
            zr.service_obj AS serviceObj,
            zr.publish_org_name AS publishOrgName,
            zr.contact_person AS contactPerson,
            zr.contact_phone AS contactPhone,
            zr.start_time AS startTime,
            zr.end_time AS endTime,
            zr.`status` AS `status`,
            zr.picture AS picture
        FROM zyz_requirement AS zr
        <where>
          AND zr.is_deleted = 0
          AND ( zr.status = 'ras_wait_docking' OR zr.status = 'ras_docking_success' )
          AND zr.auto_status = TRUE
            <if test="params.key != null and params.key != ''">
                AND ( zr.name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_person LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zr.contact_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%')) )
            </if>
            <if test="params.type != null and params.type != ''">
                AND zr.type = #{params.type}
            </if>
            <if test="params.fieldId != null and params.fieldId != ''">
                AND (zr.belong_field_top = #{params.fieldId} OR zr.belong_field_end = #{params.fieldId})
            </if>
            <if test="params.serviceObj != null and params.serviceObj != ''">
                AND zr.service_obj = #{params.serviceObj}
            </if>
            <if test="params.status != null and params.status != ''">
                AND zr.status = #{params.status}
            </if>
            <if test="params.startTime != null and params.endTime != null">
                AND NOT ( zr.end_time &lt; #{params.startTime} OR zr.start_time &gt; #{params.endTime} )
            </if>
            <if test="params.publishOrgCodeList != null and params.publishOrgCodeList.size > 0">
                AND zr.publish_org_code IN
                <foreach item="item" collection="params.publishOrgCodeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY zr.create_date DESC
    </select>

    <select id="pageResourcesByRequirementId" resultType="com.fykj.scaffold.zyz.domain.dto.ResourceByRequirementDto">
        SELECT 
            r.id,
            r.picture,
            r.name,
            r.type,
            r.publish_org_name as publishOrgName,
            r.start_time as startTime,
            r.end_time as endTime,
            r.status
        FROM zyz_resource r
        INNER JOIN zyz_activity a ON r.id = a.res_id
        WHERE a.req_id = #{params.reqId}
          AND r.is_deleted = 0
          AND ( r.status = 'ras_wait_docking' OR r.status = 'ras_docking_success' OR r.status = 'res_audit_success')
        ORDER BY r.create_date DESC
    </select>
</mapper>
