package com.fykj.scaffold.security.business.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class OrgMapDto {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 部门code
     */

    @ApiModelProperty(value = "部门code")
    private String code;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;


    /**
     * 层级（1协会，2分协会，3社区，4小区（预留））
     */
    @ApiModelProperty(value = "层级（1协会，2分协会，3社区，4小区（预留））")
    private Integer level;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * gps84经度
     */
    @ApiModelProperty(value = "gps84经度")
    private BigDecimal wsg84Longitude;

    /**
     * gps84纬度
     */
    @ApiModelProperty(value = "gps84纬度")
    private BigDecimal wsg84Latitude;

}
