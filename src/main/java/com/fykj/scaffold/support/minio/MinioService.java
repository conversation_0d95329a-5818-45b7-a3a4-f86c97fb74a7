package com.fykj.scaffold.support.minio;


import com.fykj.scaffold.security.business.domain.entity.OssConfig;
import com.fykj.scaffold.security.business.service.IOssConfigService;
import exception.BusinessException;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import result.ResultCode;

import java.io.InputStream;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MinIO工具类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MinioService {
    /**
     * MinioClient实例
     */
    private MinioClient minioClient;

    @Autowired
    private IOssConfigService ossConfigService;

    private OssConfig config;

    /**
     * 分隔符
     */
    public static final String SEPARATOR = "/";

    private MinioService() {
    }

    /**
     * 创建基于Java端的MinioClient
     */
    public void initMinioClient() {
        try {
            if (null == minioClient) {
                this.config = ossConfigService.getConfig();
                log.info("开始创建MinioClient...");
                minioClient = MinioClient.builder()
                        .endpoint(config.getMinioEndpoint())
                        .credentials(config.getMinioAccessKey(), config.getMinioSecret())
                        .build();
                String bucket = config.getMinioBucket();
                boolean bucketExist = bucketExists(bucket);
                if (!bucketExist) {
                    createBucket(config.getMinioBucket());
                }
                log.info("MinioClient创建完毕...");
            }
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "连接MinIO服务器失败", e);
        }
    }

    /**
     * 获取上传文件前缀路径
     *
     * @return 前缀路径
     */
    public String getBasisUrl() {
        return config.getMinioEndpoint() + SEPARATOR + config.getMinioBucket() + SEPARATOR;
    }

    /**
     * 启动SpringBoot容器的时候初始化Bucket，如果没有Bucket则创建
     */
    public void createBucket(String bucketName) {
        if (bucketExists(bucketName)) {
            return;
        }
        MakeBucketArgs args = MakeBucketArgs.builder().bucket(bucketName).build();
        try {
            minioClient.makeBucket(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "初始化Bucket失败", e);
        }
    }

    /**
     * 判断Bucket是否存在
     *
     * @return true：存在，false：不存在
     */
    public boolean bucketExists(String bucketName) {
        BucketExistsArgs args = BucketExistsArgs.builder().bucket(bucketName).build();
        try {
            return minioClient.bucketExists(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "查询Bucket失败", e);
        }
    }

    /**
     * 获得Bucket策略
     *
     * @param bucketName 存储桶名称
     * @return Bucket策略
     */
    public String getBucketPolicy(String bucketName) {
        GetBucketPolicyArgs args = GetBucketPolicyArgs.builder().bucket(bucketName).build();
        try {
            return minioClient.getBucketPolicy(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "获得Bucket策略失败", e);
        }
    }


    /**
     * 获得所有Bucket列表
     *
     * @return Bucket列表
     */
    public List<Bucket> getAllBuckets() {
        try {
            return minioClient.listBuckets();
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "获得Bucket列表失败", e);
        }
    }

    /**
     * 根据存储桶名称获取其相关信息
     *
     * @param bucketName 存储桶名称
     * @return 相关信息
     */
    public Optional<Bucket> getBucket(String bucketName) {
        return getAllBuckets().stream().filter(b -> b.name().equals(bucketName)).findFirst();
    }

    /**
     * 根据存储桶名称删除Bucket，true：删除成功；false：删除失败，文件或已不存在
     *
     * @param bucketName 存储桶名称
     */
    public void removeBucket(String bucketName) {
        RemoveBucketArgs args = RemoveBucketArgs.builder().bucket(bucketName).build();
        try {
            minioClient.removeBucket(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "删除Bucket失败", e);
        }
    }

    /**
     * 判断文件是否存在
     *
     * @param objectName 文件名
     * @return true：存在；false：不存在
     */
    public boolean isObjectExist(String objectName) {
        return isObjectExist(config.getMinioBucket(), objectName);
    }

    /**
     * 判断文件是否存在
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件名
     * @return true：存在；false：不存在
     */
    public boolean isObjectExist(String bucketName, String objectName) {
        try {
            StatObjectArgs args = StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build();
            minioClient.statObject(args);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断文件夹是否存在
     *
     * @param objectName 文件夹名称
     * @return true：存在；false：不存在
     */
    public boolean isFolderExist(String objectName) {
        return isFolderExist(config.getMinioBucket(), objectName);
    }

    /**
     * 判断文件夹是否存在
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件夹名称
     * @return true：存在；false：不存在
     */
    public boolean isFolderExist(String bucketName, String objectName) {
        try {
            List<Item> list = getAllObjectsByPrefix(bucketName, objectName, false);
            return list.stream()
                    .filter(Item::isDir)
                    .anyMatch(it -> objectName.equals(it.objectName()));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 根据文件前缀查询文件
     *
     * @param prefix    前缀
     * @param recursive 是否使用递归查询
     * @return MinioItem列表
     */
    public List<Item> getAllObjectsByPrefix(String prefix, boolean recursive) {
        return getAllObjectsByPrefix(config.getMinioBucket(), prefix, recursive);
    }

    /**
     * 根据文件前缀查询文件
     *
     * @param bucketName 存储桶名称
     * @param prefix     前缀
     * @param recursive  是否使用递归查询
     * @return MinioItem列表
     */
    public List<Item> getAllObjectsByPrefix(String bucketName, String prefix, boolean recursive) {
        try {
            Iterable<Result<Item>> results = listObjects(bucketName, prefix, recursive);
            List<Item> list = new ArrayList<>();
            for (Result<Item> o : results) {
                Item item = o.get();
                list.add(item);
            }
            return list;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "删除Bucket失败", e);
        }
    }

    /**
     * 获取文件流
     *
     * @param objectName 文件名
     * @return 二进制流
     */
    public InputStream getObject(String objectName) {
        return getObject(config.getMinioBucket(), objectName);
    }

    /**
     * 获取文件流
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件名
     * @return 二进制流
     */
    public InputStream getObject(String bucketName, String objectName) {
        GetObjectArgs args = GetObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .build();
        try {
            return minioClient.getObject(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "获取文件列表失败", e);
        }
    }

    /**
     * 断点下载
     *
     * @param objectName 文件名称
     * @param offset     起始字节的位置
     * @param length     要读取的长度
     * @return 二进制流
     */
    public InputStream getObject(String objectName, long offset, long length) {
        return getObject(config.getMinioBucket(), objectName, offset, length);
    }

    /**
     * 断点下载
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件名称
     * @param offset     起始字节的位置
     * @param length     要读取的长度
     * @return 二进制流
     */
    public InputStream getObject(String bucketName, String objectName, long offset, long length) {
        GetObjectArgs args = GetObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .offset(offset)
                .length(length)
                .build();
        try {
            return minioClient.getObject(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "下载文件失败", e);
        }
    }

    /**
     * 获取路径下文件列表
     *
     * @param prefix    文件名称
     * @param recursive 是否递归查找，false：模拟文件夹结构查找
     * @return 二进制流
     */
    public Iterable<Result<Item>> listObjects(String prefix, boolean recursive) {
        return listObjects(config.getMinioBucket(), prefix, recursive);
    }

    /**
     * 获取路径下文件列表
     *
     * @param bucketName 存储桶名称
     * @param prefix     文件名称
     * @param recursive  是否递归查找，false：模拟文件夹结构查找
     * @return 二进制流
     */
    public Iterable<Result<Item>> listObjects(String bucketName, String prefix, boolean recursive) {
        ListObjectsArgs args = ListObjectsArgs.builder()
                .bucket(bucketName)
                .prefix(prefix)
                .recursive(recursive)
                .build();
        return minioClient.listObjects(args);
    }

    /**
     * 使用MultipartFile进行文件上传
     *
     * @param file       文件名
     * @param objectName 对象名
     * @return ObjectWriteResponse对象
     */
    public ObjectWriteResponse uploadFile(MultipartFile file, String objectName) {
        return uploadFile(config.getMinioBucket(), file, objectName);
    }


    /**
     * 使用MultipartFile进行文件上传
     *
     * @param bucketName 存储桶名称
     * @param file       文件名
     * @param objectName 对象名
     * @return ObjectWriteResponse对象
     */
    public ObjectWriteResponse uploadFile(String bucketName, MultipartFile file, String objectName) {
        try (InputStream in = file.getInputStream()) {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .contentType(file.getContentType())
                    .stream(in, in.available(), -1)
                    .build();
            return minioClient.putObject(args);

        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "文件上传失败", e);
        }
    }

    /**
     * 上传本地文件
     *
     * @param objectName 对象名称
     * @param fileName   本地文件路径
     */
    public ObjectWriteResponse uploadFile(String objectName, String fileName) {
        return uploadFile(config.getMinioBucket(), objectName, fileName);
    }

    /**
     * 上传本地文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param fileName   本地文件路径
     */
    public ObjectWriteResponse uploadFile(String bucketName, String objectName, String fileName) {
        try {
            UploadObjectArgs args = UploadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .filename(fileName)
                    .build();
            return minioClient.uploadObject(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "文件上传失败", e);
        }
    }

    /**
     * 通过流上传文件
     *
     * @param objectName  文件对象
     * @param inputStream 文件流
     * @param contentType contentType
     */
    public ObjectWriteResponse uploadFile(String objectName, InputStream inputStream, String contentType) {
        return uploadFile(config.getMinioBucket(), objectName, inputStream, contentType);
    }

    /**
     * 通过流上传文件
     *
     * @param bucketName  存储桶名称
     * @param objectName  文件对象
     * @param inputStream 文件流
     */
    public ObjectWriteResponse uploadFile(String bucketName, String objectName, InputStream inputStream, String contentType) {
        try {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .contentType(contentType)
                    .stream(inputStream, inputStream.available(), -1)
                    .build();
            return minioClient.putObject(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "文件上传失败", e);
        }
    }

    /**
     * 获取文件信息, 如果抛出异常则说明文件不存在
     *
     * @param objectName 文件名称
     */
    public String getFileStatusInfo(String objectName) {
        return getFileStatusInfo(config.getMinioBucket(), objectName);
    }

    /**
     * 获取文件信息, 如果抛出异常则说明文件不存在
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件名称
     */
    public String getFileStatusInfo(String bucketName, String objectName) {
        StatObjectArgs args = StatObjectArgs.builder().bucket(bucketName).object(objectName).build();
        try {
            return minioClient.statObject(args).toString();
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "获取文件信息失败", e);
        }
    }

    /**
     * 拷贝文件
     *
     * @param objectName    文件名
     * @param srcObjectName 目标文件名
     */
    public ObjectWriteResponse copyFile(String objectName, String srcObjectName) {
        return copyFile(config.getMinioBucket(), objectName, config.getMinioBucket(), srcObjectName);
    }

    /**
     * 拷贝文件
     *
     * @param objectName    文件名
     * @param srcBucketName 目标存储桶
     * @param srcObjectName 目标文件名
     */
    public ObjectWriteResponse copyFile(String objectName, String srcBucketName, String srcObjectName) {
        return copyFile(config.getMinioBucket(), objectName, srcBucketName, srcObjectName);
    }

    /**
     * 拷贝文件
     *
     * @param bucketName    存储桶名称
     * @param objectName    文件名
     * @param srcBucketName 目标存储桶
     * @param srcObjectName 目标文件名
     */
    public ObjectWriteResponse copyFile(String bucketName, String objectName, String srcBucketName, String srcObjectName) {
        CopySource source = CopySource.builder()
                .bucket(bucketName)
                .object(objectName)
                .build();
        CopyObjectArgs args = CopyObjectArgs.builder()
                .source(source)
                .bucket(srcBucketName)
                .object(srcObjectName)
                .build();
        try {
            return minioClient.copyObject(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "复制文件失败", e);
        }
    }

    /**
     * 删除文件
     *
     * @param objectName 文件名称
     */
    public void removeFile(String objectName) {
        removeFile(config.getMinioBucket(), objectName);
    }

    /**
     * 删除文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件名称
     */
    public void removeFile(String bucketName, String objectName) {
        RemoveObjectArgs args = RemoveObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .build();
        try {
            minioClient.removeObject(args);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "删除文件失败", e);
        }
    }

    /**
     * 批量删除文件
     *
     * @param keys 需要删除的文件列表
     */
    public void removeFiles(List<String> keys) {
        removeFiles(config.getMinioBucket(), keys);
    }

    /**
     * 批量删除文件
     *
     * @param bucketName 存储桶名称
     * @param keys       需要删除的文件列表
     */
    public void removeFiles(String bucketName, List<String> keys) {
        keys.forEach(key -> {
            try {
                removeFile(bucketName, key);
            } catch (Exception e) {
                log.error("批量删除失败！key:{0}, error:{1}", key, e);
            }
        });
    }

    /**
     * 获取文件外链
     *
     * @param objectName 文件名
     * @return 文件外链
     */
    public String getPreSignedObjectUrl(String objectName) {
        return getPreSignedObjectUrl(config.getMinioBucket(), objectName, null);
    }

    /**
     * 获取文件外链
     *
     * @param objectName 文件名
     * @param expires    过期时间（单位：秒） 默认7天
     * @return 文件外链
     */
    public String getPreSignedObjectUrl(String objectName, Integer expires) {
        return getPreSignedObjectUrl(config.getMinioBucket(), objectName, expires);
    }

    /**
     * 获得文件外链
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件名
     * @return 文件外链
     */
    public String getPreSignedObjectUrl(String bucketName, String objectName) {
        return getPreSignedObjectUrl(bucketName, objectName, null);
    }

    /**
     * 获取文件外链
     *
     * @param bucketName 存储桶名称
     * @param objectName 文件名
     * @param expires    过期时间（单位：秒） 默认7天
     * @return 文件外链
     */
    public String getPreSignedObjectUrl(String bucketName, String objectName, Integer expires) {
        GetPresignedObjectUrlArgs.Builder builder = GetPresignedObjectUrlArgs.builder()
                .method(Method.GET)
                .bucket(bucketName)
                .object(objectName);
        if (expires != null) {
            builder.expiry(expires);
        }
        try {
            return minioClient.getPresignedObjectUrl(builder.build());
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "获取文件外链失败", e);
        }
    }

    /**
     * 将URLDecoder编码转成UTF8
     *
     * @param str 字符串
     * @return 编码
     */
    public String getUtf8ByDecoder(String str) {
        String url = str.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
        try {
            return URLDecoder.decode(url, "UTF-8");
        } catch (Exception e) {
            throw new BusinessException(ResultCode.FAIL, "转码失败", e);
        }
    }
}
