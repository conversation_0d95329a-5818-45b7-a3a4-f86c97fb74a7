package com.fykj.scaffold.zyz.xxl_job;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.sgb_docking.service.ISgbSyncPushService;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.List;


/**
 * SGB活动同步定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgbActivitySyncTask {

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private IZyzActivityTimePeriodService actTimePeriodService;

    @Autowired
    private IZyzActivityApplyService applyService;

    @Autowired
    private ISgbSyncPushService sgbSyncPushService;

    @XxlJob("SGB_Activity_Sync_Handler")
    public void sgbActivitySyncJobHandler() {
        XxlJobHelper.log("SGB_Activity_Sync_Handler-Job, START!");

        // SGB活动同步逻辑
        List<Long> sgbIds = activityService.getSgbNeedSync();
        if (CollectionUtil.isNotEmpty(sgbIds)) {
            try {
                sgbSyncPushService.syncActivityBatch(sgbIds);
            } catch (Exception e) {
                log.error("SGB活动批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }

        // SGB发布招募同步逻辑
        List<Long> sgbActivityRecruitIds = actTimePeriodService.getSgbNeedSync();
        if (CollectionUtil.isNotEmpty(sgbActivityRecruitIds)) {
            try {
                sgbSyncPushService.syncActivityRecruitBatch(sgbActivityRecruitIds);
            } catch (Exception e) {
                log.error("SGB活动时间段发布招募批量同步失败，失败原因：{}", e.getMessage(), e);
            }
        }

        // SGB活动申请加入招募同步逻辑
        List<Long> sgbNeedMemberSyncIds = applyService.getSgbNeedMemberSync(null);
        if (CollectionUtil.isNotEmpty(sgbNeedMemberSyncIds)) {
            Instant start = Instant.now();
            try {
                sgbSyncPushService.syncActivityMemberBatch(sgbNeedMemberSyncIds);
            } catch (Exception e) {
                log.error("SGB活动申请加入招募批量同步失败，失败原因：{}", e.getMessage(), e);
            }
            Instant end = Instant.now();
            log.info("SGB批量同步加入招募用时：{}", Duration.between(start, end).toMillis());
        }

        // SGB活动志愿者服务时长同步逻辑
        List<Long> sgbNeedSyncServiceTimeApplyIds = applyService.getSgbNeedServiceTimeSync(null);
        if (CollectionUtil.isNotEmpty(sgbNeedSyncServiceTimeApplyIds)) {
            Instant start = Instant.now();
            try {
                sgbSyncPushService.syncServiceTimeBatch(sgbNeedSyncServiceTimeApplyIds);
            } catch (Exception e) {
                log.error("SGB活动志愿者服务时长批量同步失败，失败原因：{}", e.getMessage(), e);
            }
            Instant end = Instant.now();
            log.info("SGB批量同步服务时长用时：{}", Duration.between(start, end).toMillis());
        }

        XxlJobHelper.log("SGB_Activity_Sync_Handler-Job, END!");
    }
}
