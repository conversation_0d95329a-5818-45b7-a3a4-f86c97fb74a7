package com.fykj.scaffold.zyz.xxl_job;

import com.fykj.scaffold.zyz.service.IZyzBlackListService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 30天内志愿者参加活动爽约次数是否超过
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VolunteerBlankListTask {
    @Autowired
    private IZyzBlackListService zyzBlackListService;

    @XxlJob("Volunteer_Blank_List_Sync_Handler")
    public void volunteerBlankListSyncJobHandler() {
        XxlJobHelper.log("Volunteer_Blank_List_Sync_Handler-Job, START!");
        zyzBlackListService.getMeetsRequirementsBlackList();
        XxlJobHelper.log("Volunteer_Blank_List_Sync_Handler-Job, END!");
    }
}
