package com.fykj.scaffold.zsq_docking.log.service;

import com.fykj.scaffold.zsq_docking.log.domain.dto.NewsDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingNewsRecord;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.bind.annotation.RequestParam;
import result.Result;

import java.util.List;

/**
 * 知社区对接新闻对接记录
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-13
 */
public interface IZSQDockingNewsRecordService extends IBaseService<ZSQDockingNewsRecord> {

    /**
     * 获取新闻对接记录列表
     * @param newsId
     * @return
     */
    List<ZSQDockingNewsRecord> getNewsDockingRecords(Long newsId);

    /**
     * 失败重试
     * @param newsId
     * @param recordId
     */
    void retry(Long newsId, Long recordId);

    /**
     * 获取对接记录详情
     * @param recordId
     * @return
     */
    NewsDockingRecordDetailDto getDockingRecordDetail(Long recordId);

    /**
     * 更新对接记录
     * @param record
     * @param result
     */
    void updateDockingRecord(ZSQDockingNewsRecord record, Result result);

    /**
     * 更新对接记录
     * @param recordId
     * @param result
     */
    void updateDockingRecord(Long recordId, Result result);
}

