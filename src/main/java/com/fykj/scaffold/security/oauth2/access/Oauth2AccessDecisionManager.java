package com.fykj.scaffold.security.oauth2.access;

import com.fykj.scaffold.support.conns.Oauth2AuthorizeCons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.web.FilterInvocation;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class Oauth2AccessDecisionManager implements AccessDecisionManager {
    @Override
    public void decide(Authentication authentication, Object resource, Collection<ConfigAttribute> configAttributes) throws AccessDeniedException, InsufficientAuthenticationException {
        FilterInvocation filterInvocation = (FilterInvocation) resource;
        // options不作处理
        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(filterInvocation.getRequest().getMethod())) {
            return;
        }
        //如果用户有admin权限，直接放
        if (Oauth2Util.isAdmin()) {
            return;
        }
        String requestUrl = filterInvocation.getRequestUrl();
        log.debug("[决策管理器]:开始判断请求 {} 需要的权限", requestUrl);
        if (configAttributes == null || configAttributes.isEmpty()) {
            log.debug("[决策管理器]:请求 {} 无需权限", requestUrl);
            return;
        }

        log.debug("[决策管理器]:请求 {} 需要的权限 - {}", requestUrl, configAttributes);
        // 判断用户所拥有的权限，是否符合对应的Url权限，用户权限是实现 UserDetailsService#loadUserByUsername 返回用户所对应的权限
        Iterator<ConfigAttribute> ite = configAttributes.iterator();
        Map<String, String> map = ((OAuth2Authentication) authentication).getOAuth2Request().getRequestParameters();
        String grantType = map.get(Oauth2AuthorizeCons.GRANT_TYPE);
        log.debug("[决策管理器]:用户 {} 拥有的权限 - {}", authentication.getName(), authentication.getAuthorities());
        while (ite.hasNext()) {
            ConfigAttribute neededAuthority = ite.next();
            String neededAuthorityStr = neededAuthority.getAttribute();
            if (Oauth2AuthorizeCons.CLIENT_CREDENTIALS.equals(grantType)
                    && neededAuthorityStr.equals(map.get(Oauth2AuthorizeCons.CLIENT_ID))) {
                return;
            }
            for (GrantedAuthority existingAuthority : authentication.getAuthorities()) {
                if (neededAuthorityStr.equals(existingAuthority.getAuthority())) {
                    return;
                }
            }
        }
        log.debug("[决策管理器]:用户 {} 没有访问资源 {} 的权限!", authentication.getName(), requestUrl);
        throw new AccessDeniedException("权限不足!");
    }

    @Override
    public boolean supports(ConfigAttribute attribute) {
        return true;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return true;
    }
}
