<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fykj.scaffold.zyz.mapper.ZyzActivityAppraiseMapper">


    <select id="pageForAppraise" resultType="com.fykj.scaffold.zyz.domain.entity.ZyzActivityAppraise">
        SELECT
        zry.*,
        zaa.time_period_start_time as timePeriodStartTime,
        zaa.time_period_end_time as timePeriodEndTime
        FROM zyz_activity_appraise AS zry
        left join zyz_activity_apply zaa on zaa.id=zry.apply_id
        left join sys_org sp on sp.code=zaa.org_code
        WHERE zry.is_deleted = 0 and zry.appraise_time is not null
        <if test="params.key != null and params.key != ''">
            AND ( zry.activity_name LIKE CONCAT('%', CONCAT(#{params.key}, '%')) OR zry.volunteer_name LIKE CONCAT('%',
            CONCAT(#{params.key}, '%'))
            OR zry.volunteer_phone LIKE CONCAT('%', CONCAT(#{params.key}, '%'))
            OR zry.appraise_content LIKE CONCAT('%', CONCAT(#{params.key}, '%')))
        </if>
        <if test="params.appraiseStatus != null ">
            AND zry.appraise_status = #{params.appraiseStatus}
        </if>
        <if test="params.teamMember != null ">
            AND zaa.is_team_member = #{params.teamMember}
        </if>
        ORDER BY zry.appraise_time DESC
    </select>

    <select id="getAppraiseNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT volunteer_id)
        FROM zyz_activity_appraise
        WHERE is_deleted = 0
        <if test="params.start != null">
            AND appraise_time &gt;= #{params.start}
        </if>
        <if test="params.end != null">
            AND appraise_time &lt;= #{params.end}
        </if>
    </select>
</mapper>
