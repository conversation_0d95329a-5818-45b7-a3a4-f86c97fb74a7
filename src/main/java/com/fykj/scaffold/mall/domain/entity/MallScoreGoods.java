package com.fykj.scaffold.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR> @email ${email}
 * @date 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mall_score_goods")
public class MallScoreGoods extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品描述
     */
    @TableField("goods_desc")
    @ApiModelProperty(value = "商品描述")
    private String goodsDesc;

    /**
     * 商品详解
     */
    @TableField("goods_content")
    @ApiModelProperty(value = "商品详解")
    private String goodsContent;

    /**
     * 商品Logo
     */
    @TableField("goods_logo")
    @ApiModelProperty(value = "商品Logo")
    private String goodsLogo;

    /**
     * 商品类型(1:服务类,2:实物类)
     */
    @TableField("goods_type")
    @ApiModelProperty(value = "商品类型(1:服务类,2:实物类)")
    private Integer goodsType;

    /**
     * 商品领取开始时间
     */
    @TableField("start_date")
    @ApiModelProperty(value = "商品领取开始时间")
    private LocalDateTime startDate;

    /**
     * 商品领取截止时间
     */
    @TableField("end_date")
    @ApiModelProperty(value = "商品领取截止时间")
    private LocalDateTime endDate;

    /**
     * 商品数量
     */
    @TableField("goods_count")
    @ApiModelProperty(value = "商品数量")
    private Integer goodsCount;

    /**
     * 推荐礼品
     */
    @TableField("is_hot")
    @ApiModelProperty(value = "推荐礼品")
    private Boolean hot;

    /**
     * 浏览数
     */
    @TableField("view_count")
    @ApiModelProperty(value = "浏览数")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long viewCount;

    /**
     * 商品价格积分
     */
    @TableField("required_score")
    @ApiModelProperty(value = "商品价格积分")
    private BigDecimal requiredScore;

    /**
     * 礼品等级
     */
    @TableField("area_level")
    @ApiModelProperty(value = "礼品等级")
    private Integer areaLevel;

    /**
     * 备注
     */
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 地址
     */
    @TableField("exchange_address")
    @ApiModelProperty(value = "地址")
    private String exchangeAddress;

    /**
     * 经度
     */
    @TableField("longitude")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @TableField("latitude")
    @ApiModelProperty(value = "维度")
    private BigDecimal latitude;

    /**
     * 联系方式
     */
    @TableField("goods_brand")
    @ApiModelProperty(value = "商品品牌")
    private String goodsBrand;

    /**
     * 组织机构code
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织机构code")
    private String orgCode;

    /**
     * 组织机构名称
     */
    @TableField("org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 上架下架
     */
    @TableField("status")
    @ApiModelProperty(value = "上架下架")
    private Boolean status;

    /**
     * 负责人名称
     */
    @TableField("link_person_name")
    @ApiModelProperty(value = "负责人名称")
    private String linkPersonName;

    /**
     * 负责人联系电话
     */
    @TableField("link_phone")
    @ApiModelProperty(value = "负责人联系电话")
    private String linkPhone;

    /**
     * 商品兑换类型
     */
    @TableField("exchange_type")
    @ApiModelProperty(value = "商品兑换类型")
    @DictTrans(transTo = "exchangeTypeText")
    private String exchangeType;

    @TableField(exist = false)
    @ApiModelProperty(value = "类型数据Text")
    private String exchangeTypeText;
    /**
     * 线下兑换积分
     */
    @TableField("offline_score")
    @ApiModelProperty(value = "线下兑换积分")
    private BigDecimal offlineScore;

    /**
     * 商品类型
     */
    @DictTrans(transTo = "scoreGoodsTypeText")
    @TableField("score_goods_type")
    @ApiModelProperty(value = "商品类型")
    private String scoreGoodsType;

    @TableField(exist = false)
    @ApiModelProperty(value = "商品类型Text")
    private String scoreGoodsTypeText;

    /**
     * 商品sku
     */
    @TableField("sku")
    @ApiModelProperty(value = "商品sku")
    private String sku;
    /**
     * 兑换机商品编码
     */
    @TableField("machine_goods_code")
    @ApiModelProperty(value = "兑换机商品编码")
    private String machineGoodsCode;
    /**
     * 兑换机编码
     */
    @TableField("machine_no")
    @ApiModelProperty(value = "兑换机编码")
    private String machineNo;

    /**
     * pad站点兑换商品编码
     */
    @TableField("station_goods_code")
    @ApiModelProperty(value = "pad站点兑换商品编码")
    private String stationGoodsCode;

    /**
     * pad站点编码
     */
    @TableField("station_code")
    @ApiModelProperty(value = "pad站点编码")
    private String stationCode;

    /**
     * pad站点兑换商品二维码
     */
    @TableField("station_goods_qrcode_url")
    @ApiModelProperty(value = "pad站点兑换商品二维码")
    private String stationGoodsQrcodeUrl;

    /**
     * 兑换频率
     */
    @TableField("frequency")
    @ApiModelProperty(value = "兑换频率")
    @DictTrans(transTo = "frequencyText")
    private String frequency;
    /**
     * 兑换频率
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "兑换频率")
    private String frequencyText;

    /**
     * 排序
     */
    @TableField("sequence")
    @ApiModelProperty(value = "排序")
    private Integer sequence;


    /**
     * 是否是星光闪耀商品
     */
    @TableField("is_star")
    @ApiModelProperty(value = "排序")
    private Boolean star;

    /**
     * 星光闪耀礼遇类别
     */
    @TableField("courteous_type")
    @ApiModelProperty(value = "星光闪耀礼遇类别")
    @DictTrans(transTo = "courteousTypeText")
    private String courteousType;

    /**
     * 星光闪耀礼遇类别
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "星光闪耀礼遇类别")
    private String courteousTypeText;


    /**
     * 激励对象
     */
    @TableField("encourage")
    @ApiModelProperty(value = "激励对象")
    private String encourage;

    /**
     * 支持单位
     */
    @TableField("support_unit")
    @ApiModelProperty(value = "支持单位")
    private String supportUnit;

    /**
     * 服务时长
     */
    @ApiModelProperty(value = "服务时长")
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(value="service_long",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal serviceLong;

    /**
     * 今年服务时长
     */
    @TableField("service_long_this_year")
    @ApiModelProperty(value = "今年服务时长")
    private BigDecimal serviceLongThisYear;

    /**
     * 是否需要兑换
     */
    @TableField("need_exchange")
    @ApiModelProperty(value = "是否需要兑换")
    private Boolean needExchange;

    /**
     * 是否需要报名
     */
    @TableField("need_apply")
    @ApiModelProperty(value = "是否需要报名")
    private Boolean needApply;

    /**
     * 兑换成功信息
     */
    @TableField("success_msg")
    @ApiModelProperty(value = "兑换成功信息")
    private String successMsg;

    /**
     * 展示状态（可兑换/敬请期待）
     */
    @TableField("display_status")
    @ApiModelProperty(value = "展示状态（可兑换/敬请期待）")
    @DictTrans(transTo = "displayStatusText")
    private String displayStatus;

    /**
     * 展示状态（可兑换/敬请期待）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "展示状态（可兑换/敬请期待）")
    private String displayStatusText;


    /**
     * 第三方平台code
     */
    @TableField("third_platform_code")
    @ApiModelProperty(value = "第三方平台code")
    private String thirdPlatformCode;

    /**
     * 第三方商品编码或者id
     */
    @TableField("third_platform_goods_code")
    @ApiModelProperty(value = "第三方商品编码或者id")
    private String thirdPlatformGoodsCode;


    /**
     * 是否满足兑换要求
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否满足兑换要求")
    private Boolean canExchange;

    /**
     * 订单No
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "订单No")
    private String orderNo;

    /**
     * 现有积分要求，不但要符合商品所需积分，现有总积分也要符合一定要求，（例如商品兑换需要50积分，但是兑换该商品需要积分满足100以上的志愿者才可以兑换）
     */
    @TableField("total_point")
    @ApiModelProperty(value = "现有积分要求，不但要符合商品所需积分，现有总积分也要符合一定要求，（例如商品兑换需要50积分，但是兑换该商品需要积分满足100以上的志愿者才可以兑换）")
    private BigDecimal totalPoint;

    /**
     * 报名类型
     */
    @TableField("apply_type")
    @ApiModelProperty(value = "报名类型")
    private String applyType;
}
