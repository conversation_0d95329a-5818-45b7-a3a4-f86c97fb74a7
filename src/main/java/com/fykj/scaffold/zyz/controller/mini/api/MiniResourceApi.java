package com.fykj.scaffold.zyz.controller.mini.api;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.AppointmentRecordPc;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzResource;
import com.fykj.scaffold.zyz.domain.params.ZyzResourceParams;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzResourceService;
import fykj.microservice.cache.support.DictTransUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import utils.StringUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/api/mini/resource")
@Api(tags = "微信小程序资源接口--无需登录")
public class MiniResourceApi {

    @Autowired
    private IZyzResourceService resourceService;
    @Autowired
    private IZyzActivityService activityService;

    /**
     * 1.获取上架的资源 -- 分页查询
     * 2.资源详情
     */

    @ApiOperation("获取资源列表--分页查询")
    @PostMapping("/pagesForResource")
    public JsonResult<IPage<ZyzResource>> pagesForResource(@RequestBody ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        //获取上架的资源
        params.setAutoStatus(true);
        IPage<ZyzResource> iPage = resourceService.pagesResourceForMini(params);

        DictTransUtil.trans(iPage.getRecords());
        //姓名脱敏
        for (ZyzResource record : iPage.getRecords()) {
            if(StringUtil.isNotEmpty(record.getContactPerson())) {
                record.setContactPerson(StrUtil.hide(record.getContactPerson(), 1, 4));
            }

        }
        return new JsonResult<>(iPage);
    }

    @ApiOperation("获取资源详情")
    @GetMapping("/getResourceById")
    public Result getResourceById(@RequestParam Long id) {
        ZyzResource resource = resourceService.getById(id);
        resource.setSurplusAppointmentNum(Math.max(resource.getAppointmentNum() - resource.getHasAppointmentNum(), 0));
        DictTransUtil.trans(resource);
        if(StringUtil.isNotEmpty(resource.getContactPerson())){
            resource.setContactPerson(StrUtil.hide(resource.getContactPerson(), 1, 4));
        }
        return new JsonResult<>(resource);
    }

    @ApiOperation(" 获取资源活动列表--分页查询")
    @GetMapping("/getResActivityPagesForMini")
    public JsonResult<List<ZyzActivity>> getResActivityPagesForMini(@RequestParam Long resId) {
        List<ZyzActivity> res = activityService.getResActivityPagesForMini(resId);
        DictTransUtil.trans(res);
        return new JsonResult<>(res);
    }


    @ApiOperation(" 获取资源预约列表--分页查询")
    @GetMapping("/getResAppointmentForMini")
    public JsonResult<List<AppointmentRecordPc>> getResAppointmentForMini(@RequestParam Long resId) {
        List<AppointmentRecordPc> res = resourceService.getResAppointmentForMini(resId);
        DictTransUtil.trans(res);
        return new JsonResult<>(res);
    }

    /**
     * 获取资源预约记录列表 -- 分页查询
     */
    @ApiOperation("获取资源预约记录列表--分页查询")
    @PostMapping("/pagesByResIdForResourceAppointment")
    public JsonResult<IPage<AppointmentRecordPc>> pagesByResIdForResourceAppointment(@RequestBody ZyzResourceParams params) {
        if (ObjectUtil.isEmpty(params)) {
            params = new ZyzResourceParams();
        }
        //获取上架的资源
        IPage<AppointmentRecordPc> iPage = resourceService.getResRecordPagesForMini(params);
        DictTransUtil.trans(iPage.getRecords());
        return new JsonResult<>(iPage);
    }
}
