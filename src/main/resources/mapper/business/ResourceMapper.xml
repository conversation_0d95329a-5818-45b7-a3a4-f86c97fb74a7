<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fykj.scaffold.security.business.mapper.ResourceMapper">
    <select id="findByRoleIdAndTypeAndParent" resultType="com.fykj.scaffold.security.business.domain.entity.Resource">
        SELECT
        re.*
        FROM
        sys_resource re
        INNER JOIN sys_role_resource rr ON rr.resource_id = re.id
        INNER JOIN sys_role ro ON rr.role_id = ro.id
        WHERE
        ro.id = #{roleId}
        AND re.type = #{type}
        <choose>
            <when test="parentId == null or parentId == ''">
                AND re.parent_id is NULL
            </when>
            <otherwise>
                AND re.parent_id = #{parentId}
            </otherwise>
        </choose>
        AND re.is_deleted = 0
        ORDER BY re.sequence ASC
    </select>
    <select id="findByRoleId" resultType="com.fykj.scaffold.security.business.domain.entity.Resource">
        SELECT re.*
        FROM sys_resource re
                 INNER JOIN sys_role_resource rr ON rr.resource_id = re.id
                 INNER JOIN sys_role ro ON rr.role_id = ro.id
        WHERE ro.id = #{roleId}
          AND re.is_deleted = 0
        ORDER BY re.sequence ASC
    </select>

    <select id="findResourceByUserId" resultType="com.fykj.scaffold.security.business.domain.entity.Resource">
        SELECT *
        FROM sys_resource r
        WHERE r.id IN (
            SELECT rr.resource_id
            FROM sys_role_resource rr
                     INNER JOIN sys_user_role ur ON ur.role_id = rr.role_id
                     INNER JOIN sys_role r on r.id = ur.role_id
            WHERE rr.is_deleted = FALSE
              AND r.is_deleted = false
              AND r.status = true
              AND ur.is_deleted = FALSE
              AND ur.user_id = #{userId})
          and r.is_deleted = false
          and r.status = true
        ORDER by r.sequence asc
    </select>

    <select id="findResourceByUserIdAndRoleIds" resultType="com.fykj.scaffold.security.business.domain.entity.Resource">
        SELECT *
        FROM sys_resource r
        WHERE r.id IN (
            SELECT rr.resource_id
            FROM sys_role_resource rr
                     INNER JOIN sys_user_role ur ON ur.role_id = rr.role_id
            WHERE user_id = #{userId}
              AND ur.is_deleted = FALSE
              AND rr.is_deleted = FALSE
              and rr.role_id in
                (<foreach item="item" collection="roleIds" index="" separator=",">#{item}</foreach>)
        )
        and r.is_deleted = false
        and r.status = true
        and r.type = #{type}
        ORDER by r.sequence asc
    </select>
</mapper>
