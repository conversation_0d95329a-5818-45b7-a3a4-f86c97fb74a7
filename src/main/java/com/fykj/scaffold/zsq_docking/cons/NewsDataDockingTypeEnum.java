package com.fykj.scaffold.zsq_docking.cons;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

@Getter
public enum NewsDataDockingTypeEnum {

    CREATE("CREATE", "新闻新增"),
    UPDATE("UPDATE", "新闻修改"),
    DELETE("DELETE", "新闻删除"),
    ON_OFF("ON_OFF", "新闻上/下架"),
    AUDIT("AUDIT", "新闻审核"),
    UNKNOWN("UNKNOWN", "未知操作");

    private final String code;
    private final String text;

    NewsDataDockingTypeEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    /**
     * 根据 code 获取枚举实例
     */
    public static NewsDataDockingTypeEnum fromCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return UNKNOWN;
        }
        for (NewsDataDockingTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return UNKNOWN;
    }
}
