package com.fykj.scaffold.sgb_docking.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 苏州志愿者平台字典数据
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("苏州志愿者平台字典数据")
@TableName("sgb_dict")
public class SgbDict extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 字典值
     */
    @TableField("value")
    @ApiModelProperty("字典值")
    private String value;

    /**
     * 字典标签
     */
    @TableField("label")
    @ApiModelProperty("字典标签")
    private String label;

    /**
     * 字典类型
     */
    @TableField("type")
    @ApiModelProperty("字典类型")
    private String type;
    
    /**
     * 字典描述
     */
    @TableField("`describe`")
    @ApiModelProperty("字典描述")
    private String describe;
} 