package com.fykj.scaffold.mall.xxl_job;

import com.fykj.scaffold.mall.cons.MallCons;
import com.fykj.scaffold.mall.domain.entity.MallExchange;
import com.fykj.scaffold.mall.domain.entity.MallExchangeDistributionReport;
import com.fykj.scaffold.mall.domain.entity.ZyzPointRecord;
import com.fykj.scaffold.mall.service.IMallExchangeDistributionReportService;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import com.fykj.scaffold.mall.service.IZyzPointRecordService;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApply;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AddExchangeDistributionReportTask {
    @Autowired
    private IZyzVolunteerService volunteerService;
    @Autowired
    private IMallExchangeService exchangeService;
    @Autowired
    private IMallExchangeDistributionReportService exchangeDistributionReportService;
    @Autowired
    private IZyzPointRecordService pointRecordService;
    @Autowired
    private IZyzActivityApplyService applyService;

    @XxlJob("add_exchange_distribution_report_Sync_Handler")
    public void addExchangeDistributionReportSyncJobHandler() {
        XxlJobHelper.log("Add_Exchange_Distribution_Report_Sync_Handler-Job, START!");
        // 获取待同步的活动数据以及活动结束时间三天内活动同步失败的数据
        LocalDateTime startTime = LocalDateTime.of(LocalDate.from(LocalDateTime.now().minusYears(1).with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(LocalDate.from(LocalDateTime.now().minusYears(1).with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);
        Map<Long, BigDecimal> volunteerIdAndServices = applyService.lambdaQuery().eq(ZyzActivityApply::getExchange, true)
                .ge(ZyzActivityApply::getSignTime, startTime).le(ZyzActivityApply::getSignTime, endTime)
                .list().stream()
                .collect(Collectors.groupingBy(ZyzActivityApply::getVolunteerId, Collectors.reducing(BigDecimal.ZERO, ZyzActivityApply::getServiceLong, BigDecimal::add)));
        ;
        getVolunteerIds(25, 50, "25-50", volunteerIdAndServices);
        getVolunteerIds(50, 100, "50-100", volunteerIdAndServices);
        getVolunteerIds(100, 300, "100-300", volunteerIdAndServices);
        getVolunteerIds(300, 600, "300-600", volunteerIdAndServices);
        getVolunteerIds(600, Integer.MAX_VALUE, ">600", volunteerIdAndServices);
        XxlJobHelper.log("Add_Exchange_Distribution_Report_Sync_Handler-Job, END!");
    }

    private void getVolunteerIds(Integer minNum, Integer maxNum, String serverTime, Map<Long, BigDecimal> volunteerIdAndServices) {
        List<Long> volunteerListMin =new ArrayList<Long>(volunteerIdAndServices.entrySet().stream().filter(map -> (map.getValue()
                .compareTo(new BigDecimal(minNum)) > 0 && map.getValue().compareTo(new BigDecimal(maxNum)) < 1))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))
                .keySet());
        addExchangeDRByVolunteers(volunteerListMin, serverTime);
    }

    private void addExchangeDRByVolunteers(List<Long> volunteerIds, String serverTime) {
        List<MallExchange> exchangeList;
        List<ZyzPointRecord> pointRecords;
        List<ZyzActivityApply> activityApplies;
        LocalDateTime startTime = LocalDateTime.of(LocalDate.from(LocalDateTime.now().minusYears(1).with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(LocalDate.from(LocalDateTime.now().minusYears(1).with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);
        if (volunteerIds.size() != 0) {
            pointRecords = pointRecordService.lambdaQuery().ne(ZyzPointRecord::getBizType, MallCons.POINT_CHANGE_BIZ_TYPE_EXCHANGE)
                    .ge(ZyzPointRecord::getExchangeTime, startTime).le(ZyzPointRecord::getExchangeTime, endTime)
                    .eq(ZyzPointRecord::getType, MallCons.ZYZ_POINT_RECORD_TYPE_ADD).in(ZyzPointRecord::getVolunteerId, volunteerIds).list();
            activityApplies = applyService.lambdaQuery().eq(ZyzActivityApply::getExchange, true)
                    .ge(ZyzActivityApply::getSignTime, startTime).le(ZyzActivityApply::getSignTime, endTime)
                    .in(ZyzActivityApply::getVolunteerId, volunteerIds).list();
            exchangeList = exchangeService.lambdaQuery().eq(MallExchange::getStatus, MallCons.MALL_EXCHANGE_STATUS_SUCCESS)
                    .ge(MallExchange::getExchangeTime, startTime).le(MallExchange::getExchangeTime, endTime).in(MallExchange::getExchangeVolunteerId, volunteerIds).list();
        } else {
            pointRecords = new ArrayList<>();
            exchangeList = new ArrayList<>();
            activityApplies = new ArrayList<>();
        }
        BigDecimal point = pointRecords.stream().map(ZyzPointRecord::getExchangePoint).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal serviceLongThisYear = activityApplies.stream().map(ZyzActivityApply::getServiceLong).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal exchangePoint = exchangeList.stream().map(MallExchange::getExchangePoint).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<MallExchange> exchangeVolunteerList = exchangeList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                -> new TreeSet<>(Comparator.comparing(MallExchange::getExchangeVolunteerId))), ArrayList::new));
        MallExchangeDistributionReport exchangeDistributionReport = new MallExchangeDistributionReport();
        exchangeDistributionReport.setServerTime(serverTime);
        exchangeDistributionReport.setQualifiedPersonnelNum(volunteerIds.size());
        exchangeDistributionReport.setYear(Integer.toString(LocalDateTime.now().minusYears(1).getYear()));
        exchangeDistributionReport.setTotalExchangeNum(exchangeVolunteerList.size());
        exchangeDistributionReport.setTotalExchangePoint(exchangePoint);
        exchangeDistributionReport.setTotalPoint(point);
        exchangeDistributionReport.setTotalServerTime(serviceLongThisYear);
        exchangeDistributionReportService.save(exchangeDistributionReport);
    }
}
