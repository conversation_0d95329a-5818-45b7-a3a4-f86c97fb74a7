package com.fykj.scaffold.config.mybatisplus;

import java.io.Serializable;

public class UserThreadLocal {

    private static final ThreadLocal<Serializable> currentUser = new ThreadLocal<>();

    // 设置当前线程的值
    public static void set(Serializable userId) {
        currentUser.set(userId);
    }

    // 获取当前线程的值
    public static Serializable get() {
        return currentUser.get();
    }

    // 移除当前线程的值（防止内存泄漏）
    public static void remove() {
        currentUser.remove();
    }

}
