package com.fykj.scaffold.security.business.api;

import com.fykj.scaffold.security.business.service.IUserWriteOffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.Result;

import static fykj.microservice.core.base.AbstractController.OK;


/**
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/api/user/write_off")
@Api(tags = "系统用户注销-无需token")
public class SysUserWriteOffApi {

    @Autowired
    private IUserWriteOffService userWriteOffService;

    @ApiOperation("取消注销")
    @GetMapping("/cancel")
    public Result cancel(String mobile) {
        userWriteOffService.writeOffCancelByMobile(mobile);
        return OK;
    }
}
