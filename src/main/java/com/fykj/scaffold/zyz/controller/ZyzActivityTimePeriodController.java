package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ActSyncRecordDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityTimePeriodParams;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 活动-时段表
 * <p>
 * 前端控制器
 *
 * <AUTHOR> @email ${email}
 * @date 2023-07-06
 */
@RestController
@RequestMapping("/admin/zyz/activity-time-period")
@Api(tags = "活动时间段接口")
public class ZyzActivityTimePeriodController extends BaseController<IZyzActivityTimePeriodService, ZyzActivityTimePeriod, ZyzActivityTimePeriodParams> {

    @Autowired
    private ScheduledExecutorService newSingleThreadScheduledExecutor;

    @Autowired
    private IZyzActivityApplyService applyService;

    @Autowired
    private IZyzActivityService activityService;

    @Value("${act-sync-by-time-period}")
    private Boolean actSyncByTimePeriod = false;

    @ApiOperation("获取活动的时段")
    @GetMapping("/getTimePeriodListByActivityId")
    public Result getTimePeriodListByActivityId(Long activityId) {
        List<ZyzActivityTimePeriod> list = baseService
                .lambdaQuery()
                .eq(ZyzActivityTimePeriod::getActivityId, activityId)
                .orderByAsc(ZyzActivityTimePeriod::getStartTime)
                .list();
        DictTransUtil.trans(list);
        ZyzActivity activity = activityService.getById(activityId);
        LocalDateTime now = LocalDateTime.now();
        list.forEach(it -> {
            boolean actStatus = ZyzCons.ACTIVITY_STATUS_AUDIT_SUCCESS.equals(activity.getAuditStatus());
            boolean timeEnable = !it.getEndTime().isAfter(now);
            boolean recruitSync = !Cons.PlatformSyncState.SUCCESS.equals(it.getRecruitSync());
            if (!actSyncByTimePeriod) {
                it.setEnableSync(actStatus && timeEnable && recruitSync);
                return;
            }
            boolean sync = !Cons.PlatformSyncState.SUCCESS.equals(it.getSync());
            it.setEnableSync(actStatus && timeEnable && (sync || recruitSync));
        });
        return new JsonResult<>(list);
    }

    @AuditLog("变更时间段启用禁用操作")
    @ApiOperation("变更时间段启用禁用操作")
    @PostMapping("/changeWebShow")
    public Result changeWebShow(Long id) {
        ZyzActivityTimePeriod timePeriod = baseService.getById(id);
        timePeriod.setWebShow(!timePeriod.getWebShow());
        baseService.updateById(timePeriod);
        return OK;
    }

    @ApiOperation("活动按时间段同步（单条）(分时间段活动同步 + 分时间段招募同步)")
    @GetMapping("/sync")
    public Result sync(@RequestParam Long id) {
        baseService.sync(id);
        return OK;
    }

    @ApiOperation("活动时间段报名同步")
    @GetMapping("/syncApplyByTimePeriodId")
    public Result syncApplyByTimePeriodId(@RequestParam Long id) {
        // 启一个线程去进行数据的解析和保存
        newSingleThreadScheduledExecutor.execute(() -> {
            List<ActSyncRecordDto> applyRecordsOfNeedMemberSync = applyService.getNeedMemberSync(id);
            if (CollectionUtil.isNotEmpty(applyRecordsOfNeedMemberSync)) {
                applyService.syncMemberBatch(applyRecordsOfNeedMemberSync);
            }
            List<ActSyncRecordDto> applyRecordsOfNeedServiceTimeSync = applyService.getNeedServiceTimeSync(id);
            if (CollectionUtil.isNotEmpty(applyRecordsOfNeedServiceTimeSync)) {
                applyService.syncServiceTimeBatch(applyRecordsOfNeedServiceTimeSync);
            }
        });
        return new Result(ResultCode.OK.code(), "活动报名同步中……请前往报名列表页面查看进度。");
    }
}
