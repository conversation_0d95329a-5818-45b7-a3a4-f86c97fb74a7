package com.fykj.scaffold.big_screen.domain.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "big_screen_data", autoResultMap = true)
public class BigScreenData extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 数据维度
     */
    @TableField("dimension")
    @NotBlank(message = "数据维度不能为空")
    @ApiModelProperty(value = "数据维度")
    @DictTrans(transTo = "dimensionName")
    private String dimension;

    @TableField(exist = false)
    @ApiModelProperty(value = "数据维度名称")
    private String dimensionName;

    /**
     * 组织
     */
    @TableField("org")
    @ApiModelProperty(value = "组织")
    private String org;

    @TableField(exist = false)
    @ApiModelProperty(value = "组织名")
    private String orgName;

    /**
     * 统计类型
     */
    @TableField("type")
    @ApiModelProperty(value = "统计类型")
    private String type;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("status")
    private Boolean status;

    /**
     * 数据
     */
    @TableField(value = "data", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty(value = "数据")
    private JSONObject data;
}
