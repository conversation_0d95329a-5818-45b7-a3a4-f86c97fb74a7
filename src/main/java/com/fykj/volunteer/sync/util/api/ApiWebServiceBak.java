package com.fykj.volunteer.sync.util.api;//package com.fykj.volunteer.sync.util.api;
//
//import com.fykj.volunteer.sync.util.domain.old.*;
//import com.fykj.volunteer.sync.util.domain.old.resource.*;
//import com.fykj.volunteer.sync.util.domain.service_type.ServiceCodeResult;
//import com.fykj.volunteer.sync.util.domain.sync_user.SyncUser;
//import com.fykj.volunteer.sync.util.util.ApiWebUtil;
//import com.fykj.volunteer.sync.util.util.XmlUtils;
//import exception.BusinessException;
//import lombok.extern.slf4j.Slf4j;
//import result.ResultCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Slf4j
//public class ApiWebServiceBak {
//    /**
//     * 查询志愿者信息
//     *
//     * @return
//     */
//    public static UserResource getUserInfoByType(UserInfo userInfo) {
//        String xml = XmlUtils.toStr(userInfo, true, false);
//        String result = ApiWebUtil.sendHttpRequest("getUserInfo", xml);
//        return XmlUtils.parseBean(result, UserResource.class);
//    }
//
//    /**
//     * 创建组织
//     *
//     * @return
//     */
//    public static AddOrgsResource registerOrgs(SyncOrgs orgs) {
//        SyncOrgs syncOrgs = new SyncOrgs();
//        String xml = XmlUtils.toStr(orgs, true, false);
//        String result = ApiWebUtil.sendHttpRequest("registerOrgs", xml);
//        return XmlUtils.parseBean(result, AddOrgsResource.class);
//    }
//
//    /**
//     * 创建组织单个
//     *
//     * @return
//     */
//    public static AddOrgResource registerOrgs(SyncOrg orgs) {
//        SyncOrgs syncOrgs = new SyncOrgs();
//        List<SyncOrg> users = new ArrayList<>();
//        users.add(orgs);
//        syncOrgs.setInfo(users);
//        AddOrgsResource addOrgsResource = registerOrgs(syncOrgs);
//        if (addOrgsResource.getInfo().getInfo().size() == 0) {
//            log.debug("registerOrgs出错" + orgs.getUniqueCode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return addOrgsResource.getInfo().getInfo().get(0);
//    }
//
//    /**
//     * 创建组织list
//     *
//     * @return
//     */
//    public static List<AddOrgResource> registerOrgs(List<SyncOrg> orgs) {
//        SyncOrgs syncOrgs = new SyncOrgs();
//        syncOrgs.setInfo(orgs);
//        AddOrgsResource addOrgsResource = registerOrgs(syncOrgs);
//        return addOrgsResource.getInfo().getInfo();
//    }
//
//    /**
//     * 申请志愿者
//     *
//     * @return
//     */
//    public static AddSyncUsersResource registerUsers(SyncUsers users) {
//        String xml = XmlUtils.toStr(users, true, false);
//        String result = ApiWebUtil.sendHttpRequest("registerUsers", xml);
//        return XmlUtils.parseBean(result, AddSyncUsersResource.class);
//    }
//
//    /**
//     * 申请志愿者list
//     *
//     * @return
//     */
//    public static List<AddSyncUser> registerUsers(List<SyncUser> users) {
//        SyncUsers syncUsers = new SyncUsers();
//        syncUsers.setInfo(users);
//        AddSyncUsersResource addSyncUsersResource = registerUsers(syncUsers);
//        return addSyncUsersResource.getInfo().getInfo();
//    }
//
////    /**
////     * 申请志愿者单个
////     *
////     * @return
////     */
////    public static AddSyncUser registerUsers(SyncUser user) {
////
////        SyncUsers syncUsers = new SyncUsers();
////        List<SyncUser> users = new ArrayList<>();
////        users.add(user);
////        syncUsers.setInfo(users);
////        AddSyncUsersResource addSyncUsersResource = registerUsers(syncUsers);
////        if (addSyncUsersResource.getInfo().getInfo().size() == 0) {
////            log.debug("registerUsers出错" + user.getIdcode() + "返回值为空");
////            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
////        }
////        return addSyncUsersResource.getInfo().getInfo().get(0);
////    }
//
//    /**
//     * 志愿者加入组织
//     *
//     * @return
//     */
//    public static VolunteersJoinOrgResource joinOrg(VolunteersJoinOrg volunteersJoinOrg) {
//        String xml = XmlUtils.toStr(volunteersJoinOrg, true, false);
//        String result = ApiWebUtil.sendHttpRequest("joinOrg", xml);
//        return XmlUtils.parseBean(result, VolunteersJoinOrgResource.class);
//    }
//
//    /**
//     * 志愿者加入组织单个
//     *
//     * @return
//     */
//    public static VolunteerJoinOrgResource joinOrg(VolunteerUserJoinOrg volunteerUserJoinOrg) {
//        VolunteersJoinOrg volunteersJoinOrg = new VolunteersJoinOrg();
//        List<VolunteerUserJoinOrg> orgs = new ArrayList<>();
//        orgs.add(volunteerUserJoinOrg);
//        volunteersJoinOrg.setInfo(orgs);
//        VolunteersJoinOrgResource volunteersJoinOrgResource = joinOrg(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().getOrg_outinfo().size() == 0) {
//            log.debug("joinOrg出错" + volunteerUserJoinOrg.getIdcode() + volunteerUserJoinOrg.getOrgid() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().getOrg_outinfo().get(0);
//    }
//
//    /**
//     * 志愿者加入组织list
//     *
//     * @return
//     */
//    public static List<VolunteerJoinOrgResource> joinOrg(List<VolunteerUserJoinOrg> volunteerUserJoinOrgList) {
//        VolunteersJoinOrg volunteersJoinOrg = new VolunteersJoinOrg();
//        volunteersJoinOrg.setInfo(volunteerUserJoinOrgList);
//        VolunteersJoinOrgResource volunteersJoinOrgResource = joinOrg(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo().getOrg_outinfo();
//    }
//
//    /**
//     * 活动登记
//     *
//     * @return
//     */
//    public static SyncActivityRegistersResource synchronizeAct(SyncActivityRegisters syncActivityRegisters) {
//        String xml = XmlUtils.toStr(syncActivityRegisters, true, false);
//        String result = ApiWebUtil.sendHttpRequest("synchronizeAct", xml);
//        return XmlUtils.parseBean(result, SyncActivityRegistersResource.class);
//    }
//
//    /**
//     * 活动登记单个
//     *
//     * @return
//     */
//    public static SyncActivityRegisterResource synchronizeAct(SyncActivityRegister syncActivityRegisters) {
//        SyncActivityRegisters volunteersJoinOrg = new SyncActivityRegisters();
//        List<SyncActivityRegister> orgs = new ArrayList<>();
//        orgs.add(syncActivityRegisters);
//        volunteersJoinOrg.setInfo(orgs);
//        SyncActivityRegistersResource volunteersJoinOrgResource = synchronizeAct(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().getInfo().size() == 0) {
//            log.debug("synchronizeAct出错" + syncActivityRegisters.getUniqueCode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().getInfo().get(0);
//    }
//
//    /**
//     * 活动登记list
//     *
//     * @return
//     */
//    public static List<SyncActivityRegisterResource> synchronizeAct(List<SyncActivityRegister> syncActivityRegisters) {
//        SyncActivityRegisters volunteersJoinOrg = new SyncActivityRegisters();
//        volunteersJoinOrg.setInfo(syncActivityRegisters);
//        SyncActivityRegistersResource volunteersJoinOrgResource = synchronizeAct(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo().getInfo();
//    }
//
//    /**
//     * 查询活动id
//     *
//     * @return
//     */
//    public static SyncActivityQueryResource getActId(SyncActivityQuery syncActivityQuery) {
//        String xml = XmlUtils.toStr(syncActivityQuery, true, false);
//        String result = ApiWebUtil.sendHttpRequest("getActId", xml);
//        return XmlUtils.parseBean(result, SyncActivityQueryResource.class);
//    }
//
//    /**
//     * 新时代活动登记
//     *
//     * @return
//     */
//    public static SyncPlanActivityRegisterResource syncActp(SyncActivityRegisterPlanDto syncActivityRegisterPlanDto) {
//        String xml = XmlUtils.toStr(syncActivityRegisterPlanDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("syncActp", xml);
//        return XmlUtils.parseBean(result, SyncPlanActivityRegisterResource.class);
//    }
//
//    /**
//     * 新时代活动登记单个
//     *
//     * @return
//     */
//    public static SyncActivityRegisterPlanRes syncActp(SyncActivityRegisterPlan syncActivityRegisterPlanDto) {
//        SyncActivityRegisterPlanDto volunteersJoinOrg = new SyncActivityRegisterPlanDto();
//        volunteersJoinOrg.setInfo(syncActivityRegisterPlanDto);
//        SyncPlanActivityRegisterResource volunteersJoinOrgResource = syncActp(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo().getInfo();
//    }
//
//    /**
//     * 志愿者活动招募
//     *
//     * @return
//     */
//    public static VolunteerRecruitsResource syncActRelease(VolunteerRecruitDto volunteerRecruitDto) {
//        String xml = XmlUtils.toStr(volunteerRecruitDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("syncActRelease", xml);
//        return XmlUtils.parseBean(result, VolunteerRecruitsResource.class);
//    }
//
//    /**
//     * 志愿者活动招募单个
//     *
//     * @return
//     */
//    public static VolunteerRecruitResource syncActRelease(VolunteerRecruit volunteerRecruitDto) {
//        VolunteerRecruitDto volunteersJoinOrg = new VolunteerRecruitDto();
//        volunteersJoinOrg.setInfo(volunteerRecruitDto);
//        VolunteerRecruitsResource volunteersJoinOrgResource = syncActRelease(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().size() == 0) {
//            log.debug("syncActRelease出错" + volunteerRecruitDto.getUniquecode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().get(0);
//    }
//
//    /**
//     * 查询活动招募id
//     *
//     * @return
//     */
//    public static VolunteerRecruitsQueryResource getReleaseId(VolunteerRecruitsQuery volunteerRecruitResource) {
//        String xml = XmlUtils.toStr(volunteerRecruitResource, true, false);
//        String result = ApiWebUtil.sendHttpRequest("getReleaseId", xml);
//        return XmlUtils.parseBean(result, VolunteerRecruitsQueryResource.class);
//    }
//
//    /**
//     * 加入志愿者招募
//     *
//     * @return
//     */
//    public static VolunteerJoinActReleaseResource joinActRelease(VolunteerJoinActReleaseDto volunteerJoinActReleaseDto) {
//        String xml = XmlUtils.toStr(volunteerJoinActReleaseDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("joinActRelease", xml);
//        return XmlUtils.parseBean(result, VolunteerJoinActReleaseResource.class);
//    }
//
//    /**
//     * 活动时长上传
//     *
//     * @return
//     */
//    public static ServerTimeResource reportServerTime(ServerTimeDto serverTimeDto) {
//        String xml = XmlUtils.toStr(serverTimeDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("reportServerTime", xml);
//        return XmlUtils.parseBean(result, ServerTimeResource.class);
//    }
//
//    /**
//     * 活动时长上传单个
//     *
//     * @return
//     */
//    public static ServerTimeRes reportServerTime(ServerTime serverTimeDto) {
//        ServerTimeDto volunteersJoinOrg = new ServerTimeDto();
//        List<ServerTime> serverTimeList = new ArrayList<>();
//        serverTimeList.add(serverTimeDto);
//        volunteersJoinOrg.setInfo(serverTimeList);
//        ServerTimeResource volunteersJoinOrgResource = reportServerTime(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().getInfo().size() == 0) {
//            log.debug("reportServerTime出错" + serverTimeDto.getUniquecode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().getInfo().get(0);
//    }
//
//    /**
//     * 活动时长上传list
//     *
//     * @return
//     */
//    public static List<ServerTimeRes> reportServerTime(List<ServerTime> serverTimeDto) {
//        ServerTimeDto volunteersJoinOrg = new ServerTimeDto();
//        volunteersJoinOrg.setInfo(serverTimeDto);
//        ServerTimeResource volunteersJoinOrgResource = reportServerTime(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo().getInfo();
//    }
//
//    /**
//     * 新增新闻
//     *
//     * @return
//     */
//    public static VolunteerCmsResource synchronizeCms(VolunteerCmsDto volunteerCmsDto) {
//        String xml = XmlUtils.toStr(volunteerCmsDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("synchronizeCms", xml);
//        return XmlUtils.parseBean(result, VolunteerCmsResource.class);
//    }
//
//    /**
//     * 新增新闻单个
//     *
//     * @return
//     */
//    public static VolunteerCmsRes synchronizeCms(VolunteerCms volunteerCmsDto) {
//        VolunteerCmsDto volunteersJoinOrg = new VolunteerCmsDto();
//        List<VolunteerCms> volunteerCms = new ArrayList<>();
//        volunteerCms.add(volunteerCmsDto);
//        volunteersJoinOrg.setInfo(volunteerCms);
//        VolunteerCmsResource volunteersJoinOrgResource = synchronizeCms(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().getInfo().size() == 0) {
//            log.debug("synchronizeCms出错" + volunteerCmsDto.getUniqueCode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().getInfo().get(0);
//    }
//
//    /**
//     * 新增新闻list
//     *
//     * @return
//     */
//    public static List<VolunteerCmsRes> synchronizeCms(List<VolunteerCms> volunteerCmsDto) {
//        VolunteerCmsDto volunteersJoinOrg = new VolunteerCmsDto();
//        volunteersJoinOrg.setInfo(volunteerCmsDto);
//        VolunteerCmsResource volunteersJoinOrgResource = synchronizeCms(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo().getInfo();
//    }
//
//    /**
//     * 新时代新增团队
//     *
//     * @return
//     */
//    public static ResponseOrganizationDto regOrg(SysOrganizationPlanDto sysOrganizationPlanDto) {
//        String xml = XmlUtils.toStr(sysOrganizationPlanDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("regOrg", xml);
//        return XmlUtils.parseBean(result, ResponseOrganizationDto.class);
//    }
//
//    /**
//     * 新时代新增团队单个
//     *
//     * @return
//     */
//    public static ResponseOrganization regOrg(SysOrganizationPlan sysOrganizationPlanDto) {
//        SysOrganizationPlanDto volunteersJoinOrg = new SysOrganizationPlanDto();
//        List<SysOrganizationPlan> volunteerCms = new ArrayList<>();
//        volunteerCms.add(sysOrganizationPlanDto);
//        volunteersJoinOrg.setInfo(volunteerCms);
//        ResponseOrganizationDto volunteersJoinOrgResource = regOrg(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().size() == 0) {
//            log.debug("regOrg出错" + sysOrganizationPlanDto.getUniqueCode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().get(0);
//    }
//
//    /**
//     * 新时代新增团队list
//     *
//     * @return
//     */
//    public static List<ResponseOrganization> regOrg(List<SysOrganizationPlan> sysOrganizationPlanDto) {
//        SysOrganizationPlanDto volunteersJoinOrg = new SysOrganizationPlanDto();
//        volunteersJoinOrg.setInfo(sysOrganizationPlanDto);
//        ResponseOrganizationDto volunteersJoinOrgResource = regOrg(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo();
//    }
//
//    /**
//     * //新时代新增新闻资源
//     *
//     * @return
//     */
//    public static ResponseSysEraResourceDto resPub(SysEraResourcesDto sysEraResourcesDto) {
//        String xml = XmlUtils.toStr(sysEraResourcesDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("resPub", xml);
//        return XmlUtils.parseBean(result, ResponseSysEraResourceDto.class);
//    }
//
//    /**
//     * //新时代新增新闻资源单个
//     *
//     * @return
//     */
//    public static ResponseSysEraResource resPub(SysEraResources sysEraResourcesDto) {
//        SysEraResourcesDto volunteersJoinOrg = new SysEraResourcesDto();
//        List<SysEraResources> volunteerCms = new ArrayList<>();
//        volunteerCms.add(sysEraResourcesDto);
//        volunteersJoinOrg.setInfo(volunteerCms);
//        ResponseSysEraResourceDto volunteersJoinOrgResource = resPub(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().size() == 0) {
//            log.debug("resPub出错" + sysEraResourcesDto.getUniCode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().get(0);
//    }
//
//    /**
//     * //新时代新增新闻资源list
//     *
//     * @return
//     */
//    public static List<ResponseSysEraResource> resPub(List<SysEraResources> sysEraResourcesDto) {
//        SysEraResourcesDto volunteersJoinOrg = new SysEraResourcesDto();
//        volunteersJoinOrg.setInfo(sysEraResourcesDto);
//        ResponseSysEraResourceDto volunteersJoinOrgResource = resPub(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo();
//    }
//
//    /**
//     * //资源预约和需求预约
//     *
//     * @param responseResReserveDto
//     * @return
//     */
//    public static SysResResponseDataResource resReserve(ResponseResReserveDto responseResReserveDto) {
//        String xml = XmlUtils.toStr(responseResReserveDto, true, false);
//        String result = ApiWebUtil.sendHttpRequest("resReserve", xml);
//        return XmlUtils.parseBean(result, SysResResponseDataResource.class);
//    }
//
//    /**
//     * //资源预约和需求预约单个
//     *
//     * @param responseResReserveDto
//     * @return
//     */
//    public static SysResResponseData resReserve(SysResReserveXml responseResReserveDto) {
//        ResponseResReserveDto volunteersJoinOrg = new ResponseResReserveDto();
//        List<SysResReserveXml> volunteerCms = new ArrayList<>();
//        volunteerCms.add(responseResReserveDto);
//        volunteersJoinOrg.setInfo(volunteerCms);
//        SysResResponseDataResource volunteersJoinOrgResource = resReserve(volunteersJoinOrg);
//        if (volunteersJoinOrgResource.getInfo().size() == 0) {
//            log.debug("resReserve出错" + responseResReserveDto.getUniCode() + "返回值为空");
//            throw new BusinessException(ResultCode.FAIL, "返回值为空！");
//        }
//        return volunteersJoinOrgResource.getInfo().get(0);
//    }
//
//    /**
//     * //资源预约和需求预约list
//     *
//     * @param responseResReserveDto
//     * @return
//     */
//    public static List<SysResResponseData> resReserve(List<SysResReserveXml> responseResReserveDto) {
//        ResponseResReserveDto volunteersJoinOrg = new ResponseResReserveDto();
//        volunteersJoinOrg.setInfo(responseResReserveDto);
//        SysResResponseDataResource volunteersJoinOrgResource = resReserve(volunteersJoinOrg);
//        return volunteersJoinOrgResource.getInfo();
//    }
//
//    /**
//     * 2.16.	服务领域查询
//     *
//     * @return 获取服务领域的数据字典
//     */
//    public static List<ServiceCodeResult> getZyzServiceType() {
//        String result = ApiWebUtil.sendHttpRequest("getZyzServiceType", "");
//        ServiceCodeResult.ServiceCodePs ps = XmlUtils.parseBean(result, ServiceCodeResult.ServiceCodePs.class);
//        if (ps == null) {
//            log.error("（服务领域查询）市平台调用成功，返回的xml无法解析:{}", result);
//            throw new BusinessException(ResultCode.BAD_REQUEST, "请求市平台出错");
//        }
//        return ps.getInfo();
//    }
//
//    public static void main(String[] args) {
//        getZyzServiceType();
//    }
//}
