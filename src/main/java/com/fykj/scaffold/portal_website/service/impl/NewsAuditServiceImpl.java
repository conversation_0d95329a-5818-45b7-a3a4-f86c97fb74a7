package com.fykj.scaffold.portal_website.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.portal_website.domain.entity.NewsAudit;
import com.fykj.scaffold.portal_website.mapper.NewsAuditMapper;
import com.fykj.scaffold.portal_website.service.INewsAuditService;
import com.fykj.scaffold.security.business.domain.entity.User;
import com.fykj.scaffold.security.business.service.IUserService;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新闻资讯审核服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class NewsAuditServiceImpl extends BaseServiceImpl<NewsAuditMapper, NewsAudit> implements INewsAuditService {

    @Autowired
    private IUserService userService;

    @Override
    public IPage<NewsAudit> page(BaseParams params) {
        return super.page(params).convert(this::convert);
    }

    private NewsAudit convert(NewsAudit record) {
        User creator;
        try {
            creator = userService.getById(record.getCreator());
        } catch (Exception e) {
            creator = null;
        }
        record.setAuditor(creator == null ? "审核人未知或已移除" :  creator.getName());
        record.setAuditTime(record.getCreateDate());
        return record;
    }
}
