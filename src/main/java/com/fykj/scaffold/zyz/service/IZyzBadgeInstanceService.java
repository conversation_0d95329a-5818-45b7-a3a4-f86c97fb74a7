package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgeGiftDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgePurchaseDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgeTradePublishDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzBadgeTradePublishInfoDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeInstance;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTradeListing;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeInstanceParams;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceDetailVO;
import com.fykj.scaffold.zyz.domain.vo.ZyzBadgeInstanceVO;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 勋章实例-服务类
 *
 * @date 2025-05-09
 */
public interface IZyzBadgeInstanceService extends IBaseService<ZyzBadgeInstance> {

    /**
     * 查询当前登录用户的勋章列表
     * @return 勋章实例列表（含勋章基础信息）
     */
    List<ZyzBadgeInstanceVO> listMyBadges();
    
    /**
     * 分页查询当前登录用户的勋章列表
     * @param params 查询参数
     * @return 分页勋章实例列表（含勋章基础信息）
     */
    IPage<ZyzBadgeInstanceVO> pageMyBadges(ZyzBadgeInstanceParams params);
    
    /**
     * 通用分页查询勋章列表
     * @param params 查询参数
     * @return 分页勋章实例列表（含勋章基础信息）
     */
    IPage<ZyzBadgeInstanceVO> pageBadges(ZyzBadgeInstanceParams params);
    
    /**
     * 发布勋章交易
     *
     * @param dto 勋章交易发布数据
     * @return 交易发布记录
     */
    ZyzBadgeTradeListing publishBadgeTrade(ZyzBadgeTradePublishDto dto);

    /**
     * 编辑勋章交易
     *
     * @param dto 勋章交易发布数据
     * @return 交易发布记录
     */
    ZyzBadgeTradeListing editBadgeTrade(ZyzBadgeTradePublishDto dto);
    
    /**
     * 取消勋章交易
     *
     * @param listingId 交易上架ID
     * @return 是否取消成功
     */
    boolean cancelBadgeTrade(Long listingId);
    
    /**
     * 获取勋章交易发布信息
     *
     * @param badgeId 勋章ID
     * @param pointsBeforeFee 费率前积分，如果不为null则使用该值计算，否则使用勋章默认所需积分
     * @return 勋章交易发布数据
     */
    ZyzBadgeTradePublishInfoDto getTradePublishInfo(Long badgeId, Integer pointsBeforeFee);
    
    /**
     * 获取用户的指定勋章实例列表
     * 
     * @param badgeId 勋章ID
     * @param volunteerId 志愿者id
     * @return 勋章实例
     * @throws exception.BusinessException 如果用户没有可用的此类勋章
     */
    ZyzBadgeInstance getUserBadgeInstances(Long badgeId, Long volunteerId);
    
    /**
     * 获取当前用户的勋章实例详情
     * 
     * @param badgeId 勋章ID
     * @return 勋章实例详情，包含实例ID和是否已上架
     */
    ZyzBadgeInstanceDetailVO getMyBadgeInstanceDetail(Long badgeId);
    
    /**
     * 检查勋章实例是否已上架
     * 
     * @param instanceId 勋章实例ID
     * @return 是否已上架
     */
    boolean checkBadgeInstanceListed(Long instanceId);
    
    /**
     * 兑换勋章获取积分
     * 
     * @param badgeId 勋章ID
     * @return 是否兑换成功
     */
    boolean exchangeForPoints(Long badgeId);
    
    /**
     * 赠送勋章给其他志愿者
     *
     * @param giftDto 赠送勋章参数
     */
    void giftBadge(ZyzBadgeGiftDto giftDto);
    
    /**
     * 购买勋章
     * 
     * @param purchaseDto 购买勋章参数
     * @return 购买结果
     */
    boolean purchaseBadge(ZyzBadgePurchaseDto purchaseDto);
}

