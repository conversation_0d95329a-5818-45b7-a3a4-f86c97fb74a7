package com.fykj.scaffold.race.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 照片路径情况详情
 *
 * <AUTHOR> @email ${email}
 * @date 2022-10-08
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel("照片路径")
public class WinPhotoExcelDto implements Serializable {
    private static final long serialVersionUID = -6524844330606853296L;

    @ExcelProperty(index = 0)
    @ApiModelProperty(value = "序号")
    private String sequence;

    @ExcelProperty(index = 1)
    @ApiModelProperty(value = "奖项")
    private String prizeName;

    @ExcelProperty(index = 2)
    @ApiModelProperty(value = "作品名称")
    private String photoName;

    @ExcelProperty(index = 3)
    @ApiModelProperty(value = "作品编号")
    private String number;

    @ExcelProperty(index = 4)
    @ApiModelProperty(value = "作者姓名")
    private String author;

    @ExcelProperty(index = 5)
    @ApiModelProperty(value = "联系方式")
    private String authorPhone;

    @ExcelProperty(index = 6)
    @ApiModelProperty(value = "联系方式")
    private String picNums;


    @ExcelProperty(index = 7)
    @ApiModelProperty(value = "文字（修改版）")
    private String remarks;
}
