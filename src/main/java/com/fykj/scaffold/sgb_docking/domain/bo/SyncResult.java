package com.fykj.scaffold.sgb_docking.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 同步结果封装类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public class SyncResult {
    private final boolean success;
    private final String resultCode;
    private final String resultMsg;
    private final String sgbId;

    /**
     * 创建失败的同步结果
     *
     * @param resultMsg 结果消息
     * @param resultCode 结果代码
     * @param sgbId SGB平台ID
     * @return 失败的同步结果
     */
    public static SyncResult failed(String resultMsg, String resultCode, String sgbId) {
        return new SyncResult(false, resultCode, resultMsg, sgbId);
    }

    /**
     * 创建成功的同步结果
     *
     * @param resultCode 结果代码
     * @param resultMsg 结果消息
     * @param sgbId SGB平台ID
     * @return 成功的同步结果
     */
    public static SyncResult success(String resultCode, String resultMsg, String sgbId) {
        return new SyncResult(true, resultCode, resultMsg, sgbId);
    }
}
