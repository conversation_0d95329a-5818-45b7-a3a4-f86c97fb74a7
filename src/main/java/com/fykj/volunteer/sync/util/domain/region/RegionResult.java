package com.fykj.volunteer.sync.util.domain.region;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

/**
 * <info>
 *     <code>SUCCESS</code>
 *     <msg>成功</msg>
 *     <regionCode>3205</regionCode>
 *     <regionName>苏州市</regionName>
 *     <parentCode>32</parentCode>
 *   </info>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "info")
public class RegionResult {
    //地域code
    private String regionCode;
    //地域名称
    private String regionName;
    //父id
    private String parentCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "ps")
    public static class Ps {
        private List<RegionResult> info = new ArrayList<>();
    }
}
