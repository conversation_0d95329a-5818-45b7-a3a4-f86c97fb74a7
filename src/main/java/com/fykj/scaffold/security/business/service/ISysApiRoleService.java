package com.fykj.scaffold.security.business.service;

import com.fykj.scaffold.security.business.domain.dto.ActionOrRoleApiPathDto;
import fykj.microservice.core.base.IBaseService;
import com.fykj.scaffold.security.business.domain.entity.SysApiRole;

import java.util.List;

/**
 * 接口-角色映射表
 *
 * 服务类
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
public interface ISysApiRoleService extends IBaseService<SysApiRole> {

    /**
     * 获取按钮已绑定的接口列表
     * @param roleId
     * @return
     */
    List<String> roleApi(Long roleId);

    /**
     * 保存授权
     * @param dto
     * @return
     */
    boolean savePath(ActionOrRoleApiPathDto dto);
}

