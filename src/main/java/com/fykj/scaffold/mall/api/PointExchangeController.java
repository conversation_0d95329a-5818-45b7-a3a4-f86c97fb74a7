package com.fykj.scaffold.mall.api;


import com.fykj.scaffold.mall.domain.dto.MachineResultDto;
import com.fykj.scaffold.mall.domain.dto.PointExchangeDto;
import com.fykj.scaffold.mall.service.IMallExchangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import result.Result;

/**
 * <AUTHOR>
 * @since 2020-02-05
 */
@RestController
@RequestMapping("/api/pointExchange")
@Api(tags = "pc --礼品兑换接口")
public class PointExchangeController {

    @Autowired
    private IMallExchangeService exchangeService;

    @ApiOperation("机器获取扫码的结果")
    @PostMapping(value = "/getScanResult")
    public MachineResultDto getScanResult(String OrderNum) {
        return exchangeService.isAcceptOrder(OrderNum);
    }

    @ApiOperation("机器通知礼品掉落情况")
    @GetMapping(value = "/outStockResult")
    public MachineResultDto outStockResult(String OrderNum, int Status) {
        return exchangeService.outStockResult(OrderNum, Status);
    }

}

