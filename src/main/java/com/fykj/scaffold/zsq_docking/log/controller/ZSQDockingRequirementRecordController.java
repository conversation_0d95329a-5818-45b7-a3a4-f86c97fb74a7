package com.fykj.scaffold.zsq_docking.log.controller;


import com.fykj.scaffold.zsq_docking.log.domain.dto.RequirementDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.dto.ResourceDockingRecordDetailDto;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingRequirementRecord;
import com.fykj.scaffold.zsq_docking.log.domain.entity.ZSQDockingResourceRecord;
import com.fykj.scaffold.zsq_docking.log.domain.params.ZSQDockingRequirementRecordParams;
import com.fykj.scaffold.zsq_docking.log.service.IZSQDockingRequirementRecordService;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;
import result.Result;

import java.util.List;

@RestController
@RequestMapping("/admin/zsq_docking_requirement_record")
@Api(tags = "知社区需求对接记录")
public class ZSQDockingRequirementRecordController extends BaseController<IZSQDockingRequirementRecordService, ZSQDockingRequirementRecord, ZSQDockingRequirementRecordParams> {

    @ApiOperation("获取全部方法")
    @GetMapping({"/getRequirementDockingRecords"})
    public JsonResult<List<ZSQDockingRequirementRecord>> getRequirementDockingRecords(@RequestParam Long requirementId) {
        return new JsonResult<>(this.baseService.getRequirementDockingRecords(requirementId));
    }

    @ApiOperation("失败重试")
    @GetMapping({"/retry"})
    public Result retry(@RequestParam Long requirementId, @RequestParam Long recordId) {
        baseService.retry(requirementId, recordId);
        return OK;
    }

    @ApiOperation("获取对接记录详情")
    @GetMapping({"/getDockingRecordDetail"})
    public JsonResult<RequirementDockingRecordDetailDto> getDockingRecordDetail(@RequestParam Long recordId) {
        return new JsonResult<>(baseService.getDockingRecordDetail(recordId));
    }
}