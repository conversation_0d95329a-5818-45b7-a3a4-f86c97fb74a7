package com.fykj.scaffold.capability.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户学分管理
 * 表对应：cu_user_credits
 * 2025/2/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "cu_user_credits", autoResultMap = true)
public class UserCredits extends BaseEntity {

    @TableField("user_id")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @TableField("credits")
    @ApiModelProperty(value = "学分")
    private Long credits;

}
