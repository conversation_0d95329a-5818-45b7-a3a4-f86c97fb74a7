package com.fykj.scaffold.mall.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mall.domain.entity.MallStockNumChange;
import com.fykj.scaffold.mall.domain.params.MallStockNumChangeParams;
import com.fykj.scaffold.mall.service.IMallStockNumChangeService;
import com.fykj.scaffold.support.syslog.AuditLog;
import fykj.microservice.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

/**
 * 积分商城-库存变动
 *
 * 前端控制器
 * <AUTHOR> @email ${email}
 * @date 2023-05-25
 */
@RestController
@RequestMapping("/admin/mall/stock/num/change")
@Api(tags = "积分商城-库存变动接口")
public class MallStockNumChangeController extends BaseController<IMallStockNumChangeService, MallStockNumChange, MallStockNumChangeParams> {

    @AuditLog("商品库存分页查询")
    @ApiOperation("商品库存分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<MallStockNumChange>> getPages(@RequestBody MallStockNumChangeParams params) {
        return new JsonResult<>(baseService.getPage(params));
    }
}
