package com.fykj.scaffold.dynamic.domain.params;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 字段信息快照-查询参数
 *
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("字段信息快照-查询参数")
public class DynamicFieldSnapshotParams extends BaseParams {

    private static final long serialVersionUID = 1952900891757856702L;

    @ApiModelProperty(value = "快照ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @MatchType(value = QueryType.EQ)
    private Long snapshotId;

}
