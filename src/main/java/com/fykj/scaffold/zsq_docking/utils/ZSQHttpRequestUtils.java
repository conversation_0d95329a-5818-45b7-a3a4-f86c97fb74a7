package com.fykj.scaffold.zsq_docking.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.zsq_docking.push_mq.dto.ActZyzSyncBelongFieldDto;
import com.fykj.scaffold.zsq_docking.push_mq.dto.ActZyzSyncBelongRegionDto;
import com.fykj.scaffold.zsq_docking.push_mq.dto.RequirementSyncForZyzDto;
import com.fykj.scaffold.zsq_docking.push_mq.dto.ResourceSyncForZyzDto;
import com.fykj.scaffold.zsq_docking.push_mq.event.*;
import exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import result.ResultCode;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.fykj.scaffold.zsq_docking.cons.ZSQDockingCons.*;
import org.springframework.cglib.beans.BeanMap;

/**
 * @Author：yangxu
 * @Date：2024/12/10 18:12
 * @Description：
 */
@Component
@Slf4j
public class ZSQHttpRequestUtils {

    @Value("${spring.profiles.active:''}")
    private String env;
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final RestTemplate restTemplate = new RestTemplate();

    public String getNewsDetailFetchUrl() {
        if ("dev".equals(env)) {
            return ZSQ_NEWS_DETAIL_FETCH_DEV;
        } else if("test".equals(env)) {
            return ZSQ_NEWS_DETAIL_FETCH_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_NEWS_DETAIL_FETCH_PROD;
        } else {
            return null;
        }
    }

    public String getActivityDetailFetchUrl() {
        if ("dev".equals(env)) {
            return ZSQ_ACTIVITY_DETAIL_FETCH_DEV;
        } else if("test".equals(env)) {
            return ZSQ_ACTIVITY_DETAIL_FETCH_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_ACTIVITY_DETAIL_FETCH_PROD;
        } else {
            return null;
        }
    }

    public String getNewsDockingUrl() {
        if ("dev".equals(env)) {
            return ZSQ_NEWS_DOCKING_URL_DEV;
        } else if("test".equals(env)) {
            return ZSQ_NEWS_DOCKING_URL_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_NEWS_DOCKING_URL_PROD;
        } else {
            return null;
        }
    }

    public String getActivityDockingUrl() {
        if ("dev".equals(env)) {
            return ZSQ_ACTIVITY_DOCKING_URL_DEV;
        } else if("test".equals(env)) {
            return ZSQ_ACTIVITY_DOCKING_URL_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_ACTIVITY_DOCKING_URL_PROD;
        } else {
            return null;
        }
    }

    public String getResourceDockingUrl() {
        if ("dev".equals(env)) {
            return ZSQ_RESOURCE_DOCKING_URL_DEV;
        } else if("test".equals(env)) {
            return ZSQ_RESOURCE_DOCKING_URL_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_RESOURCE_DOCKING_URL_PROD;
        } else {
            return null;
        }
    }

    public String getRequirementDockingUrl() {
        if ("dev".equals(env)) {
            return ZSQ_REQUIREMENT_DOCKING_URL_DEV;
        } else if("test".equals(env)) {
            return ZSQ_REQUIREMENT_DOCKING_URL_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_REQUIREMENT_DOCKING_URL_PROD;
        } else {
            return null;
        }
    }


    public String getSysOrgDockingUrl() {
        if ("dev".equals(env)) {
            return ZSQ_SYS_ORG_DOCKING_URL_DEV;
        } else if("test".equals(env)) {
            return ZSQ_SYS_ORG_DOCKING_URL_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_SYS_ORG_DOCKING_URL_PROD;
        } else {
            return null;
        }
    }

    public String getBelongFieldDockingUrl() {
        if ("dev".equals(env)) {
            return ZSQ_BELONG_FIELD_DOCKING_URL_DEV;
        } else if("test".equals(env)) {
            return ZSQ_BELONG_FIELD_DOCKING_URL_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_BELONG_FIELD_DOCKING_URL_PROD;
        } else {
            return null;
        }
    }

    public String getRegionAllDockingUrl() {
        if ("dev".equals(env)) {
            return ZSQ_REGION_ALL_DOCKING_URL_DEV;
        } else if("test".equals(env)) {
            return ZSQ_REGION_ALL_DOCKING_URL_TEST;
        } else if("prod".equals(env)) {
            return ZSQ_REGION_ALL_DOCKING_URL_PROD;
        } else {
            return null;
        }
    }
    public JSONObject getNewsDetail(Long newsId) {
        String url = getNewsDetailFetchUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "获取知社区新闻详情失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(newsId));
        return doGet(url, params);
    }

    public JSONObject newsDockingPush(NewsDockingEvent message) {
        String url = getNewsDockingUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "新闻操作通知知社区失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("operation", message.getOperation());
        params.put("newsId", message.getNewsId());
        params.put("zwtUserId", message.getZwtUserId());
        params.put("onShelf", message.getOnShelf());
        return doPost(url, params);
    }


    public JSONObject activityDockingPush(ActivityDockingEvent message) {
        String url = getActivityDockingUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "活动操作通知知社区失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("operation", message.getOperation());
        params.put("actId", message.getActId());
        params.put("zwtUserId", message.getZwtUserId());
        params.put("onShelf", message.getOnShelf());
        return doPost(url, params);
    }
    public JSONObject resourceDockingPush(ResourceDockingEvent message) {
        String url = getResourceDockingUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "资源操作通知知社区失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        Map<String, Object> params = new HashMap<>();
        ResourceSyncForZyzDto resource = message.getResourceSyncForZyzDto();
        if (resource != null) {
            params=objectToMap(resource);
        }
        return doPost(url, params);
    }
    private   Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> map = new HashMap<>();
        BeanMap beanMap = BeanMap.create(obj);
        for (Object key : beanMap.keySet()) {
            String property = String.valueOf(key);
            if ("class".equals(property)) {
                continue; // 忽略class属性
            }
            Object value = beanMap.get(key);
            if (value instanceof Date) {
                value = DATE_FORMAT.format((Date) value);
            } else if (value instanceof LocalDateTime) {
                value = ((LocalDateTime) value).format(DATE_TIME_FORMATTER);
            } else if (value instanceof LocalDate) {
                value = ((LocalDate) value).atStartOfDay().format(DATE_TIME_FORMATTER);
            }
            map.put(property, value);
        }
        return map;
    }
    public JSONObject requirementDockingPush(RequirementDockingEvent message) {
        String url = getRequirementDockingUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "需求操作通知知社区失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        Map<String, Object> params = new HashMap<>();
        RequirementSyncForZyzDto requirement = message.getRequirementSyncForZyzDto();
        if (requirement != null) {
            params=objectToMap(requirement);
        }
        return doPost(url, params);
    }

    public JSONObject sysOrgDockingPush(SysOrgDockingEvent message) {
        String url = getSysOrgDockingUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "组织操作通知知社区失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("operation", message.getOperation());
        params.put("zwtUserId", message.getZwtUserId());
        return doPost(url, params);
    }

    public JSONObject belongFieldDockingPush(BelongFieldDockingEvent message) {
        String url = getBelongFieldDockingUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "所属领域操作通知知社区失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        List<ActZyzSyncBelongFieldDto> list = message.getBelongFieldList();
        if (CollUtil.isNotEmpty(list)) {
            String jsonBody = JSONObject.toJSONString(list);
            return jsonPost(url, jsonBody);
        }
        return null;

    }


    public JSONObject regionAllDockingPush(RegionAllDockingEvent message) {
        String url = getRegionAllDockingUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "所有社区操作通知知社区失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        List<ActZyzSyncBelongRegionDto> list = message.getRegionAllList();
        if (CollUtil.isNotEmpty(list)) {
            String jsonBody = JSONObject.toJSONString(list);
            return jsonPost(url, jsonBody);
        }
       return null;
    }

    public static JSONObject jsonPost(String url, String jsonBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON); // 设置为 JSON
        Map<String, String> headerMap = AuthHeaderUtil.addHeaders();
        if (headerMap != null && !headerMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                headers.add(entry.getKey(), entry.getValue());
            }
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        return JSONObject.parseObject(response.getBody());
    }
    /**
     * 发送 GET 请求
     * @param url 请求地址
     * @param params 请求参数
     * @return 响应结果 JSONObject
     */
    public static JSONObject doGet(String url, Map<String, String> params) {
        StringBuilder fullUrl = new StringBuilder(url);
        if (params != null && !params.isEmpty()) {
            fullUrl.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                fullUrl.append(entry.getKey()).append("=")
                        .append(entry.getValue()).append("&");
            }
            fullUrl.setLength(fullUrl.length() - 1); // 移除最后的&
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        Map<String, String> headers = AuthHeaderUtil.addHeaders();
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue());
            }
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<String> response = restTemplate.exchange(
                fullUrl.toString(),
                HttpMethod.GET,
                requestEntity,
                String.class);
        return JSONObject.parseObject(response.getBody());
    }

    /**
     * 发送 POST 请求
     * @param url 请求地址
     * @param params 请求参数
     * @return 响应结果 JSONObject
     */
    public static JSONObject doPost(String url, Map<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON); // 设置为 JSON
        Map<String, String> headerMap = AuthHeaderUtil.addHeaders();
        if (headerMap != null && !headerMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                headers.add(entry.getKey(), entry.getValue());
            }
        }
        // 将参数 map 转为 JSON 字符串
        String jsonBody = JSONObject.toJSONString(params);
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        return JSONObject.parseObject(response.getBody());
    }

    public JSONObject getActivityDetail(Long activityId) {
        String url = getActivityDetailFetchUrl();
        if (url == null) {
            throw new BusinessException(ResultCode.FAIL, "获取知社区活动详情失败，失败原因：环境变量配置异常导致url未获取到！");
        }
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(activityId));
        return doGet(url, params);
    }
}
