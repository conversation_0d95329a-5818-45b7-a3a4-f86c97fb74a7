package com.fykj.scaffold.zyz.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fykj.scaffold.zyz.domain.dto.BadgeImportDto;
import com.fykj.scaffold.zyz.domain.dto.BatchAwardBadgeDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadge;
import com.fykj.scaffold.zyz.domain.params.ZyzBadgeParams;
import com.fykj.scaffold.zyz.service.IZyzBadgeService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.base.BaseEntity;
import fykj.microservice.core.support.excel.ExcelUtil;
import fykj.microservice.core.support.syslog.SysLogMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 勋章信息-接口控制器
 *
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/admin/zyz/badge")
@Api(tags = "勋章信息-管理接口")
public class ZyzBadgeController extends BaseController<IZyzBadgeService, ZyzBadge, ZyzBadgeParams> {

    @ApiOperation("切换置顶状态")
    @PostMapping("/switchTop")
    public Result switchTop(@RequestParam Long id) {
        baseService.switchTopStatus(id);
        return OK;
    }

    @ApiOperation("切换上下架状态")
    @PostMapping("/switchStatus")
    public Result switchStatus(@RequestParam Long id) {
        baseService.switchStatus(id);
        return OK;
    }
    
    @SysLogMethod("创建勋章并自动发布")
    @ApiOperation("创建勋章并自动发布")
    @PostMapping("/saveAndCreateListing")
    public Result saveAndCreateListing(@RequestBody @Validated({BaseEntity.Add.class}) ZyzBadge badge) {
        boolean result = baseService.saveAndCreateListing(badge);
        return result ? OK : new Result(ResultCode.FAIL);
    }
    
    @SysLogMethod("更新勋章并自动处理发布")
    @ApiOperation("更新勋章并自动处理发布")
    @PostMapping("/updateAndProcessListing")
    public Result updateAndProcessListing(@RequestBody @Validated({BaseEntity.Modify.class}) ZyzBadge badge) {
        boolean result = baseService.updateAndProcessListing(badge);
        return result ? OK : new Result(ResultCode.DATA_EXPIRED);
    }
    
    @SysLogMethod("批量授予勋章")
    @ApiOperation("批量授予勋章")
    @PostMapping("/batch/award")
    public Result batchAward(@RequestParam("excel") MultipartFile excel, @RequestParam Long badgeId) {
        List<BadgeImportDto> failureList = baseService.batchAwardBadge(excel, badgeId);
        if (CollectionUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "badge_award_error.xlsx", BadgeImportDto.class);
        }
        return OK;
    }
    
    @ApiOperation("勋章授予Excel模板下载")
    @GetMapping("/award/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/badge_award_template.xlsx");
    }

    @ApiOperation("批量上下架")
    @PostMapping("/batchSwitchStatus")
    public Result batchSwitchStatus(@RequestParam String ids) {
        if (ids == null || ids.trim().isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "勋章ID列表不能为空");
        }
        Arrays.asList(ids.split(",")).forEach(it
                -> baseService.switchStatus(Long.valueOf(it.trim())));
        return OK;
    }

    @ApiOperation("批量授予勋章")
    @PostMapping("/batchAwardBadge")
    public JsonResult<String> batchAwardBadge(@RequestBody @Validated BatchAwardBadgeDto dto) {
        String result = baseService.batchAwardBadgeByIds(dto.getBadgeId(), dto.getVolunteerIds());
        return new JsonResult<>(result);
    }
}
