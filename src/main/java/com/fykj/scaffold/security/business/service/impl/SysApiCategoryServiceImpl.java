package com.fykj.scaffold.security.business.service.impl;

import com.fykj.scaffold.security.business.domain.dto.CascaderDto;
import com.fykj.scaffold.security.business.domain.entity.SysApiCategory;
import com.fykj.scaffold.security.business.mapper.SysApiCategoryMapper;
import com.fykj.scaffold.security.business.service.ISysApiCategoryService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import utils.StringUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 接口目录表
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2022-02-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysApiCategoryServiceImpl extends BaseServiceImpl<SysApiCategoryMapper, SysApiCategory> implements ISysApiCategoryService {

    @Override
    public List<CascaderDto> cascader() {
        List<CascaderDto> categories = findByParent(null);
        if (CollectionUtils.isEmpty(categories)) {
            return Collections.emptyList();
        }
        categories.forEach(this::buildTree);
        return categories;
    }

    @SuppressWarnings("unchecked")
    public List<CascaderDto> findByParent(Long parentId) {
        List<CascaderDto> list = baseMapper.findByParent(parentId);
        return CollectionUtils.isEmpty(list) ? null : list;
    }

    /**
     * 构建分类树-递归查询所有子分类
     *
     * @param category 树分类根节点
     */
    private void buildTree(CascaderDto category) {
        List<CascaderDto> subCategories = baseMapper.findByParent(category.getValue());
        category.setChildren(CollectionUtils.isEmpty(subCategories) ? null : subCategories);
        if (!CollectionUtils.isEmpty(subCategories)) {
            subCategories.forEach(this::buildTree);
        }
    }

    @Override
    public List<SysApiCategory> tree() {
        List<SysApiCategory> categories = findByParent(null, true);
        if (categories == null || categories.size() == 0) {
            return Collections.emptyList();
        }
        categories.forEach(it -> buildTree(it, true));
        return categories;
    }

    @SuppressWarnings("unchecked")
    private List<SysApiCategory> findByParent(Long parentId, Boolean enable) {
        List<SysApiCategory> list = lambdaQuery().eq(StringUtil.isNotEmpty(parentId), SysApiCategory::getParentId, parentId)
                .eq(enable != null, SysApiCategory::getStatus, enable)
                .isNull(StringUtil.isEmpty(parentId), SysApiCategory::getParentId)
                .orderByAsc(SysApiCategory::getSequence).list();
        return CollectionUtils.isEmpty(list) ? null : list;
    }

    /**
     * 构建分类树-递归查询所有子分类
     *
     * @param category 树分类根节点
     */
    private void buildTree(SysApiCategory category, Boolean enable) {
        List<SysApiCategory> subCategories = findByParent(category.getId(), enable);
        category.setSubCategories(subCategories);
        if (subCategories != null && subCategories.size() > 0) {
            subCategories.forEach(it -> buildTree(it, enable));
        }
    }

    @Override
    public boolean setEnable(Long id) {
        SysApiCategory category = getById(id);
        category.setStatus(!category.getStatus());
        return super.updateById(category);
    }

    @Override
    public boolean save(SysApiCategory category) {
        getCategoryLevel(category);
        return super.save(category);
    }

    @Override
    public boolean updateById(SysApiCategory category) {
        getCategoryLevel(category);
        return super.updateById(category);
    }

    private void getCategoryLevel(SysApiCategory category) {
        Long parentId = category.getParentId();
        if (StringUtil.isNotEmpty(parentId)) {
            SysApiCategory parent = getById(parentId);
            category.setLevel(parent.getLevel() + 1);
        }
    }

    @Override
    public List<SysApiCategory> listOfEnable() {
        List<SysApiCategory> apiCategories = super.list();
        List<SysApiCategory> parentNotExistAndDisabled = new ArrayList<>();
        apiCategories.forEach(it -> {
            if(!ObjectUtils.isEmpty(it.getParentId())
                    && CollectionUtils.isEmpty(apiCategories.stream().filter(im -> im.getId().equals(it.getParentId())).collect(Collectors.toList()))) {
                parentNotExistAndDisabled.add(it);
            }
            if (!it.getStatus()) {
                parentNotExistAndDisabled.add(it);
            }
        });
        List<SysApiCategory> needRemove = new ArrayList<>();
        getNeedRemoveCategories(apiCategories, parentNotExistAndDisabled, needRemove);
        List<Long> needRemoveId = needRemove.stream().map(SysApiCategory::getId).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        return apiCategories.stream().filter(it -> !needRemoveId.contains(it.getId())).collect(Collectors.toList());
    }

    private void getNeedRemoveCategories (List<SysApiCategory> all, List<SysApiCategory> init, List<SysApiCategory> result) {
        for (SysApiCategory it:init) {
            result.add(it);
            List<SysApiCategory> subCategories = all.stream().filter(im -> !ObjectUtils.isEmpty(im.getParentId()) && im.getParentId().equals(it.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(subCategories)) {
                getNeedRemoveCategories(all, subCategories, result);
            }
        }
    }

    @Override
    public List<SysApiCategory> list() {
        return super.list().stream().sorted(Comparator.comparing(SysApiCategory::getSequence)).collect(Collectors.toList());
    }
}
